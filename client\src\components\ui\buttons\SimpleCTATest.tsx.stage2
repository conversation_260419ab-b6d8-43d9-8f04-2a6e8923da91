/**
 * Simple Test Page for CTA Buttons
 * 用于测试和验证按钮样式是否正确应用
 */

import React from 'react';
import { Rocket, MessageCircle  } from 'lucide-react';
import {  SimpleCTAPrimaryButton, SimpleCTASecondaryButton  } from "./SimpleCTAButtons";

const SimpleCTATest: React.FC = () => {;
  return (
    <div className="min-h-screen bg-gray-100 flex items-center justify-center p-8">
      <div className="bg-white p-12 rounded-xl shadow-lg max-w-2xl">
        <h1 className="text-3xl font-bold text-gray-800 mb-8 text-center">
          🧪 CTA 按钮测试页面
        </h1>
        {/* 测试按钮 */}
        <div className="space-y-8">
          {/* Primary Button 测试 */}
          <div className="text-center">
            <h3 className="text-lg font-semibold mb-4">Primary Button (Get Started Free)</h3>
            <SimpleCTAPrimaryButton
              variant="primary"
              onClick={() => alert('Primary Button Clicked!')}
              icon={<Rocket className="w-5 h-5" />}
            >
              Get Started Free
            </SimpleCTAPrimaryButton>
            <p className="text-sm text-gray-500 mt-2">
              应该显示：紫色到粉色渐变，圆角10px，高度52px
            </p>
          </div>
          {/* Secondary Button 测试 */}
          <div className="text-center">
            <h3 className="text-lg font-semibold mb-4">Secondary Button (Contact, Us)</h3>
            <SimpleCTASecondaryButton
              variant="secondary"
              onClick={() => alert('Secondary Button Clicked!')}
              icon={<MessageCircle className="w-4 h-4" />}
            >
              Contact Us
            </SimpleCTASecondaryButton>
            <p className="text-sm text-gray-500 mt-2">
              应该显示：透明背景，灰色边框，圆角10px，高度52px
            </p>
          </div>
          {/* 并排测试 */}
          <div className="text-center">
            <h3 className="text-lg font-semibold mb-4">并排显示测试</h3>
            <div className="flex flex-col sm:flex-row justify-center items-center gap-6">
              <SimpleCTAPrimaryButton
                variant="primary"
                onClick={() => console.log('Primary clicked')}
                icon={<Rocket className="w-5 h-5" />}
              >
                Get Started Free
              </SimpleCTAPrimaryButton>
              <SimpleCTASecondaryButton
                variant="secondary"
                onClick={() => console.log('Secondary clicked')}
                icon={<MessageCircle className="w-4 h-4" />}
              >
                Contact Us
              </SimpleCTASecondaryButton>
            </div>
          </div>
        </div>
        {/* 调试信息 */}
        <div className="mt-12 p-4 bg-gray-50 rounded-lg">
          <h4 className="font-semibold mb-2">🔧 调试信息：</h4>
          <ul className="text-sm text-gray-600 space-y-1">
            <li>• 如果按钮显示不正确，请检查浏览器控制台是否有错误</li>
            <li>• 尝试硬刷新 (Ctrl+F5 或 Cmd+Shift+R)</li>
            <li>• 检查 Tailwind CSS 是否正确加载</li>
            <li>• 确认没有其他样式覆盖这些按钮</li>
          </ul>
        </div>
      </div>
    </div>

};

export default SimpleCTATest;