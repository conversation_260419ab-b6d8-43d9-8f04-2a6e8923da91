"use strict";(self.webpackChunkclient=self.webpackChunkclient||[]).push([[603],{"./src/components/ui/alert.stories.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{Default:()=>Default,Destructive:()=>Destructive,__namedExportsOrder:()=>__namedExportsOrder,default:()=>alert_stories});var react=__webpack_require__("./node_modules/react/index.js"),clsx=__webpack_require__("./node_modules/clsx/dist/clsx.mjs");const falsyToString=value=>"boolean"==typeof value?`${value}`:0===value?"0":value,cx=clsx.$;var utils=__webpack_require__("./src/lib/utils.js"),jsx_runtime=__webpack_require__("./node_modules/react/jsx-runtime.js");const alertVariants=(base="relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",config={variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}},props=>{var _config_compoundVariants;if(null==(null==config?void 0:config.variants))return cx(base,null==props?void 0:props.class,null==props?void 0:props.className);const{variants,defaultVariants}=config,getVariantClassNames=Object.keys(variants).map((variant=>{const variantProp=null==props?void 0:props[variant],defaultVariantProp=null==defaultVariants?void 0:defaultVariants[variant];if(null===variantProp)return null;const variantKey=falsyToString(variantProp)||falsyToString(defaultVariantProp);return variants[variant][variantKey]})),propsWithoutUndefined=props&&Object.entries(props).reduce(((acc,param)=>{let[key,value]=param;return void 0===value||(acc[key]=value),acc}),{}),getCompoundVariantClassNames=null==config||null===(_config_compoundVariants=config.compoundVariants)||void 0===_config_compoundVariants?void 0:_config_compoundVariants.reduce(((acc,param)=>{let{class:cvClass,className:cvClassName,...compoundVariantOptions}=param;return Object.entries(compoundVariantOptions).every((param=>{let[key,value]=param;return Array.isArray(value)?value.includes({...defaultVariants,...propsWithoutUndefined}[key]):{...defaultVariants,...propsWithoutUndefined}[key]===value}))?[...acc,cvClass,cvClassName]:acc}),[]);return cx(base,getVariantClassNames,getCompoundVariantClassNames,null==props?void 0:props.class,null==props?void 0:props.className)});var base,config;const Alert=react.forwardRef(((_ref,ref)=>{let{className,variant,...props}=_ref;return(0,jsx_runtime.jsx)("div",{ref,role:"alert",className:(0,utils.cn)(alertVariants({variant}),className),...props})}));Alert.displayName="Alert";const AlertTitle=react.forwardRef(((_ref2,ref)=>{let{className,children,...props}=_ref2;return(0,jsx_runtime.jsx)("h5",{ref,className:(0,utils.cn)("mb-1 font-medium leading-none tracking-tight",className),...props,children})}));AlertTitle.displayName="AlertTitle";const AlertDescription=react.forwardRef(((_ref3,ref)=>{let{className,children,...props}=_ref3;return(0,jsx_runtime.jsx)("div",{ref,className:(0,utils.cn)("text-sm [&>p]:leading-relaxed",className),...props,children})}));AlertDescription.displayName="AlertDescription",Alert.__docgenInfo={description:"",methods:[],displayName:"Alert"},AlertTitle.__docgenInfo={description:"",methods:[],displayName:"AlertTitle"},AlertDescription.__docgenInfo={description:"",methods:[],displayName:"AlertDescription"};const alert_stories={title:"UI/Alert",component:Alert,tags:["autodocs"],argTypes:{variant:{control:{type:"select"},options:["default","destructive"]}},args:{variant:"default"}},Default={render:args=>(0,jsx_runtime.jsxs)(Alert,{...args,children:[(0,jsx_runtime.jsx)(AlertTitle,{children:"Notice"}),(0,jsx_runtime.jsx)(AlertDescription,{children:"This is a default alert message."})]})},Destructive={args:{variant:"destructive"},render:args=>(0,jsx_runtime.jsxs)(Alert,{...args,children:[(0,jsx_runtime.jsx)(AlertTitle,{children:"Error"}),(0,jsx_runtime.jsx)(AlertDescription,{children:"Something went wrong."})]})},__namedExportsOrder=["Default","Destructive"];Default.parameters={...Default.parameters,docs:{...Default.parameters?.docs,source:{originalSource:"{\n  render: args => <Alert {...args}>\r\n      <AlertTitle>Notice</AlertTitle>\r\n      <AlertDescription>This is a default alert message.</AlertDescription>\r\n    </Alert>\n}",...Default.parameters?.docs?.source}}},Destructive.parameters={...Destructive.parameters,docs:{...Destructive.parameters?.docs,source:{originalSource:"{\n  args: {\n    variant: 'destructive'\n  },\n  render: args => <Alert {...args}>\r\n      <AlertTitle>Error</AlertTitle>\r\n      <AlertDescription>Something went wrong.</AlertDescription>\r\n    </Alert>\n}",...Destructive.parameters?.docs?.source}}}},"./src/lib/utils.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{absoluteUrl:()=>absoluteUrl,cn:()=>cn,formatPrice:()=>formatPrice});var clsx__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__("./node_modules/clsx/dist/clsx.mjs"),tailwind_merge__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("./node_modules/tailwind-merge/dist/bundle-mjs.mjs"),process=__webpack_require__("./node_modules/process/browser.js");function cn(){for(var _len=arguments.length,inputs=new Array(_len),_key=0;_key<_len;_key++)inputs[_key]=arguments[_key];return(0,tailwind_merge__WEBPACK_IMPORTED_MODULE_0__.QP)((0,clsx__WEBPACK_IMPORTED_MODULE_1__.$)(inputs))}function formatPrice(price){return new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(price)}function absoluteUrl(path){return`${process.env.NEXT_PUBLIC_APP_URL||""}${path}`}}}]);