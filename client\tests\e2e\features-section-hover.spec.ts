import { test, expect } from '@playwright/test';

test.describe('Features Section Hover Effects', () => {
  test.beforeEach(async ({ page }) => {
    // 导航到主页
    await page.goto('/');
    
    // 等待页面加载完成
    await page.waitForLoadState('networkidle');
    
    // 滚动到features section
    await page.locator('#features').scrollIntoViewIfNeeded();
    await page.waitForTimeout(1000); // 等待滚动动画完成
  });

  test('should display all 4 feature cards', async ({ page }) => {
    // 检查是否有4个feature卡片
    const featureCards = page.locator('[data-testid="feature-card"]');
    await expect(featureCards).toHaveCount(4);
    
    // 检查每个卡片的标题
    const expectedTitles = [
      'AI-Powered Content Generation',
      'Ease of use', 
      'Flexible Pricing',
      'Cloud-based Platform'
    ];
    
    for (let i = 0; i < expectedTitles.length; i++) {
      await expect(featureCards.nth(i)).toContainText(expectedTitles[i]);
    }
  });

  test('should show hover effects on all feature cards', async ({ page }) => {
    const featureCards = page.locator('[data-testid="feature-card"]');
    
    for (let i = 0; i < 4; i++) {
      const card = featureCards.nth(i);
      
      // 获取hover前的样式
      const beforeHover = await card.evaluate((el) => {
        const computedStyle = window.getComputedStyle(el);
        const bgGradient = el.querySelector('.absolute.inset-0');
        const bgGradientStyle = bgGradient ? window.getComputedStyle(bgGradient) : null;
        
        return {
          zIndex: computedStyle.zIndex,
          bgOpacity: bgGradientStyle ? bgGradientStyle.opacity : '0'
        };
      });
      
      // 悬停在卡片上
      await card.hover();
      await page.waitForTimeout(500); // 等待hover动画完成
      
      // 检查hover后的样式变化
      const afterHover = await card.evaluate((el) => {
        const computedStyle = window.getComputedStyle(el);
        const bgGradient = el.querySelector('.absolute.inset-0');
        const bgGradientStyle = bgGradient ? window.getComputedStyle(bgGradient) : null;
        const leftBorder = el.querySelector('.absolute.left-0');
        const leftBorderStyle = leftBorder ? window.getComputedStyle(leftBorder) : null;
        const titleSpan = el.querySelector('span');
        const titleStyle = titleSpan ? window.getComputedStyle(titleSpan) : null;
        
        return {
          zIndex: computedStyle.zIndex,
          bgOpacity: bgGradientStyle ? bgGradientStyle.opacity : '0',
          borderHeight: leftBorderStyle ? leftBorderStyle.height : '0px',
          titleTransform: titleStyle ? titleStyle.transform : 'none'
        };
      });
      
      // 验证hover效果
      console.log(`Card ${i} hover effects:`, { beforeHover, afterHover });
      
      // 检查背景渐变是否显示
      expect(parseFloat(afterHover.bgOpacity)).toBeGreaterThan(parseFloat(beforeHover.bgOpacity));
      
      // 检查z-index是否提升
      expect(parseInt(afterHover.zIndex) || 0).toBeGreaterThanOrEqual(parseInt(beforeHover.zIndex) || 0);
      
      // 移开鼠标，准备测试下一个卡片
      await page.mouse.move(0, 0);
      await page.waitForTimeout(300);
    }
  });

  test('should specifically test the 4th feature card (Cloud-based Platform)', async ({ page }) => {
    const fourthCard = page.locator('[data-testid="feature-card"]').nth(3);
    
    // 确保第4个卡片可见
    await fourthCard.scrollIntoViewIfNeeded();
    
    // 检查卡片内容
    await expect(fourthCard).toContainText('Cloud-based Platform');
    await expect(fourthCard).toContainText('Access your content creation tools anywhere');
    
    // 测试hover效果
    await fourthCard.hover();
    await page.waitForTimeout(500);
    
    // 检查hover状态下的样式
    const hoverStyles = await fourthCard.evaluate((el) => {
      const bgGradient = el.querySelector('.absolute.inset-0');
      const icon = el.querySelector('.mb-4.relative.z-10');
      const leftBorder = el.querySelector('.absolute.left-0');
      const title = el.querySelector('span');
      
      return {
        bgGradientOpacity: bgGradient ? window.getComputedStyle(bgGradient).opacity : '0',
        iconColor: icon ? window.getComputedStyle(icon).color : '',
        borderBg: leftBorder ? window.getComputedStyle(leftBorder).backgroundColor : '',
        titleTransform: title ? window.getComputedStyle(title).transform : 'none',
        titleColor: title ? window.getComputedStyle(title).color : ''
      };
    });
    
    console.log('4th card hover styles:', hoverStyles);
    
    // 验证hover效果是否正常工作
    expect(parseFloat(hoverStyles.bgGradientOpacity)).toBeGreaterThan(0);
    expect(hoverStyles.titleTransform).not.toBe('none');
    
    // 截图用于调试
    await page.screenshot({ 
      path: `client/tests/e2e/screenshots/feature-card-4-hover-${Date.now()}.png`,
      fullPage: false 
    });
  });

  test('should check CSS classes and hover states', async ({ page }) => {
    const featureCards = page.locator('[data-testid="feature-card"]');
    
    for (let i = 0; i < 4; i++) {
      const card = featureCards.nth(i);
      
      // 检查基础CSS类
      const classes = await card.getAttribute('class');
      console.log(`Card ${i} classes:`, classes);
      
      // 验证必要的CSS类存在
      expect(classes).toContain('group');
      expect(classes).toContain('cursor-pointer');
      expect(classes).toContain('transition-all');
      
      // 悬停并检查hover状态
      await card.hover();
      await page.waitForTimeout(300);
      
      // 检查hover状态下的伪类
      const isHovered = await card.evaluate((el) => {
        return el.matches(':hover');
      });
      
      expect(isHovered).toBe(true);
      
      // 移开鼠标
      await page.mouse.move(0, 0);
      await page.waitForTimeout(200);
    }
  });
});
