import React, { useState, useEffect } from 'react';
import { <PERSON>, Badge, List, Avatar, Typography, Space, Tag, Button } from 'antd';
import { MessageCircle, Clock, MoreHorizontal } from 'lucide-react';

const { Text } = Typography;

interface ChatSession {
  id: string;
  customerName: string;
  platform: string;
  status: 'active' | 'waiting' | 'resolved';
  lastMessage: string;
  timestamp: Date;
  agentId?: string;
  priority: 'high' | 'medium' | 'low';
}

// interface RealtimeChatMonitorProps {
//   className?: string;
// }

const RealtimeChatMonitor: React.FC = () => {
  const [sessions, setSessions] = useState<ChatSession[]>([]);
  const [loading] = useState<boolean>(false);

  useEffect(() => {
    // Mock data for demonstration
    const mockSessions: ChatSession[] = [
      {
        id: '1',
        customerName: 'John Doe', platform: 'WhatsApp', status: 'active',
        lastMessage: 'I need help with my order', timestamp: new Date(),
        agentId: 'agent1', priority: 'high'
      },
      {
        id: '2', 
        customerName: '<PERSON>', platform: 'Facebook', status: 'waiting',
        lastMessage: 'Hello, are you there?', timestamp: new Date(Date.now() - 300000), priority: 'medium'
      }
    ];
    
    setSessions(mockSessions);
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'green';
      case 'waiting': return 'orange';
      case 'resolved': return 'blue';
      default: return 'default';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'red';
      case 'medium': return 'orange';
      case 'low': return 'green';
      default: return 'default';
    }
  };

  return (
    <Card
      className=""
      title={
        <Space>
          <MessageCircle size={20} />
          <span>Real-time Conversations</span>
          <Badge count={sessions.length} />
        </Space>
      }
      extra={
        <Button type="text" icon={<MoreHorizontal size={16} />} />
      }
      loading={loading}
    >
      <List
        dataSource={sessions}
        renderItem={(session: ChatSession) => (
          <List.Item
            key={session.id}
            actions={[
              <Tag color={getPriorityColor(session.priority)}>
                {session.priority}
              </Tag>,
              <Tag color={getStatusColor(session.status)}>
                {session.status}
              </Tag>
            ]}
          >
            <List.Item.Meta
              avatar={
                <Avatar style={{ backgroundColor: '#1890ff' }}>
                  {session.customerName.charAt(0)}
                </Avatar>
              }
              title={
                <Space>
                  <Text strong>{session.customerName}</Text>
                  <Text type="secondary">via {session.platform}</Text>
                </Space>
              }
              description={
                <Space direction="vertical" size="small">
                  <Text ellipsis style={{ maxWidth: 200 }}>
                    {session.lastMessage}
                  </Text>
                  <Space>
                    <Clock size={12} />
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      {session.timestamp.toLocaleTimeString()}
                    </Text>
                  </Space>
                </Space>
              }
            />
          </List.Item>
        )}
        locale={{ emptyText: 'No active conversations' }}
      />
    </Card>
  );
};

export default RealtimeChatMonitor; 