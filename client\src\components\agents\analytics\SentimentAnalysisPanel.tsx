import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { <PERSON>, <PERSON><PERSON>, <PERSON>own, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, MessageSquare } from "lucide-react";
import { cn } from "@/lib/utils";

interface SentimentAnalysisPanelProps {
  agentId?: string;
  timeRange: string;
  className?: string;
}

const SentimentAnalysisPanel: React.FC<SentimentAnalysisPanelProps> = ({
  agentId,
  timeRange,
  className
}) => {
  const sentimentData = {
    overall: {
      positive: 65.2,
      neutral: 28.1,
      negative: 6.7,
      score: 0.65,
    },
    trends: [
      { date: "2024-01-15", positive: 62, neutral: 30, negative: 8, score: 0.62 },
      { date: "2024-01-16", positive: 64, neutral: 29, negative: 7, score: 0.64 },
      { date: "2024-01-17", positive: 66, neutral: 28, negative: 6, score: 0.66 },
      { date: "2024-01-18", positive: 65, neutral: 28, negative: 7, score: 0.65 },
      { date: "2024-01-19", positive: 67, neutral: 27, negative: 6, score: 0.67 },
      { date: "2024-01-20", positive: 65, neutral: 28, negative: 7, score: 0.65 },
    ],
    topics: [
      { topic: "Product Quality", sentiment: 0.8, mentions: 245, keywords: ["quality", "excellent", "great"] },
      { topic: "Customer Service", sentiment: 0.7, mentions: 189, keywords: ["helpful", "responsive", "support"] },
      { topic: "Pricing", sentiment: 0.3, mentions: 156, keywords: ["expensive", "cost", "price"] },
      { topic: "Delivery", sentiment: 0.6, mentions: 134, keywords: ["fast", "shipping", "delivery"] },
      { topic: "User Interface", sentiment: 0.75, mentions: 98, keywords: ["easy", "intuitive", "design"] },
    ],
    alerts: [
      {
        type: "negative_spike" as const,
        message: "Negative sentiment increased by 15% in pricing discussions",
        severity: "medium" as const,
        timestamp: "2024-01-20T10:30:00Z",
      },
      {
        type: "positive_trend" as const,
        message: "Product quality sentiment trending upward for 3 days",
        severity: "low" as const,
        timestamp: "2024-01-20T09:15:00Z",
      },
    ],
  };

  const getSentimentIcon = (sentiment: number) => {
    if (sentiment > 0.6) return <Smile className="h-4 w-4 text-green-600" />;
    if (sentiment > 0.4) return <Meh className="h-4 w-4 text-yellow-600" />;
    return <Frown className="h-4 w-4 text-red-600" />;
  };

  const getSentimentColor = (sentiment: number) => {
    if (sentiment > 0.6) return "text-green-600";
    if (sentiment > 0.4) return "text-yellow-600";
    return "text-red-600";
  };

  const getAlertIcon = (type: string) => {
    switch (type) {
      case "negative_spike":
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case "positive_trend":
        return <TrendingUp className="h-4 w-4 text-green-500" />;
      default:
        return <MessageSquare className="h-4 w-4 text-blue-500" />;
    }
  };

  const getAlertColor = (severity: string) => {
    switch (severity) {
      case "high":
        return "bg-red-50 border-red-200";
      case "medium":
        return "bg-yellow-50 border-yellow-200";
      case "low":
        return "bg-green-50 border-green-200";
      default:
        return "bg-gray-50 border-gray-200";
    }
  };

  return (
    <div className={cn("space-y-6", className)}>
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2 mb-2">
              <Smile className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium">Positive</span>
            </div>
            <div className="text-2xl font-bold text-green-600">
              {sentimentData.overall.positive.toFixed(1)}%
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2 mb-2">
              <Meh className="h-4 w-4 text-yellow-600" />
              <span className="text-sm font-medium">Neutral</span>
            </div>
            <div className="text-2xl font-bold text-yellow-600">
              {sentimentData.overall.neutral.toFixed(1)}%
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2 mb-2">
              <Frown className="h-4 w-4 text-red-600" />
              <span className="text-sm font-medium">Negative</span>
            </div>
            <div className="text-2xl font-bold text-red-600">
              {sentimentData.overall.negative.toFixed(1)}%
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2 mb-2">
              <TrendingUp className="h-4 w-4 text-blue-600" />
              <span className="text-sm font-medium">Overall Score</span>
            </div>
            <div className="text-2xl font-bold text-blue-600">
              {sentimentData.overall.score.toFixed(2)}
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5 text-primary" />
            Sentiment Distribution
          </CardTitle>
          <CardDescription>
            Overall sentiment breakdown across all conversations
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="flex items-center gap-2">
                  <Smile className="h-4 w-4 text-green-600" />
                  Positive
                </span>
                <span className="font-medium">{sentimentData.overall.positive.toFixed(1)}%</span>
              </div>
              <Progress value={sentimentData.overall.positive} className="h-2" />
            </div>
            
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="flex items-center gap-2">
                  <Meh className="h-4 w-4 text-yellow-600" />
                  Neutral
                </span>
                <span className="font-medium">{sentimentData.overall.neutral.toFixed(1)}%</span>
              </div>
              <Progress value={sentimentData.overall.neutral} className="h-2" />
            </div>
            
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="flex items-center gap-2">
                  <Frown className="h-4 w-4 text-red-600" />
                  Negative
                </span>
                <span className="font-medium">{sentimentData.overall.negative.toFixed(1)}%</span>
              </div>
              <Progress value={sentimentData.overall.negative} className="h-2" />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Topic Sentiment Analysis</CardTitle>
          <CardDescription>Sentiment breakdown by conversation topics</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {sentimentData.topics.map((topic, index) => (
              <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-3">
                  {getSentimentIcon(topic.sentiment)}
                  <div>
                    <h4 className="font-medium">{topic.topic}</h4>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <span>{topic.mentions} mentions</span>
                      <span></span>
                      <span>{topic.keywords.slice(0, 2).join(", ")}</span>
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className={cn("font-medium", getSentimentColor(topic.sentiment))}>
                    {(topic.sentiment * 100).toFixed(0)}%
                  </div>
                  <Badge variant="outline" className="text-xs">
                    {topic.sentiment > 0.6 ? "Positive" : topic.sentiment > 0.4 ? "Neutral" : "Negative"}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Sentiment Alerts</CardTitle>
          <CardDescription>Recent sentiment changes and trends</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {sentimentData.alerts.map((alert, index) => (
              <div key={index} className={cn("p-3 rounded-lg border", getAlertColor(alert.severity))}>
                <div className="flex items-start gap-3">
                  {getAlertIcon(alert.type)}
                  <div className="flex-1">
                    <p className="text-sm font-medium">{alert.message}</p>
                    <p className="text-xs text-muted-foreground mt-1">
                      {new Date(alert.timestamp).toLocaleString()}
                    </p>
                  </div>
                  <Badge variant="outline" className="text-xs">
                    {alert.severity}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SentimentAnalysisPanel;
