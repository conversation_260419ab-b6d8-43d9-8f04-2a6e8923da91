// Define and export AgentType
import { Staff, Team, AISchedulingSettings } from './booking'

export enum AgentType {
  AI_AUTO_REPLY = 'AI_AUTO_REPLY',
  CONTENT_GENERATOR = 'CONTENT_GENERATOR',
  BOOKING_ONSITE = 'BOOKING_ONSITE',
  BOOKING_WALKIN = 'BOOKING_WALKIN',
  LEAD_GENERATION = 'LEAD_GENERATION'
};

// Define AgentType as an object for easier imports, renamed to AgentTypes
export const AgentTypes = {
  AI_AUTO_REPLY: 'AI_AUTO_REPLY' as AgentType,
  CONTENT_GENERATOR: 'CONTENT_GENERATOR' as AgentType,
  BOOKING_ONSITE: 'BOOKING_ONSITE' as AgentType,
  BOOKING_WALKIN: 'BOOKING_WALKIN' as AgentType,
  LEAD_GENERATION: 'LEAD_GENERATION' as AgentType
};

// Define and export AgentStatus
export enum AgentStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  DRAFT = 'DRAFT',
  ERROR = 'ERROR',
  DEPLOYING = 'DEPLOYING'
};

export enum ConfigurationDifficulty {
  BEGINNER = 'beginner',
  INTERMEDIATE = 'intermediate',
  ADVANCED = 'advanced'
};

// Legacy Agent interface for backward compatibility
export interface LegacyAgent {
  id: string;
  name: string;
  agentType: AgentType; // Use the exported AgentType;
  status: AgentStatus; // Use the exported AgentStatus;
  description: string;
  createdAt: string;
  updatedAt: string;
  keyMetrics?: Record<string, any>;
}

// Platform Configuration
export interface PlatformConfiguration {
  enabled: boolean;
  priority: 'high' | 'medium' | 'low';
  responseTimeout: number;
  autoReply: boolean;
  customSettings: Record<string, any>;
}

export enum ConnectionStatus {
  CONNECTED = 'connected',
  CONNECTING = 'connecting',
  DISCONNECTED = 'disconnected',
  ERROR = 'error',
  PENDING = 'pending'
};

// Health Status
export interface HealthStatus {
  status: 'healthy' | 'warning' | 'error' | 'unknown';
  responseTime: number;
  successRate: number;
  lastCheck: Date;
  errorCount: number;
  issues: HealthIssue[];
}

export interface HealthIssue {
  type: 'connectivity' | 'performance' | 'configuration' | 'quota';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  suggestion?: string;
  autoFixable: boolean;
}

// Connected Platform
export interface ConnectedPlatform {
  platformId: string;
  platformName: string;
  isConnected: boolean;
  connectionStatus: ConnectionStatus;
  lastSync: Date;
  configuration: PlatformConfiguration;
  health: HealthStatus;
}

// Performance Metrics
export interface PerformanceMetrics {
  totalInteractions: number;
  successfulResponses: number;
  failedResponses: number;
  averageResponseTime: number;
  userSatisfactionScore: number;
  uptime: number;
  lastUpdated: Date;
}

// Escalation Rules
export interface EscalationRule {
  id: string;
  condition: string;
  action: 'human_handoff' | 'send_notification' | 'custom_workflow';
  threshold: number;
  isActive: boolean;
}

// Integration Settings
export interface IntegrationSettings {
  crmIntegration?: boolean;
  analyticsIntegration?: boolean;
  notificationChannels: string[];
  webhookUrls: string[];
}

// Agent Configuration
export interface AgentConfiguration {
  // General Configuration
  systemPrompt?: string;
  responseMode?: 'direct' | 'draft' | 'collaborative';
  maxTokens?: number;
  temperature?: number;
  
  // Platform-specific Configuration
  platforms: Record<string, PlatformConfiguration>;
  
  // Business Logic Configuration
  businessHours?: string;
  escalationRules?: EscalationRule[];
  autoResponse?: boolean;
  responseTimeout?: number;
  
  // Advanced Configuration
  customFields?: Record<string, any>;
  integrationSettings?: IntegrationSettings;
}

// Core Agent Interface (New, Version)
export interface Agent {
  id: string;
  name: string;
  description?: string;
  type: AgentType;
  status: AgentStatus;
  isActive: boolean;
  platforms: ConnectedPlatform[];
  configuration: AgentConfiguration;
  performance: PerformanceMetrics;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  lastModifiedBy: string;
}

export interface AgentSettings {
  id?: string;
  agentId?: string;
  // Common settings for all agents
  name?: string;
  description?: string;
  active?: boolean;
  // Type-specific settings will be in a config object
  config?: Record<string, any>;
  updatedAt?: string;
  
  // 客服代理相关设置
  type?: string;
  welcomeMessage?: string;
  helpPrompt?: string;
  workingHours?: {
    enabled: boolean;
    startTime?: string;
    endTime?: string;
    timezone?: string;
    offHoursMessage?: string
  };
  responseTime?: {
    maxResponseTime: number;
    timeUnit: "seconds" | "minutes"
  };
  fallbackResponse?: string;
  
  // Knowledge base source type and URL list
  knowledgeSource?: 'url' | 'file';
  knowledgeUrls?: { url: string }[];
  knowledgeBase?: {
    documents: any[];
    settings: any
  };
  llmSettings?: {
    temperature: number;
    maxTokens: number;
    topP: number;
    frequencyPenalty: number;
    presencePenalty: number
  };
  conversationSettings?: {
    saveHistory: boolean;
    maxHistoryLength: number;
    userFeedback: boolean;
    collectMetrics: boolean
  };
  
  // 预约系统共享设置
  bookingConfig?: {
    // 营业时间设置
    businessHours?: {
      enabled: boolean;
      schedule: {
        [day: string]: { start: string; end: string; close: boolean }
      };
      timezone: string
    };
    // 服务类型
    serviceTypes?: Array<{
      id: string;
      name: string;
      description: string;
      duration: number;
      price?: number;
      active: boolean
    }>;
    // 通知模板
    notificationTemplates?: {
      confirmation: string;
      reminder: string;
      cancellation: string
    };
    
    // 团队和人员设置
    staffManagement?: {
      teams: Team[];
      staff: Staff[];
      // 分配策略
      assignmentStrategy: 'client-based' | 'service-based' | 'round-robin' | 'manual'
    };
    
    // AI调度设置
    aiScheduling?: AISchedulingSettings;
    
    // 地图服务配置
    mapService?: {
      provider: 'google' | 'amap' | 'baidu' | 'other';
      apiKey?: string;
      defaultZoom: number;
      defaultCenter: { lat: number; lng: number } };
    
    // 现场排队特定设置
    bookingWalkin?: {
      queueSettings: {
        maxWaitTime: number;
        waitTimeEstimation: "fixed" | "dynamic"
        queueDisplay: boolean;
        checkInMethod: "kiosk" | "staff" | "both"
      };
      ticketTemplate?: string;
      // 员工分配设置
      staffAssignment?: {
        enabled: boolean;
        defaultAssignmentMethod: 'auto' | 'client-choice' // 自动分配或客户选择
        allowSpecificStaff: boolean; // 是否允许指定具体员工
        loadBalancing: boolean; // 是否启用负载均衡
        skillMatching: boolean; // 是否启用技能匹配
      } };
    
    // 在线预约特定设置
    bookingOnsite?: {
      appointmentSettings: {
        leadTime: number;
        bufferTime: number;
        maxFutureBookingDays: number;
        allowRescheduling: boolean;
        cancellationPolicy: {
          deadline: number;
          fee?: number
        } };
      resourceAllocation?: {
        enabled: boolean;
        resources: Array<{
          id: string;
          name: string;
          type: string;
          availability: any
        }>
      };
      // 团队分配设置
      teamAssignment?: {
        enabled: boolean;
        defaultTeam?: string; // 默认团队ID
        allowClientSelection: boolean; // 客户是否可以选择团队
        clientTeamMappings: Array<{ clientId: string; teamId: string }>; // 客户-团队映射
        routeOptimization: boolean; // 是否启用路线优化
        geolocationRequired: boolean; // 是否要求地理位置信息
      } }
} };

export interface AgentData {
  summary: Record<string, any>;
  chartsData: {
    [key: string]: Array<{ date: string; [key: string]: any
  }>
} };

export interface AgentLog {
  id: string;
  agentId: string;
  timestamp: string;
  level: 'INFO' | 'WARNING' | 'ERROR'
  message: string;
  details?: Record<string, any>
};

export interface AgentRule {
  id: string;
  agentId: string;
  priority: number;
  pattern: string;
  response: string;
  isRegex: boolean;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  metadata?: Record<string, any>
};

// Agent Creation Data
export interface CreateAgentRequest {
  name: string;
  description?: string;
  type: AgentType;
  templateId?: string;
  initialConfiguration?: Partial<AgentConfiguration>;
  selectedPlatforms?: string[];
};

// Agent Update Data
export interface UpdateAgentRequest {
  name?: string;
  description?: string;
  configuration?: Partial<AgentConfiguration>;
  platforms?: string[];
  isActive?: boolean
};

// Agent Template
export interface AgentTemplate {
  id: string;
  name: string;
  description: string;
  category: TemplateCategory;
  agentType: AgentType;
  difficulty: ConfigurationDifficulty;
  estimatedSetupTime: string;
  configuration: Partial<AgentConfiguration>;
  recommendedPlatforms: string[];
  useCase: string[];
  benefits: string[];
  tags: string[];
  isPopular: boolean;
  rating: number;
  usageCount: number;
  createdAt: Date;
  updatedAt: Date
};

export enum TemplateCategory {
  CUSTOMER_SERVICE = 'customer-service',
  SALES = 'sales',
  MARKETING = 'marketing',
  SUPPORT = 'support',
  AUTOMATION = 'automation',
  CUSTOM = 'custom'
};

// Business Analysis for Smart Wizard
export interface BusinessAnalysis {
  businessType: string;
  primaryGoals: string[];
  targetAudience: string;
  currentChallenges: string[];
  expectedVolume: number;
  budget: string;
  timeline: string
};

// Wizard Data
export interface WizardData {
  businessAnalysis: BusinessAnalysis;
  selectedAgentType: AgentType;
  selectedPlatforms: string[];
  customConfiguration: Partial<AgentConfiguration>;
  deploymentPreferences: DeploymentPreferences
};

export interface DeploymentPreferences {
  autoActivate: boolean;
  testingPeriod: number;
  monitoringLevel: 'basic' | 'detailed' | 'comprehensive'
  notificationPreferences: string[]
};

// Bulk Operations
export interface BulkOperationRequest {
  agentIds: string[];
  operation: 'activate' | 'deactivate' | 'delete' | 'update_config' | 'clone'
  parameters?: Record<string, any>
};

export interface BulkOperationResult {
  total: number;
  successful: number;
  failed: number;
  results: Array<{
    agentId: string;
    success: boolean;
    error?: string
  }>
};

// Agent Analytics
export interface AgentAnalytics {
  agentId: string;
  timeRange: TimeRange;
  metrics: AnalyticsMetrics;
  trends: TrendData[];
  comparison: ComparisonData[]
};

export interface AnalyticsMetrics {
  totalInteractions: number;
  responseTime: {
    average: number;
    median: number;
    p95: number
  };
  successRate: number;
  userSatisfaction: number;
  platformBreakdown: Record<string, number>;
  hourlyDistribution: number[];
  topQueries: Array<{ query: string; count: number }>
};

export interface TrendData {
  date: string;
  value: number;
  metric: string
};

export interface ComparisonData {
  period: string;
  current: number;
  previous: number;
  change: number;
  changePercent: number
};

export enum TimeRange {
  LAST_24_HOURS = 'last_24_hours',
  LAST_7_DAYS = 'last_7_days',
  LAST_30_DAYS = 'last_30_days',
  LAST_90_DAYS = 'last_90_days',
  CUSTOM = 'custom'
};

// Agent Error Types
export interface AgentError {
  id: string;
  agentId: string;
  platform?: string;
  errorCode: string;
  message: string;
  severity: 'low' | 'medium' | 'high' | 'critical'
  timestamp: Date;
  resolved: boolean;
  autoFixable: boolean
};

export interface ErrorDiagnosis {
  errorId: string;
  diagnosis: string;
  possibleCauses: string[];
  recommendedActions: string[];
  autoFixActions?: string[];
  preventionTips: string[];
  estimatedFixTime: string;
  helpResources: string[]
};