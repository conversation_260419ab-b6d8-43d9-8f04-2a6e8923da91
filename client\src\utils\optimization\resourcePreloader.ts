/**
 * 资源预加载系统
 * 用于预加载不同类型的资源，减少首次使用时的延迟
 */

// 资源预加载选项接口
export interface PreloadOptions {
  /** 资源URL */
  url: string;
  /** 资源类型 */
  type: 'script' | 'style' | 'image' | 'font' | 'fetch';
  /** 可选的资源完整性校验 */
  integrity?: string;
  /** 可选的跨域设置 */
  crossOrigin?: 'anonymous' | 'use-credentials';
  /** 预加载优先级 */
  priority?: 'high' | 'medium' | 'low';
  /** 预加载超时(ms) */
  timeout?: number;
}

/**
 * 资源预加载器
 * 用于预加载关键资源，提升页面性能
 */
class ResourcePreloader {
  private loadedResources: Set<string> = new Set();
  private pendingLoads: Map<string, Promise<void>> = new Map();
  private defaultTimeout: number = 10000; // 10秒默认超时
  private customLoadFunctions: Map<string, () => Promise<boolean>> = new Map();
  
  /**
   * 预加载资源
   * @param options 预加载选项
   * @returns Promise 在加载完成时解析
   */
  public preload(options: PreloadOptions): Promise<void> {
    const { url, type, timeout = this.defaultTimeout } = options;
    
    // 如果资源已加载，立即返回
    if (this.loadedResources.has(url)) {
      return Promise.resolve();
    }
    
    // 如果已经在加载中，返回现有Promise
    if (this.pendingLoads.has(url)) {
      return this.pendingLoads.get(url) as Promise<void>;
    }
    
    // 创建超时Promise
    const timeoutPromise = new Promise<void>((_, reject) => {
      setTimeout(() => reject(new Error(`加载资源超时: ${url}`)), timeout);
    });
    
    // 基于资源类型选择预加载方法
    let loadPromise: Promise<void>;
    
    switch (type) {
      case 'script':
        loadPromise = this.preloadScript(options);
        break;
      case 'style':
        loadPromise = this.preloadStyle(options);
        break;
      case 'image':
        loadPromise = this.preloadImage(options);
        break;
      case 'font':
        loadPromise = this.preloadFont(options);
        break;
      case 'fetch':
      default:
        loadPromise = this.preloadFetch(options);
        break;
    }
    
    // 创建竞争Promise，超时或完成都会触发
    const resultPromise = Promise.race([loadPromise, timeoutPromise])
      .then(() => {
        // 标记为已加载
        this.loadedResources.add(url);
        // 从待处理中移除
        this.pendingLoads.delete(url);
      })
      .catch(error => {
        // 从待处理中移除
        this.pendingLoads.delete(url);
        throw error;
      });
    
    // 添加到待处理加载中
    this.pendingLoads.set(url, resultPromise);
    
    return resultPromise;
  }
  
  /**
   * 预加载脚本
   */
  private preloadScript(options: PreloadOptions): Promise<void> {
    const { url, integrity, crossOrigin } = options;
    
    return new Promise<void>((resolve, reject) => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'script';
      link.href = url;
      
      if (integrity) link.integrity = integrity;
      if (crossOrigin) link.crossOrigin = crossOrigin;
      
      link.onload = () => resolve();
      link.onerror = () => reject(new Error(`脚本预加载失败: ${url}`));
      
      document.head.appendChild(link);
    });
  }
  
  /**
   * 预加载样式
   */
  private preloadStyle(options: PreloadOptions): Promise<void> {
    const { url, integrity, crossOrigin } = options;
    
    return new Promise<void>((resolve, reject) => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'style';
      link.href = url;
      
      if (integrity) link.integrity = integrity;
      if (crossOrigin) link.crossOrigin = crossOrigin;
      
      link.onload = () => resolve();
      link.onerror = () => reject(new Error(`样式预加载失败: ${url}`));
      
      document.head.appendChild(link);
    });
  }
  
  /**
   * 预加载图像
   */
  private preloadImage(options: PreloadOptions): Promise<void> {
    const { url, crossOrigin } = options;
    
    return new Promise<void>((resolve, reject) => {
      const img = new Image();
      
      if (crossOrigin) img.crossOrigin = crossOrigin;
      
      img.onload = () => resolve();
      img.onerror = () => reject(new Error(`图像预加载失败: ${url}`));
      
      img.src = url;
    });
  }
  
  /**
   * 预加载字体
   */
  private preloadFont(options: PreloadOptions): Promise<void> {
    const { url, integrity, crossOrigin } = options;
    
    return new Promise<void>((resolve, reject) => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'font';
      link.href = url;
      link.type = this.getFontMimeType(url);
      
      if (integrity) link.integrity = integrity;
      if (crossOrigin) link.crossOrigin = crossOrigin || 'anonymous'; // 字体通常需要crossorigin
      
      link.onload = () => resolve();
      link.onerror = () => reject(new Error(`字体预加载失败: ${url}`));
      
      document.head.appendChild(link);
    });
  }
  
  /**
   * 获取字体MIME类型
   */
  private getFontMimeType(url: string): string {
    const extension = url.split('.').pop()?.toLowerCase();
    
    switch (extension) {
      case 'woff':
        return 'font/woff';
      case 'woff2':
        return 'font/woff2';
      case 'ttf':
        return 'font/ttf';
      case 'otf':
        return 'font/otf';
      case 'eot':
        return 'application/vnd.ms-fontobject';
      default:
        return 'font/woff2'; // 默认为woff2
    }
  }
  
  /**
   * 预加载通用资源（使用fetch）
   */
  private preloadFetch(options: PreloadOptions): Promise<void> {
    const { url } = options;
    
    return fetch(url, { 
      method: 'HEAD',
      mode: 'no-cors',
      cache: 'force-cache'
    })
    .then(() => {
      // 请求成功，资源已预加载
    });
  }
  
  /**
   * 检查资源是否已加载
   */
  public isLoaded(url: string): boolean {
    return this.loadedResources.has(url);
  }
  
  /**
   * 检查资源是否正在加载
   */
  public isLoading(url: string): boolean {
    return this.pendingLoads.has(url);
  }
  
  /**
   * 清理缓存
   */
  public clearCache(): void {
    this.loadedResources.clear();
    this.pendingLoads.clear();
  }
  
  /**
   * 批量预加载
   */
  public preloadBatch(options: PreloadOptions[]): Promise<void[]> {
    return Promise.all(options.map(opt => this.preload(opt)));
  }
  
  /**
   * 注册自定义加载函数
   */
  public registerLoadFunction(resourceId: string, loadFn: () => Promise<boolean>): void {
    this.customLoadFunctions.set(resourceId, loadFn);
  }
}

// 导出实例
export const resourcePreloader = new ResourcePreloader();
export default resourcePreloader;