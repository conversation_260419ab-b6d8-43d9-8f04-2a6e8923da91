# 终端处理指南

## 问题：Ctrl+C 导致终端自动终止

在Windows环境下使用PowerShell终端时，按下Ctrl+C组合键通常会导致以下错误消息：

```
终端进程"C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe"已终止，退出代码: 2。
```

这是因为Ctrl+C在PowerShell中默认是终止当前进程的信号，而不仅仅是中断命令。

## 解决方案

### 方法1：使用不同的终端模拟器

推荐使用Windows Terminal，它提供了更好的终端体验：

1. 从Microsoft Store安装[Windows Terminal](https://aka.ms/terminal)
2. 在VSCode中设置为默认终端:
   - 打开设置 (`Ctrl+,`)
   - 搜索"terminal.integrated.defaultProfile.windows"
   - 将其设置为"Windows Terminal"或"PowerShell"

### 方法2：修改PowerShell设置

创建PowerShell配置文件以改变Ctrl+C的行为：

1. 打开PowerShell
2. 运行`notepad $PROFILE`创建/编辑配置文件
3. 添加以下内容：

```powershell
# 更改Ctrl+C行为，避免终止终端
Set-PSReadlineKeyHandler -Key Ctrl+C -Function CopyOrCancelLine
```

4. 保存文件并重启PowerShell

### 方法3：使用命令行参数

启动PowerShell时添加`-NoExit`参数，这样Ctrl+C不会完全退出PowerShell：

```
powershell.exe -NoExit
```

在VSCode中，可以在settings.json中配置：

```json
"terminal.integrated.profiles.windows": {
  "PowerShell": {
    "source": "PowerShell",
    "args": ["-NoExit"]
  }
}
```

### 方法4：使用VSCode任务

创建一个任务，使用cmd.exe而不是PowerShell来运行命令：

```json
{
  "version": "2.0.0",
  "tasks": [
    {
      "label": "Run with CMD",
      "type": "shell",
      "command": "cmd.exe",
      "args": ["/c", "your-command-here"],
      "problemMatcher": []
    }
  ]
}
```

## 注意事项

- Ctrl+C在大多数UNIX/Linux系统中用于发送SIGINT信号，通常只中断命令而不终止终端
- Windows PowerShell默认行为是不同的，按Ctrl+C会终止整个PowerShell进程
- 如果你需要真正中断命令而不是复制文本，可以使用Ctrl+Break键 