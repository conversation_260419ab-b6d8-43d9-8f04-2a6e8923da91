/**
 * Service Worker 注册和管理工具
 * 提供Service Worker的注册、更新、通信等功能
 */

// 检查浏览器是否支持Service Worker
export const isServiceWorkerSupported = (): boolean => {
  return 'serviceWorker' in navigator && 'PushManager' in window;
};

// Service Worker注册选项
export interface ServiceWorkerOptions {
  /** Service Worker脚本路径 */
  swPath?: string;
  /** 是否在注册完成后立即控制页面 */
  immediate?: boolean;
  /** 自动更新检查间隔(毫秒)，0表示禁用自动检查 */
  updateCheckInterval?: number;
  /** 注册成功回调 */
  onSuccess?: (registration: ServiceWorkerRegistration) => void;
  /** 注册失败回调 */
  onError?: (error: Error) => void;
  /** 发现更新回调 */
  onUpdate?: (registration: ServiceWorkerRegistration) => void;
  /** 调试模式 */
  debug?: boolean;
};

// 默认选项
const DEFAULT_OPTIONS: ServiceWorkerOptions = {
  swPath: '/serviceWorker.js',
  immediate: false,
  updateCheckInterval: 60 * 60 * 1000, // 1小时
  debug: process.env.NODE_ENV === 'development'
};

// 全局Service Worker注册对象
let swRegistration: ServiceWorkerRegistration | null = null;
let updateCheckInterval: number | null = null;

/**
 * 注册Service Worker
 * @param options 选项
 * @returns Promise<ServiceWorkerRegistration>
 */
export async function registerServiceWorker(
  options: ServiceWorkerOptions = {}
): Promise<ServiceWorkerRegistration | null> {
  // 合并选项
  const opts = { ...DEFAULT_OPTIONS, ...options };
  
  if (!isServiceWorkerSupported()) {
    console.warn('浏览器不支持Service Worker');
    return null;
  }
  
  try {
    // 注册Service Worker
    const registration = await navigator.serviceWorker.register(opts.swPath!, {
      scope: '/'
    });
    
    swRegistration = registration;
    
    if (opts.debug) {
      console.log('Service Worker 注册成功:', registration);
    }
    
    // 如果需要立即控制页面
    if (opts.immediate && registration.active) {
      await forceActivateServiceWorker(registration);
    }
    
    // 检查更新
    await checkForUpdates(registration, opts);
    
    // 设置自动更新检查
    if (opts.updateCheckInterval && opts.updateCheckInterval > 0) {
      startUpdateCheck(opts.updateCheckInterval, opts);
    }
    
    // 触发成功回调
    if (opts.onSuccess) {
      opts.onSuccess(registration);
    }
    
    return registration;
  } catch (error) {
    console.error('Service Worker 注册失败:', error);
    
    // 触发错误回调
    if (opts.onError) {
      opts.onError(error instanceof Error ? error : new Error(String(error)));
    }
    
    return null;
  }
}

/**
 * 强制Service Worker激活并控制页面
 * @param registration Service Worker注册对象
 */
export async function forceActivateServiceWorker(
  registration: ServiceWorkerRegistration
): Promise<void> {
  if (!registration.active) {
    return;
  }
  
  // 更新客户端
  await registration.update();
  
  // 如果存在等待中的Service Worker，激活它
  if (registration.waiting) {
    // 使用postMessage通知Service Worker跳过等待
    registration.waiting.postMessage({ type: 'SKIP_WAITING' });
  }
}

/**
 * 检查Service Worker更新
 * @param registration Service Worker注册对象
 * @param options 选项
 */
export async function checkForUpdates(
  registration: ServiceWorkerRegistration,
  options: ServiceWorkerOptions
): Promise<boolean> {
  try {
    // 尝试更新Service Worker
    await registration.update();
    
    // 检查是否有新版本
    if (registration.waiting) {
      if (options.debug) {
        console.log('Service Worker 有新版本可用');
      }
      
      // 触发更新回调
      if (options.onUpdate) {
        options.onUpdate(registration);
      }
      
      return true;
    }
    
    return false;
  } catch (error) {
    console.error('Service Worker 更新检查失败:', error);
    return false;
  }
}

/**
 * 启动自动更新检查
 * @param interval 检查间隔(毫秒)
 * @param options 选项
 */
export function startUpdateCheck(
  interval: number,
  options: ServiceWorkerOptions
): void {
  // 清除现有计时器
  stopUpdateCheck();
  
  // 设置新计时器
  updateCheckInterval = window.setInterval(async () => {
    if (swRegistration) {
      if (options.debug) {
        console.log('Service Worker 执行定期更新检查');
      }
      
      await checkForUpdates(swRegistration, options);
    }
  }, interval);
  
  if (options.debug) {
    console.log(`Service Worker 自动更新检查已启动，间隔: ${interval}ms`);
  }
}

/**
 * 停止自动更新检查
 */
export function stopUpdateCheck(): void {
  if (updateCheckInterval !== null) {
    window.clearInterval(updateCheckInterval);
    updateCheckInterval = null;
  }
}

/**
 * 向Service Worker发送消息
 * @param message 消息对象
 * @returns Promise<any> 响应数据
 */
export async function sendMessageToServiceWorker(message: any): Promise<any> {
  if (!swRegistration) {
    throw new Error('没有活动的Service Worker注册');
  }
  
  if (!swRegistration.active) {
    throw new Error('Service Worker不处于活动状态');
  }
  
  return new Promise((resolve, reject) => {
    // 创建MessageChannel
    const messageChannel = new MessageChannel();
    
    // 设置接收响应的处理程序
    messageChannel.port1.onmessage = (event) => {
      if (event.data && event.data.error) {
        reject(new Error(event.data.error));
      } else {
        resolve(event.data);
      }
    };
    
    // 发送消息
    swRegistration.active!.postMessage(message, [messageChannel.port2]);
    
    // 设置超时
    setTimeout(() => {
      reject(new Error('Service Worker消息响应超时'));
    }, 10000);
  });
}

/**
 * 清除所有缓存
 * @returns Promise<boolean> 是否成功
 */
export async function clearCache(): Promise<boolean> {
  try {
    const cacheNames = await caches.keys();
    await Promise.all(
      cacheNames.map(cacheName => caches.delete(cacheName))
    );
    console.log('所有缓存已清除');
    return true;
  } catch (error) {
    console.error('清除缓存失败:', error);
    return false;
  }
}

/**
 * 更新缓存
 * @returns Promise<boolean> 是否成功
 */
export async function updateCache(): Promise<boolean> {
  try {
    if (swRegistration) {
      await swRegistration.update();
      console.log('缓存更新完成');
      return true;
    }
    return false;
  } catch (error) {
    console.error('缓存更新失败:', error);
    return false;
  }
}

/**
 * 获取Service Worker注册对象
 * @returns Service Worker注册对象或null
 */
export function getServiceWorkerRegistration(): ServiceWorkerRegistration | null {
  return swRegistration;
}

/**
 * 注销Service Worker
 * @returns Promise<boolean> 是否成功
 */
export async function unregisterServiceWorker(): Promise<boolean> {
  try {
    if (swRegistration) {
      await swRegistration.unregister();
      swRegistration = null;
      console.log('Service Worker 已注销');
      return true;
    }
    return false;
  } catch (error) {
    console.error('Service Worker 注销失败:', error);
    return false;
  }
}

/**
 * 检查是否有Service Worker在控制页面
 * @returns boolean
 */
export function isServiceWorkerControlling(): boolean {
  return !!navigator.serviceWorker.controller;
}

/**
 * 等待Service Worker控制页面
 * @param timeout 超时时间(毫秒)，默认10秒
 * @returns Promise<boolean>
 */
export async function waitForServiceWorkerControl(timeout = 10000): Promise<boolean> {
  if (isServiceWorkerControlling()) {
    return true;
  }
  
  return new Promise((resolve) => {
    const timeoutId = setTimeout(() => {
      resolve(false);
    }, timeout);
    
    navigator.serviceWorker.addEventListener('controllerchange', function handler() {
      navigator.serviceWorker.removeEventListener('controllerchange', handler);
      clearTimeout(timeoutId);
      resolve(true);
    });
  });
}

const serviceWorkerRegistration = {
  isServiceWorkerSupported,
  registerServiceWorker,
  forceActivateServiceWorker,
  checkForUpdates,
  startUpdateCheck,
  stopUpdateCheck,
  sendMessageToServiceWorker,
  clearCache,
  updateCache,
  getServiceWorkerRegistration,
  unregisterServiceWorker,
  isServiceWorkerControlling,
  waitForServiceWorkerControl
};

export default serviceWorkerRegistration;