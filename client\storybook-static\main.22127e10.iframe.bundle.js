(self.webpackChunkclient=self.webpackChunkclient||[]).push([[792],{"./.storybook/preview.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{"use strict";__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{decorators:()=>decorators,default:()=>_storybook_preview,globalTypes:()=>globalTypes});__webpack_require__("./node_modules/react/index.js");var styled_components_browser_esm=__webpack_require__("./node_modules/styled-components/dist/styled-components.browser.esm.js");const theme={colors:{primary:"#5c6ac4",secondary:"#6c757d",background:"#f5f7fa",surface:"#ffffff",text:"#212529",textLight:"#ffffff",error:"#e74c3c",warning:"#f39c12",success:"#2ecc71"},fontSizes:{small:"0.875rem",base:"1rem",large:"1.25rem",xlarge:"1.5rem"},radii:{small:"4px",base:"8px",large:"16px"},shadows:{card:"0 2px 8px rgba(0,0,0,0.1)",dropdown:"0 4px 16px rgba(0,0,0,0.1)"}};var chunk_AYJ5UCUI=__webpack_require__("./node_modules/react-router/dist/development/chunk-AYJ5UCUI.mjs"),AuthContext=__webpack_require__("./src/context/AuthContext.js"),jsx_runtime=__webpack_require__("./node_modules/react/jsx-runtime.js");const mockAuthContextValue={session:{},user:{id:"mock-user-id",email:"<EMAIL>",plan:"free"},isAuthenticated:!0,loading:!1,login:()=>Promise.resolve({success:!0}),register:()=>Promise.resolve({success:!0}),logout:()=>Promise.resolve(),signInWithGoogle:()=>Promise.resolve(),updatePlan:()=>Promise.resolve({success:!0})},globalTypes={theme:{name:"Theme",description:"全局主题",defaultValue:"default",toolbar:{icon:"circlehollow",items:[{value:"default",title:"默认主题"}]}},auth:{name:"Authentication",description:"认证状态",defaultValue:"authenticated",toolbar:{icon:"user",items:[{value:"authenticated",title:"已登录"},{value:"unauthenticated",title:"未登录"},{value:"loading",title:"加载中"}]}}},decorators=[(Story,context)=>{const authState=context.globals.auth,authContextValue={...mockAuthContextValue,isAuthenticated:"authenticated"===authState,loading:"loading"===authState,user:"authenticated"===authState?mockAuthContextValue.user:null};return(0,jsx_runtime.jsx)(styled_components_browser_esm.NP,{theme,children:(0,jsx_runtime.jsx)(AuthContext.cy.Provider,{value:authContextValue,children:(0,jsx_runtime.jsx)(chunk_AYJ5UCUI.fS,{children:(0,jsx_runtime.jsx)(Story,{})})})})}],_storybook_preview={parameters:{controls:{matchers:{color:/(background|color)$/i,date:/Date$/i}},interactions:{disable:!1,controls:{expanded:!0}},a11y:{options:{runOnly:{type:"tag",values:["wcag2a","wcag2aa"]}},manual:!0},jest:{disableAutoDeps:!0}}}},"./node_modules/@storybook/instrumenter/dist sync recursive":module=>{function webpackEmptyContext(req){var e=new Error("Cannot find module '"+req+"'");throw e.code="MODULE_NOT_FOUND",e}webpackEmptyContext.keys=()=>[],webpackEmptyContext.resolve=webpackEmptyContext,webpackEmptyContext.id="./node_modules/@storybook/instrumenter/dist sync recursive",module.exports=webpackEmptyContext},"./node_modules/@storybook/test/dist sync recursive":module=>{function webpackEmptyContext(req){var e=new Error("Cannot find module '"+req+"'");throw e.code="MODULE_NOT_FOUND",e}webpackEmptyContext.keys=()=>[],webpackEmptyContext.resolve=webpackEmptyContext,webpackEmptyContext.id="./node_modules/@storybook/test/dist sync recursive",module.exports=webpackEmptyContext},"./src lazy recursive ^\\.\\/.*$ include: (?%21.*node_modules)(?:[\\\\/]src(?:[\\\\/](?%21\\.)(?:(?:(?%21(?:^%7C[\\\\/])\\.).)*?)[\\\\/]%7C[\\\\/]%7C$)(?%21\\.)(?=.)[^\\\\/]*?\\.mdx)$":(module,__unused_webpack_exports,__webpack_require__)=>{var map={"./stories/Accessibility.mdx":["./src/stories/Accessibility.mdx",961,844,967],"./stories/Configure.mdx":["./src/stories/Configure.mdx",961,844,568],"./stories/UIComponents.mdx":["./src/stories/UIComponents.mdx",961,844,871]};function webpackAsyncContext(req){if(!__webpack_require__.o(map,req))return Promise.resolve().then((()=>{var e=new Error("Cannot find module '"+req+"'");throw e.code="MODULE_NOT_FOUND",e}));var ids=map[req],id=ids[0];return Promise.all(ids.slice(1).map(__webpack_require__.e)).then((()=>__webpack_require__(id)))}webpackAsyncContext.keys=()=>Object.keys(map),webpackAsyncContext.id="./src lazy recursive ^\\.\\/.*$ include: (?%21.*node_modules)(?:[\\\\/]src(?:[\\\\/](?%21\\.)(?:(?:(?%21(?:^%7C[\\\\/])\\.).)*?)[\\\\/]%7C[\\\\/]%7C$)(?%21\\.)(?=.)[^\\\\/]*?\\.mdx)$",module.exports=webpackAsyncContext},"./src lazy recursive ^\\.\\/.*$ include: (?%21.*node_modules)(?:[\\\\/]src(?:[\\\\/](?%21\\.)(?:(?:(?%21(?:^%7C[\\\\/])\\.).)*?)[\\\\/]%7C[\\\\/]%7C$)(?%21\\.)(?=.)[^\\\\/]*?\\.stories\\.(js%7Cjsx%7Cmjs%7Cts%7Ctsx))$":(module,__unused_webpack_exports,__webpack_require__)=>{var map={"./components/AuthWrapper.stories":["./src/components/AuthWrapper.stories.jsx",619],"./components/AuthWrapper.stories.jsx":["./src/components/AuthWrapper.stories.jsx",619],"./components/dashboard/AutoReplyDashboard.stories":["./src/components/dashboard/AutoReplyDashboard.stories.jsx",961,284,857,42,758,628],"./components/dashboard/AutoReplyDashboard.stories.jsx":["./src/components/dashboard/AutoReplyDashboard.stories.jsx",961,284,857,42,758,628],"./components/layouts/DataCenterLayout.stories":["./src/components/layouts/DataCenterLayout.stories.jsx",411],"./components/layouts/DataCenterLayout.stories.jsx":["./src/components/layouts/DataCenterLayout.stories.jsx",411],"./components/layouts/SettingsCenterLayout.stories":["./src/components/layouts/SettingsCenterLayout.stories.jsx",452],"./components/layouts/SettingsCenterLayout.stories.jsx":["./src/components/layouts/SettingsCenterLayout.stories.jsx",452],"./components/ui/alert.stories":["./src/components/ui/alert.stories.tsx",681,603],"./components/ui/alert.stories.tsx":["./src/components/ui/alert.stories.tsx",681,603],"./components/ui/button.stories":["./src/components/ui/button.stories.tsx",681,187],"./components/ui/button.stories.tsx":["./src/components/ui/button.stories.tsx",681,187],"./components/ui/card.stories":["./src/components/ui/card.stories.tsx",681,421],"./components/ui/card.stories.tsx":["./src/components/ui/card.stories.tsx",681,421],"./components/ui/dialog.stories":["./src/components/ui/dialog.stories.tsx",961,681,721,0],"./components/ui/dialog.stories.tsx":["./src/components/ui/dialog.stories.tsx",961,681,721,0],"./components/ui/select.stories":["./src/components/ui/select.stories.tsx",961,681,721,686,983],"./components/ui/select.stories.tsx":["./src/components/ui/select.stories.tsx",961,681,721,686,983],"./components/ui/visually-hidden.stories":["./src/components/ui/visually-hidden.stories.tsx",681,467],"./components/ui/visually-hidden.stories.tsx":["./src/components/ui/visually-hidden.stories.tsx",681,467],"./pages/AppointmentMapPage.stories":["./src/pages/AppointmentMapPage.stories.jsx",961,284,253,972,283,8],"./pages/AppointmentMapPage.stories.jsx":["./src/pages/AppointmentMapPage.stories.jsx",961,284,253,972,283,8],"./pages/BrandSettingsPage.stories":["./src/pages/BrandSettingsPage.stories.jsx",961,284,857,253,816,427],"./pages/BrandSettingsPage.stories.jsx":["./src/pages/BrandSettingsPage.stories.jsx",961,284,857,253,816,427],"./pages/ContentAgentPage.stories":["./src/pages/ContentAgentPage.stories.jsx",961,742,241],"./pages/ContentAgentPage.stories.jsx":["./src/pages/ContentAgentPage.stories.jsx",961,742,241],"./pages/PlatformApiManagement.stories":["./src/pages/PlatformApiManagement.stories.jsx",961,284,857,253,816,972,847,758,532,332],"./pages/PlatformApiManagement.stories.jsx":["./src/pages/PlatformApiManagement.stories.jsx",961,284,857,253,816,972,847,758,532,332],"./stories/Button.stories":["./src/stories/Button.stories.js",791],"./stories/Button.stories.js":["./src/stories/Button.stories.js",791],"./stories/Header.stories":["./src/stories/Header.stories.js",512],"./stories/Header.stories.js":["./src/stories/Header.stories.js",512],"./stories/Page.stories":["./src/stories/Page.stories.js",290],"./stories/Page.stories.js":["./src/stories/Page.stories.js",290],"./stories/UserMenu.stories":["./src/stories/UserMenu.stories.jsx",816,42,847,353,899],"./stories/UserMenu.stories.jsx":["./src/stories/UserMenu.stories.jsx",816,42,847,353,899]};function webpackAsyncContext(req){if(!__webpack_require__.o(map,req))return Promise.resolve().then((()=>{var e=new Error("Cannot find module '"+req+"'");throw e.code="MODULE_NOT_FOUND",e}));var ids=map[req],id=ids[0];return Promise.all(ids.slice(1).map(__webpack_require__.e)).then((()=>__webpack_require__(id)))}webpackAsyncContext.keys=()=>Object.keys(map),webpackAsyncContext.id="./src lazy recursive ^\\.\\/.*$ include: (?%21.*node_modules)(?:[\\\\/]src(?:[\\\\/](?%21\\.)(?:(?:(?%21(?:^%7C[\\\\/])\\.).)*?)[\\\\/]%7C[\\\\/]%7C$)(?%21\\.)(?=.)[^\\\\/]*?\\.stories\\.(js%7Cjsx%7Cmjs%7Cts%7Ctsx))$",module.exports=webpackAsyncContext},"./src/context/AuthContext.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{"use strict";__webpack_require__.d(__webpack_exports__,{cy:()=>AuthContext,As:()=>useAuth});var react=__webpack_require__("./node_modules/react/index.js"),dist_module=__webpack_require__("./node_modules/@supabase/supabase-js/dist/module/index.js"),process=__webpack_require__("./node_modules/process/browser.js");const supabaseUrl=process.env.REACT_APP_SUPABASE_URL||"https://mgheimlvdvazclsutmzf.supabase.co",supabaseAnonKey=process.env.REACT_APP_SUPABASE_ANON_KEY||"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1naGVpbWx2ZHZhemNsc3V0bXpmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM1OTM1MDAsImV4cCI6MjA1OTE2OTUwMH0.QfcKOfYqVqRB1UshudDO5VmsV7qx3bT0tPItLrY9Vno";supabaseUrl&&"https://mgheimlvdvazclsutmzf.supabase.co"!==supabaseUrl&&supabaseAnonKey&&"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1naGVpbWx2ZHZhemNsc3V0bXpmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM1OTM1MDAsImV4cCI6MjA1OTE2OTUwMH0.QfcKOfYqVqRB1UshudDO5VmsV7qx3bT0tPItLrY9Vno"!==supabaseAnonKey||console.warn("Supabase URL or Anon Key is not configured. Please create a .env file in the client directory with REACT_APP_SUPABASE_URL and REACT_APP_SUPABASE_ANON_KEY.");(0,dist_module.UU)(supabaseUrl,supabaseAnonKey);__webpack_require__("./node_modules/react-router/dist/development/chunk-AYJ5UCUI.mjs"),__webpack_require__("./node_modules/react/jsx-runtime.js");const AuthContext=(0,react.createContext)(),useAuth=()=>(0,react.useContext)(AuthContext)},"./storybook-config-entry.js":(__unused_webpack_module,__unused_webpack___webpack_exports__,__webpack_require__)=>{"use strict";var external_STORYBOOK_MODULE_CHANNELS_=__webpack_require__("storybook/internal/channels"),csf=__webpack_require__("./node_modules/@storybook/core/dist/csf/index.js"),external_STORYBOOK_MODULE_PREVIEW_API_=__webpack_require__("storybook/internal/preview-api"),external_STORYBOOK_MODULE_GLOBAL_=__webpack_require__("@storybook/global");const importers=[async path=>{if(!/^\.[\\/](?:src(?:[\\/](?!\.)(?:(?:(?!(?:^|[\\/])\.).)*?)[\\/]|[\\/]|$)(?!\.)(?=.)[^\\/]*?\.mdx)$/.exec(path))return;const pathRemainder=path.substring(6);return __webpack_require__("./src lazy recursive ^\\.\\/.*$ include: (?%21.*node_modules)(?:[\\\\/]src(?:[\\\\/](?%21\\.)(?:(?:(?%21(?:^%7C[\\\\/])\\.).)*?)[\\\\/]%7C[\\\\/]%7C$)(?%21\\.)(?=.)[^\\\\/]*?\\.mdx)$")("./"+pathRemainder)},async path=>{if(!/^\.[\\/](?:src(?:[\\/](?!\.)(?:(?:(?!(?:^|[\\/])\.).)*?)[\\/]|[\\/]|$)(?!\.)(?=.)[^\\/]*?\.stories\.(js|jsx|mjs|ts|tsx))$/.exec(path))return;const pathRemainder=path.substring(6);return __webpack_require__("./src lazy recursive ^\\.\\/.*$ include: (?%21.*node_modules)(?:[\\\\/]src(?:[\\\\/](?%21\\.)(?:(?:(?%21(?:^%7C[\\\\/])\\.).)*?)[\\\\/]%7C[\\\\/]%7C$)(?%21\\.)(?=.)[^\\\\/]*?\\.stories\\.(js%7Cjsx%7Cmjs%7Cts%7Ctsx))$")("./"+pathRemainder)}];const channel=(0,external_STORYBOOK_MODULE_CHANNELS_.createBrowserChannel)({page:"preview"});external_STORYBOOK_MODULE_PREVIEW_API_.addons.setChannel(channel),"DEVELOPMENT"===external_STORYBOOK_MODULE_GLOBAL_.global.CONFIG_TYPE&&(window.__STORYBOOK_SERVER_CHANNEL__=channel);const preview=new external_STORYBOOK_MODULE_PREVIEW_API_.PreviewWeb((async function importFn(path){for(let i=0;i<importers.length;i++){const moduleExports=await(x=()=>importers[i](path),x());if(moduleExports)return moduleExports}var x}),(()=>{const previewAnnotations=[__webpack_require__("./node_modules/@storybook/react/dist/entry-preview.mjs"),__webpack_require__("./node_modules/@storybook/react/dist/entry-preview-docs.mjs"),__webpack_require__("./node_modules/@storybook/addon-essentials/dist/actions/preview.mjs"),__webpack_require__("./node_modules/@storybook/addon-essentials/dist/docs/preview.mjs"),__webpack_require__("./node_modules/@storybook/addon-essentials/dist/backgrounds/preview.mjs"),__webpack_require__("./node_modules/@storybook/addon-essentials/dist/viewport/preview.mjs"),__webpack_require__("./node_modules/@storybook/addon-essentials/dist/measure/preview.mjs"),__webpack_require__("./node_modules/@storybook/addon-essentials/dist/outline/preview.mjs"),__webpack_require__("./node_modules/@storybook/addon-essentials/dist/highlight/preview.mjs"),__webpack_require__("./node_modules/@storybook/addon-interactions/dist/preview.mjs"),__webpack_require__("./.storybook/preview.js")],userPreview=previewAnnotations[previewAnnotations.length-1]?.default;return(0,csf.bU)(userPreview)?userPreview.composed:(0,external_STORYBOOK_MODULE_PREVIEW_API_.composeConfigs)(previewAnnotations)}));window.__STORYBOOK_PREVIEW__=preview,window.__STORYBOOK_STORY_STORE__=preview.storyStore,window.__STORYBOOK_ADDONS_CHANNEL__=channel},"@storybook/global":module=>{"use strict";module.exports=__STORYBOOK_MODULE_GLOBAL__},"storybook/internal/channels":module=>{"use strict";module.exports=__STORYBOOK_MODULE_CHANNELS__},"storybook/internal/client-logger":module=>{"use strict";module.exports=__STORYBOOK_MODULE_CLIENT_LOGGER__},"storybook/internal/core-events":module=>{"use strict";module.exports=__STORYBOOK_MODULE_CORE_EVENTS__},"storybook/internal/preview-api":module=>{"use strict";module.exports=__STORYBOOK_MODULE_PREVIEW_API__},"storybook/internal/preview-errors":module=>{"use strict";module.exports=__STORYBOOK_MODULE_CORE_EVENTS_PREVIEW_ERRORS__}},__webpack_require__=>{__webpack_require__.O(0,[777],(()=>{return moduleId="./storybook-config-entry.js",__webpack_require__(__webpack_require__.s=moduleId);var moduleId}));__webpack_require__.O()}]);