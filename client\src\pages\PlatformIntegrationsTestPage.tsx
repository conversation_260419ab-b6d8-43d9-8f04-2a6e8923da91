import React from 'react'
import { PlatformIntegrationsDemo } from '@/components/examples/platform-integrations-demo'

export default function PlatformIntegrationsTestPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 p-8">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            平台集成图标云
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300">
            展示我们支持集成的平台，包括 Lazada 和 Webhook
          </p>
        </div>
        
        <div className="flex justify-center">
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8">
            <PlatformIntegrationsDemo />
          </div>
        </div>
        
        <div className="mt-8 text-center">
          <p className="text-sm text-gray-500 dark:text-gray-400">
            互动图标云 - 鼠标悬停查看平台名称，点击可旋转
          </p>
        </div>
      </div>
    </div>
  )
} 