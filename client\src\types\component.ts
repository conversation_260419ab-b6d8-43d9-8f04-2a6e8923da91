/**
 * 组件接口规范类型定义
 * 定义通用组件接口和属性规范
 */

import type { ReactNode, CSSProperties, ReactElement } from 'react';
import type { ThemeMode } from './design-system';

// 基础组件属性
export interface BaseComponentProps {
  className?: string;
  style?: CSSProperties;
  children?: ReactNode;
  id?: string;
  'data-testid'?: string;
}

// 尺寸规范
export type ComponentSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';

// 变体规范
export type ComponentVariant = 
  | 'primary' 
  | 'secondary' 
  | 'success' 
  | 'warning' 
  | 'error' 
  | 'info' 
  | 'ghost' 
  | 'outline' 
  | 'solid';

// 颜色方案
export type ColorScheme = 
  | 'primary'
  | 'secondary' 
  | 'success'
  | 'warning'
  | 'error'
  | 'info'
  | 'neutral';

// 响应式断点
export type Breakpoint = 'sm' | 'md' | 'lg' | 'xl' | '2xl';

// 响应式值
export type ResponsiveValue<T> = T | Partial<Record<Breakpoint, T>>;

// 按钮组件接口
export interface ButtonProps extends BaseComponentProps {
  variant?: ComponentVariant;
  size?: ComponentSize;
  colorScheme?: ColorScheme;
  isDisabled?: boolean;
  isLoading?: boolean;
  loadingText?: string;
  leftIcon?: ReactNode;
  rightIcon?: ReactNode;
  fullWidth?: boolean;
  onClick?: (event: React.MouseEvent<HTMLButtonElement>) => void;
  type?: 'button' | 'submit' | 'reset';
  form?: string;
}

// 输入框组件接口
export interface InputProps extends BaseComponentProps {
  variant?: 'outline' | 'filled' | 'flushed' | 'unstyled';
  size?: ComponentSize;
  colorScheme?: ColorScheme;
  placeholder?: string;
  value?: string;
  defaultValue?: string;
  isDisabled?: boolean;
  isInvalid?: boolean;
  isRequired?: boolean;
  isReadOnly?: boolean;
  leftElement?: ReactNode;
  rightElement?: ReactNode;
  onChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onFocus?: (event: React.FocusEvent<HTMLInputElement>) => void;
  onBlur?: (event: React.FocusEvent<HTMLInputElement>) => void;
  type?: 'text' | 'password' | 'email' | 'number' | 'tel' | 'url' | 'search';
}

// 卡片组件接口
export interface CardProps extends BaseComponentProps {
  variant?: 'elevated' | 'outline' | 'filled' | 'ghost';
  size?: ComponentSize;
  padding?: ResponsiveValue<string>;
  isHoverable?: boolean;
  isClickable?: boolean;
  onClick?: (event: React.MouseEvent<HTMLDivElement>) => void;
}

// 模态框组件接口
export interface ModalProps extends BaseComponentProps {
  isOpen: boolean;
  onClose: () => void;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'full';
  isCentered?: boolean;
  closeOnOverlayClick?: boolean;
  closeOnEsc?: boolean;
  motionPreset?: 'slideInBottom' | 'slideInTop' | 'scale' | 'none';
  initialFocusRef?: React.RefObject<HTMLElement>;
  finalFocusRef?: React.RefObject<HTMLElement>;
}

// 弹出提示组件接口
export interface TooltipProps extends BaseComponentProps {
  label: string;
  placement?: 'top' | 'bottom' | 'left' | 'right';
  hasArrow?: boolean;
  isDisabled?: boolean;
  openDelay?: number;
  closeDelay?: number;
  offset?: [number, number];
}

// 表格组件接口
export interface TableProps extends BaseComponentProps {
  variant?: 'simple' | 'striped' | 'unstyled';
  size?: ComponentSize;
  colorScheme?: ColorScheme;
  isHoverable?: boolean;
  layout?: 'fixed' | 'auto';
}

// 表单组件接口
export interface FormControlProps extends BaseComponentProps {
  isRequired?: boolean;
  isDisabled?: boolean;
  isInvalid?: boolean;
  isReadOnly?: boolean;
}

export interface FormLabelProps extends BaseComponentProps {
  htmlFor?: string;
  requiredIndicator?: ReactNode;
}

export interface FormErrorMessageProps extends BaseComponentProps {
  // 错误信息内容
}

export interface FormHelperTextProps extends BaseComponentProps {
  // 帮助文本内容
}

// 导航组件接口
export interface NavItemProps extends BaseComponentProps {
  href?: string;
  isActive?: boolean;
  isDisabled?: boolean;
  icon?: ReactNode;
  badge?: string | number;
  onClick?: (event: React.MouseEvent) => void;
}

export interface BreadcrumbProps extends BaseComponentProps {
  separator?: ReactNode;
  spacing?: string;
}

// 反馈组件接口
export interface AlertProps extends BaseComponentProps {
  status?: 'info' | 'warning' | 'success' | 'error';
  variant?: 'solid' | 'subtle' | 'left-accent' | 'top-accent';
  onClose?: () => void;
  isClosable?: boolean;
}

export interface ToastProps extends BaseComponentProps {
  title?: string;
  description?: string;
  status?: 'info' | 'warning' | 'success' | 'error';
  duration?: number | null;
  isClosable?: boolean;
  position?: 'top' | 'bottom' | 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  onClose?: () => void;
}

// 加载状态组件接口
export interface SpinnerProps extends BaseComponentProps {
  size?: ComponentSize;
  color?: string;
  thickness?: string;
  speed?: string;
  emptyColor?: string;
  label?: string;
}

export interface SkeletonProps extends BaseComponentProps {
  height?: string | number;
  width?: string | number;
  isLoaded?: boolean;
  fadeDuration?: number;
  speed?: number;
  startColor?: string;
  endColor?: string;
}

// 数据展示组件接口
export interface BadgeProps extends BaseComponentProps {
  variant?: 'solid' | 'subtle' | 'outline';
  colorScheme?: ColorScheme;
  size?: ComponentSize;
}

export interface TagProps extends BaseComponentProps {
  variant?: 'solid' | 'subtle' | 'outline';
  colorScheme?: ColorScheme;
  size?: ComponentSize;
  isClosable?: boolean;
  onClose?: () => void;
}

// 布局组件接口
export interface FlexProps extends BaseComponentProps {
  direction?: ResponsiveValue<'row' | 'column' | 'row-reverse' | 'column-reverse'>;
  wrap?: ResponsiveValue<'nowrap' | 'wrap' | 'wrap-reverse'>;
  align?: ResponsiveValue<'start' | 'center' | 'end' | 'stretch' | 'baseline'>;
  justify?: ResponsiveValue<'start' | 'center' | 'end' | 'space-between' | 'space-around' | 'space-evenly'>;
  gap?: ResponsiveValue<string>;
}

export interface GridProps extends BaseComponentProps {
  columns?: ResponsiveValue<number>;
  rows?: ResponsiveValue<number>;
  gap?: ResponsiveValue<string>;
  rowGap?: ResponsiveValue<string>;
  columnGap?: ResponsiveValue<string>;
  templateColumns?: ResponsiveValue<string>;
  templateRows?: ResponsiveValue<string>;
  autoColumns?: ResponsiveValue<string>;
  autoRows?: ResponsiveValue<string>;
}

export interface StackProps extends BaseComponentProps {
  direction?: ResponsiveValue<'row' | 'column'>;
  spacing?: ResponsiveValue<string>;
  align?: ResponsiveValue<'start' | 'center' | 'end' | 'stretch' | 'baseline'>;
  justify?: ResponsiveValue<'start' | 'center' | 'end' | 'space-between' | 'space-around' | 'space-evenly'>;
  divider?: ReactNode;
  shouldWrapChildren?: boolean;
}

// 组件工厂类型
export type ComponentFactory<T extends BaseComponentProps> = (props: T) => ReactElement;

// 主题感知组件属性
export interface ThemeAwareComponentProps extends BaseComponentProps {
  theme?: ThemeMode;
  useSystemTheme?: boolean;
} 