-- 插入测试数据到 agent_data 表
-- 注意：请将下面的 '替换为实际的agent-id' 改为您实际的 agent ID
-- 比如 'e41595bf-4f41-4b14-8efd-3543abb17c58'

-- 互动数据
INSERT INTO agent_data (agent_id, timestamp, key, value)
VALUES 
  ('e41595bf-4f41-4b14-8efd-3543abb17c58', NOW() - INTERVAL '6 days', 'interactions', 15),
  ('e41595bf-4f41-4b14-8efd-3543abb17c58', NOW() - INTERVAL '5 days', 'interactions', 22),
  ('e41595bf-4f41-4b14-8efd-3543abb17c58', NOW() - INTERVAL '4 days', 'interactions', 18),
  ('e41595bf-4f41-4b14-8efd-3543abb17c58', NOW() - INTERVAL '3 days', 'interactions', 25),
  ('e41595bf-4f41-4b14-8efd-3543abb17c58', NOW() - INTERVAL '2 days', 'interactions', 30),
  ('e41595bf-4f41-4b14-8efd-3543abb17c58', NOW() - INTERVAL '1 day', 'interactions', 28),
  ('e41595bf-4f41-4b14-8efd-3543abb17c58', NOW(), 'interactions', 20);

-- 响应时间数据
INSERT INTO agent_data (agent_id, timestamp, key, value)
VALUES 
  ('e41595bf-4f41-4b14-8efd-3543abb17c58', NOW() - INTERVAL '6 days', 'response_time', 2.5),
  ('e41595bf-4f41-4b14-8efd-3543abb17c58', NOW() - INTERVAL '5 days', 'response_time', 2.2),
  ('e41595bf-4f41-4b14-8efd-3543abb17c58', NOW() - INTERVAL '4 days', 'response_time', 2.8),
  ('e41595bf-4f41-4b14-8efd-3543abb17c58', NOW() - INTERVAL '3 days', 'response_time', 2.1),
  ('e41595bf-4f41-4b14-8efd-3543abb17c58', NOW() - INTERVAL '2 days', 'response_time', 1.9),
  ('e41595bf-4f41-4b14-8efd-3543abb17c58', NOW() - INTERVAL '1 day', 'response_time', 2.0),
  ('e41595bf-4f41-4b14-8efd-3543abb17c58', NOW(), 'response_time', 1.8);

-- 满意度数据
INSERT INTO agent_data (agent_id, timestamp, key, value)
VALUES 
  ('e41595bf-4f41-4b14-8efd-3543abb17c58', NOW() - INTERVAL '6 days', 'satisfaction', 4.2),
  ('e41595bf-4f41-4b14-8efd-3543abb17c58', NOW() - INTERVAL '5 days', 'satisfaction', 4.3),
  ('e41595bf-4f41-4b14-8efd-3543abb17c58', NOW() - INTERVAL '4 days', 'satisfaction', 4.1),
  ('e41595bf-4f41-4b14-8efd-3543abb17c58', NOW() - INTERVAL '3 days', 'satisfaction', 4.4),
  ('e41595bf-4f41-4b14-8efd-3543abb17c58', NOW() - INTERVAL '2 days', 'satisfaction', 4.5),
  ('e41595bf-4f41-4b14-8efd-3543abb17c58', NOW() - INTERVAL '1 day', 'satisfaction', 4.6),
  ('e41595bf-4f41-4b14-8efd-3543abb17c58', NOW(), 'satisfaction', 4.7);

-- 插入测试数据到 agent_logs 表
INSERT INTO agent_logs (agent_id, timestamp, level, message, metadata)
VALUES 
  ('e41595bf-4f41-4b14-8efd-3543abb17c58', NOW() - INTERVAL '6 hours', 'INFO', '代理启动成功', '{"source": "system", "user_id": null}'),
  ('e41595bf-4f41-4b14-8efd-3543abb17c58', NOW() - INTERVAL '5 hours', 'INFO', '收到用户消息', '{"source": "chat", "user_id": "user123"}'),
  ('e41595bf-4f41-4b14-8efd-3543abb17c58', NOW() - INTERVAL '5 hours', 'INFO', '代理响应已发送', '{"source": "chat", "user_id": "user123", "response_time_ms": 1250}'),
  ('e41595bf-4f41-4b14-8efd-3543abb17c58', NOW() - INTERVAL '4 hours', 'WARNING', '响应时间超过阈值', '{"source": "monitor", "threshold_ms": 2000, "actual_ms": 2500}'),
  ('e41595bf-4f41-4b14-8efd-3543abb17c58', NOW() - INTERVAL '3 hours', 'INFO', '收到用户反馈', '{"source": "feedback", "user_id": "user456", "rating": 4}'),
  ('e41595bf-4f41-4b14-8efd-3543abb17c58', NOW() - INTERVAL '2 hours', 'ERROR', 'API 调用失败', '{"source": "api", "endpoint": "/external/data", "status_code": 500}'),
  ('e41595bf-4f41-4b14-8efd-3543abb17c58', NOW() - INTERVAL '1 hour', 'INFO', '代理配置已更新', '{"source": "admin", "user_id": "admin789", "changes": ["response_time", "welcome_message"]}'),
  ('e41595bf-4f41-4b14-8efd-3543abb17c58', NOW(), 'INFO', '系统定期检查完成', '{"source": "system", "status": "healthy"}'); 