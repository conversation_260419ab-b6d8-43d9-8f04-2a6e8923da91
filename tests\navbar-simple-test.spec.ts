import { test, expect } from '@playwright/test';

test.describe('Simple Navbar Tests', () => {
  test('navbar居中和亮点效果测试', async ({ page }) => {
    // 导航到首页
    await page.goto('http://localhost:3000');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);

    // 截图全页面
    await page.screenshot({ 
      path: 'tests/screenshots/full-page.png',
      fullPage: true
    });

    // 检查navbar是否存在
    const navbar = page.locator('.fixed.top-6');
    await expect(navbar).toBeVisible();

    // 获取navbar位置信息
    const navbarBox = await navbar.boundingBox();
    const viewportSize = page.viewportSize();
    
    console.log('Navbar位置:', navbarBox);
    console.log('视口大小:', viewportSize);
    
    if (navbarBox && viewportSize) {
      const navbarCenter = navbarBox.x + navbarBox.width / 2;
      const viewportCenter = viewportSize.width / 2;
      const centerDiff = Math.abs(navbarCenter - viewportCenter);
      
      console.log('Navbar中心:', navbarCenter);
      console.log('视口中心:', viewportCenter);
      console.log('居中偏差:', centerDiff);
    }

    // 检查Home按钮的active状态和亮点
    const homeButton = page.locator('button:has-text("Home")');
    await expect(homeButton).toBeVisible();
    
    // 检查亮点元素
    const highlight = page.locator('div[style*="background: rgb(139, 92, 246)"]');
    const highlightExists = await highlight.count() > 0;
    console.log('亮点元素存在:', highlightExists);
    
    if (highlightExists) {
      const highlightBox = await highlight.first().boundingBox();
      console.log('亮点位置:', highlightBox);
    }

    // 截图navbar区域
    await page.screenshot({ 
      path: 'tests/screenshots/navbar-detail.png',
      fullPage: false,
      clip: { x: 0, y: 0, width: 1200, height: 200 }
    });

    // 测试hover效果
    await homeButton.hover();
    await page.waitForTimeout(500);
    
    await page.screenshot({ 
      path: 'tests/screenshots/navbar-hover-detail.png',
      fullPage: false,
      clip: { x: 0, y: 0, width: 1200, height: 200 }
    });

    // 测试点击Pricing
    const pricingButton = page.locator('button:has-text("Pricing")');
    await pricingButton.click();
    await page.waitForTimeout(1000);
    
    await page.screenshot({ 
      path: 'tests/screenshots/navbar-pricing-detail.png',
      fullPage: false,
      clip: { x: 0, y: 0, width: 1200, height: 200 }
    });
  });
}); 