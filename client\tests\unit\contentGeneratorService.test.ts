import axios from 'axios';
import { 
  generateContent, 
  getTemplates, 
  createTemplate, 
  updateTemplate, 
  deleteTemplate,
  getHistory
} from '../../src/services/contentGeneratorService';

// 模拟axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('contentGeneratorService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('generateContent', () => {
    test('成功生成内容', async () => {
      // 设置模拟响应
      const mockResponse = {
        data: {
          content: '生成的内容',
          id: 'gen_123',
          prompt: '测试提示',
          timestamp: '2023-05-20T12:00:00Z',
        }
      };
      mockedAxios.post.mockResolvedValueOnce(mockResponse);

      // 调用服务方法
      const result = await generateContent({ prompt: '测试提示' });

      // 验证结果
      expect(result).toEqual(mockResponse.data);
      expect(mockedAxios.post).toHaveBeenCalledWith(
        '/api/content-generator/generate',
        { prompt: '测试提示' }
      );
    });

    test('处理API错误', async () => {
      // 模拟API错误
      const errorMessage = 'API错误';
      mockedAxios.post.mockRejectedValueOnce(new Error(errorMessage));

      // 验证错误被正确处理
      await expect(generateContent({ prompt: '测试' })).rejects.toThrow();
    });
  });

  describe('getTemplates', () => {
    test('成功获取模板列表', async () => {
      // 设置模拟响应
      const mockTemplates = [
        { id: 'template_1', name: '模板1' },
        { id: 'template_2', name: '模板2' }
      ];
      mockedAxios.get.mockResolvedValueOnce({ data: mockTemplates });

      // 调用服务
      const result = await getTemplates();

      // 验证结果
      expect(result).toEqual(mockTemplates);
      expect(mockedAxios.get).toHaveBeenCalledWith('/api/content-generator/templates');
    });
  });

  describe('createTemplate', () => {
    test('创建新模板', async () => {
      // 设置模拟响应
      const mockTemplate = {
        id: 'template_new',
        name: '新模板',
        description: '描述',
        prompt: '提示'
      };
      mockedAxios.post.mockResolvedValueOnce({ data: mockTemplate });

      // 调用服务
      const result = await createTemplate({
        name: '新模板',
        description: '描述',
        prompt: '提示'
      });

      // 验证结果
      expect(result).toEqual(mockTemplate);
      expect(mockedAxios.post).toHaveBeenCalledWith(
        '/api/content-generator/templates',
        {
          name: '新模板',
          description: '描述',
          prompt: '提示'
        }
      );
    });
  });

  describe('updateTemplate', () => {
    test('更新模板', async () => {
      // 设置模拟响应
      const mockTemplate = {
        id: 'template_1',
        name: '更新后的名称',
        description: '更新后的描述',
        prompt: '更新后的提示'
      };
      mockedAxios.put.mockResolvedValueOnce({ data: mockTemplate });

      // 调用服务
      const result = await updateTemplate('template_1', {
        name: '更新后的名称',
        description: '更新后的描述',
        prompt: '更新后的提示'
      });

      // 验证结果
      expect(result).toEqual(mockTemplate);
      expect(mockedAxios.put).toHaveBeenCalledWith(
        '/api/content-generator/templates/template_1',
        {
          name: '更新后的名称',
          description: '更新后的描述',
          prompt: '更新后的提示'
        }
      );
    });
  });

  describe('deleteTemplate', () => {
    test('删除模板', async () => {
      // 设置模拟响应
      const mockResponse = { data: { success: true } };
      mockedAxios.delete.mockResolvedValueOnce(mockResponse);

      // 调用服务
      const result = await deleteTemplate('template_1');

      // 验证结果
      expect(result).toEqual(mockResponse.data);
      expect(mockedAxios.delete).toHaveBeenCalledWith('/api/content-generator/templates/template_1');
    });
  });
}); 