/**
 * 设计系统类型定义
 * 定义设计token、组件和主题相关的TypeScript类型
 */

// 颜色类型定义
export interface ColorScale {
  50: string;
  100: string;
  200: string;
  300: string;
  400: string;
  500: string;
  600: string;
  700: string;
  800: string;
  900: string;
  950: string;
}

export interface SemanticColors {
  light: string;
  default: string;
  dark: string;
  gradient?: string;
}

export interface BackgroundColors {
  primary: string;
  secondary: string;
  accent: string;
  muted: string;
  hover?: string;
}

export interface TextColors {
  primary: string;
  secondary: string;
  muted: string;
  accent: string;
  inverse: string;
  heading?: string;
  subheading?: string;
  caption?: string;
  link?: string;
  linkHover?: string;
}

export interface BorderColors {
  light: string;
  default: string;
  medium?: string;
  dark: string;
  accent: string;
  subtle?: string;
  soft?: string;
  visible?: string;
}

// 间距类型
export interface SpacingScale {
  xs: string;
  sm: string;
  md: string;
  lg: string;
  xl: string;
  '2xl'?: string;
  '3xl'?: string;
  '4xl'?: string;
  '5xl'?: string;
  xxl?: string;
  xxxl?: string;
}

export interface ComponentSpacing {
  ctaPadding?: {
    mobile: string;
    tablet: string;
    desktop: string;
  };
  footerPadding?: {
    mobile: string;
    tablet: string;
    desktop: string;
  };
  buttonPadding?: {
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
}

// 圆角类型
export interface BorderRadiusScale {
  none: string;
  sm: string;
  md: string;
  lg: string;
  xl: string;
  xxl?: string;
  full: string;
}

// 阴影类型
export interface ShadowScale {
  sm: string;
  card?: string;
  hover?: string;
  focus?: string;
  large?: string;
  none: string;
}

// 字体类型
export interface FontFamily {
  sans: string[];
  display?: string[];
  mono: string[];
}

export interface FontSize {
  xs: string | [string, { lineHeight: string }];
  sm: string | [string, { lineHeight: string }];
  base: string | [string, { lineHeight: string }];
  lg: string | [string, { lineHeight: string }];
  xl: string | [string, { lineHeight: string }];
  '2xl': string | [string, { lineHeight: string }];
  '3xl': string | [string, { lineHeight: string }];
  '4xl': string | [string, { lineHeight: string }];
}

export interface FontWeight {
  light: string;
  normal: string;
  medium: string;
  semibold: string;
  bold: string;
}

export interface LineHeight {
  tight: string;
  normal: string;
  relaxed: string;
}

// 动画类型
export interface AnimationDuration {
  fast: string;
  normal: string;
  slow: string;
  ultraFast?: string;
  dramatic?: string;
}

export interface AnimationEasing {
  easeOut: string;
  easeIn: string;
  easeInOut: string;
}

export interface SpringConfig {
  tension: number;
  friction: number;
  precision: number;
}

export interface MicroInteraction {
  scale?: number;
  y?: number;
  transition: {
    type: string;
    stiffness?: number;
    damping?: number;
  };
}

// 断点类型
export interface Breakpoints {
  sm: string;
  md: string;
  lg: string;
  xl: string;
  '2xl': string;
}

// 设计系统主接口
export interface DesignTokens {
  colors: {
    primary: ColorScale;
    background: BackgroundColors;
    text: TextColors;
    semantic: {
      success: SemanticColors;
      warning: SemanticColors;
      error: SemanticColors;
      info?: SemanticColors;
    };
    border: BorderColors;
    chart?: {
      primary: string;
      secondary: string;
      tertiary: string;
      gradient: {
        from: string;
        to: string;
      };
      categorical: string[];
    };
  };
  spacing: SpacingScale & {
    component?: ComponentSpacing;
  };
  borderRadius: BorderRadiusScale;
  shadows: ShadowScale;
  typography: {
    fontFamily: FontFamily;
    fontSize: FontSize;
    fontWeight: FontWeight;
    lineHeight: LineHeight;
  };
  animation: {
    duration: AnimationDuration;
    easing: AnimationEasing;
    spring?: {
      gentle: SpringConfig;
      smooth: SpringConfig;
      snappy: SpringConfig;
      dramatic: SpringConfig;
    };
    microInteractions?: {
      buttonHover: MicroInteraction;
      buttonTap: MicroInteraction;
      cardHover: MicroInteraction;
    };
  };
  breakpoints: Breakpoints;
}

// 主题配置类型
export type ThemeMode = 'light' | 'dark';

export interface ThemeConfig {
  mode: ThemeMode;
  tokens: DesignTokens;
}

// 设计token相关的CSS变量类型
export type CSSVariables = {
  [key: string]: string;
};

export interface DesignSystemContext {
  theme: ThemeConfig;
  tokens: DesignTokens;
  cssVariables: CSSVariables;
  setTheme: (mode: ThemeMode) => void;
} 