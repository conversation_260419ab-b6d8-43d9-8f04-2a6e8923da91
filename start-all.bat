@echo off
title ibuddy2 System Startup
echo.
echo ================================================
echo    🚀 ibuddy2 微服务系统启动脚本
echo ================================================
echo.

echo 📋 检查环境...
where npm >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到 npm，请先安装 Node.js
    pause
    exit /b 1
)

echo ✅ Node.js 环境检查通过
echo.

echo 🔧 启动各项服务...
echo.

echo 📡 启动 API Gateway (端口 3001)...
start "API Gateway" cmd /k "cd api-gateway && npm start"
timeout /t 2 /nobreak >nul

echo 🔧 启动 Core Service (端口 3002)...
start "Core Service" cmd /k "cd core-service && npm start"
timeout /t 2 /nobreak >nul

echo 🤖 启动 AI Service (端口 3003)...
start "AI Service" cmd /k "cd ai-service && npm start"
timeout /t 2 /nobreak >nul

echo 🌐 启动 Client (端口 3000)...
start "Client" cmd /k "cd client && npm start"
timeout /t 2 /nobreak >nul

echo.
echo ================================================
echo    ✅ 所有服务启动完成！
echo ================================================
echo.
echo 📊 服务访问地址:
echo    • 客户端:     http://localhost:3000
echo    • API网关:    http://localhost:3001
echo    • 核心服务:   http://localhost:3002  
echo    • AI服务:     http://localhost:3003
echo.
echo 💡 提示: 所有服务将在独立窗口中运行
echo 🛑 关闭服务: 关闭对应的命令行窗口即可
echo.
echo 按任意键退出此窗口...
pause >nul 