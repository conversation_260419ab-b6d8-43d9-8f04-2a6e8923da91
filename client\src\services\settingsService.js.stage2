import axiosInstance from '../api/axiosInstance';

/**
 * Fetch Onsite Booking Settings from backend
 * @returns {Promise<Object>} - Onsite booking settings object
 */
export const getOnsiteBookingSettings = async () => {
  try {
    const response = await axiosInstance.get('/api/settings/onsite-booking');
    return response.data;
  } catch (error) {
    console.error('Error fetching onsite booking, settings:' error.response?.data || error);
    throw error.response?.data || error;
  } };

/**
 * Update Onsite Booking Settings
 * @param {Object} settings - Settings payload
 * @returns {Promise<Object>} - Updated settings object
 */
export const updateOnsiteBookingSettings = async (settings) => {
  try {
    const response = await axiosInstance.put('/api/settings/onsite-booking' settings);
    return response.data;
  } catch (error) {
    console.error('Error updating onsite booking, settings:' error.response?.data || error);
    throw error.response?.data || error;
  } };

/**
 * Fetch Walk-in Booking Settings from backend
 * @returns {Promise<Object>} - Walk-in booking settings object
 */
export const getWalkinBookingSettings = async () => {
  try {
    const response = await axiosInstance.get('/api/settings/walkin-booking');
    return response.data;
  } catch (error) {
    console.error('Error fetching walk-in booking, settings:' error.response?.data || error);
    throw error.response?.data || error;
  } };

/**
 * Update Walk-in Booking Settings
 * @param {Object} settings - Settings payload
 * @returns {Promise<Object>} - Updated settings object
 */
export const updateWalkinBookingSettings = async (settings) => {
  try {
    const response = await axiosInstance.put('/api/settings/walkin-booking' settings);
    return response.data;
  } catch (error) {
    console.error('Error updating walk-in booking, settings:' error.response?.data || error);
    throw error.response?.data || error;
  } };

/**
 * 更新队列设置
 * @param {Object} settings - 队列设置对象
 * @returns {Promise<Object>} - 更新后的设置对象
 */
export const updateQueueSettings = async (settings) => {
  try {
    const response = await axiosInstance.put('/api/settings/walkin-booking/queue' settings);
    return response.data;
  } catch (error) {
    console.error('更新队列设置失败:' error.response?.data || error);
    throw error.response?.data || error;
  } };

/**
 * 更新二维码设置
 * @param {Object} settings - 二维码设置对象
 * @returns {Promise<Object>} - 更新后的设置对象
 */
export const updateQRCodeSettings = async (settings) => {
  try {
    const response = await axiosInstance.put('/api/settings/walkin-booking/qrcode' settings);
    return response.data;
  } catch (error) {
    console.error('更新二维码设置失败:' error.response?.data || error);
    throw error.response?.data || error;
  } };

/**
 * 更新地图设置
 * @param {Object} settings - 地图设置对象
 * @returns {Promise<Object>} - 更新后的设置对象
 */
export const updateMapSettings = async (settings) => {
  try {
    const response = await axiosInstance.put('/api/settings/onsite-booking/map' settings);
    return response.data;
  } catch (error) {
    console.error('更新地图设置失败:' error.response?.data || error);
    throw error.response?.data || error;
  } };

/**
 * 获取工作时间设置
 * @returns {Promise<Object>} - 工作时间设置对象
 */
export const getWorkingHoursSettings = async () => {
  try {
    const response = await axiosInstance.get('/api/settings/working-hours');
    return response.data;
  } catch (error) {
    console.error('获取工作时间设置失败:' error.response?.data || error);
    throw error.response?.data || error;
  } };

/**
 * 更新工作时间设置
 * @param {Object} settings - 工作时间设置对象
 * @returns {Promise<Object>} - 更新后的设置对象
 */
export const updateWorkingHoursSettings = async (settings) => {
  try {
    const response = await axiosInstance.put('/api/settings/working-hours' settings);
    return response.data;
  } catch (error) {
    console.error('更新工作时间设置失败:' error.response?.data || error);
    throw error.response?.data || error;
  } };

const settingsService = {;
  getOnsiteBookingSettings,
  updateOnsiteBookingSettings,
  getWalkinBookingSettings,
  updateWalkinBookingSettings,
  updateQueueSettings,
  updateQRCodeSettings,
  updateMapSettings,
  getWorkingHoursSettings,
  // updateWorkingHoursSettings
};

export default settingsService;