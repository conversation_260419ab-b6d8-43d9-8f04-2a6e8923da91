// Hover效果测试脚本
// 用于验证4个特定feature卡片的hover效果

function testHoverEffects() {
  console.log('🔍 开始测试Feature卡片hover效果...');
  
  // 获取所有feature卡片
  const featureCards = document.querySelectorAll('[data-testid="feature-card"]');
  console.log(`📊 找到 ${featureCards.length} 个feature卡片`);
  
  // 需要测试的卡片索引
  const problematicCards = [2, 3, 6, 7]; // Flexible Pricing, Cloud-based Platform, Smart Optimization, Built with Love
  const cardNames = ['Flexible Pricing', 'Cloud-based Platform', 'Smart Optimization', 'Built with Love'];
  
  problematicCards.forEach((index, i) => {
    const card = featureCards[index];
    if (!card) {
      console.error(`❌ 卡片 ${index} 未找到`);
      return;
    }
    
    console.log(`\n🧪 测试卡片 ${index}: ${cardNames[i]}`);
    
    // 检查基础CSS类
    const hasGroup = card.classList.contains('group');
    const hasCursor = card.classList.contains('cursor-pointer');
    console.log(`  - Group类: ${hasGroup ? '✅' : '❌'}`);
    console.log(`  - Cursor类: ${hasCursor ? '✅' : '❌'}`);
    
    // 检查hover背景元素
    const hoverBg = card.querySelector('.group-hover\\:opacity-100');
    console.log(`  - Hover背景: ${hoverBg ? '✅' : '❌'}`);
    
    // 检查左边框元素
    const leftBorder = card.querySelector('.group-hover\\:h-8');
    console.log(`  - 左边框: ${leftBorder ? '✅' : '❌'}`);
    
    // 检查标题移动元素
    const titleMove = card.querySelector('.group-hover\\:translate-x-2');
    console.log(`  - 标题移动: ${titleMove ? '✅' : '❌'}`);
    
    // 检查颜色变化元素
    const colorChange = card.querySelectorAll('.group-hover\\:text-purple-600, .group-hover\\:text-purple-700');
    console.log(`  - 颜色变化元素: ${colorChange.length} 个`);
    
    // 模拟hover事件
    console.log(`  🖱️ 模拟hover事件...`);
    
    // 添加hover类来测试效果
    card.classList.add('hover');
    
    // 检查计算样式
    const computedStyle = window.getComputedStyle(card);
    console.log(`  - Z-index: ${computedStyle.zIndex}`);
    console.log(`  - Position: ${computedStyle.position}`);
    
    // 移除hover类
    setTimeout(() => {
      card.classList.remove('hover');
    }, 1000);
  });
  
  console.log('\n✅ Hover效果测试完成');
}

// 运行测试
testHoverEffects();
