import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { 
  Play, 
  Pause, 
  Stop, 
  BarChart3, 
  TrendingUp, 
  Users,
  Target,
  Calendar
} from 'lucide-react';

interface ABTest {
  id: string;
  name: string;
  status: 'draft' | 'running' | 'paused' | 'completed';
  startDate: string;
  endDate?: string;
  variants: {
    name: string;
    traffic: number;
    conversions: number;
    conversionRate: number;
  }[];
  metrics: {
    totalVisitors: number;
    totalConversions: number;
    confidence: number;
  };
}

interface ABTestManagerProps {
  className?: string;
}

export const ABTestManager: React.FC<ABTestManagerProps> = ({ className }) => {
  const [tests, setTests] = useState<ABTest[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // 模拟加载数据
    const mockTests: ABTest[] = [
      {
        id: '1',
        name: '登陆页面标题测试',
        status: 'running',
        startDate: '2024-01-15',
        variants: [
          { name: '原版本', traffic: 50, conversions: 45, conversionRate: 12.5 },
          { name: '新版本', traffic: 50, conversions: 62, conversionRate: 17.2 }
        ],
        metrics: {
          totalVisitors: 720,
          totalConversions: 107,
          confidence: 95
        }
      },
      {
        id: '2',
        name: '邮件主题行测试',
        status: 'completed',
        startDate: '2024-01-01',
        endDate: '2024-01-14',
        variants: [
          { name: '主题A', traffic: 50, conversions: 28, conversionRate: 8.3 },
          { name: '主题B', traffic: 50, conversions: 37, conversionRate: 11.1 }
        ],
        metrics: {
          totalVisitors: 580,
          totalConversions: 65,
          confidence: 88
        }
      }
    ];

    setTimeout(() => {
      setTests(mockTests);
      setLoading(false);
    }, 1000);
  }, []);

  const getStatusBadge = (status: ABTest['status']) => {
    const variants = {
      draft: { label: '草稿', className: 'bg-gray-100 text-gray-800' },
      running: { label: '运行中', className: 'bg-green-100 text-green-800' },
      paused: { label: '已暂停', className: 'bg-yellow-100 text-yellow-800' },
      completed: { label: '已完成', className: 'bg-blue-100 text-blue-800' }
    };
    
    const variant = variants[status];
    return <Badge className={variant.className}>{variant.label}</Badge>;
  };

  const handleTestAction = (testId: string, action: 'start' | 'pause' | 'stop') => {
    setTests(prev => prev.map(test => {
      if (test.id === testId) {
        let newStatus: ABTest['status'];
        switch (action) {
          case 'start':
            newStatus = test.status === 'paused' ? 'running' : 'running';
            break;
          case 'pause':
            newStatus = 'paused';
            break;
          case 'stop':
            newStatus = 'completed';
            break;
          default:
            newStatus = test.status;
        }
        return { ...test, status: newStatus };
      }
      return test;
    }));
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-violet-600 mx-auto"></div>
            <p className="mt-2 text-sm text-gray-600">加载测试数据...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={className}>
      <div className="mb-6 flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">A/B 测试管理</h2>
          <p className="text-gray-600">管理和监控您的A/B测试实验</p>
        </div>
        <Button className="bg-violet-600 hover:bg-violet-700">
          创建新测试
        </Button>
      </div>

      <div className="grid gap-6">
        {tests.map((test) => (
          <Card key={test.id}>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <CardTitle className="text-lg">{test.name}</CardTitle>
                  <div className="flex items-center space-x-4 text-sm text-gray-600">
                    <div className="flex items-center">
                      <Calendar className="w-4 h-4 mr-1" />
                      开始: {test.startDate}
                    </div>
                    {test.endDate && (
                      <div className="flex items-center">
                        <Calendar className="w-4 h-4 mr-1" />
                        结束: {test.endDate}
                      </div>
                    )}
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {getStatusBadge(test.status)}
                  <div className="flex space-x-1">
                    {test.status === 'running' ? (
                      <>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleTestAction(test.id, 'pause')}
                        >
                          <Pause className="w-4 h-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleTestAction(test.id, 'stop')}
                        >
                          <Stop className="w-4 h-4" />
                        </Button>
                      </>
                    ) : test.status === 'paused' ? (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleTestAction(test.id, 'start')}
                      >
                        <Play className="w-4 h-4" />
                      </Button>
                    ) : null}
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {/* 整体指标 */}
                <div className="space-y-4">
                  <h4 className="font-medium text-gray-900">整体指标</h4>
                  <div className="space-y-3">
                    <div className="flex items-center">
                      <Users className="w-4 h-4 mr-2 text-gray-500" />
                      <span className="text-sm text-gray-600">访客总数:</span>
                      <span className="ml-auto font-medium">{test.metrics.totalVisitors}</span>
                    </div>
                    <div className="flex items-center">
                      <Target className="w-4 h-4 mr-2 text-gray-500" />
                      <span className="text-sm text-gray-600">转化总数:</span>
                      <span className="ml-auto font-medium">{test.metrics.totalConversions}</span>
                    </div>
                    <div className="flex items-center">
                      <TrendingUp className="w-4 h-4 mr-2 text-gray-500" />
                      <span className="text-sm text-gray-600">置信度:</span>
                      <span className="ml-auto font-medium">{test.metrics.confidence}%</span>
                    </div>
                  </div>
                </div>

                {/* 变体对比 */}
                <div className="md:col-span-2">
                  <h4 className="font-medium text-gray-900 mb-4">变体表现</h4>
                  <div className="space-y-3">
                    {test.variants.map((variant, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center space-x-3">
                          <div className={`w-3 h-3 rounded-full ${index === 0 ? 'bg-blue-500' : 'bg-green-500'}`}></div>
                          <span className="font-medium">{variant.name}</span>
                        </div>
                        <div className="flex items-center space-x-6 text-sm">
                          <div className="text-center">
                            <div className="text-gray-600">流量</div>
                            <div className="font-medium">{variant.traffic}%</div>
                          </div>
                          <div className="text-center">
                            <div className="text-gray-600">转化</div>
                            <div className="font-medium">{variant.conversions}</div>
                          </div>
                          <div className="text-center">
                            <div className="text-gray-600">转化率</div>
                            <div className="font-medium text-green-600">{variant.conversionRate}%</div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}

        {tests.length === 0 && (
          <Card>
            <CardContent className="text-center py-12">
              <BarChart3 className="w-12 h-12 mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">还没有A/B测试</h3>
              <p className="text-gray-600 mb-4">创建您的第一个A/B测试来优化转化率</p>
              <Button className="bg-violet-600 hover:bg-violet-700">
                创建测试
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default ABTestManager; 