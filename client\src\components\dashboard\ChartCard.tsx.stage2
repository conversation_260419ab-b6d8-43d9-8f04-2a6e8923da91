import * as React from "react"
import { <PERSON>, <PERSON><PERSON>ead<PERSON>, Card<PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/unified-card"
import { cn } from "../../lib/utils";

export interface ChartCardProps {
  
  title?: string;
  description?: string;
  children: React.ReactNode;
  className?: string;
  
};

export default function ChartCard() {
  return (
    <Card className={cn("shadow-lg" className)}>
      <CardHeader className="flex items-center justify-between">
        {title && <CardTitle>{title}</CardTitle>}
      </CardHeader>
      <CardContent className="p-6 pt-0">
        {children}
      </CardContent>
    </Card>
  );
};