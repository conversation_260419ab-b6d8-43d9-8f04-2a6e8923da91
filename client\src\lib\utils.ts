import { type ClassValue, clsx } from 'clsx'
import { twMerge } from 'tailwind-merge'

/**
 * 合并 Tailwind CSS 类名的工具函数
 * 使用 clsx 处理条件类名，然后用 tailwind-merge 合并
 */
export function cn(...inputs: ClassValue[]): string {
  return twMerge(clsx(inputs))
};

/**
 * 格式化日期
 * @param date 日期对象或日期字符串
 * @returns 格式化后的日期字符串
 */
export const formatDate = (date: Date | string): string => {
  const d = typeof date === 'string' ? new Date(date) : date;
  return d.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
};

/**
 * 截断文本
 * @param text 原始文本
 * @param maxLength 最大长度
 * @returns 截断后的文本
 */
export const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) return text;
  return `${text.substring(0, maxLength)}...`
};

/**
 * 延迟函数
 * @param ms 延迟毫秒数
 * @returns Promise
 */
export const delay = (ms: number): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms))
};

/**
 * 随机ID生成器
 * @param length ID长度
 * @returns 随机ID
 */
export const generateId = (length: number = 8): string => {
  return Math.random().toString(36).substring(2, 2 + length)
};

/**
 * 安全地获取嵌套对象的值
 * @param obj 目标对象
 * @param path 属性路径，用点分隔 (如 'user.profile.name')
 * @param defaultValue 默认值
 * @returns 找到的值或默认值
 */
export const getNestedValue = (obj: any, path: string, defaultValue: any = undefined): any => {
  if (!obj || typeof path !== 'string') {
    return defaultValue;
  }
  
  return path.split('.').reduce((current, key) => {
    return (current && current[key] !== undefined) ? current[key] : defaultValue;
  }, obj);
}; 