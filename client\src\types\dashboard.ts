export interface Kpi {
  title: string;
  value: number | string;
  icon?: any; // ReactNode or serialized icon info
}

export interface Transaction {
  id: number | string;
  customer: string;
  amount: number | string;
  status: string;
  date: string;
}

export interface SettingsSummary {
  totalBrands: number;
  totalPlatforms: number;
  pendingApprovals: number;
}

export interface SalesPoint {
  date: string;
  value: number;
}