import React from 'react';
import { CheckCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface ConnectionSuccessModalProps {
  isOpen: boolean;
  onClose: () => void;
  platformName: string;
  className?: string;
}

export const ConnectionSuccessModal: React.FC<ConnectionSuccessModalProps> = ({
  isOpen,
  onClose,
  platformName,
  className
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <div className={cn(
        "bg-white dark:bg-gray-800 p-8 rounded-xl shadow-xl max-w-md w-full mx-4",
        "transform transition-all duration-300 scale-100",
        className
      )}>
        <div className="text-center space-y-4">
          <div className="mx-auto w-16 h-16 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center">
            <CheckCircle className="w-8 h-8 text-green-600 dark:text-green-400" />
          </div>
          
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
            连接成功！
          </h3>
          
          <p className="text-gray-600 dark:text-gray-300">
            您已成功连接到 {platformName}
          </p>
          
          <Button
            onClick={onClose}
            className="w-full mt-6"
          >
            完成
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ConnectionSuccessModal;
