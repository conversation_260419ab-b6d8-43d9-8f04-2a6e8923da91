/*! For license information please see 353.73a0cab6.iframe.bundle.js.LICENSE.txt */
(self.webpackChunkclient=self.webpackChunkclient||[]).push([[353],{"./node_modules/@babel/runtime/helpers/esm/objectWithoutPropertiesLoose.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{"use strict";function _objectWithoutPropertiesLoose(r,e){if(null==r)return{};var t={};for(var n in r)if({}.hasOwnProperty.call(r,n)){if(-1!==e.indexOf(n))continue;t[n]=r[n]}return t}__webpack_require__.d(__webpack_exports__,{A:()=>_objectWithoutPropertiesLoose})},"./node_modules/@restart/hooks/esm/useEventCallback.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{"use strict";__webpack_require__.d(__webpack_exports__,{A:()=>useEventCallback});var react=__webpack_require__("./node_modules/react/index.js");const esm_useCommittedRef=function useCommittedRef(value){const ref=(0,react.useRef)(value);return(0,react.useEffect)((()=>{ref.current=value}),[value]),ref};function useEventCallback(fn){const ref=esm_useCommittedRef(fn);return(0,react.useCallback)((function(...args){return ref.current&&ref.current(...args)}),[ref])}},"./node_modules/@restart/hooks/esm/useMergedRefs.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{"use strict";__webpack_require__.d(__webpack_exports__,{A:()=>__WEBPACK_DEFAULT_EXPORT__});var react__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("./node_modules/react/index.js");const toFnRef=ref=>ref&&"function"!=typeof ref?value=>{ref.current=value}:ref;const __WEBPACK_DEFAULT_EXPORT__=function useMergedRefs(refA,refB){return(0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)((()=>function mergeRefs(refA,refB){const a=toFnRef(refA),b=toFnRef(refB);return value=>{a&&a(value),b&&b(value)}}(refA,refB)),[refA,refB])}},"./node_modules/@restart/ui/esm/Button.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{"use strict";__webpack_require__.d(__webpack_exports__,{Am:()=>useButtonProps,Ay:()=>__WEBPACK_DEFAULT_EXPORT__});var react__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("./node_modules/react/index.js"),react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__("./node_modules/react/jsx-runtime.js");const _excluded=["as","disabled"];function useButtonProps({tagName,disabled,href,target,rel,role,onClick,tabIndex=0,type}){tagName||(tagName=null!=href||null!=target||null!=rel?"a":"button");const meta={tagName};if("button"===tagName)return[{type:type||"button",disabled},meta];const handleClick=event=>{(disabled||"a"===tagName&&function isTrivialHref(href){return!href||"#"===href.trim()}(href))&&event.preventDefault(),disabled?event.stopPropagation():null==onClick||onClick(event)};return"a"===tagName&&(href||(href="#"),disabled&&(href=void 0)),[{role:null!=role?role:"button",disabled:void 0,tabIndex:disabled?void 0:tabIndex,href,target:"a"===tagName?target:void 0,"aria-disabled":disabled||void 0,rel:"a"===tagName?rel:void 0,onClick:handleClick,onKeyDown:event=>{" "===event.key&&(event.preventDefault(),handleClick(event))}},meta]}const Button=react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(((_ref,ref)=>{let{as:asProp,disabled}=_ref,props=function _objectWithoutPropertiesLoose(r,e){if(null==r)return{};var t={};for(var n in r)if({}.hasOwnProperty.call(r,n)){if(e.indexOf(n)>=0)continue;t[n]=r[n]}return t}(_ref,_excluded);const[buttonProps,{tagName:Component}]=useButtonProps(Object.assign({tagName:asProp,disabled},props));return(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Component,Object.assign({},props,buttonProps,{ref}))}));Button.displayName="Button";const __WEBPACK_DEFAULT_EXPORT__=Button},"./node_modules/@restart/ui/node_modules/@restart/hooks/esm/useCommittedRef.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{"use strict";__webpack_require__.d(__webpack_exports__,{A:()=>__WEBPACK_DEFAULT_EXPORT__});var react__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("./node_modules/react/index.js");const __WEBPACK_DEFAULT_EXPORT__=function useCommittedRef(value){const ref=(0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(value);return(0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)((()=>{ref.current=value}),[value]),ref}},"./node_modules/@restart/ui/node_modules/@restart/hooks/esm/useEventCallback.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{"use strict";__webpack_require__.d(__webpack_exports__,{A:()=>useEventCallback});var react__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("./node_modules/react/index.js"),_useCommittedRef__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__("./node_modules/@restart/ui/node_modules/@restart/hooks/esm/useCommittedRef.js");function useEventCallback(fn){const ref=(0,_useCommittedRef__WEBPACK_IMPORTED_MODULE_1__.A)(fn);return(0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((function(...args){return ref.current&&ref.current(...args)}),[ref])}},"./node_modules/@restart/ui/node_modules/@restart/hooks/esm/useIsomorphicEffect.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{"use strict";__webpack_require__.d(__webpack_exports__,{A:()=>__WEBPACK_DEFAULT_EXPORT__});var react__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("./node_modules/react/index.js");const isReactNative=void 0!==__webpack_require__.g&&__webpack_require__.g.navigator&&"ReactNative"===__webpack_require__.g.navigator.product,__WEBPACK_DEFAULT_EXPORT__="undefined"!=typeof document||isReactNative?react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect:react__WEBPACK_IMPORTED_MODULE_0__.useEffect},"./node_modules/@restart/ui/node_modules/@restart/hooks/esm/useMounted.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{"use strict";__webpack_require__.d(__webpack_exports__,{A:()=>useMounted});var react__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("./node_modules/react/index.js");function useMounted(){const mounted=(0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(!0),isMounted=(0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)((()=>mounted.current));return(0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)((()=>(mounted.current=!0,()=>{mounted.current=!1})),[]),isMounted.current}},"./node_modules/@restart/ui/node_modules/@restart/hooks/esm/usePrevious.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{"use strict";__webpack_require__.d(__webpack_exports__,{A:()=>usePrevious});var react__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("./node_modules/react/index.js");function usePrevious(value){const ref=(0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);return(0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)((()=>{ref.current=value})),ref.current}},"./node_modules/classnames/index.js":(module,exports)=>{var __WEBPACK_AMD_DEFINE_RESULT__;!function(){"use strict";var hasOwn={}.hasOwnProperty;function classNames(){for(var classes="",i=0;i<arguments.length;i++){var arg=arguments[i];arg&&(classes=appendClass(classes,parseValue(arg)))}return classes}function parseValue(arg){if("string"==typeof arg||"number"==typeof arg)return arg;if("object"!=typeof arg)return"";if(Array.isArray(arg))return classNames.apply(null,arg);if(arg.toString!==Object.prototype.toString&&!arg.toString.toString().includes("[native code]"))return arg.toString();var classes="";for(var key in arg)hasOwn.call(arg,key)&&arg[key]&&(classes=appendClass(classes,key));return classes}function appendClass(value,newClass){return newClass?value?value+" "+newClass:value+newClass:value}module.exports?(classNames.default=classNames,module.exports=classNames):void 0===(__WEBPACK_AMD_DEFINE_RESULT__=function(){return classNames}.apply(exports,[]))||(module.exports=__WEBPACK_AMD_DEFINE_RESULT__)}()},"./node_modules/dom-helpers/esm/addEventListener.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{"use strict";__webpack_require__.d(__webpack_exports__,{Ay:()=>__WEBPACK_DEFAULT_EXPORT__});var _canUseDOM__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("./node_modules/dom-helpers/esm/canUseDOM.js"),optionsSupported=!1,onceSupported=!1;try{var options={get passive(){return optionsSupported=!0},get once(){return onceSupported=optionsSupported=!0}};_canUseDOM__WEBPACK_IMPORTED_MODULE_0__.A&&(window.addEventListener("test",options,options),window.removeEventListener("test",options,!0))}catch(e){}const __WEBPACK_DEFAULT_EXPORT__=function addEventListener(node,eventName,handler,options){if(options&&"boolean"!=typeof options&&!onceSupported){var once=options.once,capture=options.capture,wrappedHandler=handler;!onceSupported&&once&&(wrappedHandler=handler.__once||function onceHandler(event){this.removeEventListener(eventName,onceHandler,capture),handler.call(this,event)},handler.__once=wrappedHandler),node.addEventListener(eventName,wrappedHandler,optionsSupported?options:capture)}node.addEventListener(eventName,handler,options)}},"./node_modules/dom-helpers/esm/canUseDOM.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{"use strict";__webpack_require__.d(__webpack_exports__,{A:()=>__WEBPACK_DEFAULT_EXPORT__});const __WEBPACK_DEFAULT_EXPORT__=!("undefined"==typeof window||!window.document||!window.document.createElement)},"./node_modules/dom-helpers/esm/listen.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{"use strict";__webpack_require__.d(__webpack_exports__,{A:()=>__WEBPACK_DEFAULT_EXPORT__});var _addEventListener__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("./node_modules/dom-helpers/esm/addEventListener.js"),_removeEventListener__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__("./node_modules/dom-helpers/esm/removeEventListener.js");const __WEBPACK_DEFAULT_EXPORT__=function listen(node,eventName,handler,options){return(0,_addEventListener__WEBPACK_IMPORTED_MODULE_0__.Ay)(node,eventName,handler,options),function(){(0,_removeEventListener__WEBPACK_IMPORTED_MODULE_1__.A)(node,eventName,handler,options)}}},"./node_modules/dom-helpers/esm/ownerDocument.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{"use strict";function ownerDocument(node){return node&&node.ownerDocument||document}__webpack_require__.d(__webpack_exports__,{A:()=>ownerDocument})},"./node_modules/dom-helpers/esm/removeEventListener.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{"use strict";__webpack_require__.d(__webpack_exports__,{A:()=>__WEBPACK_DEFAULT_EXPORT__});const __WEBPACK_DEFAULT_EXPORT__=function removeEventListener(node,eventName,handler,options){var capture=options&&"boolean"!=typeof options?options.capture:options;node.removeEventListener(eventName,handler,capture),handler.__once&&node.removeEventListener(eventName,handler.__once,capture)}},"./node_modules/prop-types/factoryWithThrowingShims.js":(module,__unused_webpack_exports,__webpack_require__)=>{"use strict";var ReactPropTypesSecret=__webpack_require__("./node_modules/prop-types/lib/ReactPropTypesSecret.js");function emptyFunction(){}function emptyFunctionWithReset(){}emptyFunctionWithReset.resetWarningCache=emptyFunction,module.exports=function(){function shim(props,propName,componentName,location,propFullName,secret){if(secret!==ReactPropTypesSecret){var err=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw err.name="Invariant Violation",err}}function getShim(){return shim}shim.isRequired=shim;var ReactPropTypes={array:shim,bigint:shim,bool:shim,func:shim,number:shim,object:shim,string:shim,symbol:shim,any:shim,arrayOf:getShim,element:shim,elementType:shim,instanceOf:getShim,node:shim,objectOf:getShim,oneOf:getShim,oneOfType:getShim,shape:getShim,exact:getShim,checkPropTypes:emptyFunctionWithReset,resetWarningCache:emptyFunction};return ReactPropTypes.PropTypes=ReactPropTypes,ReactPropTypes}},"./node_modules/prop-types/index.js":(module,__unused_webpack_exports,__webpack_require__)=>{module.exports=__webpack_require__("./node_modules/prop-types/factoryWithThrowingShims.js")()},"./node_modules/prop-types/lib/ReactPropTypesSecret.js":module=>{"use strict";module.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},"./node_modules/react-bootstrap/esm/Button.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{"use strict";__webpack_require__.d(__webpack_exports__,{A:()=>__WEBPACK_DEFAULT_EXPORT__});var classnames__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("./node_modules/classnames/index.js"),classnames__WEBPACK_IMPORTED_MODULE_0___default=__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_0__),react__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__("./node_modules/react/index.js"),_restart_ui_Button__WEBPACK_IMPORTED_MODULE_4__=__webpack_require__("./node_modules/@restart/ui/esm/Button.js"),_ThemeProvider__WEBPACK_IMPORTED_MODULE_3__=__webpack_require__("./node_modules/react-bootstrap/esm/ThemeProvider.js"),react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__=__webpack_require__("./node_modules/react/jsx-runtime.js");const Button=react__WEBPACK_IMPORTED_MODULE_1__.forwardRef((({as,bsPrefix,variant="primary",size,active=!1,disabled=!1,className,...props},ref)=>{const prefix=(0,_ThemeProvider__WEBPACK_IMPORTED_MODULE_3__.oU)(bsPrefix,"btn"),[buttonProps,{tagName}]=(0,_restart_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Am)({tagName:as,disabled,...props}),Component=tagName;return(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Component,{...buttonProps,...props,ref,disabled,className:classnames__WEBPACK_IMPORTED_MODULE_0___default()(className,prefix,active&&"active",variant&&`${prefix}-${variant}`,size&&`${prefix}-${size}`,props.href&&disabled&&"disabled")})}));Button.displayName="Button";const __WEBPACK_DEFAULT_EXPORT__=Button},"./node_modules/react-bootstrap/esm/Dropdown.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{"use strict";__webpack_require__.d(__webpack_exports__,{A:()=>react_bootstrap_esm_Dropdown});var classnames=__webpack_require__("./node_modules/classnames/index.js"),classnames_default=__webpack_require__.n(classnames),react=__webpack_require__("./node_modules/react/index.js"),querySelectorAll=__webpack_require__("./node_modules/dom-helpers/esm/querySelectorAll.js"),addEventListener=__webpack_require__("./node_modules/dom-helpers/esm/addEventListener.js");function useUncontrolledProp(propValue,defaultValue,handler){const wasPropRef=(0,react.useRef)(void 0!==propValue),[stateValue,setState]=(0,react.useState)(defaultValue),isProp=void 0!==propValue,wasProp=wasPropRef.current;return wasPropRef.current=isProp,!isProp&&wasProp&&stateValue!==defaultValue&&setState(defaultValue),[isProp?propValue:stateValue,(0,react.useCallback)(((...args)=>{const[value,...rest]=args;let returnValue=null==handler?void 0:handler(value,...rest);return setState(value),returnValue}),[handler])]}var usePrevious=__webpack_require__("./node_modules/@restart/ui/node_modules/@restart/hooks/esm/usePrevious.js");var useEventListener=__webpack_require__("./node_modules/@restart/ui/node_modules/@restart/hooks/esm/useEventListener.js"),useEventCallback=__webpack_require__("./node_modules/@restart/ui/node_modules/@restart/hooks/esm/useEventCallback.js");const esm_DropdownContext=react.createContext(null);var useCallbackRef=__webpack_require__("./node_modules/@restart/ui/node_modules/@restart/hooks/esm/useCallbackRef.js"),usePopper=__webpack_require__("./node_modules/@restart/ui/esm/usePopper.js"),useClickOutside=__webpack_require__("./node_modules/@restart/ui/esm/useClickOutside.js"),mergeOptionsWithPopperConfig=__webpack_require__("./node_modules/@restart/ui/esm/mergeOptionsWithPopperConfig.js"),jsx_runtime=__webpack_require__("./node_modules/react/jsx-runtime.js");const _excluded=["children","usePopper"];const noop=()=>{};function useDropdownMenu(options={}){const context=(0,react.useContext)(esm_DropdownContext),[arrowElement,attachArrowRef]=(0,useCallbackRef.A)(),hasShownRef=(0,react.useRef)(!1),{flip,offset,rootCloseEvent,fixed=!1,placement:placementOverride,popperConfig={},enableEventListeners=!0,usePopper:shouldUsePopper=!!context}=options,show=null==(null==context?void 0:context.show)?!!options.show:context.show;show&&!hasShownRef.current&&(hasShownRef.current=!0);const{placement,setMenu,menuElement,toggleElement}=context||{},popper=(0,usePopper.A)(toggleElement,menuElement,(0,mergeOptionsWithPopperConfig.Ay)({placement:placementOverride||placement||"bottom-start",enabled:shouldUsePopper,enableEvents:null==enableEventListeners?show:enableEventListeners,offset,flip,fixed,arrowElement,popperConfig})),menuProps=Object.assign({ref:setMenu||noop,"aria-labelledby":null==toggleElement?void 0:toggleElement.id},popper.attributes.popper,{style:popper.styles.popper}),metadata={show,placement,hasShown:hasShownRef.current,toggle:null==context?void 0:context.toggle,popper:shouldUsePopper?popper:null,arrowProps:shouldUsePopper?Object.assign({ref:attachArrowRef},popper.attributes.arrow,{style:popper.styles.arrow}):{}};return(0,useClickOutside.A)(menuElement,(e=>{null==context||context.toggle(!1,e)}),{clickTrigger:rootCloseEvent,disabled:!show}),[menuProps,metadata]}function DropdownMenu(_ref){let{children,usePopper:usePopperProp=!0}=_ref,options=function DropdownMenu_objectWithoutPropertiesLoose(r,e){if(null==r)return{};var t={};for(var n in r)if({}.hasOwnProperty.call(r,n)){if(e.indexOf(n)>=0)continue;t[n]=r[n]}return t}(_ref,_excluded);const[props,meta]=useDropdownMenu(Object.assign({},options,{usePopper:usePopperProp}));return(0,jsx_runtime.jsx)(jsx_runtime.Fragment,{children:children(props,meta)})}DropdownMenu.displayName="DropdownMenu";const esm_DropdownMenu=DropdownMenu,$b5e257d569688ac6$var$defaultContext={prefix:String(Math.round(1e10*Math.random())),current:0},$b5e257d569688ac6$var$SSRContext=react.createContext($b5e257d569688ac6$var$defaultContext),$b5e257d569688ac6$var$IsSSRContext=react.createContext(!1);Boolean("undefined"!=typeof window&&window.document&&window.document.createElement);let $b5e257d569688ac6$var$componentIds=new WeakMap;function $b5e257d569688ac6$var$useCounter(isDisabled=!1){let ctx=(0,react.useContext)($b5e257d569688ac6$var$SSRContext),ref=(0,react.useRef)(null);if(null===ref.current&&!isDisabled){var _React___SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED_ReactCurrentOwner,_React___SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;let currentOwner=null===(_React___SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=react.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED)||void 0===_React___SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED||null===(_React___SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED_ReactCurrentOwner=_React___SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner)||void 0===_React___SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED_ReactCurrentOwner?void 0:_React___SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED_ReactCurrentOwner.current;if(currentOwner){let prevComponentValue=$b5e257d569688ac6$var$componentIds.get(currentOwner);null==prevComponentValue?$b5e257d569688ac6$var$componentIds.set(currentOwner,{id:ctx.current,state:currentOwner.memoizedState}):currentOwner.memoizedState!==prevComponentValue.state&&(ctx.current=prevComponentValue.id,$b5e257d569688ac6$var$componentIds.delete(currentOwner))}ref.current=++ctx.current}return ref.current}const $b5e257d569688ac6$export$619500959fc48b26="function"==typeof react.useId?function $b5e257d569688ac6$var$useModernSSRSafeId(defaultId){let id=react.useId(),[didSSR]=(0,react.useState)(function $b5e257d569688ac6$export$535bd6ca7f90a273(){return"function"==typeof react.useSyncExternalStore?react.useSyncExternalStore($b5e257d569688ac6$var$subscribe,$b5e257d569688ac6$var$getSnapshot,$b5e257d569688ac6$var$getServerSnapshot):(0,react.useContext)($b5e257d569688ac6$var$IsSSRContext)}());return defaultId||`${didSSR?"react-aria":`react-aria${$b5e257d569688ac6$var$defaultContext.prefix}`}-${id}`}:function $b5e257d569688ac6$var$useLegacySSRSafeId(defaultId){let ctx=(0,react.useContext)($b5e257d569688ac6$var$SSRContext),counter=$b5e257d569688ac6$var$useCounter(!!defaultId),prefix=`react-aria${ctx.prefix}`;return defaultId||`${prefix}-${counter}`};function $b5e257d569688ac6$var$getSnapshot(){return!1}function $b5e257d569688ac6$var$getServerSnapshot(){return!0}function $b5e257d569688ac6$var$subscribe(onStoreChange){return()=>{}}const isRoleMenu=el=>{var _el$getAttribute;return"menu"===(null==(_el$getAttribute=el.getAttribute("role"))?void 0:_el$getAttribute.toLowerCase())},DropdownToggle_noop=()=>{};function useDropdownToggle(){const id=$b5e257d569688ac6$export$619500959fc48b26(),{show=!1,toggle=DropdownToggle_noop,setToggle,menuElement}=(0,react.useContext)(esm_DropdownContext)||{},handleClick=(0,react.useCallback)((e=>{toggle(!show,e)}),[show,toggle]),props={id,ref:setToggle||DropdownToggle_noop,onClick:handleClick,"aria-expanded":!!show};return menuElement&&isRoleMenu(menuElement)&&(props["aria-haspopup"]=!0),[props,{show,toggle}]}function DropdownToggle({children}){const[props,meta]=useDropdownToggle();return(0,jsx_runtime.jsx)(jsx_runtime.Fragment,{children:children(props,meta)})}DropdownToggle.displayName="DropdownToggle";const esm_DropdownToggle=DropdownToggle,makeEventKey=(eventKey,href=null)=>null!=eventKey?String(eventKey):href||null,esm_SelectableContext=react.createContext(null),NavContext=react.createContext(null);NavContext.displayName="NavContext";const esm_NavContext=NavContext;var Button=__webpack_require__("./node_modules/@restart/ui/esm/Button.js"),DataKey=__webpack_require__("./node_modules/@restart/ui/esm/DataKey.js");const DropdownItem_excluded=["eventKey","disabled","onClick","active","as"];function useDropdownItem({key,href,active,disabled,onClick}){const onSelectCtx=(0,react.useContext)(esm_SelectableContext),navContext=(0,react.useContext)(esm_NavContext),{activeKey}=navContext||{},eventKey=makeEventKey(key,href),isActive=null==active&&null!=key?makeEventKey(activeKey)===eventKey:active;return[{onClick:(0,useEventCallback.A)((event=>{disabled||(null==onClick||onClick(event),onSelectCtx&&!event.isPropagationStopped()&&onSelectCtx(eventKey,event))})),"aria-disabled":disabled||void 0,"aria-selected":isActive,[(0,DataKey.sE)("dropdown-item")]:""},{isActive}]}const DropdownItem=react.forwardRef(((_ref,ref)=>{let{eventKey,disabled,onClick,active,as:Component=Button.Ay}=_ref,props=function DropdownItem_objectWithoutPropertiesLoose(r,e){if(null==r)return{};var t={};for(var n in r)if({}.hasOwnProperty.call(r,n)){if(e.indexOf(n)>=0)continue;t[n]=r[n]}return t}(_ref,DropdownItem_excluded);const[dropdownItemProps]=useDropdownItem({key:eventKey,href:props.href,disabled,onClick,active});return(0,jsx_runtime.jsx)(Component,Object.assign({},props,{ref},dropdownItemProps))}));DropdownItem.displayName="DropdownItem";const esm_DropdownItem=DropdownItem;var useWindow=__webpack_require__("./node_modules/@restart/ui/esm/useWindow.js");function useRefWithUpdate(){const forceUpdate=function useForceUpdate(){const[,dispatch]=(0,react.useReducer)((revision=>revision+1),0);return dispatch}(),ref=(0,react.useRef)(null),attachRef=(0,react.useCallback)((element=>{ref.current=element,forceUpdate()}),[forceUpdate]);return[ref,attachRef]}function Dropdown({defaultShow,show:rawShow,onSelect,onToggle:rawOnToggle,itemSelector=`* [${(0,DataKey.sE)("dropdown-item")}]`,focusFirstItemOnShow,placement="bottom-start",children}){const window=(0,useWindow.A)(),[show,onToggle]=useUncontrolledProp(rawShow,defaultShow,rawOnToggle),[menuRef,setMenu]=useRefWithUpdate(),menuElement=menuRef.current,[toggleRef,setToggle]=useRefWithUpdate(),toggleElement=toggleRef.current,lastShow=(0,usePrevious.A)(show),lastSourceEvent=(0,react.useRef)(null),focusInDropdown=(0,react.useRef)(!1),onSelectCtx=(0,react.useContext)(esm_SelectableContext),toggle=(0,react.useCallback)(((nextShow,event,source=(null==event?void 0:event.type))=>{onToggle(nextShow,{originalEvent:event,source})}),[onToggle]),handleSelect=(0,useEventCallback.A)(((key,event)=>{null==onSelect||onSelect(key,event),toggle(!1,event,"select"),event.isPropagationStopped()||null==onSelectCtx||onSelectCtx(key,event)})),context=(0,react.useMemo)((()=>({toggle,placement,show,menuElement,toggleElement,setMenu,setToggle})),[toggle,placement,show,menuElement,toggleElement,setMenu,setToggle]);menuElement&&lastShow&&!show&&(focusInDropdown.current=menuElement.contains(menuElement.ownerDocument.activeElement));const focusToggle=(0,useEventCallback.A)((()=>{toggleElement&&toggleElement.focus&&toggleElement.focus()})),maybeFocusFirst=(0,useEventCallback.A)((()=>{const type=lastSourceEvent.current;let focusType=focusFirstItemOnShow;if(null==focusType&&(focusType=!(!menuRef.current||!isRoleMenu(menuRef.current))&&"keyboard"),!1===focusType||"keyboard"===focusType&&!/^key.+$/.test(type))return;const first=(0,querySelectorAll.A)(menuRef.current,itemSelector)[0];first&&first.focus&&first.focus()}));(0,react.useEffect)((()=>{show?maybeFocusFirst():focusInDropdown.current&&(focusInDropdown.current=!1,focusToggle())}),[show,focusInDropdown,focusToggle,maybeFocusFirst]),(0,react.useEffect)((()=>{lastSourceEvent.current=null}));const getNextFocusedChild=(current,offset)=>{if(!menuRef.current)return null;const items=(0,querySelectorAll.A)(menuRef.current,itemSelector);let index=items.indexOf(current)+offset;return index=Math.max(0,Math.min(index,items.length)),items[index]};return(0,useEventListener.A)((0,react.useCallback)((()=>window.document),[window]),"keydown",(event=>{var _menuRef$current,_toggleRef$current;const{key}=event,target=event.target,fromMenu=null==(_menuRef$current=menuRef.current)?void 0:_menuRef$current.contains(target),fromToggle=null==(_toggleRef$current=toggleRef.current)?void 0:_toggleRef$current.contains(target);if(/input|textarea/i.test(target.tagName)&&(" "===key||"Escape"!==key&&fromMenu||"Escape"===key&&"search"===target.type))return;if(!fromMenu&&!fromToggle)return;if(!("Tab"!==key||menuRef.current&&show))return;lastSourceEvent.current=event.type;const meta={originalEvent:event,source:event.type};switch(key){case"ArrowUp":{const next=getNextFocusedChild(target,-1);return next&&next.focus&&next.focus(),void event.preventDefault()}case"ArrowDown":if(event.preventDefault(),show){const next=getNextFocusedChild(target,1);next&&next.focus&&next.focus()}else onToggle(!0,meta);return;case"Tab":(0,addEventListener.Ay)(target.ownerDocument,"keyup",(e=>{var _menuRef$current2;("Tab"!==e.key||e.target)&&null!=(_menuRef$current2=menuRef.current)&&_menuRef$current2.contains(e.target)||onToggle(!1,meta)}),{once:!0});break;case"Escape":"Escape"===key&&(event.preventDefault(),event.stopPropagation()),onToggle(!1,meta)}})),(0,jsx_runtime.jsx)(esm_SelectableContext.Provider,{value:handleSelect,children:(0,jsx_runtime.jsx)(esm_DropdownContext.Provider,{value:context,children})})}Dropdown.displayName="Dropdown",Dropdown.Menu=esm_DropdownMenu,Dropdown.Toggle=esm_DropdownToggle,Dropdown.Item=esm_DropdownItem;const esm_Dropdown=Dropdown;var esm=__webpack_require__("./node_modules/uncontrollable/lib/esm/index.js"),esm_useEventCallback=__webpack_require__("./node_modules/@restart/hooks/esm/useEventCallback.js");const DropdownContext_DropdownContext=react.createContext({});DropdownContext_DropdownContext.displayName="DropdownContext";const react_bootstrap_esm_DropdownContext=DropdownContext_DropdownContext;var ThemeProvider=__webpack_require__("./node_modules/react-bootstrap/esm/ThemeProvider.js");const DropdownDivider=react.forwardRef((({className,bsPrefix,as:Component="hr",role="separator",...props},ref)=>(bsPrefix=(0,ThemeProvider.oU)(bsPrefix,"dropdown-divider"),(0,jsx_runtime.jsx)(Component,{ref,className:classnames_default()(className,bsPrefix),role,...props}))));DropdownDivider.displayName="DropdownDivider";const esm_DropdownDivider=DropdownDivider,DropdownHeader=react.forwardRef((({className,bsPrefix,as:Component="div",role="heading",...props},ref)=>(bsPrefix=(0,ThemeProvider.oU)(bsPrefix,"dropdown-header"),(0,jsx_runtime.jsx)(Component,{ref,className:classnames_default()(className,bsPrefix),role,...props}))));DropdownHeader.displayName="DropdownHeader";const esm_DropdownHeader=DropdownHeader;var Anchor=__webpack_require__("./node_modules/@restart/ui/esm/Anchor.js");const DropdownItem_DropdownItem=react.forwardRef((({bsPrefix,className,eventKey,disabled=!1,onClick,active,as:Component=Anchor.A,...props},ref)=>{const prefix=(0,ThemeProvider.oU)(bsPrefix,"dropdown-item"),[dropdownItemProps,meta]=useDropdownItem({key:eventKey,href:props.href,disabled,onClick,active});return(0,jsx_runtime.jsx)(Component,{...props,...dropdownItemProps,ref,className:classnames_default()(className,prefix,meta.isActive&&"active",disabled&&"disabled")})}));DropdownItem_DropdownItem.displayName="DropdownItem";const react_bootstrap_esm_DropdownItem=DropdownItem_DropdownItem,DropdownItemText=react.forwardRef((({className,bsPrefix,as:Component="span",...props},ref)=>(bsPrefix=(0,ThemeProvider.oU)(bsPrefix,"dropdown-item-text"),(0,jsx_runtime.jsx)(Component,{ref,className:classnames_default()(className,bsPrefix),...props}))));DropdownItemText.displayName="DropdownItemText";const esm_DropdownItemText=DropdownItemText;var useIsomorphicEffect=__webpack_require__("./node_modules/@restart/hooks/esm/useIsomorphicEffect.js"),esm_useMergedRefs=__webpack_require__("./node_modules/@restart/hooks/esm/useMergedRefs.js");__webpack_require__("./node_modules/warning/warning.js");const context=react.createContext(null);context.displayName="InputGroupContext";const InputGroupContext=context,NavbarContext_context=react.createContext(null);NavbarContext_context.displayName="NavbarContext";const NavbarContext=NavbarContext_context;__webpack_require__("./node_modules/invariant/browser.js");function useWrappedRefWithWarning(ref,componentName){return ref}function getDropdownMenuPlacement(alignEnd,dropDirection,isRTL){let placement=alignEnd?isRTL?"bottom-start":"bottom-end":isRTL?"bottom-end":"bottom-start";return"up"===dropDirection?placement=alignEnd?isRTL?"top-start":"top-end":isRTL?"top-end":"top-start":"end"===dropDirection?placement=alignEnd?isRTL?"left-end":"right-end":isRTL?"left-start":"right-start":"start"===dropDirection?placement=alignEnd?isRTL?"right-end":"left-end":isRTL?"right-start":"left-start":"down-centered"===dropDirection?placement="bottom":"up-centered"===dropDirection&&(placement="top"),placement}const DropdownMenu_DropdownMenu=react.forwardRef((({bsPrefix,className,align,rootCloseEvent,flip=!0,show:showProps,renderOnMount,as:Component="div",popperConfig,variant,...props},ref)=>{let alignEnd=!1;const isNavbar=(0,react.useContext)(NavbarContext),prefix=(0,ThemeProvider.oU)(bsPrefix,"dropdown-menu"),{align:contextAlign,drop,isRTL}=(0,react.useContext)(react_bootstrap_esm_DropdownContext);align=align||contextAlign;const isInputGroup=(0,react.useContext)(InputGroupContext),alignClasses=[];if(align)if("object"==typeof align){const keys=Object.keys(align);if(keys.length){const brkPoint=keys[0],direction=align[brkPoint];alignEnd="start"===direction,alignClasses.push(`${prefix}-${brkPoint}-${direction}`)}}else"end"===align&&(alignEnd=!0);const placement=getDropdownMenuPlacement(alignEnd,drop,isRTL),[menuProps,{hasShown,popper,show,toggle}]=useDropdownMenu({flip,rootCloseEvent,show:showProps,usePopper:!isNavbar&&0===alignClasses.length,offset:[0,2],popperConfig,placement});if(menuProps.ref=(0,esm_useMergedRefs.A)(useWrappedRefWithWarning(ref),menuProps.ref),(0,useIsomorphicEffect.A)((()=>{show&&(null==popper||popper.update())}),[show]),!hasShown&&!renderOnMount&&!isInputGroup)return null;"string"!=typeof Component&&(menuProps.show=show,menuProps.close=()=>null==toggle?void 0:toggle(!1),menuProps.align=align);let style=props.style;return null!=popper&&popper.placement&&(style={...props.style,...menuProps.style},props["x-placement"]=popper.placement),(0,jsx_runtime.jsx)(Component,{...props,...menuProps,style,...(alignClasses.length||isNavbar)&&{"data-bs-popper":"static"},className:classnames_default()(className,prefix,show&&"show",alignEnd&&`${prefix}-end`,variant&&`${prefix}-${variant}`,...alignClasses)})}));DropdownMenu_DropdownMenu.displayName="DropdownMenu";const react_bootstrap_esm_DropdownMenu=DropdownMenu_DropdownMenu;var esm_Button=__webpack_require__("./node_modules/react-bootstrap/esm/Button.js");const DropdownToggle_DropdownToggle=react.forwardRef((({bsPrefix,split,className,childBsPrefix,as:Component=esm_Button.A,...props},ref)=>{const prefix=(0,ThemeProvider.oU)(bsPrefix,"dropdown-toggle"),dropdownContext=(0,react.useContext)(esm_DropdownContext);void 0!==childBsPrefix&&(props.bsPrefix=childBsPrefix);const[toggleProps]=useDropdownToggle();return toggleProps.ref=(0,esm_useMergedRefs.A)(toggleProps.ref,useWrappedRefWithWarning(ref)),(0,jsx_runtime.jsx)(Component,{className:classnames_default()(className,prefix,split&&`${prefix}-split`,(null==dropdownContext?void 0:dropdownContext.show)&&"show"),...toggleProps,...props})}));DropdownToggle_DropdownToggle.displayName="DropdownToggle";const react_bootstrap_esm_DropdownToggle=DropdownToggle_DropdownToggle,Dropdown_Dropdown=react.forwardRef(((pProps,ref)=>{const{bsPrefix,drop="down",show,className,align="start",onSelect,onToggle,focusFirstItemOnShow,as:Component="div",navbar:_4,autoClose=!0,...props}=(0,esm.Zw)(pProps,{show:"onToggle"}),isInputGroup=(0,react.useContext)(InputGroupContext),prefix=(0,ThemeProvider.oU)(bsPrefix,"dropdown"),isRTL=(0,ThemeProvider.Wz)(),handleToggle=(0,esm_useEventCallback.A)(((nextShow,meta)=>{var _meta$originalEvent;var source;(null==(_meta$originalEvent=meta.originalEvent)||null==(_meta$originalEvent=_meta$originalEvent.target)?void 0:_meta$originalEvent.classList.contains("dropdown-toggle"))&&"mousedown"===meta.source||(meta.originalEvent.currentTarget!==document||"keydown"===meta.source&&"Escape"!==meta.originalEvent.key||(meta.source="rootClose"),source=meta.source,(!1===autoClose?"click"===source:"inside"===autoClose?"rootClose"!==source:"outside"!==autoClose||"select"!==source)&&(null==onToggle||onToggle(nextShow,meta)))})),placement=getDropdownMenuPlacement("end"===align,drop,isRTL),contextValue=(0,react.useMemo)((()=>({align,drop,isRTL})),[align,drop,isRTL]),directionClasses={down:prefix,"down-centered":`${prefix}-center`,up:"dropup","up-centered":"dropup-center dropup",end:"dropend",start:"dropstart"};return(0,jsx_runtime.jsx)(react_bootstrap_esm_DropdownContext.Provider,{value:contextValue,children:(0,jsx_runtime.jsx)(esm_Dropdown,{placement,show,onSelect,onToggle:handleToggle,focusFirstItemOnShow,itemSelector:`.${prefix}-item:not(.disabled):not(:disabled)`,children:isInputGroup?props.children:(0,jsx_runtime.jsx)(Component,{...props,ref,className:classnames_default()(className,show&&"show",directionClasses[drop])})})})}));Dropdown_Dropdown.displayName="Dropdown";const react_bootstrap_esm_Dropdown=Object.assign(Dropdown_Dropdown,{Toggle:react_bootstrap_esm_DropdownToggle,Menu:react_bootstrap_esm_DropdownMenu,Item:react_bootstrap_esm_DropdownItem,ItemText:esm_DropdownItemText,Divider:esm_DropdownDivider,Header:esm_DropdownHeader})},"./node_modules/react-bootstrap/esm/Image.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{"use strict";__webpack_require__.d(__webpack_exports__,{A:()=>__WEBPACK_DEFAULT_EXPORT__});var classnames__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("./node_modules/classnames/index.js"),classnames__WEBPACK_IMPORTED_MODULE_0___default=__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_0__),react__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__("./node_modules/react/index.js"),prop_types__WEBPACK_IMPORTED_MODULE_3__=__webpack_require__("./node_modules/prop-types/index.js"),prop_types__WEBPACK_IMPORTED_MODULE_3___default=__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_3__),_ThemeProvider__WEBPACK_IMPORTED_MODULE_4__=__webpack_require__("./node_modules/react-bootstrap/esm/ThemeProvider.js"),react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__=__webpack_require__("./node_modules/react/jsx-runtime.js");prop_types__WEBPACK_IMPORTED_MODULE_3___default().string,prop_types__WEBPACK_IMPORTED_MODULE_3___default().bool,prop_types__WEBPACK_IMPORTED_MODULE_3___default().bool,prop_types__WEBPACK_IMPORTED_MODULE_3___default().bool,prop_types__WEBPACK_IMPORTED_MODULE_3___default().bool;const Image=react__WEBPACK_IMPORTED_MODULE_1__.forwardRef((({bsPrefix,className,fluid=!1,rounded=!1,roundedCircle=!1,thumbnail=!1,...props},ref)=>(bsPrefix=(0,_ThemeProvider__WEBPACK_IMPORTED_MODULE_4__.oU)(bsPrefix,"img"),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)("img",{ref,...props,className:classnames__WEBPACK_IMPORTED_MODULE_0___default()(className,fluid&&`${bsPrefix}-fluid`,rounded&&"rounded",roundedCircle&&"rounded-circle",thumbnail&&`${bsPrefix}-thumbnail`)}))));Image.displayName="Image";const __WEBPACK_DEFAULT_EXPORT__=Image},"./node_modules/react-bootstrap/esm/ThemeProvider.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{"use strict";__webpack_require__.d(__webpack_exports__,{Jm:()=>useBootstrapMinBreakpoint,Wz:()=>useIsRTL,gy:()=>useBootstrapBreakpoints,oU:()=>useBootstrapPrefix});var react__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("./node_modules/react/index.js");__webpack_require__("./node_modules/react/jsx-runtime.js");const DEFAULT_BREAKPOINTS=["xxl","xl","lg","md","sm","xs"],ThemeContext=react__WEBPACK_IMPORTED_MODULE_0__.createContext({prefixes:{},breakpoints:DEFAULT_BREAKPOINTS,minBreakpoint:"xs"}),{Consumer,Provider}=ThemeContext;function useBootstrapPrefix(prefix,defaultPrefix){const{prefixes}=(0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(ThemeContext);return prefix||prefixes[defaultPrefix]||defaultPrefix}function useBootstrapBreakpoints(){const{breakpoints}=(0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(ThemeContext);return breakpoints}function useBootstrapMinBreakpoint(){const{minBreakpoint}=(0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(ThemeContext);return minBreakpoint}function useIsRTL(){const{dir}=(0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(ThemeContext);return"rtl"===dir}},"./node_modules/warning/warning.js":module=>{"use strict";var warning=function(){};module.exports=warning}}]);