"use strict";(self.webpackChunkclient=self.webpackChunkclient||[]).push([[427],{"./node_modules/react-bootstrap/esm/Alert.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.d(__webpack_exports__,{A:()=>esm_Alert});var classnames=__webpack_require__("./node_modules/classnames/index.js"),classnames_default=__webpack_require__.n(classnames),react=__webpack_require__("./node_modules/react/index.js"),esm=__webpack_require__("./node_modules/uncontrollable/lib/esm/index.js"),useEventCallback=__webpack_require__("./node_modules/@restart/hooks/esm/useEventCallback.js"),ThemeProvider=__webpack_require__("./node_modules/react-bootstrap/esm/ThemeProvider.js"),divWithClassName=__webpack_require__("./node_modules/react-bootstrap/esm/divWithClassName.js"),jsx_runtime=__webpack_require__("./node_modules/react/jsx-runtime.js");const DivStyledAsH4=(0,divWithClassName.A)("h4");DivStyledAsH4.displayName="DivStyledAsH4";const AlertHeading=react.forwardRef((({className,bsPrefix,as:Component=DivStyledAsH4,...props},ref)=>(bsPrefix=(0,ThemeProvider.oU)(bsPrefix,"alert-heading"),(0,jsx_runtime.jsx)(Component,{ref,className:classnames_default()(className,bsPrefix),...props}))));AlertHeading.displayName="AlertHeading";const esm_AlertHeading=AlertHeading;var Anchor=__webpack_require__("./node_modules/@restart/ui/esm/Anchor.js");const AlertLink=react.forwardRef((({className,bsPrefix,as:Component=Anchor.A,...props},ref)=>(bsPrefix=(0,ThemeProvider.oU)(bsPrefix,"alert-link"),(0,jsx_runtime.jsx)(Component,{ref,className:classnames_default()(className,bsPrefix),...props}))));AlertLink.displayName="AlertLink";const esm_AlertLink=AlertLink;var Fade=__webpack_require__("./node_modules/react-bootstrap/esm/Fade.js"),CloseButton=__webpack_require__("./node_modules/react-bootstrap/esm/CloseButton.js");const Alert=react.forwardRef(((uncontrolledProps,ref)=>{const{bsPrefix,show=!0,closeLabel="Close alert",closeVariant,className,children,variant="primary",onClose,dismissible,transition=Fade.A,...props}=(0,esm.Zw)(uncontrolledProps,{show:"onClose"}),prefix=(0,ThemeProvider.oU)(bsPrefix,"alert"),handleClose=(0,useEventCallback.A)((e=>{onClose&&onClose(!1,e)})),Transition=!0===transition?Fade.A:transition,alert=(0,jsx_runtime.jsxs)("div",{role:"alert",...Transition?void 0:props,ref,className:classnames_default()(className,prefix,variant&&`${prefix}-${variant}`,dismissible&&`${prefix}-dismissible`),children:[dismissible&&(0,jsx_runtime.jsx)(CloseButton.A,{onClick:handleClose,"aria-label":closeLabel,variant:closeVariant}),children]});return Transition?(0,jsx_runtime.jsx)(Transition,{unmountOnExit:!0,...props,ref:void 0,in:show,children:alert}):show?alert:null}));Alert.displayName="Alert";const esm_Alert=Object.assign(Alert,{Link:esm_AlertLink,Heading:esm_AlertHeading})},"./node_modules/react-bootstrap/esm/Card.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.d(__webpack_exports__,{A:()=>esm_Card});var classnames=__webpack_require__("./node_modules/classnames/index.js"),classnames_default=__webpack_require__.n(classnames),react=__webpack_require__("./node_modules/react/index.js"),ThemeProvider=__webpack_require__("./node_modules/react-bootstrap/esm/ThemeProvider.js"),jsx_runtime=__webpack_require__("./node_modules/react/jsx-runtime.js");const CardBody=react.forwardRef((({className,bsPrefix,as:Component="div",...props},ref)=>(bsPrefix=(0,ThemeProvider.oU)(bsPrefix,"card-body"),(0,jsx_runtime.jsx)(Component,{ref,className:classnames_default()(className,bsPrefix),...props}))));CardBody.displayName="CardBody";const esm_CardBody=CardBody,CardFooter=react.forwardRef((({className,bsPrefix,as:Component="div",...props},ref)=>(bsPrefix=(0,ThemeProvider.oU)(bsPrefix,"card-footer"),(0,jsx_runtime.jsx)(Component,{ref,className:classnames_default()(className,bsPrefix),...props}))));CardFooter.displayName="CardFooter";const esm_CardFooter=CardFooter,context=react.createContext(null);context.displayName="CardHeaderContext";const CardHeaderContext=context,CardHeader=react.forwardRef((({bsPrefix,className,as:Component="div",...props},ref)=>{const prefix=(0,ThemeProvider.oU)(bsPrefix,"card-header"),contextValue=(0,react.useMemo)((()=>({cardHeaderBsPrefix:prefix})),[prefix]);return(0,jsx_runtime.jsx)(CardHeaderContext.Provider,{value:contextValue,children:(0,jsx_runtime.jsx)(Component,{ref,...props,className:classnames_default()(className,prefix)})})}));CardHeader.displayName="CardHeader";const esm_CardHeader=CardHeader,CardImg=react.forwardRef((({bsPrefix,className,variant,as:Component="img",...props},ref)=>{const prefix=(0,ThemeProvider.oU)(bsPrefix,"card-img");return(0,jsx_runtime.jsx)(Component,{ref,className:classnames_default()(variant?`${prefix}-${variant}`:prefix,className),...props})}));CardImg.displayName="CardImg";const esm_CardImg=CardImg,CardImgOverlay=react.forwardRef((({className,bsPrefix,as:Component="div",...props},ref)=>(bsPrefix=(0,ThemeProvider.oU)(bsPrefix,"card-img-overlay"),(0,jsx_runtime.jsx)(Component,{ref,className:classnames_default()(className,bsPrefix),...props}))));CardImgOverlay.displayName="CardImgOverlay";const esm_CardImgOverlay=CardImgOverlay,CardLink=react.forwardRef((({className,bsPrefix,as:Component="a",...props},ref)=>(bsPrefix=(0,ThemeProvider.oU)(bsPrefix,"card-link"),(0,jsx_runtime.jsx)(Component,{ref,className:classnames_default()(className,bsPrefix),...props}))));CardLink.displayName="CardLink";const esm_CardLink=CardLink;var divWithClassName=__webpack_require__("./node_modules/react-bootstrap/esm/divWithClassName.js");const DivStyledAsH6=(0,divWithClassName.A)("h6"),CardSubtitle=react.forwardRef((({className,bsPrefix,as:Component=DivStyledAsH6,...props},ref)=>(bsPrefix=(0,ThemeProvider.oU)(bsPrefix,"card-subtitle"),(0,jsx_runtime.jsx)(Component,{ref,className:classnames_default()(className,bsPrefix),...props}))));CardSubtitle.displayName="CardSubtitle";const esm_CardSubtitle=CardSubtitle,CardText=react.forwardRef((({className,bsPrefix,as:Component="p",...props},ref)=>(bsPrefix=(0,ThemeProvider.oU)(bsPrefix,"card-text"),(0,jsx_runtime.jsx)(Component,{ref,className:classnames_default()(className,bsPrefix),...props}))));CardText.displayName="CardText";const esm_CardText=CardText,DivStyledAsH5=(0,divWithClassName.A)("h5"),CardTitle=react.forwardRef((({className,bsPrefix,as:Component=DivStyledAsH5,...props},ref)=>(bsPrefix=(0,ThemeProvider.oU)(bsPrefix,"card-title"),(0,jsx_runtime.jsx)(Component,{ref,className:classnames_default()(className,bsPrefix),...props}))));CardTitle.displayName="CardTitle";const esm_CardTitle=CardTitle,Card=react.forwardRef((({bsPrefix,className,bg,text,border,body=!1,children,as:Component="div",...props},ref)=>{const prefix=(0,ThemeProvider.oU)(bsPrefix,"card");return(0,jsx_runtime.jsx)(Component,{ref,...props,className:classnames_default()(className,prefix,bg&&`bg-${bg}`,text&&`text-${text}`,border&&`border-${border}`),children:body?(0,jsx_runtime.jsx)(esm_CardBody,{children}):children})}));Card.displayName="Card";const esm_Card=Object.assign(Card,{Img:esm_CardImg,Title:esm_CardTitle,Subtitle:esm_CardSubtitle,Body:esm_CardBody,Link:esm_CardLink,Text:esm_CardText,Header:esm_CardHeader,Footer:esm_CardFooter,ImgOverlay:esm_CardImgOverlay})},"./node_modules/react-bootstrap/esm/Spinner.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.d(__webpack_exports__,{A:()=>__WEBPACK_DEFAULT_EXPORT__});var classnames__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("./node_modules/classnames/index.js"),classnames__WEBPACK_IMPORTED_MODULE_0___default=__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_0__),react__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__("./node_modules/react/index.js"),_ThemeProvider__WEBPACK_IMPORTED_MODULE_3__=__webpack_require__("./node_modules/react-bootstrap/esm/ThemeProvider.js"),react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__=__webpack_require__("./node_modules/react/jsx-runtime.js");const Spinner=react__WEBPACK_IMPORTED_MODULE_1__.forwardRef((({bsPrefix,variant,animation="border",size,as:Component="div",className,...props},ref)=>{const bsSpinnerPrefix=`${bsPrefix=(0,_ThemeProvider__WEBPACK_IMPORTED_MODULE_3__.oU)(bsPrefix,"spinner")}-${animation}`;return(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Component,{ref,...props,className:classnames__WEBPACK_IMPORTED_MODULE_0___default()(className,bsSpinnerPrefix,size&&`${bsSpinnerPrefix}-${size}`,variant&&`text-${variant}`)})}));Spinner.displayName="Spinner";const __WEBPACK_DEFAULT_EXPORT__=Spinner},"./src/api/axiosInstance.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.d(__webpack_exports__,{A:()=>__WEBPACK_DEFAULT_EXPORT__});var axios__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("./node_modules/axios/lib/axios.js");const API_BASE_URL=__webpack_require__("./node_modules/process/browser.js").env.REACT_APP_API_BASE_URL||"http://localhost:3001",axiosInstance=axios__WEBPACK_IMPORTED_MODULE_0__.A.create({baseURL:API_BASE_URL,timeout:1e4,headers:{"Content-Type":"application/json"}});axiosInstance.interceptors.request.use((config=>{const token=localStorage.getItem("auth_token");return console.log("Axios Interceptor - Reading token using key 'auth_token':",token?"Found":"Not Found"),token?(config.headers.Authorization=`Bearer ${token}`,console.log("Axios Interceptor - Setting Authorization header.")):console.log("Axios Interceptor - 'auth_token' not found in localStorage."),config}),(error=>Promise.reject(error))),axiosInstance.interceptors.response.use((response=>response),(error=>(error.response&&401===error.response.status&&console.error("Unauthorized access - 401"),Promise.reject(error))));const __WEBPACK_DEFAULT_EXPORT__=axiosInstance},"./src/hooks/useFeatureGuard.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.d(__webpack_exports__,{B:()=>useFeatureGuard});var react__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("./node_modules/react/index.js"),react_router_dom__WEBPACK_IMPORTED_MODULE_2__=__webpack_require__("./node_modules/react-router/dist/development/chunk-AYJ5UCUI.mjs"),_context_AuthContext__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__("./src/context/AuthContext.js");const planOrder=["free","proA","proB","enterprise"];function useFeatureGuard(minPlan){const navigate=(0,react_router_dom__WEBPACK_IMPORTED_MODULE_2__.Zp)(),{user}=(0,_context_AuthContext__WEBPACK_IMPORTED_MODULE_1__.As)(),userPlan=(null==user?void 0:user.plan)||"free";(0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)((()=>{planOrder.indexOf(userPlan)<planOrder.indexOf(minPlan)&&navigate("/upgrade",{replace:!0})}),[userPlan,minPlan,navigate])}},"./src/pages/BrandSettingsPage.stories.jsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{Default:()=>Default,__namedExportsOrder:()=>__namedExportsOrder,default:()=>BrandSettingsPage_stories});var react=__webpack_require__("./node_modules/react/index.js"),classnames=__webpack_require__("./node_modules/classnames/index.js"),classnames_default=__webpack_require__.n(classnames),ThemeProvider=__webpack_require__("./node_modules/react-bootstrap/esm/ThemeProvider.js"),jsx_runtime=__webpack_require__("./node_modules/react/jsx-runtime.js");const Container=react.forwardRef((({bsPrefix,fluid=!1,as:Component="div",className,...props},ref)=>{const prefix=(0,ThemeProvider.oU)(bsPrefix,"container"),suffix="string"==typeof fluid?`-${fluid}`:"-fluid";return(0,jsx_runtime.jsx)(Component,{ref,...props,className:classnames_default()(className,fluid?`${prefix}${suffix}`:prefix)})}));Container.displayName="Container";const esm_Container=Container;var Card=__webpack_require__("./node_modules/react-bootstrap/esm/Card.js"),Spinner=__webpack_require__("./node_modules/react-bootstrap/esm/Spinner.js"),Alert=__webpack_require__("./node_modules/react-bootstrap/esm/Alert.js"),Form=__webpack_require__("./node_modules/react-bootstrap/esm/Form.js"),Button=__webpack_require__("./node_modules/react-bootstrap/esm/Button.js"),chunk_AYJ5UCUI=__webpack_require__("./node_modules/react-router/dist/development/chunk-AYJ5UCUI.mjs"),axiosInstance=__webpack_require__("./src/api/axiosInstance.js"),dist=__webpack_require__("./node_modules/react-toastify/dist/index.mjs"),useFeatureGuard=(__webpack_require__("./node_modules/react-toastify/dist/ReactToastify.css"),__webpack_require__("./src/hooks/useFeatureGuard.js"));function BrandSettingsPage(){(0,useFeatureGuard.B)("free");const navigate=(0,chunk_AYJ5UCUI.Zp)(),[profile,setProfile]=(0,react.useState)({brandName:"",brandDescription:"",targetAudience:"",toneAndVoice:"",keywords:""}),[isLoading,setIsLoading]=(0,react.useState)(!0),[isSaving,setIsSaving]=(0,react.useState)(!1),[error,setError]=(0,react.useState)(null),[profileExists,setProfileExists]=(0,react.useState)(!1);(0,react.useEffect)((()=>{(async()=>{setIsLoading(!0),setError(null),setProfileExists(!1);try{console.log("Fetching brand profile...");const response=await axiosInstance.A.get("/api/brand-profile");console.log("[DEBUG] GET /api/brand-profile response:",response),response.data&&response.data.profile?(console.log("Brand profile found:",response.data.profile),setProfile({brandName:response.data.profile.profile_name||"",brandDescription:response.data.profile.brand_description||"",targetAudience:response.data.profile.target_audience||"",toneAndVoice:(response.data.profile.brand_tone||[]).join(", "),keywords:(response.data.profile.brand_keywords||[]).join(", ")}),setProfileExists(!0),console.log("[DEBUG] Profile found, setting profileExists = true")):(console.log("No existing brand profile data returned (profile is null/undefined)."),console.log("[DEBUG] Profile not found or null, profileExists remains false"))}catch(err){if(console.error("Error fetching brand profile:",err),err.response&&404===err.response.status)console.log("No existing brand profile found (API returned 404).");else{var _err$response,_err$response$data;const message=(null===(_err$response=err.response)||void 0===_err$response||null===(_err$response$data=_err$response.data)||void 0===_err$response$data?void 0:_err$response$data.message)||"Failed to load brand profile details.";setError(message),dist.oR.error(message)}}finally{setIsLoading(!1)}})()}),[]);const handleChange=e=>{const{name,value}=e.target;setProfile((prev=>({...prev,[name]:value})))};return(0,jsx_runtime.jsxs)(esm_Container,{className:"mt-4",children:[(0,jsx_runtime.jsxs)(Card.A,{children:[(0,jsx_runtime.jsx)(Card.A.Header,{as:"h5",children:"Brand Profile Settings"}),(0,jsx_runtime.jsxs)(Card.A.Body,{children:[(0,jsx_runtime.jsx)(Card.A.Text,{children:"Define your brand's identity here. This information helps the AI generate content that aligns with your style and goals."}),isLoading?(0,jsx_runtime.jsx)("div",{className:"text-center",children:(0,jsx_runtime.jsx)(Spinner.A,{animation:"border",role:"status",children:(0,jsx_runtime.jsx)("span",{className:"visually-hidden",children:"Loading..."})})}):error&&!isSaving?(0,jsx_runtime.jsxs)(Alert.A,{variant:"danger",children:["Error loading profile: ",error]}):(0,jsx_runtime.jsxs)(Form.A,{onSubmit:async e=>{e.preventDefault(),setIsSaving(!0),setError(null);const apiData={profile_name:profile.brandName,brand_description:profile.brandDescription,target_audience:profile.targetAudience,brand_tone:profile.toneAndVoice?profile.toneAndVoice.split(",").map((t=>t.trim())).filter((t=>""!==t)):[],brand_keywords:profile.keywords?profile.keywords.split(",").map((k=>k.trim())).filter((k=>""!==k)):[]};try{let response;console.log("Saving brand profile with mapped data:",apiData),console.log("[DEBUG] handleSubmit: profileExists state is:",profileExists),profileExists?(console.log("Updating existing profile (PUT)"),response=await axiosInstance.A.put("/api/brand-profile",apiData)):(console.log("Creating new profile (POST)"),response=await axiosInstance.A.post("/api/brand-profile",apiData)),console.log("Save response:",response.data),dist.oR.success(response.data.message||"Brand profile saved successfully!"),setProfileExists(!0)}catch(err){var _err$response2,_err$response2$data;console.error("Error saving brand profile:",err);const message=(null===(_err$response2=err.response)||void 0===_err$response2||null===(_err$response2$data=_err$response2.data)||void 0===_err$response2$data?void 0:_err$response2$data.message)||"Failed to save brand profile.";setError(message),dist.oR.error(message)}finally{setIsSaving(!1)}},children:[(0,jsx_runtime.jsxs)(Form.A.Group,{className:"mb-3",controlId:"brandName",children:[(0,jsx_runtime.jsx)(Form.A.Label,{children:"Brand Name"}),(0,jsx_runtime.jsx)(Form.A.Control,{type:"text",name:"brandName",placeholder:"Your Company or Product Name",value:profile.brandName,onChange:handleChange,disabled:isSaving,required:!0})]}),(0,jsx_runtime.jsxs)(Form.A.Group,{className:"mb-3",controlId:"brandDescription",children:[(0,jsx_runtime.jsx)(Form.A.Label,{children:"Brand Description"}),(0,jsx_runtime.jsx)(Form.A.Control,{as:"textarea",rows:3,name:"brandDescription",placeholder:"What does your brand do? What makes it unique?",value:profile.brandDescription,onChange:handleChange,disabled:isSaving})]}),(0,jsx_runtime.jsxs)(Form.A.Group,{className:"mb-3",controlId:"targetAudience",children:[(0,jsx_runtime.jsx)(Form.A.Label,{children:"Target Audience"}),(0,jsx_runtime.jsx)(Form.A.Control,{type:"text",name:"targetAudience",placeholder:"e.g., Small business owners, Developers, Young professionals",value:profile.targetAudience,onChange:handleChange,disabled:isSaving})]}),(0,jsx_runtime.jsxs)(Form.A.Group,{className:"mb-3",controlId:"toneAndVoice",children:[(0,jsx_runtime.jsx)(Form.A.Label,{children:"Tone and Voice"}),(0,jsx_runtime.jsx)(Form.A.Control,{type:"text",name:"toneAndVoice",placeholder:"e.g., Friendly, Professional, Witty, Authoritative, Casual",value:profile.toneAndVoice,onChange:handleChange,disabled:isSaving}),(0,jsx_runtime.jsx)(Form.A.Text,{className:"text-muted",children:"Describe the desired personality of your brand's communication."})]}),(0,jsx_runtime.jsxs)(Form.A.Group,{className:"mb-3",controlId:"keywords",children:[(0,jsx_runtime.jsx)(Form.A.Label,{children:"Keywords"}),(0,jsx_runtime.jsx)(Form.A.Control,{type:"text",name:"keywords",placeholder:"e.g., AI, content marketing, social media, productivity",value:profile.keywords,onChange:handleChange,disabled:isSaving}),(0,jsx_runtime.jsx)(Form.A.Text,{className:"text-muted",children:"Comma-separated keywords relevant to your brand or topics."})]}),error&&isSaving&&(0,jsx_runtime.jsx)(Alert.A,{variant:"danger",children:error}),(0,jsx_runtime.jsxs)("div",{className:"d-flex gap-2 mt-3",children:[(0,jsx_runtime.jsx)(Button.A,{variant:"primary",type:"submit",disabled:isSaving||isLoading,children:isSaving?(0,jsx_runtime.jsxs)(jsx_runtime.Fragment,{children:[(0,jsx_runtime.jsx)(Spinner.A,{as:"span",animation:"border",size:"sm",role:"status","aria-hidden":"true"}),(0,jsx_runtime.jsx)("span",{className:"ms-2",children:"Saving..."})]}):profileExists?"Update Profile":"Save Profile"}),(0,jsx_runtime.jsx)(Button.A,{variant:"outline-secondary",onClick:()=>navigate(-1),disabled:isSaving||isLoading,children:"Back"})]})]})]})]}),(0,jsx_runtime.jsx)(dist.N9,{position:"top-right",autoClose:3e3,hideProgressBar:!1,closeOnClick:!0})]})}const pages_BrandSettingsPage=BrandSettingsPage;BrandSettingsPage.__docgenInfo={description:"",methods:[],displayName:"BrandSettingsPage"};var AuthContext=__webpack_require__("./src/context/AuthContext.js");const BrandSettingsPage_stories={title:"Pages/Brand Settings Page",tags:["backend"],component:pages_BrandSettingsPage,decorators:[Story=>(0,jsx_runtime.jsx)(AuthContext.cy.Provider,{value:{user:{plan:"enterprise"}},children:(0,jsx_runtime.jsx)(chunk_AYJ5UCUI.fS,{initialEntries:["/settings/content-generator"],children:(0,jsx_runtime.jsx)(Story,{})})})]},Default=(args=>(0,jsx_runtime.jsx)(pages_BrandSettingsPage,{})).bind({});Default.args={};const __namedExportsOrder=["Default"];Default.parameters={...Default.parameters,docs:{...Default.parameters?.docs,source:{originalSource:"args => <BrandSettingsPage />",...Default.parameters?.docs?.source}}}}}]);