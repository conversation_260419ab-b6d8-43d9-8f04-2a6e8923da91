const express = require('express');
const moduleController = require('../controllers/moduleController');

const router = express.Router();

// 获取所有模块列表
router.get('/modules', moduleController.getAllModules);

// 获取特定模块
router.get('/modules/:moduleId', moduleController.getModuleById);

// 更新模块状态或高级配置
router.patch('/modules/:moduleId', moduleController.updateModule);

// 获取模块详细设置
router.get('/modules/:moduleId/settings', moduleController.getModuleSettings);

// 更新模块详细设置
router.put('/modules/:moduleId/settings', moduleController.updateModuleSettings);

module.exports = router; 