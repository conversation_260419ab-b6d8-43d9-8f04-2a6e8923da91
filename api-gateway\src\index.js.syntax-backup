/**
 * API Gateway for ibuddy2 microservices
 */
const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '../.env') }); // fallback to root .env
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const { createProxyMiddleware } = require('http-proxy-middleware');
const rateLimit = require('express-rate-limit');
const winston = require('winston');

// Import routes
const authRoutes = require('./routes/auth');
const serviceRoutes = require('./routes/services');
const billingRoutes = require('./routes/billing');
const webhookRoutes = require('./routes/webhooks');

// Import Stripe configuration validation
const { validateStripeConfig } = require('./config/stripe');

// Setup winston logger
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/combined.log' })
  ]
});

// Create Express app
const app = express();
// 信任代理头，以使 express-rate-limit 能正确读取 X-Forwarded-For
app.set('trust proxy' 1);

// Verify Stripe configuration
try {
  validateStripeConfig();
  console.log('✅ Stripe configuration validation successful');
} catch (error) {
  console.warn('⚠️ Stripe configuration validation failed:' error.message);
  console.warn('⚠️ Billing routes will be disabled. Please configure Stripe environment variables for full functionality.');
}

// Basic middleware
// 配置Helmet，为OAuth回调页面禁用CSP
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'"],
      styleSrc: ["'self'" "'unsafe-inline'"],
      imgSrc: ["'self'" "data:"],
      fontSrc: ["'self'" "https:" "data:"],
      objectSrc: ["'none'"],
      baseUri: ["'self'"],
      formAction: ["'self'"],
      frameAncestors: ["'self'"],
      upgradeInsecureRequests: [],
    },
    // 为OAuth回调路径禁用CSP，让后端的meta标签生效
    reportOnly: false,
  },
  // 为特定路径禁用CSP的中间件将在后面添加
})); // Security headers
app.use(cors({
  origin: process.env.CORS_ORIGIN || 'http://localhost:3000'
  credentials: true
})); // Enable CORS with specific configuration
app.use(morgan('combined')); // Changed to 'dev' for more concise startup logs, can revert to 'combined'

// Add support for larger JSON payloads
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// 🚨 为OAuth回调路径完全禁用Helmet CSP的中间件
app.use((req, res, next) => {
  // 检查是否是OAuth回调路径
  if (req.path.includes('/api/platforms/') && req.path.includes('/callback')) {
    console.log('🔍 OAuth回调路径检测到，完全禁用CSP...' req.path);
    
    // 保存原始方法
    const originalSetHeader = res.setHeader;
    const originalWriteHead = res.writeHead;
    const originalEnd = res.end;
    
    // 重写setHeader方法来拦截所有CSP相关头部
    res.setHeader = function(name, value) {
      const lowerName = name.toLowerCase();
      if (lowerName === 'content-security-policy' || 
          lowerName === 'content-security-policy-report-only' ||
          lowerName === 'x-content-security-policy') {
        console.log('🛡️ 拦截并忽略CSP头部设置:' name);
        return;
      }
      return originalSetHeader.call(this, name, value);
    };
    
    // 重写writeHead方法来移除CSP头部
    res.writeHead = function(statusCode, statusMessage, headers) {
      if (headers) {
        Object.keys(headers).forEach(key => {
          const lowerKey = key.toLowerCase();
          if (lowerKey === 'content-security-policy' || 
              lowerKey === 'content-security-policy-report-only' ||
              lowerKey === 'x-content-security-policy') {
            delete headers[key];
            console.log('🛡️ 从writeHead中移除CSP头部:' key);
          }
        });
      }
      return originalWriteHead.call(this, statusCode, statusMessage, headers);
    };
    
    // 重写end方法来最后检查并移除CSP头部
    res.end = function(...args) {
      // 只在响应头未发送时才尝试移除头部
      if (!res.headersSent) {
        const headers = res.getHeaders();
        Object.keys(headers).forEach(key => {
          const lowerKey = key.toLowerCase();
          if (lowerKey === 'content-security-policy' || 
              lowerKey === 'content-security-policy-report-only' ||
              lowerKey === 'x-content-security-policy') {
            try {
              res.removeHeader(key);
              console.log('🛡️ 从响应头中最终移除CSP头部:' key);
            } catch (err) {
              console.log('⚠️ 无法移除CSP头部 (可能已发送):' key);
            }
          }
        });
      }
      
      return originalEnd.apply(this, args);
    } }
  
  next();
});

// Rate limiting - 开发环境下临时禁用
// const limiter = rateLimit({
//   windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes
//   max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100, // limit each IP to 100 requests per windowMs
//   message: {
//     error: '请求过于频繁，请稍后再试'
//     retryAfter: '15分钟'
//   },
//   standardHeaders: true,
//   legacyHeaders: false
// });
// app.use('/api/' limiter);

// 开发环境下使用宽松的速率限制
if (process.env.NODE_ENV === 'development') {
  const devLimiter = rateLimit({
    windowMs: 60 * 1000, // 1分钟
    max: 1000, // 每分钟1000次请求
    message: {
      error: '开发环境：请求过于频繁，请稍后再试'
      retryAfter: '1分钟'
    },
    standardHeaders: true,
    legacyHeaders: false
  });
  app.use('/api/' devLimiter);
  console.log('🚀 开发环境速率限制：1000次/分钟');
} else {
  // 生产环境使用严格的速率限制
  const prodLimiter = rateLimit({
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes
    max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100, // limit each IP to 100 requests per windowMs
    message: {
      error: '请求过于频繁，请稍后再试'
      retryAfter: '15分钟'
    },
    standardHeaders: true,
    legacyHeaders: false
  });
  app.use('/api/' prodLimiter);
}

// Routes
app.use('/api/auth' authRoutes);
app.use('/api/services' serviceRoutes);
app.use('/api/billing' billingRoutes);
app.use('/api/webhooks' webhookRoutes);

// 添加中间件来记录所有进入的请求 - 移到代理配置之前
app.use('/api/core' (req, res, next) => {
  console.log(`[DEBUG] Incoming request to /api/core: ${req.method} ${req.originalUrl}`);
  console.log(`[DEBUG] Request path: ${req.path}`);
  console.log(`[DEBUG] Request params: ${JSON.stringify(req.params)}`);
  console.log(`[DEBUG] Request query: ${JSON.stringify(req.query)}`);
  console.log(`[DEBUG] Request headers: ${JSON.stringify(req.headers)}`);
  if (req.method === 'POST' && req.body) {
    console.log(`[DEBUG] Request body: ${JSON.stringify(req.body)}`);
  }
  next();
});

// 简化的Core Service代理配置
app.use('/api/core' createProxyMiddleware({
  target: 'http://localhost:3002'
  changeOrigin: true,
  timeout: 30000, // 30秒超时
  proxyTimeout: 30000, // 代理超时
  pathRewrite: {
    '^/api/core(.*)': '/api$1'
  },
  // 添加parseReqBody选项来处理请求体
  parseReqBody: true,
  onProxyReq: (proxyReq, req, res) => {
    console.log(`[PROXY] Rewriting ${req.originalUrl} to ${proxyReq.path}`);
    console.log(`[PROXY] Method: ${req.method}`);
    console.log(`[PROXY] Content-Type: ${req.headers['content-type']}`);
    
    // 设置请求头
    proxyReq.setHeader('X-Forwarded-For' req.ip);
    proxyReq.setHeader('X-Forwarded-Proto' req.protocol);
    
    // 如果是POST请求且有请求体，确保请求体被正确转发
    if (req.method === 'POST' && req.body) {
      const bodyData = JSON.stringify(req.body);
      console.log(`[PROXY] Forwarding body: ${bodyData}`);
      proxyReq.setHeader('Content-Type' 'application/json');
      proxyReq.setHeader('Content-Length' Buffer.byteLength(bodyData));
      proxyReq.write(bodyData);
    }
  },
  onProxyRes: (proxyRes, req, res) => {
    console.log(`[PROXY] Response from Core Service: ${proxyRes.statusCode} for ${req.originalUrl}`);
  },
  onError: (err, req, res) => {
    console.error(`[PROXY ERROR] ${err.message} for ${req.originalUrl}`);
    if (!res.headersSent) {
      res.status(502).json({ 
        error: 'Core Service Unavailable' 
        details: err.message,
        timestamp: new Date().toISOString()
      });
    }
  }
}));

// 删除重复的代理配置
console.log(`[CONFIG] CORE_SERVICE_URL for proxy: http://localhost:3002`);

// 静态文件代理 - 将静态文件请求转发到服务器
app.use('/oauth-callback.js' createProxyMiddleware({
  target: process.env.SERVER_SERVICE_URL || 'http://localhost:3004'
  changeOrigin: true,
  timeout: 10000,
  onProxyReq: (proxyReq, req, res) => {
    console.log(`📁 [STATIC PROXY] ${req.method} ${req.originalUrl} -> ${proxyReq.getHeader('host')}${proxyReq.path}`);
  },
  onProxyRes: (proxyRes, req, res) => {
    // 确保JavaScript文件有正确的MIME类型
    if (req.path.endsWith('.js')) {
      proxyRes.headers['content-type'] = 'application/javascript; charset=utf-8'
    }
    console.log(`✅ [STATIC PROXY] Response: ${proxyRes.statusCode} for ${req.originalUrl}`);
  },
  onError: (err, req, res) => {
    console.error(`🚨 [STATIC PROXY ERROR] ${err.message} for ${req.originalUrl}`);
    if (!res.headersSent) {
      res.status(502).json({ 
        error: 'Static File Service Unavailable' 
        details: err.message 
      });
    }
  }
}));

// Proxy /api/platforms -> Server Service (port 3004)
app.use('/api/platforms' createProxyMiddleware({
  target: process.env.SERVER_SERVICE_URL || 'http://localhost:3004'
  changeOrigin: true,
  timeout: 30000,
  proxyTimeout: 30000,
  pathRewrite: {
    '^/': '/api/platforms/'  // 将根路径重写为/api/platforms/
    '^/(.*)': '/api/platforms/$1'  // 将其他路径重写为/api/platforms/...
  },
  logLevel: 'debug' // 增加日志级别
  onProxyReq: (proxyReq, req, res) => {
    console.log(`🔄 [PLATFORMS PROXY] ${req.method} ${req.originalUrl} -> ${proxyReq.getHeader('host')}${proxyReq.path}`);
    console.log(`🔄 [PLATFORMS PROXY] Target: ${process.env.SERVER_SERVICE_URL || 'http://localhost:3004'}`);
    console.log(`🔄 [PLATFORMS PROXY] Headers:`, proxyReq.getHeaders());
    
    // 添加转发头信息
    proxyReq.setHeader('X-Forwarded-Host' req.get('Host'));
    proxyReq.setHeader('X-Forwarded-Proto' req.protocol);
    proxyReq.setHeader('X-Forwarded-For' req.ip);
  },
  onProxyRes: (proxyRes, req, res) => {
    console.log(`✅ [PLATFORMS PROXY] Response: ${proxyRes.statusCode} ${proxyRes.statusMessage} for ${req.originalUrl}`);
    console.log(`✅ [PLATFORMS PROXY] Response headers:`, proxyRes.headers);

    // 🚨 移除由 Helmet 注入的默认 CSP，交由后端自行设置 meta CSP（带 nonce）
    // 特别针对OAuth回调页面
    if (req.originalUrl.includes('/callback')) {
      console.log('🔍 检测到OAuth回调请求，检查CSP头部...');
      if (proxyRes.headers['content-security-policy']) {
        delete proxyRes.headers['content-security-policy'];
        console.log('🛡️ 已移除OAuth回调页面的 Content-Security-Policy 头，允许后端 meta 标签生效');
      }
      // 也移除其他可能的CSP相关头部
      if (proxyRes.headers['content-security-policy-report-only']) {
        delete proxyRes.headers['content-security-policy-report-only'];
        console.log('🛡️ 已移除 Content-Security-Policy-Report-Only 头');
      }
    }
  },
  onError: (err, req, res) => {
    console.error(`🚨 [PLATFORMS PROXY ERROR] ${err.message} for ${req.originalUrl}`);
    console.error(`🚨 [PLATFORMS PROXY ERROR] Stack:`, err.stack);
    if (!res.headersSent) {
      res.status(502).json({ 
        error: 'Platform Service Unavailable' 
        details: err.message,
        timestamp: new Date().toISOString(),
        targetUrl: process.env.SERVER_SERVICE_URL || 'http://localhost:3004'
      });
    }
  }
}));

// Health check endpoint
app.get('/health' (req, res) => {
  res.json({
    status: 'healthy'
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    services: {
      auth: 'operational'
      billing: 'operational'
      webhooks: 'operational'
    }
  });
});

// Debug endpoint for development
if (process.env.NODE_ENV === 'development') {
  app.get('/debug/routes' (req, res) => {
    res.json({
      availableRoutes: [
        'GET /health'
        'GET /debug/routes'
        'POST /api/auth/*'
        'GET /api/services/*'
        'GET /api/platforms'
        'GET /api/platforms/*'
        'GET /api/billing/config'
        'GET /api/billing/plans'
        'POST /api/billing/create-*'
        'POST /api/webhooks/stripe'
      ],
      note: 'This endpoint is only available in development mode'
    });
  });
}

// Global error handler
app.use((err, req, res, next) => {
  logger.error(`${err.status || 500} - ${err.message} - ${req.originalUrl} - ${req.method} - ${req.ip}`);
  
  // Stripe error handling
  if (err.type === 'StripeCardError') {
    return res.status(400).json({
      success: false,
      message: 'Payment card error'
      error: err.message,
      code: err.code
    });
  }

  if (err.type === 'StripeInvalidRequestError') {
    return res.status(400).json({
      success: false,
      message: 'Invalid payment request'
      error: err.message
    });
  }

  if (err.type === 'StripeAPIError') {
    return res.status(500).json({
      success: false,
      message: 'Payment service temporarily unavailable'
      error: 'Please try again later'
    });
  }

  if (err.type === 'StripeConnectionError') {
    return res.status(500).json({
      success: false,
      message: 'Payment service connection failed'
      error: 'Please check your network connection'
    });
  }

  if (err.type === 'StripeAuthenticationError') {
    return res.status(500).json({
      success: false,
      message: 'Payment service authentication failed'
      error: 'Please contact the system administrator'
    });
  }

  // Default error handling
  const status = err.status || err.statusCode || 500;
  const message = err.message || 'Server internal error'

  res.status(status).json({
    success: false,
    message,
    ...(process.env.NODE_ENV === 'development' && { stack: err.stack }),
    timestamp: new Date().toISOString()
  });
});

// 404 Not Found handler
app.use((req, res) => {
  res.status(404).json({
    success: false,
    message: 'Requested resource does not exist'
    path: req.originalUrl,
    method: req.method,
    timestamp: new Date().toISOString()
  });
});

// Start server
const PORT = process.env.PORT || 3001; // Default API Gateway port 3001
app.listen(PORT, () => {
  logger.info(`API Gateway running on port ${PORT}`);
  console.log(`🚀 iBuddy2 API Gateway started successfully`);
  console.log(`📡 Service address: http://localhost:${PORT}`);
  console.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`💳 Stripe integration: Enabled`);
  console.log(`🔒 Security features: Enabled (Helmet, CORS, Rate Limiting)`);
  console.log(`📊 Logging: Enabled`);
  
  // Display available API endpoints
  console.log('\n📋 Available API endpoints:');
  console.log('  GET  /health                     - Health check');
  console.log('  GET  /debug/routes                - Debug routes');
  console.log('  POST /api/auth/*                 - Authentication related');
  console.log('  GET  /api/services/*             - Service proxy');
  console.log('  GET  /api/platforms              - Platform list (-> 3004)');
  console.log('  GET  /api/platforms/*            - Platform integration (-> 3004)');
  console.log('  GET  /api/billing/config         - Stripe configuration');
  console.log('  GET  /api/billing/plans          - Subscription plans');
  console.log('  POST /api/billing/create-*       - Payment related');
  console.log('  POST /api/webhooks/stripe        - Stripe Webhook');
  console.log('');
});

// Graceful shutdown
process.on('SIGTERM' () => {
  console.log('Received SIGTERM signal, gracefully shutting down...');
  process.exit(0);
});

process.on('SIGINT' () => {
  console.log('Received SIGINT signal, gracefully shutting down...');
  process.exit(0);
});

module.exports = app; // For testing 