const moduleService = require('../services/moduleService');
const { ApiError } = require('../utils/errors');

/**
 * u83b7u53d6u6240u6709u6a21u5757u5217u8868
 */
exports.getAllModules = async (req, res, next) => {
  try {
    const modules = await moduleService.getAllModules();
    res.status(200).json(modules);
  } catch (error) {
    next(error);
  }
};

/**
 * u6839u636eIDu83b7u53d6u7279u5b9au6a21u5757
 */
exports.getModuleById = async (req, res, next) => {
  try {
    const { moduleId } = req.params;
    const module = await moduleService.getModuleById(moduleId);
    
    if (!module) {
      throw new ApiError(404, `u672au627eu5230IDu4e3a ${moduleId} u7684u6a21u5757`);
    }
    
    res.status(200).json(module);
  } catch (error) {
    next(error);
  }
};

/**
 * u66f4u65b0u6a21u5757u72b6u6001u6216u9ad8u7ea7u914du7f6e
 */
exports.updateModule = async (req, res, next) => {
  try {
    const { moduleId } = req.params;
    const updateData = req.body;
    
    // u9a8cu8bc1u8bf7u6c42u4f53
    if (Object.keys(updateData).length === 0) {
      throw new ApiError(400, 'u8bf7u63d0u4f9bu8981u66f4u65b0u7684u6570u636e');
    }
    
    // u68c0u67e5u6a21u5757u662fu5426u5b58u5728
    const existingModule = await moduleService.getModuleById(moduleId);
    if (!existingModule) {
      throw new ApiError(404, `u672au627eu5230IDu4e3a ${moduleId} u7684u6a21u5757`);
    }
    
    const updatedModule = await moduleService.updateModule(moduleId, updateData);
    res.status(200).json(updatedModule);
  } catch (error) {
    next(error);
  }
};

/**
 * u83b7u53d6u6a21u5757u7684u8be6u7ec6u8bbeu7f6e
 */
exports.getModuleSettings = async (req, res, next) => {
  try {
    const { moduleId } = req.params;
    
    // u68c0u67e5u6a21u5757u662fu5426u5b58u5728
    const module = await moduleService.getModuleById(moduleId);
    if (!module) {
      throw new ApiError(404, `u672au627eu5230IDu4e3a ${moduleId} u7684u6a21u5757`);
    }
    
    // u68c0u67e5u6a21u5757u662fu5426u6709u8be6u7ec6u8bbeu7f6e
    if (!module.hasDetailedSettings) {
      throw new ApiError(404, `u6a21u5757 ${moduleId} u6ca1u6709u8be6u7ec6u8bbeu7f6e`);
    }
    
    const settings = await moduleService.getModuleSettings(moduleId);
    
    // u5982u679cu9700u8981u8fd4u56deu5e26u6709u9a8cu8bc1u5143u6570u636eu7684u7ed3u6784
    const response = {
      settings: settings,
      metadata: await moduleService.getModuleSettingsMetadata(moduleId)
    };
    
    res.status(200).json(response);
  } catch (error) {
    next(error);
  }
};

/**
 * u66f4u65b0u6a21u5757u7684u8be6u7ec6u8bbeu7f6e
 */
exports.updateModuleSettings = async (req, res, next) => {
  try {
    const { moduleId } = req.params;
    const newSettings = req.body;
    
    // u9a8cu8bc1u8bf7u6c42u4f53
    if (Object.keys(newSettings).length === 0) {
      throw new ApiError(400, 'u8bf7u63d0u4f9bu8981u66f4u65b0u7684u8bbeu7f6e');
    }
    
    // u68c0u67e5u6a21u5757u662fu5426u5b58u5728
    const module = await moduleService.getModuleById(moduleId);
    if (!module) {
      throw new ApiError(404, `u672au627eu5230IDu4e3a ${moduleId} u7684u6a21u5757`);
    }
    
    // u68c0u67e5u6a21u5757u662fu5426u6709u8be6u7ec6u8bbeu7f6e
    if (!module.hasDetailedSettings) {
      throw new ApiError(404, `u6a21u5757 ${moduleId} u6ca1u6709u8be6u7ec6u8bbeu7f6e`);
    }
    
    // u9a8cu8bc1u8bbeu7f6e
    await moduleService.validateModuleSettings(moduleId, newSettings);
    
    // u66f4u65b0u8bbeu7f6e
    const updatedSettings = await moduleService.updateModuleSettings(moduleId, newSettings);
    res.status(200).json(updatedSettings);
  } catch (error) {
    next(error);
  }
}; 