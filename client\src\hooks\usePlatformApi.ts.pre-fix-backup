/**
 * 🔧 优化版Platform API管理Hook
 * 
 * 🎯 优化亮点:
 * - 智能错误处理和恢复
 * - 状态管理优化
 * - 内存泄漏防护
 * - 性能优化（防抖、缓存）
 * - 开发模式支持
 * - 详细的调试功能
 */

import { useState, useEffect, useCallback, useRef, useMemo } from 'react'
import { platformApiService, type PlatformStatus, type ConnectionResult, type ApiError } from '@/services/api/platformApi'
import { getDefaultPlatforms } from '@/config/platformConfig'

// 🎯 Hook返回类型
export interface UsePlatformApiReturn {
  // 数据状态
  platforms: PlatformStatus[];
  loading: boolean;
  error: ApiError | null;
  connectedCount: number;
  realTimeEnabled: boolean;
  
  // 操作方法
  refreshPlatforms: (force?: boolean) => Promise<void>;
  connectPlatform: (platformId: string, options?: { force?: boolean }) => Promise<ConnectionResult>;
  connectPlatformWithPopup: (platformId: string, options?: { width?: number; height?: number }) => Promise<ConnectionResult>;
  disconnectPlatform: (platformId: string) => Promise<boolean>;
  getPlatformAccountInfo: (platformId: string) => Promise<any>;
  getAuthUrl: (platformId: string, options?: { redirectUrl?: string }) => Promise<{ authUrl: string; state?: string; error?: string }>;
  
  // 实时功能
  toggleRealTime: () => void;
  subscribeToEvents: (platformId: string, onEvent: (event: any) => void) => () => void;
  
  // Webhook功能
  configureWebhook: (platformId: string, config: { url: string; events: string[]; secret?: string }) => Promise<any>;
  testWebhook: (platformId: string, webhookId: string) => Promise<any>;
  
  // 工具方法
  clearError: () => void;
  retryLastOperation: () => Promise<void>;
  getDebugInfo: () => any;
}

// 🔧 使用配置文件中的默认平台列表

// 🎯 主要Hook实现
export const usePlatformApi = (): UsePlatformApiReturn => {
  // 📊 状态管理
  const [platforms, setPlatforms] = useState<PlatformStatus[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<ApiError | null>(null);
  const [realTimeEnabled, setRealTimeEnabled] = useState(false);
  
  // 🔧 引用管理
  const eventSourcesRef = useRef<Map<string, EventSource>>(new Map());
  const refreshIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const lastOperationRef = useRef<(() => Promise<void>) | null>(null);
  const mountedRef = useRef(true);
  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 📈 计算属性 - 使用useMemo优化性能
  const connectedCount = useMemo(() => {
    return platforms.filter(p => p.status === 'Connected').length;
  }, [platforms]);

  // 🔔 OAuth回调处理函数
  const handleOAuthCallback = useCallback((callbackData: any) => {
    if (!mountedRef.current) return;
    
    console.log('🔔 收到OAuth回调:' callbackData);
    
    if (callbackData.type === 'OAUTH_CALLBACK') {
      if (callbackData.success) {
        // 成功连接：更新平台状态
        setPlatforms(prev => prev.map(p => 
          p.id === callbackData.platform 
            ? { 
                ...p, 
                status: 'Connected' as const, 
                accountInfo: callbackData.accountInfo,
                connectedAt: callbackData.timestamp
              }
            : p
        ));
        
        // 可选：显示成功通知
        console.log(`✅ ${callbackData.platform} 连接成功!`, callbackData.accountInfo);
        
        // 重新获取最新的平台状态
        setTimeout(() => {
          refreshPlatforms(true);
        }, 1000);
        
      } else {
        // 连接失败：显示错误
        const authError: ApiError = {
          code: 'OAUTH_FAILED'
          message: `${callbackData.platform} 连接失败: ${callbackData.error}`,
          details: callbackData,
          timestamp: callbackData.timestamp,
          platform: callbackData.platform
        };
        
        setError(authError);
        console.error(`❌ ${callbackData.platform} 连接失败:`, callbackData.error);
      }
    }
  }, []);

  // 🎧 设置OAuth回调监听器
  useEffect(() => {
    if (!mountedRef.current) return;
    
    console.log('🎧 设置OAuth回调监听器...');
    
    // 监听器1: PostMessage (弹窗通信)
    const handlePostMessage = (event: MessageEvent) => {
      // 验证来源安全性
      const allowedOrigins = [
        window.location.origin,
        process.env.REACT_APP_API_BASE_URL || 'http://localhost:3001'
        'http://localhost:3004' // Server服务
      ];
      
      if (!allowedOrigins.includes(event.origin)) {
        console.warn('⚠️ OAuth回调来源不被信任:' event.origin);
        return;
      }
      
      if (event.data?.type === 'OAUTH_CALLBACK') {
        handleOAuthCallback(event.data);
      }
    };
    
    // 监听器2: BroadcastChannel (跨标签页通信)
    let broadcastChannel: BroadcastChannel | null = null;
    if (typeof BroadcastChannel !== 'undefined') {
      broadcastChannel = new BroadcastChannel('oauth-callback');
      broadcastChannel.onmessage = (event) => {
        if (event.data?.type === 'OAUTH_CALLBACK') {
          handleOAuthCallback(event.data);
        }
      } }
    
    // 监听器3: LocalStorage 事件 (兜底方案)
    const handleStorageChange = (event: StorageEvent) => {
      if (event.key?.startsWith('oauth-callback-') && event.newValue) {
        try {
          const callbackData = JSON.parse(event.newValue);
          if (callbackData?.type === 'OAUTH_CALLBACK') {
            handleOAuthCallback(callbackData);
            // 清理已处理的回调数据
            localStorage.removeItem(event.key);
          }
        } catch (error) {
          console.warn('⚠️ 解析localStorage OAuth回调数据失败:' error);
        }
      }
    };
    
    // 添加事件监听器
    window.addEventListener('message' handlePostMessage);
    window.addEventListener('storage' handleStorageChange);
    
    console.log('✅ OAuth回调监听器已设置');

    // 清理函数
    return () => {
      window.removeEventListener('message' handlePostMessage);
      window.removeEventListener('storage' handleStorageChange);
      
      if (broadcastChannel) {
        broadcastChannel.close();
      }
      
      console.log('🧹 OAuth回调监听器已清理');
    } }, [handleOAuthCallback]);

  // 🧹 清理函数
  const cleanup = useCallback(() => {
    // 清理定时器
    if (refreshIntervalRef.current) {
      clearInterval(refreshIntervalRef.current);
      refreshIntervalRef.current = null;
    }
    
    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current);
      retryTimeoutRef.current = null;
    }
    
    // 关闭所有事件流
    eventSourcesRef.current.forEach(eventSource => {
      try {
        eventSource.close();
      } catch (e) {
        console.warn('🚨 关闭事件流时发生错误:' e);
      }
    });
    eventSourcesRef.current.clear();
    
    console.log('🧹 usePlatformApi 清理完成');
  }, []);

  // 🚨 错误处理优化
  const handleError = useCallback((error: any, context: string) => {
    if (!mountedRef.current) return;

    console.error(`🚨 [${context}] 错误:`, error);
    
    const apiError: ApiError = {
      code: error?.code || 'UNKNOWN_ERROR'
      message: error?.message || '发生未知错误'
      details: error?.details,
      timestamp: new Date().toISOString(),
      platform: error?.platform,
    };
    
    setError(apiError);
    
    // 自动重试某些错误
    if (shouldAutoRetry(apiError)) {
      console.log(`🔄 [${context}] 将在3秒后自动重试...`);
      retryTimeoutRef.current = setTimeout(() => {
        if (mountedRef.current && lastOperationRef.current) {
          lastOperationRef.current().catch(console.error);
        }
      }, 3000);
    }
  }, []);

  // 🔄 判断是否应该自动重试
  const shouldAutoRetry = useCallback((error: ApiError): boolean => {
    return error.code === 'NETWORK_ERROR' || 
           error.code.startsWith('HTTP_5') ||
           error.code === 'HTTP_408' // Request Timeout
  }, []);

  // 📋 获取平台列表 - 优化版
  const refreshPlatforms = useCallback(async (force: boolean = false) => {
    if (!mountedRef.current) return;

    const operation = async () => {
    try {
      setLoading(true);
      setError(null);
      
        console.log('🔄 开始获取平台列表...' { force });
        
        const platformsData = await platformApiService.getAllPlatforms(!force);
        
        if (mountedRef.current) {
      setPlatforms(platformsData);
          console.log(`✅ 成功获取 ${platformsData.length} 个平台`);
        }
    } catch (err) {
        if (!mountedRef.current) return;
        
        console.error('❌ 获取平台列表失败:' err);
        handleError(err, 'refreshPlatforms');
      
                 // 使用默认平台列表作为降级方案
         setPlatforms(getDefaultPlatforms());
        console.log('🔄 使用默认平台列表作为降级方案');
    } finally {
        if (mountedRef.current) {
      setLoading(false);
    }
      }
    };

    lastOperationRef.current = operation;
    await operation();
  }, [handleError]);

  // 🔗 连接平台 - 优化版
  const connectPlatform = useCallback(async (
    platformId: string, 
    options?: { force?: boolean }
  ): Promise<ConnectionResult> => {
    if (!mountedRef.current) {
      throw new Error('组件已卸载');
    }

    const operation = async (): Promise<void> => {
    try {
      setError(null);
        console.log(`🔗 开始连接平台: ${platformId}`, options);
        
        const result = await platformApiService.connectPlatform(platformId, options);
      
        if (mountedRef.current && result.success) {
        // 更新本地状态
        setPlatforms(prev => prev.map(p => 
          p.id === platformId 
              ? { 
                  ...p, 
                  status: 'Connected' as const, 
                  accountInfo: result.accountInfo,
                  connectedAt: new Date().toISOString()
                }
            : p
        ));
          
          console.log(`✅ 成功连接平台: ${platformId}`);
        }
      } catch (err) {
        console.error(`❌ 连接平台失败: ${platformId}`, err);
        handleError(err, `connectPlatform(${platformId})`);
        throw err;
      }
    };

    lastOperationRef.current = operation;
    
    // 执行操作并返回结果
    try {
      await operation();
      const result = await platformApiService.connectPlatform(platformId, options);
      return result;
    } catch (err) {
      throw err;
    }
  }, [handleError]);

  // 🔌 断开平台连接 - 优化版
  const disconnectPlatform = useCallback(async (platformId: string): Promise<boolean> => {
    if (!mountedRef.current) {
      throw new Error('组件已卸载');
    }

    const operation = async (): Promise<void> => {
    try {
      setError(null);
        console.log(`🔌 开始断开平台: ${platformId}`);
        
      const result = await platformApiService.disconnectPlatform(platformId);
      
        if (mountedRef.current && result.success) {
        // 更新本地状态
        setPlatforms(prev => prev.map(p => 
          p.id === platformId 
              ? { 
                  ...p, 
                  status: 'Not Connected' as const, 
                  accountInfo: undefined,
                  connectedAt: undefined
                }
            : p
        ));
        
          // 关闭对应的事件流
        const eventSource = eventSourcesRef.current.get(platformId);
        if (eventSource) {
          eventSource.close();
          eventSourcesRef.current.delete(platformId);
            console.log(`🔌 已关闭平台 ${platformId} 的事件流`);
          }
          
          console.log(`✅ 成功断开平台: ${platformId}`);
        }
      } catch (err) {
        console.error(`❌ 断开平台失败: ${platformId}`, err);
        handleError(err, `disconnectPlatform(${platformId})`);
        throw err;
      }
    };

    lastOperationRef.current = operation;
      
    // 执行操作并返回结果
    try {
      await operation();
      const result = await platformApiService.disconnectPlatform(platformId);
      return result.success;
    } catch (err) {
      return false;
    }
  }, [handleError]);

  // 👤 获取平台账户信息 - 优化版
  const getPlatformAccountInfo = useCallback(async (platformId: string) => {
    if (!mountedRef.current) {
      throw new Error('组件已卸载');
    }

    try {
      setError(null);
      console.log(`👤 获取平台账户信息: ${platformId}`);
      
      const accountInfo = await platformApiService.getPlatformAccountInfo(platformId);
      console.log(`✅ 成功获取平台账户信息: ${platformId}`);
      
      return accountInfo;
    } catch (err: any) {
      console.error(`❌ 获取平台账户信息失败: ${platformId}`, err);
      handleError(err, `getPlatformAccountInfo(${platformId})`);
      throw err;
    }
  }, [handleError]);

  // 🔐 获取授权URL - 优化版
  const getAuthUrl = useCallback(async (
    platformId: string, 
    options?: { redirectUrl?: string }
  ) => {
    if (!mountedRef.current) {
      throw new Error('组件已卸载');
    }

    try {
      setError(null);
      console.log(`🔐 获取授权URL: ${platformId}`, options);
      
      const result = await platformApiService.getAuthUrl(platformId, options);
      console.log(`✅ 成功获取授权URL: ${platformId}`);
      
      return result;
    } catch (err) {
      console.error(`❌ 获取授权URL失败: ${platformId}`, err);
      handleError(err, `getAuthUrl(${platformId})`);
      
      // 返回带错误信息的结果，而不是抛出异常
      return {
        authUrl: ''
        error: (err as any)?.message || '获取授权URL失败'
      } }
  }, [handleError]);

  // 🚀 弹窗OAuth连接 - 新增方法
  const connectPlatformWithPopup = useCallback(async (
    platformId: string,
    options?: { width?: number; height?: number }
  ): Promise<ConnectionResult> => {
    if (!mountedRef.current) {
      throw new Error('组件已卸载');
    }

    return new Promise(async (resolve, reject) => {
      try {
        setError(null);
        console.log(`🚀 开始弹窗OAuth连接: ${platformId}`, options);

        // 1. 获取授权URL
        const authUrlResult = await getAuthUrl(platformId);
        
        if ('error' in authUrlResult && authUrlResult.error || !authUrlResult.authUrl) {
          const error = new Error(('error' in authUrlResult ? authUrlResult.error : undefined) || '无法获取授权URL');
          return reject(error);
        }

        // 2. 计算弹窗尺寸和位置
        const popupWidth = options?.width || 600;
        const popupHeight = options?.height || 700;
        const left = Math.round((window.screen.width - popupWidth) / 2);
        const top = Math.round((window.screen.height - popupHeight) / 2);

        // 3. 打开OAuth弹窗
        const popup = window.open(
          authUrlResult.authUrl,
          `oauth-${platformId}`,
          `width=${popupWidth},height=${popupHeight},left=${left},top=${top},scrollbars=yes,resizable=yes,status=yes,location=yes`
        );

        if (!popup) {
          const error = new Error('无法打开OAuth弹窗，请检查浏览器是否阻止了弹窗');
          return reject(error);
        }

        console.log(`🔗 OAuth弹窗已打开: ${platformId}`);

        // 4. 设置超时处理
        const timeoutId = setTimeout(() => {
          if (!popup.closed) {
            popup.close();
          }
          cleanup();
          const error = new Error('OAuth授权超时（5分钟）');
          reject(error);
        }, 5 * 60 * 1000); // 5分钟超时

        // 5. 设置成功回调处理
        let isResolved = false;
        const handleSuccess = (callbackData: any) => {
          if (isResolved) return;
          isResolved = true;
          
          console.log(`✅ OAuth弹窗连接成功: ${platformId}`, callbackData);
          
          const result: ConnectionResult = {
            success: true,
            message: `成功连接到 ${platformId}`,
            platform: platformId,
            accountInfo: callbackData.accountInfo
          };
          
          cleanup();
          resolve(result);
        };

        // 6. 设置失败回调处理
        const handleError = (callbackData: any) => {
          if (isResolved) return;
          isResolved = true;
          
          console.error(`❌ OAuth弹窗连接失败: ${platformId}`, callbackData);
          
          const error = new Error(callbackData.error || 'OAuth授权失败');
          cleanup();
          reject(error);
        };

        // 7. 监听OAuth回调消息  ——  PostMessage / BroadcastChannel / localStorage 多通道
        const messageListener = (event: MessageEvent) => {
          if (isResolved) return;
          if (event.data?.type === 'OAUTH_CALLBACK' && event.data?.platform === platformId) {
            console.log(`📨 [postMessage] 收到OAuth回调消息`, event.data);
            if (event.data.success) {
              isResolved = true;
              handleSuccess(event.data);
            } else {
              isResolved = true;
              handleError(event.data);
            }
          }
        };

        // BroadcastChannel 兜底
        let bc: BroadcastChannel | null = null;
        if (typeof BroadcastChannel !== 'undefined') {
          bc = new BroadcastChannel('oauth-callback');
          bc.onmessage = (ev) => {
            if (isResolved) return;
            if (ev.data?.type === 'OAUTH_CALLBACK' && ev.data?.platform === platformId) {
              console.log('📨 [BroadcastChannel] 收到OAuth回调消息' ev.data);
              if (ev.data.success) {
                isResolved = true;
                handleSuccess(ev.data);
              } else {
                isResolved = true;
                handleError(ev.data);
              }
            }
          } }

        // localStorage 事件兜底
        const storageListener = (ev: StorageEvent) => {
          if (isResolved) return;
          if (ev.key?.startsWith('oauth-callback-') && ev.newValue) {
            try {
              const data = JSON.parse(ev.newValue);
              if (data?.type === 'OAUTH_CALLBACK' && data.platform === platformId) {
                console.log('📨 [localStorage] 收到OAuth回调消息' data);
                if (data.success) {
                  isResolved = true;
                  handleSuccess(data);
                } else {
                  isResolved = true;
                  handleError(data);
                }
              }
            } catch {}
          }
        };
         
        // 8. 监听弹窗关闭 - 修复竞态条件
        const popupCheckInterval = setInterval(() => {
          if (popup.closed) {
            clearInterval(popupCheckInterval); // 立即停止检查
            // 给消息处理更多时间，特别是对于慢速网络
            setTimeout(() => {
              if (!isResolved) {
                isResolved = true;
                cleanup();
                const error = new Error('用户取消了OAuth授权');
                console.log('🚪 弹窗已关闭，未收到成功消息，判定为用户取消');
                reject(error);
              } else {
                console.log('🚪 弹窗已关闭，但已收到成功消息，忽略关闭事件');
              }
            }, 3000); // 增加到3000ms延迟，给消息处理更多时间
          }
        }, 500); // 减少检查间隔以更快检测到关闭

        // 9. 清理函数
        const cleanup = () => {
          clearTimeout(timeoutId);
          clearInterval(popupCheckInterval);
          window.removeEventListener('message' messageListener);
          window.removeEventListener('storage' storageListener);
          if (bc) bc.close();
          
          if (!popup.closed) {
            popup.close();
          }
        };

        // 10. 添加事件监听器
        window.addEventListener('message' messageListener);
        window.addEventListener('storage' storageListener);
        
        console.log(`⏳ 等待OAuth授权完成: ${platformId}`);

      } catch (err) {
        console.error(`❌ 弹窗OAuth连接异常: ${platformId}`, err);
        reject(err);
      }
    });
  }, [getAuthUrl, handleError]);

  // 🪝 配置Webhook - 优化版
  const configureWebhook = useCallback(async (
    platformId: string, 
    config: { url: string; events: string[]; secret?: string }
  ) => {
    if (!mountedRef.current) {
      throw new Error('组件已卸载');
    }

    try {
      setError(null);
      console.log(`🪝 配置Webhook: ${platformId}`, config);
      
      const result = await platformApiService.configureWebhook(platformId, config);
      console.log(`✅ 成功配置Webhook: ${platformId}`);
      
      return result;
    } catch (err) {
      console.error(`❌ 配置Webhook失败: ${platformId}`, err);
      handleError(err, `configureWebhook(${platformId})`);
      throw err;
    }
  }, [handleError]);

  // 🧪 测试Webhook - 优化版
  const testWebhook = useCallback(async (platformId: string, webhookId: string) => {
    if (!mountedRef.current) {
      throw new Error('组件已卸载');
    }

    try {
      setError(null);
      console.log(`🧪 测试Webhook: ${platformId}/${webhookId}`);
      
      const result = await platformApiService.testWebhook(platformId, webhookId);
      console.log(`✅ Webhook测试成功: ${platformId}/${webhookId}`);
      
      return result;
    } catch (err) {
      console.error(`❌ Webhook测试失败: ${platformId}/${webhookId}`, err);
      handleError(err, `testWebhook(${platformId})`);
      throw err;
    }
  }, [handleError]);

  // ⚡ 切换实时模式 - 优化版
  const toggleRealTime = useCallback(() => {
    if (!mountedRef.current) return;

    setRealTimeEnabled(prev => {
      const newValue = !prev;
      console.log(`⚡ 实时模式: ${newValue ? '启用' : '禁用'}`);
      
      if (newValue) {
        // 启动定期刷新 - 使用更智能的间隔
        const interval = process.env.NODE_ENV === 'development' ? 10000 : 30000; // 开发模式更频繁
        refreshIntervalRef.current = setInterval(() => {
          if (mountedRef.current) {
            refreshPlatforms(false).catch(console.error);
          }
        }, interval);
        
        console.log(`🔄 已启动定期刷新 (${interval / 1000}秒)`);
      } else {
        // 停止定期刷新
        if (refreshIntervalRef.current) {
          clearInterval(refreshIntervalRef.current);
          refreshIntervalRef.current = null;
          console.log('⏹️ 已停止定期刷新');
        }
        
        // 关闭所有事件流
        eventSourcesRef.current.forEach((eventSource, platformId) => {
          try {
            eventSource.close();
            console.log(`🔌 已关闭 ${platformId} 事件流`);
          } catch (e) {
            console.warn(`⚠️ 关闭 ${platformId} 事件流时出错:`, e);
          }
        });
        eventSourcesRef.current.clear();
      }
      
      return newValue;
    });
  }, [refreshPlatforms]);

  // 📡 订阅平台事件 - 优化版
  const subscribeToEvents = useCallback((
    platformId: string, 
    onEvent: (event: any) => void
  ): (() => void) => {
    if (!realTimeEnabled) {
      console.warn(`⚠️ 实时模式未启用，无法订阅 ${platformId} 事件`);
      return () => {} }

    if (!mountedRef.current) {
      console.warn(`⚠️ 组件已卸载，无法订阅 ${platformId} 事件`);
      return () => {} }

    console.log(`📡 订阅平台事件: ${platformId}`);

    const eventSource = platformApiService.createEventStream(
      platformId,
      (event) => {
        if (mountedRef.current) {
          onEvent(event);
        }
      },
      (error) => {
        if (mountedRef.current) {
          console.error(`📡 平台 ${platformId} 事件流错误:`, error);
          handleError({
            code: 'EVENT_STREAM_ERROR'
            message: `平台 ${platformId} 实时连接中断`,
            platform: platformId
          }, `subscribeToEvents(${platformId})`);
        }
      }
    );

    if (eventSource) {
      eventSourcesRef.current.set(platformId, eventSource);
      
      return () => {
        try {
        eventSource.close();
        eventSourcesRef.current.delete(platformId);
          console.log(`📡 已取消订阅 ${platformId} 事件`);
        } catch (e) {
          console.warn(`⚠️ 取消订阅 ${platformId} 事件时出错:`, e);
        }
      } }

    return () => {} }, [realTimeEnabled, handleError]);

  // 🔧 工具方法
  const clearError = useCallback(() => {
    setError(null);
    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current);
      retryTimeoutRef.current = null;
    }
  }, []);

  const retryLastOperation = useCallback(async () => {
    if (lastOperationRef.current) {
      console.log('🔄 重试上次操作...');
      await lastOperationRef.current();
    } else {
      console.warn('⚠️ 没有可重试的操作');
    }
  }, []);

  const getDebugInfo = useCallback(() => {
    return {
      ...platformApiService.getDebugInfo(),
      platforms: platforms.length,
      connectedCount,
      realTimeEnabled,
      hasError: !!error,
      eventSourceCount: eventSourcesRef.current.size,
      mounted: mountedRef.current,
    } }, [platforms.length, connectedCount, realTimeEnabled, error]);

  // 🚀 初始化和清理
  useEffect(() => {
    mountedRef.current = true;
    console.log('🚀 usePlatformApi 初始化');
    
    // 初始加载
    refreshPlatforms(false);

    return () => {
      mountedRef.current = false;
      cleanup();
      console.log('🏁 usePlatformApi 卸载');
    } }, [refreshPlatforms, cleanup]);

  // 🎯 返回Hook接口
  return {
    // 数据状态
    platforms,
    loading,
    error,
    connectedCount,
    realTimeEnabled,
    
    // 操作方法
    refreshPlatforms,
    connectPlatform,
    connectPlatformWithPopup,
    disconnectPlatform,
    getPlatformAccountInfo,
    getAuthUrl,
    
    // 实时功能
    toggleRealTime,
    subscribeToEvents,
    
    // Webhook功能
    configureWebhook,
    testWebhook,
    
    // 工具方法
    clearError,
    retryLastOperation,
    getDebugInfo,
  } }; 