import * as React from "react"
import { cva } from "class-variance-authority"

import { cn } from "../../lib/utils"

export interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: "default" | "purple" | "neutral" | "success" | "warning" | "error" | "dark" | "outline" | "data" | "interactive"
}

const cardVariants = cva(
  "rounded-lg border shadow-sm",
  {
    variants: {
      variant: {
        default: "border-border bg-card text-foreground",
        purple: "border-violet-100 bg-violet-50 text-violet-950 dark:border-violet-800 dark:bg-violet-900/30 dark:text-violet-100",
        neutral: "border-gray-200 bg-gray-50 text-gray-950 dark:border-gray-800 dark:bg-gray-800/30 dark:text-gray-100",
        success: "border-green-100 bg-green-50 text-green-950 dark:border-green-800 dark:bg-green-900/30 dark:text-green-100",
        warning: "border-yellow-100 bg-yellow-50 text-yellow-950 dark:border-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-100",
        error: "border-red-100 bg-red-50 text-red-950 dark:border-red-800 dark:bg-red-900/30 dark:text-red-100",
        dark: "border-gray-800 bg-gray-950 text-gray-50 dark:border-gray-800",
        outline: "border-gray-200 bg-transparent text-foreground",
        data: "bg-card border-blue-100 border-l-4 border-l-blue-500 text-foreground",
        interactive: "border-border bg-card text-foreground hover:shadow-md transition-all duration-200 cursor-pointer"
      }
    },
    defaultVariants: {
      variant: "default"
    }
  }
);

const Card = React.forwardRef<
  HTMLDivElement,
  CardProps
>(({ className, variant, ...props }, ref) => (
  <div
    ref={ref}
    className={cn(cardVariants({ variant }), className)}
    {...props}
  />
))
Card.displayName = "Card"

const CardHeader = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex flex-col space-y-1.5 p-4", className)}
    {...props}
  />
))
CardHeader.displayName = "CardHeader"

const CardTitle = React.forwardRef<
  HTMLHeadingElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, children, ...props }, ref) => (
  <h3
    ref={ref}
    className={cn("font-semibold leading-none tracking-tight", className)}
    {...props}
  >
    {children}
  </h3>
))
CardTitle.displayName = "CardTitle"

const CardDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <p
    ref={ref}
    className={cn("text-sm", className)}
    {...props}
  />
))
CardDescription.displayName = "CardDescription"

const CardContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div ref={ref} className={cn("p-4 pt-0", className)} {...props} />
))
CardContent.displayName = "CardContent"

const CardFooter = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex items-center p-4 pt-0", className)}
    {...props}
  />
))
CardFooter.displayName = "CardFooter"

export { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };
