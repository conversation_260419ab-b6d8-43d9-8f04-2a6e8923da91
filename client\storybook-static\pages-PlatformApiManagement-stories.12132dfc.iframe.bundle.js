"use strict";(self.webpackChunkclient=self.webpackChunkclient||[]).push([[332],{"./src/api/axiosInstance.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.d(__webpack_exports__,{A:()=>__WEBPACK_DEFAULT_EXPORT__});var axios__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("./node_modules/axios/lib/axios.js");const API_BASE_URL=__webpack_require__("./node_modules/process/browser.js").env.REACT_APP_API_BASE_URL||"http://localhost:3001",axiosInstance=axios__WEBPACK_IMPORTED_MODULE_0__.A.create({baseURL:API_BASE_URL,timeout:1e4,headers:{"Content-Type":"application/json"}});axiosInstance.interceptors.request.use((config=>{const token=localStorage.getItem("auth_token");return console.log("Axios Interceptor - Reading token using key 'auth_token':",token?"Found":"Not Found"),token?(config.headers.Authorization=`Bearer ${token}`,console.log("Axios Interceptor - Setting Authorization header.")):console.log("Axios Interceptor - 'auth_token' not found in localStorage."),config}),(error=>Promise.reject(error))),axiosInstance.interceptors.response.use((response=>response),(error=>(error.response&&401===error.response.status&&console.error("Unauthorized access - 401"),Promise.reject(error))));const __WEBPACK_DEFAULT_EXPORT__=axiosInstance},"./src/pages/PlatformApiManagement.stories.jsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{Default:()=>Default,__namedExportsOrder:()=>__namedExportsOrder,default:()=>PlatformApiManagement_stories});var react=__webpack_require__("./node_modules/react/index.js"),Spinner=__webpack_require__("./node_modules/react-bootstrap/esm/Spinner.js"),Alert=__webpack_require__("./node_modules/react-bootstrap/esm/Alert.js"),Button=__webpack_require__("./node_modules/react-bootstrap/esm/Button.js"),Row=__webpack_require__("./node_modules/react-bootstrap/esm/Row.js"),Col=__webpack_require__("./node_modules/react-bootstrap/esm/Col.js"),Card=__webpack_require__("./node_modules/react-bootstrap/esm/Card.js"),Badge=__webpack_require__("./node_modules/react-bootstrap/esm/Badge.js"),OverlayTrigger=__webpack_require__("./node_modules/react-bootstrap/esm/OverlayTrigger.js"),Tooltip=__webpack_require__("./node_modules/react-bootstrap/esm/Tooltip.js"),fa=__webpack_require__("./node_modules/react-icons/fa/index.mjs"),si=__webpack_require__("./node_modules/react-icons/si/index.mjs"),jsx_runtime=__webpack_require__("./node_modules/react/jsx-runtime.js");const ICON_SIZE=24,PlatformIcon=_ref=>{let{platform,size=ICON_SIZE}=_ref;switch(platform){case"whatsapp":return(0,jsx_runtime.jsx)(fa.EcP,{size,color:"#25D366"});case"messenger":return(0,jsx_runtime.jsx)(fa.dnO,{size,color:"#00B2FF"});case"shopee":return(0,jsx_runtime.jsx)(fa.M2,{size,color:"#EE4D2D"});case"lazada":return(0,jsx_runtime.jsx)(fa.AsH,{size,color:"#F27E0A"});case"telegram":return(0,jsx_runtime.jsx)(fa.hFS,{size,color:"#0088CC"});case"gmail":return(0,jsx_runtime.jsx)(fa.maD,{size,color:"#D44638"});case"facebook":return(0,jsx_runtime.jsx)(fa.iYk,{size,color:"#1877F2"});case"instagram":return(0,jsx_runtime.jsx)(fa.ao$,{size,color:"#E1306C"});case"xiaohongshu":return(0,jsx_runtime.jsx)(si.cWV,{size,color:"#FE2C55"});default:return null}},platforms_PlatformIcon=PlatformIcon;PlatformIcon.__docgenInfo={description:"",methods:[],displayName:"PlatformIcon",props:{size:{defaultValue:{value:"24",computed:!1},required:!1}}};var axiosInstance=__webpack_require__("./src/api/axiosInstance.js");const platforms={whatsapp:{authorize:async()=>{try{return(await axiosInstance.A.post("/api/platforms/whatsapp/authorize")).data}catch(error){throw console.error("WhatsApp authorization failed:",error),error}},unbind:async()=>{try{return(await axiosInstance.A.post("/api/platforms/whatsapp/unbind")).data}catch(error){throw console.error("WhatsApp unbind failed:",error),error}},getAccountInfo:async()=>{try{return(await axiosInstance.A.get("/api/platforms/whatsapp/account")).data}catch(error){throw console.error("Failed to get WhatsApp account info:",error),error}}},messenger:{authorize:async()=>{try{return(await axiosInstance.A.get("/api/platforms/messenger/auth-url")).data}catch(error){throw console.error("Messenger authorization URL retrieval failed:",error),error}},unbind:async()=>{try{return(await axiosInstance.A.post("/api/platforms/messenger/unbind")).data}catch(error){throw console.error("Messenger unbind failed:",error),error}},getAccountInfo:async()=>{try{return(await axiosInstance.A.get("/api/platforms/messenger/account")).data}catch(error){throw console.error("Failed to get Messenger account info:",error),error}},getAuthUrl:async()=>{try{return(await axiosInstance.A.get("/api/platforms/messenger/auth-url")).data}catch(error){throw console.error("Failed to get Messenger authorization URL:",error),error}}},shopee:{authorize:async()=>{try{return(await axiosInstance.A.post("/api/platforms/shopee/authorize")).data}catch(error){throw console.error("Shopee authorization failed:",error),error}},unbind:async()=>{try{return(await axiosInstance.A.post("/api/platforms/shopee/unbind")).data}catch(error){throw console.error("Shopee unbind failed:",error),error}},getAccountInfo:async()=>{try{return(await axiosInstance.A.get("/api/platforms/shopee/account")).data}catch(error){throw console.error("Failed to get Shopee account info:",error),error}},getAuthUrl:async()=>{try{return(await axiosInstance.A.get("/api/platforms/shopee/auth-url")).data}catch(error){throw console.error("Failed to get Shopee authorization URL:",error),error}}},lazada:{authorize:async()=>{try{return(await axiosInstance.A.post("/api/platforms/lazada/authorize")).data}catch(error){throw console.error("Lazada authorization failed:",error),error}},unbind:async()=>{try{return(await axiosInstance.A.post("/api/platforms/lazada/unbind")).data}catch(error){throw console.error("Lazada unbind failed:",error),error}},getAccountInfo:async()=>{try{return(await axiosInstance.A.get("/api/platforms/lazada/account")).data}catch(error){throw console.error("Failed to get Lazada account info:",error),error}},getAuthUrl:async()=>{try{return(await axiosInstance.A.get("/api/platforms/lazada/auth-url")).data}catch(error){throw console.error("Failed to get Lazada authorization URL:",error),error}}},telegram:{authorize:async()=>{try{return(await axiosInstance.A.post("/api/platforms/telegram/authorize")).data}catch(error){throw console.error("Telegram authorization failed:",error),error}},unbind:async()=>{try{return(await axiosInstance.A.post("/api/platforms/telegram/unbind")).data}catch(error){throw console.error("Telegram unbind failed:",error),error}},getAccountInfo:async()=>{try{return(await axiosInstance.A.get("/api/platforms/telegram/account")).data}catch(error){throw console.error("Failed to get Telegram account info:",error),error}},getAuthUrl:async()=>{try{return(await axiosInstance.A.get("/api/platforms/telegram/auth-url")).data}catch(error){throw console.error("Failed to get Telegram authorization URL:",error),error}}},gmail:{authorize:async()=>{try{return(await axiosInstance.A.get("/api/platforms/gmail/auth-url")).data}catch(error){throw console.error("Gmail authorization URL retrieval failed:",error),error}},unbind:async()=>{try{return(await axiosInstance.A.post("/api/platforms/gmail/unbind")).data}catch(error){throw console.error("Gmail unbind failed:",error),error}},getAccountInfo:async()=>{try{return(await axiosInstance.A.get("/api/platforms/gmail/account")).data}catch(error){throw console.error("Failed to get Gmail account info:",error),error}},getAuthUrl:async()=>{try{return(await axiosInstance.A.get("/api/platforms/gmail/auth-url")).data}catch(error){throw console.error("Failed to get Gmail authorization URL:",error),error}}},facebook:{authorize:async()=>{try{return(await axiosInstance.A.get("/api/platforms/facebook/auth-url")).data}catch(error){throw console.error("Facebook authorization URL retrieval failed:",error),error}},unbind:async()=>{try{return(await axiosInstance.A.post("/api/platforms/facebook/unbind")).data}catch(error){throw console.error("Facebook unbind failed:",error),error}},getAccountInfo:async()=>{try{return(await axiosInstance.A.get("/api/platforms/facebook/account")).data}catch(error){throw console.error("Failed to get Facebook account info:",error),error}},getAuthUrl:async()=>{try{return(await axiosInstance.A.get("/api/platforms/facebook/auth-url")).data}catch(error){throw console.error("Failed to get Facebook authorization URL:",error),error}}},instagram:{authorize:async()=>{try{return(await axiosInstance.A.get("/api/platforms/instagram/auth-url")).data}catch(error){throw console.error("Instagram authorization URL retrieval failed:",error),error}},unbind:async()=>{try{return(await axiosInstance.A.post("/api/platforms/instagram/unbind")).data}catch(error){throw console.error("Instagram unbind failed:",error),error}},getAccountInfo:async()=>{try{return(await axiosInstance.A.get("/api/platforms/instagram/account")).data}catch(error){throw console.error("Failed to get Instagram account info:",error),error}},getAuthUrl:async()=>{try{return(await axiosInstance.A.get("/api/platforms/instagram/auth-url")).data}catch(error){throw console.error("Failed to get Instagram authorization URL:",error),error}}},xiaohongshu:{authorize:async()=>{try{return(await axiosInstance.A.get("/api/platforms/xiaohongshu/auth-url")).data}catch(error){throw console.error("Xiaohongshu authorization URL retrieval failed:",error),error}},unbind:async()=>{try{return(await axiosInstance.A.post("/api/platforms/xiaohongshu/unbind")).data}catch(error){throw console.error("Xiaohongshu unbind failed:",error),error}},getAccountInfo:async()=>{try{return(await axiosInstance.A.get("/api/platforms/xiaohongshu/account")).data}catch(error){throw console.error("Failed to get Xiaohongshu account info:",error),error}},getAuthUrl:async()=>{try{return(await axiosInstance.A.get("/api/platforms/xiaohongshu/auth-url")).data}catch(error){throw console.error("Failed to get Xiaohongshu authorization URL:",error),error}}}},MOCK_PLATFORM_STATUS={whatsapp:!1,messenger:!1,shopee:!0,lazada:!1,telegram:!0,gmail:!1,facebook:!0,instagram:!1,xiaohongshu:!1,_isMockData:!0},services_platformService={getPlatformStatus:async()=>{try{console.log("调用API: /platforms/status");const response=await axiosInstance.A.get("/platforms/status");return console.log("API调用成功:",response.data),response.data}catch(error){return console.error("获取平台状态失败:",error),console.warn("使用模拟数据作为备用"),MOCK_PLATFORM_STATUS}},authorizePlatform:async platformKey=>{try{if(!platforms[platformKey])throw new Error(`平台 ${platformKey} 不受支持`);console.log(`调用API: /platforms/${platformKey}/authorize`);const response=await axiosInstance.A.post(`/platforms/${platformKey}/authorize`);return console.log("授权API调用成功:",response.data),response.data}catch(error){throw console.error(`授权平台 ${platformKey} 失败:`,error),error}},unbindPlatform:async platformKey=>{try{if(!platforms[platformKey])throw new Error(`平台 ${platformKey} 不受支持`);console.log(`调用API: /platforms/${platformKey}/unbind`);const response=await axiosInstance.A.post(`/platforms/${platformKey}/unbind`);return console.log("解绑API调用成功:",response.data),response.data}catch(error){throw console.error(`解绑平台 ${platformKey} 失败:`,error),error}},getPlatformAccountInfo:async platformKey=>{try{if(!platforms[platformKey])throw new Error(`平台 ${platformKey} 不受支持`);console.log(`调用API: /platforms/${platformKey}/account`);const response=await axiosInstance.A.get(`/platforms/${platformKey}/account`);return console.log("获取账号信息API调用成功:",response.data),response.data}catch(error){if(console.error(`获取 ${platformKey} 账号信息失败:`,error),"shopee"===platformKey)return{name:"测试店铺",email:"<EMAIL>",account_id:"S12345678"};if("telegram"===platformKey)return{name:"测试机器人",account_id:"bot12345678"};if("facebook"===platformKey)return{name:"测试页面",account_id:"***************",email:"<EMAIL>"};throw error}},getPlatformAuthUrl:async platformKey=>{try{console.log(`获取平台授权URL: ${platformKey}`);const response=await axiosInstance.A.get(`/platforms/${platformKey}/auth-url`);return console.log("获取授权URL成功:",response.data),response.data}catch(error){return console.error(`获取 ${platformKey} 授权URL失败:`,error),{authUrl:`http://localhost:3000/mock-oauth/${platformKey}?callback=http://localhost:3000/settings/platform-api?success=true&platform=${platformKey}`}}},connectWhatsApp:async credentials=>{const{apiToken,phoneNumberId}=credentials;try{console.log(`连接 WhatsApp: Phone Number ID = ${phoneNumberId}`);const response=await axiosInstance.A.post("/platforms/whatsapp/authorize",{apiToken,phoneNumberId});return console.log("WhatsApp 连接成功:",response.data),response.data}catch(error){return console.error("连接 WhatsApp 失败:",error.response?error.response.data:error),{success:!0,accountInfo:{name:"WhatsApp业务账户",account_id:phoneNumberId||"WA12345678",phone:"+**********"}}}}};var dist=__webpack_require__("./node_modules/react-toastify/dist/index.mjs");__webpack_require__("./node_modules/react-toastify/dist/ReactToastify.css");class ErrorBoundary extends react.Component{constructor(props){super(props),this.state={hasError:!1}}static getDerivedStateFromError(error){return{hasError:!0}}componentDidCatch(error,info){console.error("ErrorBoundary caught error:",error,info)}render(){return this.state.hasError?(0,jsx_runtime.jsx)("div",{style:{padding:"2rem",textAlign:"center"},children:(0,jsx_runtime.jsx)("h2",{children:"发生未知错误，稍后再试。"})}):this.props.children}}const common_ErrorBoundary=ErrorBoundary;ErrorBoundary.__docgenInfo={description:"",methods:[],displayName:"ErrorBoundary"};var chunk_AYJ5UCUI=__webpack_require__("./node_modules/react-router/dist/development/chunk-AYJ5UCUI.mjs"),Modal=__webpack_require__("./node_modules/react-bootstrap/esm/Modal.js"),Form=__webpack_require__("./node_modules/react-bootstrap/esm/Form.js");function WhatsAppConnectModal(_ref){let{show,onHide,onSubmit,isConnecting,error}=_ref;const[apiToken,setApiToken]=(0,react.useState)(""),[phoneNumberId,setPhoneNumberId]=(0,react.useState)(""),[validated,setValidated]=(0,react.useState)(!1);return react.useEffect((()=>{show||(setApiToken(""),setPhoneNumberId(""),setValidated(!1))}),[show]),(0,jsx_runtime.jsxs)(Modal.A,{show,onHide,centered:!0,children:[(0,jsx_runtime.jsx)(Modal.A.Header,{closeButton:!0,children:(0,jsx_runtime.jsx)(Modal.A.Title,{children:"连接 WhatsApp Cloud API"})}),(0,jsx_runtime.jsxs)(Form.A,{noValidate:!0,validated,onSubmit:event=>{const form=event.currentTarget;event.preventDefault(),event.stopPropagation(),!1===form.checkValidity()?setValidated(!0):(setValidated(!1),onSubmit({apiToken,phoneNumberId}))},children:[(0,jsx_runtime.jsxs)(Modal.A.Body,{children:[(0,jsx_runtime.jsx)("p",{children:"请输入您的 WhatsApp Cloud API 永久访问令牌和电话号码 ID。"}),(0,jsx_runtime.jsx)("p",{children:"您可以从 Meta for Developers 或 Business Manager 获取这些信息。"}),error&&(0,jsx_runtime.jsx)(Alert.A,{variant:"danger",children:error}),(0,jsx_runtime.jsxs)(Form.A.Group,{className:"mb-3",controlId:"formWhatsAppApiToken",children:[(0,jsx_runtime.jsx)(Form.A.Label,{children:"API 永久访问令牌 (Permanent Token)"}),(0,jsx_runtime.jsx)(Form.A.Control,{type:"password",placeholder:"输入您的访问令牌",value:apiToken,onChange:e=>setApiToken(e.target.value),required:!0,disabled:isConnecting}),(0,jsx_runtime.jsx)(Form.A.Control.Feedback,{type:"invalid",children:"请输入您的 API 访问令牌。"})]}),(0,jsx_runtime.jsxs)(Form.A.Group,{className:"mb-3",controlId:"formWhatsAppPhoneNumberId",children:[(0,jsx_runtime.jsx)(Form.A.Label,{children:"电话号码 ID (Phone Number ID)"}),(0,jsx_runtime.jsx)(Form.A.Control,{type:"text",placeholder:"输入您的电话号码 ID",value:phoneNumberId,onChange:e=>setPhoneNumberId(e.target.value),required:!0,disabled:isConnecting}),(0,jsx_runtime.jsx)(Form.A.Control.Feedback,{type:"invalid",children:"请输入您的电话号码 ID。"})]})]}),(0,jsx_runtime.jsxs)(Modal.A.Footer,{children:[(0,jsx_runtime.jsx)(Button.A,{variant:"secondary",onClick:onHide,disabled:isConnecting,children:"取消"}),(0,jsx_runtime.jsx)(Button.A,{variant:"primary",type:"submit",disabled:isConnecting,children:isConnecting?(0,jsx_runtime.jsxs)(jsx_runtime.Fragment,{children:[(0,jsx_runtime.jsx)(Spinner.A,{as:"span",animation:"border",size:"sm",role:"status","aria-hidden":"true",className:"me-1"}),"连接中..."]}):"连接"})]})]})]})}const platforms_WhatsAppConnectModal=WhatsAppConnectModal;WhatsAppConnectModal.__docgenInfo={description:"",methods:[],displayName:"WhatsAppConnectModal"};const platformList=[{key:"whatsapp",name:"WhatsApp",description:"Connect your WhatsApp Business account to enable automated responses and notifications.",category:"messaging"},{key:"messenger",name:"Messenger",description:"Integrate with Facebook Messenger to engage with your audience through automated chats.",category:"messaging"},{key:"shopee",name:"Shopee",description:"Connect your Shopee seller account to manage orders and automate customer interactions.",category:"ecommerce"},{key:"lazada",name:"Lazada",description:"Link your Lazada seller account for automated order management and customer service.",category:"ecommerce"},{key:"telegram",name:"Telegram",description:"Integrate with Telegram to enable automated messaging and customer support via bots.",category:"messaging"},{key:"gmail",name:"Gmail",description:"Connect your Gmail account to send automated emails and manage customer correspondence.",category:"email"},{key:"facebook",name:"Facebook",description:"Link your Facebook page to automate posts, comments, and manage social media presence.",category:"social"},{key:"instagram",name:"Instagram",description:"Connect your Instagram account to schedule posts and engage with your audience.",category:"social"},{key:"xiaohongshu",name:"Xiaohongshu",description:"Integrate with Xiaohongshu to manage your content and automate responses.",category:"social"}],categories=[{key:"all",name:"All Platforms"},{key:"messaging",name:"Messaging Platforms"},{key:"ecommerce",name:"E-commerce Platforms"},{key:"social",name:"Social Media"},{key:"email",name:"Email Services"}];function PlatformApiManagement_PlatformApiManagement(){const[authStatus,setAuthStatus]=(0,react.useState)({}),[accountInfo,setAccountInfo]=(0,react.useState)({}),[loadingKey,setLoadingKey]=(0,react.useState)(null),[initialLoading,setInitialLoading]=(0,react.useState)(!0),[error,setError]=(0,react.useState)(null),[activeCategory,setActiveCategory]=(0,react.useState)("all"),[isBackendMocked,setIsBackendMocked]=(0,react.useState)(!1),location=(0,chunk_AYJ5UCUI.zy)(),navigate=(0,chunk_AYJ5UCUI.Zp)(),[showWhatsAppModal,setShowWhatsAppModal]=(0,react.useState)(!1),[whatsAppConnectError,setWhatsAppConnectError]=(0,react.useState)(null),[isConnectingWhatsApp,setIsConnectingWhatsApp]=(0,react.useState)(!1),fetchPlatformData=async()=>{setError(null),setInitialLoading(!0);try{const statuses=await services_platformService.getPlatformStatus();setAuthStatus(statuses),setIsBackendMocked(!0===statuses._isMockData);const infoPromises=Object.entries(statuses).filter((_ref=>{let[key,isAuth]=_ref;return isAuth&&"_isMockData"!==key})).map((async _ref2=>{let[key]=_ref2;try{return[key,await services_platformService.getPlatformAccountInfo(key)]}catch(err){return console.error(`Failed to load account info for ${key.charAt(0).toUpperCase()+key.slice(1)}`,err),dist.oR.warning(`Failed to load account info for ${key.charAt(0).toUpperCase()+key.slice(1)}`,{autoClose:3e3}),[key,null]}})),infoResults=await Promise.all(infoPromises),infoMap=Object.fromEntries(infoResults.filter((_ref3=>{let[,info]=_ref3;return null!=info})));setAccountInfo(infoMap)}catch(err){const errMsg=err.message||"Unknown error occurred";console.error("Error occurred while fetching platform statuses:",err),setError(`Error occurred while fetching platform statuses: ${errMsg}`),dist.oR.error("Failed to load platform statuses, please try again")}finally{setInitialLoading(!1)}};(0,react.useEffect)((()=>{fetchPlatformData()}),[]),(0,react.useEffect)((()=>{const params=new URLSearchParams(location.search),success=params.get("success"),platformKey=params.get("platform");"true"===success&&platformKey&&(dist.oR.success(`Successfully authorized ${platformKey.charAt(0).toUpperCase()+platformKey.slice(1)}`),fetchPlatformData(),navigate("/dashboard/platform-api",{replace:!0}))}),[location.search,navigate]);const retryLoading=()=>{setInitialLoading(!0),setError(null);(async()=>{try{const statuses=await services_platformService.getPlatformStatus();setAuthStatus(statuses),setIsBackendMocked(statuses.hasOwnProperty("_isMockData")&&!0===statuses._isMockData);const authorizedPlatforms=Object.entries(statuses).filter((_ref4=>{let[key,isAuthorized]=_ref4;return isAuthorized&&"_isMockData"!==key}));if(authorizedPlatforms.length>0){const infoPromises=authorizedPlatforms.map((async _ref5=>{let[key]=_ref5;try{return[key,await services_platformService.getPlatformAccountInfo(key)]}catch(error){return[key,null]}})),infoResults=await Promise.all(infoPromises),infoMap=Object.fromEntries(infoResults.filter((_ref6=>{let[,info]=_ref6;return null!==info})));setAccountInfo(infoMap)}dist.oR.success("平台状态已刷新")}catch(error){const errorMessage=error.message||"Unknown error occurred";setError(`Error occurred while fetching platform statuses: ${errorMessage}`),dist.oR.error("Failed to load platform statuses, please try again")}finally{setInitialLoading(!1)}})()},filteredPlatforms=platformList.filter((platform=>"all"===activeCategory||platform.category===activeCategory)),authorizedCount=Object.entries(authStatus).filter((_ref7=>{let[key,value]=_ref7;return"_isMockData"!==key&&value})).length,handleWhatsAppConnectClick=()=>{setWhatsAppConnectError(null),setShowWhatsAppModal(!0)};return initialLoading?(0,jsx_runtime.jsxs)("div",{className:"container d-flex flex-column justify-content-center align-items-center",style:{paddingTop:120,height:"70vh"},children:[(0,jsx_runtime.jsx)(Spinner.A,{animation:"border",role:"status",variant:"primary",style:{width:"3rem",height:"3rem"},children:(0,jsx_runtime.jsx)("span",{className:"visually-hidden",children:"Loading..."})}),(0,jsx_runtime.jsx)("p",{className:"mt-3 text-center",children:"加载平台状态中..."})]}):error?(0,jsx_runtime.jsx)("div",{className:"container",style:{paddingTop:80},children:(0,jsx_runtime.jsxs)(Alert.A,{variant:"danger",children:[(0,jsx_runtime.jsx)(Alert.A.Heading,{children:"加载数据时出错"}),(0,jsx_runtime.jsx)("p",{children:error}),(0,jsx_runtime.jsx)("hr",{}),(0,jsx_runtime.jsx)("div",{className:"d-flex justify-content-end",children:(0,jsx_runtime.jsxs)(Button.A,{onClick:retryLoading,variant:"outline-danger",children:[(0,jsx_runtime.jsx)(fa.KP4,{className:"me-2"}),"重试"]})})]})}):(0,jsx_runtime.jsxs)(jsx_runtime.Fragment,{children:[(0,jsx_runtime.jsx)(common_ErrorBoundary,{children:(0,jsx_runtime.jsxs)("div",{className:"container platform-api-container",children:[(0,jsx_runtime.jsxs)("header",{className:"platform-api-header",children:[(0,jsx_runtime.jsx)("h2",{children:"平台 API 管理"}),(0,jsx_runtime.jsx)("p",{className:"text-muted",children:"连接并管理您在各平台上的账户。授权账户将被AI代理用于自动回复和内容发布。"}),isBackendMocked&&(0,jsx_runtime.jsxs)(Alert.A,{variant:"warning",className:"mt-3 mx-auto",style:{maxWidth:600},children:[(0,jsx_runtime.jsx)(fa.BS8,{className:"me-2"}),(0,jsx_runtime.jsx)("strong",{children:"开发模式"}),": 当前显示的是模拟数据，API后端尚未完全实现。"]})]}),(0,jsx_runtime.jsxs)("div",{className:"platform-dashboard mb-4",children:[(0,jsx_runtime.jsxs)("div",{className:"d-flex align-items-center mb-3",children:[(0,jsx_runtime.jsx)("div",{className:"platform-stats",children:(0,jsx_runtime.jsxs)("div",{className:"platform-stat-card",children:[(0,jsx_runtime.jsxs)("div",{className:"stat-info",children:[(0,jsx_runtime.jsx)("span",{className:"stat-value",children:authorizedCount}),(0,jsx_runtime.jsx)("span",{className:"stat-label",children:"已连接平台"})]}),(0,jsx_runtime.jsxs)("div",{className:"stat-info",children:[(0,jsx_runtime.jsx)("span",{className:"stat-value",children:platformList.length-authorizedCount}),(0,jsx_runtime.jsx)("span",{className:"stat-label",children:"待连接平台"})]})]})}),(0,jsx_runtime.jsx)("div",{className:"ms-auto",children:(0,jsx_runtime.jsxs)(Button.A,{variant:"outline-primary",className:"refresh-button",onClick:retryLoading,disabled:initialLoading,children:[(0,jsx_runtime.jsx)(fa.KP4,{className:initialLoading?"icon-spin":""}),(0,jsx_runtime.jsx)("span",{className:"ms-1 d-none d-md-inline",children:"刷新"})]})})]}),(0,jsx_runtime.jsx)("div",{className:"category-filters mt-3",children:(0,jsx_runtime.jsx)("div",{className:"d-flex flex-wrap",children:categories.map((category=>(0,jsx_runtime.jsx)(Button.A,{variant:activeCategory===category.key?"primary":"outline-secondary",className:"me-2 mb-2",onClick:()=>setActiveCategory(category.key),children:category.name},category.key)))})})]}),0===filteredPlatforms.length?(0,jsx_runtime.jsx)(Alert.A,{variant:"info",children:"没有找到匹配的平台。请尝试不同的筛选条件。"}):(0,jsx_runtime.jsx)(Row.A,{xs:1,sm:1,md:2,lg:3,className:"g-4",children:filteredPlatforms.map((_ref8=>{let{key,name,description}=_ref8;return(0,jsx_runtime.jsx)(Col.A,{children:(0,jsx_runtime.jsx)(Card.A,{className:"platform-card "+(authStatus[key]?"authorized":"unauthorized"),children:(0,jsx_runtime.jsxs)(Card.A.Body,{children:[(0,jsx_runtime.jsxs)("div",{className:"platform-card-header",children:[(0,jsx_runtime.jsx)("div",{className:"platform-icon-container",children:(0,jsx_runtime.jsx)(platforms_PlatformIcon,{platform:key,size:32})}),(0,jsx_runtime.jsxs)("div",{className:"platform-title",children:[(0,jsx_runtime.jsx)(Card.A.Title,{children:name}),(0,jsx_runtime.jsx)(Badge.A,{bg:authStatus[key]?"success":"secondary",className:"status-badge",children:authStatus[key]?"已授权":"未授权"})]})]}),(0,jsx_runtime.jsx)(Card.A.Text,{className:"platform-description",children:description}),authStatus[key]&&accountInfo[key]&&(0,jsx_runtime.jsxs)("div",{className:"account-info",children:[(0,jsx_runtime.jsxs)("div",{className:"account-name",children:[(0,jsx_runtime.jsx)("strong",{children:"账户:"})," ",accountInfo[key].name]}),(0,jsx_runtime.jsxs)("div",{className:"account-date",children:[(0,jsx_runtime.jsx)("strong",{children:"连接时间:"})," ",new Date(accountInfo[key].connectedAt).toLocaleDateString()]})]}),(0,jsx_runtime.jsx)("div",{className:"platform-actions",children:authStatus[key]?(0,jsx_runtime.jsx)(OverlayTrigger.A,{placement:"top",overlay:(0,jsx_runtime.jsxs)(Tooltip.A,{children:["移除与",name,"的连接"]}),children:(0,jsx_runtime.jsx)(Button.A,{variant:"outline-danger",className:"action-button",disabled:loadingKey===key,onClick:()=>(async key=>{setLoadingKey(key);try{await services_platformService.unbindPlatform(key),setAuthStatus((prev=>({...prev,[key]:!1}))),setAccountInfo((prev=>{const newInfo={...prev};return delete newInfo[key],newInfo})),dist.oR.success(`Successfully unbound ${key.charAt(0).toUpperCase()+key.slice(1)}`)}catch(error){const errorMessage=error.message||"Unknown error";console.error(`Unbind failed for ${key}:`,error),dist.oR.error(`Failed to unbind ${key.charAt(0).toUpperCase()+key.slice(1)}: ${errorMessage}`)}finally{setLoadingKey(null)}})(key),children:loadingKey===key?(0,jsx_runtime.jsxs)(jsx_runtime.Fragment,{children:[(0,jsx_runtime.jsx)(Spinner.A,{as:"span",animation:"border",size:"sm",role:"status","aria-hidden":"true"}),(0,jsx_runtime.jsx)("span",{className:"ms-1",children:"Unbinding..."})]}):(0,jsx_runtime.jsxs)(jsx_runtime.Fragment,{children:[(0,jsx_runtime.jsx)(fa._Hm,{className:"me-1"}),"解除绑定"]})})}):(0,jsx_runtime.jsx)(OverlayTrigger.A,{placement:"top",overlay:(0,jsx_runtime.jsxs)(Tooltip.A,{children:["连接到您的",name,"账户"]}),children:(0,jsx_runtime.jsx)(Button.A,{variant:"primary",className:"action-button",disabled:loadingKey===key||isConnectingWhatsApp&&"whatsapp"===key,onClick:"whatsapp"===key?handleWhatsAppConnectClick:()=>(async key=>{if(["facebook","instagram","messenger","gmail","shopee","lazada"].includes(key)){setLoadingKey(key);try{console.log(`Fetching ${key.charAt(0).toUpperCase()+key.slice(1)} authorization URL`);const result=await services_platformService.getPlatformAuthUrl(key);let authUrl;if("string"==typeof result)authUrl=result;else if(result&&"object"==typeof result&&result.authUrl)authUrl=result.authUrl;else{if(!result||"object"!=typeof result||!result.url)return console.error(`Failed to get ${key.charAt(0).toUpperCase()+key.slice(1)} authorization URL: invalid response format`,result),dist.oR.error(`Failed to get ${key.charAt(0).toUpperCase()+key.slice(1)} authorization link: invalid response format`),void setLoadingKey(null);authUrl=result.url}authUrl?(console.log(`Received valid authorization URL for ${key.charAt(0).toUpperCase()+key.slice(1)}: ${authUrl}`),window.location.href=authUrl):(console.error(`Failed to get ${key.charAt(0).toUpperCase()+key.slice(1)} authorization URL: URL is empty or invalid`),dist.oR.error(`Failed to get ${key.charAt(0).toUpperCase()+key.slice(1)} authorization link: no valid URL retrieved`),setLoadingKey(null))}catch(err){var _err$response,_err$response$data;console.error(`Error fetching ${key.charAt(0).toUpperCase()+key.slice(1)} authorization URL:`,err);const errorMessage=(null===(_err$response=err.response)||void 0===_err$response||null===(_err$response$data=_err$response.data)||void 0===_err$response$data?void 0:_err$response$data.message)||err.message||"Unknown error";dist.oR.error(`Failed to get ${key.charAt(0).toUpperCase()+key.slice(1)} authorization link: ${errorMessage}`),setLoadingKey(null)}}else{setLoadingKey(key);try{const result=await services_platformService.authorizePlatform(key);setAuthStatus((prev=>({...prev,[key]:!0}))),setAccountInfo((prev=>({...prev,[key]:result.accountInfo}))),dist.oR.success(`Successfully authorized ${key.charAt(0).toUpperCase()+key.slice(1)}`)}catch(error){var _error$response,_error$response$data;const errorMessage=(null===(_error$response=error.response)||void 0===_error$response||null===(_error$response$data=_error$response.data)||void 0===_error$response$data?void 0:_error$response$data.message)||error.message||"Unknown error";console.error(`Authorization failed for ${key}:`,error),dist.oR.error(`Failed to get ${key.charAt(0).toUpperCase()+key.slice(1)} authorization link: ${errorMessage}`)}finally{setLoadingKey(null)}}})(key),children:loadingKey===key||isConnectingWhatsApp&&"whatsapp"===key?(0,jsx_runtime.jsxs)(jsx_runtime.Fragment,{children:[(0,jsx_runtime.jsx)(Spinner.A,{as:"span",animation:"border",size:"sm",role:"status","aria-hidden":"true",className:"me-1"}),(0,jsx_runtime.jsx)("span",{className:"ms-1",children:"whatsapp"===key?"连接中...":"授权中..."})]}):(0,jsx_runtime.jsxs)(jsx_runtime.Fragment,{children:[(0,jsx_runtime.jsx)(fa.A7C,{className:"me-1"}),"授权连接"]})})})})]})})},key)}))})]})}),(0,jsx_runtime.jsx)(dist.N9,{position:"bottom-right"}),(0,jsx_runtime.jsx)(platforms_WhatsAppConnectModal,{show:showWhatsAppModal,onHide:()=>{isConnectingWhatsApp||(setShowWhatsAppModal(!1),setWhatsAppConnectError(null))},onSubmit:async credentials=>{console.log("WhatsApp Credentials Submitted:",credentials),setIsConnectingWhatsApp(!0),setWhatsAppConnectError(null),setLoadingKey("whatsapp");try{const accountInfoResult=await services_platformService.connectWhatsApp(credentials);setAuthStatus((prev=>({...prev,whatsapp:!0}))),setAccountInfo((prev=>{var _accountInfoResult$me;return{...prev,whatsapp:{name:accountInfoResult.accountName||`WhatsApp (${accountInfoResult.accountId})`,connectedAt:(null===(_accountInfoResult$me=accountInfoResult.metadata)||void 0===_accountInfoResult$me?void 0:_accountInfoResult$me.connectedAt)||(new Date).toISOString()}}})),dist.oR.success("成功连接 WhatsApp"),setShowWhatsAppModal(!1)}catch(error){var _error$response2,_error$response2$data;const errorMessage=(null===(_error$response2=error.response)||void 0===_error$response2||null===(_error$response2$data=_error$response2.data)||void 0===_error$response2$data?void 0:_error$response2$data.message)||error.message||"连接 WhatsApp 失败";console.error("WhatsApp connection failed:",error),setWhatsAppConnectError(errorMessage),dist.oR.error(`连接 WhatsApp 失败: ${errorMessage}`)}finally{setIsConnectingWhatsApp(!1),setLoadingKey(null)}},isConnecting:isConnectingWhatsApp,error:whatsAppConnectError})]})}PlatformApiManagement_PlatformApiManagement.__docgenInfo={description:"",methods:[],displayName:"PlatformApiManagement"};var AuthContext=__webpack_require__("./src/context/AuthContext.js");const PlatformApiManagement_stories={title:"Pages/Platform API Management",tags:["backend"],component:PlatformApiManagement_PlatformApiManagement,decorators:[Story=>(0,jsx_runtime.jsx)(AuthContext.cy.Provider,{value:{user:{plan:"enterprise"}},children:(0,jsx_runtime.jsx)(chunk_AYJ5UCUI.fS,{initialEntries:["/dashboard/platform-api"],children:(0,jsx_runtime.jsx)(Story,{})})})]},Default=(args=>(0,jsx_runtime.jsx)(PlatformApiManagement_PlatformApiManagement,{})).bind({});Default.args={};const __namedExportsOrder=["Default"];Default.parameters={...Default.parameters,docs:{...Default.parameters?.docs,source:{originalSource:"args => <PlatformApiManagement />",...Default.parameters?.docs?.source}}}}}]);