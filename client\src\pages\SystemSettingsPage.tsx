import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Settings, Shield, Database, Bell } from 'lucide-react';

function SystemSettingsPage() {
  return (
    <div className="space-y-6 p-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">System Settings</h1>
        <p className="text-muted-foreground">
          Configure system preferences and security settings
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <div className="flex items-center space-x-2">
              <Settings className="h-5 w-5" />
              <CardTitle>General Settings</CardTitle>
            </div>
            <CardDescription>
              Basic system configuration options
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium">Site Name</label>
                <p className="text-sm text-muted-foreground">Intelligent Agent Control Center</p>
              </div>
              <div>
                <label className="text-sm font-medium">Time Zone</label>
                <p className="text-sm text-muted-foreground">Asia/Shanghai</p>
              </div>
              <div>
                <label className="text-sm font-medium">Date Format</label>
                <p className="text-sm text-muted-foreground">YYYY-MM-DD</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <div className="flex items-center space-x-2">
              <Shield className="h-5 w-5" />
              <CardTitle>Security Settings</CardTitle>
            </div>
            <CardDescription>
              Security and authentication configuration
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium">Session Timeout</label>
                <p className="text-sm text-muted-foreground">30 minutes</p>
              </div>
              <div>
                <label className="text-sm font-medium">Password Policy</label>
                <p className="text-sm text-muted-foreground">Strong passwords required</p>
              </div>
              <div>
                <label className="text-sm font-medium">Two-Factor Authentication</label>
                <p className="text-sm text-muted-foreground">Enabled</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <div className="flex items-center space-x-2">
              <Database className="h-5 w-5" />
              <CardTitle>Database Settings</CardTitle>
            </div>
            <CardDescription>
              Database configuration and backup settings
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium">Database Type</label>
                <p className="text-sm text-muted-foreground">PostgreSQL</p>
              </div>
              <div>
                <label className="text-sm font-medium">Backup Frequency</label>
                <p className="text-sm text-muted-foreground">Daily at 2:00 AM</p>
              </div>
              <div>
                <label className="text-sm font-medium">Connection Pool</label>
                <p className="text-sm text-muted-foreground">Max 20 connections</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <div className="flex items-center space-x-2">
              <Bell className="h-5 w-5" />
              <CardTitle>Notification Settings</CardTitle>
            </div>
            <CardDescription>
              Configure system notifications and alerts
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium">Email Notifications</label>
                <p className="text-sm text-muted-foreground">Enabled</p>
              </div>
              <div>
                <label className="text-sm font-medium">SMS Alerts</label>
                <p className="text-sm text-muted-foreground">Critical only</p>
              </div>
              <div>
                <label className="text-sm font-medium">System Alerts</label>
                <p className="text-sm text-muted-foreground">All events</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

export default SystemSettingsPage;