/**
 * API缓存配置
 * 使用axios-cache-interceptor实现API请求缓存
 */
import axios, { AxiosResponse, InternalAxiosRequestConfig } from 'axios';
import { setupCache, CacheRequestConfig } from 'axios-cache-interceptor';
import { API_BASE_URL, IS_PRODUCTION } from '../../config/constants';

// 缓存存储对象
interface CacheStorage {
  [key: string]: {
    data: any;
    expires: number;
  };
}

// 内存缓存存储
const memoryStorage: CacheStorage = {};

// 默认缓存配置（15分钟）
export const createDefaultCache = () => {
  const instance = axios.create({
    baseURL: API_BASE_URL,
    timeout: 10000, // 10秒超时
    headers: {
      'Content-Type': 'application/json'
    }
  });
  
  // 添加请求拦截器，记录重试状态
  instance.interceptors.request.use(
    (config: InternalAxiosRequestConfig) => {
      // 添加请求开始时间戳，用于计算请求时长
      config.headers.set('x-request-time', Date.now().toString());
      return config;
    },
    (error) => {
      console.error('[Cache] Request interceptor error:', error);
      return Promise.reject(error);
    }
  );

  // 添加响应拦截器，记录请求时长
  instance.interceptors.response.use(
    (response: AxiosResponse) => {
      const requestTime = response.config.headers?.['x-request-time'];
      if (requestTime) {
        const duration = Date.now() - parseInt(requestTime as string, 10);
        console.log(`[Cache] Request to ${response.config.url} took ${duration}ms`);
      }
      return response;
    },
    (error) => {
      console.error('[Cache] Response interceptor error:', error);
      return Promise.reject(error);
    }
  );

  return setupCache(instance, {
    ttl: 15 * 60 * 1000, // 15分钟
    methods: ['get'],
    debug: !IS_PRODUCTION ? console.log : undefined,
    generateKey: (request: CacheRequestConfig) => {
      // 生成缓存键，包含URL和重要参数
      const url = request.url || '';
      const params = request.params ? JSON.stringify(request.params) : '';
      return `${request.method}:${url}:${params}`;
    }
  });
};

// 长期缓存配置（1天）
export const createLongTermCache = () => {
  const instance = axios.create({
    baseURL: API_BASE_URL,
    timeout: 15000, // 15秒超时，长期缓存可以等待更久
    headers: {
      'Content-Type': 'application/json'
    }
  });

  return setupCache(instance, {
    ttl: 24 * 60 * 60 * 1000, // 1天
    methods: ['get'],
    debug: !IS_PRODUCTION ? console.log : undefined,
    generateKey: (request: CacheRequestConfig) => {
      const url = request.url || '';
      const params = request.params ? JSON.stringify(request.params) : '';
      return `long:${request.method}:${url}:${params}`;
    }
  });
};

// 无缓存配置
export const createNoCache = () => {
  const instance = axios.create({
    baseURL: API_BASE_URL,
    timeout: 8000, // 8秒超时，无缓存需要快速响应
    headers: {
      'Content-Type': 'application/json'
    }
  });

  return setupCache(instance, {
    ttl: 0, // 不缓存
    methods: [],
    debug: !IS_PRODUCTION ? console.log : undefined
  });
};

// 缓存管理器
export const cacheManager = {
  // 清除所有缓存
  clearAll: () => {
    Object.keys(memoryStorage).forEach(key => {
      delete memoryStorage[key];
    });
    console.log('[Cache] All cache cleared');
  },

  // 清除过期缓存
  clearExpired: () => {
    const now = Date.now();
    Object.keys(memoryStorage).forEach(key => {
      if (memoryStorage[key].expires < now) {
        delete memoryStorage[key];
      }
    });
    console.log('[Cache] Expired cache cleared');
  },

  // 清除指定URL的缓存（向后兼容）
  clearCache: (url: string, params?: Record<string, any>) => {
    const paramsString = params ? JSON.stringify(params) : '';
    
    // 清除所有可能的缓存键变体
    const keysToDelete = Object.keys(memoryStorage).filter(key => {
      return key.includes(url) && (params ? key.includes(paramsString) : true);
    });
    
    keysToDelete.forEach(key => {
      delete memoryStorage[key];
    });
    
    console.log(`[Cache] Cleared cache for: ${url}${params ? ` with params: ${paramsString}` : ''}`);
  },

  // 获取缓存统计
  getStats: () => {
    const total = Object.keys(memoryStorage).length;
    const expired = Object.values(memoryStorage).filter(
      item => item.expires < Date.now()
    ).length;
    return {
      total,
      active: total - expired,
      expired
    };
  }
}; 