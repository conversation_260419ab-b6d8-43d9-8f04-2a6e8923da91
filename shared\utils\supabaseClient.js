/**
 * 共享Supabase客户端工具
 * 为所有微服务提供统一的Supabase客户端配置
 */
const { createClient } = require('@supabase/supabase-js');

/**
 * 创建Supabase客户端
 * @param {string} supabaseUrl - Supabase URL
 * @param {string} supabaseKey - Supabase Key (可以是anon key或service_role key)
 * @param {Object} options - 额外配置选项
 * @returns {Object} Supabase客户端实例
 */
const createSupabaseClient = (supabaseUrl, supabaseKey, options = {}) => {
  if (!supabaseUrl || !supabaseKey) {
    console.error('Supabase URL和Key必须提供');
    return null;
  }
  
  const defaultOptions = {
    auth: {
      autoRefreshToken: true,
      persistSession: true
    }
  };
  
  const clientOptions = { ...defaultOptions, ...options };
  
  try {
    return createClient(supabaseUrl, supabaseKey, clientOptions);
  } catch (error) {
    console.error('创建Supabase客户端失败:' error.message);
    return null;
  }
};

/**
 * 从环境变量创建Supabase客户端
 * @param {boolean} useServiceRole - 是否使用service_role key
 * @returns {Object} Supabase客户端实例
 */
const createSupabaseClientFromEnv = (useServiceRole = false) => {
  const supabaseUrl = process.env.SUPABASE_URL || process.env.REACT_APP_SUPABASE_URL;
  const supabaseAnonKey = process.env.SUPABASE_ANON_KEY || process.env.REACT_APP_SUPABASE_ANON_KEY;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_SERVICE_KEY || process.env.REACT_APP_SUPABASE_SERVICE_ROLE_KEY;
  
  const key = useServiceRole ? supabaseServiceKey : supabaseAnonKey;
  
  if (!supabaseUrl) {
    console.error('Supabase URL not found in environment variables.');
  }
  if (!key && !useServiceRole) {
    console.error('Supabase Anon Key not found in environment variables.');
  }
  if (!key && useServiceRole) {
    console.error('Supabase Service Role Key not found in environment variables.');
  }

  return createSupabaseClient(supabaseUrl, key);
};

/**
 * 检查Supabase连接状态
 * @param {Object} supabase - Supabase客户端实例
 * @returns {Promise<boolean>} - 连接是否可用
 */
const checkSupabaseConnection = async (supabase) => {
  if (!supabase) {
    return false;
  }
  
  try {
    // 执行简单查询来测试连接
    const { error } = await supabase.from('health_check').select('id').limit(1);
    
    // 如果没有health_check表，可以尝试其他方法
    if (error && error.code === '42P01') {
      // 表不存在，尝试auth.getUser()
      const { error: authError } = await supabase.auth.getUser();
      return !authError;
    }
    
    return !error;
  } catch (error) {
    console.error('Supabase连接检查失败:' error.message);
    return false;
  }
};

/**
 * 格式化Supabase错误
 * @param {Object} error - Supabase错误对象
 * @returns {Object} - 格式化的错误对象
 */
const formatSupabaseError = (error) => {
  if (!error) {
    return null;
  }
  
  return {
    message: error.message || '未知错误'
    code: error.code || 'UNKNOWN_ERROR'
    details: error.details || null,
    hint: error.hint || null
  };
};

module.exports = {
  createSupabaseClient,
  createSupabaseClientFromEnv,
  checkSupabaseConnection,
  formatSupabaseError
};