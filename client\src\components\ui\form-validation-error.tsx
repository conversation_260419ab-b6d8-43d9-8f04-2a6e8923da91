import React from 'react';
import { cn } from '../../lib/utils';

interface FormValidationErrorProps {
  error?: string | string[];
  className?: string;
}

export const FormValidationError: React.FC<FormValidationErrorProps> = ({ 
  error, 
  className 
}) => {
  if (!error) return null;

  const errors = Array.isArray(error) ? error : [error];

  return (
    <div className={cn("text-sm text-red-500 mt-1", className)}>
      {errors.map((err, index) => (
        <div key={index}>{err}</div>
      ))}
    </div>
  );
};

export default FormValidationError; 