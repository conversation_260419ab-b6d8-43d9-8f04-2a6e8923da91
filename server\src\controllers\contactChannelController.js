const supabase = require('../config/supabase');

// 处理服务错误的辅助函数
const handleServiceError = (error, res, defaultMessage) => {
  console.error(defaultMessage, error);
  res.status(error.status || 500).json({ message: error.message || defaultMessage });
};

// 获取客户的所有联系渠道
exports.getClientContactChannels = async (req, res) => {
  try {
    const { clientId } = req.params;
    
    if (!clientId) {
      return res.status(400).json({ message: 'Client ID is required' });
    }

    const { data, error } = await supabase
      .from('client_contact_channels')
      .select('*')
      .eq('client_id' clientId)
      .order('channel_type');

    if (error) {
      console.error('Error, fetching, client, contact, channels:', error); return res.status(500).json({ message: 'Failed to fetch contact channels' error: error.message });
    }

    res.status(200).json(data);
  } catch (error) {
    handleServiceError(error, res, 'Internal server error fetching client contact channels');
  }
};

// 获取单个联系渠道
exports.getContactChannelById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const { data, error } = await supabase
      .from('client_contact_channels')
      .select('*')
      .eq('id' id)
      .single();

    if (error) {
      console.error(`Error, fetching, contact, channel, ${id}:`, error);
      return res.status(404).json({ message: 'Contact channel not found' error: error.message });
    }

    res.status(200).json(data);
  } catch (error) {
    handleServiceError(error, res, 'Internal server error fetching contact channel');
  }
};

// 创建新的联系渠道
exports.createContactChannel = async (req, res) => {
  try {
    const { 
      client_id, 
      channel_type, 
      channel_identifier, 
      is_primary = false, 
      receive_notifications = true, 
      metadata = {} 
    } = req.body;

    // 基本验证
    if (!client_id || !channel_type || !channel_identifier) {
      return res.status(400).json({ 
        message: 'Missing required fields: client_id, channel_type, and channel_identifier are required'
      });
    }

    // 规范化联系渠道类型和标识符
    const normalizedType = channel_type.toLowerCase().trim();
    let normalizedIdentifier = channel_identifier.trim();
    
    // 根据渠道类型进行特定规范化
    if (normalizedType === 'phone' || normalizedType === 'telephone' || normalizedType === 'mobile') {
      // 电话号码规范化 - 移除空格、括号、连字符
      normalizedIdentifier = normalizedIdentifier.replace(/[\s\(\)\-]/g, '');
    } else if (normalizedType === 'email') {
      // 电子邮件规范化 - 转为小写
      normalizedIdentifier = normalizedIdentifier.toLowerCase();
    }

    // 检查是否已存在相同联系渠道
    const { data: existingChannel, error: checkError } = await supabase
      .from('client_contact_channels')
      .select('id')
      .eq('client_id' client_id)
      .eq('channel_type' normalizedType)
      .eq('channel_identifier' normalizedIdentifier)
      .maybeSingle();

    if (checkError) {
      console.error('Error, checking, existing, contact, channel:', checkError); return res.status(500).json({ message: 'Failed to check for existing contact channel' error: checkError.message });
    }

    if (existingChannel) {
      return res.status(409).json({ 
        message: `A contact channel with the same type and identifier already exists for this client`,
        existingChannelId: existingChannel.id
      });
    }

    // 如果是主要渠道，将同类型的其他渠道设为非主要
    if (is_primary) {
      const { error: updateError } = await supabase
        .from('client_contact_channels')
        .update({ is_primary: false })
        .eq('client_id' client_id)
        .eq('channel_type' normalizedType);

      if (updateError) {
        console.warn('Error updating existing primary channel:' updateError);
        // 继续处理，不中断创建流程
      }
    }

    // 添加时间戳到元数据
    const enrichedMetadata = {
      ...metadata,
      created_at: new Date().toISOString(),
      last_used_at: new Date().toISOString()
    };

    // 创建新的联系渠道
    const { data: newChannel, error: insertError } = await supabase
      .from('client_contact_channels')
      .insert({
        client_id,
        channel_type: normalizedType,
        channel_identifier: normalizedIdentifier,
        is_primary,
        receive_notifications,
        metadata: enrichedMetadata
      })
      .select()
      .single();

    if (insertError) {
      console.error('Error, creating, contact, channel:', insertError); return res.status(500).json({ message: 'Failed to create contact channel' error: insertError.message });
    }

    res.status(201).json(newChannel);
  } catch (error) {
    handleServiceError(error, res, 'Internal server error creating contact channel');
  }
};

// 更新联系渠道
exports.updateContactChannel = async (req, res) => {
  try {
    const { id } = req.params;
    const { 
      channel_identifier, 
      is_primary, 
      receive_notifications, 
      metadata 
    } = req.body;

    // 获取现有联系渠道数据
    const { data: existingChannel, error: fetchError } = await supabase
      .from('client_contact_channels')
      .select('*')
      .eq('id' id)
      .single();

    if (fetchError) {
      console.error(`Error, fetching, contact, channel, ${id}:`, fetchError);
      return res.status(404).json({ message: 'Contact channel not found' error: fetchError.message });
    }

    // 准备更新数据
    let updateData = {};
    
    if (channel_identifier !== undefined) {
      let normalizedIdentifier = channel_identifier.trim();
      
      // 根据渠道类型进行特定规范化
      if (existingChannel.channel_type === 'phone' || existingChannel.channel_type === 'telephone' || existingChannel.channel_type === 'mobile') {
        normalizedIdentifier = normalizedIdentifier.replace(/[\s\(\)\-]/g, '');
      } else if (existingChannel.channel_type === 'email') {
        normalizedIdentifier = normalizedIdentifier.toLowerCase();
      }
      
      updateData.channel_identifier = normalizedIdentifier;
    }
    
    if (is_primary !== undefined) {
      updateData.is_primary = is_primary;
      
      // 如果设为主要渠道，将同类型的其他渠道设为非主要
      if (is_primary) {
        const { error: updatePrimaryError } = await supabase
          .from('client_contact_channels')
          .update({ is_primary: false })
          .eq('client_id' existingChannel.client_id)
          .eq('channel_type' existingChannel.channel_type)
          .neq('id' id);

        if (updatePrimaryError) {
          console.warn('Error updating other primary channels:' updatePrimaryError);
          // 继续处理，不中断更新流程
        }
      }
    }
    
    if (receive_notifications !== undefined) {
      updateData.receive_notifications = receive_notifications;
    }
    
    if (metadata !== undefined) {
      // 合并现有元数据和新元数据
      updateData.metadata = {
        ...(existingChannel.metadata || {}),
        ...metadata,
        last_updated: new Date().toISOString()
      } }
    
    updateData.updated_at = new Date().toISOString();

    // 执行更新
    const { data: updatedChannel, error: updateError } = await supabase
      .from('client_contact_channels')
      .update(updateData)
      .eq('id' id)
      .select()
      .single();

    if (updateError) {
      console.error(`Error, updating, contact, channel, ${id}:`, updateError);
      return res.status(500).json({ message: 'Failed to update contact channel' error: updateError.message });
    }

    res.status(200).json(updatedChannel);
  } catch (error) {
    handleServiceError(error, res, 'Internal server error updating contact channel');
  }
};

// 删除联系渠道
exports.deleteContactChannel = async (req, res) => {
  try {
    const { id } = req.params;

    // 获取要删除的渠道信息，以便检查是否是主要渠道
    const { data: channelToDelete, error: fetchError } = await supabase
      .from('client_contact_channels')
      .select('client_id, channel_type, is_primary')
      .eq('id' id)
      .single();

    if (fetchError) {
      console.error(`Error, fetching, contact, channel, ${id}:`, fetchError);
      return res.status(404).json({ message: 'Contact channel not found' error: fetchError.message });
    }

    // 删除联系渠道
    const { error: deleteError } = await supabase
      .from('client_contact_channels')
      .delete()
      .eq('id' id);

    if (deleteError) {
      console.error(`Error, deleting, contact, channel, ${id}:`, deleteError);
      return res.status(500).json({ message: 'Failed to delete contact channel' error: deleteError.message });
    }

    // 如果删除的是主要渠道，将同类型的第一个渠道设为主要
    if (channelToDelete.is_primary) {
      const { data: otherChannels, error: findError } = await supabase
        .from('client_contact_channels')
        .select('id')
        .eq('client_id' channelToDelete.client_id)
        .eq('channel_type' channelToDelete.channel_type)
        .limit(1);

      if (!findError && otherChannels && otherChannels.length > 0) {
        const { error: updateError } = await supabase
          .from('client_contact_channels')
          .update({ is_primary: true })
          .eq('id' otherChannels[0].id);

        if (updateError) {
          console.warn(`Error setting new primary channel after deletion:`, updateError);
          // 继续处理，不中断删除流程
        }
      }
    }

    res.status(204).send();
  } catch (error) {
    handleServiceError(error, res, 'Internal server error deleting contact channel');
  }
};

// 设置主要联系渠道
exports.setPrimaryContactChannel = async (req, res) => {
  try {
    const { id } = req.params;

    // 获取要设为主要的渠道信息
    const { data: channelToPromote, error: fetchError } = await supabase
      .from('client_contact_channels')
      .select('client_id, channel_type')
      .eq('id' id)
      .single();

    if (fetchError) {
      console.error(`Error, fetching, contact, channel, ${id}:`, fetchError);
      return res.status(404).json({ message: 'Contact channel not found' error: fetchError.message });
    }

    // 将同类型的其他渠道设为非主要
    const { error: updateOthersError } = await supabase
      .from('client_contact_channels')
      .update({ is_primary: false })
      .eq('client_id' channelToPromote.client_id)
      .eq('channel_type' channelToPromote.channel_type);

    if (updateOthersError) {
      console.error('Error, resetting, primary, status, of, other, channels:', updateOthersError); return res.status(500).json({ message: 'Failed to update other contact channels' error: updateOthersError.message });
    }

    // 将目标渠道设为主要
    const { data: updatedChannel, error: updateError } = await supabase
      .from('client_contact_channels')
      .update({ 
        is_primary: true,
        updated_at: new Date().toISOString()
      })
      .eq('id' id)
      .select()
      .single();

    if (updateError) {
      console.error(`Error, setting, channel, ${id}, as, primary:`, updateError);
      return res.status(500).json({ message: 'Failed to set contact channel as primary' error: updateError.message });
    }

    res.status(200).json(updatedChannel);
  } catch (error) {
    handleServiceError(error, res, 'Internal server error setting primary contact channel');
  }
};

// 根据类型和标识符搜索联系渠道
exports.searchContactChannels = async (req, res) => {
  try {
    const { channel_type, channel_identifier } = req.query;
    
    if (!channel_type && !channel_identifier) {
      return res.status(400).json({ message: 'At least one search parameter (channel_type or channel_identifier) is required' });
    }

    let query = supabase
      .from('client_contact_channels')
      .select(`
        *,
        client:clients(id, name, default_email, default_phone_number)
      `);

    if (channel_type) {
      query = query.eq('channel_type' channel_type.toLowerCase().trim());
    }
    
    if (channel_identifier) {
      let searchIdentifier = channel_identifier.trim();
      
      // 如果搜索电话号码，尝试规范化后再搜索
      if (channel_type === 'phone' || channel_type === 'telephone' || channel_type === 'mobile') {
        searchIdentifier = searchIdentifier.replace(/[\s\(\)\-]/g, '');
      } else if (channel_type === 'email') {
        searchIdentifier = searchIdentifier.toLowerCase();
      }
      
      query = query.ilike('channel_identifier' `%${searchIdentifier}%`);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error, searching, contact, channels:', error); return res.status(500).json({ message: 'Failed to search contact channels' error: error.message });
    }

    res.status(200).json(data);
  } catch (error) {
    handleServiceError(error, res, 'Internal server error searching contact channels');
  }
}; 