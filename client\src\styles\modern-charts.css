/* 现代化图表样式系统 - 2025年设计标准 */

/* 主容器样式 */
.modern-chart-container {
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.95) 0%, 
    rgba(248, 250, 252, 0.95) 100%);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(226, 232, 240, 0.8);
  border-radius: 16px;
  padding: 24px;
  box-shadow: 
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  position: relative;
}

.modern-chart-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(99, 102, 241, 0.3) 50%, 
    transparent 100%);
}

.modern-chart-container:hover {
  transform: translateY(-2px);
  box-shadow: 
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.modern-chart-container:hover::before {
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(99, 102, 241, 0.6) 50%, 
    transparent 100%);
}

/* 图表标题区域 */
.chart-header {
  margin-bottom: 20px;
}

.chart-header .chart-title {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 4px;
  line-height: 1.4;
  background: linear-gradient(135deg, #1e293b 0%, #475569 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.chart-header .chart-subtitle {
  font-size: 14px;
  color: #64748b;
  line-height: 1.4;
  opacity: 0.8;
}

/* 现代化 Tooltip 样式 */
.modern-tooltip,
.ai-tooltip,
.system-tooltip,
.prediction-tooltip {
  position: relative;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(226, 232, 240, 0.8);
  border-radius: 12px;
  padding: 16px;
  box-shadow: 
    0 20px 25px -5px rgba(0, 0, 0, 0.15),
    0 10px 10px -5px rgba(0, 0, 0, 0.1);
  max-width: 300px;
}

.modern-tooltip .tooltip-backdrop,
.ai-tooltip .tooltip-backdrop,
.system-tooltip .tooltip-backdrop,
.prediction-tooltip .tooltip-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.9) 0%, 
    rgba(248, 250, 252, 0.9) 100%);
  border-radius: 12px;
  z-index: -1;
}

.modern-tooltip .tooltip-header,
.ai-tooltip .tooltip-header,
.system-tooltip .tooltip-header,
.prediction-tooltip .tooltip-header {
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 8px;
  font-size: 14px;
  text-align: center;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(226, 232, 240, 0.5);
}

.modern-tooltip .tooltip-body,
.ai-tooltip .tooltip-body,
.system-tooltip .tooltip-body,
.prediction-tooltip .tooltip-body {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.modern-tooltip .tooltip-item,
.ai-tooltip .tooltip-item,
.system-tooltip .tooltip-item,
.prediction-tooltip .tooltip-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 4px 0;
}

.modern-tooltip .tooltip-item .color-indicator,
.ai-tooltip .tooltip-item .color-indicator,
.system-tooltip .tooltip-item .color-indicator,
.prediction-tooltip .tooltip-item .color-indicator {
  width: 12px;
  height: 12px;
  border-radius: 6px;
  flex-shrink: 0;
  border: 2px solid rgba(255, 255, 255, 0.8);
}

.modern-tooltip .tooltip-item .item-name,
.ai-tooltip .tooltip-item .item-name,
.system-tooltip .tooltip-item .item-name,
.prediction-tooltip .tooltip-item .item-name {
  font-size: 13px;
  color: #64748b;
  flex: 1;
}

.modern-tooltip .tooltip-item .item-value,
.ai-tooltip .tooltip-item .item-value,
.system-tooltip .tooltip-item .item-value,
.prediction-tooltip .tooltip-item .item-value {
  font-weight: 600;
  color: #1e293b;
  font-size: 13px;
  text-align: right;
}

/* AI 分析 Tooltip 样式 */
.ai-tooltip .ai-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  padding: 6px 12px;
  background: linear-gradient(135deg, 
    rgba(79, 172, 254, 0.1) 0%, 
    rgba(0, 242, 254, 0.1) 100%);
  border-radius: 8px;
  border: 1px solid rgba(79, 172, 254, 0.2);
}

.ai-tooltip .ai-indicator .ai-pulse {
  width: 8px;
  height: 8px;
  background: linear-gradient(45deg, #4facfe, #00f2fe);
  border-radius: 50%;
  animation: ai-pulse 2s infinite;
}

.ai-tooltip .ai-indicator span {
  font-size: 12px;
  color: #4facfe;
  font-weight: 500;
}

/* 系统监控 Tooltip 样式 */
.system-tooltip .system-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  padding: 6px 12px;
  background: linear-gradient(135deg, 
    rgba(16, 185, 129, 0.1) 0%, 
    rgba(6, 182, 212, 0.1) 100%);
  border-radius: 8px;
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.system-tooltip .system-indicator .status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.system-tooltip .system-indicator .status-dot.status-healthy {
  background: linear-gradient(45deg, #10b981, #06b6d4);
  animation: status-pulse 2s infinite;
}

.system-tooltip .system-indicator .status-dot.status-warning {
  background: linear-gradient(45deg, #f59e0b, #fbbf24);
}

.system-tooltip .system-indicator .status-dot.status-critical {
  background: linear-gradient(45deg, #ef4444, #f87171);
}

.system-tooltip .system-indicator span {
  font-size: 12px;
  color: #10b981;
  font-weight: 500;
}

/* 预测分析 Tooltip 样式 */
.prediction-tooltip .prediction-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  padding: 6px 12px;
  background: linear-gradient(135deg, 
    rgba(250, 112, 154, 0.1) 0%, 
    rgba(254, 225, 64, 0.1) 100%);
  border-radius: 8px;
  border: 1px solid rgba(250, 112, 154, 0.2);
}

.prediction-tooltip .prediction-indicator .prediction-pulse {
  width: 8px;
  height: 8px;
  background: linear-gradient(45deg, #fa709a, #fee140);
  border-radius: 50%;
  animation: prediction-pulse 1.5s infinite;
}

.prediction-tooltip .prediction-indicator span {
  font-size: 12px;
  color: #fa709a;
  font-weight: 500;
}

.prediction-tooltip .confidence-badge {
  background: linear-gradient(135deg, #10b981, #06b6d4);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 600;
  margin-left: 8px;
}

/* 径向图表容器 */
.modern-radial-chart-container {
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.95) 0%, 
    rgba(248, 250, 252, 0.95) 100%);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(226, 232, 240, 0.8);
  border-radius: 16px;
  padding: 24px;
  box-shadow: 
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.modern-radial-chart-container .radial-chart-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  pointer-events: none;
}

.modern-radial-chart-container .radial-chart-center .radial-value {
  font-size: 24px;
  font-weight: 700;
  color: #1e293b;
  line-height: 1;
}

.modern-radial-chart-container .radial-chart-center .radial-unit {
  font-size: 14px;
  color: #64748b;
  margin-top: 2px;
}

/* 系统健康指标卡片 */
.health-metric-card {
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.95) 0%, 
    rgba(248, 250, 252, 0.95) 100%);
  border: 1px solid rgba(226, 232, 240, 0.8);
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.health-metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(99, 102, 241, 0.5) 50%, 
    transparent 100%);
}

.health-metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.health-metric-card:hover::before {
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(99, 102, 241, 0.8) 50%, 
    transparent 100%);
}

.health-metric-card .metric-icon {
  margin-bottom: 12px;
  opacity: 0.8;
  transition: opacity 0.3s ease;
}

.health-metric-card .metric-icon:hover {
  opacity: 1;
}

.health-metric-card .metric-value {
  font-size: 24px;
  font-weight: 700;
  color: #1e293b;
  margin: 8px 0;
  background: linear-gradient(135deg, #1e293b 0%, #475569 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.health-metric-card .metric-description {
  font-size: 12px;
  color: #64748b;
  line-height: 1.4;
}

/* 健康状态仪表盘 */
.health-dashboard {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

/* 动画定义 */
@keyframes ai-pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(79, 172, 254, 0.7);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.1);
    box-shadow: 0 0 0 10px rgba(79, 172, 254, 0);
  }
}

@keyframes status-pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.1);
    box-shadow: 0 0 0 8px rgba(16, 185, 129, 0);
  }
}

@keyframes prediction-pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(250, 112, 154, 0.7);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.2);
    box-shadow: 0 0 0 8px rgba(250, 112, 154, 0);
  }
}

@keyframes loading-shimmer {
  0% { 
    background-position: -200% 0; 
  }
  100% { 
    background-position: 200% 0; 
  }
}

@keyframes chart-enter {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 图表加载状态 */
.chart-loading {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.chart-loading .skeleton-bar {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading-shimmer 1.5s infinite;
  border-radius: 4px;
}

/* 图表入场动画 */
.chart-enter {
  opacity: 0;
  transform: scale(0.9) translateY(20px);
}

.chart-enter-active {
  opacity: 1;
  transform: scale(1) translateY(0);
  transition: all 0.6s ease-out;
  animation: chart-enter 0.6s ease-out;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .modern-chart-container,
  .modern-radial-chart-container {
    padding: 16px;
    border-radius: 12px;
  }
  
  .chart-header {
    margin-bottom: 16px;
  }
  
  .chart-header .chart-title {
    font-size: 16px;
  }
  
  .chart-header .chart-subtitle {
    font-size: 13px;
  }
  
  .modern-tooltip,
  .ai-tooltip,
  .system-tooltip,
  .prediction-tooltip {
    padding: 12px;
    max-width: 250px;
  }
  
  .modern-tooltip .tooltip-header,
  .ai-tooltip .tooltip-header,
  .system-tooltip .tooltip-header,
  .prediction-tooltip .tooltip-header {
    font-size: 13px;
  }
  
  .modern-tooltip .tooltip-item .item-name,
  .modern-tooltip .tooltip-item .item-value,
  .ai-tooltip .tooltip-item .item-name,
  .ai-tooltip .tooltip-item .item-value,
  .system-tooltip .tooltip-item .item-name,
  .system-tooltip .tooltip-item .item-value,
  .prediction-tooltip .tooltip-item .item-name,
  .prediction-tooltip .tooltip-item .item-value {
    font-size: 12px;
  }
  
  .health-dashboard {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .health-metric-card {
    padding: 16px;
  }
  
  .health-metric-card .metric-value {
    font-size: 20px;
  }
}

@media (max-width: 480px) {
  .modern-chart-container,
  .modern-radial-chart-container {
    padding: 12px;
    border-radius: 8px;
  }
  
  .chart-header {
    margin-bottom: 12px;
  }
  
  .chart-header .chart-title {
    font-size: 14px;
  }
  
  .chart-header .chart-subtitle {
    font-size: 12px;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .modern-chart-container,
  .modern-radial-chart-container {
    background: linear-gradient(135deg, 
      rgba(30, 41, 59, 0.95) 0%, 
      rgba(15, 23, 42, 0.95) 100%);
    border-color: rgba(51, 65, 85, 0.8);
  }
  
  .modern-chart-container::before,
  .modern-radial-chart-container::before {
    background: linear-gradient(90deg, 
      transparent 0%, 
      rgba(139, 92, 246, 0.3) 50%, 
      transparent 100%);
  }
  
  .chart-header .chart-title {
    color: #f1f5f9 !important;
    background: linear-gradient(135deg, #f1f5f9 0%, #cbd5e1 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .chart-header .chart-subtitle {
    color: #94a3b8 !important;
  }
  
  .modern-tooltip,
  .ai-tooltip,
  .system-tooltip,
  .prediction-tooltip {
    background: rgba(30, 41, 59, 0.98);
    border-color: rgba(51, 65, 85, 0.8);
  }
  
  .modern-tooltip .tooltip-backdrop,
  .ai-tooltip .tooltip-backdrop,
  .system-tooltip .tooltip-backdrop,
  .prediction-tooltip .tooltip-backdrop {
    background: linear-gradient(135deg, 
      rgba(30, 41, 59, 0.9) 0%, 
      rgba(15, 23, 42, 0.9) 100%);
  }
  
  .modern-tooltip .tooltip-header,
  .ai-tooltip .tooltip-header,
  .system-tooltip .tooltip-header,
  .prediction-tooltip .tooltip-header {
    color: #f1f5f9;
    border-bottom-color: rgba(51, 65, 85, 0.5);
  }
  
  .modern-tooltip .tooltip-item .item-name,
  .ai-tooltip .tooltip-item .item-name,
  .system-tooltip .tooltip-item .item-name,
  .prediction-tooltip .tooltip-item .item-name {
    color: #94a3b8;
  }
  
  .modern-tooltip .tooltip-item .item-value,
  .ai-tooltip .tooltip-item .item-value,
  .system-tooltip .tooltip-item .item-value,
  .prediction-tooltip .tooltip-item .item-value {
    color: #f1f5f9;
  }
  
  .health-metric-card {
    background: linear-gradient(135deg, 
      rgba(30, 41, 59, 0.95) 0%, 
      rgba(15, 23, 42, 0.95) 100%);
    border-color: rgba(51, 65, 85, 0.8);
  }
  
  .health-metric-card .metric-value {
    color: #f1f5f9;
    background: linear-gradient(135deg, #f1f5f9 0%, #cbd5e1 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .health-metric-card .metric-description {
    color: #94a3b8;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .modern-chart-container,
  .modern-radial-chart-container {
    border: 2px solid #000;
    background: #fff;
  }
  
  .chart-header .chart-title {
    color: #000 !important;
    background: none !important;
    -webkit-text-fill-color: #000;
  }
  
  .modern-tooltip,
  .ai-tooltip,
  .system-tooltip,
  .prediction-tooltip {
    border: 2px solid #000;
    background: #fff;
  }
  
  .modern-tooltip .tooltip-header,
  .modern-tooltip .tooltip-item .item-value,
  .ai-tooltip .tooltip-header,
  .ai-tooltip .tooltip-item .item-value,
  .system-tooltip .tooltip-header,
  .system-tooltip .tooltip-item .item-value,
  .prediction-tooltip .tooltip-header,
  .prediction-tooltip .tooltip-item .item-value {
    color: #000;
  }
}

/* 确保图表文字可见性 */
.recharts-pie-label-text,
.recharts-label {
  fill: #1f2937 !important;
  font-weight: 600 !important;
  font-size: 12px !important;
}

.recharts-text {
  fill: #374151 !important;
}

.recharts-cartesian-axis-tick-value {
  fill: #374151 !important;
  font-weight: 500 !important;
}

.recharts-default-legend .recharts-legend-item-text {
  color: #374151 !important;
  font-weight: 500 !important;
}

/* 饼图文字增强可见性 */
.recharts-pie-label-line {
  stroke: #374151 !important;
  stroke-width: 1px !important;
}

/* 确保深色背景下的文字可见性 */
@media (prefers-color-scheme: dark) {
  .recharts-pie-label-text,
  .recharts-label,
  .recharts-text,
  .recharts-cartesian-axis-tick-value {
    fill: #f8fafc !important;
  }
  
  .recharts-default-legend .recharts-legend-item-text {
    color: #f8fafc !important;
  }
  
  .recharts-pie-label-line {
    stroke: #f8fafc !important;
  }
}

/* 统一的Export按钮样式 */
.export-button-primary {
  @apply flex items-center gap-2 h-8 px-4 bg-purple-600 hover:bg-purple-700 text-white font-semibold shadow-md hover:shadow-lg border-2 border-purple-500 hover:border-purple-600 transition-all duration-200 hover:scale-105;
}

.export-button-outline {
  @apply flex items-center gap-2 h-8 px-4 bg-white text-gray-800 border-2 border-gray-300 hover:bg-gray-50 hover:border-gray-400 font-semibold shadow-md hover:shadow-lg transition-all duration-200 hover:scale-105;
}

/* 实时指示器强制可见性 */
.real-time-badge-override {
  background-color: #059669 !important;
  color: white !important;
  font-weight: 700 !important;
  opacity: 1 !important;
  z-index: 1000 !important;
  border: 2px solid #10b981 !important;
}

.real-time-badge-override.offline {
  background-color: #dc2626 !important;
  border-color: #ef4444 !important;
}

/* KPI卡片增强样式 */
.kpi-card-enhanced {
  @apply rounded-lg shadow-lg transition-all duration-300 hover:shadow-xl hover:scale-105 border;
}

.kpi-card-primary {
  @apply bg-gradient-to-br from-purple-500 to-purple-700 text-white border-purple-300;
}

.kpi-card-success {
  @apply bg-gradient-to-br from-emerald-500 to-emerald-700 text-white border-emerald-300;
}

.kpi-card-warning {
  @apply bg-gradient-to-br from-amber-500 to-amber-700 text-white border-amber-300;
}

.kpi-card-error {
  @apply bg-gradient-to-br from-rose-500 to-rose-700 text-white border-rose-300;
}

/* 高亮数据文本确保可见性 */
.highlighted-text {
  color: white !important;
  font-weight: 700 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

.highlighted-value {
  color: white !important;
  font-weight: 900 !important;
  font-size: 1.875rem !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

/* 趋势指示器样式 */
.trend-indicator {
  @apply inline-flex items-center rounded-full px-3 py-1 text-sm font-semibold text-white bg-white/20 border border-white/30;
}

/* 图标容器样式 */
.icon-container {
  @apply rounded-full bg-white/20 p-3 border border-white/30;
}

.icon-container .icon {
  @apply text-white;
} 