import axios from 'axios';

// 服务地址配置
export const API_GATEWAY_URL = process.env.REACT_APP_API_GATEWAY_URL || 'http://localhost:3001';
export const AI_SERVICE_URL = process.env.REACT_APP_AI_SERVICE_URL || 'http://localhost:3003';
console.log('Using API_GATEWAY_URL:', API_GATEWAY_URL);
console.log('Using AI_SERVICE_URL for direct AI connection:', AI_SERVICE_URL);

const aiServiceApi = axios.create({ 
  baseURL: AI_SERVICE_URL,
  headers: {
    'Content-Type': 'application/json'
  },
  timeout: 30000, // 30秒超时
  maxContentLength: 10485760, // 10MB，文件上传需要较大限制
  maxBodyLength: 10485760,    // 10MB
});

// 请求拦截器 - 添加认证
aiServiceApi.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    // 为调试添加请求日志
    console.log(`[AI API] ${config.method?.toUpperCase()} ${config.url}`, {
      data: config.data,
      params: config.params
    });
    
    return config;
  },
  (error) => {
    console.error('[AI API] Request error:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器 - 统一错误处理
aiServiceApi.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // 记录详细错误
    console.error('[AI API] Response error:', {
      message: error.message,
      code: error.code,
      stack: error.stack,
      response: error.response ? {
        status: error.response.status,
        data: error.response.data
      } : 'No response',
      request: error.request || 'No request'
    });
    
    // 超时处理
    if (error.code === 'ECONNABORTED' && error.message && error.message.includes('timeout')) {
      console.error('[AI API] Request timed out after', aiServiceApi.defaults.timeout, 'ms');
    }
    
    return Promise.reject(error);
  }
);

// 特殊配置用于文件上传
export const createUploadInstance = (onUploadProgress?: (progressEvent: any) => void) => {
  // 从本地存储获取令牌
  const token = localStorage.getItem('token');
  
  // 如果没有令牌，使用测试令牌
  const authToken = token || 'test-token-for-api-authentication';
  console.log('Upload auth token:', authToken ? (authToken.substring(0, 10) + '...') : 'none');
  
  const instance = axios.create({ 
    baseURL: API_GATEWAY_URL, // 使用网关地址而非直接AI服务地址
    timeout: 60000, // 上传文件需要更长的超时时间
    headers: {
      'Content-Type': 'multipart/form-data',
      'Authorization': `Bearer ${authToken}`
    },
    maxContentLength: 20971520, // 20MB
    maxBodyLength: 20971520,    // 20MB
    onUploadProgress
  });
  
  // 添加错误拦截器用于详细日志
  instance.interceptors.response.use(
    (response) => response,
    (error) => {
      console.error('[AI Upload API] Error:', {
        message: error.message,
        code: error.code,
        response: error.response ? {
          status: error.response.status,
          data: error.response.data
        } : 'No response'
      });
      return Promise.reject(error);
    }
  );
  
  return instance;
};

export default aiServiceApi;