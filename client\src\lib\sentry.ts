import * as Sentry from '@sentry/react';
// import { BrowserTracing } from '@sentry/tracing';

// 配置 Sentry - 基础版本，无版本冲突
export const initSentry = () => {
  // 检查 DSN 是否设置
  const dsn = process.env.REACT_APP_SENTRY_DSN;
  
  if (!dsn) {
    console.warn('⚠️ Sentry DSN not set. Skipping Sentry initialization.');
    return;
  }

  Sentry.init({
    // 您需要从 Sentry.io 获取您的 DSN
    dsn: dsn,
    
    // 设置环境
    environment: process.env.NODE_ENV || 'development',
    
    // ⚠️ 暂时移除 BrowserTracing 以解决版本问题
    integrations: [
      // 可以在此添加其他兼容的集成
    ],
    
    // ⚠️ 重要: 性能监控采样率 - 确保数据被收集
    tracesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,
    
    // 错误采样率
    sampleRate: 1.0,
    
    // ⚠️ 重要: 在开发环境中显示更多调试信息
    debug: process.env.NODE_ENV === 'development',
    
    // ⚠️ 重要: 启用默认 PII 数据收集
    sendDefaultPii: true,
    
    // 设置用户上下文
    beforeSend(event, hint) {
      // 在开发环境中，可以在控制台中看到错误
      if (process.env.NODE_ENV === 'development') {
        console.group('🚨 Sentry Error');
        console.error('Error:', hint.originalException || hint.syntheticException);
        console.error('Event:', event);
        console.log('DSN Check:', !!dsn);
        console.log('DSN Value:', dsn ? 'SET' : 'NOT SET');
        console.groupEnd();
      }
      return event;
    },
    
    // 忽略某些错误
    ignoreErrors: [
      // 忽略网络错误
      'Network Error',
      'NetworkError',
      // 忽略脚本加载错误
      'Script error',
      // 忽略 ResizeObserver 错误（常见的浏览器兼容性问题）
      'ResizeObserver loop limit exceeded',
      // 忽略取消的请求
      'AbortError',
      // 忽略断开连接的客户端错误 (减少噪音)
      'request aborted',
    ],
    
    // 设置发布版本
    release: process.env.REACT_APP_VERSION || '1.0.0',
  });

  // ⚠️ 重要: 初始化后立即测试
  console.log('✅ Sentry initialized successfully');
  console.log('DSN Status:', dsn ? '✅ SET' : '❌ NOT SET');
  
  if (process.env.NODE_ENV === 'development') {
    // 发送测试事件
    setTimeout(() => {
      Sentry.captureMessage('Sentry React frontend initialized - Test Message', 'info');
      console.log('📤 Test message sent to Sentry');
      
      // 测试错误捕获
      setTimeout(() => {
        try {
          throw new Error('Test error for Sentry verification');
        } catch (error) {
          Sentry.captureException(error);
          console.log('📤 Test error sent to Sentry');
        }
      }, 2000);
    }, 1000);
  }
};

// 手动捕获错误的工具函数
export const captureError = (error: Error, context?: Record<string, any>) => {
  Sentry.withScope((scope) => {
    if (context) {
      Object.keys(context).forEach((key) => {
        scope.setContext(key, context[key]);
      });
    }
    Sentry.captureException(error);
  });
};

// 手动记录消息的工具函数
export const captureMessage = (message: string, level: Sentry.SeverityLevel = 'info') => {
  Sentry.captureMessage(message, level);
};

// 设置用户信息
export const setUser = (user: { id: string; email?: string; username?: string }) => {
  Sentry.setUser(user);
};

// 添加面包屑（用户操作记录）
export const addBreadcrumb = (message: string, category: string, level: Sentry.SeverityLevel = 'info') => {
  Sentry.addBreadcrumb({
    message,
    category,
    level,
    timestamp: Date.now() / 1000,
  });
};

// ⚠️ 简化的性能测量
export const measurePerformance = (name: string, fn: () => void) => {
  const start = performance.now();
  try {
    const result = fn();
    const duration = performance.now() - start;
    Sentry.addBreadcrumb({
      message: `Performance: ${name} took ${duration.toFixed(2)}ms`,
      category: 'performance',
      level: 'info',
    });
    return result;
  } catch (error) {
    Sentry.captureException(error);
    throw error;
  }
};

// ⚠️ 新增: 测试 Sentry 连接
export const testSentryConnection = () => {
  try {
    Sentry.captureMessage('Testing Sentry connection from frontend', 'info');
    console.log('✅ Sentry test message sent');
    return true;
  } catch (error) {
    console.error('❌ Sentry test failed:', error);
    return false;
  }
};

// React 错误边界组件
export const SentryErrorBoundary = Sentry.ErrorBoundary;

export default Sentry; 