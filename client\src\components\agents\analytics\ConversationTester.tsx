﻿/* eslint-disable unicode-bom, @typescript-eslint/no-unused-vars */
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Send, RotateCcw, MessageSquare, Bot, User, Clock, Play, Pause, MessageCircle } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { cn } from '@/lib/utils';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';

interface ConversationTesterProps {
  agentId?: string;
  onTestComplete?: (results: any) => void;
}

interface TestResult {
  id: string;
  input: string;
  output: string;
  timestamp: Date;
  responseTime: number;
  confidence: number;
  intent?: string;
  entities?: any[];
}

interface TestMetrics {
  totalTests: number;
  averageResponseTime: number;
  averageConfidence: number;
  successRate: number;
}

const ConversationTester: React.FC<ConversationTesterProps> = ({
  agentId,
  onTestComplete
}) => {
  const { t } = useTranslation();
  
  // Core testing state
  const [testInput, setTestInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [currentTest, setCurrentTest] = useState<TestResult | null>(null);
  
  // Test metrics
  const [testMetrics] = useState<TestMetrics>({
    totalTests: 0,
    averageResponseTime: 0,
    averageConfidence: 0,
    successRate: 0
  });

  const handleSingleTest = async () => {
    if (!testInput.trim()) return;
    
    setIsLoading(true);
    setCurrentTest(null);
    
    try {
      const startTime = Date.now();
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
      
      const responseTime = Date.now() - startTime;
      const result: TestResult = {
        id: `test-${Date.now()}`,
        input: testInput,
        output: `This is a simulated response to: "${testInput}". The agent would analyze this input and provide an appropriate response based on its training.`,
        timestamp: new Date(),
        responseTime,
        confidence: 0.7 + Math.random() * 0.3,
        intent: "general_inquiry",
        entities: [],
      };
      
      setCurrentTest(result);
      setTestResults(prev => [result, ...prev]);
      onTestComplete?.(result);
      
    } catch (error) {
      console.error('Test failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleClearResults = () => {
    setTestResults([]);
    setCurrentTest(null);
  };

  const formatResponseTime = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    return `${(ms / 1000).toFixed(1)}s`;
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-600';
    if (confidence >= 0.6) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">{t('agents.testing.conversationTester.title')}</h3>
        <div className="flex gap-2">
          <Button variant="outline" onClick={handleClearResults} disabled={testResults.length === 0}>
            <RotateCcw className="w-4 h-4 mr-2" />
            Clear Results
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Test Input</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Textarea
            placeholder="Enter a message to test how your agent would respond..."
            value={testInput}
            onChange={(e) => setTestInput(e.target.value)}
            rows={3}
          />
          <Button 
            onClick={handleSingleTest} 
            disabled={!testInput.trim() || isLoading}
            className="w-full"
          >
            {isLoading ? (
              <>
                <Pause className="w-4 h-4 mr-2" />
                Testing...
              </>
            ) : (
              <>
                <Play className="w-4 h-4 mr-2" />
                Test Response
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {currentTest && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MessageCircle className="w-5 h-5" />
              Latest Test Result
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h4 className="font-semibold text-sm text-muted-foreground mb-2">Input:</h4>
              <p className="bg-muted p-3 rounded">{currentTest.input}</p>
            </div>
            <div>
              <h4 className="font-semibold text-sm text-muted-foreground mb-2">Response:</h4>
              <p className="bg-muted p-3 rounded">{currentTest.output}</p>
            </div>
            <div className="flex gap-4 text-sm">
              <span>Response Time: <Badge variant="secondary">{formatResponseTime(currentTest.responseTime)}</Badge></span>
              <span>Confidence: <Badge variant="secondary" className={getConfidenceColor(currentTest.confidence)}>
                {Math.round(currentTest.confidence * 100)}%
              </Badge></span>
              {currentTest.intent && (
                <span>Intent: <Badge variant="outline">{currentTest.intent}</Badge></span>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      <Tabs defaultValue="results" className="space-y-4">
        <TabsList>
          <TabsTrigger value="results">Test Results</TabsTrigger>
          <TabsTrigger value="metrics">Metrics</TabsTrigger>
        </TabsList>

        <TabsContent value="results">
          <Card>
            <CardHeader>
              <CardTitle>Test History ({testResults.length})</CardTitle>
            </CardHeader>
            <CardContent>
              {testResults.length === 0 ? (
                <p className="text-muted-foreground text-center py-8">No test results yet. Start testing to see results here.</p>
              ) : (
                <div className="space-y-4 max-h-96 overflow-y-auto">
                  {testResults.map((result) => (
                    <div key={result.id} className="border rounded-lg p-4">
                      <div className="flex justify-between items-start mb-2">
                        <span className="text-sm font-medium">{result.timestamp.toLocaleTimeString()}</span>
                        <div className="flex gap-2">
                          <Badge variant="secondary">{formatResponseTime(result.responseTime)}</Badge>
                          <Badge variant="secondary" className={getConfidenceColor(result.confidence)}>
                            {Math.round(result.confidence * 100)}%
                          </Badge>
                        </div>
                      </div>
                      <p className="text-sm text-muted-foreground mb-2">Input: {result.input}</p>
                      <p className="text-sm">Response: {result.output}</p>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="metrics">
          <Card>
            <CardHeader>
              <CardTitle>Performance Metrics</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold">{testMetrics.totalTests}</div>
                  <div className="text-sm text-muted-foreground">Total Tests</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">{testMetrics.averageResponseTime}ms</div>
                  <div className="text-sm text-muted-foreground">Avg Response Time</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">{Math.round(testMetrics.averageConfidence * 100)}%</div>
                  <div className="text-sm text-muted-foreground">Avg Confidence</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">{Math.round(testMetrics.successRate * 100)}%</div>
                  <div className="text-sm text-muted-foreground">Success Rate</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ConversationTester;
