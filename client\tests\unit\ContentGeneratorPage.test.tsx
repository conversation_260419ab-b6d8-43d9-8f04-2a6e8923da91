import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import ContentGeneratorPage from '../../src/pages/modules/ContentGeneratorPage';
import * as contentGeneratorService from '../../src/services/contentGeneratorService';

// 模拟内容生成服务
jest.mock('../../src/services/contentGeneratorService', () => ({
  generateContent: jest.fn(),
  getTemplates: jest.fn().mockResolvedValue([]),
  getHistory: jest.fn().mockResolvedValue([])
}));

// 模拟路由相关组件
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => jest.fn(),
  useParams: () => ({ moduleId: '123' })
}));

describe('ContentGeneratorPage组件', () => {
  beforeEach(() => {
    // 重置所有模拟函数
    jest.clearAllMocks();
  });

  test('渲染包含表单、生成按钮和Tab切换', () => {
    render(<ContentGeneratorPage />);
    
    // 检查表单元素是否存在
    expect(screen.getByRole('textbox', { name: /prompt/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /generate/i })).toBeInTheDocument();
    
    // 检查Tab切换
    expect(screen.getByRole('tab', { name: /generator/i })).toBeInTheDocument();
    expect(screen.getByRole('tab', { name: /history/i })).toBeInTheDocument();
    expect(screen.getByRole('tab', { name: /templates/i })).toBeInTheDocument();
  });

  test('点击生成按钮后调用生成内容服务', async () => {
    // 模拟生成服务返回成功结果
    const mockResult = { 
      content: '这是生成的内容',
      id: 'gen_123' 
    };
    (contentGeneratorService.generateContent as jest.Mock).mockResolvedValue(mockResult);
    
    render(<ContentGeneratorPage />);
    
    // 输入提示文本
    const promptInput = screen.getByRole('textbox', { name: /prompt/i });
    fireEvent.change(promptInput, { target: { value: '测试提示' } });
    
    // 点击生成按钮
    const generateButton = screen.getByRole('button', { name: /generate/i });
    fireEvent.click(generateButton);
    
    // 验证服务被调用
    await waitFor(() => {
      expect(contentGeneratorService.generateContent).toHaveBeenCalledWith({
        prompt: '测试提示'
      });
    });
    
    // 验证显示生成结果
    await waitFor(() => {
      expect(screen.getByText('这是生成的内容')).toBeInTheDocument();
    });
  });

  test('生成内容时显示加载状态', async () => {
    // 模拟服务需要时间响应
    (contentGeneratorService.generateContent as jest.Mock).mockImplementation(
      () => new Promise(resolve => setTimeout(() => resolve({ content: '结果' }), 100))
    );
    
    render(<ContentGeneratorPage />);
    
    // 输入提示并点击生成
    fireEvent.change(screen.getByRole('textbox', { name: /prompt/i }), { 
      target: { value: '测试' } 
    });
    fireEvent.click(screen.getByRole('button', { name: /generate/i }));
    
    // 验证加载状态
    expect(screen.getByText(/loading/i) || screen.getByRole('progressbar')).toBeInTheDocument();
    
    // 等待结果
    await waitFor(() => {
      expect(screen.getByText('结果')).toBeInTheDocument();
    });
  });

  test('处理生成内容失败的情况', async () => {
    // 模拟服务返回错误
    (contentGeneratorService.generateContent as jest.Mock).mockRejectedValue(
      new Error('生成失败')
    );
    
    render(<ContentGeneratorPage />);
    
    // 输入提示并点击生成
    fireEvent.change(screen.getByRole('textbox', { name: /prompt/i }), { 
      target: { value: '错误测试' } 
    });
    fireEvent.click(screen.getByRole('button', { name: /generate/i }));
    
    // 验证显示错误消息
    await waitFor(() => {
      expect(screen.getByText(/error|失败|错误/i)).toBeInTheDocument();
    });
  });
}); 