require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

console.log('🔍 Supabase调试脚本启动...\n');

// 读取环境变量
const supabaseUrl = process.env.SUPABASE_URL || process.env.REACT_APP_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_ANON_KEY || process.env.REACT_APP_SUPABASE_ANON_KEY;

console.log('📋 环境变量检查:');
console.log('- SUPABASE_URL:', supabaseUrl ? '✅ 已设置' : '❌ 未设置');
console.log('- SUPABASE_ANON_KEY:', supabaseKey ? '✅ 已设置' : '❌ 未设置');
console.log('- NODE_ENV:', process.env.NODE_ENV || 'undefined');
console.log('');

if (!supabaseUrl || !supabaseKey) {
  console.log('❌ 缺少必要的Supabase环境变量');
  process.exit(1);
}

// 显示配置信息
console.log('🔗 连接信息:');
console.log('- URL:', supabaseUrl);
console.log('- Key:', supabaseKey.substring(0, 20) + '...');
console.log('');

// 创建Supabase客户端
console.log('🚀 初始化Supabase客户端...');
let supabase;

try {
  supabase = createClient(supabaseUrl, supabaseKey);
  console.log('✅ Supabase客户端创建成功');
} catch (error) {
  console.log('❌ Supabase客户端创建失败:', error.message);
  process.exit(1);
}

// 测试连接
async function testConnection() {
  console.log('\n🧪 测试数据库连接...');
  
  try {
    // 1. 测试基本查询
    console.log('1. 测试基本查询...');
    const { data, error } = await supabase
      .from('agents')
      .select('count')
      .limit(1);
    
    if (error) {
      console.log('❌ 基本查询失败:', error.message);
      console.log('   错误详情:', error);
      
      // 如果表不存在，显示具体错误
      if (error.code === '42P01') {
        console.log('💡 表 "agents" 不存在，需要创建数据库表');
      }
    } else {
      console.log('✅ 基本查询成功');
    }
    
    // 2. 测试插入数据 (使用不存在的表来测试错误处理)
    console.log('\n2. 测试表结构...');
    const { data: tableData, error: tableError } = await supabase
      .from('agents')
      .select('*')
      .limit(1);
    
    if (tableError) {
      console.log('❌ 表查询失败:', tableError.message);
      if (tableError.code === '42P01') {
        console.log('💡 需要运行数据库迁移脚本创建表');
      }
    } else {
      console.log('✅ 表查询成功，找到数据:', tableData?.length || 0, '条记录');
    }
    
  } catch (error) {
    console.log('❌ 连接测试失败:', error.message);
    console.log('   详细错误:', error);
  }
}

// 运行测试
testConnection().then(() => {
  console.log('\n🎯 调试完成');
}).catch((error) => {
  console.log('\n❌ 调试过程出错:', error.message);
}); 