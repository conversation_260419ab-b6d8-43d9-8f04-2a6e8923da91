/**
 * 数字格式化工具函数
 */

/**
 * 格式化为货币格式
 * @param value 数值
 * @param currency 货币符号，默认为￥
 * @param decimals 小数位数，默认为2
 * @returns 格式化后的货币字符串
 */
export const formatCurrency = (
  value: number,
  currency: string = '￥',
  decimals: number = 2
): string => {
  if (value === null || value === undefined) return '';
  
  return `${currency}${value.toFixed(decimals).replace(/\B(?=(\d{3})+(?!\d))/g, ',')}`;
};

/**
 * 格式化为百分比
 * @param value 数值（0-1之间）
 * @param decimals 小数位数，默认为0
 * @returns 格式化后的百分比字符串
 */
export const formatPercent = (value: number, decimals: number = 0): string => {
  if (value === null || value === undefined) return '';
  
  return `${(value * 100).toFixed(decimals)}%`;
};

/**
 * 格式化文件大小
 * @param bytes 字节数
 * @param decimals 小数位数，默认为2
 * @returns 格式化后的文件大小字符串
 */
export const formatFileSize = (bytes: number, decimals: number = 2): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(decimals))} ${sizes[i]}`;
};

/**
 * 格式化数字为K,M,B格式（例如：1.5K, 2.3M）
 * @param value 数值
 * @param decimals 小数位数，默认为1
 * @returns 格式化后的字符串
 */
export const formatCompact = (value: number, decimals: number = 1): string => {
  if (value === null || value === undefined) return '';
  
  if (value < 1000) {
    return value.toString();
  } else if (value < 1000000) {
    return `${(value / 1000).toFixed(decimals)}K`;
  } else if (value < 1000000000) {
    return `${(value / 1000000).toFixed(decimals)}M`;
  } else {
    return `${(value / 1000000000).toFixed(decimals)}B`;
  }
}; 