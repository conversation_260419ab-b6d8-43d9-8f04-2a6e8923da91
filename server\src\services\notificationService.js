const supabase = require('../config/supabase');
const { format } = require('date-fns');

/**
 * 通知服务 - 处理预约提醒通知
 */
class NotificationService {
  /**
   * 查找需要发送提醒的预约
   * @param {Object} options - 查询选项
   * @param {number} options.hoursAhead - 提前多少小时查找（默认24小时）
   * @param {string[]} options.notificationChannels - 筛选通知渠道
   * @param {string[]} options.bookingTypes - 筛选预约类型
   * @returns {Promise<Array>} 需要发送提醒的预约列表
   */
  async findAppointmentsForReminders(options = {}) {
    const {
      hoursAhead = 24,
      notificationChannels = null,
      bookingTypes = null
    } = options;

    try {
      // 计算时间范围
      const now = new Date();
      const futureTime = new Date(now.getTime() + hoursAhead * 60 * 60 * 1000);

      // 构建基础查询
      let query = supabase
        .from('appointments')
        .select(`
          *,
          service:services(id, name, duration, max_overlap, max_overlap_walkin, max_overlap_onsite), 
          team:teams(id, name, phone),
          client:clients(id, email, phone, messenger_id, instagram_id, whatsapp_number)
        `)
        .gte('start', now.toISOString())
        .lt('start', futureTime.toISOString())
        .in('status', ['confirmed', 'pending'])
        .is('notification_status', null);
      
      // 根据通知渠道筛选
      if (notificationChannels && notificationChannels.length > 0) {
        query = query.in('booked_via_channel', notificationChannels);
      }

      // 根据预约类型筛选
      if (bookingTypes && bookingTypes.length > 0) {
        query = query.in('booking_type', bookingTypes);
      }
      
      const { data, error } = await query;
      
      if (error) {
        console.error("Error finding appointments for reminders:", error);
        throw error;
      }
      
      return data || [];
    } catch (error) {
      console.error("Error in findAppointmentsForReminders:", error);
      throw error;
    }
  }

  /**
   * 查找适用于特定预约的通知规则
   * @param {Object} appointment - 预约对象
   * @returns {Promise<Array>} 适用的通知规则列表
   */
  async findApplicableRules(appointment) {
    try {
      const { service_id, booking_type } = appointment;
      
      let query = supabase
        .from('notification_rules')
        .select('*')
        .eq('is_active', true);
      
      // 筛选：通用规则，或特定服务规则，或特定预约类型规则
      query = query.or(`service_id.is.null,service_id.eq.${service_id}`);
      
      if (booking_type) {
        query = query.or(`booking_type.is.null,booking_type.eq.${booking_type}`);
      }
      
      const { data, error } = await query;
      
      if (error) {
        console.error("Error finding applicable notification rules:", error);
        throw error;
      }
      
      return data || [];
    } catch (error) {
      console.error("Error in findApplicableRules:", error);
      throw error;
    }
  }

  /**
   * 发送预约提醒通知
   * @param {Object} appointment - 预约信息
   * @param {Object} rule - 通知规则
   * @returns {Promise<Object>} 发送结果
   */
  async sendNotification(appointment, rule) {
    try {
      // 获取预约和客户信息
      const { clientName, start, service, client, team, booked_via_channel } = appointment;
      const { notification_channel, message_content } = rule;
      
      // 确定使用的通知渠道 - 优先使用预约创建时的渠道，如果规则有特定要求则使用规则中的渠道
      const channelToUse = (notification_channel === 'same_as_booking') 
        ? booked_via_channel || 'email' 
        : notification_channel;
        
      // 准备通知内容
      const appointmentTime = format(new Date(start), 'yyyy-MM-dd HH:mm');
      const serviceName = service?.name || '未指定服务';
      const teamName = team?.name || '未指定团队';
      
      // 替换模板占位符
      let finalMessage = message_content
        .replace('{clientName}', clientName)
        .replace('{appointmentTime}', appointmentTime)
        .replace('{serviceName}', serviceName)
        .replace('{teamName}', teamName);
      
      // 根据渠道发送通知
      let result = {
        success: false,
        channel: channelToUse,
        error: null
      };
      
      switch (channelToUse) {
        case 'email':
          // 如果有客户邮箱，则通过邮件发送
          if (client?.email) {
            result = await this._sendEmailNotification(client.email, finalMessage, appointment);
          } else {
            result.error = 'Client email not available';
          }
          break;
          
        case 'whatsapp':
          // 如果有WhatsApp号码，则通过WhatsApp发送
          if (client?.whatsapp_number) {
            result = await this._sendWhatsAppNotification(client.whatsapp_number, finalMessage, appointment);
          } else {
            result.error = 'WhatsApp number not available';
          }
          break;
          
        case 'messenger':
          // 如果有Messenger ID，则通过Messenger发送
          if (client?.messenger_id) {
            result = await this._sendMessengerNotification(client.messenger_id, finalMessage, appointment);
          } else {
            result.error = 'Messenger ID not available';
          }
          break;
          
        case 'instagram':
          // 如果有Instagram ID，则通过Instagram发送
          if (client?.instagram_id) {
            result = await this._sendInstagramNotification(client.instagram_id, finalMessage, appointment);
          } else {
            result.error = 'Instagram ID not available';
          }
          break;
          
        case 'sms':
          // 如果有电话号码，则通过SMS发送
          if (client?.phone) {
            result = await this._sendSMSNotification(client.phone, finalMessage, appointment);
          } else {
            result.error = 'Phone number not available';
          }
          break;
          
        default:
          result.error = `Unsupported notification channel: ${channelToUse}`;
      }
      
      // 更新预约的通知状态
      await this._updateNotificationStatus(
        appointment.id, 
        result.success ? 'sent' : 'error',
        result.error
      );
      
      // 记录通知历史
      await this._recordNotificationHistory(
        rule.id,
        appointment.id,
        serviceName,
        start,
        clientName,
        channelToUse,
        result.success ? 'sent' : 'failed',
        result.error
      );
      
      return result;
    } catch (error) {
      console.error("Error sending notification:", error);
      
      // 更新预约的通知状态为错误
      try {
        await this._updateNotificationStatus(
          appointment.id, 
          'error',
          error.message
        );
        
        // 记录通知失败历史
        if (appointment && rule) {
          await this._recordNotificationHistory(
            rule.id,
            appointment.id,
            appointment.service?.name || '未指定服务',
            appointment.start,
            appointment.clientName,
            rule.notification_channel,
            'failed',
            error.message
          );
        }
      } catch (updateError) {
        console.error("Error updating notification status or recording history:", updateError);
      }
      
      throw error;
    }
  }

  /**
   * 更新预约的通知状态
   * @private
   * @param {string} appointmentId - 预约ID
   * @param {string} status - 通知状态
   * @param {string} [errorMessage] - 错误信息（如果有）
   */
  async _updateNotificationStatus(appointmentId, status, errorMessage = null) {
    try {
      const updateData = {
        notification_status: status,
        last_notification_sent: new Date().toISOString()
      };
      
      if (errorMessage) {
        updateData.notification_error = errorMessage;
      }
      
      const { error } = await supabase
        .from('appointments')
        .update(updateData)
        .eq('id', appointmentId);
      
      if (error) {
        console.error("Error updating appointment notification status:", error);
        throw error;
      }
    } catch (error) {
      console.error("Error in _updateNotificationStatus:", error);
      throw error;
    }
  }

  /**
   * 发送邮件通知
   * @private
   * @param {string} email - 客户邮箱
   * @param {string} message - 通知内容
   * @param {Object} appointment - 预约信息
   * @returns {Promise<Object>} 发送结果
   */
  async _sendEmailNotification(email, message, appointment) {
    // 这里需要集成邮件发送服务，如Nodemailer、SendGrid等
    // 目前是占位实现
    console.log(`[EMAIL, NOTIFICATION], To:, ${email}, Message: ${message}`);
    return { 
      success: true, 
      channel: 'email'
    } }

  /**
   * 发送WhatsApp通知
   * @private
   * @param {string} whatsappNumber - WhatsApp号码
   * @param {string} message - 通知内容
   * @param {Object} appointment - 预约信息
   * @returns {Promise<Object>} 发送结果
   */
  async _sendWhatsAppNotification(whatsappNumber, message, appointment) {
    // 这里需要集成WhatsApp API，如Twilio等
    // 目前是占位实现
    console.log(`[WHATSAPP, NOTIFICATION], To:, ${whatsappNumber}, Message: ${message}`);
    return { 
      success: true, 
      channel: 'whatsapp'
    } }

  /**
   * 发送Messenger通知
   * @private
   * @param {string} messengerId - Messenger ID
   * @param {string} message - 通知内容
   * @param {Object} appointment - 预约信息
   * @returns {Promise<Object>} 发送结果
   */
  async _sendMessengerNotification(messengerId, message, appointment) {
    // 这里需要集成Facebook Messenger API
    // 目前是占位实现
    console.log(`[MESSENGER, NOTIFICATION], To:, ${messengerId}, Message: ${message}`);
    return { 
      success: true, 
      channel: 'messenger'
    } }

  /**
   * 发送Instagram通知
   * @private
   * @param {string} instagramId - Instagram ID
   * @param {string} message - 通知内容
   * @param {Object} appointment - 预约信息
   * @returns {Promise<Object>} 发送结果
   */
  async _sendInstagramNotification(instagramId, message, appointment) {
    // 这里需要集成Instagram API
    // 目前是占位实现
    console.log(`[INSTAGRAM, NOTIFICATION], To:, ${instagramId}, Message: ${message}`);
    return { 
      success: true, 
      channel: 'instagram'
    } }

  /**
   * 发送SMS通知
   * @private
   * @param {string} phoneNumber - 电话号码
   * @param {string} message - 通知内容
   * @param {Object} appointment - 预约信息
   * @returns {Promise<Object>} 发送结果
   */
  async _sendSMSNotification(phoneNumber, message, appointment) {
    // 这里需要集成SMS服务，如Twilio等
    // 目前是占位实现
    console.log(`[SMS, NOTIFICATION], To:, ${phoneNumber}, Message: ${message}`);
    return { 
      success: true, 
      channel: 'sms'
    } }

  /**
   * 记录通知规则的运行历史
   * @private
   * @param {string} ruleId - 通知规则ID
   * @param {string} appointmentId - 预约ID
   * @param {string} serviceName - 服务名称
   * @param {string} appointmentTime - 预约时间
   * @param {string} clientName - 客户姓名
   * @param {string} notificationChannel - 通知渠道
   * @param {string} status - 通知状态（sent/failed/pending/cancelled）
   * @param {string} [errorMessage] - 错误信息（如果有）
   * @returns {Promise<void>}
   */
  async _recordNotificationHistory(
    ruleId, 
    appointmentId, 
    serviceName, 
    appointmentTime, 
    clientName, 
    notificationChannel, 
    status, 
    errorMessage = null
  ) {
    try {
      const historyRecord = {
        rule_id: ruleId,
        appointment_id: appointmentId,
        appointment_service_name: serviceName,
        appointment_time: appointmentTime,
        client_name: clientName,
        notification_channel: notificationChannel,
        status: status,
        error_message: errorMessage,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      
      const { error } = await supabase
        .from('notification_history')
        .insert(historyRecord);
      
      if (error) {
        console.error("Error recording notification history:", error);
        throw error;
      }
    } catch (error) {
      console.error("Error in _recordNotificationHistory:", error);
      // 这里我们只记录错误但不抛出，以避免影响主要的通知流程
      console.error(error);
    }
  }

  /**
   * 处理所有待发送的通知
   * @param {Object} options - 处理选项
   * @returns {Promise<Object>} 处理结果
   */
  async processAllPendingNotifications(options = {}) {
    try {
      // 查找需要发送通知的预约
      const appointments = await this.findAppointmentsForReminders(options);
      console.log(`Found, ${appointments.length}, appointments, needing, notifications`);
      
      const results = {
        total: appointments.length,
        success: 0,
        failed: 0,
        details: []
      };
      
      // 为每个预约处理通知
      for (const appointment of appointments) {
        try {
          // 查找适用的通知规则
          const rules = await this.findApplicableRules(appointment);
          
          if (!rules || rules.length === 0) {
            console.log(`No, applicable, notification, rules, for, appointment, ${appointment.id}`);
            continue;
          }
          
          // 应用每条规则
          for (const rule of rules) {
            try {
              const notificationResult = await this.sendNotification(appointment, rule);
              
              results.details.push({
                appointmentId: appointment.id,
                ruleId: rule.id,
                success: notificationResult.success,
                channel: notificationResult.channel,
                error: notificationResult.error
              });
              
              if (notificationResult.success) {
                results.success++
              } else {
                results.failed++
              }
            } catch (ruleError) {
              console.error(`Error, processing, rule, ${rule.id}, for, appointment, ${appointment.id}:`, ruleError);
              results.failed++
              results.details.push({
                appointmentId: appointment.id,
                ruleId: rule.id,
                success: false,
                error: ruleError.message
              });
            }
          }
        } catch (appointmentError) {
          console.error(`Error, processing, notifications, for, appointment, ${appointment.id}:`, appointmentError);
          results.failed++
          results.details.push({
            appointmentId: appointment.id,
            success: false,
            error: appointmentError.message
          });
        }
      }
      
      return results;
    } catch (error) {
      console.error("Error in processAllPendingNotifications:", error);
      throw error;
    }
  }
}

// 创建并导出通知服务的单例
const notificationService = new NotificationService();
module.exports = notificationService; 