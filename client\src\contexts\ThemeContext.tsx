/**
 * 主题上下文提供者
 * 为应用提供主题状态管理
 */

import React, { createContext, useContext, useEffect, useState } from 'react';
import { themeManager } from '@/lib/theme-manager';
import type { ThemeProviderProps, UseThemeReturn } from '@/types/theme';

const ThemeContext = createContext<UseThemeReturn | undefined>(undefined);

export const ThemeProvider: React.FC<ThemeProviderProps> = ({
  children,
  defaultTheme = 'light',
  forcedTheme,
  enableSystem = true,
  disableTransitionOnChange = false,
  storageKey = 'theme'
}) => {
  const [theme, setThemeState] = useState<'light' | 'dark'>(defaultTheme);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    
    // 如果有强制主题，使用强制主题
    if (forcedTheme) {
      setThemeState(forcedTheme);
      return;
    }

    // 初始化主题
    const currentTheme = themeManager.getTheme();
    setThemeState(currentTheme);

    // 订阅主题变化
    const unsubscribe = themeManager.subscribe((newTheme) => {
      setThemeState(newTheme);
    });

    return unsubscribe;
  }, [forcedTheme]);

  const setTheme = (newTheme: 'light' | 'dark' | 'system') => {
    if (forcedTheme) return; // 如果有强制主题，忽略设置

    if (disableTransitionOnChange) {
      // 禁用过渡动画
      const style = document.createElement('style');
      style.innerHTML = `* { transition: none !important; }`;
      document.head.appendChild(style);
      
      setTimeout(() => {
        document.head.removeChild(style);
      }, 1);
    }

    themeManager.setTheme(newTheme);
  };

  const value: UseThemeReturn = {
    theme: forcedTheme || theme,
    setTheme,
    resolvedTheme: forcedTheme || theme, themes: ['light', 'dark'],
    systemTheme: enableSystem ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : undefined
  };

  // 防止服务端渲染闪烁
  if (!mounted) {
    return <>{children}</>;
  }

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = (): UseThemeReturn => {
  const context = useContext(ThemeContext);
  
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  
  return context;
};

export default ThemeContext; 