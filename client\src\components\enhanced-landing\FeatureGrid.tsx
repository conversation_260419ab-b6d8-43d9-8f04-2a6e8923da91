// import React from 'react';
// import { motion } from 'framer-motion';
// import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
// import { Badge } from '@/components/ui/badge';
// import { MessageSquare, BarChart3, Users, Zap, Shield, Settings } from 'lucide-react';
// import { cn } from '@/lib/utils';
// import { Settings2 } from 'lucide-react';

// const features = [
//   {
//     title: '智能对话',
//     description: '先进的AI对话系统，提供自然流畅的客户服务体验',
//     icon: '💬',
//     category: 'AI'
//   },
//   {
//     title: '数据分析',
//     description: '深度数据洞察，帮助您做出更明智的业务决策',
//     icon: '📊',
//     category: 'Analytics'
//   },
//   {
//     title: '用户管理',
//     description: '完整的用户生命周期管理解决方案',
//     icon: '👥',
//     category: 'Management'
//   },
//   {
//     title: '快速响应',
//     description: '毫秒级响应速度，提供极致的用户体验',
//     icon: '⚡',
//     category: 'Performance'
//   },
//   {
//     title: '安全保护',
//     description: '企业级安全防护，保障您的数据安全',
//     icon: '🛡️',
//     category: 'Security'
//   },
//   {
//     title: '自定义配置',
//     description: '灵活的配置选项，满足各种业务需求',
//     icon: '⚙️',
//     category: 'Customization'
//   }
// ];

// ... existing code ... 