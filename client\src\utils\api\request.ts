/**
 * API请求工具函数
 */
import axios, { AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';

// 创建axios实例
const apiClient = axios.create({
  baseURL: process.env.REACT_APP_API_URL || '/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
});

// 添加请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    // 从localStorage获取token并添加到请求头
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 添加响应拦截器
apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  (error: AxiosError) => {
    // 处理401错误（未授权）
    if (error.response?.status === 401) {
      // 可以在这里添加重定向到登录页面的逻辑
      localStorage.removeItem('auth_token');
      window.location.href = '/login';
    }
    
    // 处理500错误（服务器错误）
    if (error.response?.status === 500) {
      console.error('Server error:', error);
    }
    
    return Promise.reject(error);
  }
);

/**
 * 发送GET请求
 * @param url 请求URL
 * @param params 请求参数
 * @param config Axios配置选项
 * @returns Promise包含响应数据
 */
export const get = async <T>(
  url: string,
  params?: Record<string, any>,
  config?: AxiosRequestConfig
): Promise<T> => {
  const response: AxiosResponse<T> = await apiClient.get(url, {
    params,
    ...config
  });
  return response.data;
};

/**
 * 发送POST请求
 * @param url 请求URL
 * @param data 请求体数据
 * @param config Axios配置选项
 * @returns Promise包含响应数据
 */
export const post = async <T>(
  url: string,
  data?: any,
  config?: AxiosRequestConfig
): Promise<T> => {
  const response: AxiosResponse<T> = await apiClient.post(url, data, config);
  return response.data;
};

/**
 * 发送PUT请求
 * @param url 请求URL
 * @param data 请求体数据
 * @param config Axios配置选项
 * @returns Promise包含响应数据
 */
export const put = async <T>(
  url: string,
  data?: any,
  config?: AxiosRequestConfig
): Promise<T> => {
  const response: AxiosResponse<T> = await apiClient.put(url, data, config);
  return response.data;
};

/**
 * 发送DELETE请求
 * @param url 请求URL
 * @param config Axios配置选项
 * @returns Promise包含响应数据
 */
export const del = async <T>(
  url: string,
  config?: AxiosRequestConfig
): Promise<T> => {
  const response: AxiosResponse<T> = await apiClient.delete(url, config);
  return response.data;
};

export default apiClient;