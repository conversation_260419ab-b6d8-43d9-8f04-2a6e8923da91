import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, Card, Form, But<PERSON>, Spinner, Alert } from '../../components/compat/react-bootstrap-compat';
import { useNavigate } from 'react-router-dom';
import api from '../../api/axiosInstance';
import { toast } from 'react-toastify';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { useFeatureGuard } from '../../hooks/useFeatureGuard';

function BrandSettingsPage() {
  useFeatureGuard('free');
  const navigate = useNavigate();
  const [profile, setProfile] = useState({
    brandName: '',
    brandDescription: '',
    targetAudience: '',
    toneAndVoice: '',
    keywords: '',
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState(null);
  const [profileExists, setProfileExists] = useState(false);

  useEffect(() => {
    const fetchProfile = async () => {
      setIsLoading(true);
      setError(null);
      setProfileExists(false);
      try {
        console.log('Fetching brand profile...');
        const response = await api.get('/api/brand-profile');
        console.log('[DEBUG] GET /api/brand-profile response:', response);

        if (response.data && response.data.profile) {
          console.log('Brand profile found:', response.data.profile);
          setProfile({
            brandName: response.data.profile.profile_name || '',
            brandDescription: response.data.profile.brand_description || '',
            targetAudience: response.data.profile.target_audience || '',
            toneAndVoice: (response.data.profile.brand_tone || []).join(', '),
            keywords: (response.data.profile.brand_keywords || []).join(', '),
          });
          setProfileExists(true);
          console.log('[DEBUG] Profile found, setting profileExists = true');
        } else {
          console.log('No existing brand profile data returned (profile is null/undefined).');
          console.log('[DEBUG] Profile not found or null, profileExists remains false');
        }
      } catch (err) {
        console.error('Error fetching brand profile:', err);
        if (err.response && err.response.status === 404) {
          console.log('No existing brand profile found (API returned 404).');
        } else {
          const message = err.response?.data?.message || 'Failed to load brand profile details.';
          setError(message);
          toast.error(message);
        }
      } finally {
        setIsLoading(false);
      }
    };

    fetchProfile();
  }, []);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setProfile(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSaving(true);
    setError(null);

    const apiData = {
      profile_name: profile.brandName,
      brand_description: profile.brandDescription,
      target_audience: profile.targetAudience,
      brand_tone: profile.toneAndVoice ? profile.toneAndVoice.split(',').map(t => t.trim()).filter(t => t !== '') : [],
      brand_keywords: profile.keywords ? profile.keywords.split(',').map(k => k.trim()).filter(k => k !== '') : [],
    };

    try {
      console.log('Saving brand profile with mapped data:', apiData);
      let response;
      console.log('[DEBUG] handleSubmit: profileExists state is:', profileExists);
      if (profileExists) {
        console.log('Updating existing profile (PUT)');
        response = await api.put('/api/brand-profile', apiData);
      } else {
        console.log('Creating new profile (POST)');
        response = await api.post('/api/brand-profile', apiData);
      }
      console.log('Save response:', response.data);
      toast.success(response.data.message || 'Brand profile saved successfully!');
      setProfileExists(true);
    } catch (err) {
      console.error('Error saving brand profile:', err);
      const message = err.response?.data?.message || 'Failed to save brand profile.';
      setError(message);
      toast.error(message);
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <Container className="mt-4">
      <Card>
        <Card.Header as="h5">Brand Profile Settings</Card.Header>
        <Card.Body>
          <Card.Text>
            Define your brand's identity here. This information helps the AI generate content that aligns with your style and goals.
          </Card.Text>
          {isLoading ? (
            <div className="text-center">
              <Spinner animation="border" role="status">
                <span className="visually-hidden">Loading...</span>
              </Spinner>
            </div>
          ) : error && !isSaving ? (
            <Alert variant="danger">Error loading profile: {error}</Alert>
          ) : (
            <Form onSubmit={handleSubmit}>
              <Form.Group className="mb-3" controlId="brandName">
                <Form.Label>Brand Name</Form.Label>
                <Form.Control
                  type="text"
                  name="brandName"
                  placeholder="Your Company or Product Name"
                  value={profile.brandName}
                  onChange={handleChange}
                  disabled={isSaving}
                />
              </Form.Group>

              <Form.Group className="mb-3" controlId="brandDescription">
                <Form.Label>Brand Description</Form.Label>
                <Form.Control
                  as="textarea"
                  rows={3}
                  name="brandDescription"
                  placeholder="What does your brand do? What makes it unique?"
                  value={profile.brandDescription}
                  onChange={handleChange}
                  disabled={isSaving}
                />
              </Form.Group>

              <Form.Group className="mb-3" controlId="targetAudience">
                <Form.Label>Target Audience</Form.Label>
                <Form.Control
                  type="text"
                  name="targetAudience"
                  placeholder="e.g., Small business owners, Developers, Young professionals"
                  value={profile.targetAudience}
                  onChange={handleChange}
                  disabled={isSaving}
                />
              </Form.Group>

              <Form.Group className="mb-3" controlId="toneAndVoice">
                <Form.Label>Tone and Voice</Form.Label>
                <Form.Control
                  type="text"
                  name="toneAndVoice"
                  placeholder="e.g., Friendly, Professional, Witty, Authoritative, Casual"
                  value={profile.toneAndVoice}
                  onChange={handleChange}
                  disabled={isSaving}
                />
                <Form.Text className="text-muted">
                  Describe the desired personality of your brand's communication.
                </Form.Text>
              </Form.Group>

              <Form.Group className="mb-3" controlId="keywords">
                <Form.Label>Keywords</Form.Label>
                <Form.Control
                  type="text"
                  name="keywords"
                  placeholder="e.g., AI, content marketing, social media, productivity"
                  value={profile.keywords}
                  onChange={handleChange}
                  disabled={isSaving}
                />
                <Form.Text className="text-muted">
                  Comma-separated keywords relevant to your brand or topics.
                </Form.Text>
              </Form.Group>

              {error && isSaving && <Alert variant="danger">{error}</Alert>}
              
              <div className="d-flex gap-2 mt-3">
                <Button variant="primary" type="submit" disabled={isSaving || isLoading}>
                  {isSaving ? (
                    <>
                      <Spinner as="span" animation="border" size="sm" role="status" aria-hidden="true" />
                      <span className="ms-2">Saving...</span>
                    </>
                  ) : (profileExists ? 'Update Profile' : 'Save Profile')}
                </Button>
                
                <Button variant="outline-secondary" onClick={() => navigate(-1)} disabled={isSaving || isLoading}>
                    Back
                </Button>
              </div>
            </Form>
          )}
        </Card.Body>
      </Card>
      <ToastContainer position="top-right" autoClose={3000} hideProgressBar={false} closeOnClick />
    </Container>
  );
}

export default BrandSettingsPage; 