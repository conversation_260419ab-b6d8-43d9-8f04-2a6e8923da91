import React, { Component, ReactNode } from 'react';
import { captureError } from '../../lib/sentry';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: any) => void;
}

interface State {
  hasError: boolean;
  error?: Error;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: any) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    
    // 发送错误到 Sentry
    captureError(error, {
      errorInfo,
      componentStack: errorInfo.componentStack,
      errorBoundary: true,
    });

    // 调用自定义错误处理器
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  render() {
    if (this.state.hasError) {
      // 自定义错误UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div className="error-boundary">
          <div className="error-content">
            <h2>🚨 出错了！</h2>
            <p>抱歉，应用程序遇到了一个错误。</p>
            <details style={{ whiteSpace: 'pre-wrap' }}>
              <summary>错误详情</summary>
              {this.state.error && this.state.error.toString()}
            </details>
            <button
              onClick={() => window.location.reload()}
              className="error-retry-button"
            >
              刷新页面
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary; 