import React from "react";
import { cn } from "@/lib/utils";
import { But<PERSON> } from "./button";
import { Card, CardContent } from "./card";
import { FileX, Database, BarChart3, Users, Search, Wifi, AlertCircle } from "lucide-react";

interface EmptyStateProps {
  icon?: React.ComponentType<any>;
  title: string;
  description: string;
  action?: {
    label: string;
    onClick: () => void;
    variant?: "default" | "outline" | "secondary";
  };
  className?: string;
  size?: "sm" | "md" | "lg";
}

const sizeClasses = {
  sm: {
    container: "py-8",
    icon: "w-12 h-12",
    iconContainer: "p-3",
    title: "text-base",
    description: "text-sm"
  },
  md: {
    container: "py-12",
    icon: "w-16 h-16",
    iconContainer: "p-4",
    title: "text-lg",
    description: "text-base"
  },
  lg: {
    container: "py-16",
    icon: "w-20 h-20",
    iconContainer: "p-5",
    title: "text-xl",
    description: "text-lg"
  }
};

export function EmptyState({
  icon: Icon = FileX,
  title,
  description,
  action,
  className,
  size = "md"
}: EmptyStateProps) {
  const sizeConfig = sizeClasses[size];

  return (
    <div className={cn(
      "flex flex-col items-center justify-center text-center",
      sizeConfig.container,
      className
    )}>
      <div className={cn(
        "bg-slate-100 dark:bg-slate-800 rounded-full mb-4 flex items-center justify-center",
        sizeConfig.iconContainer
      )}>
        <Icon className={cn("text-slate-400 dark:text-slate-500", sizeConfig.icon)} />
      </div>
      <h3 className={cn("font-semibold text-slate-900 dark:text-slate-100 mb-2", sizeConfig.title)}>
        {title}
      </h3>
      <p className={cn("text-slate-500 dark:text-slate-400 max-w-sm mb-6", sizeConfig.description)}>
        {description}
      </p>
      {action && (
        <Button
          onClick={action.onClick}
          variant={action.variant || "default"}
          size={size === "sm" ? "sm" : "default"}
        >
          {action.label}
        </Button>
      )}
    </div>
  );
}

export function NoDataState({
  action,
  title = "No Data Available",
  description = "There is no data to display at the moment. Try adjusting your filters or check back later."
}: Partial<EmptyStateProps>) {
  return (
    <EmptyState
      icon={Database}
      title={title}
      description={description}
      action={action}
    />
  );
}

export function NoSearchResultsState({
  searchTerm,
  action
}: {
  searchTerm?: string;
  action?: EmptyStateProps["action"];
}) {
  return (
    <EmptyState
      icon={Search}
      title="No Results Found"
      description={searchTerm 
        ? `No results found for "${searchTerm}". Try adjusting your search terms.`
        : "No results match your current search criteria."
      }
      action={action}
    />
  );
}

export function NoUsersState({ action }: { action?: EmptyStateProps["action"] }) {
  return (
    <EmptyState
      icon={Users}
      title="No Users Found"
      description="No users have been added to your system yet. Start by inviting team members or creating user accounts."
      action={action}
    />
  );
}

export function NoChartsState({ action }: { action?: EmptyStateProps["action"] }) {
  return (
    <EmptyState
      icon={BarChart3}
      title="No Chart Data"
      description="Chart data is currently unavailable. This could be due to insufficient data or a temporary issue."
      action={action}
    />
  );
}

export function ConnectionErrorState({ action }: { action?: EmptyStateProps["action"] }) {
  return (
    <EmptyState
      icon={Wifi}
      title="Connection Error"
      description="Unable to load data due to a connection issue. Please check your network and try again."
      action={action || {
        label: "Retry",
        onClick: () => window.location.reload()
      }}
    />
  );
}

export function ErrorState({
  error,
  action
}: {
  error?: string;
  action?: EmptyStateProps["action"];
}) {
  return (
    <EmptyState
      icon={AlertCircle}
      title="Something Went Wrong"
      description={error || "An unexpected error occurred. Please try again or contact support if the problem persists."}
      action={action || {
        label: "Try Again",
        onClick: () => window.location.reload()
      }}
    />
  );
}

export function EmptyStateCard({
  children,
  className
}: {
  children: React.ReactNode;
  className?: string;
}) {
  return (
    <Card className={cn("border-dashed border-2 border-slate-200 dark:border-slate-700", className)}>
      <CardContent className="p-0">
        {children}
      </CardContent>
    </Card>
  );
}
