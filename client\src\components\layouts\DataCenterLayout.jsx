import React from 'react';
import { Outlet } from 'react-router-dom';
import Sidebar from './Sidebar';

const DataCenterLayout = () => {
  return (
    <div className="flex h-screen bg-gray-50">
      <Sidebar />
      <div className="flex-1 flex flex-col overflow-hidden">
        <header className="bg-white shadow-sm border-b border-gray-200">
          <div className="px-6 py-4">
            <h1 className="text-2xl font-semibold text-gray-900">数据中心</h1>
            <p className="text-sm text-gray-600 mt-1">查看和分析您的业务数据</p>
          </div>
        </header>
        
        <main className="flex-1 overflow-y-auto">
          <div className="p-6">
            <Outlet />
          </div>
        </main>
      </div>
    </div>
  );
};

export default DataCenterLayout;
