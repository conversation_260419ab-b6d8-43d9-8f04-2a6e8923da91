/**
 * 🔧 优化版Platform API服务
 * 
 * 🎯 优化亮点:
 * - 统一错误处理机制
 * - 类型安全和接口规范
 * - 请求拦截和响应处理
 * - 缓存和重试机制
 * - 开发模式支持
 * - 详细的调试日志
 */

import axios, { AxiosInstance, AxiosResponse, AxiosError } from 'axios'

// 🏗️ 环境配置
const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:3001'
const API_TIMEOUT = 30000; // 30秒超时
const MAX_RETRIES = 3;

// 📋 类型定义 - 增强版
export interface PlatformStatus {
  id: string;
  name: string;
  displayName?: string;
  description: string;
  category: 'Social' | 'Communication' | 'E-commerce' | 'Productivity'
  status: 'Connected' | 'Not Connected' | 'Requires Attention' | 'Error'
  logo?: React.ReactElement;
  type?: any;
  isActive?: boolean;
  isPopular?: boolean;
  supportedFeatures?: any[];
  requiredCredentials?: any[];
  connectedAt?: string;
  lastSyncAt?: string;
  accountInfo?: {
    id: string;
    name: string;
    email?: string;
    avatar?: string
};
  webhookUrl?: string;
  manageUrl?: string;
  errorDetails?: {
    code: string;
    message: string;
    timestamp: string
};
  metadata?: Record<string, any>
};

export interface ConnectionResult {
  success: boolean;
  message: string;
  platform: string;
  authUrl?: string;
  accountInfo?: {
    id: string;
    name: string;
    email?: string;
    connectedAt: string
};
  error?: {
    code: string;
    details?: any
} };

export interface ApiError {
  code: string;
  message: string;
  details?: any;
  timestamp: string;
  platform?: string
};

export interface WebhookConfig {
  url: string;
  events: string[];
  secret?: string;
  isActive?: boolean
};

export interface PlatformAnalytics {
  platform: string;
  timeRange: string;
  metrics: {
    connections: number;
    messages: number;
    errors: number;
    uptime: number
};
  trends?: Record<string, number[]>
};

// 🔧 优化版Platform API服务类
class OptimizedPlatformApiService {
  private client: AxiosInstance;
  private token: string | null = null;
  private cache: Map<string, { data: any; timestamp: number }> = new Map();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5分钟缓存

  constructor() {
    // 初始化token
    this.token = localStorage.getItem('authToken');

    // 创建优化的axios实例
    this.client = axios.create({
      baseURL: API_BASE_URL, // 修复：移除重复的/api前缀
      timeout: API_TIMEOUT,
      headers: {
        'Content-Type': 'application/json'
      }
    });

    // 设置请求拦截器
    this.setupInterceptors()
};

  // 🔒 设置拦截器
  private setupInterceptors(): void {
    // 请求拦截器
    this.client.interceptors.request.use(
      (config) => {
        // 动态添加认证头
        const token = localStorage.getItem('authToken');
        
        if (token) {
          config.headers.Authorization = `Bearer ${token}`
} else if (process.env.NODE_ENV === 'development') {
          // 开发模式下使用默认token
          config.headers.Authorization = 'Bearer dev-token'
          config.headers['X-Development'] = 'true'
        };

        // 调试日志
        console.log(`🚀 [API] ${config.method?.toUpperCase()} ${config.url}`, {
          headers: config.headers,
          data: config.data });

        return config;
      },
      (error) => {
        console.error('🚨 请求拦截器错误:', error);
        return Promise.reject(error);
      }
    );

    // 响应拦截器
    this.client.interceptors.response.use(
      (response: AxiosResponse) => {
        console.log(`✅ [API] ${response.status} ${response.config.url}`, response.data);
        return response
},
      (error: AxiosError) => {
        return this.handleApiError(error)
}
    )
};

  // 🚨 统一错误处理
  private handleApiError(error: AxiosError): Promise<never> {
    const apiError: ApiError = {
      code: 'UNKNOWN_ERROR',
      message: '未知错误',
      timestamp: new Date().toISOString()
};

    if (error.response) {
      // 服务器响应错误
      const { status, data } = error.response;
      apiError.code = `HTTP_${status}`;
      apiError.message = (data as any)?.message || error.message;
      apiError.details = data;

      console.error(`🚨 [API ERROR] ${status}:`, {
        url: error.config?.url,
        method: error.config?.method,
        data: error.config?.data,
        response: data
      });

      // 特殊错误处理
      switch (status) {
        case 401: // 认证失效，清除token
          localStorage.removeItem('authToken');
          window.location.href = '/login';
          break;
        case 404:
          apiError.message = '请求的资源不存在或服务暂不可用';
          break;
        case 422:
          apiError.message = '平台配置错误，请检查配置信息';
          break;
        case 429:
          apiError.message = '请求过于频繁，请稍后再试';
          break;
        case 500:
          apiError.message = '服务器内部错误，请稍后再试';
          break
}
    } else if (error.request) {
      // 网络错误
      apiError.code = 'NETWORK_ERROR';
      apiError.message = '网络连接失败，请检查网络连接';
      console.error('🚨 [NETWORK ERROR]:', error.request)
} else {
      // 其他错误
      apiError.message = error.message;
      console.error('🚨 [REQUEST ERROR]:', error.message)
}

    return Promise.reject(apiError)
}

  // 📦 缓存管理
  private getCachedData<T>(key: string): T | null {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
      console.log(`📦 [CACHE HIT] ${key}`);
      return cached.data as T
}
    return null
}

  private setCachedData(key: string, data: any): void {
    this.cache.set(key, { data, timestamp: Date.now() });
    console.log(`📦 [CACHE SET] ${key}`)
}

  // 🔄 重试机制
  private async retryRequest<T>(
    requestFn: () => Promise<T>,
    retries: number = MAX_RETRIES
  ): Promise<T> {
    try {
      return await requestFn()
} catch (error) {
      if (retries > 0 && this.shouldRetry(error as ApiError)) {
        console.log(`🔄 [RETRY] 剩余重试次数: ${retries}`);
        await new Promise(resolve => setTimeout(resolve, 1000)); // 1秒延迟
        return this.retryRequest(requestFn, retries - 1)
}
      throw error
}
  }

  private shouldRetry(error: ApiError): boolean {
    // 网络错误或服务器错误可以重试
    return error.code === 'NETWORK_ERROR' || error.code.startsWith('HTTP_5') || error.code === 'HTTP_408'; // Request Timeout
  }

  // 🌟 API方法 - 优化版

  /**
   * 获取所有平台状态
   */
  async getAllPlatforms(useCache: boolean = true): Promise<PlatformStatus[]> {
    const cacheKey = 'platforms-all';
    
    if (useCache) {
      const cached = this.getCachedData<PlatformStatus[]>(cacheKey);
      if (cached) return cached
}

    return this.retryRequest(async () => {
      const response = await this.client.get<PlatformStatus[]>('/api/platforms');
      const platforms = response.data;
      
      if (useCache) {
        this.setCachedData(cacheKey, platforms)
}
      
      return platforms
})
}

  /**
   * 获取平台连接状态
   */
  async getPlatformStatus(useCache: boolean = true): Promise<Record<string, boolean>> {
    const cacheKey = 'platform-status';
    
    if (useCache) {
      const cached = this.getCachedData<Record<string, boolean>>(cacheKey);
      if (cached) return cached
}

    return this.retryRequest(async () => {
      const response = await this.client.get<Record<string, boolean>>('/api/platforms/status');
      const status = response.data;
      
      if (useCache) {
        this.setCachedData(cacheKey, status)
}
      
      return status
})
}

  /**
   * 连接平台
   */
  async connectPlatform(platformId: string, options?: { force?: boolean; }): Promise<ConnectionResult> {
    // 清除相关缓存
    this.cache.delete('platforms-all');
    this.cache.delete('platform-status');
    this.cache.delete(`platform_${platformId}`);

    return this.retryRequest(async () => {
      const response = await this.client.post<ConnectionResult>(
        `/api/platforms/${platformId}/connect`,
        { options }
      );
      return response.data
})
};

  /**
   * 断开平台连接
   */
  async disconnectPlatform(platformId: string): Promise<{ success: boolean; message: string }> {
    // 清除相关缓存
    this.cache.delete('platforms-all');
    this.cache.delete('platform-status');
    this.cache.delete(`platform_${platformId}`);

    return this.retryRequest(async () => {
      const response = await this.client.post<{ success: boolean; message: string }>(
        `/api/platforms/${platformId}/disconnect`
      );
      return response.data
})
};

  /**
   * 获取平台账户信息
   */
  async getPlatformAccountInfo(platformId: string, useCache: boolean = true): Promise<any> {
    const cacheKey = `account-${platformId}`;
    
    if (useCache) {
      const cached = this.getCachedData(cacheKey);
      if (cached) return cached
}

    return this.retryRequest(async () => {
      const response = await this.client.get(`/api/platforms/${platformId}/account`);
      const accountInfo = response.data;
      
      if (useCache) {
        this.setCachedData(cacheKey, accountInfo)
}
      
      return accountInfo
})
};

  /**
   * 获取OAuth授权URL
   */
  async getAuthUrl(platformId: string, options?: { redirectUrl?: string; }): Promise<{ authUrl: string; state?: string; }> {
    return this.retryRequest(async () => {
      const response = await this.client.post<{ authUrl: string; state?: string; }>(
        `/api/platforms/${platformId}/auth-url`,
        { params: options }
      );
      return response.data
})
};

  /**
   * 配置Webhook
   */
  async configureWebhook(
    platformId: string,
    webhookConfig: WebhookConfig
  ): Promise<{ success: boolean; webhookId: string }> {
    return this.retryRequest(async () => {
      const response = await this.client.post<{ success: boolean; webhookId: string }>(
        `/api/platforms/${platformId}/webhook`,
        webhookConfig
      );
      return response.data
})
};

  /**
   * 测试Webhook连接
   */
  async testWebhook(
    platformId: string,
    webhookId: string
  ): Promise<{ success: boolean; response: any }> {
    return this.retryRequest(async () => {
      const response = await this.client.post<{ success: boolean; response: any }>(
        `/api/platforms/${platformId}/webhook/${webhookId}/test`
      );
      return response.data
})
};

  /**
   * 获取实时事件流 (SSE) - 优化版
   */
  createEventStream(
    platformId: string,
    onEvent: (event: any) => void,
    onError?: (error: any) => void
  ): EventSource | null {
    if (!window.EventSource) {
      console.warn('🚨 浏览器不支持Server-Sent Events');
      return null
};

    const token = localStorage.getItem('authToken');
    const url = `${API_BASE_URL}/api/platforms/${platformId}/events${token ? `?token=${token}` : ''}`;
    
    console.log(`🔗 [SSE] 连接事件流: ${url}`);
    
    const eventSource = new EventSource(url);

    eventSource.onopen = () => {
      console.log(`✅ [SSE] 事件流已连接: ${platformId}`)
};

    eventSource.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        console.log(`📨 [SSE] 收到事件:`, data);
        onEvent(data)
} catch (error) {
        console.error('🚨 [SSE] 解析事件数据失败:', error);
        onError?.(error)
} };

    eventSource.onerror = (error) => {
      console.error(`🚨 [SSE] 事件流错误 (${platformId}):`, error);
      onError?.(error)
};

    return eventSource
};

  /**
   * 获取平台分析数据
   */
  async getPlatformAnalytics(
    platformId: string,
    timeRange: '24h' | '7d' | '30d' = '24h',
    useCache: boolean = true
  ): Promise<PlatformAnalytics> {
    const cacheKey = `analytics-${platformId}-${timeRange}`;
    
    if (useCache) {
      const cached = this.getCachedData<PlatformAnalytics>(cacheKey);
      if (cached) return cached
};

    return this.retryRequest(async () => {
      const response = await this.client.get<PlatformAnalytics>(
        `/api/platforms/${platformId}/analytics`,
        { params: { timeRange } }
      );
      const analytics = response.data;
      
      if (useCache) {
        // 分析数据缓存时间更短
        this.cache.set(cacheKey, { data: analytics, timestamp: Date.now() })
};
      
      return analytics
})
};

  // 🔧 工具方法

  /**
   * 健康检查
   */
  async healthCheck(): Promise<{ status: string; timestamp: string; services: Record<string, boolean> }> {
    return this.retryRequest(async () => {
      const response = await this.client.get('/api/health');
      return response.data
})
};

  /**
   * 清除所有缓存
   */
  clearCache(): void {
    this.cache.clear();
    console.log('🗑️ [CACHE] 所有缓存已清除')
};

  /**
   * 更新认证token
   */
  updateToken(token: string | null): void {
    this.token = token;
    if (token) {
      localStorage.setItem('authToken', token)
} else {
      localStorage.removeItem('authToken')
};
    console.log(`🔐 [AUTH] Token已${token ? '更新' : '清除'}`)
};

  /**
   * 获取调试信息
   */
  getDebugInfo(): {
    baseUrl: string;
    cacheSize: number;
    hasToken: boolean;
    timestamp: string
} {
    return {
      baseUrl: API_BASE_URL,
      cacheSize: this.cache.size,
      hasToken: !!this.token,
      timestamp: new Date().toISOString()
}
}
};

// 🎯 导出单例实例
export const platformApiService = new OptimizedPlatformApiService();
export default platformApiService;