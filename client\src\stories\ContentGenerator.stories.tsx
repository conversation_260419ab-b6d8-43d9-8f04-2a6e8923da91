import type { Meta, StoryObj } from '@storybook/react';
import { rest } from 'msw';
import { within, userEvent, expect } from '@storybook/test';
import ContentGeneratorPage from '../pages/modules/ContentGeneratorPage';

// 模拟路由相关组件
const withRouter = (Story: React.ComponentType) => {
  return (
    <div>
      <Story />
    </div>
  );
};

const meta = {
  title: 'Modules/ContentGenerator',
  component: ContentGeneratorPage,
  decorators: [withRouter],
  parameters: {
    layout: 'fullscreen',
    msw: {
      handlers: [
        // 生成内容
        rest.post('/api/content-generator/generate', (req, res, ctx) => {
          // 确保req.body是一个对象类型
          const body = typeof req.body === 'object' ? req.body || {} : {};
          
          return res(
            ctx.delay(1000),
            ctx.json({
              id: 'gen_123456',
              content: '这是通过Storybook生成的示例内容。基于提示: ' + (body.prompt || ''),
              prompt: body.prompt || '默认提示',
              timestamp: new Date().toISOString(),
              metadata: {
                model: 'gpt-4o-mini',
                tokensUsed: 150,
                processingTime: '1.2s'
              }
            })
          );
        }),
        
        // 获取模板
        rest.get('/api/content-generator/templates', (req, res, ctx) => {
          return res(ctx.json([
            {
              id: 'template_1',
              name: '销售邮件',
              description: '用于发送产品促销邮件',
              prompt: '写一封推广{{产品}}的邮件，针对{{目标客户}}，强调{{优势}}',
              createdAt: '2023-01-15T12:00:00Z',
              updatedAt: '2023-04-18T09:30:00Z'
            },
            {
              id: 'template_2',
              name: '博客文章',
              description: '为公司博客创建新文章',
              prompt: '写一篇关于{{主题}}的博客文章，包含{{关键点}}，目标读者是{{读者}}',
              createdAt: '2023-02-20T15:45:00Z',
              updatedAt: '2023-02-20T15:45:00Z'
            }
          ]));
        }),
        
        // 获取历史
        rest.get('/api/content-generator/history', (req, res, ctx) => {
          return res(ctx.json([
            {
              id: 'hist_1',
              prompt: '写一封营销邮件',
              content: '这是一封针对新产品的营销邮件...',
              timestamp: '2023-05-10T14:30:00Z',
              model: 'gpt-4o-mini'
            },
            {
              id: 'hist_2',
              prompt: '生成产品描述',
              content: '这款创新产品提供了独特的解决方案...',
              timestamp: '2023-05-09T11:15:00Z',
              model: 'gpt-4o-mini'
            }
          ]));
        })
      ]
    }
  }
} satisfies Meta<typeof ContentGeneratorPage>;

export default meta;
type Story = StoryObj<typeof meta>;

// 基本状态
export const Default: Story = {};

// 生成内容
export const GenerateContent: Story = {
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    
    // 输入提示
    const promptInput = canvas.getByRole('textbox', { name: /prompt/i });
    await userEvent.type(promptInput, '写一篇关于AI的短文');
    
    // 点击生成按钮
    const generateButton = canvas.getByRole('button', { name: /generate/i });
    await userEvent.click(generateButton);
    
    // 等待生成完成
    await expect(canvas.getByText('这是通过Storybook生成的示例内容')).toBeInTheDocument();
  }
};

// 查看历史
export const ViewHistory: Story = {
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    
    // 切换到历史标签
    const historyTab = canvas.getByRole('tab', { name: /history/i });
    await userEvent.click(historyTab);
    
    // 验证历史条目
    await expect(canvas.getByText('写一封营销邮件')).toBeInTheDocument();
    await expect(canvas.getByText('生成产品描述')).toBeInTheDocument();
  }
};

// 查看模板
export const ViewTemplates: Story = {
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    
    // 切换到模板标签
    const templatesTab = canvas.getByRole('tab', { name: /templates/i });
    await userEvent.click(templatesTab);
    
    // 验证模板条目
    await expect(canvas.getByText('销售邮件')).toBeInTheDocument();
    await expect(canvas.getByText('博客文章')).toBeInTheDocument();
  }
}; 