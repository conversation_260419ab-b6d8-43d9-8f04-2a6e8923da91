// Enhanced Data Insight Components
export { EnhancedKPICard, type KPIData } from './EnhancedKPICard';
export { Interactive<PERSON>hart, type ChartConfig, type ChartDataPoint, type ChartSeries } from './InteractiveChart';
export { FilterPanel, type FilterField, type FilterValue, type FilterOption, type SavedFilter } from './FilterPanel';

// Enhanced Lead Generation Insights
export { default as EnhancedLeadGenerationInsights } from '../../datainsights/EnhancedLeadGenerationInsights';

// Theme and utilities
export { 
  DATA_INSIGHT_THEME, 
  getChartTheme, 
  getTabTheme, 
  getCardTheme, 
  getButtonTheme,
  getChartColor,
  getStatusColor,
  getTemperatureColor,
  responsive,
  getMultipleChartColors
  // getGradientByValue
} from '@/theme/data-insight-theme';

// Hooks (commented out as they may not exist yet)
// export { useDataInsight, useDataInsightPerformance, type DataInsightConfig, type DataInsightState, type DataInsightActions } from '@/hooks/useDataInsight'; 