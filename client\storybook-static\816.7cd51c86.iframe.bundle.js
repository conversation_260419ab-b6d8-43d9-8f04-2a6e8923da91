"use strict";(self.webpackChunkclient=self.webpackChunkclient||[]).push([[816],{"./node_modules/@babel/runtime/helpers/esm/extends.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{function _extends(){return _extends=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},_extends.apply(null,arguments)}__webpack_require__.d(__webpack_exports__,{A:()=>_extends})},"./node_modules/@restart/ui/esm/Anchor.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.d(__webpack_exports__,{A:()=>esm_Anchor});var react=__webpack_require__("./node_modules/react/index.js"),useEventCallback=(__webpack_require__("./node_modules/@restart/ui/node_modules/@restart/hooks/esm/useCallbackRef.js"),__webpack_require__("./node_modules/@restart/ui/node_modules/@restart/hooks/esm/useCommittedRef.js"),__webpack_require__("./node_modules/@restart/ui/node_modules/@restart/hooks/esm/useEventCallback.js"));__webpack_require__("./node_modules/@restart/ui/node_modules/@restart/hooks/esm/useEventListener.js");__webpack_require__("./node_modules/@restart/ui/node_modules/@restart/hooks/esm/useMounted.js"),__webpack_require__("./node_modules/@restart/ui/node_modules/@restart/hooks/esm/usePrevious.js");__webpack_require__("./node_modules/@restart/ui/node_modules/@restart/hooks/esm/useIsomorphicEffect.js");new WeakMap;var Button=__webpack_require__("./node_modules/@restart/ui/esm/Button.js"),jsx_runtime=__webpack_require__("./node_modules/react/jsx-runtime.js");const _excluded=["onKeyDown"];const Anchor=react.forwardRef(((_ref,ref)=>{let{onKeyDown}=_ref,props=function _objectWithoutPropertiesLoose(r,e){if(null==r)return{};var t={};for(var n in r)if({}.hasOwnProperty.call(r,n)){if(e.indexOf(n)>=0)continue;t[n]=r[n]}return t}(_ref,_excluded);const[buttonProps]=(0,Button.Am)(Object.assign({tagName:"a"},props)),handleKeyDown=(0,useEventCallback.A)((e=>{buttonProps.onKeyDown(e),null==onKeyDown||onKeyDown(e)}));return function isTrivialHref(href){return!href||"#"===href.trim()}(props.href)||"button"===props.role?(0,jsx_runtime.jsx)("a",Object.assign({ref},props,buttonProps,{onKeyDown:handleKeyDown})):(0,jsx_runtime.jsx)("a",Object.assign({ref},props,{onKeyDown}))}));Anchor.displayName="Anchor";const esm_Anchor=Anchor},"./node_modules/@restart/ui/node_modules/@restart/hooks/esm/useCallbackRef.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.d(__webpack_exports__,{A:()=>useCallbackRef});var react__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("./node_modules/react/index.js");function useCallbackRef(){return(0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null)}},"./node_modules/@restart/ui/node_modules/@restart/hooks/esm/useEventListener.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.d(__webpack_exports__,{A:()=>useEventListener});var react__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("./node_modules/react/index.js"),_useEventCallback__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__("./node_modules/@restart/ui/node_modules/@restart/hooks/esm/useEventCallback.js");function useEventListener(eventTarget,event,listener,capture=!1){const handler=(0,_useEventCallback__WEBPACK_IMPORTED_MODULE_1__.A)(listener);(0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)((()=>{const target="function"==typeof eventTarget?eventTarget():eventTarget;return target.addEventListener(event,handler,capture),()=>target.removeEventListener(event,handler,capture)}),[eventTarget])}},"./node_modules/invariant/browser.js":module=>{module.exports=function(condition,format,a,b,c,d,e,f){if(!condition){var error;if(void 0===format)error=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var args=[a,b,c,d,e,f],argIndex=0;(error=new Error(format.replace(/%s/g,(function(){return args[argIndex++]})))).name="Invariant Violation"}throw error.framesToPop=1,error}}},"./node_modules/uncontrollable/lib/esm/index.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.d(__webpack_exports__,{Zw:()=>useUncontrolled,iC:()=>useUncontrolledProp});var esm_extends=__webpack_require__("./node_modules/@babel/runtime/helpers/esm/extends.js"),objectWithoutPropertiesLoose=__webpack_require__("./node_modules/@babel/runtime/helpers/esm/objectWithoutPropertiesLoose.js"),react=__webpack_require__("./node_modules/react/index.js");__webpack_require__("./node_modules/invariant/browser.js");function defaultKey(key){return"default"+key.charAt(0).toUpperCase()+key.substr(1)}function _toPropertyKey(arg){var key=function _toPrimitive(input,hint){if("object"!=typeof input||null===input)return input;var prim=input[Symbol.toPrimitive];if(void 0!==prim){var res=prim.call(input,hint||"default");if("object"!=typeof res)return res;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===hint?String:Number)(input)}(arg,"string");return"symbol"==typeof key?key:String(key)}function useUncontrolledProp(propValue,defaultValue,handler){var wasPropRef=(0,react.useRef)(void 0!==propValue),_useState=(0,react.useState)(defaultValue),stateValue=_useState[0],setState=_useState[1],isProp=void 0!==propValue,wasProp=wasPropRef.current;return wasPropRef.current=isProp,!isProp&&wasProp&&stateValue!==defaultValue&&setState(defaultValue),[isProp?propValue:stateValue,(0,react.useCallback)((function(value){for(var _len=arguments.length,args=new Array(_len>1?_len-1:0),_key=1;_key<_len;_key++)args[_key-1]=arguments[_key];handler&&handler.apply(void 0,[value].concat(args)),setState(value)}),[handler])]}function useUncontrolled(props,config){return Object.keys(config).reduce((function(result,fieldName){var _extends2,_ref=result,defaultValue=_ref[defaultKey(fieldName)],propsValue=_ref[fieldName],rest=(0,objectWithoutPropertiesLoose.A)(_ref,[defaultKey(fieldName),fieldName].map(_toPropertyKey)),handlerName=config[fieldName],_useUncontrolledProp=useUncontrolledProp(propsValue,defaultValue,props[handlerName]),value=_useUncontrolledProp[0],handler=_useUncontrolledProp[1];return(0,esm_extends.A)({},rest,((_extends2={})[fieldName]=value,_extends2[handlerName]=handler,_extends2))}),props)}function componentWillMount(){var state=this.constructor.getDerivedStateFromProps(this.props,this.state);null!=state&&this.setState(state)}function componentWillReceiveProps(nextProps){this.setState(function updater(prevState){var state=this.constructor.getDerivedStateFromProps(nextProps,prevState);return null!=state?state:null}.bind(this))}function componentWillUpdate(nextProps,nextState){try{var prevProps=this.props,prevState=this.state;this.props=nextProps,this.state=nextState,this.__reactInternalSnapshotFlag=!0,this.__reactInternalSnapshot=this.getSnapshotBeforeUpdate(prevProps,prevState)}finally{this.props=prevProps,this.state=prevState}}componentWillMount.__suppressDeprecationWarning=!0,componentWillReceiveProps.__suppressDeprecationWarning=!0,componentWillUpdate.__suppressDeprecationWarning=!0}}]);