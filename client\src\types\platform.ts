// Platform Management System Types
// 平台管理系统完整类型定义

export enum PlatformType {
  MESSAGING = 'messaging',
  SOCIAL_MEDIA = 'social_media',
  EMAIL = 'email',
  ECOMMERCE = 'ecommerce',
  CRM = 'crm',
  VOICE = 'voice',
  CUSTOM = 'custom'
}

export enum PlatformCategory {
  COMMUNICATION = 'communication',
  MARKETING = 'marketing',
  SALES = 'sales',
  CUSTOMER_SERVICE = 'customer_service',
  ANALYTICS = 'analytics',
  PRODUCTIVITY = 'productivity'
}

// Base Platform Definition
export interface Platform {
  id: string;
  name: string;
  displayName: string;
  description: string;
  type: PlatformType;
  category: PlatformCategory | 'Social' | 'Communication' | 'E-commerce' | 'Productivity';
  status?: 'Connected' | 'Not Connected' | 'Requires Attention' | 'Error';
  logo?: React.ReactElement;
  iconUrl?: string;
  logoUrl?: string;
  websiteUrl?: string;
  documentationUrl?: string;
  manageUrl?: string;
  isActive: boolean;
  isPopular: boolean;
  supportedFeatures: PlatformFeature[];
  requiredCredentials: CredentialRequirement[];
  rateLimit?: RateLimit;
  metadata: PlatformMetadata;
}

// Platform Features
export interface PlatformFeature {
  id: string;
  name: string;
  description: string;
  isSupported: boolean;
  limitations?: string[];
  requiresPremium?: boolean;
}

// Credential Requirements
export interface CredentialRequirement {
  key: string;
  name: string;
  description: string;
  type: 'text' | 'password' | 'token' | 'url' | 'json' | 'file';
  required: boolean;
  placeholder?: string;
  validationRegex?: string;
  helpText?: string;
  securityLevel: 'low' | 'medium' | 'high' | 'critical';
}

// Rate Limiting
export interface RateLimit {
  requestsPerMinute: number;
  requestsPerHour: number;
  requestsPerDay: number;
  burstLimit?: number;
  resetStrategy: 'fixed_window' | 'sliding_window' | 'token_bucket';
}

// Platform Metadata
export interface PlatformMetadata {
  version: string;
  lastUpdated: Date;
  supportedRegions: string[];
  pricing: PlatformPricing;
  compliance: ComplianceInfo;
  technicalSpecs: TechnicalSpecs;
}

export interface PlatformPricing {
  model: 'free' | 'freemium' | 'subscription' | 'usage_based' | 'enterprise';
  startingPrice?: number;
  currency?: string;
  billingCycle?: 'monthly' | 'yearly' | 'per_request';
  hasFreeTier: boolean;
}

export interface ComplianceInfo {
  gdprCompliant: boolean;
  soc2Certified: boolean;
  iso27001Certified: boolean;
  hipaaCompliant?: boolean;
  dataResidency: string[];
}

export interface TechnicalSpecs {
  apiVersion: string;
  authMethods: string[];
  dataFormats: string[];
  webhookSupport: boolean;
  batchOperations: boolean;
  realTimeSupport: boolean;
}

// User Platform Connection
export interface UserPlatformConnection {
  id: string;
  userId: string;
  platformId: string;
  platform: Platform;
  connectionName: string;
  isActive: boolean;
  status: ConnectionStatus;
  credentials: PlatformCredentials;
  configuration: UserPlatformConfiguration;
  permissions: PlatformPermissions;
  usageStats: UsageStatistics;
  healthMetrics: ConnectionHealthMetrics;
  createdAt: Date;
  updatedAt: Date;
  lastUsed: Date;
  expiresAt?: Date;
}

export enum ConnectionStatus {
  CONNECTED = 'connected',
  CONNECTING = 'connecting',
  DISCONNECTED = 'disconnected',
  ERROR = 'error',
  EXPIRED = 'expired',
  SUSPENDED = 'suspended',
  PENDING_APPROVAL = 'pending_approval'
}

// Platform Credentials (Encrypted)
export interface PlatformCredentials {
  id: string;
  platformId: string;
  credentialData: Record<string, any>; // Encrypted credential values
  isValid: boolean;
  lastValidated: Date;
  validationErrors?: string[];
  expiresAt?: Date;
  autoRefresh: boolean;
}

// User Platform Configuration
export interface UserPlatformConfiguration {
  defaultSettings: Record<string, any>;
  customFields: Record<string, any>;
  notificationPreferences: NotificationPreferences;
  operationalLimits: OperationalLimits;
  securitySettings: SecuritySettings;
}

export interface NotificationPreferences {
  onConnectionError: boolean;
  onRateLimitReached: boolean;
  onQuotaWarning: boolean;
  onSecurityAlert: boolean;
  emailNotifications: boolean;
  slackNotifications: boolean;
  webhookNotifications: boolean;
}

export interface OperationalLimits {
  maxRequestsPerHour: number;
  maxConcurrentConnections: number;
  dataRetentionDays: number;
  allowBulkOperations: boolean;
  enableCaching: boolean;
  cacheExpiryMinutes: number;
}

export interface SecuritySettings {
  ipWhitelist: string[];
  requireTwoFactor: boolean;
  sessionTimeout: number;
  encryptionLevel: 'standard' | 'enhanced' | 'maximum';
  auditLogging: boolean;
  accessRestrictions: string[];
}

// Platform Permissions
export interface PlatformPermissions {
  read: boolean;
  write: boolean;
  delete: boolean;
  admin: boolean;
  scopes: string[];
  restrictions: string[];
  grantedAt: Date;
  grantedBy: string;
}

// Usage Statistics
export interface UsageStatistics {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  dataTransferred: number;
  quotaUsage: QuotaUsage;
  hourlyDistribution: number[];
  errorBreakdown: Record<string, number>;
  lastReset: Date;
}

export interface QuotaUsage {
  used: number;
  limit: number;
  percentage: number;
  resetDate: Date;
  isNearLimit: boolean;
  warningThreshold: number;
}

// Health Metrics
export interface ConnectionHealthMetrics {
  overallHealth: HealthScore;
  connectivity: ConnectivityHealth;
  performance: PerformanceHealth;
  reliability: ReliabilityHealth;
  security: SecurityHealth;
  lastHealthCheck: Date;
  healthTrend: 'improving' | 'stable' | 'declining';
}

export interface HealthScore {
  score: number; // 0-100
  grade: 'A' | 'B' | 'C' | 'D' | 'F';
  status: 'excellent' | 'good' | 'fair' | 'poor' | 'critical';
  factors: HealthFactor[];
}

export interface HealthFactor {
  name: string;
  impact: 'positive' | 'neutral' | 'negative';
  weight: number;
  description: string;
}

export interface ConnectivityHealth {
  isReachable: boolean;
  latency: number;
  uptime: number;
  lastOutage?: Date;
  outageCount: number;
}

export interface PerformanceHealth {
  responseTime: number;
  throughput: number;
  errorRate: number;
  timeoutRate: number;
}

export interface ReliabilityHealth {
  successRate: number;
  consistencyScore: number;
  predictabilityScore: number;
  recoveryTime: number;
}

export interface SecurityHealth {
  certificateStatus: 'valid' | 'expiring' | 'expired' | 'invalid';
  encryptionStrength: number;
  vulnerabilityCount: number;
  lastSecurityScan: Date;
}

// Platform Analytics
export interface PlatformAnalytics {
  platformId: string;
  timeRange: TimeRange;
  usageMetrics: PlatformUsageMetrics;
  performanceMetrics: PlatformPerformanceMetrics;
  errorAnalysis: ErrorAnalysis;
  userEngagement: UserEngagementMetrics;
  costAnalysis: CostAnalysis;
}

export interface PlatformUsageMetrics {
  totalConnections: number;
  activeConnections: number;
  requestVolume: number;
  dataVolume: number;
  featureUsage: Record<string, number>;
  geographicDistribution: Record<string, number>;
}

export interface PlatformPerformanceMetrics {
  averageResponseTime: number;
  throughputPerSecond: number;
  availabilityPercentage: number;
  errorRates: Record<string, number>;
  performanceTrends: TrendData[];
}

export interface ErrorAnalysis {
  totalErrors: number;
  errorTypes: Record<string, number>;
  errorTrends: TrendData[];
  topErrors: Array<{
    error: string;
    count: number;
    impact: 'low' | 'medium' | 'high' | 'critical';
  }>;
  resolutionTimes: number[];
}

export interface UserEngagementMetrics {
  activeUsers: number;
  newUsers: number;
  retentionRate: number;
  engagementScore: number;
  featureAdoption: Record<string, number>;
  userSatisfaction: number;
}

export interface CostAnalysis {
  totalCost: number;
  costPerRequest: number;
  costPerUser: number;
  costBreakdown: Record<string, number>;
  costTrends: TrendData[];
  optimizationOpportunities: string[];
}

export interface TrendData {
  timestamp: Date;
  value: number;
  metric: string;
}

export enum TimeRange {
  LAST_HOUR = 'last_hour',
  LAST_24_HOURS = 'last_24_hours',
  LAST_7_DAYS = 'last_7_days',
  LAST_30_DAYS = 'last_30_days',
  LAST_90_DAYS = 'last_90_days',
  CUSTOM = 'custom'
}

// Platform Recommendations
export interface PlatformRecommendation {
  id: string;
  platformId: string;
  recommendationType: RecommendationType;
  priority: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  benefits: string[];
  estimatedImpact: ImpactEstimate;
  implementationEffort: 'low' | 'medium' | 'high';
  actionItems: ActionItem[];
  dueDate?: Date;
  tags: string[];
}

export enum RecommendationType {
  OPTIMIZATION = 'optimization',
  SECURITY = 'security',
  COST_SAVING = 'cost_saving',
  FEATURE_ADOPTION = 'feature_adoption',
  TROUBLESHOOTING = 'troubleshooting',
  UPGRADE = 'upgrade'
}

export interface ImpactEstimate {
  performanceImprovement?: number;
  costSavings?: number;
  securityEnhancement?: number;
  userExperienceImprovement?: number;
  timeframe: string;
}

export interface ActionItem {
  id: string;
  title: string;
  description: string;
  type: 'configuration' | 'upgrade' | 'integration' | 'monitoring';
  isRequired: boolean;
  estimatedTime: string;
  dependencies?: string[];
}

// Bulk Platform Operations
export interface BulkPlatformOperation {
  id: string;
  operation: BulkOperationType;
  platformIds: string[];
  parameters: Record<string, any>;
  status: OperationStatus;
  progress: OperationProgress;
  results: BulkOperationResults;
  createdAt: Date;
  completedAt?: Date;
  createdBy: string;
}

export enum BulkOperationType {
  CONNECT = 'connect',
  DISCONNECT = 'disconnect',
  UPDATE_CONFIG = 'update_config',
  REFRESH_CREDENTIALS = 'refresh_credentials',
  HEALTH_CHECK = 'health_check',
  SYNC_DATA = 'sync_data',
  EXPORT_DATA = 'export_data'
}

export enum OperationStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
  PARTIAL_SUCCESS = 'partial_success'
}

export interface OperationProgress {
  total: number;
  completed: number;
  failed: number;
  percentage: number;
  currentItem?: string;
  estimatedTimeRemaining?: number;
}

export interface BulkOperationResults {
  successful: string[];
  failed: Array<{
    platformId: string;
    error: string;
    suggestion?: string;
  }>;
  summary: {
    totalProcessed: number;
    successCount: number;
    failureCount: number;
    successRate: number;
  };
}