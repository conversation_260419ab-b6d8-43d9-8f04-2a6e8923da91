#!/usr/bin/env node\n\nconst { spawn } = require('child_process');\nconst path = require('path');\nconst http = require('http');\n\nconsole.log('╔════════════════════════════════════════════╗');\nconsole.log('║        React 应用编译和启动测试            ║');\nconsole.log('╚════════════════════════════════════════════╝');\n\n// 杀掉现有进程\nconsole.log('[清理] 杀掉现有Node进程...');\ntry {\n  spawn('taskkill', ['/f', '/im', 'node.exe'], { stdio: 'ignore' });\n} catch (e) {\n  // 忽略错误\n}\n\n// 等待2秒后启动\nsetTimeout(() => {\n  console.log('[启动] 启动React应用...');\n  \n  const clientPath = path.join(__dirname, '../client');\n  const reactApp = spawn('npm.cmd', ['start'], {\n    cwd: clientPath,\n    stdio: 'pipe'\n  });\n  \n  let compilationStarted = false;\n  let compilationComplete = false;\n  \n  reactApp.stdout.on('data', (data) => {\n    const output = data.toString();\n    \n    if (output.includes('Starting the development server')) {\n      console.log('[状态] 开发服务器启动中...');\n      compilationStarted = true;\n    }\n    \n    if (output.includes('Compiled successfully')) {\n      console.log('[成功] ✅ React应用编译成功!');\n      compilationComplete = true;\n      \n      // 测试连接\n      setTimeout(() => {\n        testConnection();\n      }, 2000);\n    }\n    \n    if (output.includes('Failed to compile') || output.includes('Error:')) {\n      console.log('[错误] ❌ 编译失败:');\n      console.log(output);\n    }\n  });\n  \n  reactApp.stderr.on('data', (data) => {\n    const error = data.toString();\n    if (error.includes('Error') || error.includes('Failed')) {\n      console.log('[错误] ❌ 编译错误:');\n      console.log(error);\n    }\n  });\n  \n  // 超时检查\n  setTimeout(() => {\n    if (!compilationComplete) {\n      console.log('[超时] ⏰ 编译超时，可能有错误');\n      reactApp.kill();\n    }\n  }, 60000); // 60秒超时\n  \n}, 2000);\n\nfunction testConnection() {\n  console.log('[测试] 测试应用连接...');\n  \n  const req = http.request({\n    hostname: 'localhost',\n    port: 3000,\n    path: '/',\n    method: 'GET',\n    timeout: 5000\n  }, (res) => {\n    if (res.statusCode === 200) {\n      console.log('[成功] ✅ React应用运行正常!');\n      console.log('[访问] 🌐 http://localhost:3000');\n    } else {\n      console.log(`[警告] ⚠️ 应用响应状态码: ${res.statusCode}`);\n    }\n  });\n  \n  req.on('error', (err) => {\n    console.log('[错误] ❌ 无法连接到应用:', err.message);\n  });\n  \n  req.on('timeout', () => {\n    console.log('[超时] ⏰ 连接超时');\n    req.abort();\n  });\n  \n  req.end();\n}\n\n// 处理退出\nprocess.on('SIGINT', () => {\n  console.log('\\n[退出] 测试结束');\n  process.exit(0);\n}); 