/**
 * 优化工具包统一导出
 * 提供各种性能优化和资源管理功能
 */

import { appOptimizer, AppOptimizer, AppOptimizationConfig } from './appOptimizer';
import { resourceOptimizer } from './resourceOptimizer';
import { resourcePreloader } from './resourcePreloader';
import { optimizationManager } from './optimizationManager';
import { lazyComponentManager } from './LazyComponentLoader';
import { AdvancedCodeSplitting } from './AdvancedCodeSplitting';
import { OptimizationIntegrator } from './OptimizationIntegrator';

/**
 * 优化工具集合
 */
export const optimizationTools = {
  // 应用优化器
  appOptimizer,
  
  // 资源优化器
  resourceOptimizer,
  
  // 资源预加载器
  resourcePreloader,
  
  // 优化管理器
  optimizationManager,
  
  // 懒加载管理器
  lazyLoader: lazyComponentManager,
  
  // 高级代码拆分
  codeSplitting: AdvancedCodeSplitting,
  
  // 优化集成器
  integrator: optimizationIntegrator,
};

/**
 * 快速创建优化配置
 */
export const createOptimizationConfig = (overrides: Partial<AppOptimizationConfig> = {}): AppOptimizationConfig => ({
  resourcePreloading: {
    enabled: true,
    routes: ['/dashboard', '/analytics'],
    components: ['Dashboard', 'Analytics'],
    assets: [],
    criticalResources: [],
    ...overrides.resourcePreloading
  },
  
  fontOptimization: {
    enabled: true,
    fontDisplay: 'swap',
    criticalFonts: [],
    preloadFonts: [],
    ...overrides.fontOptimization
  },
  
  codeSplitting: {
    enabled: true,
    routeLevel: true,
    componentLevel: false,
    vendorChunks: true,
    chunkSizeThreshold: 244000,
    ...overrides.codeSplitting
  },
  
  performance: {
    enabled: true,
    enableWebVitals: true,
    enableMemoryTracking: false,
    enableNetworkTracking: false,
    enableUserTiming: false,
    ...overrides.performance
  },
  
  caching: {
    enabled: true,
    routes: true,
    api: true,
    assets: true,
    ttl: 300000,
    ...overrides.caching
  }
});

/**
 * 懒加载组件创建器
 */
export const createLazyComponent = <T extends React.ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  fallback?: React.ComponentType
) => {
  return lazyComponentManager.createLazyComponent(importFn, fallback);
};

/**
 * 创建优化的应用实例
 */
export const createOptimizedApp = async (config?: Partial<AppOptimizationConfig>) => {
  const finalConfig = createOptimizationConfig(config);
  return new AppOptimizer(finalConfig);
};

/**
 * 初始化全局优化
 */
export const initializeOptimizations = async (config?: Partial<AppOptimizationConfig>) => {
  console.log('🚀 初始化应用优化...');
  
  const optimizer = await createOptimizedApp(config);
  
  // 启动优化管理器
  optimizationManager.initialize();
  
  // 启动集成器
  optimizationIntegrator.start();
  
  console.log('✅ 应用优化初始化完成');
  return optimizer;
};

// 导出类型和工具
export type { AppOptimizationConfig };
export {
  appOptimizer,
  AppOptimizer,
  resourceOptimizer,
  resourcePreloader,
  optimizationManager,
  lazyComponentManager,
  AdvancedCodeSplitting,
  OptimizationIntegrator
};

// 默认导出
export default optimizationTools;