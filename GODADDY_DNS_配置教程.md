# 🌐 GoDaddy DNS 配置详细教程

## 📋 配置概览
**目标**: 将 `iterabiz.com` 域名连接到 Vercel 部署
**平台**: GoDaddy → Vercel
**所需时间**: 5-10 分钟
**生效时间**: 最多 48 小时

---

## 🚀 第一步：获取 Vercel DNS 信息

### 1.1 登录 Vercel Dashboard
1. 访问 https://vercel.com/login
2. 登录您的账户
3. 进入项目：https://vercel.com/creai-technologys-projects/iterabiz

### 1.2 添加自定义域名
1. 点击项目页面的 **"Settings"** 标签
2. 在左侧菜单选择 **"Domains"**
3. 在输入框中输入：`iterabiz.com`
4. 点击 **"Add"** 按钮
5. 重复添加：`www.iterabiz.com`

### 1.3 获取 DNS 记录信息
添加域名后，Vercel 会显示需要配置的 DNS 记录：

```
域名: iterabiz.com
类型: A
名称: @
值: 76.76.19.19

域名: www.iterabiz.com  
类型: CNAME
名称: www
值: cname.vercel-dns.com
```

---

## 🔧 第二步：登录 GoDaddy 管理面板

### 2.1 访问 GoDaddy
1. 打开浏览器访问：https://www.godaddy.com
2. 点击右上角 **"Sign In"** 登录
3. 输入您的 GoDaddy 账户信息

### 2.2 进入域名管理
1. 登录后，点击 **"My Products"** 或 **"我的产品"**
2. 找到 **"Domains"** 或 **"域名"** 部分
3. 找到 `iterabiz.com` 域名
4. 点击域名旁边的 **"Manage"** 或 **"管理"** 按钮

---

## ⚙️ 第三步：配置 DNS 记录

### 3.1 进入 DNS 管理
1. 在域名管理页面，找到 **"DNS"** 标签
2. 点击 **"Manage DNS"** 或 **"管理 DNS"**
3. 您会看到当前的 DNS 记录列表

### 3.2 配置主域名 A 记录

**删除现有 A 记录（如果存在）**：
1. 找到类型为 **"A"**，名称为 **"@"** 的记录
2. 点击记录右侧的 **"编辑"** 或 **铅笔图标**
3. 点击 **"删除"** 或 **"Delete"**

**添加新的 A 记录**：
1. 点击 **"Add"** 或 **"添加记录"** 按钮
2. 选择记录类型：**"A"**
3. 填写以下信息：
   - **名称/Name**: `@`
   - **值/Value**: `76.76.19.19`
   - **TTL**: 保持默认（通常是 1 小时）
4. 点击 **"Save"** 或 **"保存"**

### 3.3 配置 www 子域名 CNAME 记录

**删除现有 CNAME 记录（如果存在）**：
1. 找到类型为 **"CNAME"**，名称为 **"www"** 的记录
2. 点击记录右侧的 **"编辑"** 或 **铅笔图标**
3. 点击 **"删除"** 或 **"Delete"**

**添加新的 CNAME 记录**：
1. 点击 **"Add"** 或 **"添加记录"** 按钮
2. 选择记录类型：**"CNAME"**
3. 填写以下信息：
   - **名称/Name**: `www`
   - **值/Value**: `cname.vercel-dns.com`
   - **TTL**: 保持默认（通常是 1 小时）
4. 点击 **"Save"** 或 **"保存"**

---

## 📱 第四步：验证配置

### 4.1 检查 DNS 记录
配置完成后，您的 DNS 记录应该如下所示：

| 类型 | 名称 | 值 | TTL |
|------|------|-----|-----|
| A | @ | 76.76.19.19 | 1 Hour |
| CNAME | www | cname.vercel-dns.com | 1 Hour |

### 4.2 保存更改
1. 确认所有记录正确
2. 点击页面顶部的 **"Save All Changes"** 或 **"保存所有更改"**
3. GoDaddy 会显示更改已保存的确认信息

---

## ⏰ 第五步：等待 DNS 传播

### 5.1 传播时间
- **最短时间**: 15-30 分钟
- **通常时间**: 2-6 小时
- **最长时间**: 24-48 小时

### 5.2 检查传播状态
使用以下工具检查 DNS 传播：
1. **DNS Checker**: https://dnschecker.org/
2. **What's My DNS**: https://whatsmydns.net/
3. **命令行检查**:
   ```bash
   nslookup iterabiz.com
   nslookup www.iterabiz.com
   ```

---

## ✅ 第六步：在 Vercel 验证域名

### 6.1 回到 Vercel Dashboard
1. 返回 https://vercel.com/creai-technologys-projects/iterabiz
2. 进入 **Settings** → **Domains**

### 6.2 验证域名状态
- `iterabiz.com` 应显示 ✅ **"Valid Configuration"**
- `www.iterabiz.com` 应显示 ✅ **"Valid Configuration"**

### 6.3 测试访问
DNS 传播完成后，测试以下链接：
- https://iterabiz.com
- https://www.iterabiz.com

---

## 🚨 常见问题和解决方案

### ❓ 问题 1：域名显示 "Invalid Configuration"
**解决方案**：
1. 检查 DNS 记录是否完全正确
2. 等待更长时间（最多 48 小时）
3. 清除浏览器 DNS 缓存

### ❓ 问题 2：www 子域名无法访问
**解决方案**：
1. 确认 CNAME 记录值为 `cname.vercel-dns.com`
2. 检查名称字段是否为 `www`（不是 `www.iterabiz.com`）

### ❓ 问题 3：SSL 证书错误
**解决方案**：
1. DNS 配置正确后，Vercel 会自动生成 SSL 证书
2. 通常需要等待 10-30 分钟

### ❓ 问题 4：网站显示 404 错误
**解决方案**：
1. 确认 Vercel 部署状态正常
2. 检查域名是否正确添加到 Vercel 项目

---

## 📞 需要帮助？

如果您在配置过程中遇到问题，请提供以下信息：
1. 当前 DNS 记录截图
2. Vercel 域名状态截图
3. 具体的错误信息

**🎯 配置完成后，您的 iTeraBiz 平台将通过 https://www.iterabiz.com 访问！** 