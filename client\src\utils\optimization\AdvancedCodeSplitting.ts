/**
 * 高级代码分割与智能预加载系统
 * 根据用户行为和导航模式自动分析和加载所需资源
 */

import { resourcePreloader } from './resourcePreloader';
import { storageCache } from '../api/localStorageCache';

// 路由数据
export interface RouteData {
  /** 路由路径 */
  path: string;
  /** 相关资源 */
  resources: string[];
  /** 子路由 */
  children?: RouteData[];
  /** 加载优先级 (1-10) */
  priority: number;
  /** 使用频率 */
  frequency: number;
  /** 平均停留时间(ms) */
  avgStayTime?: number;
  /** 最后访问时间 */
  lastVisited?: number;
}

// 用户行为模式
export interface UserPattern {
  /** 模式ID */
  id: string;
  /** 常用路径序列 */
  pathSequence: string[];
  /** 访问概率 */
  probability: number;
  /** 模式权重 */
  weight: number;
  /** 最后更新时间 */
  lastUpdated: number;
}

// 组件预加载配置
export interface PreloadConfig {
  /** 是否启用视口预加载 */
  enableViewportPreload: boolean;
  /** 视口预加载距离阈值 */
  viewportThreshold: number;
  /** 是否启用鼠标悬停预加载 */
  enableHoverPreload: boolean;
  /** 悬停延迟(ms) */
  hoverDelay: number;
  /** 是否启用预测性预加载 */
  enablePredictivePreload: boolean;
  /** 预测阈值 */
  predictionThreshold: number;
  /** 并发预加载限制 */
  concurrentLimit: number;
  /** 预加载超时(ms) */
  timeout: number;
}

/**
 * 高级代码分割与预加载系统
 */
export class AdvancedCodeSplitting {
  private routeGraph: Map<string, RouteData> = new Map();
  private userPatterns: UserPattern[] = [];
  private routeHistory: string[] = [];
  private currentRoute: string = '';
  private isInitialized: boolean = false;
  private preloadQueue: string[] = [];
  private activePreloads: Set<string> = new Set();
  private config: PreloadConfig = {
    enableViewportPreload: true,
    viewportThreshold: 100,
    enableHoverPreload: true,
    hoverDelay: 150,
    enablePredictivePreload: true,
    predictionThreshold: 0.4,
    concurrentLimit: 3,
    timeout: 10000
  };

  /**
   * 构造函数
   */
  constructor() {
    this.loadStoredData();
  }

  /**
   * 初始化系统
   */
  public initialize(): void {
    if (this.isInitialized) return;

    // 从本地存储加载数据
    this.loadStoredData();

    // 注册导航事件监听
    this.registerNavigationListener();

    // 注册鼠标悬停监听
    if (this.config.enableHoverPreload) {
      this.registerHoverListener();
    }

    // 注册视口监听
    if (this.config.enableViewportPreload) {
      this.registerViewportListener();
    }

    // 开始处理预加载队列
    this.processPreloadQueue();

    this.isInitialized = true;
    console.log('[CodeSplitter] 高级代码分割系统初始化完成');
  }

  /**
   * 从存储加载数据
   */
  private loadStoredData(): void {
    try {
      // 加载路由图
      const storedRouteGraph = storageCache.get<[string, RouteData][]>('route_graph');
      if (storedRouteGraph) {
        this.routeGraph = new Map(storedRouteGraph);
      }

      // 加载用户模式
      const storedPatterns = storageCache.get<UserPattern[]>('user_patterns');
      if (storedPatterns) {
        this.userPatterns = storedPatterns;
      }

      // 加载路由历史
      const storedHistory = storageCache.get<string[]>('route_history');
      if (storedHistory) {
        this.routeHistory = storedHistory.slice(-20); // 只保留最近20条
      }

      // 加载配置
      const storedConfig = storageCache.get<PreloadConfig>('preload_config');
      if (storedConfig) {
        this.config = { ...this.config, ...storedConfig };
      }
    } catch (error: unknown) {
      console.warn('[CodeSplitter] 加载存储数据失败:', error);
    }
  }

  /**
   * 保存数据到存储
   */
  private saveToStorage(): void {
    try {
      // 保存路由图
      storageCache.set('route_graph', Array.from(this.routeGraph.entries()), 24 * 60 * 60); // 24小时

      // 保存用户模式
      storageCache.set('user_patterns', this.userPatterns, 24 * 60 * 60); // 24小时

      // 保存路由历史
      storageCache.set('route_history', this.routeHistory.slice(-20), 24 * 60 * 60); // 24小时

      // 保存配置
      storageCache.set('preload_config', this.config, 24 * 60 * 60); // 24小时
    } catch (error: unknown) {
      console.warn('[CodeSplitter] 保存数据到存储失败:', error);
    }
  }

  /**
   * 注册导航监听器
   */
  private registerNavigationListener(): void {
    // 监听 popstate 事件
    window.addEventListener('popstate', () => {
      this.handleRouteChange(window.location.pathname);
    });

    // 拦截 History API
    const originalPushState = history.pushState;
    const originalReplaceState = history.replaceState;

    history.pushState = (...args) => {
      originalPushState.apply(history, args);
      this.handleRouteChange(window.location.pathname);
    };

    history.replaceState = (...args) => {
      originalReplaceState.apply(history, args);
      this.handleRouteChange(window.location.pathname);
    };

    // 初始化当前路由
    this.handleRouteChange(window.location.pathname);
  }

  /**
   * 处理路由变化
   */
  private handleRouteChange(newRoute: string): void {
    if (newRoute === this.currentRoute) return;

    // 更新当前路由停留时间
    if (this.currentRoute) {
      this.updateRouteStayTime(this.currentRoute);
    }

    // 记录路由历史
    this.routeHistory.push(newRoute);
    if (this.routeHistory.length > 50) {
      this.routeHistory = this.routeHistory.slice(-50);
    }

    // 更新当前路由
    this.currentRoute = newRoute;

    // 更新路由图
    this.updateRouteFrequency(newRoute);

    // 分析用户模式
    this.analyzeUserPatterns();

    // 预测下一个路由并预加载
    if (this.config.enablePredictivePreload) {
      this.predictNextRoutes();
    }

    // 保存数据
    this.saveToStorage();
  }

  /**
   * 更新路由停留时间
   */
  private updateRouteStayTime(route: string): void {
    const routeData = this.routeGraph.get(route);
    if (routeData && routeData.lastVisited) {
      const stayTime = Date.now() - routeData.lastVisited;
      routeData.avgStayTime = routeData.avgStayTime
        ? (routeData.avgStayTime + stayTime) / 2
        : stayTime;
    }
  }

  /**
   * 更新路由频率
   */
  private updateRouteFrequency(route: string): void {
    const existing = this.routeGraph.get(route);
    if (existing) {
      existing.frequency += 1;
      existing.lastVisited = Date.now();
    } else {
      this.routeGraph.set(route, {
        path: route,
        resources: [],
        priority: 5,
        frequency: 1,
        lastVisited: Date.now()
      });
    }
  }

  /**
   * 分析用户模式
   */
  private analyzeUserPatterns(): void {
    if (this.routeHistory.length < 3) return;

    const sequenceLength = 3;
    const newPatterns: UserPattern[] = [];

    for (let i = 0; i <= this.routeHistory.length - sequenceLength; i++) {
      const sequence = this.routeHistory.slice(i, i + sequenceLength);
      const sequenceKey = sequence.join('->');

      // 检查是否已存在该模式
      let existingPattern = this.userPatterns.find(p => p.id === sequenceKey);

      if (existingPattern) {
        existingPattern.weight += 1;
        existingPattern.lastUpdated = Date.now();
      } else {
        newPatterns.push({
          id: sequenceKey,
          pathSequence: sequence,
          probability: 0,
          weight: 1,
          lastUpdated: Date.now()
        });
      }
    }

    // 添加新模式
    this.userPatterns.push(...newPatterns);

    // 保持模式数量在合理范围内
    if (this.userPatterns.length > 100) {
      this.userPatterns.sort((a, b) => b.weight - a.weight);
      this.userPatterns = this.userPatterns.slice(0, 100);
    }

    // 重新计算概率
    this.recalculatePatternProbabilities();
  }

  /**
   * 重新计算模式概率
   */
  private recalculatePatternProbabilities(): void {
    const totalWeight = this.userPatterns.reduce((sum, pattern) => sum + pattern.weight, 0);
    
    if (totalWeight > 0) {
      this.userPatterns.forEach(pattern => {
        pattern.probability = pattern.weight / totalWeight;
      });
    }
  }

  /**
   * 预测下一个路由
   */
  private predictNextRoutes(): void {
    if (this.routeHistory.length < 2) return;

    const recentSequence = this.routeHistory.slice(-2);
    const predictions: { route: string; confidence: number }[] = [];

    // 基于模式匹配预测
    for (const pattern of this.userPatterns) {
      const sequenceIndex = pattern.pathSequence.findIndex((path, index) => {
        return index + recentSequence.length <= pattern.pathSequence.length &&
               pattern.pathSequence.slice(index, index + recentSequence.length).join('->') === recentSequence.join('->');
      });

      if (sequenceIndex !== -1 && sequenceIndex + recentSequence.length < pattern.pathSequence.length) {
        const predictedRoute = pattern.pathSequence[sequenceIndex + recentSequence.length];
        const existingPrediction = predictions.find(p => p.route === predictedRoute);

        if (existingPrediction) {
          existingPrediction.confidence += pattern.probability;
        } else {
          predictions.push({
            route: predictedRoute,
            confidence: pattern.probability
          });
        }
      }
    }

    // 按置信度排序并预加载高置信度的路由
    predictions.sort((a, b) => b.confidence - a.confidence);

    for (const prediction of predictions) {
      if (prediction.confidence >= this.config.predictionThreshold) {
        this.schedulePreload(prediction.route);
      }
    }
  }

  /**
   * 注册悬停监听器
   */
  private registerHoverListener(): void {
    let hoverTimeout: number | null = null;

    const handleMouseEnter = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      const link = target.closest('a[href]') as HTMLAnchorElement;

      if (link && link.href) {
        hoverTimeout = window.setTimeout(() => {
          this.schedulePreload(link.href);
        }, this.config.hoverDelay);
      }
    };

    const handleMouseLeave = () => {
      if (hoverTimeout) {
        clearTimeout(hoverTimeout);
        hoverTimeout = null;
      }
    };

    // 使用事件委托
    document.addEventListener('mouseenter', handleMouseEnter, true);
    document.addEventListener('mouseleave', handleMouseLeave, true);
  }

  /**
   * 注册视口监听器
   */
  private registerViewportListener(): void {
    if ('IntersectionObserver' in window) {
      const observer = new IntersectionObserver(
        (entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              const link = entry.target as HTMLAnchorElement;
              if (link.href) {
                this.schedulePreload(link.href);
              }
            }
          });
        },
        {
          rootMargin: `${this.config.viewportThreshold}px`
        }
      );

      const updateObservedLinks = () => {
        const links = document.querySelectorAll('a[href]');
        links.forEach(link => {
          observer.observe(link);
        });
      };

      // 初始化观察
      updateObservedLinks();

      // 监听DOM变化
      const mutationObserver = new MutationObserver(() => {
        updateObservedLinks();
      });

      mutationObserver.observe(document.body, {
        childList: true,
        subtree: true
      });
    }
  }

  /**
   * 安排预加载
   */
  private schedulePreload(url: string): void {
    // 解析URL，只预加载同源资源
    try {
      const urlObject = new URL(url, window.location.origin);
      
      if (urlObject.origin !== window.location.origin) {
        return; // 跨域资源不预加载
      }

      const cleanPath = urlObject.pathname;

      // 检查是否已在队列中或正在加载
      if (!this.preloadQueue.includes(cleanPath) && !this.activePreloads.has(cleanPath)) {
        this.preloadQueue.push(cleanPath);
        console.log(`[CodeSplitter] 添加到预加载队列: ${cleanPath}`);
      }
    } catch (error) {
      console.warn(`[CodeSplitter] 无效URL: ${url}`, error);
    }
  }

  /**
   * 处理预加载队列
   */
  private processPreloadQueue(): void {
    setInterval(() => {
      // 如果已经达到并发限制，不处理
      if (this.activePreloads.size >= this.config.concurrentLimit) return;

      // 可以处理的数量
      const availableSlots = this.config.concurrentLimit - this.activePreloads.size;

      // 从队列获取要预加载的资源
      const resourcesToLoad = this.preloadQueue.splice(0, availableSlots);

      // 预加载资源
      for (const resource of resourcesToLoad) {
        this.activePreloads.add(resource);

        resourcePreloader.preload({
          url: resource,
          type: this.getResourceType(resource),
          timeout: this.config.timeout
        })
        .then(() => {
          console.log(`[CodeSplitter] 预加载成功: ${resource}`);
        })
        .catch((error: unknown) => {
          console.warn(`[CodeSplitter] 预加载失败: ${resource}`, error);
        })
        .finally(() => {
          this.activePreloads.delete(resource);
        });
      }
    }, 100); // 每100ms检查一次队列
  }

  /**
   * 获取资源类型
   */
  private getResourceType(url: string): 'script' | 'style' | 'image' | 'font' | 'fetch' {
    const extension = url.split('.').pop()?.toLowerCase();
    
    switch (extension) {
      case 'js':
      case 'jsx':
      case 'ts':
      case 'tsx':
        return 'script';
      case 'css':
      case 'scss':
      case 'sass':
        return 'style';
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'webp':
      case 'svg':
        return 'image';
      case 'woff':
      case 'woff2':
      case 'ttf':
      case 'otf':
        return 'font';
      default:
        return 'fetch';
    }
  }

  /**
   * 获取路由图
   */
  public getRouteGraph(): Map<string, RouteData> {
    return new Map(this.routeGraph);
  }

  /**
   * 获取用户模式
   */
  public getUserPatterns(): UserPattern[] {
    return [...this.userPatterns];
  }

  /**
   * 获取当前配置
   */
  public getConfig(): PreloadConfig {
    return { ...this.config };
  }

  /**
   * 更新配置
   */
  public updateConfig(newConfig: Partial<PreloadConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.saveToStorage();
  }

  /**
   * 获取预加载统计
   */
  public getStats(): {
    totalRoutes: number;
    totalPatterns: number;
    queueSize: number;
    activePreloads: number;
  } {
    return {
      totalRoutes: this.routeGraph.size,
      totalPatterns: this.userPatterns.length,
      queueSize: this.preloadQueue.length,
      activePreloads: this.activePreloads.size
    };
  }

  /**
   * 重置分析数据
   */
  public resetAnalytics(): void {
    this.routeGraph.clear();
    this.userPatterns = [];
    this.routeHistory = [];
    this.preloadQueue = [];
    this.activePreloads.clear();
    
    // 清除存储数据
    storageCache.remove('route_graph');
    storageCache.remove('user_patterns');
    storageCache.remove('route_history');
    
    console.log('[CodeSplitter] 分析数据已重置');
  }
}

// 创建单例
export const advancedCodeSplitting = new AdvancedCodeSplitting();

// 页面加载时自动初始化
if (typeof window !== 'undefined') {
  window.addEventListener('load', () => {
    // 延迟初始化，避免与关键渲染竞争资源
    setTimeout(() => {
      advancedCodeSplitting.initialize();
    }, 2000);
  });
}

export default advancedCodeSplitting;