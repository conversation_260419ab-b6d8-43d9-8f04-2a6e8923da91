/**
 * Simple CTA Buttons Demo
 * 展示与 Hero Section 一致的 CTA 按钮设计
 */

import React from 'react';
import { Rocket, MessageCircle  } from 'lucide-react';
import {  SimpleCTAPrimaryButton, SimpleCTASecondaryButton  } from "./SimpleCTAButtons";

const SimpleCTADemo: React.FC = () => {;
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-purple-50/30 to-gray-100 flex items-center justify-center p-8">
      <div className="max-w-5xl mx-auto text-center">
        <h1 className="text-4xl font-bold text-gray-800 mb-4">
          CTA 按钮设计 (优化尺寸和圆角)
        </h1>
        <p className="text-lg text-gray-600 mb-12">
          优化按钮尺寸和圆角处理，保持与 Hero Section 相同的视觉效果
        </p>
        {/* 按钮演示 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
          {/* Primary Button 演示 */}
          <div className="space-y-6">
            <h3 className="text-2xl font-semibold text-gray-800">Get Started Free 按钮</h3>
            <div className="bg-white p-8 rounded-xl shadow-sm border">
              <SimpleCTAPrimaryButton
                variant="primary"
                onClick={() => console.log('Get Started Free clicked')}
                icon={<Rocket className="w-5 h-5" />}
              >
                Get Started Free
              </SimpleCTAPrimaryButton>
            </div>
            <div className="text-left space-y-2 text-sm text-gray-600">
              <p><strong>设计特点：</strong></p>
              <ul className="space-y-1 list-disc list-inside">
                <li>与 Hero 的 Start Free Trial 相同渐变 (purple-600 → pink-600)</li>
                <li>相同的浅灰色边框 (border-gray-200)</li>
                <li>优化尺寸 (padding: 14px 28px, height: 52px)</li>
                <li>圆角优化 (border-radius: 10px)</li>
                <li>相同的 hover 效果：阴影 + 缩放动画</li>
                <li>Hove,r: 渐变加深 + 边框变化</li>
              </ul>
            </div>
          </div>
          {/* Secondary Button 演示 */}
          <div className="space-y-6">
            <h3 className="text-2xl font-semibold text-gray-800">Contact Us 按钮</h3>
            <div className="bg-white p-8 rounded-xl shadow-sm border">
              <SimpleCTASecondaryButton
                variant="secondary"
                onClick={() => console.log('Contact Us clicked')}
                icon={<MessageCircle className="w-4 h-4" />}
              >
                Contact Us
              </SimpleCTASecondaryButton>
            </div>
            <div className="text-left space-y-2 text-sm text-gray-600">
              <p><strong>设计特点：</strong></p>
              <ul className="space-y-1 list-disc list-inside">
                <li>与 Hero 的 Watch Demo 相同样式</li>
                <li>透明背景 + 灰色边框 (border-gray-300)</li>
                <li>灰色文字 (text-gray-700)</li>
                <li>优化尺寸 (padding: 14px 28px, height: 52px)</li>
                <li>圆角优化 (border-radius: 10px)</li>
                <li>Hove,r: 阴影 + 边框加深 + 缩放动画</li>
              </ul>
            </div>
          </div>
        </div>
        {/* 对比展示 */}
        <div className="bg-white p-8 rounded-xl shadow-sm border">
          <h3 className="text-2xl font-semibold text-gray-800 mb-6">并排效果展示</h3>
          <div className="flex flex-col sm:flex-row justify-center items-center gap-6">
            <SimpleCTAPrimaryButton
              variant="primary"
              onClick={() => console.log('Get Started Free clicked')}
              icon={<Rocket className="w-5 h-5" />}
            >
              Get Started Free
            </SimpleCTAPrimaryButton>
            <SimpleCTASecondaryButton
              variant="secondary"
              onClick={() => console.log('Contact Us clicked')}
              icon={<MessageCircle className="w-4 h-4" />}
            >
              Contact Us
            </SimpleCTASecondaryButton>
          </div>
        </div>
        {/* 设计说明 */}
        <div className="mt-12 text-sm text-gray-500 space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 text-left">
            <div>
              <p><strong>设计一致性：</strong></p>
              <ul className="space-y-1 mt-2">
                <li>• Get Started Free = Hero 的 Start Free Trial 样式</li>
                <li>• Contact Us = Hero 的 Watch Demo 样式</li>
                <li>• 保持相同的视觉语言和交互效果</li>
                <li>• 适配 CTA 区域的紧凑布局</li>
              </ul>
            </div>
            <div>
              <p><strong>尺寸优化：</strong></p>
              <ul className="space-y-1 mt-2">
                <li>• 更舒适的 padding (14px, 28px)</li>
                <li>• 更圆润的圆角 (10px)</li>
                <li>• 适中的按钮高度 (52px)</li>
                <li>• 最小宽度保证 (200px)</li>
                <li>• 保留相同的 hover 动画效果</li>
                <li>• 更好的视觉平衡和点击体验</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>

};

export default SimpleCTADemo;