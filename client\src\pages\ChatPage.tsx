import React, { useState, useRef, useEffect } from 'react';
// import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { PageTitle } from '@/components/common/PageTitle';
import {
  Send,
  // Calendar,
  // Clock,
  MapPin,
  Users,
  Settings,
  MessageSquare,
  Phone,
  // CheckCircle,
  Bot,
  User
} from 'lucide-react';

interface Message {
  id: string;
  content: string;
  sender: 'user' | 'bot';
  timestamp: Date;
  type?: 'text' | 'booking' | 'quick-action';
  bookingData?: {
    type: 'walkin' | 'onsite';
    service: string;
    time?: string;
    address?: string;
    status: 'pending' | 'confirmed' | 'completed';
  };
}

interface QuickAction {
  id: string;
  label: string;
  action: string;
  icon: React.ReactNode;
  description: string;
}

export default function ChatPage() {
  // const { t } = useTranslation(); // Commented out unused variable
  
  const [messages, setMessages] = useState<Message[]>([{
    id: '1',
    content: '欢迎使用智能助手！我可以帮您预约服务、回答问题。',
    sender: 'bot',
    timestamp: new Date()
  }]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('chat');
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const quickActions: QuickAction[] = [
    {
      id: 'walkin-booking',
      label: '到店预约',
      action: 'book-walkin',
      icon: <Users className="h-4 w-4" />,
      description: '预约到店服务'
    },
    {
      id: 'onsite-booking',
      label: '上门服务',
      action: 'book-onsite',
      icon: <MapPin className="h-4 w-4" />,
      description: '预约上门服务'
    },
    {
      id: 'service-inquiry',
      label: '服务咨询',
      action: 'inquiry',
      icon: <MessageSquare className="h-4 w-4" />,
      description: '了解服务详情'
    },
    {
      id: 'contact-support',
      label: '联系客服',
      action: 'support',
      icon: <Phone className="h-4 w-4" />,
      description: '获取人工帮助'
    }
  ];

  // 滚动到最新消息
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // 处理快速操作
  const handleQuickAction = (action: string) => {
    let responseMessage = '';
    let bookingData: Message['bookingData'] = undefined;

    switch (action) {
      case 'book-walkin':
        responseMessage = '好的，我来帮您预约到店服务。请选择您需要的服务类型：';
        bookingData = {
          type: 'walkin',
          service: '清洁服务',
          time: '明天下午2点',
          status: 'pending'
        };
        break;
      case 'book-onsite':
        responseMessage = '好的，我来帮您预约上门服务。请提供您的地址信息：';
        bookingData = {
          type: 'onsite',
          service: '维修服务',
          address: '待确认',
          status: 'pending'
        };
        break;
      case 'inquiry':
        responseMessage = '我很乐意为您介绍我们的服务。请问您对哪个服务感兴趣？';
        break;
      case 'support':
        responseMessage = '正在为您转接人工客服，请稍候...';
        break;
      default:
        responseMessage = '抱歉，我不太理解您的需求，请重新描述。';
    }

    const userMessage: Message = {
      id: Date.now().toString(),
      content: `选择了：${quickActions.find(a => a.action === action)?.label}`,
      sender: 'user',
      timestamp: new Date(),
      type: 'quick-action'
    };

    const botMessage: Message = {
      id: (Date.now() + 1).toString(),
      content: responseMessage,
      sender: 'bot',
      timestamp: new Date(),
      type: bookingData ? 'booking' : 'text',
      bookingData
    };

    setMessages(prev => [...prev, userMessage, botMessage]);
  };

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      content: inputValue.trim(),
      sender: 'user',
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);

    // 模拟AI响应
    setTimeout(() => {
      const botMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: '感谢您的消息！我正在处理您的请求，请稍候...',
        sender: 'bot',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, botMessage]);
      setIsLoading(false);
    }, 1000);
  };

  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <div className="container mx-auto py-6 max-w-6xl">
      <PageTitle
        title="智能助手"
        description="24/7 AI助手为您提供服务支持"
        size="lg"
      />

      <div className="mt-8">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="chat">对话</TabsTrigger>
            <TabsTrigger value="history">历史记录</TabsTrigger>
            <TabsTrigger value="settings">设置</TabsTrigger>
          </TabsList>

          <TabsContent value="chat" className="mt-6">
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
              {/* 快速操作面板 */}
              <div className="lg:col-span-1">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center gap-2">
                      <Settings className="h-5 w-5" />
                      快速操作
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    {quickActions.map((action) => (
                      <Button
                        key={action.id}
                        variant="outline"
                        className="w-full justify-start h-auto p-4"
                        onClick={() => handleQuickAction(action.action)}
                      >
                        <div className="flex items-start gap-3">
                          {action.icon}
                          <div className="text-left">
                            <div className="font-medium">{action.label}</div>
                            <div className="text-xs text-muted-foreground">
                              {action.description}
                            </div>
                          </div>
                        </div>
                      </Button>
                    ))}
                  </CardContent>
                </Card>
              </div>

              {/* 聊天界面 */}
              <div className="lg:col-span-3">
                <Card className="h-[600px] flex flex-col">
                  <CardHeader className="border-b">
                    <CardTitle className="flex items-center gap-2">
                      <Bot className="h-5 w-5" />
                      AI助手
                      <Badge variant="secondary" className="ml-auto">
                        在线
                      </Badge>
                    </CardTitle>
                  </CardHeader>

                  {/* 消息列表 */}
                  <CardContent className="flex-1 overflow-y-auto p-4 space-y-4">
                    {messages.map((message) => (
                      <div
                        key={message.id}
                        className={`flex gap-3 ${message.sender === 'user' ? 'flex-row-reverse' : 'flex-row'}`}
                      >
                        <Avatar className="h-8 w-8">
                          <AvatarFallback>
                            {message.sender === 'user' ? <User className="h-4 w-4" /> : <Bot className="h-4 w-4" />}
                          </AvatarFallback>
                        </Avatar>
                        
                        <div className={`max-w-[70%] ${message.sender === 'user' ? 'text-right' : 'text-left'}`}>
                          <div
                            className={`rounded-lg p-3 ${
                              message.sender === 'user'
                                ? 'bg-primary text-primary-foreground'
                                : 'bg-muted'
                            }`}
                          >
                            <p className="text-sm">{message.content}</p>
                            
                            {/* 预约信息卡片 */}
                            {message.bookingData && (
                              <div className="mt-3 p-3 bg-background/10 rounded border">
                                <div className="flex items-center gap-2 mb-2">
                                  {message.bookingData.type === 'walkin' ? (
                                    <Users className="h-4 w-4" />
                                  ) : (
                                    <MapPin className="h-4 w-4" />
                                  )}
                                  <span className="font-medium text-xs">
                                    {message.bookingData.type === 'walkin' ? '到店预约' : '上门服务'}
                                  </span>
                                </div>
                                <div className="text-xs space-y-1">
                                  <div>服务：{message.bookingData.service}</div>
                                  {message.bookingData.time && (
                                    <div>时间：{message.bookingData.time}</div>
                                  )}
                                  {message.bookingData.address && (
                                    <div>地址：{message.bookingData.address}</div>
                                  )}
                                  <div className="flex items-center gap-1">
                                    <span>状态：</span>
                                    <Badge 
                                      variant={message.bookingData.status === 'confirmed' ? 'default' : 'secondary'}
                                      className="text-xs"
                                    >
                                      {message.bookingData.status === 'pending' && '待确认'}
                                      {message.bookingData.status === 'confirmed' && '已确认'}
                                      {message.bookingData.status === 'completed' && '已完成'}
                                    </Badge>
                                  </div>
                                </div>
                              </div>
                            )}
                          </div>
                          
                          <div className="text-xs text-muted-foreground mt-1">
                            {message.timestamp.toLocaleTimeString()}
                          </div>
                        </div>
                      </div>
                    ))}
                    
                    {isLoading && (
                      <div className="flex gap-3">
                        <Avatar className="h-8 w-8">
                          <AvatarFallback>
                            <Bot className="h-4 w-4" />
                          </AvatarFallback>
                        </Avatar>
                        <div className="bg-muted rounded-lg p-3">
                          <div className="flex space-x-1">
                            <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce"></div>
                            <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                            <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                          </div>
                        </div>
                      </div>
                    )}
                    
                    <div ref={messagesEndRef} />
                  </CardContent>

                  {/* 输入区域 */}
                  <div className="border-t p-4">
                    <div className="flex gap-2">
                      <Input
                        value={inputValue}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => setInputValue(e.target.value)}
                        onKeyDown={handleKeyPress}
                        placeholder="输入您的消息..."
                        disabled={isLoading}
                        className="flex-1"
                      />
                      <Button 
                        onClick={handleSendMessage} 
                        disabled={!inputValue.trim() || isLoading}
                        size="icon"
                      >
                        <Send className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </Card>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="history" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>对话历史</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  暂无历史对话记录
                </p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="settings" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>聊天设置</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  聊天偏好设置功能开发中...
                </p>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
} 