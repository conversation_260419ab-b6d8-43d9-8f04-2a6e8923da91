/**
 * 主题系统类型定义
 * 定义主题相关的TypeScript类型
 */

import type { DesignTokens, ThemeMode } from './design-system';

// 主题提供者属性
export interface ThemeProviderProps {
  children: React.ReactNode;
  defaultTheme?: ThemeMode;
  forcedTheme?: ThemeMode;
  enableSystem?: boolean;
  disableTransitionOnChange?: boolean;
  storageKey?: string;
}

// 主题钩子返回值
export interface UseThemeReturn {
  theme: ThemeMode;
  setTheme: (theme: ThemeMode) => void;
  resolvedTheme: ThemeMode;
  themes: ThemeMode[];
  systemTheme: ThemeMode | undefined;
}

// 主题变量
export interface ThemeVariables {
  // CSS 自定义属性
  '--color-primary': string;
  '--color-primary-50': string;
  '--color-primary-100': string;
  '--color-primary-200': string;
  '--color-primary-300': string;
  '--color-primary-400': string;
  '--color-primary-500': string;
  '--color-primary-600': string;
  '--color-primary-700': string;
  '--color-primary-800': string;
  '--color-primary-900': string;
  '--color-primary-950': string;
  
  '--color-background-primary': string;
  '--color-background-secondary': string;
  '--color-background-accent': string;
  '--color-background-muted': string;
  
  '--color-text-primary': string;
  '--color-text-secondary': string;
  '--color-text-muted': string;
  '--color-text-accent': string;
  '--color-text-inverse': string;
  
  '--color-border-light': string;
  '--color-border-default': string;
  '--color-border-dark': string;
  '--color-border-accent': string;
  
  '--spacing-xs': string;
  '--spacing-sm': string;
  '--spacing-md': string;
  '--spacing-lg': string;
  '--spacing-xl': string;
  
  '--border-radius-sm': string;
  '--border-radius-md': string;
  '--border-radius-lg': string;
  '--border-radius-xl': string;
  '--border-radius-full': string;
  
  '--shadow-sm': string;
  '--shadow-card': string;
  '--shadow-hover': string;
  '--shadow-focus': string;
  '--shadow-large': string;
  
  '--animation-duration-fast': string;
  '--animation-duration-normal': string;
  '--animation-duration-slow': string;
  
  '--font-family-sans': string;
  '--font-family-mono': string;
  
  '--breakpoint-sm': string;
  '--breakpoint-md': string;
  '--breakpoint-lg': string;
  '--breakpoint-xl': string;
  '--breakpoint-2xl': string;
}

// 主题配置
export interface ThemeConfiguration {
  light: DesignTokens;
  dark: DesignTokens;
}

// 主题上下文
export interface ThemeContextValue {
  mode: ThemeMode;
  tokens: DesignTokens;
  setTheme: (mode: ThemeMode) => void;
  toggleTheme: () => void;
  isDark: boolean;
  isLight: boolean;
}

// 主题元数据
export interface ThemeMetadata {
  name: string;
  displayName: string;
  description?: string;
  author?: string;
  version?: string;
  tags?: string[];
}

// 自定义主题
export interface CustomTheme extends ThemeMetadata {
  tokens: DesignTokens;
  variables: Partial<ThemeVariables>;
}

// 主题管理器
export interface ThemeManager {
  currentTheme: ThemeMode;
  availableThemes: ThemeMode[];
  customThemes: CustomTheme[];
  setTheme: (theme: ThemeMode) => void;
  addCustomTheme: (theme: CustomTheme) => void;
  removeCustomTheme: (name: string) => void;
  exportTheme: (name?: string) => string;
  importTheme: (themeData: string) => void;
  resetToDefault: () => void;
}

// 主题预设
export interface ThemePreset {
  name: string;
  displayName: string;
  description: string;
  tokens: Partial<DesignTokens>;
  preview: {
    primaryColor: string;
    backgroundColor: string;
    textColor: string;
  };
}

// 响应式主题
export interface ResponsiveTheme {
  mobile: Partial<DesignTokens>;
  tablet: Partial<DesignTokens>;
  desktop: Partial<DesignTokens>;
}

// 主题过渡动画
export interface ThemeTransition {
  duration: number;
  easing: string;
  properties: string[];
} 