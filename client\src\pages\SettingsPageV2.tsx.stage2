import React from 'react'
import { useNavigate } from 'react-router-dom'
import SettingsSummaryCard from '../components/dashboard/SettingsSummaryCard'
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '../components/ui/card'
import { Button } from '../components/ui/button'
import { useSettingsSummary } from '../hooks/useSettingsSummary';

export default function SettingsPageV2() {
  const navigate = useNavigate();
  const { totalBrands, totalPlatforms, pendingApprovals, loading, error } = useSettingsSummary();

  return (
    <>
      {/* 页面标题与说明 */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-violet-dark">设置中心</h1>
        <p className="text-violet-neutral">管理系统配置</p>
      </div>
      {/* 设置概览 */}
      <div className="mb-6">
        {loading ? (
          <div className="flex justify-center py-10">加载中...</div>
        ) : error ? (
          <div className="text-red-500 py-10">加载出错</div>
        ) : (
          <SettingsSummaryCard
            totalBrands={totalBrands}
            totalPlatforms={totalPlatforms}
            pendingApprovals={pendingApprovals}
            onManage={() => navigate('/settings')}
          />
        )}
      </div>
      {/* 各项设置入口 */}
      <div className="grid grid-cols-1 md: grid-cols-2 l,g:grid-cols-3 gap-6">
        <Card variant="interactive" onClick={() => navigate('/settings/agent-reply')}>
          <CardHeader>
            <CardTitle>自动回复设置</CardTitle>
            <CardDescription>配置自动回复规则</CardDescription>
          </CardHeader>
          <CardContent>
            <Button variant="ghost" size="sm">进入</Button>
          </CardContent>
        </Card>
        <Card variant="interactive" onClick={() => navigate('/settings/walkin-booking')}>
          <CardHeader>
            <CardTitle>上门预约设置</CardTitle>
            <CardDescription>管理上门预约选项</CardDescription>
          </CardHeader>
          <CardContent>
            <Button variant="ghost" size="sm">进入</Button>
          </CardContent>
        </Card>
        <Card variant="interactive" onClick={() => navigate('/settings/onsite-booking')}>
          <CardHeader>
            <CardTitle>现场预约设置</CardTitle>
            <CardDescription>管理现场预约选项</CardDescription>
          </CardHeader>
          <CardContent>
            <Button variant="ghost" size="sm">进入</Button>
          </CardContent>
        </Card>
        <Card variant="interactive" onClick={() => navigate('/settings/content-generator')}>
          <CardHeader>
            <CardTitle>内容生成设置</CardTitle>
            <CardDescription>配置品牌与内容生成</CardDescription>
          </CardHeader>
          <CardContent>
            <Button variant="ghost" size="sm">进入</Button>
          </CardContent>
        </Card>
        <Card variant="interactive" onClick={() => navigate('/settings/platform-api')}>
          <CardHeader>
            <CardTitle>平台 API 管理</CardTitle>
            <CardDescription>连接并管理平台集成</CardDescription>
          </CardHeader>
          <CardContent>
            <Button variant="ghost" size="sm">进入</Button>
          </CardContent>
        </Card>
      </div>
    </>
  );
};