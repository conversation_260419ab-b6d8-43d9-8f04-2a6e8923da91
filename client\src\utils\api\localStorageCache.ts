/**
 * 缓存项接口
 */
interface CacheItem<T> {
  value: T;
  expiry: number; // 过期时间戳
  createdAt: number; // 创建时间戳
}

/**
 * 本地存储缓存选项
 */
export interface StorageCacheOptions {
  /** 缓存前缀，防止冲突 */
  prefix?: string;
  /** 默认过期时间(秒)，0表示永不过期 */
  defaultTTL?: number;
  /** 自动清理间隔(秒)，0表示禁用自动清理 */
  cleanupInterval?: number;
  /** 是否启用调试日志 */
  debug?: boolean;
}

/**
 * 本地存储缓存管理器
 * 提供带TTL的数据缓存功能，支持自动过期清理
 */
export class LocalStorageCache {
  private prefix: string;
  private defaultTTL: number;
  private cleanupTimer: number | null = null;
  private debug: boolean;

  /**
   * 创建缓存管理器实例
   * @param options 缓存选项
   */
  constructor(options: StorageCacheOptions = {}) {
    this.prefix = options.prefix || 'app_cache_';
    this.defaultTTL = options.defaultTTL || 86400; // 默认1天
    this.debug = options.debug || false;

    // 设置自动清理
    if (options.cleanupInterval && options.cleanupInterval > 0) {
      this.startCleanup(options.cleanupInterval);
    }

    if (this.debug) {
      console.log('[LocalStorageCache] 初始化', {
        prefix: this.prefix,
        defaultTTL: this.defaultTTL,
        cleanupInterval: options.cleanupInterval
      });
    }
  }

  /**
   * 生成缓存键
   * @param key 原始键
   * @returns 带前缀的缓存键
   */
  private getKey(key: string): string {
    return `${this.prefix}${key}`;
  }

  /**
   * 设置缓存项
   * @param key 缓存键
   * @param value 缓存值
   * @param ttl 过期时间(秒)，默认使用实例默认值
   * @returns 是否设置成功
   */
  set<T>(key: string, value: T, ttl?: number): boolean {
    try {
      const now = Date.now();
      const expiryTime = ttl !== undefined && ttl >= 0
        ? now + ttl * 1000
        : ttl === 0 
          ? 0 // 0表示永不过期
          : now + this.defaultTTL * 1000;

      const item: CacheItem<T> = {
        value,
        expiry: expiryTime,
        createdAt: now
      };

      localStorage.setItem(this.getKey(key), JSON.stringify(item));
      
      if (this.debug) {
        console.log(`[LocalStorageCache] 设置缓存: ${key}`, {
          value,
          expiry: new Date(expiryTime).toLocaleString(),
          ttl: ttl || this.defaultTTL
        });
      }
      
      return true;
    } catch (err) {
      console.error('[LocalStorageCache] 设置缓存失败:', err);
      return false;
    }
  }

  /**
   * 获取缓存项
   * @param key 缓存键
   * @returns 缓存值或null(不存在或已过期)
   */
  get<T>(key: string): T | null {
    try {
      const storageItem = localStorage.getItem(this.getKey(key));
      
      if (!storageItem) {
        if (this.debug) console.log(`[LocalStorageCache] 缓存未命中: ${key}`);
        return null;
      }

      const item: CacheItem<T> = JSON.parse(storageItem);
      const now = Date.now();
      
      // 检查是否过期 (expiry为0表示永不过期)
      if (item.expiry !== 0 && item.expiry < now) {
        if (this.debug) {
          console.log(`[LocalStorageCache] 缓存已过期: ${key}`, {
            expiry: new Date(item.expiry).toLocaleString(),
            now: new Date(now).toLocaleString()
          });
        }
        this.remove(key);
        return null;
      }

      if (this.debug) {
        console.log(`[LocalStorageCache] 缓存命中: ${key}`, {
          value: item.value,
          age: Math.round((now - item.createdAt) / 1000) + '秒',
          expiry: item.expiry === 0 ? '永不过期' : new Date(item.expiry).toLocaleString()
        });
      }
      
      return item.value;
    } catch (err) {
      console.error('[LocalStorageCache] 获取缓存失败:', err);
      return null;
    }
  }

  /**
   * 移除缓存项
   * @param key 缓存键
   */
  remove(key: string): void {
    localStorage.removeItem(this.getKey(key));
    if (this.debug) console.log(`[LocalStorageCache] 移除缓存: ${key}`);
  }

  /**
   * 清空所有缓存
   * @param onlyExpired 是否只清除过期项目
   */
  clear(onlyExpired: boolean = false): void {
    if (onlyExpired) {
      this.clearExpired();
      return;
    }

    // 清除所有带前缀的缓存项
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith(this.prefix)) {
        localStorage.removeItem(key);
      }
    }
    
    if (this.debug) console.log('[LocalStorageCache] 清空所有缓存');
  }

  /**
   * 清除所有过期缓存
   */
  clearExpired(): void {
    const now = Date.now();
    let count = 0;

    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith(this.prefix)) {
        try {
          const item = JSON.parse(localStorage.getItem(key) || '');
          if (item.expiry !== 0 && item.expiry < now) {
            localStorage.removeItem(key);
            count++;
          }
        } catch (err) {
          // 忽略解析错误，可能是其他非缓存项
        }
      }
    }
    
    if (this.debug && count > 0) {
      console.log(`[LocalStorageCache] 清理过期缓存: 移除${count}项`);
    }
  }

  /**
   * 获取缓存统计信息
   */
  getStats(): { total: number; expired: number; valid: number; size: string } {
    const now = Date.now();
    let total = 0;
    let expired = 0;
    let valid = 0;
    let totalSize = 0;

    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith(this.prefix)) {
        total++;
        try {
          const itemStr = localStorage.getItem(key) || '';
          totalSize += itemStr.length;
          
          const item = JSON.parse(itemStr);
          if (item.expiry !== 0 && item.expiry < now) {
            expired++;
          } else {
            valid++;
          }
        } catch (err) {
          // 忽略解析错误
        }
      }
    }

    return {
      total,
      expired,
      valid,
      size: this.formatSize(totalSize)
    };
  }

  /**
   * 格式化大小
   */
  private formatSize(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * 开始自动清理
   */
  private startCleanup(interval: number): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }

    this.cleanupTimer = window.setInterval(() => {
      this.clearExpired();
    }, interval * 1000);

    if (this.debug) {
      console.log(`[LocalStorageCache] 启动自动清理，间隔: ${interval}秒`);
    }
  }

  /**
   * 停止自动清理
   */
  stopCleanup(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }

    if (this.debug) {
      console.log('[LocalStorageCache] 停止自动清理');
    }
  }

  /**
   * 更新默认过期时间
   */
  updateDefaultExpiry(ttl: number): void {
    this.defaultTTL = ttl;
    if (this.debug) {
      console.log(`[LocalStorageCache] 更新默认过期时间: ${ttl}秒`);
    }
  }
}

// 导出默认实例
export const storageCache = new LocalStorageCache({
  prefix: 'ibuddy_',
  defaultTTL: 86400, // 1天
  cleanupInterval: 3600, // 每小时清理一次
  debug: process.env.NODE_ENV === 'development'
});

export default storageCache;