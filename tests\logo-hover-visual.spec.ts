import { test, expect } from '@playwright/test';

test.describe('Logo Hover Visual', () => {
  test('logo hover should not show white or rectangular background', async ({ page }) => {
    await page.goto('http://localhost:3000');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(1000);

    // 定位logo按钮
    const logoBtn = page.locator('img[alt="iTeraBiz Logo"]');
    await expect(logoBtn).toBeVisible();

    // 悬停logo
    await logoBtn.hover();
    await page.waitForTimeout(500);

    // 截图用于人工/自动比对
    await page.screenshot({ path: 'test-results/logo-hover.png', fullPage: false });

    // 检查logo父级按钮的背景色
    const parentBtn = await logoBtn.evaluateHandle(el => el.closest('button'));
    const bg = await parentBtn.evaluate(el => window.getComputedStyle(el).backgroundColor);
    // 允许透明或完全无色，不能是白色或明显可见色
    expect(bg === 'rgba(0, 0, 0, 0)' || bg === 'transparent').toBeTruthy();
  });
}); 