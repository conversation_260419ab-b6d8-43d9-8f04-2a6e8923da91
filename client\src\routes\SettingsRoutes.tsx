import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';

/**
 * 设置模块路由配置
 * 将所有设置相关页面集中管理，实现代码分割
 * 
 * 注意：Platform API 管理页面已迁移到主路由 /platform-api
 */
const SettingsRoutes = () => {
  return (
    <Routes>
      <Route index element={<Navigate to="/settings/general" replace />} />
      <Route path="platform-api" element={<Navigate to="/platform-api" replace />} />
      <Route path="general" element={<div>General Settings</div>} />
      <Route path="*" element={<Navigate to="/settings/general" replace />} />
    </Routes>
  )
};

export default SettingsRoutes;