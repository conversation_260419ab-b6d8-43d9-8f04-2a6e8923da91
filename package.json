{"name": "iterabiz", "version": "1.0.0", "description": "iTeraBiz 微服务平台 - 智能业务助手", "private": true, "scripts": {"start": "node tools/start-all-services.js", "start:simple": "node tools/simple-start.js", "start:dev": "node tools/start-dev.js", "start:client": "cd client && npm start", "start:api-gateway": "cd api-gateway && npm run dev", "start:core-service": "cd core-service && npm run dev", "start:ai-service": "cd ai-service && npm run dev", "install:all": "npm install && cd client && npm install && cd ../api-gateway && npm install && cd ../core-service && npm install && cd ../ai-service && npm install", "build": "cd client && npm run build", "test": "cd client && npm test", "test:all": "npm run test:client && npm run test:api-gateway && npm run test:core-service && npm run test:ai-service", "test:client": "cd client && npm test -- --watchAll=false", "test:api-gateway": "cd api-gateway && npm test", "test:core-service": "cd core-service && npm test", "test:ai-service": "cd ai-service && npm test", "lint": "cd client && npm run lint", "lint:fix": "cd client && npm run lint -- --fix", "clean": "rm -rf client/build client/node_modules api-gateway/node_modules core-service/node_modules ai-service/node_modules node_modules", "clean:builds": "rm -rf client/build", "setup": "npm run install:all && npm run build", "dev": "npm start", "status": "node tools/check-services-status.js", "logs": "node tools/view-logs.js", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "dev:backend": "concurrently -k -s first -n \"gateway,core,ai,server\" -c \"cyan,green,magenta,blue\" \"cross-env PORT=3001 NODE_ENV=development npm --prefix api-gateway run dev\" \"cross-env PORT=3002 NODE_ENV=development npm --prefix core-service run dev\" \"cross-env PORT=3003 NODE_ENV=development npm --prefix ai-service run dev\" \"cross-env PORT=3004 NODE_ENV=development npm --prefix server run dev\"", "dev:all": "npm-run-all --parallel dev:backend start:client", "predev:backend": "npx kill-port 3001 3002 3003 3004"}, "workspaces": ["client", "api-gateway", "core-service", "ai-service"], "repository": {"type": "git", "url": "https://github.com/your-org/iterabiz.git"}, "keywords": ["microservices", "react", "node.js", "typescript", "ai", "saas", "business-platform", "iterabiz"], "author": "iTeraBiz Team", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "devDependencies": {"@playwright/test": "^1.53.1", "@types/node": "^24.0.7", "concurrently": "^8.2.2", "cross-env": "^7.0.3", "fast-glob": "^3.3.3", "kill-port": "^2.0.1", "nodemon": "^3.0.1", "npm-run-all": "^4.1.5", "rimraf": "^5.0.1", "wait-on": "^7.0.1"}, "dependencies": {"@types/antd": "^0.12.32", "antd": "^5.26.2", "dotenv": "^16.3.1", "express-rate-limit": "^6.7.0", "glob": "^11.0.3", "http-proxy-middleware": "^3.0.5", "next-themes": "^0.4.6", "react-icon-cloud": "^4.1.7"}}