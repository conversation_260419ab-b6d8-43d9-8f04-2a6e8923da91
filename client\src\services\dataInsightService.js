import axiosInstance from '../api/axiosInstance';

/**
 * 获取KPI指标数据
 * @param {Object} params - 查询参数 
 * @returns {Promise<Object>} - 返回KPI数据
 */
export const getKpiData = async (params = {}) => {
  try {
    const response = await axiosInstance.get('/api/insights/kpis', { params });
    return response.data;
  } catch (error) {
    console.error('获取KPI数据失败:', error.response?.data || error);
    throw error;
  }
};

/**
 * 获取预约分析数据
 * @param {Object} params - 查询参数，包含时间范围 
 * @returns {Promise<Object>} - 返回预约分析数据
 */
export const getBookingAnalytics = async (params = {}) => {
  try {
    const response = await axiosInstance.get('/api/insights/bookings', { params });
    return response.data;
  } catch (error) {
    console.error('获取预约分析数据失败:', error.response?.data || error);
    throw error;
  }
};

/**
 * 导出预约数据报告
 * @param {Object} params - 导出参数，包含时间范围和格式
 * @returns {Promise<Blob>} - 返回报告文件Blob
 */
export const exportBookingReport = async (params = {}) => {
  try {
    const response = await axiosInstance.get('/api/insights/export', {
      params,
      responseType: 'blob'
    });
    return response.data;
  } catch (error) {
    console.error('导出预约报告失败:', error.response?.data || error);
    throw error;
  }
}; 
 