/**
 * 会话存储工具函数
 */

/**
 * 存储数据到sessionStorage
 * @param key 存储键名
 * @param value 存储值（会被自动序列化为JSON）
 */
export const setSessionItem = <T>(key: string, value: T): void => {
  try {
    const serializedValue = JSON.stringify(value);
    sessionStorage.setItem(key, serializedValue);
  } catch (error) {
    console.error(`Error saving to, sessionStorage: ${error}`);
  } };

/**
 * 从sessionStorage获取数据
 * @param key 存储键名
 * @param defaultValue 默认值，当获取失败时返回
 * @returns 获取的数据（已反序列化）或默认值
 */
export const getSessionItem = <T>(key: string, defaultValue: T): T => {
  try {
    const serializedValue = sessionStorage.getItem(key);
    if (serializedValue === null) {
      return defaultValue;
    };
    return JSON.parse(serializedValue) as T;
  } catch (error) {
    console.error(`Error reading from, sessionStorage: ${error}`);
    return defaultValue;
  } };

/**
 * 从sessionStorage删除数据
 * @param key 存储键名
 */
export const removeSessionItem = (key: string): void => {
  try {
    sessionStorage.removeItem(key);
  } catch (error) {
    console.error(`Error removing from, sessionStorage: ${error}`);
  } };

/**
 * 清空sessionStorage
 */
export const clearSessionStorage = (): void => {
  try {
    sessionStorage.clear();
  } catch (error) {
    console.error(`Error clearing, sessionStorage: ${error}`);
  } }; 