import React from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Star } from 'lucide-react';

const ReviewsDemo: React.FC = () => {
  const reviews = [
    {
      id: 1, name: '<PERSON>', rating: 5, comment: 'Excellent service! The AI assistant was very helpful.', date: '2024-01-15'
    },
    {
      id: 2, name: '<PERSON>', rating: 4, comment: 'Great experience overall. Fast and efficient.', date: '2024-01-14'
    },
    {
      id: 3, name: '<PERSON>', rating: 5, comment: 'Amazing platform! Highly recommend to everyone.', date: '2024-01-13'
    }
  ];

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <Star
        key={index}
        className={`w-4 h-4 ${
          index < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
        }`}
      />
    ));
  };

  return (
    <div className="container mx-auto p-6">
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Customer Reviews</h1>
          <p className="text-muted-foreground">
            See what our customers are saying about our service
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {reviews.map((review) => (
            <Card key={review.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">{review.name}</CardTitle>
                  <div className="flex space-x-1">
                    {renderStars(review.rating)}
                  </div>
                </div>
                <CardDescription>{review.date}</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700">{review.comment}</p>
              </CardContent>
            </Card>
          ))}
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Overall Rating</CardTitle>
            <CardDescription>Based on {reviews.length} reviews</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              <div className="flex space-x-1">
                {renderStars(5)}
              </div>
              <span className="text-2xl font-bold">4.7</span>
              <span className="text-muted-foreground">out of 5</span>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default ReviewsDemo; 