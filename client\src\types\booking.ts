import { GeoLocation } from './common';

// 员工/服务人员
export interface Staff {
  id: string;
  name: string;
  position: string;
  skills: string[];
  availability: {
    schedule: Record<string, { start: string; end: string; available: boolean }>;
    exceptions: Array<{ date: string; available: boolean; hours?: { start: string; end: string } }>;
  };
  active: boolean;
  maxAppointmentsPerDay?: number;
  serviceEfficiency?: Record<string, number>; // 服务类型对应的效率系数
  restTimeBetweenAppointments?: number; // 预约间隔休息时间(分钟)
  location?: GeoLocation; // 员工当前/默认位置
}

// 团队
export interface Team {
  id: string;
  name: string;
  description: string;
  members: string[]; // Staff IDs
  leader: string; // Staff ID
  serviceTypes: string[]; // 能处理的服务类型IDs
  clients?: string[]; // 负责的客户IDs（针对onsite场景）
  active: boolean;
  serviceCapabilities?: ServiceCapability[]; // 能提供的服务类型及详情
  serviceArea?: ServiceArea; // 服务覆盖区域
  maxDailyAppointments?: number;
  dispatchStrategy?: 'nearest' | 'load-balanced' | 'skill-priority';
}

// 服务能力
export interface ServiceCapability {
  serviceTypeId: string;
  efficiency: number; // 效率系数，影响服务时长
  maxParallelServices: number; // 最大并行服务数
  priority: number; // 专长优先级
}

// 服务区域
export interface ServiceArea {
  baseLocation: GeoLocation; // 团队基地位置
  radius: number; // 服务半径(公里)
  regions: string[]; // 区域编码列表
  travelSpeedFactor: number; // 交通速度系数
}

// 预约
export interface Appointment {
  id: string;
  customerName: string;
  customerId?: string;
  customerPhone?: string;
  serviceTypeId: string;
  serviceTypeName: string;
  date: string;
  startTime: string;
  endTime: string;
  status: AppointmentStatus;
  location?: GeoLocation; // 服务地点
  assignedTeamId?: string;
  assignedStaffId?: string;
  notes?: string;
  estimatedServiceTime?: number; // 预计服务时间(分钟)
  travelTimeEstimates?: Record<string, number>; // 各团队预计到达时间
  priority?: number; // 预约优先级
  constraints?: AppointmentConstraint[]; // 特殊约束条件
  createdAt: string;
  updatedAt: string;
}

// 预约状态
export type AppointmentStatus =
  | 'pending'    // 待确认
  | 'confirmed'  // 已确认
  | 'in_progress'// 进行中
  | 'completed'  // 已完成
  | 'cancelled'  // 已取消
  | 'no_show';   // 未到场

// 约束条件
export interface AppointmentConstraint {
  type: 'time-window' | 'specific-staff' | 'equipment' | 'skill-level';
  value: any;
}

// 队列项
export interface QueueItem {
  id: string;
  number: string;
  customerName: string;
  customerId?: string;
  customerPhone?: string;
  serviceTypeId: string;
  serviceTypeName: string;
  waitTime: number; // 以分钟为单位
  status: QueueItemStatus;
  checkInTime: string;
  assignedStaffId?: string;
  priority?: number;
  notes?: string;
}

// 队列项状态
export type QueueItemStatus =
  | 'waiting'   // 等待中
  | 'serving'   // 服务中
  | 'completed' // 已完成
  | 'no_show'   // 未到场
  | 'cancelled';// 已取消

// AI调度设置
export interface AISchedulingSettings {
  enabled: boolean;
  prioritizationFactors: {
    customerImportance: number; // 客户重要性权重 (1-5)
    serviceUrgency: number; // 服务紧急性权重 (1-5)
    teamEfficiency: number; // 团队效率权重 (1-5)
    travelDistance: number; // 路程距离权重 (1-5)
    timeSlotOptimization: number; // 时间槽优化权重 (1-5)
  };
  constraints: {
    maxTravelTime: number; // 最大允许行程时间(分钟)
    minRestTime: number; // 最小休息时间(分钟)
    serviceQualityThreshold: number; // 服务质量阈值 (1-10)
  };
  optimizationGoals: {
    maximizeTeamUtilization: boolean; // 最大化团队利用率
    minimizeTravelTime: boolean; // 最小化行程时间
    balanceWorkload: boolean; // 平衡工作负载
    prioritizeCustomerPreference: boolean; // 优先考虑客户偏好
  };
}