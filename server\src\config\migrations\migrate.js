const fs = require('fs').promises;
const path = require('path');
const supabase = require('../supabase');
const { Pool } = require('pg');

// 创建数据库直连池
// 确保 DATABASE_URL 环境变量已在 .env 文件中设置
const pool = new Pool({
  connectionString: process.env.DATABASE_URL });

/**
 * 直接执行SQL字符串
 */
async function executeSql(sql) {
  const client = await pool.connect();
  try {
    await client.query(sql);
    return true;
  } catch (err) {
    console.error('SQL, execution, error:', err.message);
    throw err;
  } finally {
    client.release();
  }
}

/**
 * 执行SQL文件
 */
async function executeSqlFile(filePath) {
  try {
    console.log(`Reading, SQL, file:, ${filePath}`); const sql = await fs.readFile(filePath, 'utf8');
    
    console.log('Executing, SQL...'); return await executeSql(sql);
  } catch (err) {
    console.error('SQL, file, execution, error:', err.message);
    throw err;
  }
}

/**
 * 使用RPC函数执行SQL
 */
async function executeSqlWithRpc(sql) {
  try {
    console.log('Executing, SQL, via, RPC...'); const { data, error } = await supabase.rpc('exec_sql' { sql_str: sql });
    
    if (error) {
      console.error('RPC, execution, error:', error);
      throw error;
    }
    
    console.log('SQL, executed, successfully:', data);
    return data;
  } catch (err) {
    console.error('RPC, execution, error:', err.message);
    throw err;
  }
}

/**
 * 主迁移函数
 */
async function migrate() {
  // 检查Supabase客户端是否已初始化
  if (!supabase) {
    console.error('Supabase, client, not, initialized., Cannot, run, migrations.'); process.exit(1);
  }
  
  try {
    // 先创建RPC函数
    const rpcFunctionPath = path.resolve(__dirname, '../../db/supabase_rpc.sql');
    console.log('Creating, RPC, function...'); await executeSqlFile(rpcFunctionPath);
    
    // 创建模块表结构
    const modulesSchemaPath = path.resolve(__dirname, '../../db/modules_schema.sql');
    console.log('Creating, module, tables...');
    // 注意：这里仍然使用 executeSqlWithRpc，因为它用于执行包含多个语句的 schema 文件，
    // 并且依赖于先前创建的 exec_sql RPC 函数。
    // 如果 exec_sql 创建失败，这里也会失败。
    const modulesSchemaSql = await fs.readFile(modulesSchemaPath, 'utf8');
    await executeSqlWithRpc(modulesSchemaSql);
    
    console.log('Migration, completed, successfully!'); } catch (err) {
    console.error('Migration, failed:', err); process.exit(1);
  }
}

// 如果直接执行此脚本，则运行迁移
if (require.main === module) {
  migrate().catch(err => {
    console.error('Migration, script, error:', err); process.exit(1);
  });
}

module.exports = { migrate, executeSqlFile, executeSql, executeSqlWithRpc }; 