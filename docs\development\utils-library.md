# iBuddy2 工具函数库使用指南

本文档介绍了iBuddy2项目中共享工具函数库的结构和使用方法，旨在减少代码重复并提高开发效率。

## 工具函数库结构

工具函数库按功能分为以下几个主要模块：

```
src/utils/
├── formatting/       # 数据格式化工具
│   ├── string.ts     # 字符串格式化
│   ├── number.ts     # 数字格式化
│   └── index.ts      # 统一导出
├── validation/       # 数据验证工具
├── storage/          # 本地存储工具
│   ├── local.ts      # localStorage操作
│   ├── session.ts    # sessionStorage操作
│   └── index.ts      # 统一导出
├── api/              # API请求工具
│   ├── request.ts    # 请求发送工具
│   ├── response.ts   # 响应处理工具
│   └── index.ts      # 统一导出
└── index.ts          # 主入口，统一导出所有工具
```

## 导入与使用

### 基本用法

你可以通过两种方式导入工具函数：

#### 1. 通过主入口导入

```typescript
// 导入多个函数
import { formatCurrency, truncate, setLocalItem } from '@utils';

// 使用函数
const price = formatCurrency(100);
const shortText = truncate('This is a long text', 10);
setLocalItem('user', { id: 1, name: 'John' });
```

#### 2. 通过特定模块导入

```typescript
// 仅导入特定模块的函数
import { formatCurrency, formatPercent } from '@utils/formatting';
import { setLocalItem, getLocalItem } from '@utils/storage';
```

### 具体模块用法

#### 格式化工具 (@utils/formatting)

```typescript
import { 
  // 字符串格式化
  capitalize,     // 首字母大写
  truncate,       // 文本截断
  slugify,        // 转换为URL友好格式
  formatIdNumber, // 身份证号脱敏
  formatPhone,    // 电话号码脱敏
  
  // 数字格式化
  formatCurrency, // 货币格式化
  formatPercent,  // 百分比格式化
  formatFileSize, // 文件大小格式化
  formatCompact   // 数字简化(1K, 1M等)
} from '@utils/formatting';

// 示例
capitalize('hello');       // "Hello"
truncate('Long text', 5);  // "Long..."
formatCurrency(1000);      // "￥1,000.00"
formatFileSize(1024 * 1024); // "1.00 MB"
```

#### 存储工具 (@utils/storage)

```typescript
import {
  // localStorage
  setLocalItem,
  getLocalItem,
  removeLocalItem,
  clearLocalStorage,
  
  // sessionStorage
  setSessionItem,
  getSessionItem,
  removeSessionItem,
  clearSessionStorage
} from '@utils/storage';

// 示例
setLocalItem('preferences', { theme: 'dark' });
const prefs = getLocalItem('preferences', { theme: 'light' });
```

#### API工具 (@utils/api)

```typescript
import {
  get,
  post,
  put,
  del,
  apiClient,
  normalizeError,
  getErrorMessage
} from '@utils/api';

// 示例
async function fetchUsers() {
  try {
    // 类型化请求
    const users = await get<User[]>('/users');
    return users;
  } catch (error) {
    // 错误处理
    const normalizedError = normalizeError(error);
    console.error(getErrorMessage(error));
    throw normalizedError;
  }
}
```

## 扩展工具库

### 添加新函数

如需添加新的工具函数，请遵循以下步骤：

1. 确定函数应该属于哪个模块，或创建新模块
2. 在相应文件中添加函数，附带JSDoc注释
3. 确保函数在模块的index.ts中导出
4. 如果是新模块，确保在主utils/index.ts中导出

### 创建新模块示例

如需添加日期工具模块：

1. 创建目录和文件：
```
src/utils/date/
├── format.ts   # 日期格式化
├── calc.ts     # 日期计算
└── index.ts    # 统一导出
```

2. 在utils/index.ts中添加导出：
```typescript
// 导出日期工具
export * from './date';
```

## 最佳实践

- 使用TypeScript类型，确保类型安全
- 为所有函数添加JSDoc注释，包括参数和返回值说明
- 优先使用现有工具函数，避免重复实现
- 保持函数纯净，避免副作用
- 对复杂工具函数编写单元测试 