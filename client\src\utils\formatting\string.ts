/**
 * 字符串格式化工具函数
 */

/**
 * 首字母大写
 * @param str 输入字符串
 * @returns 首字母大写的字符串
 */
export const capitalize = (str: string): string => {
  if (!str) return str;
  return str.charAt(0).toUpperCase() + str.slice(1);
};

/**
 * 截断文本并添加省略号
 * @param text 输入文本
 * @param maxLength 最大长度
 * @returns 截断后的文本
 */
export const truncate = (text: string, maxLength: number): string => {
  if (!text) return '';
  if (text.length <= maxLength) return text;
  return `${text.slice(0, maxLength)}...`;
};

/**
 * 转换为URL友好的slug
 * @param text 输入文本
 * @returns slug格式文本
 */
export const slugify = (text: string): string => {
  if (!text) return '';
  return text
    .toLowerCase()
    .replace(/\s+/g, '-')
    .replace(/[^\w-]+/g, '')
    .replace(/--+/g, '-')
    .replace(/^-+/, '')
    .replace(/-+$/, '');
};

/**
 * 格式化身份证号（显示前4位和后4位，中间用星号替代）
 * @param idNumber 身份证号
 * @returns 格式化后的身份证号
 */
export const formatIdNumber = (idNumber: string): string => {
  if (!idNumber) return '';
  if (idNumber.length < 8) return idNumber;
  return `${idNumber.slice(0, 4)}****${idNumber.slice(-4)}`;
};

/**
 * 格式化电话号码（显示前3位和后4位，中间用星号替代）
 * @param phone 电话号码
 * @returns 格式化后的电话号码
 */
export const formatPhone = (phone: string): string => {
  if (!phone) return '';
  if (phone.length < 7) return phone;
  return `${phone.slice(0, 3)}****${phone.slice(-4)}`;
}; 