import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import ContentTemplatesTab from '../../src/components/agents/content-generator/ContentTemplatesTab';
import * as contentGeneratorService from '../../src/services/contentGeneratorService';

// 模拟服务
jest.mock('../../src/services/contentGeneratorService', () => ({
  getTemplates: jest.fn(),
  createTemplate: jest.fn(),
  updateTemplate: jest.fn(),
  deleteTemplate: jest.fn()
}));

// 模拟数据
const mockTemplates = [
  {
    id: 'template_1',
    name: '销售邮件',
    description: '用于发送产品促销邮件',
    prompt: '写一封推广{{产品}}的邮件，针对{{目标客户}}，强调{{优势}}',
    createdAt: '2023-01-15T12:00:00Z',
    updatedAt: '2023-04-18T09:30:00Z'
  },
  {
    id: 'template_2',
    name: '博客文章',
    description: '为公司博客创建新文章',
    prompt: '写一篇关于{{主题}}的博客文章，包含{{关键点}}，目标读者是{{读者}}',
    createdAt: '2023-02-20T15:45:00Z',
    updatedAt: '2023-02-20T15:45:00Z'
  }
];

describe('ContentTemplatesTab组件', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // 默认模拟返回模板列表
    (contentGeneratorService.getTemplates as jest.Mock).mockResolvedValue(mockTemplates);
  });

  test('渲染模板列表和添加按钮', async () => {
    render(<ContentTemplatesTab />);
    
    // 验证加载模板的API被调用
    expect(contentGeneratorService.getTemplates).toHaveBeenCalled();
    
    // 验证模板列表和添加按钮在DOM中
    await waitFor(() => {
      expect(screen.getByText('销售邮件')).toBeInTheDocument();
      expect(screen.getByText('博客文章')).toBeInTheDocument();
    });
    
    // 验证添加按钮
    expect(screen.getByRole('button', { name: /add|create|new/i })).toBeInTheDocument();
  });

  test('点击"添加模板"按钮显示表单', async () => {
    render(<ContentTemplatesTab />);
    
    // 等待加载完成
    await waitFor(() => {
      expect(screen.getByText('销售邮件')).toBeInTheDocument();
    });
    
    // 点击添加按钮
    const addButton = screen.getByRole('button', { name: /add|create|new/i });
    fireEvent.click(addButton);
    
    // 检查表单是否显示
    await waitFor(() => {
      expect(screen.getByText(/create template|new template/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/name/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/prompt/i)).toBeInTheDocument();
    });
  });

  test('新增模板调用创建API', async () => {
    // 模拟创建成功
    const newTemplate = {
      id: 'new_template',
      name: '新模板',
      description: '测试描述',
      prompt: '测试提示',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    (contentGeneratorService.createTemplate as jest.Mock).mockResolvedValue(newTemplate);
    
    render(<ContentTemplatesTab />);
    
    // 等待加载完成并点击添加按钮
    await waitFor(() => screen.getByText('销售邮件'));
    fireEvent.click(screen.getByRole('button', { name: /add|create|new/i }));
    
    // 填写表单
    await waitFor(() => {
      const nameInput = screen.getByLabelText(/name/i);
      const descInput = screen.getByLabelText(/description/i);
      const promptInput = screen.getByLabelText(/prompt/i);
      
      fireEvent.change(nameInput, { target: { value: '新模板' } });
      fireEvent.change(descInput, { target: { value: '测试描述' } });
      fireEvent.change(promptInput, { target: { value: '测试提示' } });
      
      // 提交表单
      const submitButton = screen.getByRole('button', { name: /save|submit|create/i });
      fireEvent.click(submitButton);
    });
    
    // 验证API调用
    await waitFor(() => {
      expect(contentGeneratorService.createTemplate).toHaveBeenCalledWith({
        name: '新模板',
        description: '测试描述',
        prompt: '测试提示'
      });
    });
    
    // 验证成功后刷新列表
    await waitFor(() => {
      expect(contentGeneratorService.getTemplates).toHaveBeenCalledTimes(2);
    });
  });

  test('删除模板调用删除API', async () => {
    (contentGeneratorService.deleteTemplate as jest.Mock).mockResolvedValue({ success: true });
    
    render(<ContentTemplatesTab />);
    
    // 等待加载完成
    await waitFor(() => screen.getByText('销售邮件'));
    
    // 点击第一个模板的删除按钮
    const deleteButtons = screen.getAllByRole('button', { name: /delete|remove/i });
    fireEvent.click(deleteButtons[0]);
    
    // 检查确认对话框并确认
    await waitFor(() => {
      const confirmButton = screen.getByRole('button', { name: /confirm|yes|ok/i });
      fireEvent.click(confirmButton);
    });
    
    // 验证删除API调用
    await waitFor(() => {
      expect(contentGeneratorService.deleteTemplate).toHaveBeenCalledWith('template_1');
    });
    
    // 验证成功后刷新列表
    await waitFor(() => {
      expect(contentGeneratorService.getTemplates).toHaveBeenCalledTimes(2);
    });
  });
}); 