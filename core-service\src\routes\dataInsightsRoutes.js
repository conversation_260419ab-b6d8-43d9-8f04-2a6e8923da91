/**
 * Data Insights Routes
 * 处理所有数据分析和KPI相关的请求
 */
const express = require('express');
const router = express.Router();

/**
 * @route GET /data-insights/kpis
 * @desc 获取主要KPI指标
 * @access Private
 */
router.get('/kpis', async (req, res) => {
  try {
    req.logger?.info(`Request to GET /data-insights/kpis received in core-service`, { requestId: req.id });
    
    const supabase = req.app.locals.supabase;
    
    if (!supabase) {
      return res.status(500).json({
        message: '数据库服务不可用'
      });
    }
    
    // 从agents表获取数据
    const { data: agents, error } = await supabase
      .from('agents')
      .select('*');
    
    if (error) {
      req.logger?.error('Error fetching agents for KPIs', { error, requestId: req.id });
      return res.status(500).json({ message: 'Error fetching KPI data', error: error.message });
    }
    
    // 生成KPI数据
    // 这里只是示例，您可以根据实际需求修改计算逻辑
    const kpis = [
      {
        id: 'total_agents',
        title: '总代理数',
        value: agents.length,
        change: '+5%',
        changeType: 'increase', // 'increase' 或 'decrease'
        period: '本月'
      },
      {
        id: 'active_agents',
        title: '活跃代理',
        value: agents.filter(agent => agent.status === 'active').length,
        change: '+3%',
        changeType: 'increase',
        period: '本月'
      },
      {
        id: 'pending_tasks',
        title: '待处理任务',
        value: 24, // 示例值，实际应从相应表中查询
        change: '-2%',
        changeType: 'decrease',
        period: '本周'
      },
      {
        id: 'completion_rate',
        title: '任务完成率',
        value: '87%', // 示例值，实际应从相应表中查询
        change: '+1.2%',
        changeType: 'increase',
        period: '本周'
      }
    ];
    
    res.json(kpis);
  } catch (err) {
    req.logger?.error('Unexpected error processing KPI data', { error: err.message, requestId: req.id });
    res.status(500).json({ message: 'Server error', error: err.message });
  }
});

/**
 * @route GET /data-insights/reports/:reportName
 * @desc 获取指定报告
 * @access Private
 */
router.get('/reports/:reportName', async (req, res) => {
  try {
    const { reportName } = req.params;
    const { startDate, endDate, segment } = req.query;
    
    req.logger?.info(`Request for report ${reportName} received`, { 
      reportName, startDate, endDate, segment, requestId: req.id 
    });
    
    // 这里添加报表逻辑，根据reportName提供不同的报表数据
    // 示例返回
    res.json({
      name: reportName,
      title: `${reportName.charAt(0).toUpperCase() + reportName.slice(1)} 报告`,
      dateRange: { startDate, endDate },
      segment,
      data: [] // 真实数据应从数据库获取
    });
  } catch (err) {
    req.logger?.error('Error generating report', { error: err.message, requestId: req.id });
    res.status(500).json({ message: 'Error generating report', error: err.message });
  }
});

/**
 * @route GET /data-insights/trends
 * @desc 获取趋势数据
 * @access Private
 */
router.get('/trends', async (req, res) => {
  try {
    const { type, period } = req.query;
    
    req.logger?.info(`Request for trends received`, { type, period, requestId: req.id });
    
    // 示例趋势数据
    const trends = [
      {
        id: 'activity',
        title: '代理活动',
        data: [
          { date: '2025-05-01', value: 24 },
          { date: '2025-05-02', value: 28 },
          { date: '2025-05-03', value: 32 },
          { date: '2025-05-04', value: 36 },
          { date: '2025-05-05', value: 30 }
        ]
      }
    ];
    
    res.json(trends);
  } catch (err) {
    req.logger?.error('Error fetching trends', { error: err.message, requestId: req.id });
    res.status(500).json({ message: 'Error fetching trends', error: err.message });
  }
});

module.exports = router; 