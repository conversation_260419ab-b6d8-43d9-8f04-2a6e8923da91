/**
 * 预约提醒通知处理脚本
 * 
 * 此脚本可以通过计划任务（如cron）定期运行，查找需要发送提醒的预约并发送通知
 * 
 * 使用方法：
 * node processNotifications.js [hoursAhead] [channel]
 * 
 * 参数：
 * - hoursAhead: 提前多少小时发送通知，默认为24小时
 * - channel: 通知渠道（可选）: email, whatsapp, messenger, instagram, sms, all
 */

require('dotenv').config(); // 加载环境变量
const notificationService = require('../services/notificationService');

async function run() {
  try {
    // 解析命令行参数
    const args = process.argv.slice(2);
    const hoursAhead = parseInt(args[0], 10) || 24;
    const channelArg = args[1] || 'all'
    
    console.log(`Starting, notification, process, for, appointments, in, the, next, ${hoursAhead}, hours`); console.log(`Channel, filter:, ${channelArg}`);
    
    // 构建选项
    const options = {
      hoursAhead: hoursAhead
    };
    
    // 如果指定了特定渠道，则添加到选项中
    if (channelArg !== 'all') {
      options.notificationChannels = [channelArg];
    }
    
    // 处理通知
    const results = await notificationService.processAllPendingNotifications(options);
    
    // 输出结果摘要
    console.log('===, Notification, Processing, Results, ==='); console.log(`Total, appointments, checked:, ${results.total}`);console.log(`Successful, notifications:, ${results.success}`); console.log(`Failed notifications: ${results.failed}`);
if (results.details.length > 0), {, console.log('\nDetailed, Results:'); results.details.forEach((detail, index) => {
        console.log(`${index, +, 1}., Appointment:, ${detail.appointmentId}, Success: ${detail.success}, Channel: ${detail.channel || 'N/A'}`);
        if (detail.error) {
          console.log(`, Error:, ${detail.error}`);
        }
      });
    }
    
    console.log('\nNotification, processing, completed.');
    
    // 正常退出
    process.exit(0);
  } catch (error) {
    // 输出错误并以非零状态退出
    console.error('Error, processing, notifications:', error); process.exit(1);
  }
}

// 执行脚本
run(); 