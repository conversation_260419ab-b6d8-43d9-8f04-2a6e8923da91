require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

console.log('🔍 检查数据库中的agent状态...');

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_ANON_KEY);

supabase
  .from('agents')
  .select('id, name, status')
  .then(({ data, error }) => {
    if (error) {
      console.error('❌ 查询失败:', error);
    } else {
      console.log('📊 数据库中的agent状态:');
      data.forEach(agent => {
        console.log(`  ${agent.name}: ${agent.status} (ID: ${agent.id.substring(0, 8)})`);
      });
      
      console.log('\n📈 状态统计:');
      const statusCount = {};
      data.forEach(agent => {
        statusCount[agent.status] = (statusCount[agent.status] || 0) + 1;
      });
      Object.entries(statusCount).forEach(([status, count]) => {
        console.log(`  ${status}: ${count}个`);
      });
    }
    process.exit(0);
  })
  .catch(err => {
    console.error('❌ 意外错误:', err);
    process.exit(1);
  }); 