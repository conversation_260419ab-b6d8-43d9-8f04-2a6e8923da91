#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');

console.log('╔══════════════════════════════════════════╗');
console.log('║       iTeraBiz Core Service 测试         ║');
console.log('╚══════════════════════════════════════════╝');

// 启动Core Service
console.log('[测试] 启动 Core Service...');

const coreServicePath = path.join(__dirname, '../core-service');
const coreService = spawn('npm.cmd', ['run', 'dev'], {
  cwd: coreServicePath,
  stdio: 'inherit',
  shell: true
});

// 处理退出
process.on('SIGINT', () => {
  console.log('\n[退出] 停止 Core Service...');
  coreService.kill('SIGTERM');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n[退出] 停止 Core Service...');
  coreService.kill('SIGTERM');
  process.exit(0);
});

coreService.on('exit', (code) => {
  console.log(`[Core Service] 退出，代码: ${code}`);
  process.exit(code);
}); 