import React from 'react';

interface ArrowRightIconProps {
  
  className?: string;
  size?: number;
  
};

export const ArrowRightIcon: React.FC<ArrowRightIconProps> = ({ 
  className = '' 
  size = 20 
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
      aria-hidden="true"
    >
      <path d="M5 12h14" />
      <path d="m12 5 7 7-7 7" />
    </svg>
  );
};

export default ArrowRightIcon;