﻿/* eslint-disable unicode-bom */
import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

interface CustomReportBuilderProps {
  onClose?: () => void;
  onSave?: (report: any) => void;
}

const CustomReportBuilder: React.FC<CustomReportBuilderProps> = ({ onClose, onSave }) => {
  const [reportName, setReportName] = useState('');

  const handleSave = () => {
    if (reportName.trim()) {
      onSave?.({
        name: reportName,
        createdAt: new Date().toISOString()
      });
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Custom Report Builder</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <Input
          placeholder="Report name"
          value={reportName}
          onChange={(e) => setReportName(e.target.value)}
        />
        <div className="flex gap-2">
          <Button onClick={handleSave} disabled={!reportName.trim()}>
            Save Report
          </Button>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default CustomReportBuilder;
