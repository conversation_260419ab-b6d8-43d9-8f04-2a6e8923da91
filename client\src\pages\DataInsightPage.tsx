import { useState, useEffect } from 'react';
// import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, <PERSON><PERSON>Trigger, Ta<PERSON>Content } from '@/components/ui/tabs';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Download, RefreshCw } from 'lucide-react';
import { KpiCardGroup } from '@/components/insights/KpiCardGroup';
import { TimeRangeSelector, TimeRangeType } from '@/components/insights/TimeRangeSelector';
import { AgentPerformanceAnalysis } from '@/components/insights/AgentPerformanceAnalysis';
import { UserEngagementAnalysis } from '@/components/insights/UserEngagementAnalysis';
import { SystemOperationsAnalysis } from '@/components/insights/SystemOperationsAnalysis';
import { BookingAnalysis } from '@/components/insights/BookingAnalysis';
// import { EnhancedAnalytics } from '@/components/insights/EnhancedAnalytics';
// import { AllAgentsActivityLogs } from '@/components/insights/AllAgentsActivityLogs';
// import { getKPIs } from '@/lib/insights/kpis';

function DataInsightPage() {
  const [activeTab, setActiveTab] = useState('overview');
  const [timeRange, setTimeRange] = useState<TimeRangeType>('last7days');
  const [customDateRange, setCustomDateRange] = useState<{ from: Date; to: Date } | undefined>();
  const [kpis, setKpis] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // 处理时间范围变更
  const handleTimeRangeChange = (value: TimeRangeType, range?: { from: Date; to: Date }) => {
    setTimeRange(value);
    setCustomDateRange(range);
    // 重新加载数据
    fetchData();
  };

  // 获取KPI数据
  const fetchData = async () => {
    setIsLoading(true);
    try {
      // 实际开发中应该传递时间范围参数
      // const data = await getKPIs({ timeRange, customDateRange });
      
      // 模拟数据
      setTimeout(() => {
        setKpis([
          {
            id: 'total_users',
            title: '总用户数',
            value: '10,483',
            change: {
              value: '+12.8%',
              type: 'positive',
            },
            unit: 'vs 上月',
          },
          {
            id: 'active_users',
            title: '月活跃用户',
            value: '6,852',
            change: {
              value: '+5.3%',
              type: 'positive',
            },
            unit: 'vs 上月',
          },
          {
            id: 'session_duration',
            title: '平均会话时长',
            value: '5m 23s',
            change: {
              value: '+0.8%',
              type: 'positive',
            },
            unit: 'vs 上月',
          },
          {
            id: 'agent_usage',
            title: '智能代理使用量',
            value: '3,617',
            change: {
              value: '+24.5%',
              type: 'positive',
            },
            unit: 'vs 上月',
          },
        ]);
        setIsLoading(false);
      }, 800);
    } catch (error) {
      console.error('Error fetching KPI data:', error);
      setIsLoading(false);
    }
  };

  // 首次加载时获取数据
  useEffect(() => {
    fetchData();
  }, []);

  // 刷新数据
  const handleRefresh = () => {
    fetchData();
  };

  // 导出报告
  const handleExportReport = () => {
    alert('导出报告功能将在后续版本实现');
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">数据洞察</h1>
          <p className="text-muted-foreground">
            分析系统运行状况和用户行为指标
          </p>
        </div>
        <div className="flex flex-col sm:flex-row gap-2">
          <TimeRangeSelector
            value={timeRange}
            customRange={customDateRange}
            onChange={handleTimeRangeChange}
          />
          <div className="flex gap-2">
            <Button variant="outline" size="icon" onClick={handleRefresh} title="刷新数据">
              <RefreshCw className="h-4 w-4" />
            </Button>
            <Button onClick={handleExportReport}>
              <Download className="mr-2 h-4 w-4" />
              导出报告
            </Button>
          </div>
        </div>
      </div>

      {/* KPI 卡片 */}
      <KpiCardGroup kpis={kpis} isLoading={isLoading} />
      
      {/* 主要内容区域 */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">系统概览</TabsTrigger>
          <TabsTrigger value="agents">智能代理分析</TabsTrigger>
          <TabsTrigger value="users">用户分析</TabsTrigger>
          <TabsTrigger value="bookings">预约数据</TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview" className="space-y-6 mt-6">
          <SystemOperationsAnalysis timeRange={timeRange} />
        </TabsContent>
        
        <TabsContent value="agents" className="space-y-6 mt-6">
          <AgentPerformanceAnalysis timeRange={timeRange} />
        </TabsContent>
        
        <TabsContent value="users" className="space-y-6 mt-6">
          <UserEngagementAnalysis timeRange={timeRange} />
        </TabsContent>
        
        <TabsContent value="bookings" className="space-y-6 mt-6">
          <BookingAnalysis timeRange={timeRange} />
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default DataInsightPage;