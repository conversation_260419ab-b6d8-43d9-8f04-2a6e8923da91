/**
 * API响应处理工具函数
 */
import { AxiosError } from 'axios';

/**
 * API错误接口
 */
export interface ApiError {
  status: number;
  message: string;
  errors?: Record<string, string[]>;
  data?: any;
}

/**
 * 标准化API错误
 * @param error Axios错误对象
 * @returns 标准化的API错误对象
 */
export const normalizeError = (error: AxiosError): ApiError => {
  // 默认错误
  const defaultError: ApiError = {
    status: 500,
    message: '未知错误，请稍后重试'
  };

  if (!error.response) {
    // 网络错误
    return {
      status: 0,
      message: '网络连接错误，请检查您的网络连接'
    };
  }

  const { status, data } = error.response;

  // 常见HTTP状态码错误信息
  const statusMessages: Record<number, string> = {
    400: '请求参数错误',
    401: '未授权，请重新登录',
    403: '拒绝访问',
    404: '请求的资源不存在',
    405: '请求方法不允许',
    408: '请求超时',
    409: '资源冲突',
    422: '请求参数验证失败',
    429: '请求次数过多，请稍后再试',
    500: '服务器内部错误',
    502: '网关错误',
    503: '服务不可用',
    504: '网关超时'
  };

  // 处理不同格式的错误响应
  if (typeof data === 'object' && data !== null) {
    // 使用类型断言将data转换为Record<string, any>类型
    const responseData = data as Record<string, any>;
    
    // 处理标准API错误格式
    if (responseData.message || responseData.msg) {
      return {
        status,
        message: responseData.message || responseData.msg || statusMessages[status] || defaultError.message,
        errors: responseData.errors,
        data: responseData.data
      };
    }
    
    // 处理表单验证错误
    if (responseData.errors && typeof responseData.errors === 'object') {
      const firstError = Object.values(responseData.errors)[0];
      const errorMessage = Array.isArray(firstError) ? firstError[0] : String(firstError);
      
      return {
        status,
        message: errorMessage || statusMessages[status] || defaultError.message,
        errors: responseData.errors
      };
    }
  }

  // 返回状态码对应的默认消息
  return {
    status,
    message: statusMessages[status] || defaultError.message
  };
};

/**
 * 从错误对象中提取用户友好的错误消息
 * @param error 错误对象（可以是任何类型）
 * @returns 用户友好的错误消息
 */
export const getErrorMessage = (error: any): string => {
  if (error instanceof Error) {
    return error.message;
  }
  
  if (typeof error === 'string') {
    return error;
  }
  
  if (typeof error === 'object' && error !== null) {
    const errorObj = error as Record<string, any>;
    if (errorObj.message) {
      return errorObj.message;
    }
    
    if (errorObj.msg) {
      return errorObj.msg;
    }
  }
  
  return '发生未知错误，请稍后重试';
}; 