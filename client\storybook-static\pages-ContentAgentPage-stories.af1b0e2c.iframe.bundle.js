"use strict";(self.webpackChunkclient=self.webpackChunkclient||[]).push([[241],{"./src/pages/ContentAgentPage.stories.jsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{Default:()=>Default,__namedExportsOrder:()=>__namedExportsOrder,default:()=>ContentAgentPage_stories});var react=__webpack_require__("./node_modules/react/index.js"),es_select=__webpack_require__("./node_modules/antd/es/select/index.js"),input=__webpack_require__("./node_modules/antd/es/input/index.js"),tabs=__webpack_require__("./node_modules/antd/es/tabs/index.js"),card=__webpack_require__("./node_modules/antd/es/card/index.js"),es_button=__webpack_require__("./node_modules/antd/es/button/index.js"),es_form=__webpack_require__("./node_modules/antd/es/form/index.js"),message=__webpack_require__("./node_modules/antd/es/message/index.js"),row=__webpack_require__("./node_modules/antd/es/row/index.js"),col=__webpack_require__("./node_modules/antd/es/col/index.js"),es_radio=__webpack_require__("./node_modules/antd/es/radio/index.js"),slider=__webpack_require__("./node_modules/antd/es/slider/index.js"),spin=__webpack_require__("./node_modules/antd/es/spin/index.js"),tag=__webpack_require__("./node_modules/antd/es/tag/index.js"),styled_components_browser_esm=__webpack_require__("./node_modules/styled-components/dist/styled-components.browser.esm.js"),SaveOutlined=__webpack_require__("./node_modules/@ant-design/icons/es/icons/SaveOutlined.js"),CheckOutlined=__webpack_require__("./node_modules/@ant-design/icons/es/icons/CheckOutlined.js"),CopyOutlined=__webpack_require__("./node_modules/@ant-design/icons/es/icons/CopyOutlined.js"),HistoryOutlined=__webpack_require__("./node_modules/@ant-design/icons/es/icons/HistoryOutlined.js"),jsx_runtime=__webpack_require__("./node_modules/react/jsx-runtime.js");const{Option}=es_select.A,{TextArea}=input.A,{TabPane}=tabs.A,PageContainer=styled_components_browser_esm.Ay.div`
  padding: 24px;
`,PageTitle=styled_components_browser_esm.Ay.h1`
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 8px;
`,PageDescription=styled_components_browser_esm.Ay.p`
  color: #666;
  font-size: 14px;
  margin-bottom: 24px;
`,StyledCard=(0,styled_components_browser_esm.Ay)(card.A)`
  margin-bottom: 24px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  border-radius: 8px;
`,ContentWrapper=styled_components_browser_esm.Ay.div`
  white-space: pre-wrap;
  line-height: 1.6;
`,ActionButton=(0,styled_components_browser_esm.Ay)(es_button.Ay)`
  margin-right: 8px;
`,ContentHistory=styled_components_browser_esm.Ay.div`
  max-height: 400px;
  overflow-y: auto;
`,HistoryItem=styled_components_browser_esm.Ay.div`
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  &:hover {
    background: #f5f5f5;
  }
`,HistoryDate=styled_components_browser_esm.Ay.div`
  font-size: 12px;
  color: #999;
  margin-top: 4px;
`;function ContentAgentPage(){const[form]=es_form.A.useForm(),[loading,setLoading]=(0,react.useState)(!1),[generatedContent,setGeneratedContent]=(0,react.useState)(""),[copiedStatus,setCopiedStatus]=(0,react.useState)(!1),[activeTab,setActiveTab]=(0,react.useState)("1"),[history,setHistory]=(0,react.useState)([]),languages=[{value:"en",label:"英语 (English)"},{value:"zh",label:"中文 (Chinese)"},{value:"ms",label:"马来语 (Bahasa Melayu)"},{value:"ja",label:"日语 (Japanese)"},{value:"ko",label:"韩语 (Korean)"}],contentTypes=[{value:"blog",label:"博客文章"},{value:"social",label:"社交媒体帖子"},{value:"email",label:"电子邮件"},{value:"product",label:"产品描述"},{value:"ad",label:"广告文案"}];(0,react.useEffect)((()=>{setHistory([{id:1,title:"夏季促销活动介绍",content:"夏季促销活动即将开始！我们为您准备了众多精彩折扣...",createdAt:"2023-07-15 14:23",language:"zh",contentType:"marketing"},{id:2,title:"New Product Launch Email",content:"We are excited to announce our new product launch...",createdAt:"2023-07-12 09:15",language:"en",contentType:"email"},{id:3,title:"Promosi Musim Panas",content:"Promosi musim panas akan bermula! Kami telah menyediakan pelbagai diskaun menarik...",createdAt:"2023-07-10 16:45",language:"ms",contentType:"social"}])}),[]);return(0,jsx_runtime.jsxs)(PageContainer,{children:[(0,jsx_runtime.jsx)(PageTitle,{children:"内容生成助手"}),(0,jsx_runtime.jsx)(PageDescription,{children:"使用AI助手快速生成各种类型的内容"}),(0,jsx_runtime.jsxs)(tabs.A,{activeKey:activeTab,onChange:setActiveTab,children:[(0,jsx_runtime.jsx)(TabPane,{tab:"生成内容",children:(0,jsx_runtime.jsxs)(row.A,{gutter:24,children:[(0,jsx_runtime.jsx)(col.A,{xs:24,lg:10,children:(0,jsx_runtime.jsx)(StyledCard,{title:"内容参数",children:(0,jsx_runtime.jsxs)(es_form.A,{form,layout:"vertical",onFinish:async values=>{setLoading(!0);try{setTimeout((()=>{let sampleContent="";var _contentTypes$find;"zh"===values.language?sampleContent=`# ${values.title}\n\n这是一篇关于${values.keywords}的${(null===(_contentTypes$find=contentTypes.find((ct=>ct.value===values.contentType)))||void 0===_contentTypes$find?void 0:_contentTypes$find.label)||"内容"}。\n\n我们可以看到${values.keywords}在现代社会中扮演着重要角色。无论是在工作还是生活中，${values.keywords}都为我们带来了极大的便利。\n\n## 为什么${values.keywords}如此重要？\n\n首先，${values.keywords}提高了效率。其次，它使流程更加简化。最后，它为用户创造了更好的体验。\n\n## 总结\n\n总之，${values.keywords}是一个值得关注的领域，未来它将继续发展并改变我们的生活方式。`:sampleContent="en"===values.language?`# ${values.title}\n\nThis is an article about ${values.keywords}.\n\nWe can see that ${values.keywords} plays an important role in modern society. Whether in work or life, ${values.keywords} has brought us great convenience.\n\n## Why is ${values.keywords} so important?\n\nFirstly, ${values.keywords} improves efficiency. Secondly, it simplifies processes. Finally, it creates a better experience for users.\n\n## Summary\n\nIn conclusion, ${values.keywords} is a field worth paying attention to, and in the future it will continue to develop and change our way of life.`:"ms"===values.language?`# ${values.title}\n\nIni adalah artikel tentang ${values.keywords}.\n\nKita dapat melihat bahawa ${values.keywords} memainkan peranan penting dalam masyarakat moden. Sama ada dalam kerja atau kehidupan, ${values.keywords} telah membawa kemudahan yang besar kepada kita.\n\n## Mengapa ${values.keywords} begitu penting?\n\nPertama, ${values.keywords} meningkatkan kecekapan. Kedua, ia mempermudahkan proses. Akhirnya, ia mencipta pengalaman yang lebih baik untuk pengguna.\n\n## Ringkasan\n\nKesimpulannya, ${values.keywords} adalah bidang yang perlu diberi perhatian, dan pada masa akan datang ia akan terus berkembang dan mengubah cara hidup kita.`:`Generated content for ${values.title} in ${values.language} language about ${values.keywords}.`;setGeneratedContent(sampleContent);const newHistoryItem={id:history.length+1,title:values.title,content:sampleContent,createdAt:(new Date).toLocaleString(),language:values.language,contentType:values.contentType};setHistory([newHistoryItem,...history]),setLoading(!1)}),1500)}catch(error){message.Ay.error("生成内容失败"),console.error(error),setLoading(!1)}},initialValues:{language:"zh",contentType:"blog",tone:"professional",length:500},children:[(0,jsx_runtime.jsx)(es_form.A.Item,{name:"title",label:"标题",rules:[{required:!0,message:"请输入标题"}],children:(0,jsx_runtime.jsx)(input.A,{placeholder:"输入内容标题"})}),(0,jsx_runtime.jsx)(es_form.A.Item,{name:"keywords",label:"关键词",rules:[{required:!0,message:"请输入关键词"}],children:(0,jsx_runtime.jsx)(TextArea,{placeholder:"输入关键词，使用逗号分隔",rows:2})}),(0,jsx_runtime.jsx)(es_form.A.Item,{name:"language",label:"语言",rules:[{required:!0,message:"请选择语言"}],children:(0,jsx_runtime.jsx)(es_select.A,{children:languages.map((lang=>(0,jsx_runtime.jsx)(Option,{value:lang.value,children:lang.label},lang.value)))})}),(0,jsx_runtime.jsx)(es_form.A.Item,{name:"contentType",label:"内容类型",rules:[{required:!0,message:"请选择内容类型"}],children:(0,jsx_runtime.jsx)(es_select.A,{children:contentTypes.map((type=>(0,jsx_runtime.jsx)(Option,{value:type.value,children:type.label},type.value)))})}),(0,jsx_runtime.jsx)(es_form.A.Item,{name:"tone",label:"语气",children:(0,jsx_runtime.jsx)(es_radio.Ay.Group,{children:[{value:"professional",label:"专业"},{value:"friendly",label:"友好"},{value:"casual",label:"随意"},{value:"enthusiastic",label:"热情"},{value:"formal",label:"正式"}].map((tone=>(0,jsx_runtime.jsx)(es_radio.Ay,{value:tone.value,children:tone.label},tone.value)))})}),(0,jsx_runtime.jsx)(es_form.A.Item,{name:"length",label:"内容长度",children:(0,jsx_runtime.jsx)(slider.A,{min:100,max:1e3,step:100,marks:{100:"短",500:"中",1e3:"长"}})}),(0,jsx_runtime.jsx)(es_button.Ay,{type:"primary",htmlType:"submit",loading,block:!0,children:"生成内容"})]})})}),(0,jsx_runtime.jsx)(col.A,{xs:24,lg:14,children:(0,jsx_runtime.jsx)(StyledCard,{title:"生成结果",extra:generatedContent&&(0,jsx_runtime.jsxs)(jsx_runtime.Fragment,{children:[(0,jsx_runtime.jsx)(ActionButton,{icon:(0,jsx_runtime.jsx)(SaveOutlined.A,{}),onClick:()=>{message.Ay.success("内容已保存")},children:"保存"}),(0,jsx_runtime.jsx)(ActionButton,{icon:copiedStatus?(0,jsx_runtime.jsx)(CheckOutlined.A,{}):(0,jsx_runtime.jsx)(CopyOutlined.A,{}),onClick:()=>{navigator.clipboard.writeText(generatedContent).then((()=>{setCopiedStatus(!0),message.Ay.success("内容已复制到剪贴板"),setTimeout((()=>setCopiedStatus(!1)),2e3)})).catch((err=>{message.Ay.error("复制失败"),console.error(err)}))},children:copiedStatus?"已复制":"复制"})]}),children:loading?(0,jsx_runtime.jsx)("div",{style:{textAlign:"center",padding:"40px 0"},children:(0,jsx_runtime.jsx)(spin.A,{tip:"正在生成内容..."})}):generatedContent?(0,jsx_runtime.jsx)(ContentWrapper,{children:generatedContent}):(0,jsx_runtime.jsx)("div",{style:{textAlign:"center",color:"#999",padding:"40px 0"},children:'填写左侧表单并点击"生成内容"按钮来创建内容'})})})]})},"1"),(0,jsx_runtime.jsx)(TabPane,{tab:"历史记录",icon:(0,jsx_runtime.jsx)(HistoryOutlined.A,{}),children:(0,jsx_runtime.jsx)(StyledCard,{title:"生成历史",children:(0,jsx_runtime.jsx)(ContentHistory,{children:history.length>0?history.map((item=>{var _languages$find,_contentTypes$find2;return(0,jsx_runtime.jsxs)(HistoryItem,{onClick:()=>(item=>{form.setFieldsValue({title:item.title,language:item.language,contentType:item.contentType}),setGeneratedContent(item.content),setActiveTab("1")})(item),children:[(0,jsx_runtime.jsxs)("div",{children:[(0,jsx_runtime.jsx)("strong",{children:item.title}),(0,jsx_runtime.jsx)(tag.A,{color:"blue",style:{marginLeft:8},children:(null===(_languages$find=languages.find((l=>l.value===item.language)))||void 0===_languages$find?void 0:_languages$find.label.split(" ")[0])||item.language}),(0,jsx_runtime.jsx)(tag.A,{color:"green",children:(null===(_contentTypes$find2=contentTypes.find((ct=>ct.value===item.contentType)))||void 0===_contentTypes$find2?void 0:_contentTypes$find2.label)||item.contentType})]}),(0,jsx_runtime.jsx)("div",{style:{margin:"8px 0",color:"#666"},children:item.content.length>100?`${item.content.substring(0,100)}...`:item.content}),(0,jsx_runtime.jsx)(HistoryDate,{children:item.createdAt})]},item.id)})):(0,jsx_runtime.jsx)("div",{style:{textAlign:"center",padding:"40px 0",color:"#999"},children:"没有历史记录"})})})},"2")]})]})}const pages_ContentAgentPage=ContentAgentPage;ContentAgentPage.__docgenInfo={description:"",methods:[],displayName:"ContentAgentPage"};var AuthContext=__webpack_require__("./src/context/AuthContext.js"),chunk_AYJ5UCUI=__webpack_require__("./node_modules/react-router/dist/development/chunk-AYJ5UCUI.mjs");const ContentAgentPage_stories={title:"Pages/Content Agent Page",tags:["backend"],component:pages_ContentAgentPage,decorators:[Story=>(0,jsx_runtime.jsx)(AuthContext.cy.Provider,{value:{user:{plan:"enterprise"}},children:(0,jsx_runtime.jsx)(chunk_AYJ5UCUI.fS,{initialEntries:["/data/content-generator"],children:(0,jsx_runtime.jsx)(Story,{})})})]},Default=(args=>(0,jsx_runtime.jsx)(pages_ContentAgentPage,{})).bind({});Default.args={};const __namedExportsOrder=["Default"];Default.parameters={...Default.parameters,docs:{...Default.parameters?.docs,source:{originalSource:"args => <ContentAgentPage />",...Default.parameters?.docs?.source}}}}}]);
//# sourceMappingURL=pages-ContentAgentPage-stories.af1b0e2c.iframe.bundle.js.map