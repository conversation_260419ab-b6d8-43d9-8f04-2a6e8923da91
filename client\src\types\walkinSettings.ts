import { BaseSettings, WorkingHours, NotificationSettings, Staff, ServiceType } from './settingsCommon';

/**
 * 现场预约设置类型定义
 */

export interface WalkinGeneralSettings extends BaseSettings {
  serviceName: string;
  maxDailyWalkins: number;
  waitTimeEstimationMethod: 'FIXED' | 'DYNAMIC'
  averageServiceTimeMinutes: number;
  allowWalkinOutsideHours: boolean;
  maxSimultaneousBookings: number;
  waitingTimeBuffer: number;
  enableCheckIn: boolean;
  enableSelfService: boolean;
  showQueuePosition: boolean;
  sendStatusUpdates: boolean;
  defaultServiceDuration: number;
}

export interface QueueSettings {
  // 基本队列设置
  maxWaitTimeMinutes: number;
  maxQueueLength: number;
  queueDisplayEnabled: boolean;
  checkInMethod: 'KIOSK' | 'STAFF' | 'BOTH'
  waitTimeDisplayMessage: string;
  queueFullMessage: string;
  queueMethod: 'fifo' | 'priority' | 'estimated-time' | 'appointment_first' | 'vip_priority'
  estimationMethod: 'average' | 'real-time' | 'ml-prediction'
  enableVirtualQueue: boolean;
  maxQueueSize: number;
  allowCancellation: boolean;
  notifyPosition: boolean;
  allowPreRegistration: boolean;
  
  // 队列管理高级功能
  autoAdvanceQueue: boolean;
  waitTimeWarningMinutes: number;
  positionUpdateIntervalMinutes: number;
  enableWaitTimeEstimation: boolean;
  removeOnTimeout: boolean;
  
  // 优先级队列设置
  primaryPriorityFactor?: 'customer_tier' | 'service_type' | 'waiting_time' | 'urgency_level;'
  secondaryPriorityFactor?: 'customer_tier' | 'service_type' | 'waiting_time' | 'urgency_level;'
  
  // 服务流程控制
  requireServiceConfirmation: boolean;
  enableServiceRating: boolean;
  allowQueueJumping: boolean;
  trackServiceHistory: boolean;
  
  // 异常处理
  rejectWhenFull: boolean;
  pauseOnSystemError: boolean;
  adjustOnStaffAbsence: boolean;
  emergencyPriority: boolean;
}

export interface QRCodeSettings {
  enabled: boolean;
  useCustomDesign: boolean;
  logoUrl?: string;
  primaryColor?: string;
  displayInstructions: boolean;
  instructions?: string;
  qrcodeSize: number;
  includeLogo: boolean;
  qrcodeBackgroundColor: string;
  qrcodeForegroundColor: string;
}

export interface CheckInSettings {
  method: 'kiosk' | 'staff' | 'both'
  kioskSettings?: {
  enabled: boolean;
  welcomeMessage: string;
  language: string[];
  accessibility: boolean;
};
  staffSettings?: {
    requireStaffAssignment: boolean;
    allowStaffOverride: boolean;
  };
}

export interface ServiceFlowSettings {
  serviceTypes: ServiceType[];
  qualityStandards: {
  minServiceTime: number;
  maxServiceTime: number;
  requiredSteps: string[];
};
  completionSettings: {
    requireCustomerSignature: boolean;
    enableRating: boolean;
    enableFeedback: boolean;
    autoCompleteAfterTime?: number;
  };
}

export interface TicketTemplate {
  id: string;
  name: string;
  template: string;
  variables: string[];
  isDefault: boolean;
}

export interface WalkinBookingSettings {
  general: WalkinGeneralSettings;
  workingHours: WorkingHours;
  queueSettings: QueueSettings;
  qrCodeSettings: QRCodeSettings;
  checkInSettings: CheckInSettings;
  serviceFlow: ServiceFlowSettings;
  notifications: NotificationSettings;
  ticketTemplates: TicketTemplate[];
  staff: Staff[];
}

export interface WalkinSettingsFormData {
  generalSettings: Partial<WalkinGeneralSettings>;
  queueSettings: Partial<QueueSettings>;
  qrCodeSettings: Partial<QRCodeSettings>;
  checkInSettings: Partial<CheckInSettings>;
  serviceFlowSettings: Partial<ServiceFlowSettings>;
  notificationSettings: Partial<NotificationSettings>;
  workingHours: Partial<WorkingHours>;
}

// 默认设置模板
export const defaultWalkinSettings: WalkinBookingSettings = {
  general: {
    name: '现场预约服务',
    description: '高效的现场排队管理',
    enabled: true,
    serviceName: '现场服务',
    maxDailyWalkins: 100,
    waitTimeEstimationMethod: 'DYNAMIC',
    averageServiceTimeMinutes: 15,
    allowWalkinOutsideHours: false,
    maxSimultaneousBookings: 5,
    waitingTimeBuffer: 10,
    enableCheckIn: true,
    enableSelfService: true,
    showQueuePosition: true,
    sendStatusUpdates: true,
    defaultServiceDuration: 30
  },
  workingHours: {
    enabled: true,
    timezone: 'Asia/Shanghai',
    schedule: {
      monday: { start: '09:00', end: '18:00', closed: false },
      tuesday: { start: '09:00', end: '18:00', closed: false },
      wednesday: { start: '09:00', end: '18:00', closed: false },
      thursday: { start: '09:00', end: '18:00', closed: false },
      friday: { start: '09:00', end: '18:00', closed: false },
      saturday: { start: '10:00', end: '16:00', closed: false },
      sunday: { start: '10:00', end: '16:00', closed: true }
    }
  },
  queueSettings: {
    maxWaitTimeMinutes: 120,
    maxQueueLength: 50,
    queueDisplayEnabled: true,
    checkInMethod: 'BOTH',
    waitTimeDisplayMessage: '预计等待时间：{time} 分钟',
    queueFullMessage: '当前队列已满，请稍后再试',
    queueMethod: 'fifo',
    estimationMethod: 'average',
    enableVirtualQueue: false,
    maxQueueSize: 50,
    allowCancellation: true,
    notifyPosition: true,
    allowPreRegistration: false,
    autoAdvanceQueue: false,
    waitTimeWarningMinutes: 10,
    positionUpdateIntervalMinutes: 5,
    enableWaitTimeEstimation: true,
    removeOnTimeout: false,
    primaryPriorityFactor: 'customer_tier',
    secondaryPriorityFactor: 'service_type',
    requireServiceConfirmation: true,
    enableServiceRating: true,
    allowQueueJumping: false,
    trackServiceHistory: true,
    rejectWhenFull: false,
    pauseOnSystemError: false,
    adjustOnStaffAbsence: false,
    emergencyPriority: false
  },
  qrCodeSettings: {
    enabled: true,
    useCustomDesign: false,
    primaryColor: '#3B82F6',
    displayInstructions: true,
    instructions: '扫描二维码加入队列',
    qrcodeSize: 200,
    includeLogo: false,
    qrcodeBackgroundColor: '#FFFFFF',
    qrcodeForegroundColor: '#000000'
  },
  checkInSettings: {
    method: 'both',
    kioskSettings: {
      enabled: true,
      welcomeMessage: '欢迎使用自助签到系统',
      language: ['zh-CN'],
      accessibility: true
    },
    staffSettings: {
      requireStaffAssignment: false,
      allowStaffOverride: true
    }
  },
  serviceFlow: {
    serviceTypes: [],
    qualityStandards: {
      minServiceTime: 5,
      maxServiceTime: 60,
      requiredSteps: []
    },
    completionSettings: {
      requireCustomerSignature: false,
      enableRating: true,
      enableFeedback: true
    }
  },
  notifications: {
    email: {
      enabled: false,
      provider: 'smtp',
      fromEmail: '<EMAIL>',
      fromName: '客服中心',
      smtpHost: '',
      smtpPort: 587
    },
    sms: {
      enabled: true,
      provider: 'local'
    },
    push: {
      enabled: true
    },
    templates: {
      queue_joined: {
        enabled: true,
        subject: '排队确认',
        content: '您已成功加入队列，当前位置：{position}'
      },
      position_update: {
        enabled: true,
        subject: '位置更新',
        content: '您的队列位置已更新至第 {position} 位，预计等待 {waitTime} 分钟'
      },
      ready_for_service: {
        enabled: true,
        subject: '准备服务',
        content: '即将轮到您接受服务，请准备好相关物品'
      },
      service_started: {
        enabled: true,
        subject: '服务开始',
        content: '您的服务现在开始，感谢耐心等待'
      },
      service_completed: {
        enabled: true,
        subject: '服务完成',
        content: '服务已完成，感谢您的光临！'
      },
      queue_timeout: {
        enabled: true,
        subject: '等待超时提醒',
        content: '您的等待时间较长，如需帮助请联系工作人员'
      }
    }
  },
  ticketTemplates: [
    {
      id: 'default',
      name: '默认票据模板',
      template: '排队号：{number}\n服务类型：{service}\n预计等待：{waitTime}\n时间：{time}',
      variables: ['number', 'service', 'waitTime', 'time'],
      isDefault: true
    }
  ],
  staff: []
}; 