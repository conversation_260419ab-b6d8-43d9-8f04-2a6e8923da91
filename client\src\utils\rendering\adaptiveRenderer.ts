/**
 * 自适应渲染器 - 根据设备性能和网络状况自动调整渲染质量
 */

export type DevicePerformanceTier = 'low' | 'medium' | 'high' | 'premium';
export type NetworkQuality = 'slow' | 'medium' | 'fast' | 'offline';

export interface RenderingSettings {
  /** 动画复杂度 */
  animationComplexity: 'none' | 'basic' | 'enhanced' | 'premium';
  /** 图像质量 */
  imageQuality: 'low' | 'medium' | 'high' | 'original';
  /** 视频质量 */
  videoQuality: '360p' | '720p' | '1080p' | '4k';
  /** 渲染帧率目标 */
  targetFPS: 30 | 60 | 120;
  /** 是否启用高级效果 */
  enableAdvancedEffects: boolean;
  /** 是否启用阴影 */
  enableShadows: boolean;
  /** 是否启用反射 */
  enableReflections: boolean;
  /** 渲染分辨率比例 */
  renderScale: number;
  /** 最大并发渲染任务 */
  maxConcurrentRenders: number;
}

export interface DeviceInfo {
  /** CPU核心数 */
  cpuCores: number;
  /** 内存大小(GB) */
  memory: number;
  /** GPU信息 */
  gpu: string;
  /** 屏幕分辨率 */
  screenResolution: { width: number; height: number };
  /** 设备像素比 */
  devicePixelRatio: number;
  /** 是否移动设备 */
  isMobile: boolean;
  /** 电池状态 */
  batteryLevel?: number;
  /** 是否在充电 */
  isCharging?: boolean;
}

export interface PerformanceMetrics {
  /** 平均帧率 */
  averageFPS: number;
  /** 帧时间 */
  frameTime: number;
  /** 内存使用率 */
  memoryUsage: number;
  /** CPU使用率 */
  cpuUsage: number;
  /** 渲染时间 */
  renderTime: number;
  /** 丢帧次数 */
  droppedFrames: number;
}

export class AdaptiveRenderer {
  private deviceInfo: DeviceInfo;
  private currentSettings: RenderingSettings;
  private performanceMetrics: PerformanceMetrics;
  private networkQuality: NetworkQuality = 'medium';
  private performanceTier: DevicePerformanceTier = 'medium';
  
  // 性能监控相关
  private frameCount: number = 0;
  private lastFrameTime: number = 0;
  private fpsHistory: number[] = [];
  private frameTimeHistory: number[] = [];
  private isMonitoring: boolean = false;
  
  // 自动调整相关
  private adjustmentInterval: number | null = null;
  private lastAdjustmentTime: number = 0;
  private adjustmentThreshold: number = 5000; // 5秒

  constructor() {
    this.deviceInfo = this.detectDeviceInfo();
    this.performanceTier = this.calculatePerformanceTier();
    this.currentSettings = this.getDefaultSettings();
    this.performanceMetrics = {
      averageFPS: 60,
      frameTime: 16.67,
      memoryUsage: 0,
      cpuUsage: 0,
      renderTime: 0,
      droppedFrames: 0
    };
    
    this.initializeNetworkMonitoring();
  }

  private detectDeviceInfo(): DeviceInfo {
    const deviceInfo: DeviceInfo = {
      cpuCores: navigator.hardwareConcurrency || 4,
      memory: (navigator as any).deviceMemory || 4,
      gpu: this.getGPUInfo(),
      screenResolution: {
        width: window.screen.width,
        height: window.screen.height
      },
      devicePixelRatio: window.devicePixelRatio || 1,
      isMobile: /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
    };

    // 检测电池状态
    if ('getBattery' in navigator) {
      (navigator as any).getBattery().then((battery: any) => {
        deviceInfo.batteryLevel = battery.level;
        deviceInfo.isCharging = battery.charging;
      });
    }

    return deviceInfo;
  }

  private getGPUInfo(): string {
    try {
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
      if (gl && 'getExtension' in gl) {
        const webglContext = gl as WebGLRenderingContext;
        const debugInfo = webglContext.getExtension('WEBGL_debug_renderer_info');
        if (debugInfo) {
          return webglContext.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL) || 'Unknown GPU';
        }
      }
    } catch (e) {
      console.warn('无法获取GPU信息:', e);
    }
    return 'Unknown GPU';
  }

  private calculatePerformanceTier(): DevicePerformanceTier {
    let score = 0;
    
    // CPU评分
    if (this.deviceInfo.cpuCores >= 8) score += 3;
    else if (this.deviceInfo.cpuCores >= 4) score += 2;
    else score += 1;
    
    // 内存评分
    if (this.deviceInfo.memory >= 8) score += 3;
    else if (this.deviceInfo.memory >= 4) score += 2;
    else score += 1;
    
    // 移动设备惩罚
    if (this.deviceInfo.isMobile) score -= 1;
    
    // 高分辨率奖励
    const totalPixels = this.deviceInfo.screenResolution.width * this.deviceInfo.screenResolution.height;
    if (totalPixels >= 3840 * 2160) score += 2; // 4K
    else if (totalPixels >= 1920 * 1080) score += 1; // 1080p
    
    if (score >= 7) return 'premium';
    if (score >= 5) return 'high';
    if (score >= 3) return 'medium';
    return 'low';
  }

  private getDefaultSettings(): RenderingSettings {
    const baseSettings: RenderingSettings = {
      animationComplexity: 'enhanced',
      imageQuality: 'high',
      videoQuality: '1080p',
      targetFPS: 60,
      enableAdvancedEffects: true,
      enableShadows: true,
      enableReflections: true,
      renderScale: 1.0,
      maxConcurrentRenders: 3
    };

    // 根据性能等级调整默认设置
    switch (this.performanceTier) {
      case 'premium':
        return {
          ...baseSettings,
          animationComplexity: 'premium',
          imageQuality: 'original',
          videoQuality: '4k',
          targetFPS: 120,
          maxConcurrentRenders: 5
        };
      case 'high':
        return baseSettings;
      case 'medium':
        return {
          ...baseSettings,
          animationComplexity: 'enhanced',
          enableAdvancedEffects: false,
          targetFPS: 60
        };
      case 'low':
        return {
          ...baseSettings,
          animationComplexity: 'basic',
          imageQuality: 'medium',
          videoQuality: '720p',
          targetFPS: 30,
          enableAdvancedEffects: false,
          enableShadows: false,
          enableReflections: false,
          renderScale: 0.8,
          maxConcurrentRenders: 1
        };
      default:
        return baseSettings;
    }
  }

  private initializeNetworkMonitoring(): void {
    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      this.updateNetworkQuality(connection);
      
      connection.addEventListener('change', () => {
        this.updateNetworkQuality(connection);
      });
    }
  }

  private updateNetworkQuality(connection: any): void {
    const effectiveType = connection.effectiveType;
    
    switch (effectiveType) {
      case '4g':
        this.networkQuality = 'fast';
        break;
      case '3g':
        this.networkQuality = 'medium';
        break;
      case '2g':
      case 'slow-2g':
        this.networkQuality = 'slow';
        break;
      default:
        this.networkQuality = 'medium';
    }
    
    if (!navigator.onLine) {
      this.networkQuality = 'offline';
    }
  }

  public startMonitoring(): void {
    if (this.isMonitoring) return;
    
    this.isMonitoring = true;
    this.frameCount = 0;
    this.lastFrameTime = performance.now();
    
    const monitorFrame = () => {
      if (!this.isMonitoring) return;
      
      const currentTime = performance.now();
      const deltaTime = currentTime - this.lastFrameTime;
      
      this.frameCount++;
      this.frameTimeHistory.push(deltaTime);
      
      // 计算FPS
      if (deltaTime > 0) {
        const fps = 1000 / deltaTime;
        this.fpsHistory.push(fps);
      }
      
      // 保持历史记录在合理范围内
      if (this.frameTimeHistory.length > 120) {
        this.frameTimeHistory.shift();
      }
      if (this.fpsHistory.length > 120) {
        this.fpsHistory.shift();
      }
      
      // 更新性能指标
      this.updatePerformanceMetrics();
      
      this.lastFrameTime = currentTime;
      requestAnimationFrame(monitorFrame);
    };
    
    requestAnimationFrame(monitorFrame);
    
    // 启动自动调整
    this.startAutoAdjustment();
  }

  private updatePerformanceMetrics(): void {
    if (this.fpsHistory.length === 0) return;
    
    // 计算平均FPS
    const avgFPS = this.fpsHistory.reduce((sum, fps) => sum + fps, 0) / this.fpsHistory.length;
    
    // 计算平均帧时间
    const avgFrameTime = this.frameTimeHistory.reduce((sum, time) => sum + time, 0) / this.frameTimeHistory.length;
    
    // 计算丢帧
    const targetFrameTime = 1000 / this.currentSettings.targetFPS;
    const droppedFrames = this.frameTimeHistory.filter(time => time > targetFrameTime * 1.5).length;
    
    this.performanceMetrics = {
      averageFPS: avgFPS,
      frameTime: avgFrameTime,
      memoryUsage: this.getMemoryUsage(),
      cpuUsage: this.getCPUUsage(),
      renderTime: avgFrameTime,
      droppedFrames: droppedFrames
    };
  }

  private getMemoryUsage(): number {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      return memory.usedJSHeapSize / memory.jsHeapSizeLimit;
    }
    return 0;
  }

  private getCPUUsage(): number {
    // 简单的CPU使用率估算
    const avgFrameTime = this.performanceMetrics.frameTime;
    const targetFrameTime = 1000 / this.currentSettings.targetFPS;
    return Math.min(1, avgFrameTime / targetFrameTime);
  }

  private startAutoAdjustment(): void {
    if (this.adjustmentInterval) return;
    
    this.adjustmentInterval = window.setInterval(() => {
      this.performAutoAdjustment();
    }, this.adjustmentThreshold);
  }

  private performAutoAdjustment(): void {
    const now = performance.now();
    if (now - this.lastAdjustmentTime < this.adjustmentThreshold) return;
    
    if (this.shouldDowngradeQuality()) {
      this.downgradeRenderingQuality();
      this.lastAdjustmentTime = now;
    } else if (this.shouldUpgradeQuality()) {
      this.upgradeRenderingQuality();
      this.lastAdjustmentTime = now;
    }
    
    // 根据电池状态调整
    this.adjustForBatteryStatus();
    
    // 根据网络状态调整
    this.adjustForNetworkStatus();
  }

  private shouldDowngradeQuality(): boolean {
    const targetFPS = this.currentSettings.targetFPS;
    const avgFPS = this.performanceMetrics.averageFPS;
    
    // FPS低于目标的80%
    if (avgFPS < targetFPS * 0.8) return true;
    
    // 内存使用超过80%
    if (this.performanceMetrics.memoryUsage > 0.8) return true;
    
    // 丢帧率超过20%
    if (this.fpsHistory.length > 0) {
      const dropRate = this.performanceMetrics.droppedFrames / this.fpsHistory.length;
      if (dropRate > 0.2) return true;
    }
    
    return false;
  }

  private shouldUpgradeQuality(): boolean {
    const targetFPS = this.currentSettings.targetFPS;
    const avgFPS = this.performanceMetrics.averageFPS;
    
    // FPS稳定在目标值之上
    if (avgFPS > targetFPS * 1.1 && this.performanceMetrics.memoryUsage < 0.6) {
      return true;
    }
    
    return false;
  }

  private downgradeRenderingQuality(): void {
    const oldSettings = { ...this.currentSettings };
    
    // 降低动画复杂度
    if (this.currentSettings.animationComplexity === 'premium') {
      this.currentSettings.animationComplexity = 'enhanced';
    } else if (this.currentSettings.animationComplexity === 'enhanced') {
      this.currentSettings.animationComplexity = 'basic';
    } else if (this.currentSettings.animationComplexity === 'basic') {
      this.currentSettings.animationComplexity = 'none';
    }
    
    // 降低图像质量
    if (this.currentSettings.imageQuality === 'original') {
      this.currentSettings.imageQuality = 'high';
    } else if (this.currentSettings.imageQuality === 'high') {
      this.currentSettings.imageQuality = 'medium';
    } else if (this.currentSettings.imageQuality === 'medium') {
      this.currentSettings.imageQuality = 'low';
    }
    
    // 关闭高级效果
    if (this.currentSettings.enableAdvancedEffects) {
      this.currentSettings.enableAdvancedEffects = false;
    } else if (this.currentSettings.enableReflections) {
      this.currentSettings.enableReflections = false;
    } else if (this.currentSettings.enableShadows) {
      this.currentSettings.enableShadows = false;
    }
    
    // 降低渲染分辨率
    if (this.currentSettings.renderScale > 0.5) {
      this.currentSettings.renderScale = Math.max(0.5, this.currentSettings.renderScale - 0.1);
    }
    
    // 降低目标帧率
    if (this.currentSettings.targetFPS === 120) {
      this.currentSettings.targetFPS = 60;
    } else if (this.currentSettings.targetFPS === 60) {
      this.currentSettings.targetFPS = 30;
    }
    
    console.log('[自适应渲染器] 降级渲染质量', { oldSettings, newSettings: this.currentSettings });
  }

  private upgradeRenderingQuality(): void {
    const oldSettings = { ...this.currentSettings };
    const defaultSettings = this.getDefaultSettings();
    
    // 逐步恢复到默认设置
    if (this.currentSettings.targetFPS < defaultSettings.targetFPS) {
      this.currentSettings.targetFPS = defaultSettings.targetFPS;
    }
    
    if (this.currentSettings.renderScale < defaultSettings.renderScale) {
      this.currentSettings.renderScale = Math.min(
        defaultSettings.renderScale,
        this.currentSettings.renderScale + 0.1
      );
    }
    
    if (!this.currentSettings.enableShadows && defaultSettings.enableShadows) {
      this.currentSettings.enableShadows = true;
    }
    
    console.log('[自适应渲染器] 升级渲染质量', { oldSettings, newSettings: this.currentSettings });
  }

  private adjustForBatteryStatus(): void {
    if (this.deviceInfo.batteryLevel !== undefined && this.deviceInfo.batteryLevel < 0.2) {
      // 电池电量低时降级
      if (!this.deviceInfo.isCharging) {
        this.currentSettings.targetFPS = 30;
        this.currentSettings.enableAdvancedEffects = false;
        this.currentSettings.animationComplexity = 'basic';
      }
    }
  }

  private adjustForNetworkStatus(): void {
    if (this.networkQuality === 'slow') {
      this.currentSettings.imageQuality = 'low';
      this.currentSettings.videoQuality = '360p';
    } else if (this.networkQuality === 'offline') {
      // 离线时关闭所有网络相关功能
      this.currentSettings.imageQuality = 'low';
    }
  }

  public getCurrentSettings(): RenderingSettings {
    return { ...this.currentSettings };
  }

  public setRenderingSettings(settings: Partial<RenderingSettings>): void {
    this.currentSettings = { ...this.currentSettings, ...settings };
    console.log('[自适应渲染器] 手动设置渲染质量', this.currentSettings);
  }

  public getPerformanceStats(): {
    deviceInfo: DeviceInfo;
    performanceTier: DevicePerformanceTier;
    networkQuality: NetworkQuality;
    metrics: PerformanceMetrics;
    settings: RenderingSettings;
  } {
    return {
      deviceInfo: this.deviceInfo,
      performanceTier: this.performanceTier,
      networkQuality: this.networkQuality,
      metrics: this.performanceMetrics,
      settings: this.currentSettings
    };
  }

  public stopMonitoring(): void {
    this.isMonitoring = false;
    
    if (this.adjustmentInterval) {
      clearInterval(this.adjustmentInterval);
      this.adjustmentInterval = null;
    }
  }

  public resetToDefaults(): void {
    this.currentSettings = this.getDefaultSettings();
    console.log('[自适应渲染器] 重置到默认设置', this.currentSettings);
  }
}

// 全局自适应渲染器实例
export const adaptiveRenderer = new AdaptiveRenderer();

export default adaptiveRenderer;