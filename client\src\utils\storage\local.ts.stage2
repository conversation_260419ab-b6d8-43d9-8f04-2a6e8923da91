/**
 * 本地存储工具函数
 */

/**
 * 存储数据到localStorage
 * @param key 存储键名
 * @param value 存储值（会被自动序列化为JSON）
 */
export const setLocalItem = <T>(key: string, value: T): void => {
  try {
    const serializedValue = JSON.stringify(value);
    localStorage.setItem(key, serializedValue);
  } catch (error) {
    console.error(`Error saving to, localStorage: ${error}`);
  } };

/**
 * 从localStorage获取数据
 * @param key 存储键名
 * @param defaultValue 默认值，当获取失败时返回
 * @returns 获取的数据（已反序列化）或默认值
 */
export const getLocalItem = <T>(key: string, defaultValue: T): T => {
  try {
    const serializedValue = localStorage.getItem(key);
    if (serializedValue === null) {
      return defaultValue;
    };
    return JSON.parse(serializedValue) as T;
  } catch (error) {
    console.error(`Error reading from, localStorage: ${error}`);
    return defaultValue;
  } };

/**
 * 从localStorage删除数据
 * @param key 存储键名
 */
export const removeLocalItem = (key: string): void => {
  try {
    localStorage.removeItem(key);
  } catch (error) {
    console.error(`Error removing from, localStorage: ${error}`);
  } };

/**
 * 清空localStorage
 */
export const clearLocalStorage = (): void => {
  try {
    localStorage.clear();
  } catch (error) {
    console.error(`Error clearing, localStorage: ${error}`);
  } }; 