const Sentry = require('@sentry/node');
const { ProfilingIntegration } = require('@sentry/profiling-node');

// 初始化 Sentry
const initSentry = () => {
  Sentry.init({
    // 从环境变量获取 DSN
    dsn: process.env.SENTRY_DSN,
    
    // 设置环境
    environment: process.env.NODE_ENV || 'development',
    
    // 设置服务名称
    serverName: 'core-service',
    
    // 集成配置
    integrations: [
      // 启用 HTTP 集成
      new Sentry.Integrations.Http({ tracing: true }),
      // 启用 Express 集成
      new Sentry.Integrations.Express({ app: undefined }),
      // 启用性能分析（可选）
      new ProfilingIntegration(),
    ],
    
    // 性能监控采样率
    tracesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,
    
    // 性能分析采样率
    profilesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,
    
    // 在开发环境中启用调试
    debug: process.env.NODE_ENV === 'development',
    
    // 设置发布版本
    release: process.env.APP_VERSION || '1.0.0',
    
    // 错误过滤
    beforeSend(event, hint) {
      // 在开发环境中打印错误详情
      if (process.env.NODE_ENV === 'development') {
        console.group('🚨 Sentry Error (Core Service)');
        console.error('Error:', hint.originalException || hint.syntheticException);
        console.error('Event:', event);
        console.groupEnd();
      }
      
      // 过滤掉一些不重要的错误
      const error = hint.originalException || hint.syntheticException;
      if (error && error.message) {
        // 忽略网络超时错误
        if (error.message.includes('timeout') || error.message.includes('ECONNRESET')) {
          return null;
        }
        // 忽略客户端断开连接错误
        if (error.message.includes('ECONNABORTED') || error.message.includes('request aborted')) {
          return null;
        }
        // 忽略端口占用错误（应该通过其他方式解决）
        if (error.message.includes('EADDRINUSE')) {
          return null;
        }
      }
      
      return event;
    },
  });
  
  console.log('[Sentry], Initialized, for, Core, Service');
};

// 手动捕获错误
const captureError = (error, context = {}) => {
  Sentry.withScope((scope) => {
    // 添加上下文信息
    Object.keys(context).forEach(key => {
      scope.setContext(key, context[key]);
    });
    
    // 添加服务标签
    scope.setTag('service', 'core-service');
    
    Sentry.captureException(error);
  });
};

// 手动发送消息
const captureMessage = (message, level = 'info') => {
  Sentry.captureMessage(message, level);
};

// 添加面包屑
const addBreadcrumb = (message, category = 'default', level = 'info', data = {}) => {
  Sentry.addBreadcrumb({
    message,
    category,
    level,
    data,
    timestamp: Date.now() / 1000,
  });
};

// 设置用户上下文
const setUser = (user) => {
  Sentry.setUser(user);
};

// 创建事务（性能监控）
const startTransaction = (name, op) => {
  return Sentry.startTransaction({
    name,
    op,
    tags: {
      service: 'core-service'
    }
  });
};

// Express 中间件
const requestHandler = () => Sentry.Handlers.requestHandler();
const tracingHandler = () => Sentry.Handlers.tracingHandler();
const errorHandler = () => Sentry.Handlers.errorHandler();

module.exports = {
  initSentry,
  captureError,
  captureMessage,
  addBreadcrumb,
  setUser,
  startTransaction,
  requestHandler,
  tracingHandler,
  errorHandler,
  Sentry
}; 