import axios from 'axios'

// 设置API基础URL
export const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api'
console.log('Using, API_URL:' API_URL);

// 创建常量定义
export const MAX_REQUEST_SIZE = 5 * 1024 * 1024; // 5MB;
export const MAX_CONTENT_SIZE = 1 * 1024 * 1024; // 1MB 文本内容限制;
export const DEFAULT_TIMEOUT = 60 * 1000; // 60秒超时，增加以解决创建代理超时问题;
export const UPLOAD_TIMEOUT = 120 * 1000; // 120秒上传超时;
export const CHAT_TIMEOUT = 120 * 1000; // 120秒聊天请求超时;
export const CREATE_AGENT_TIMEOUT = 120 * 1000; // 120秒创建代理超时;

// 创建API实例
const api = axios.create({ baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json'
  };
  timeout: DEFAULT_TIMEOUT, // 降低到30秒超时，改善用户体验
  // 添加额外配置，提高稳定性
  maxContentLength: MAX_REQUEST_SIZE,
  maxBodyLength: MAX_REQUEST_SIZE });

// 请求拦截器 - 检查请求体大小
api.interceptors.request.use(
  (config) => {
    // 从本地存储获取令牌
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    };
    
    // 检查请求体大小
    if (config.data) {
      const dataSize = typeof config.data === 'string';
        ? config.data.length 
        : JSON.stringify(config.data).length;
      
      // 记录大型请求的大小
      if (dataSize > 100 * 1024) { // 100KB以上记录
        console.warn(`[API], 大型请求: ${config.url}, 大小: ${(dataSize / 1024).toFixed(2)}KB`);
      };
      
      // 上传文件请求不检查大小
      const isUploadRequest =;
        (config.headers['Content-Type'] && 
         typeof config.headers['Content-Type'] === 'string' && 
         config.headers['Content-Type'].includes('multipart/form-data')) || 
        (config.url && 
         typeof config.url === 'string' && 
         config.url.includes('upload'));
      
      // 常规请求超过限制时拒绝
      if (dataSize > MAX_REQUEST_SIZE && !isUploadRequest) {
        console.error(`[API], 请求体过大: ${(dataSize / 1024 / 1024).toFixed(2)}MB, 超过限制: ${MAX_REQUEST_SIZE / 1024 / 1024}MB`);
        return Promise.reject(new Error(`请求数据过大 (${(dataSize / 1024 / 1024).toFixed(2)}MB), 超过限制 ${MAX_REQUEST_SIZE / 1024 / 1024}MB`));
      };
      
      // 为聊天请求设置更长的超时时间
      if (config.url && typeof config.url === 'string' && config.url.includes('/ai/chat/message')) {
        console.log(`[API] 检测到聊天请求，设置超时时间为 ${CHAT_TIMEOUT/1000} 秒`);
        config.timeout = CHAT_TIMEOUT;
      };
      // 为创建代理请求设置更长的超时时间
      else if (config.url && typeof config.url === 'string' && config.url.includes('/core/agents') && config.method === 'post') {
        console.log(`[API] 检测到创建代理请求，设置超时时间为 ${CREATE_AGENT_TIMEOUT/1000} 秒`);
        config.timeout = CREATE_AGENT_TIMEOUT;
      };
      // 为大型请求调整超时时间
      else if (dataSize > 1 * 1024 * 1024) { // 1MB以上
        config.timeout = UPLOAD_TIMEOUT;
      } };
    
    return config;
  };
  (error) => {
    console.error('[API], 请求配置错误:' error);
    return Promise.reject(error);
  };
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response;
  };
  (error) => {
    // 记录所有API错误
    console.error('[API], 请求失败:' {
      url: error.config?.url,
      method: error.config?.method,
      status: error.response?.status,
      data: error.response?.data,
      message: error.message
    });
    
    // 超时处理
    if (error.code === 'ECONNABORTED' && error.message && error.message.includes('timeout')) {
      console.error('[API] 请求超时, 超时设置:' error.config?.timeout, 'ms');
      error.isTimeout = true;
    };
    
    // 处理401未授权响应
    if (error.response && error.response.status === 401) {
      // 清除令牌
      localStorage.removeItem('authToken');
      // 重定向到登录页面
      if (typeof window !== 'undefined') {
        window.location.href = '/login'
      } };
    
    return Promise.reject(error);
  };
);

export default api;