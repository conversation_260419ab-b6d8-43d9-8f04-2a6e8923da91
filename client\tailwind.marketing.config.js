/** @type {import('tailwindcss').Config} */
const { marketingTokens } = require('./src/theme/marketing-tokens');

module.exports = {
  content: [
    './src/pages/marketing/**/*.{js,ts,jsx,tsx}',
    './src/components/marketing/**/*.{js,ts,jsx,tsx}',
    './src/styles/marketing/**/*.css',
  ],
  theme: {
    extend: {
      // 营销页面专用配色
      colors: {
        // 主品牌色
        primary: marketingTokens.colors.primary,
        
        // 背景色系
        'bg-primary': marketingTokens.colors.background.primary,
        'bg-secondary': marketingTokens.colors.background.secondary,
        'bg-accent': marketingTokens.colors.background.accent,
        'bg-muted': marketingTokens.colors.background.muted,
        
        // 文字色系
        'text-primary': marketingTokens.colors.text.primary,
        'text-secondary': marketingTokens.colors.text.secondary,
        'text-muted': marketingTokens.colors.text.muted,
        'text-accent': marketingTokens.colors.text.accent,
        'text-inverse': marketingTokens.colors.text.inverse,
        
        // 交互色彩
        'interactive-hover': marketingTokens.colors.interactive.hover,
        'interactive-active': marketingTokens.colors.interactive.active,
        'interactive-focus': marketingTokens.colors.interactive.focus,
        'interactive-disabled': marketingTokens.colors.interactive.disabled,
        
        // 语义色彩
        success: marketingTokens.colors.semantic.success,
        warning: marketingTokens.colors.semantic.warning,
        error: marketingTokens.colors.semantic.error,
        info: marketingTokens.colors.semantic.info,
        
        // 边框色
        'border-light': marketingTokens.colors.border.light,
        'border-default': marketingTokens.colors.border.default,
        'border-medium': marketingTokens.colors.border.medium,
        'border-dark': marketingTokens.colors.border.dark,
        'border-accent': marketingTokens.colors.border.accent,
      },
      
      // 背景渐变
      backgroundImage: {
        'gradient-primary': marketingTokens.colors.gradients.primary,
        'gradient-secondary': marketingTokens.colors.gradients.secondary,
        'gradient-hero': marketingTokens.colors.gradients.hero,
        'gradient-overlay': marketingTokens.colors.gradients.overlay,
      },
      
      // 间距系统
      spacing: marketingTokens.spacing,
      
      // 圆角系统
      borderRadius: marketingTokens.borderRadius,
      
      // 阴影系统
      boxShadow: {
        'marketing-sm': marketingTokens.shadows.sm,
        'marketing-card': marketingTokens.shadows.card,
        'marketing-hover': marketingTokens.shadows.hover,
        'marketing-focus': marketingTokens.shadows.focus,
        'marketing-large': marketingTokens.shadows.large,
        'marketing-purple': marketingTokens.shadows.purple,
      },
      
      // 字体系统
      fontFamily: marketingTokens.typography.fontFamily,
      fontSize: marketingTokens.typography.fontSize,
      fontWeight: marketingTokens.typography.fontWeight,
      lineHeight: marketingTokens.typography.lineHeight,
      
      // 动画系统
      transitionDuration: marketingTokens.animation.duration,
      transitionTimingFunction: marketingTokens.animation.easing,
      
      // 断点系统
      screens: marketingTokens.breakpoints,
      
      // 自定义动画
      keyframes: {
        'fade-in-up': {
          '0%': {
            opacity: '0',
            transform: 'translateY(20px)'
          },
          '100%': {
            opacity: '1',
            transform: 'translateY(0)'
          }
        },
        'fade-in-down': {
          '0%': {
            opacity: '0',
            transform: 'translateY(-20px)'
          },
          '100%': {
            opacity: '1',
            transform: 'translateY(0)'
          }
        },
        'scale-in': {
          '0%': {
            opacity: '0',
            transform: 'scale(0.95)'
          },
          '100%': {
            opacity: '1',
            transform: 'scale(1)'
          }
        },
        'slide-in-left': {
          '0%': {
            opacity: '0',
            transform: 'translateX(-20px)'
          },
          '100%': {
            opacity: '1',
            transform: 'translateX(0)'
          }
        },
        'slide-in-right': {
          '0%': {
            opacity: '0',
            transform: 'translateX(20px)'
          },
          '100%': {
            opacity: '1',
            transform: 'translateX(0)'
          }
        },
        'pulse-glow': {
          '0%, 100%': {
            boxShadow: '0 0 0 0 rgba(155, 93, 229, 0.7)'
          },
          '50%': {
            boxShadow: '0 0 0 10px rgba(155, 93, 229, 0)'
          }
        },
        'float': {
          '0%, 100%': {
            transform: 'translateY(0px)'
          },
          '50%': {
            transform: 'translateY(-10px)'
          }
        }
      },
      
      animation: {
        'fade-in-up': 'fade-in-up 0.6s ease-out',
        'fade-in-down': 'fade-in-down 0.6s ease-out',
        'scale-in': 'scale-in 0.4s ease-out',
        'slide-in-left': 'slide-in-left 0.6s ease-out',
        'slide-in-right': 'slide-in-right 0.6s ease-out',
        'pulse-glow': 'pulse-glow 2s infinite',
        'float': 'float 3s ease-in-out infinite',
      }
    },
  },
  plugins: [
    require('tailwindcss-animate'),
    // 自定义工具类
    function({ addUtilities }) {
      const newUtilities = {
        '.text-gradient-primary': {
          background: marketingTokens.colors.gradients.primary,
          '-webkit-background-clip': 'text',
          '-webkit-text-fill-color': 'transparent',
          'background-clip': 'text',
        },
        '.glass-effect': {
          background: 'rgba(255, 255, 255, 0.1)',
          'backdrop-filter': 'blur(10px)',
          border: '1px solid rgba(255, 255, 255, 0.2)',
        },
        '.marketing-card': {
          background: marketingTokens.colors.background.secondary,
          'border-radius': marketingTokens.borderRadius.lg,
          'box-shadow': marketingTokens.shadows.card,
          border: `1px solid ${marketingTokens.colors.border.default}`,
          transition: `all ${marketingTokens.animation.duration.normal} ${marketingTokens.animation.easing.easeOut}`,
        },
        '.marketing-card:hover': {
          'box-shadow': marketingTokens.shadows.hover,
          transform: 'translateY(-2px)',
        },
        '.marketing-button-primary': {
          background: marketingTokens.colors.gradients.primary,
          color: marketingTokens.colors.text.inverse,
          'border-radius': marketingTokens.borderRadius.lg,
          'box-shadow': marketingTokens.shadows.purple,
          transition: `all ${marketingTokens.animation.duration.normal} ${marketingTokens.animation.easing.easeOut}`,
        },
        '.marketing-button-primary:hover': {
          'box-shadow': marketingTokens.shadows.large,
          transform: 'translateY(-1px)',
        }
      }
      addUtilities(newUtilities)
    }
  ],
} 