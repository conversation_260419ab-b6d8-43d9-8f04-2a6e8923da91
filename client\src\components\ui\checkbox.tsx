import * as React from "react";
import * as CheckboxPrimitive from "@radix-ui/react-checkbox";
import { Check } from "lucide-react";
import { cn } from "@/lib/utils";

interface CheckboxProps
  extends React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root> {
  variant?: "default" | "violet";
}

const Checkbox = React.forwardRef<
  React.ElementRef<typeof CheckboxPrimitive.Root>,
  CheckboxProps
>(({ className, variant = "default", onCheckedChange, ...props }, ref) => {
  const handleCheckedChange = React.useCallback((checked: boolean | "indeterminate") => {
    if (variant === "violet") {
      console.log(`[Checkbox] Violet checkbox状态变更:`, checked);
    }
    onCheckedChange?.(checked);
  }, [onCheckedChange, variant]);

  return (
    <CheckboxPrimitive.Root
      ref={ref}
      className={cn(
        "peer h-4 w-4 shrink-0 rounded-sm border ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-200",
        variant === "violet" 
          ? "!border-violet-400 !ring-violet-500 data-[state=checked]:!bg-violet-600 data-[state=checked]:!text-white data-[state=checked]:!border-violet-600 hover:!border-violet-500 hover:!bg-violet-50 data-[state=unchecked]:!border-violet-400 data-[state=unchecked]:hover:!border-violet-500 [&>span]:data-[state=checked]:!text-white"
          : "border-primary ring-ring data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",
        className
      )}
      onCheckedChange={handleCheckedChange}
      {...props}
    >
      <CheckboxPrimitive.Indicator
        className={cn("flex items-center justify-center text-current opacity-0 data-[state=checked]:opacity-100 transition-opacity duration-200 [&>svg]:data-[state=checked]:!text-white")}
      >
        <Check className="h-4 w-4" />
      </CheckboxPrimitive.Indicator>
    </CheckboxPrimitive.Root>
  );
});

Checkbox.displayName = CheckboxPrimitive.Root.displayName;

export { Checkbox };
