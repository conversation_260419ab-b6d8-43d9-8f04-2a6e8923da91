/**
 * 数据导出工具函数
 * 支持多种格式的数据导出功能
 */

// 数据类型定义
export interface ExportData {
  [key: string]: any;
}

export interface ExportOptions {
  filename?: string;
  sheetName?: string;
  includeTimestamp?: boolean;
  customHeaders?: { [key: string]: string };
}

/**
 * 将数据导出为CSV格式
 */
export function exportToCSV(data: ExportData[], options: ExportOptions = {}) {
  const {
    filename = 'export',
    includeTimestamp = true,
    customHeaders = {}
  } = options;

  if (!data || data.length === 0) {
    throw new Error('没有可导出的数据');
  }

  // 获取表头
  const headers = Object.keys(data[0]);
  const csvHeaders = headers.map(header => customHeaders[header] || header);

  // 构建CSV内容
  const csvContent = [
    csvHeaders.join(','),
    ...data.map(row => 
      headers.map(header => {
        const value = row[header];
        // 处理包含逗号或换行的值
        if (typeof value === 'string' && (value.includes(',') || value.includes('\n'))) {
          return `"${value.replace(/"/g, '""')}"`;
        }
        return value || '';
      }).join(',')
    )
  ].join('\n');

  // 创建BOM以支持中文
  const BOM = '\uFEFF';
  const blob = new Blob([BOM + csvContent], { type: 'text/csv;charset=utf-8;' });
  
  // 生成文件名
  const timestamp = includeTimestamp ? `_${new Date().toISOString().split('T')[0]}` : '';
  const finalFilename = `${filename}${timestamp}.csv`;
  
  downloadFile(blob, finalFilename);
}

/**
 * 将数据导出为JSON格式
 */
export function exportToJSON(data: ExportData[], options: ExportOptions = {}) {
  const {
    filename = 'export',
    includeTimestamp = true
  } = options;

  if (!data || data.length === 0) {
    throw new Error('没有可导出的数据');
  }

  const exportObject = {
    exportDate: new Date().toISOString(),
    dataCount: data.length,
    data: data
  };

  const jsonContent = JSON.stringify(exportObject, null, 2);
  const blob = new Blob([jsonContent], { type: 'application/json;charset=utf-8;' });
  
  const timestamp = includeTimestamp ? `_${new Date().toISOString().split('T')[0]}` : '';
  const finalFilename = `${filename}${timestamp}.json`;
  
  downloadFile(blob, finalFilename);
}

/**
 * 导出分析报告（HTML格式）
 */
export function exportAnalyticsReport(data: {
  title: string;
  summary: string;
  metrics: Array<{ name: string; value: string | number; change?: number; }>;
  charts?: Array<{ title: string; data: any[] }>;
  insights?: Array<{ title: string; description: string; impact: string }>;
}, options: ExportOptions = {}) {
  const {
    filename = 'analytics_report',
    includeTimestamp = true
  } = options;

  const currentDate = new Date().toLocaleDateString('zh-CN');
  const currentTime = new Date().toLocaleTimeString('zh-CN');

  const htmlContent = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${data.title}</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #3B82F6;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #3B82F6;
            margin: 0;
        }
        .meta-info {
            color: #666;
            font-size: 0.9em;
            margin-top: 10px;
        }
        .summary {
            background: #f8fafc;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .metric-card {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
        }
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            color: #3B82F6;
        }
        .metric-name {
            color: #666;
            margin-top: 5px;
        }
        .metric-change {
            margin-top: 5px;
            font-size: 0.9em;
        }
        .metric-change.positive { color: #10B981; }
        .metric-change.negative { color: #EF4444; }
        .insights {
            margin-top: 30px;
        }
        .insight-card {
            background: #f1f5f9;
            border-left: 4px solid #3B82F6;
            padding: 15px;
            margin-bottom: 15px;
        }
        .insight-title {
            font-weight: bold;
            color: #3B82F6;
            margin-bottom: 10px;
        }
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e2e8f0;
            color: #666;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>${data.title}</h1>
        <div class="meta-info">
            报告生成时间: ${currentDate} ${currentTime}
        </div>
    </div>
    
    <div class="summary">
        <h2>摘要</h2>
        <p>${data.summary}</p>
    </div>
    
    <div class="metrics-grid">
        ${data.metrics.map(metric => `
            <div class="metric-card">
                <div class="metric-value">${metric.value}</div>
                <div class="metric-name">${metric.name}</div>
                ${metric.change !== undefined ? `
                    <div class="metric-change ${metric.change >= 0 ? 'positive' : 'negative'}">
                        ${metric.change >= 0 ? '↑' : '↓'} ${Math.abs(metric.change)}%
                    </div>
                ` : ''}
            </div>
        `).join('')}
    </div>
    
    ${data.insights && data.insights.length > 0 ? `
        <div class="insights">
            <h2>关键洞察</h2>
            ${data.insights.map(insight => `
                <div class="insight-card">
                    <div class="insight-title">${insight.title}</div>
                    <p><strong>描述:</strong> ${insight.description}</p>
                    <p><strong>影响:</strong> ${insight.impact}</p>
                </div>
            `).join('')}
        </div>
    ` : ''}
    
    <div class="footer">
        <p>此报告由 iBuddy2 系统自动生成</p>
    </div>
</body>
</html>`;

  const blob = new Blob([htmlContent], { type: 'text/html;charset=utf-8;' });
  const timestamp = includeTimestamp ? `_${new Date().toISOString().split('T')[0]}` : '';
  const finalFilename = `${filename}${timestamp}.html`;
  
  downloadFile(blob, finalFilename);
}

/**
 * 下载文件的通用函数
 */
function downloadFile(blob: Blob, filename: string) {
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  link.style.display = 'none';
  
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  // 清理URL对象
  setTimeout(() => {
    URL.revokeObjectURL(url);
  }, 100);
}

/**
 * 格式化数据用于导出
 */
export function formatDataForExport(rawData: any[], type: 'analytics' | 'agents' | 'insights' | 'bookings') {
  switch (type) {
    case 'analytics':
      return rawData.map(item => ({
        日期: item.date,
        页面浏览量: item.pageViews,
        独立访客: item.uniqueVisitors,
        跳出率: `${item.bounceRate}%`,
        平均停留时间: item.avgDuration
      }));
    
    case 'agents':
      return rawData.map(agent => ({
        名称: agent.name,
        状态: agent.status,
        类型: agent.type,
        创建时间: new Date(agent.createdAt).toLocaleString('zh-CN'),
        最后活动: agent.lastActivity ? new Date(agent.lastActivity).toLocaleString('zh-CN') : '无'
      }));
    
    case 'insights':
      return rawData.map(insight => ({
        标题: insight.title,
        类型: insight.type,
        重要性: insight.importance,
        状态: insight.status,
        创建时间: new Date(insight.createdAt).toLocaleString('zh-CN')
      }));
    
    case 'bookings':
      return rawData.map(booking => ({
        客户姓名: booking.customerName,
        服务类型: booking.serviceType,
        预订时间: new Date(booking.bookingTime).toLocaleString('zh-CN'),
        状态: booking.status,
        金额: booking.amount ? `¥${booking.amount}` : '未设定'
      }));
    
    default:
      return rawData;
  }
}

/**
 * 批量导出多个数据集
 */
export async function bulkExport(datasets: Array<{
  name: string;
  data: any[];
  type: 'analytics' | 'agents' | 'insights' | 'bookings';
}>, format: 'csv' | 'json' = 'csv') {
  for (const dataset of datasets) {
    const formattedData = formatDataForExport(dataset.data, dataset.type);
    
    if (format === 'csv') {
      exportToCSV(formattedData, { filename: dataset.name });
    } else {
      exportToJSON(formattedData, { filename: dataset.name });
    }
    
    // 添加小延迟以避免同时下载太多文件
    await new Promise(resolve => setTimeout(resolve, 500));
  }
}