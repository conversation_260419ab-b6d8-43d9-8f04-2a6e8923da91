import React, { useState, useEffect } from 'react'
import { fetchSimpleIcons } from 'react-icon-cloud'

interface IconTestResult {
  name: string
  exists: boolean
  icon?: any
}

export function IconNameTester() {
  const [testResults, setTestResults] = useState<IconTestResult[]>([])
  const [loading, setLoading] = useState(true)

  // 测试不同的图标名称变体
  const iconNamesToTest = [
    // Lazada 变体
    'lazada',
    'lazada-group',
    'lazadagroup',
    'lazada-sg',
    'lazada-my',
    'lazada-th',
    'lazada-ph',
    'lazada-vn',
    'lazada-id',
    
    // Webhook 变体
    'webhook',
    'webhooks',
    'webhook-io',
    'webhookio',
    'webhook-site',
    'webhooksite',
    'webhook-relay',
    'webhookrelay',
    'zapier', // 作为webhook的替代
    'ifttt', // 作为webhook的替代
    'integromat', // 作为webhook的替代
    'make', // 作为webhook的替代
    'n8n', // 作为webhook的替代
    'pipedream', // 作为webhook的替代
    
    // 其他可能的替代
    'alibaba', // 阿里巴巴集团，可能包含Lazada
    'alibabacloud',
    'aliexpress',
    'taobao',
    'tmall'
  ]

  useEffect(() => {
    async function testIcons() {
      console.log('🔍 开始测试图标名称...')
      setLoading(true)
      
      try {
        const { simpleIcons } = await fetchSimpleIcons({
          slugs: iconNamesToTest
        })
        
        const results: IconTestResult[] = iconNamesToTest.map(name => ({
          name,
          exists: !!simpleIcons[name],
          icon: simpleIcons[name]
        }))
        
        console.log('📊 测试结果:', results)
        setTestResults(results)
      } catch (error) {
        console.error('❌ 图标测试失败:', error)
      } finally {
        setLoading(false)
      }
    }

    testIcons()
  }, [])

  if (loading) {
    return (
      <div className="p-6 border rounded-lg">
        <h3 className="text-lg font-semibold mb-4">图标名称测试器</h3>
        <div className="flex items-center gap-2">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
          <span>正在测试图标名称...</span>
        </div>
      </div>
    )
  }

  const existingIcons = testResults.filter(result => result.exists)
  const missingIcons = testResults.filter(result => !result.exists)

  return (
    <div className="p-6 border rounded-lg space-y-4">
      <h3 className="text-lg font-semibold">图标名称测试结果</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <h4 className="font-medium text-green-600 mb-2">✅ 存在的图标 ({existingIcons.length})</h4>
          <div className="space-y-1 max-h-40 overflow-y-auto">
            {existingIcons.map(result => (
              <div key={result.name} className="flex items-center gap-2 text-sm">
                <span className="text-green-500">✓</span>
                <code className="bg-green-50 px-2 py-1 rounded text-green-700">
                  {result.name}
                </code>
                {result.icon && (
                  <div 
                    className="w-4 h-4" 
                    dangerouslySetInnerHTML={{ __html: result.icon.svg }}
                  />
                )}
              </div>
            ))}
          </div>
        </div>
        
        <div>
          <h4 className="font-medium text-red-600 mb-2">❌ 不存在的图标 ({missingIcons.length})</h4>
          <div className="space-y-1 max-h-40 overflow-y-auto">
            {missingIcons.map(result => (
              <div key={result.name} className="flex items-center gap-2 text-sm">
                <span className="text-red-500">✗</span>
                <code className="bg-red-50 px-2 py-1 rounded text-red-700">
                  {result.name}
                </code>
              </div>
            ))}
          </div>
        </div>
      </div>
      
      {existingIcons.length > 0 && (
        <div className="mt-4 p-3 bg-blue-50 rounded">
          <h5 className="font-medium text-blue-800 mb-2">💡 推荐使用的图标:</h5>
          <div className="flex flex-wrap gap-2">
            {existingIcons.slice(0, 5).map(result => (
              <code key={result.name} className="bg-blue-100 px-2 py-1 rounded text-blue-800 text-sm">
                "{result.name}"
              </code>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
