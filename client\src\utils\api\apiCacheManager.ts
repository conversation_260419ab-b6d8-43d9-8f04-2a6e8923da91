/**
 * 统一API缓存管理器
 * 提供全局的API缓存控制和监控
 */
import { createDefaultCache, createLongTermCache, createNoCache, cacheManager } from './cacheConfig';
// import { AxiosInstance } from 'axios';

// 缓存实例类型
type CacheType = 'default' | 'longTerm' | 'noCache';

// 缓存实例映射
const cacheInstances: Record<CacheType, any> = {
  default: createDefaultCache(),
  longTerm: createLongTermCache(),
  noCache: createNoCache()
};

// API路径缓存策略配置
const API_CACHE_STRATEGIES: Record<string, CacheType> = {
  // 用户信息相关 - 长期缓存
  '/users/profile': 'longTerm',
  '/users/preferences': 'longTerm',
  '/users/settings': 'longTerm',
  
  // 系统配置 - 长期缓存
  '/config/system': 'longTerm',
  '/config/features': 'longTerm',
  '/config/pricing': 'longTerm',
  
  // 数据统计 - 默认缓存
  '/analytics/overview': 'default',
  '/analytics/modules': 'default',
  '/dashboard/metrics': 'default',
  '/dashboard/kpi': 'default',
  
  // 实时数据 - 无缓存
  '/realtime/status': 'noCache',
  '/realtime/notifications': 'noCache',
  '/monitoring/health': 'noCache',
  
  // 业务数据 - 默认缓存
  '/agents': 'default',
  '/bookings': 'default',
  '/content': 'default',
  '/platforms': 'default',
  
  // 支付相关 - 无缓存（敏感数据）
  '/payments': 'noCache',
  '/billing': 'noCache',
  '/subscriptions': 'noCache'
};

/**
 * 获取API请求的缓存策略
 */
function getCacheStrategy(url: string): CacheType {
  // 检查精确匹配
  if (API_CACHE_STRATEGIES[url]) {
    return API_CACHE_STRATEGIES[url];
  }
  
  // 检查部分匹配
  for (const path in API_CACHE_STRATEGIES) {
    if (url.startsWith(path)) {
      return API_CACHE_STRATEGIES[path];
    }
  }
  
  // 默认使用default缓存
  return 'default';
}

/**
 * API缓存管理器
 */
export const apiCacheManager = {
  /**
   * 获取缓存实例
   */
  getInstance: (url: string): any => {
    const strategy = getCacheStrategy(url);
    return cacheInstances[strategy];
  },

  /**
   * 执行GET请求（自动选择缓存策略）
   */
  get: async <T = any>(url: string, config?: any): Promise<T> => {
    const instance = apiCacheManager.getInstance(url);
    const response = await instance.get(url, config);
    return response.data;
  },

  /**
   * 执行POST请求（通常不缓存）
   */
  post: async <T = any>(url: string, data?: any, config?: any): Promise<T> => {
    const instance = cacheInstances.noCache;
    const response = await instance.post(url, data, config);
    
    // POST后清除相关缓存
    apiCacheManager.invalidateRelated(url);
    
    return response.data;
  },

  /**
   * 执行PUT请求（通常不缓存）
   */
  put: async <T = any>(url: string, data?: any, config?: any): Promise<T> => {
    const instance = cacheInstances.noCache;
    const response = await instance.put(url, data, config);
    
    // PUT后清除相关缓存
    apiCacheManager.invalidateRelated(url);
    
    return response.data;
  },

  /**
   * 执行DELETE请求（通常不缓存）
   */
  delete: async <T = any>(url: string, config?: any): Promise<T> => {
    const instance = cacheInstances.noCache;
    const response = await instance.delete(url, config);
    
    // DELETE后清除相关缓存
    apiCacheManager.invalidateRelated(url);
    
    return response.data;
  },

  /**
   * 清除相关缓存
   */
  invalidateRelated: (url: string) => {
    // 提取基础路径
    const basePath = url.split('?')[0].split('/')[1];
    cacheManager.clearCache(basePath);
    
    // 清除父级路径缓存
    const pathParts = basePath.split('/');
    for (let i = 1; i <= pathParts.length; i++) {
      const parentPath = pathParts.slice(0, i).join('/');
      cacheManager.clearCache(parentPath);
    }
  },

  /**
   * 预热缓存
   */
  warmup: async (urls: string[]) => {
    console.log('[Cache] Starting cache warmup...');
    
    const promises = urls.map(async (url) => {
      try {
        await apiCacheManager.get(url);
        console.log(`[Cache] Warmed up: ${url}`);
      } catch (error) {
        console.warn(`[Cache] Failed to warm up ${url}:`, error);
      }
    });
    
    await Promise.allSettled(promises);
    console.log('[Cache] Cache warmup completed');
  },

  /**
   * 获取缓存统计
   */
  getStats: () => {
    return {
      ...cacheManager.getStats(),
      strategies: API_CACHE_STRATEGIES
    };
  },

  /**
   * 清理所有缓存
   */
  clearAll: () => {
    cacheManager.clearAll();
  },

  /**
   * 清理过期缓存
   */
  clearExpired: () => {
    cacheManager.clearExpired();
  }
}; 