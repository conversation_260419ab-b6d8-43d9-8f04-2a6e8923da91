# 代理设置保存问题修复

## 问题描述

系统无法保存代理(agent)设置，出现以下问题：

1. 初始错误：`relation "public.agent_settings" does not exist`，尽管Supabase数据库中已存在该表
2. 后续问题：PUT请求超时（20秒后），导致设置无法保存

## 根本原因分析

1. API网关(端口3001)无法正确转发PUT请求到核心服务(端口3002)，GET请求正常工作
2. Supabase的upsert操作在某些情况下可能卡死，导致请求超时
3. 测试脚本验证：直接连接到核心服务端口3002成功，但通过网关的PUT请求失败

## 实施的修复方案

1. **客户端直连方案**：
   - 创建了`coreServiceApi.ts`客户端，直接连接到核心服务(3002端口)
   - 修改了`agentService.ts`中的`getAgentSettings`和`updateAgentSettings`函数，使用直接连接
   - 增加了超时时间从20秒到30秒，提高操作成功率

2. **核心服务端优化**：
   - 将Supabase的upsert操作分解为检查存在和更新/插入两步
   - 添加了数据库操作的额外超时保护(10秒)
   - 添加了全局请求超时保护(15秒)，确保请求不会无限挂起

3. **诊断工具**：
   - 创建了`scripts/check-db-connection.js`脚本，验证数据库表结构
   - 创建了`scripts/test-direct-connection.js`脚本，测试连接可靠性

## 如何验证修复成功

1. 运行测试脚本：`node scripts/test-direct-connection.js`查看连接状态
2. 在前端尝试保存代理设置，应该能够成功保存并看到更新结果
3. 查看核心服务日志，确认没有超时或错误

## 后续建议

1. 调查API网关配置，修复PUT请求转发问题
2. 考虑对API网关启用详细日志，以便更好地排查类似问题
3. 定期备份Supabase数据库，防止数据丢失

如有任何问题，请联系开发团队。

---
*更新日期: 2025-05-21* 