/**
 * 真实用户监控(RUM) - Real User Monitoring
 * 收集真实用户的性能数据和用户体验指标
 */

// import { generateId } from '../common';

// 简单的ID生成函数
const generateId = () => {
  return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

// 用户会话信息
export interface UserSession {
  /** 会话ID */
  id: string;
  /** 开始时间 */
  startTime: number;
  /** 用户IP (后端填充) */
  ip?: string;
  /** 用户位置 (后端填充) */
  location?: {
    country?: string;
    region?: string;
    city?: string;
  };
  /** 设备信息 */
  device: {
    /** 用户代理 */
    userAgent: string;
    /** 屏幕尺寸 */
    screenSize: string;
    /** 设备类型 */
    deviceType: 'mobile' | 'tablet' | 'desktop' | 'unknown';
    /** 操作系统 */
    os: string;
    /** 浏览器 */
    browser: string;
  };
  /** 网络信息 */
  network: {
    /** 连接类型 */
    connectionType: string;
    /** 下行速度(Mbps) */
    downlink?: number;
    /** RTT(ms) */
    rtt?: number;
    /** 网络质量 */
    quality: 'slow' | 'medium' | 'fast' | 'unknown';
  };
}

// 用户交互事件
export interface UserInteraction {
  /** 事件ID */
  id: string;
  /** 会话ID */
  sessionId: string;
  /** 事件类型 */
  type: 'navigation' | 'click' | 'api_call' | 'error' | 'custom';
  /** 事件目标 */
  target: string;
  /** 时间戳 */
  timestamp: number;
  /** 耗时(ms) */
  duration?: number;
  /** 是否成功 */
  success: boolean;
  /** 上下文数据 */
  context?: Record<string, any>;
}

// 性能样本
export interface PerformanceSample {
  /** 样本ID */
  id: string;
  /** 会话ID */
  sessionId: string;
  /** 页面URL */
  url: string;
  /** 时间戳 */
  timestamp: number;
  /** 核心Web指标 */
  webVitals: {
    fcp?: number;
    lcp?: number;
    fid?: number;
    cls?: number;
    tti?: number;
  };
  /** 自定义指标 */
  customMetrics: Record<string, number>;
  /** 资源加载性能 */
  resources: {
    /** JS加载时间 */
    js: number;
    /** CSS加载时间 */
    css: number;
    /** 图像加载时间 */
    img: number;
    /** 字体加载时间 */
    font: number;
    /** 其他资源加载时间 */
    other: number;
  };
}

// 区域性能统计
export interface RegionalPerformance {
  /** 区域标识 */
  region: string;
  /** 样本数量 */
  sampleCount: number;
  /** 平均核心Web指标 */
  avgWebVitals: {
    fcp: number;
    lcp: number;
    fid: number;
    cls: number;
  };
  /** 平均评分 */
  averageScore: number;
  /** 网络质量分布 */
  networkDistribution: {
    slow: number;
    medium: number;
    fast: number;
    unknown: number;
  };
}

// 用户体验分析
export interface UserExperienceAnalysis {
  /** 整体性能评分 */
  overallScore: number;
  /** 区域性能统计 */
  regionalStats: RegionalPerformance[];
  /** 设备类型性能 */
  deviceTypeStats: Record<string, {
    count: number;
    avgScore: number;
    avgLcp: number;
  }>;
  /** 连接类型性能 */
  connectionTypeStats: Record<string, {
    count: number;
    avgScore: number;
    avgLcp: number;
  }>;
  /** 性能异常页面 */
  problematicPages: {
    url: string;
    avgScore: number;
    sampleCount: number;
  }[];
}

/**
 * 真实用户监控系统
 */
export class RealUserMonitoring {
  private currentSession: UserSession | null = null;
  private isInitialized: boolean = false;
  private sampleInterval: number | null = null;
  private beaconUrl: string = '/api/analytics/rum';
  private samplingRate: number = 1.0; // 0.0-1.0, 1.0表示100%用户
  private maxQueueSize: number = 100;
  private dataQueue: (UserInteraction | PerformanceSample)[] = [];
  private lastSentTime: number = 0;
  private sendInterval: number = 10000; // 10秒
  
  /**
   * 创建真实用户监控实例
   * @param config 配置选项
   */
  constructor(config?: {
    beaconUrl?: string;
    samplingRate?: number;
    sendInterval?: number;
  }) {
    if (config?.beaconUrl) this.beaconUrl = config.beaconUrl;
    if (config?.samplingRate !== undefined) this.samplingRate = Math.max(0, Math.min(1, config.samplingRate));
    if (config?.sendInterval) this.sendInterval = config.sendInterval;
    
    // 从存储加载队列
    this.loadQueue();
  }
  
  /**
   * 初始化监控系统
   */
  public initialize(): void {
    if (this.isInitialized) return;
    
    // 随机采样，决定是否对此用户进行监控
    if (Math.random() > this.samplingRate) {
      console.log('[RUM] 该用户不在采样范围内');
      return;
    }
    
    // 创建用户会话
    this.currentSession = this.createUserSession();
    
    // 开始性能采样
    this.startPerformanceSampling();
    
    // 注册全局事件监听器
    this.registerEventListeners();
    
    // 设置定期发送数据
    this.startPeriodicDataSending();
    
    this.isInitialized = true;
    console.log('[RUM] 真实用户监控系统初始化完成', this.currentSession);
  }
  
  /**
   * 创建用户会话
   */
  private createUserSession(): UserSession {
    return {
      id: generateId(),
      startTime: Date.now(),
      device: this.detectDeviceInfo(),
      network: this.detectNetworkInfo()
    };
  }
  
  /**
   * 检测设备信息
   */
  private detectDeviceInfo(): UserSession['device'] {
    const userAgent = navigator.userAgent;
    const screenSize = `${screen.width}x${screen.height}`;
    
    // 简单的设备类型检测
    let deviceType: 'mobile' | 'tablet' | 'desktop' | 'unknown' = 'unknown';
    if (/mobile/i.test(userAgent)) {
      deviceType = 'mobile';
    } else if (/tablet/i.test(userAgent)) {
      deviceType = 'tablet';
    } else if (/desktop/i.test(userAgent) || !/mobile|tablet/i.test(userAgent)) {
      deviceType = 'desktop';
    }
    
    // 操作系统检测
    let os = 'unknown';
    if (/windows/i.test(userAgent)) os = 'Windows';
    else if (/mac/i.test(userAgent)) os = 'macOS';
    else if (/linux/i.test(userAgent)) os = 'Linux';
    else if (/android/i.test(userAgent)) os = 'Android';
    else if (/ios/i.test(userAgent)) os = 'iOS';
    
    // 浏览器检测
    let browser = 'unknown';
    if (/chrome/i.test(userAgent)) browser = 'Chrome';
    else if (/firefox/i.test(userAgent)) browser = 'Firefox';
    else if (/safari/i.test(userAgent)) browser = 'Safari';
    else if (/edge/i.test(userAgent)) browser = 'Edge';
    
    return {
      userAgent,
      screenSize,
      deviceType,
      os,
      browser
    };
  }
  
  /**
   * 检测网络信息
   */
  private detectNetworkInfo(): UserSession['network'] {
    const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection;
    
    let connectionType = 'unknown';
    let downlink: number | undefined;
    let rtt: number | undefined;
    let quality: 'slow' | 'medium' | 'fast' | 'unknown' = 'unknown';
    
    if (connection) {
      connectionType = connection.effectiveType || 'unknown';
      downlink = connection.downlink;
      rtt = connection.rtt;
      
      // 基于连接速度判断网络质量
      if (connection.effectiveType) {
        switch (connection.effectiveType) {
          case 'slow-2g':
          case '2g':
            quality = 'slow';
            break;
          case '3g':
            quality = 'medium';
            break;
          case '4g':
            quality = 'fast';
            break;
          default:
            quality = 'unknown';
        }
      }
    }
    
    return {
      connectionType,
      downlink,
      rtt,
      quality
    };
  }
  
  /**
   * 开始性能采样
   */
  private startPerformanceSampling(): void {
    // 每30秒采集一次性能数据
    this.sampleInterval = window.setInterval(() => {
      this.collectPerformanceSample();
    }, 30000);
    
    // 立即采集一次
    this.collectPerformanceSample();
  }
  
  /**
   * 收集性能样本
   */
  private collectPerformanceSample(): void {
    if (!this.currentSession) return;
    
    const sample: PerformanceSample = {
      id: generateId(),
      sessionId: this.currentSession.id,
      url: window.location.href,
      timestamp: Date.now(),
      webVitals: this.collectWebVitals(),
      customMetrics: this.collectCustomMetrics(),
      resources: this.calculateResourceStats(performance.getEntriesByType('resource'))
    };
    
    this.addToQueue(sample);
  }
  
  /**
   * 收集Web核心指标
   */
  private collectWebVitals(): PerformanceSample['webVitals'] {
    const vitals: PerformanceSample['webVitals'] = {};
    
    // 从PerformanceObserver获取或计算Web Vitals
    // 这里简化实现，实际应该使用web-vitals库
    
    return vitals;
  }
  
  /**
   * 收集自定义指标
   */
  private collectCustomMetrics(): Record<string, number> {
    return {
      memoryUsage: (performance as any).memory?.usedJSHeapSize || 0,
      domElements: document.querySelectorAll('*').length,
      timing: performance.now()
    };
  }
  
  /**
   * 计算资源统计
   */
  private calculateResourceStats(resources: any[]): PerformanceSample['resources'] {
    const stats = {
      js: 0,
      css: 0,
      img: 0,
      font: 0,
      other: 0
    };
    
    resources.forEach(resource => {
      const duration = resource.responseEnd - resource.requestStart;
      
      if (resource.name.includes('.js')) {
        stats.js += duration;
      } else if (resource.name.includes('.css')) {
        stats.css += duration;
      } else if (/\.(png|jpg|jpeg|gif|webp|svg)/.test(resource.name)) {
        stats.img += duration;
      } else if (/\.(woff|woff2|ttf|otf)/.test(resource.name)) {
        stats.font += duration;
      } else {
        stats.other += duration;
      }
    });
    
    return stats;
  }
  
  /**
   * 注册全局事件监听器
   */
  private registerEventListeners(): void {
    // 页面导航监控
    this.trackPageNavigation();
    
    // 点击事件监控
    document.addEventListener('click', this.handleClick, true);
    
    // 错误监控
    window.addEventListener('error', this.handleError);
    window.addEventListener('unhandledrejection', this.handlePromiseError);
    
    // 页面卸载监控
    window.addEventListener('beforeunload', this.handleBeforeUnload);
  }
  
  /**
   * 监控页面导航
   */
  private trackPageNavigation(): void {
    // 监控History API
    const originalPushState = history.pushState;
    const originalReplaceState = history.replaceState;
    
    history.pushState = (...args) => {
      this.handleRouteChange('pushState');
      return originalPushState.apply(history, args);
    };
    
    history.replaceState = (...args) => {
      this.handleRouteChange('replaceState');
      return originalReplaceState.apply(history, args);
    };
    
    // 监控popstate事件
    window.addEventListener('popstate', () => {
      this.handleRouteChange('popstate');
    });
    
    // 监控hashchange事件
    window.addEventListener('hashchange', () => {
      this.handleRouteChange('hashchange');
    });
  }
  
  /**
   * 处理路由变化
   */
  private handleRouteChange(method: string): void {
    this.trackInteraction({
      type: 'navigation',
      target: window.location.href,
      success: true,
      context: { method }
    });
  }
  
  /**
   * 处理点击事件
   */
  private handleClick = (event: MouseEvent): void => {
    const target = event.target as HTMLElement;
    if (!target) return;
    
    const interaction: Omit<UserInteraction, 'id' | 'sessionId' | 'timestamp'> = {
      type: 'click',
      target: this.getElementPath(target),
      success: true,
      context: {
        tagName: target.tagName,
        className: target.className,
        id: target.id,
        innerText: target.innerText?.substring(0, 100)
      }
    };
    
    this.trackInteraction(interaction);
  };
  
  /**
   * 获取元素路径
   */
  private getElementPath(element: HTMLElement): string {
    const path: string[] = [];
    let current: HTMLElement | null = element;
    
    while (current) {
      let selector = current.tagName.toLowerCase();
      
      if (current.id) {
        selector += `#${current.id}`;
        path.unshift(selector);
        break;
      } else if (current.className) {
        selector += `.${current.className.split(' ').join('.')}`;
      }
      
      path.unshift(selector);
      current = current.parentElement;
    }
    
    return path.join(' > ');
  }
  
  /**
   * 处理错误事件
   */
  private handleError = (event: ErrorEvent): void => {
    this.trackInteraction({
      type: 'error',
      target: event.filename || 'unknown',
      success: false,
      context: {
        message: event.message,
        lineno: event.lineno,
        colno: event.colno,
        stack: event.error?.stack
      }
    });
  };
  
  /**
   * 处理Promise错误
   */
  private handlePromiseError = (event: PromiseRejectionEvent): void => {
    this.trackInteraction({
      type: 'error',
      target: 'promise_rejection',
      success: false,
      context: {
        reason: event.reason?.toString?.().substring(0, 500) || 'Unknown promise rejection'
      }
    });
  };
  
  /**
   * 处理页面卸载
   */
  private handleBeforeUnload = (): void => {
    // 立即发送所有队列中的数据
    this.sendQueuedData(true);
  };
  
  /**
   * 跟踪用户交互
   */
  public trackInteraction(data: Omit<UserInteraction, 'id' | 'sessionId' | 'timestamp'>): void {
    if (!this.currentSession) return;
    
    const interaction: UserInteraction = {
      ...data,
      id: generateId(),
      sessionId: this.currentSession.id,
      timestamp: Date.now()
    };
    
    this.addToQueue(interaction);
  }
  
  /**
   * 添加数据到队列
   */
  private addToQueue(data: UserInteraction | PerformanceSample): void {
    this.dataQueue.push(data);
    
    // 如果队列过大，移除最老的数据
    if (this.dataQueue.length > this.maxQueueSize) {
      this.dataQueue.shift();
    }
    
    // 保存到本地存储
    this.saveQueue();
  }
  
  /**
   * 开始定期发送数据
   */
  private startPeriodicDataSending(): void {
    setInterval(() => {
      this.sendQueuedData();
    }, this.sendInterval);
  }
  
  /**
   * 发送队列中的数据
   */
  private sendQueuedData(forceSendBeacon: boolean = false): void {
    if (this.dataQueue.length === 0) return;
    
    const payload = {
      session: this.currentSession,
      data: [...this.dataQueue]
    };
    
    // 清空队列
    this.dataQueue = [];
    this.saveQueue();
    
    try {
      if (forceSendBeacon && 'sendBeacon' in navigator) {
        // 使用sendBeacon在页面卸载时发送数据
        navigator.sendBeacon(
          this.beaconUrl,
          JSON.stringify(payload)
        );
      } else {
        // 使用fetch发送数据
        fetch(this.beaconUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(payload),
          keepalive: true
        }).catch(error => {
          console.warn('[RUM] 发送数据失败:', error);
          // 如果发送失败，将数据重新添加到队列
          this.dataQueue.unshift(...payload.data);
          this.saveQueue();
        });
      }
      
      this.lastSentTime = Date.now();
    } catch (error) {
      console.error('[RUM] 发送数据时出错:', error);
      // 重新加入队列
      this.dataQueue.unshift(...payload.data);
      this.saveQueue();
    }
  }
  
  /**
   * 保存队列到本地存储
   */
  private saveQueue(): void {
    try {
      localStorage.setItem('rum_queue', JSON.stringify(this.dataQueue));
    } catch (error) {
      console.warn('[RUM] 保存队列失败:', error);
    }
  }
  
  /**
   * 从本地存储加载队列
   */
  private loadQueue(): void {
    try {
      const saved = localStorage.getItem('rum_queue');
      if (saved) {
        this.dataQueue = JSON.parse(saved);
      }
    } catch (error) {
      console.warn('[RUM] 加载队列失败:', error);
      this.dataQueue = [];
    }
  }
  
  /**
   * 销毁监控系统
   */
  public destroy(): void {
    // 发送剩余数据
    this.sendQueuedData(true);
    
    // 清除定时器
    if (this.sampleInterval !== null) {
      window.clearInterval(this.sampleInterval);
      this.sampleInterval = null;
    }
    
    // 移除事件监听器
    document.removeEventListener('click', this.handleClick, true);
    window.removeEventListener('error', this.handleError);
    window.removeEventListener('unhandledrejection', this.handlePromiseError);
    window.removeEventListener('beforeunload', this.handleBeforeUnload);
    
    this.isInitialized = false;
    this.currentSession = null;
    
    console.log('[RUM] 真实用户监控系统已销毁');
  }
  
  /**
   * 获取当前会话信息
   */
  public getCurrentSession(): UserSession | null {
    return this.currentSession;
  }
  
  /**
   * 获取队列状态
   */
  public getQueueStatus() {
    return {
      queueLength: this.dataQueue.length,
      lastSentTime: this.lastSentTime,
      isInitialized: this.isInitialized
    };
  }
}

// 创建默认实例
export const realUserMonitoring = new RealUserMonitoring();

// 自动初始化（如果在浏览器环境）
if (typeof window !== 'undefined') {
  // 等待DOM加载完成后初始化
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      realUserMonitoring.initialize();
    });
  } else {
    realUserMonitoring.initialize();
  }
}