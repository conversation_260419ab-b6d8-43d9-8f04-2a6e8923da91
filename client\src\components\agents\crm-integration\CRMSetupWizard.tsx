import React, { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { <PERSON>alog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  ArrowLeft, 
  ArrowRight, 
  CheckCircle, 
  Building,
  Key,
  Settings,
  TestTube
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { CRM_PROVIDERS, CRMConnection } from '@/types/crmIntegration'

interface CRMSetupWizardProps {
  providerId?: string | null;
  onComplete: (connection: CRMConnection) => void;
  onCancel: () => void;
}

const CRMSetupWizard: React.FC<CRMSetupWizardProps> = ({
  providerId,
  onComplete,
  onCancel
}) => {
  const { t } = useTranslation();
  const [currentStep, setCurrentStep] = useState(0);
  const [selectedProvider, setSelectedProvider] = useState(providerId || '');
  const [connectionName, setConnectionName] = useState('');
  const [credentials, setCredentials] = useState({
    apiKey: '',
    instanceUrl: '', username: '', password: ''
  });
  const [isConnecting, setIsConnecting] = useState(false);

  const steps = [
    {
      id: 'provider', title: t('agents.crmIntegration.setup.selectProvider'), description: 'Choose your CRM provider', icon: <Building className="h-5 w-5" />
    },
    {
      id: 'credentials', title: t('agents.crmIntegration.setup.authenticate'), description: 'Enter your credentials', icon: <Key className="h-5 w-5" />
    },
    {
      id: 'configure', title: t('agents.crmIntegration.setup.configure'), description: 'Configure connection settings', icon: <Settings className="h-5 w-5" />
    },
    {
      id: 'test', title: t('agents.crmIntegration.setup.testSync'), description: 'Test the connection', icon: <TestTube className="h-5 w-5" />
    }
  ];

  const provider = selectedProvider ? CRM_PROVIDERS[selectedProvider] : null;

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleComplete = async () => {
    if (!provider) return;

    setIsConnecting(true);
    
    // 模拟连接过程
    await new Promise(resolve => setTimeout(resolve, 2000));

    const newConnection: CRMConnection = {
      id: `conn-${Date.now()}`,
      providerId: provider.id, name: connectionName || `${provider.displayName} Connection`, status: 'connected',
      createdAt: new Date().toISOString(), config: {
        instanceUrl: credentials.instanceUrl,
        customFields: {}, mappings: [], filters: []
      }, credentials: {
        type: provider.authType as any,
        apiKey: credentials.apiKey, username: credentials.username, password: credentials.password
      },
      syncSettings: {
        enabled: true, direction: 'bidirectional', frequency: 'hourly',
        batchSize: 100,
        conflictResolution: 'crm_wins',
        syncObjects: []
      },
      healthCheck: {
        status: 'healthy',
        lastCheckedAt: new Date().toISOString(), issues: [], metrics: {
          responseTime: 150,
          successRate: 1.0,
          errorRate: 0.0
        }
      }, usage: {
        period: 'today',
        apiCalls: 0,
        syncedRecords: 0, errors: 0,
        dataTransferred: 0
      }
    };

    setIsConnecting(false);
    onComplete(newConnection);
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <div className="space-y-4">
            <div className="grid grid-cols-1 gap-4">
              {Object.values(CRM_PROVIDERS).map((provider) => (
                <Card
                  key={provider.id}
                  className={cn(
                    "cursor-pointer transition-all duration-200 hover:shadow-md",
                    selectedProvider === provider.id && "ring-2 ring-primary"
                  )}
                  onClick={() => setSelectedProvider(provider.id)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 rounded-lg bg-gray-100 flex items-center justify-center">
                        <Building className="h-5 w-5" />
                      </div>
                      <div className="flex-1">
                        <h3 className="font-medium">{provider.displayName}</h3>
                        <p className="text-sm text-muted-foreground">{provider.description}</p>
                        <div className="flex gap-2 mt-2">
                          <Badge variant="secondary">{provider.tier}</Badge>
                          <Badge variant="outline">{provider.category}</Badge>
                        </div>
                      </div>
                      {selectedProvider === provider.id && (
                        <CheckCircle className="h-5 w-5 text-green-600" />
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        );

      case 1:
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="connectionName">Connection Name</Label>
              <Input
                id="connectionName"
                value={connectionName}
                onChange={(e) => setConnectionName(e.target.value)}
                placeholder={`${provider?.displayName} Connection`}
              />
            </div>

            {provider?.authType === 'api_key' && (
              <div>
                <Label htmlFor="apiKey">API Key</Label>
                <Input
                  id="apiKey"
                  type="password"
                  value={credentials.apiKey}
                  onChange={(e) => setCredentials(prev => ({ ...prev, apiKey: e.target.value }))}
                  placeholder="Enter your API key"
                />
              </div>
            )}

            {provider?.authType === 'oauth2' && (
              <div>
                <Label htmlFor="instanceUrl">Instance URL</Label>
                <Input
                  id="instanceUrl"
                  value={credentials.instanceUrl}
                  onChange={(e) => setCredentials(prev => ({ ...prev, instanceUrl: e.target.value }))}
                  placeholder="https://your-instance.salesforce.com"
                />
              </div>
            )}

            {provider?.authType === 'basic_auth' && (
              <>
                <div>
                  <Label htmlFor="username">Username</Label>
                  <Input
                    id="username"
                    value={credentials.username}
                    onChange={(e) => setCredentials(prev => ({ ...prev, username: e.target.value }))}
                    placeholder="Enter your username"
                  />
                </div>
                <div>
                  <Label htmlFor="password">Password</Label>
                  <Input
                    id="password"
                    type="password"
                    value={credentials.password}
                    onChange={(e) => setCredentials(prev => ({ ...prev, password: e.target.value }))}
                    placeholder="Enter your password"
                  />
                </div>
              </>
            )}
          </div>
        );

      case 2:
        return (
          <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-base">Sync Settings</CardTitle>
                <CardDescription>Configure how data should be synchronized</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="syncDirection">Sync Direction</Label>
                  <Select defaultValue="bidirectional">
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="bidirectional">Bidirectional</SelectItem>
                      <SelectItem value="to_crm">To CRM Only</SelectItem>
                      <SelectItem value="from_crm">From CRM Only</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="syncFrequency">Sync Frequency</Label>
                  <Select defaultValue="hourly">
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="real_time">Real-time</SelectItem>
                      <SelectItem value="hourly">Hourly</SelectItem>
                      <SelectItem value="daily">Daily</SelectItem>
                      <SelectItem value="weekly">Weekly</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>
          </div>
        );

      case 3:
        return (
          <div className="space-y-4">
            <Card>
              <CardContent className="pt-6">
                <div className="text-center space-y-4">
                  {isConnecting ? (
                    <>
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                      <h3 className="text-lg font-semibold">Testing Connection</h3>
                      <p className="text-muted-foreground">
                        Verifying credentials and establishing connection...
                      </p>
                    </>
                  ) : (
                    <>
                      <CheckCircle className="h-12 w-12 text-green-600 mx-auto" />
                      <h3 className="text-lg font-semibold">Ready to Connect</h3>
                      <p className="text-muted-foreground">
                        All settings configured. Click "Complete Setup" to establish the connection.
                      </p>
                    </>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        );

      default:
        return null;
    }
  };

  const canProceed = () => {
    switch (currentStep) {
      case 0:
        return !!selectedProvider;
      case 1:
        return !!connectionName && (
          (provider?.authType === 'api_key' && !!credentials.apiKey) ||
          (provider?.authType === 'oauth2' && !!credentials.instanceUrl) ||
          (provider?.authType === 'basic_auth' && !!credentials.username && !!credentials.password)
        );
      case 2:
        return true;
      case 3:
        return true;
      default:
        return false;
    }
  };

  return (
    <Dialog open={true} onOpenChange={onCancel}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>{t('agents.crmIntegration.setup.title')}</DialogTitle>
          <DialogDescription>
            Set up a new CRM integration in a few simple steps
          </DialogDescription>
        </DialogHeader>

        {/* Progress indicator */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            {steps.map((step, index) => (
              <div
                key={step.id}
                className={cn(
                  "flex items-center gap-2 text-sm",
                  index <= currentStep ? "text-primary" : "text-muted-foreground"
                )}
              >
                <div className={cn(
                  "w-8 h-8 rounded-full flex items-center justify-center border-2",
                  index < currentStep ? "bg-primary border-primary text-primary-foreground" :
                  index === currentStep ? "border-primary text-primary" :
                  "border-muted text-muted-foreground"
                )}>
                  {index < currentStep ? (
                    <CheckCircle className="h-4 w-4" />
                  ) : (
                    <span>{index + 1}</span>
                  )}
                </div>
                <span className="hidden sm:inline">{step.title}</span>
              </div>
            ))}
          </div>
          <Progress value={(currentStep / (steps.length - 1)) * 100} />
        </div>

        {/* Step content */}
        <div className="min-h-[300px]">
          <div className="mb-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              {steps[currentStep].icon}
              {steps[currentStep].title}
            </h3>
            <p className="text-muted-foreground">{steps[currentStep].description}</p>
          </div>
          {renderStepContent()}
        </div>

        {/* Navigation */}
        <div className="flex justify-between">
          <Button
            variant="outline"
            onClick={currentStep === 0 ? onCancel : handleBack}
            disabled={isConnecting}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            {currentStep === 0 ? 'Cancel' : 'Back'}
          </Button>

          <Button
            onClick={currentStep === steps.length - 1 ? handleComplete : handleNext}
            disabled={!canProceed() || isConnecting}
          >
            {currentStep === steps.length - 1 ? 'Complete Setup' : 'Next'}
            {currentStep < steps.length - 1 && <ArrowRight className="h-4 w-4 ml-2" />}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default CRMSetupWizard;
