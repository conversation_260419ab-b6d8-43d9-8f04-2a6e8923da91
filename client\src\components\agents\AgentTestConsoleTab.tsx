﻿/* eslint-disable unicode-bom */
import React, { useState, useEffect, useRef, useCallback } from "react";
import { Agent } from "@/types/agent";
import { useMutation } from "@tanstack/react-query";
import { testAgent } from "@/services/agentService";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Send, Bot, User, Eraser } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface AgentTestConsoleTabProps {
  agent: Agent;
}

interface Message {
  id: string;
  text: string;
  sender: "user" | "bot" | "error";
  timestamp: Date;
  metadata?: {
    confidence: number;
    intentDetected: string;
    model: string;
    processingTime: number;
  };
}

export function AgentTestConsoleTab({ agent }: AgentTestConsoleTabProps) {
  const { toast } = useToast();
  const [inputText, setInputText] = useState("");
  const [messages, setMessages] = useState<Message[]>([]);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const getLocalStorageKey = useCallback(() => `agentTestConsoleHistory_${agent.id}`, [agent.id]);

  useEffect(() => {
    const savedMessages = localStorage.getItem(getLocalStorageKey());
    if (savedMessages) {
      try {
        const parsedMessages: Message[] = JSON.parse(savedMessages).map((msg: any) => ({
          ...msg,
          timestamp: new Date(msg.timestamp)
        }));
        setMessages(parsedMessages);
      } catch (error) {
        console.error("Failed to parse messages from localStorage:", error);
        localStorage.removeItem(getLocalStorageKey());
      }
    }
  }, [getLocalStorageKey]);

  useEffect(() => {
    if (messages.length > 0) {
      localStorage.setItem(getLocalStorageKey(), JSON.stringify(messages));
    } else {
      localStorage.removeItem(getLocalStorageKey());
    }
  }, [messages, getLocalStorageKey]);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  const mutation = useMutation<
    { reply: string; [key: string]: any },
    Error,
    { inputText: string }
  >({
    mutationFn: (variables) => {
      console.log(`[代理测试] 发送测试请求到代理 ${agent.id}`, { 
        inputText: variables.inputText,
        agentType: agent.type,
        agentName: agent.name
      });
      
      const timeoutPromise = new Promise<{ reply: string }>((_, reject) => {
        setTimeout(() => {
          console.log("[代理测试] 请求超时，未在预期时间内收到响应");
          reject(new Error("请求超时，AI服务未能在预期时间内响应。"));
        }, 60000);
      });
      
      const directFetchPromise = async () => {
        console.log("[代理测试] 使用直接fetch调用AI服务");
        try {
          const requestBody = {
            text: variables.inputText,
            userId: "test-user-id",
            agentId: agent.id,
            config: {
              agentName: agent.name,
              knowledgeBaseSources: "external",
              externalKBFilesInfo: [
                {
                  id: "6405d841-6fe1-4fa6-bd44-aec95afefb74",
                  name: "MBBcurrent_562058087947_2025-04-30.pdf"
                }
              ]
            }
          };
          
          console.log("[代理测试] 发送fetch请求到AI服务", requestBody);
          
          const response = await fetch("http://localhost:3003/api/chat/message", {
            method: "POST",
            headers: {
              "Content-Type": "application/json"
            },
            body: JSON.stringify(requestBody)
          });
          
          if (!response.ok) {
            console.error("[代理测试] AI服务返回错误状态码:", response.status);
            throw new Error(`服务器返回错误: ${response.status} ${response.statusText}`);
          }
          
          const data = await response.json();
          console.log("[代理测试] AI服务返回数据:", data);
          return data;
        } catch (error) {
          console.error("[代理测试] 直接fetch调用失败:", error);
          throw error;
        }
      };
      
      return Promise.race([
        testAgent(agent.id, variables),
        timeoutPromise,
        directFetchPromise()
      ]);
    },
    onSuccess: (data) => {
      console.log(`[代理测试] 收到成功响应:`, {
        reply: data.reply,
        confidence: data.confidence,
        intentDetected: data.intentDetected,
        model: data.model,
        processingTime: data.processingTime
      });
      
      if (data.error) {
        console.warn(`[代理测试] 服务器返回了错误响应: ${data.error}`);
        
        setMessages(prev => [...prev, {
          id: Date.now().toString() + "-bot",
          text: data.reply || `服务器返回了错误: ${data.error}`,
          sender: "bot",
          timestamp: new Date(),
          metadata: {
            confidence: data.confidence || 0,
            intentDetected: data.intentDetected || "error",
            model: data.model || "fallback",
            processingTime: data.processingTime || 0
          }
        }]);
        return;
      }
      
      let plainTextReply = data.reply;
      if (plainTextReply && plainTextReply.startsWith("{") && plainTextReply.includes("content")) {
        try {
          const jsonData = JSON.parse(plainTextReply);
          plainTextReply = jsonData.response?.content?.content || 
                          jsonData.content?.content || 
                          jsonData.reply || 
                          "无法解析响应内容";
        } catch (e) {
          console.warn("[代理测试] 无法解析JSON响应:", e);
        }
      }
      
      setMessages(prev => [...prev, {
        id: Date.now().toString() + "-bot",
        text: plainTextReply || JSON.stringify(data),
        sender: "bot",
        timestamp: new Date(),
        metadata: {
          confidence: data.confidence,
          intentDetected: data.intentDetected,
          model: data.model,
          processingTime: data.processingTime
        }
      }]);
    },
    onError: (err) => {
      console.error(`[代理测试] 请求失败:`, err);
      
      let errorMessage = `错误: ${err.message || "无法获取响应，请检查代理日志以获取更多详细信息。"}`;
      
      if (err.message && err.message.includes("timeout")) {
        errorMessage = "代理响应超时。请检查AI服务状态或稍后重试。";
      } else if (err.message && err.message.includes("fetch")) {
        errorMessage = "网络连接错误。请检查AI服务是否运行中。";
      }
      
      setMessages(prev => [...prev, {
        id: Date.now().toString() + "-error",
        text: errorMessage,
        sender: "error",
        timestamp: new Date()
      }]);
      
      toast({
        title: "测试失败",
        description: errorMessage,
        variant: "destructive"
      });
    }
  });

  const handleSendMessage = (e?: React.FormEvent) => {
    e?.preventDefault();
    
    if (!inputText.trim()) return;
    
    const userMessage: Message = {
      id: Date.now().toString() + "-user",
      text: inputText,
      sender: "user",
      timestamp: new Date()
    };
    
    setMessages(prev => [...prev, userMessage]);
    
    mutation.mutate({ inputText });
    setInputText("");
  };

  const handleClearChat = () => {
    setMessages([]);
    toast({
      title: "聊天已清空",
      description: "测试对话历史已清除"
    });
  };

  const formatTimestamp = (timestamp: Date) => {
    return new Intl.DateTimeFormat("zh-CN", {
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit"
    }).format(timestamp);
  };

  const renderMessage = (message: Message) => {
    const isUser = message.sender === "user";
    const isError = message.sender === "error";
    
    return (
      <div key={message.id} className={`flex ${isUser ? "justify-end" : "justify-start"} mb-4`}>
        <div className={`flex items-start max-w-[70%] ${isUser ? "flex-row-reverse" : "flex-row"}`}>
          <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
            isUser ? "bg-blue-500 ml-2" : isError ? "bg-red-500 mr-2" : "bg-gray-500 mr-2"
          }`}>
            {isUser ? <User className="w-4 h-4 text-white" /> : <Bot className="w-4 h-4 text-white" />}
          </div>
          
          <div className={`px-4 py-2 rounded-lg ${
            isUser ? "bg-blue-500 text-white" : isError ? "bg-red-100 border border-red-300" : "bg-gray-100"
          }`}>
            <p className={`text-sm ${isError ? "text-red-700" : isUser ? "text-white" : "text-gray-800"}`}>
              {message.text}
            </p>
            
            <div className={`text-xs mt-1 ${isError ? "text-red-500" : isUser ? "text-blue-100" : "text-gray-500"}`}>
              {formatTimestamp(message.timestamp)}
              {message.metadata && (
                <span className="ml-2">
                   置信度: {(message.metadata.confidence * 100).toFixed(1)}%
                   模型: {message.metadata.model}
                   {message.metadata.processingTime}ms
                </span>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="flex flex-col h-[600px]">
      <Card className="flex-1 flex flex-col">
        <CardHeader className="pb-3">
          <div className="flex justify-between items-center">
            <div>
              <CardTitle className="text-lg">测试控制台</CardTitle>
              <CardDescription>与代理 "{agent.name}" 进行实时对话测试</CardDescription>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={handleClearChat}
              disabled={messages.length === 0}
              className="flex items-center gap-2"
            >
              <Eraser className="w-4 h-4" />
              清空对话
            </Button>
          </div>
        </CardHeader>
        
        <CardContent className="flex-1 flex flex-col">
          <div className="flex-1 overflow-y-auto mb-4 border rounded-lg p-4 bg-gray-50">
            {messages.length === 0 ? (
              <div className="text-center text-gray-500 mt-8">
                <Bot className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                <p>开始与代理对话测试</p>
                <p className="text-sm">输入消息来测试代理响应</p>
              </div>
            ) : (
              <div>
                {messages.map(renderMessage)}
                <div ref={messagesEndRef} />
              </div>
            )}
          </div>
          
          <form onSubmit={handleSendMessage} className="flex gap-2">
            <Textarea
              value={inputText}
              onChange={(e) => setInputText(e.target.value)}
              placeholder="输入测试消息..."
              className="flex-1 resize-none"
              rows={3}
              onKeyDown={(e) => {
                if (e.key === "Enter" && !e.shiftKey) {
                  e.preventDefault();
                  handleSendMessage();
                }
              }}
            />
            <Button
              type="submit"
              disabled={!inputText.trim() || mutation.isPending}
              className="self-end"
            >
              {mutation.isPending ? (
                <div className="w-4 h-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
              ) : (
                <Send className="w-4 h-4" />
              )}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
