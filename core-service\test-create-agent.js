require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

console.log('🧪 开始测试agent创建...');

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_ANON_KEY);

// 测试数据
const testAgent = {
  name: 'Test Agent ' + Date.now(),
  agentType: 'AI_AUTO_REPLY',
  description: 'Test agent creation',
  status: 'DRAFT'
};

console.log('📝 准备创建agent:', testAgent);

supabase
  .from('agents')
  .insert([testAgent])
  .select()
  .then(({ data, error }) => {
    if (error) {
      console.error('❌ 创建agent失败:', error);
    } else {
      console.log('✅ Agent创建成功:', data[0]);
    }
    process.exit(0);
  })
  .catch(err => {
    console.error('❌ 意外错误:', err);
    process.exit(1);
  });

// 设置超时
setTimeout(() => {
  console.error('⏱️ 操作超时');
  process.exit(1);
}, 10000); 