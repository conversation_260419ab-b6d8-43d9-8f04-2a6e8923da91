import React, { useState, useEffect, useCallback } from 'react';
// import { useTranslation } from 'react-i18next';
import { Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Trash2, 
  Copy, 
  Settings, 
  AlertCircle, 
  CheckCircle,
  Code,
  TestTube
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { FlowNode, FlowNodeType } from '@/types/flowBuilder';

interface NodeConfigPanelProps {
  node: FlowNode;
  onUpdateConfig: (config: any) => void;
  onDelete: () => void;
  className?: string
}

const NodeConfigPanel: React.FC<NodeConfigPanelProps> = ({
  node,
  onUpdateConfig,
  onDelete, className
}) => {
  const [localConfig, setLocalConfig] = useState(node.data.config || {});
  const [isValid, setIsValid] = useState(true);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);

  // 同步本地配置到父组件
  useEffect(() => {
    const timer = setTimeout(() => {
      onUpdateConfig(localConfig)
    }, 300);

    return () => clearTimeout(timer);
  }, [localConfig, onUpdateConfig]);

  // 更新本地配置
  const updateLocalConfig = (key: string, value: any) => {
    setLocalConfig(prev => ({ ...prev, [key]: value }));
  };

  // 验证配置
  const validateConfig = useCallback(() => {
    const errors: string[] = [];

    switch (node.type) {
      case FlowNodeType.TRIGGER:
        if (!localConfig.triggerType) {
          errors.push('Trigger type is required')
        }
        if (localConfig.triggerType === 'keyword' && !localConfig.triggerValue) {
          errors.push('Trigger keyword is required')
        }
        break;

      case FlowNodeType.CONDITION:
        if (!localConfig.conditionType) {
          errors.push('Condition type is required')
        }
        if (!localConfig.conditionValue) {
          errors.push('Condition value is required')
        }
        break;

      case FlowNodeType.AI_RESPONSE:
        if (!localConfig.prompt) {
          errors.push('AI prompt is required')
        }
        break;

      case FlowNodeType.API_CALL:
        if (!localConfig.apiUrl) {
          errors.push('API URL is required')
        }
        if (!localConfig.apiMethod) {
          errors.push('API method is required')
        }
        break;

      case FlowNodeType.DELAY:
        if (!localConfig.delayValue || localConfig.delayValue <= 0) {
          errors.push('Delay value must be greater than 0')
        }
        break;

      case FlowNodeType.EMAIL:
      case FlowNodeType.SMS:
        if (!localConfig.recipient) {
          errors.push('Recipient is required')
        }
        if (!localConfig.content) {
          errors.push('Message content is required')
        }
        break
    }

    setValidationErrors(errors);
    setIsValid(errors.length === 0)
  }, [node.type, localConfig]);

  // 在配置变化时验证
  useEffect(() => {
    validateConfig()
  }, [localConfig, node.type, validateConfig]);

  // 渲染不同节点类型的配置表单
  const renderConfigForm = () => {
    switch (node.type) {
      case FlowNodeType.TRIGGER:
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="triggerType">Trigger Type</Label>
              <Select
                value={localConfig.triggerType || ''}
                onValueChange={(value) => updateLocalConfig('triggerType', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select trigger type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="message">Any Message</SelectItem>
                  <SelectItem value="keyword">Keyword</SelectItem>
                  <SelectItem value="intent">Intent</SelectItem>
                  <SelectItem value="webhook">Webhook</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {localConfig.triggerType === 'keyword' && (
              <div>
                <Label htmlFor="triggerValue">Keyword</Label>
                <Input
                  id="triggerValue"
                  value={localConfig.triggerValue || ''}
                  onChange={(e) => updateLocalConfig('triggerValue', e.target.value)}
                  placeholder="Enter keyword..."
                />
              </div>
            )}

            {localConfig.triggerType === 'intent' && (
              <div>
                <Label htmlFor="triggerValue">Intent Name</Label>
                <Input
                  id="triggerValue"
                  value={localConfig.triggerValue || ''}
                  onChange={(e) => updateLocalConfig('triggerValue', e.target.value)}
                  placeholder="Enter intent name..."
                />
              </div>
            )}
          </div>
        );

      case FlowNodeType.CONDITION:
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="conditionType">Condition Type</Label>
              <Select
                value={localConfig.conditionType || ''}
                onValueChange={(value) => updateLocalConfig('conditionType', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select condition type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="text_contains">Text Contains</SelectItem>
                  <SelectItem value="intent_match">Intent Match</SelectItem>
                  <SelectItem value="variable_equals">Variable Equals</SelectItem>
                  <SelectItem value="user_property">User Property</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="conditionValue">Condition Value</Label>
              <Input
                id="conditionValue"
                value={localConfig.conditionValue || ''}
                onChange={(e) => updateLocalConfig('conditionValue', e.target.value)}
                placeholder="Enter condition value..."
              />
            </div>

            <div>
              <Label htmlFor="conditionOperator">Operator</Label>
              <Select
                value={localConfig.conditionOperator || 'equals'}
                onValueChange={(value) => updateLocalConfig('conditionOperator', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="equals">Equals</SelectItem>
                  <SelectItem value="contains">Contains</SelectItem>
                  <SelectItem value="starts_with">Starts With</SelectItem>
                  <SelectItem value="ends_with">Ends With</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        );

      case FlowNodeType.AI_RESPONSE:
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="prompt">AI Prompt</Label>
              <Textarea
                id="prompt"
                value={localConfig.prompt || ''}
                onChange={(e) => updateLocalConfig('prompt', e.target.value)}
                placeholder="Enter AI prompt..."
                rows={4}
              />
            </div>

            <div>
              <Label htmlFor="responseTemplate">Response Template (Optional)</Label>
              <Textarea
                id="responseTemplate"
                value={localConfig.responseTemplate || ''}
                onChange={(e) => updateLocalConfig('responseTemplate', e.target.value)}
                placeholder="Use {{response}} to insert AI response..."
                rows={2}
              />
            </div>

            <div>
              <Label htmlFor="temperature">Creativity Level: {localConfig.temperature || 0.7}</Label>
              <Slider
                value={[localConfig.temperature || 0.7]}
                onValueChange={(value) => updateLocalConfig('temperature', value[0])}
                max={1}
                min={0}
                step={0.1}
                className="mt-2"
              />
            </div>

            <div>
              <Label htmlFor="maxTokens">Max Tokens</Label>
              <Input
                id="maxTokens"
                type="number"
                value={localConfig.maxTokens || 200}
                onChange={(e) => updateLocalConfig('maxTokens', parseInt(e.target.value))}
                min={50}
                max={4000}
              />
            </div>
          </div>
        );

      case FlowNodeType.HUMAN_HANDOFF:
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="handoffReason">Handoff Reason</Label>
              <Textarea
                id="handoffReason"
                value={localConfig.handoffReason || ''}
                onChange={(e) => updateLocalConfig('handoffReason', e.target.value)}
                placeholder="Reason for transferring to human agent..."
                rows={3}
              />
            </div>

            <div>
              <Label htmlFor="assignTo">Assign To (Optional)</Label>
              <Input
                id="assignTo"
                value={localConfig.assignTo || ''}
                onChange={(e) => updateLocalConfig('assignTo', e.target.value)}
                placeholder="Specific agent or department..."
              />
            </div>

            <div>
              <Label htmlFor="priority">Priority</Label>
              <Select
                value={localConfig.priority || 'medium'}
                onValueChange={(value) => updateLocalConfig('priority', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="low">Low</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                  <SelectItem value="urgent">Urgent</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        );

      case FlowNodeType.API_CALL:
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="apiUrl">API URL</Label>
              <Input
                id="apiUrl"
                value={localConfig.apiUrl || ''}
                onChange={(e) => updateLocalConfig('apiUrl', e.target.value)}
                placeholder="https://api.example.com/endpoint"
              />
            </div>

            <div>
              <Label htmlFor="apiMethod">HTTP Method</Label>
              <Select
                value={localConfig.apiMethod || 'GET'}
                onValueChange={(value) => updateLocalConfig('apiMethod', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="GET">GET</SelectItem>
                  <SelectItem value="POST">POST</SelectItem>
                  <SelectItem value="PUT">PUT</SelectItem>
                  <SelectItem value="DELETE">DELETE</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="apiHeaders">Headers (JSON)</Label>
              <Textarea
                id="apiHeaders"
                value={localConfig.apiHeaders || '{}'}
                onChange={(e) => updateLocalConfig('apiHeaders', e.target.value)}
                placeholder='{"Content-Type": "application/json"}'
                rows={3}
              />
            </div>

            {(localConfig.apiMethod === 'POST' || localConfig.apiMethod === 'PUT') && (
              <div>
                <Label htmlFor="apiBody">Request Body (JSON)</Label>
                <Textarea
                  id="apiBody"
                  value={localConfig.apiBody || ''}
                  onChange={(e) => updateLocalConfig('apiBody', e.target.value)}
                  placeholder='{"key": "value"}'
                  rows={4}
                />
              </div>
            )}
          </div>
        );

      case FlowNodeType.DELAY:
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="delayValue">Delay Duration</Label>
              <Input
                id="delayValue"
                type="number"
                value={localConfig.delayValue || 1}
                onChange={(e) => updateLocalConfig('delayValue', parseInt(e.target.value))}
                min={1}
              />
            </div>

            <div>
              <Label htmlFor="delayType">Time Unit</Label>
              <Select
                value={localConfig.delayType || 'seconds'}
                onValueChange={(value) => updateLocalConfig('delayType', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="seconds">Seconds</SelectItem>
                  <SelectItem value="minutes">Minutes</SelectItem>
                  <SelectItem value="hours">Hours</SelectItem>
                  <SelectItem value="days">Days</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        );

      default:
        return (
          <div className="text-center py-8 text-muted-foreground">
            <Settings className="h-8 w-8 mx-auto mb-2" />
            <p>No configuration options available for this node type.</p>
          </div>
        )
    }
  };

  return (
    <div className={cn("h-full flex flex-col", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm">Node Configuration</CardTitle>
          <div className="flex items-center gap-1">
            <Button variant="ghost" size="sm" onClick={() => {}}>
              <Copy className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={onDelete}>
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="outline">{node.type}</Badge>
          {isValid ? (
            <Badge variant="default" className="bg-green-100 text-green-800">
              <CheckCircle className="h-3 w-3 mr-1" />
              Valid
            </Badge>
          ) : (
            <Badge variant="destructive">
              <AlertCircle className="h-3 w-3 mr-1" />
              Invalid
            </Badge>
          )}
        </div>
      </CardHeader>

      <CardContent className="flex-1 overflow-hidden">
        <ScrollArea className="h-full">
          <div className="space-y-4">
            {/* 基本信息 */}
            <div>
              <Label htmlFor="nodeLabel">Node Label</Label>
              <Input
                id="nodeLabel"
                value={node.data.label}
                onChange={(e) => {
                  // 这里需要更新节点的基本信息，不是配置
                  // 可以通过回调函数处理
                }}
                placeholder="Enter node label..."
              />
            </div>

            <Separator />

            {/* 节点特定配置 */}
            {renderConfigForm()}

            {/* 验证错误 */}
            {validationErrors.length > 0 && (
              <>
                <Separator />
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <AlertCircle className="h-4 w-4 text-red-500" />
                    <span className="text-sm font-medium text-red-700">Configuration Errors</span>
                  </div>
                  {validationErrors.map((error, index) => (
                    <div key={index} className="text-sm text-red-600 bg-red-50 p-2 rounded">
                      {error}
                    </div>
                  ))}
                </div>
              </>
            )}
          </div>
        </ScrollArea>
      </CardContent>

      {/* 底部操作 */}
      <div className="p-4 border-t bg-muted/30">
        <div className="flex gap-2">
          <Button variant="outline" size="sm" className="flex-1">
            <TestTube className="h-4 w-4 mr-1" />
            Test
          </Button>
          <Button variant="outline" size="sm" className="flex-1">
            <Code className="h-4 w-4 mr-1" />
            Code
          </Button>
        </div>
      </div>
    </div>
  )
};

export default NodeConfigPanel;
