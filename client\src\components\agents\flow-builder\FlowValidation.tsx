import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  CheckCircle, 
  AlertCircle, 
  AlertTriangle,
  RefreshCw
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { FlowNode, FlowEdge, FlowNodeType, FlowValidationResult, FlowValidationError, FlowValidationWarning } from '@/types/flowBuilder';

interface FlowValidationProps {
  nodes: FlowNode[];
  edges: FlowEdge[];
  className?: string
}

const FlowValidation: React.FC<FlowValidationProps> = ({ nodes, edges, className }) => {
  const { t } = useTranslation();

  // 验证流程
  const validationResult = useMemo((): FlowValidationResult => {
    const errors: FlowValidationError[] = [];
    const warnings: FlowValidationWarning[] = [];

    // 检查是否有起始节点
    const triggerNodes = nodes.filter(node => node.type === FlowNodeType.TRIGGER);
    if (triggerNodes.length === 0) {
      errors.push({
        type: 'missing_connection', message: 'Flow must have at least one trigger node', severity: 'error'
      });
    } else if (triggerNodes.length > 1) {
      warnings.push({
        type: 'best_practice', message: 'Multiple trigger nodes detected. Consider using a single entry point.', severity: 'warning'
      });
    }

    // 检查节点连接
    nodes.forEach(node => {
      const outgoingEdges = edges.filter(edge => edge.source === node.id);
      const incomingEdges = edges.filter(edge => edge.target === node.id);

      // 检查孤立节点
      if (node.type !== FlowNodeType.TRIGGER && incomingEdges.length === 0) {
        errors.push({
          nodeId: node.id,
          type: 'unreachable_node',
          message: `Node "${node.data.label}" is not reachable from any trigger`,
          severity: 'error'
        });
      }

      // 检查终端节点
      if (node.type !== FlowNodeType.END && 
          node.type !== FlowNodeType.HUMAN_HANDOFF && 
          outgoingEdges.length === 0) {
        errors.push({
          nodeId: node.id,
          type: 'missing_connection',
          message: `Node "${node.data.label}" has no outgoing connections`,
          severity: 'error'
        });
      }

      // 检查条件节点的分支
      if (node.type === FlowNodeType.CONDITION) {
        const trueEdge = outgoingEdges.find(edge => edge.sourceHandle === 'true');
        const falseEdge = outgoingEdges.find(edge => edge.sourceHandle === 'false');
        
        if (!trueEdge) {
          errors.push({
            nodeId: node.id, type: 'missing_connection', message: `Condition node "${node.data.label}" is missing TRUE branch`, severity: 'error'
          });
        }
        
        if (!falseEdge) {
          errors.push({
            nodeId: node.id, type: 'missing_connection', message: `Condition node "${node.data.label}" is missing FALSE branch`, severity: 'error'
          });
        }
      }

      // 检查节点配置
      if (!validateNodeConfig(node)) {
        errors.push({
          nodeId: node.id, type: 'invalid_config', message: `Node "${node.data.label}" has invalid configuration`, severity: 'error'
        });
      }
    });

    // 检查循环引用
    const cycles = detectCycles(nodes, edges);
    cycles.forEach(cycle => {
      errors.push({
        type: 'circular_reference', message: `Circular reference detected: ${cycle.join(' → ')}`, severity: 'error'
      });
    });

    // 性能警告
    if (nodes.length > 50) {
      warnings.push({
        type: 'performance', message: 'Large flow detected. Consider breaking into smaller sub-flows for better performance.', severity: 'warning'
      });
    }

    const apiCallNodes = nodes.filter(node => node.type === FlowNodeType.API_CALL);
    if (apiCallNodes.length > 10) {
      warnings.push({
        type: 'performance', message: 'Many API calls detected. Consider optimizing to reduce latency.', severity: 'warning'
      });
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }, [nodes, edges]);

  // 验证单个节点配置
  const validateNodeConfig = (node: FlowNode): boolean => {
    const config = node.data.config || {};

    switch (node.type) {
      case FlowNodeType.TRIGGER:
        return !!config.triggerType;
      
      case FlowNodeType.CONDITION:
        return !!config.conditionType && !!config.conditionValue;
      
      case FlowNodeType.AI_RESPONSE:
        return !!config.prompt;
      
      case FlowNodeType.API_CALL:
        return !!config.apiUrl && !!config.apiMethod;
      
      case FlowNodeType.DELAY:
        return !!config.delayValue && config.delayValue > 0;
      
      case FlowNodeType.EMAIL:
      case FlowNodeType.SMS:
        return !!config.recipient && !!config.content;
      
      case FlowNodeType.VARIABLE_SET:
        return !!config.variableName;
      
      default:
        return true
    }
  };

  // 检测循环引用
  const detectCycles = (nodes: FlowNode[], edges: FlowEdge[]): string[][] => {
    const cycles: string[][] = [];
    const visited = new Set<string>();
    const recursionStack = new Set<string>();
    const path: string[] = [];

    const dfs = (nodeId: string): boolean => {
      if (recursionStack.has(nodeId)) {
        // 找到循环，记录路径
        const cycleStart = path.indexOf(nodeId);
        if (cycleStart !== -1) {
          const cycle = path.slice(cycleStart);
          const cycleLabels = cycle.map(id => {
            const node = nodes.find(n => n.id === id);
            return node?.data.label || id
          });
          cycles.push(cycleLabels)
        }
        return true
      }

      if (visited.has(nodeId)) {
        return false
      }

      visited.add(nodeId);
      recursionStack.add(nodeId);
      path.push(nodeId);

      const outgoingEdges = edges.filter(edge => edge.source === nodeId);
      for (const edge of outgoingEdges) {
        if (dfs(edge.target)) {
          return true
        }
      }

      recursionStack.delete(nodeId);
      path.pop();
      return false
    };

    nodes.forEach(node => {
      if (!visited.has(node.id)) {
        dfs(node.id)
      }
    });

    return cycles
  };

  // 获取验证状态图标和颜色
  const getValidationStatus = () => {
    if (validationResult.errors.length > 0) {
      return {
        icon: <AlertCircle className="h-4 w-4" />, color: 'text-red-600',
        bgColor: 'bg-red-50',
        borderColor: 'border-red-200'
      };
    }
    if (validationResult.warnings.length > 0) {
      return {
        icon: <AlertTriangle className="h-4 w-4" />, color: 'text-yellow-600',
        bgColor: 'bg-yellow-50',
        borderColor: 'border-yellow-200'
      };
    }
    return {
      icon: <CheckCircle className="h-4 w-4" />, color: 'text-green-600',
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200'
    };
  };

  const status = getValidationStatus();

  return (
    <Card className={cn("", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm flex items-center gap-2">
            {status.icon}
            {t('agents.flowBuilder.validation.title')}
          </CardTitle>
          <Button variant="ghost" size="sm">
            <RefreshCw className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>

      <CardContent>
        <div className="space-y-3">
          {/* 验证摘要 */}
          <div className={cn("p-3 rounded-lg border", status.bgColor, status.borderColor)}>
            <div className="flex items-center gap-2 mb-2">
              <span className={cn("font-medium", status.color)}>
                {validationResult.isValid ? 
                  t('agents.flowBuilder.validation.noErrors') : 
                  t('agents.flowBuilder.validation.errorsFound', { count: validationResult.errors.length  })
                }
              </span>
            </div>
            
            <div className="flex gap-2">
              {validationResult.errors.length > 0 && (
                <Badge variant="destructive" className="text-xs">
                  {validationResult.errors.length} Error{validationResult.errors.length !== 1 ? 's' : ''}
                </Badge>
              )}
              {validationResult.warnings.length > 0 && (
                <Badge variant="secondary" className="text-xs bg-yellow-100 text-yellow-800">
                  {validationResult.warnings.length} Warning{validationResult.warnings.length !== 1 ? 's' : ''}
                </Badge>
              )}
              <Badge variant="outline" className="text-xs">
                {nodes.length} Node{nodes.length !== 1 ? 's' : ''}
              </Badge>
              <Badge variant="outline" className="text-xs">
                {edges.length} Connection{edges.length !== 1 ? 's' : ''}
              </Badge>
            </div>
          </div>

          {/* 错误和警告列表 */}
          {(validationResult.errors.length > 0 || validationResult.warnings.length > 0) && (
            <ScrollArea className="h-32">
              <div className="space-y-2">
                {validationResult.errors.map((error, index) => (
                  <div key={`error-${index}`} className="flex items-start gap-2 p-2 bg-red-50 rounded text-sm">
                    <AlertCircle className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
                    <div className="flex-1">
                      <p className="text-red-700">{error.message}</p>
                      {error.nodeId && (
                        <p className="text-red-500 text-xs mt-1">Node ID: {error.nodeId}</p>
                      )}
                    </div>
                  </div>
                ))}

                {validationResult.warnings.map((warning, index) => (
                  <div key={`warning-${index}`} className="flex items-start gap-2 p-2 bg-yellow-50 rounded text-sm">
                    <AlertTriangle className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                    <div className="flex-1">
                      <p className="text-yellow-700">{warning.message}</p>
                      {warning.nodeId && (
                        <p className="text-yellow-600 text-xs mt-1">Node ID: {warning.nodeId}</p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          )}

          {/* 流程统计 */}
          <div className="grid grid-cols-2 gap-2 text-xs text-muted-foreground">
            <div>Trigger Nodes: {nodes.filter(n => n.type === FlowNodeType.TRIGGER).length}</div>
            <div>End Nodes: {nodes.filter(n => n.type === FlowNodeType.END).length}</div>
            <div>AI Nodes: {nodes.filter(n => n.type === FlowNodeType.AI_RESPONSE).length}</div>
            <div>API Calls: {nodes.filter(n => n.type === FlowNodeType.API_CALL).length}</div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
};

export default FlowValidation;
