import React from 'react'
import { Container, <PERSON>, Col } from '@/components/compat/react-bootstrap-compat'
import RuleBrowser from '../components/dashboard/components/RuleBrowser'
import { useFeatureGuard } from '../hooks/useFeatureGuard'

export default function AutoReplySettingsPage() {
  useFeatureGuard('free');
  document.title = 'Auto Reply Settings'
  return (
    <Container fluid className="mt-4">
      <Row>
        <Col>
          <h2>Auto Reply Settings</h2>
          <RuleBrowser />
        </Col>
      </Row>
    </Container>)
  );
};