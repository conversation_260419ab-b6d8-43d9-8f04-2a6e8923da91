/**
 * 智能数据预取系统
 * 基于用户行为分析和机器学习预测，智能预取用户可能需要的数据
 */

import { storageCache } from '../api/localStorageCache';

// 用户行为类型
export interface UserBehavior {
  /** 行为ID */
  id: string;
  /** 行为类型 */
  type: 'page_visit' | 'click' | 'search' | 'api_call' | 'navigation';
  /** 目标资源 */
  target: string;
  /** 时间戳 */
  timestamp: number;
  /** 上下文信息 */
  context?: Record<string, any>;
  /** 会话ID */
  sessionId: string;
}

// 预测模式
export interface PredictionPattern {
  /** 模式ID */
  id: string;
  /** 触发条件 */
  trigger: string;
  /** 预测目标 */
  targets: string[];
  /** 置信度 */
  confidence: number;
  /** 使用次数 */
  usage: number;
  /** 最后更新时间 */
  lastUpdated: number;
}

// 预取配置
export interface PrefetchConfig {
  /** 最大预取数量 */
  maxPrefetchCount?: number;
  /** 最小置信度阈值 */
  minConfidence?: number;
  /** 预取延迟(毫秒) */
  prefetchDelay?: number;
  /** 缓存时间(秒) */
  cacheTime?: number;
  /** 是否启用机器学习 */
  enableML?: boolean;
  /** 用户行为历史保留天数 */
  behaviorRetentionDays?: number;
}

// 默认配置
const DEFAULT_CONFIG: PrefetchConfig = {
  maxPrefetchCount: 5,
  minConfidence: 0.6,
  prefetchDelay: 1000,
  cacheTime: 300,
  enableML: true,
  behaviorRetentionDays: 30
};

/**
 * 智能数据预取器
 */
export class SmartPrefetcher {
  private config: Required<PrefetchConfig>;
  private behaviors: UserBehavior[] = [];
  private patterns: PredictionPattern[] = [];
  private sessionId: string = this.generateSessionId();
  private prefetchQueue: Set<string> = new Set();
  private isAnalyzing: boolean = false;
  
  constructor(config: PrefetchConfig = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config } as Required<PrefetchConfig>;
    
    // 加载历史行为和模式
    this.loadBehaviorHistory();
    this.loadPredictionPatterns();
    
    // 启动定期分析
    this.startPeriodicAnalysis();
  }
  
  /**
   * 记录用户行为
   */
  public recordBehavior(
    type: UserBehavior['type'],
    target: string,
    context?: Record<string, any>
  ): void {
    const behavior: UserBehavior = {
      id: this.generateId(),
      type,
      target,
      timestamp: Date.now(),
      context,
      sessionId: this.sessionId
    };
    
    this.behaviors.push(behavior);
    
    // 限制内存中的行为记录数量
    if (this.behaviors.length > 1000) {
      this.behaviors = this.behaviors.slice(-800);
    }
    
    // 保存到本地存储
    this.saveBehaviorHistory();
    
    // 触发实时预测
    this.triggerRealTimePrediction(behavior);
  }
  
  /**
   * 实时预测和预取
   */
  private async triggerRealTimePrediction(behavior: UserBehavior): Promise<void> {
    // 查找匹配的模式
    const matchingPatterns = this.patterns.filter(pattern => {
      return pattern.trigger === behavior.target && 
             pattern.confidence >= this.config.minConfidence;
    });
    
    // 按置信度排序
    matchingPatterns.sort((a, b) => b.confidence - a.confidence);
    
    // 预取推荐的资源
    const prefetchTargets = matchingPatterns
      .slice(0, this.config.maxPrefetchCount)
      .flatMap(pattern => pattern.targets)
      .filter(target => !this.prefetchQueue.has(target));
    
    if (prefetchTargets.length > 0) {
      setTimeout(() => {
        this.executePrefetch(prefetchTargets);
      }, this.config.prefetchDelay);
    }
  }
  
  /**
   * 执行数据预取
   */
  private async executePrefetch(targets: string[]): Promise<void> {
    for (const target of targets) {
      if (this.prefetchQueue.has(target)) continue;
      
      this.prefetchQueue.add(target);
      
      try {
        // 检查是否已缓存
        const cached = storageCache.get(target);
        if (cached) {
          console.log(`[智能预取] 数据已缓存: ${target}`);
          continue;
        }
        
        // 执行预取请求
        const response = await fetch(target, {
          method: 'GET',
          headers: {
            'X-Prefetch': 'true'
          }
        });
        
        if (response.ok) {
          const data = await response.json();
          
          // 缓存预取的数据
          storageCache.set(target, data, this.config.cacheTime);
          
          console.log(`[智能预取] 预取成功: ${target}`);
        }
      } catch (error) {
        console.warn(`[智能预取] 预取失败: ${target}`, error);
      } finally {
        this.prefetchQueue.delete(target);
      }
    }
  }
  
  /**
   * 启动定期分析
   */
  private startPeriodicAnalysis(): void {
    // 每5分钟分析一次用户行为模式
    setInterval(() => {
      if (!this.isAnalyzing && this.config.enableML) {
        this.analyzeUserBehavior();
      }
    }, 5 * 60 * 1000);
  }
  
  /**
   * 分析用户行为模式
   */
  private async analyzeUserBehavior(): Promise<void> {
    if (this.isAnalyzing || this.behaviors.length < 10) return;
    
    this.isAnalyzing = true;
    
    try {
      // 清理过期的行为记录
      this.cleanupExpiredBehaviors();
      
      // 分析不同类型的模式
      this.analyzePageSequences();
      this.analyzeApiCallPatterns();
      this.analyzeNavigationPatterns();
      
      // 保存更新的模式
      this.savePredictionPatterns();
      
      console.log('[智能预取] 行为分析完成，发现', this.patterns.length, '个模式');
      
    } catch (error) {
      console.error('[智能预取] 行为分析失败:', error);
    } finally {
      this.isAnalyzing = false;
    }
  }
  
  /**
   * 分析页面访问序列
   */
  private analyzePageSequences(): void {
    const pageVisits = this.behaviors.filter(b => b.type === 'page_visit');
    const sessions = this.groupBySession(pageVisits);
    
    sessions.forEach(session => {
      for (let i = 0; i < session.length - 1; i++) {
        const current = session[i];
        const next = session[i + 1];
        
        // 如果两个页面访问在合理时间间隔内
        if (next.timestamp - current.timestamp < 30000) { // 30秒内
          this.updateOrCreatePattern(current.target, [next.target], 'page_sequence');
        }
      }
    });
  }
  
  /**
   * 分析API调用模式
   */
  private analyzeApiCallPatterns(): void {
    const apiCalls = this.behaviors.filter(b => b.type === 'api_call');
    const sessions = this.groupBySession(apiCalls);
    
    sessions.forEach(session => {
      // 寻找频繁的API调用组合
      const callCounts = new Map<string, number>();
      
      for (let i = 0; i < session.length - 1; i++) {
        const current = session[i];
        const next = session[i + 1];
        
        if (next.timestamp - current.timestamp < 10000) { // 10秒内
          const pattern = `${current.target}->${next.target}`;
          callCounts.set(pattern, (callCounts.get(pattern) || 0) + 1);
        }
      }
      
      callCounts.forEach((count, pattern) => {
        if (count >= 2) { // 至少出现2次
          const [trigger, target] = pattern.split('->');
          this.updateOrCreatePattern(trigger, [target], 'api_sequence');
        }
      });
    });
  }
  
  /**
   * 分析导航模式
   */
  private analyzeNavigationPatterns(): void {
    const navigations = this.behaviors.filter(b => b.type === 'navigation');
    
    // 分析导航后的常见行为
    navigations.forEach(nav => {
      const followingBehaviors = this.behaviors.filter(b => 
        b.timestamp > nav.timestamp && 
        b.timestamp < nav.timestamp + 60000 && // 1分钟内
        b.sessionId === nav.sessionId
      );
      
      if (followingBehaviors.length > 0) {
        const targets = followingBehaviors.map(b => b.target);
        this.updateOrCreatePattern(nav.target, targets, 'navigation_follow');
      }
    });
  }
  
  /**
   * 更新或创建预测模式
   */
  private updateOrCreatePattern(trigger: string, targets: string[], type: string): void {
    const existingPattern = this.patterns.find(p => p.trigger === trigger);
    
    if (existingPattern) {
      // 更新现有模式
      targets.forEach(target => {
        if (!existingPattern.targets.includes(target)) {
          existingPattern.targets.push(target);
        }
      });
      
      existingPattern.usage += 1;
      existingPattern.confidence = Math.min(0.95, existingPattern.confidence + 0.05);
      existingPattern.lastUpdated = Date.now();
    } else {
      // 创建新模式
      const newPattern: PredictionPattern = {
        id: this.generateId(),
        trigger,
        targets: [...targets],
        confidence: 0.3, // 初始置信度
        usage: 1,
        lastUpdated: Date.now()
      };
      
      this.patterns.push(newPattern);
    }
  }
  
  /**
   * 按会话分组行为
   */
  private groupBySession(behaviors: UserBehavior[]): UserBehavior[][] {
    const sessions = new Map<string, UserBehavior[]>();
    
    behaviors.forEach(behavior => {
      if (!sessions.has(behavior.sessionId)) {
        sessions.set(behavior.sessionId, []);
      }
      sessions.get(behavior.sessionId)!.push(behavior);
    });
    
    // 按时间排序每个会话的行为
    sessions.forEach(session => {
      session.sort((a, b) => a.timestamp - b.timestamp);
    });
    
    return Array.from(sessions.values());
  }
  
  /**
   * 清理过期的行为记录
   */
  private cleanupExpiredBehaviors(): void {
    const cutoffTime = Date.now() - (this.config.behaviorRetentionDays * 24 * 60 * 60 * 1000);
    
    this.behaviors = this.behaviors.filter(behavior => behavior.timestamp > cutoffTime);
    
    // 清理低置信度的模式
    this.patterns = this.patterns.filter(pattern => 
      pattern.confidence > 0.1 && 
      pattern.lastUpdated > cutoffTime
    );
  }
  
  /**
   * 加载行为历史
   */
  private loadBehaviorHistory(): void {
    try {
      const saved = storageCache.get('smart_prefetch_behaviors');
      if (saved && Array.isArray(saved)) {
        this.behaviors = saved;
        console.log('[智能预取] 加载了', this.behaviors.length, '条行为记录');
      }
    } catch (error) {
      console.warn('[智能预取] 加载行为历史失败:', error);
    }
  }
  
  /**
   * 保存行为历史
   */
  private saveBehaviorHistory(): void {
    try {
      // 只保存最近的500条记录到本地存储
      const recentBehaviors = this.behaviors.slice(-500);
      storageCache.set('smart_prefetch_behaviors', recentBehaviors, 86400 * this.config.behaviorRetentionDays);
    } catch (error) {
      console.warn('[智能预取] 保存行为历史失败:', error);
    }
  }
  
  /**
   * 加载预测模式
   */
  private loadPredictionPatterns(): void {
    try {
      const saved = storageCache.get('smart_prefetch_patterns');
      if (saved && Array.isArray(saved)) {
        this.patterns = saved;
        console.log('[智能预取] 加载了', this.patterns.length, '个预测模式');
      }
    } catch (error) {
      console.warn('[智能预取] 加载预测模式失败:', error);
    }
  }
  
  /**
   * 保存预测模式
   */
  private savePredictionPatterns(): void {
    try {
      storageCache.set('smart_prefetch_patterns', this.patterns, 86400 * this.config.behaviorRetentionDays);
    } catch (error) {
      console.warn('[智能预取] 保存预测模式失败:', error);
    }
  }
  
  /**
   * 生成随机ID
   */
  private generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }
  
  /**
   * 生成会话ID
   */
  private generateSessionId(): string {
    const existing = sessionStorage.getItem('smart_prefetch_session_id');
    if (existing) {
      return existing;
    }
    
    const sessionId = this.generateId();
    sessionStorage.setItem('smart_prefetch_session_id', sessionId);
    return sessionId;
  }
  
  /**
   * 获取统计信息
   */
  public getStatistics(): {
    totalBehaviors: number;
    totalPatterns: number;
    topPatterns: PredictionPattern[];
    prefetchHits: number;
  } {
    const topPatterns = this.patterns
      .sort((a, b) => b.confidence - a.confidence)
      .slice(0, 10);
    
    return {
      totalBehaviors: this.behaviors.length,
      totalPatterns: this.patterns.length,
      topPatterns,
      prefetchHits: 0 // TODO: 实现命中率统计
    };
  }
  
  /**
   * 手动预取数据
   */
  public async manualPrefetch(targets: string[]): Promise<void> {
    await this.executePrefetch(targets);
  }
  
  /**
   * 清除所有数据
   */
  public clearAllData(): void {
    this.behaviors = [];
    this.patterns = [];
    storageCache.remove('smart_prefetch_behaviors');
    storageCache.remove('smart_prefetch_patterns');
    console.log('[智能预取] 已清除所有数据');
  }
}

// 导出默认实例
export const smartPrefetcher = new SmartPrefetcher();
export default smartPrefetcher;