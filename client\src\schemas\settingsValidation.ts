import { z } from 'zod';

// 通用验证模式
export const WorkingHoursSchema = z.object({
  enabled: z.boolean(),
  timezone: z.string().min(1, '请选择时区'),
  schedule: z.record(z.object({
  start: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, '请输入有效的时间格式 (HH:MM)'),
    end: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, '请输入有效的时间格式 (HH:MM)'),
    closed: z.boolean() })).refine((schedule) => {
    // 验证结束时间晚于开始时间
    return Object.values(schedule).every(({ start, end, closed }) => {
      if (closed) return true;
      const startTime = new Date(`1970-01-01T${start}:00`);
      const endTime = new Date(`1970-01-01T${end}:00`);
      return endTime > startTime
})
}, {
    message: '结束时间必须晚于开始时间'
  }) });

export const ServiceTypeSchema = z.object({
  id: z.string(),
  name: z.string().min(1, '服务名称不能为空').max(50, '服务名称不能超过50个字符'),
  description: z.string().optional(),
  duration: z.number().min(5, '服务时长至少5分钟').max(480, '服务时长不能超过8小时'),
  price: z.number().min(0, '价格不能为负数').optional(),
  isActive: z.boolean() });

export const NotificationSettingsSchema = z.object({
  email: z.object({
  enabled: z.boolean(),
    provider: z.enum(['smtp', 'sendgrid', 'mailgun', 'ses']),
    fromEmail: z.string().email('请输入有效的邮箱地址').optional().or(z.literal('')),
    fromName: z.string().min(1, '发件人名称不能为空').max(50, '发件人名称不能超过50个字符').optional(),
    smtpHost: z.string().optional(),
    smtpPort: z.number().min(1, '端口号必须大于0').max(65535, '端口号不能超过65535').optional() }),
  sms: z.object({
  enabled: z.boolean(),
    provider: z.enum(['twilio', 'aliyun', 'local']) }),
  push: z.object({
  enabled: z.boolean() }),
  templates: z.record(z.object({
  enabled: z.boolean(),
    subject: z.string().min(1, '主题不能为空').max(100, '主题不能超过100个字符'),
    content: z.string().min(1, '内容不能为空').max(1000, '内容不能超过1000个字符') })) });

// OnSite Booking 验证schemas
export const OnsiteGeneralSettingsSchema = z.object({
  name: z.string().min(1, '服务名称不能为空').max(50, '服务名称不能超过50个字符'),
  description: z.string().max(200, '描述不能超过200个字符').optional(),
  enabled: z.boolean(),
  serviceName: z.string().min(1, '服务名称不能为空').max(50, '服务名称不能超过50个字符'),
  maxDailyBookings: z.number().min(1, '每日最大预约数至少为1').max(200, '每日最大预约数不能超过200'),
  requireConfirmation: z.boolean(),
  advanceBookingDays: z.number().min(0, '提前预约天数不能为负数').max(90, '提前预约天数不能超过90天'),
  appointmentTimeSlotMinutes: z.number().min(15, '预约时间段至少15分钟').max(480, '预约时间段不能超过8小时'),
  bufferTime: z.number().min(0, '缓冲时间不能为负数').max(120, '缓冲时间不能超过2小时'),
  allowSameDayBooking: z.boolean(),
  requireClientPhone: z.boolean(),
  requireClientAddress: z.boolean() });

export const MapSettingsSchema = z.object({
  centerLatitude: z.number().min(-90, '纬度范围：-90 到 90').max(90, '纬度范围：-90 到 90'),
  centerLongitude: z.number().min(-180, '经度范围：-180 到 180').max(180, '经度范围：-180 到 180'),
  serviceRadiusKm: z.number().min(1, '服务半径至少1公里').max(500, '服务半径不能超过500公里'),
  mapProvider: z.enum(['google', 'amap', 'baidu']),
  showTraffic: z.boolean(),
  allowCustomAreas: z.boolean() });

export const AISchedulingSettingsSchema = z.object({
  enabled: z.boolean(),
  optimizationGoal: z.enum(['time', 'cost', 'customer_satisfaction', 'efficiency']),
  maxOptimizationRadius: z.number().min(1, '优化半径至少1公里').max(100, '优化半径不能超过100公里'),
  considerTrafficData: z.boolean(),
  allowDynamicRescheduling: z.boolean(),
  customerImportanceWeight: z.number().min(0, '权重不能为负数').max(1, '权重不能超过1'),
  distanceOptimizationWeight: z.number().min(0, '权重不能为负数').max(1, '权重不能超过1'),
  urgencyWeight: z.number().min(0, '权重不能为负数').max(1, '权重不能超过1'),
  timeWindowFlexibility: z.number().min(0, '灵活性不能为负数').max(1, '灵活性不能超过1') });

// Walk-in Booking 验证schemas
export const WalkinGeneralSettingsSchema = z.object({
  serviceName: z.string().min(1, '服务名称不能为空').max(50, '服务名称不能超过50个字符'),
  enabled: z.boolean(),
  maxDailyWalkins: z.number().min(1, '每日最大人数至少为1').max(1000, '每日最大人数不能超过1000'),
  maxConcurrentCustomers: z.number().min(1, '同时服务人数至少为1').max(50, '同时服务人数不能超过50'),
  averageServiceTimeMinutes: z.number().min(1, '平均服务时间至少1分钟').max(480, '平均服务时间不能超过8小时'),
  bufferTimeMinutes: z.number().min(0, '缓冲时间不能为负数').max(60, '缓冲时间不能超过1小时'),
  waitTimeEstimationMethod: z.enum(['STATIC', 'DYNAMIC', 'MACHINE_LEARNING']),
  enableSelfService: z.boolean(),
  requirePhoneNumber: z.boolean(),
  allowWalkinRegistration: z.boolean(),
  queueDisplayEnabled: z.boolean() });

export const QueueSettingsSchema = z.object({
  maxWaitTimeMinutes: z.number().min(5, '最大等待时间至少5分钟').max(300, '最大等待时间不能超过5小时'),
  maxQueueLength: z.number().min(1, '最大队列长度至少为1').max(200, '最大队列长度不能超过200'),
  queueDisplayEnabled: z.boolean(),
  checkInMethod: z.enum(['KIOSK', 'STAFF', 'BOTH']),
  waitTimeDisplayMessage: z.string().max(100, '消息不能超过100个字符'),
  queueFullMessage: z.string().max(100, '消息不能超过100个字符'),
  queueMethod: z.enum(['fifo', 'priority', 'estimated-time', 'appointment_first', 'vip_priority']),
  estimationMethod: z.enum(['average', 'real-time', 'ml-prediction']),
  enableVirtualQueue: z.boolean(),
  maxQueueSize: z.number().min(1, '队列大小至少为1').max(500, '队列大小不能超过500'),
  allowCancellation: z.boolean(),
  notifyPosition: z.boolean(),
  allowPreRegistration: z.boolean(),
  autoAdvanceQueue: z.boolean(),
  waitTimeWarningMinutes: z.number().min(1, '警告时间至少1分钟').max(120, '警告时间不能超过2小时'),
  positionUpdateIntervalMinutes: z.number().min(1, '更新间隔至少1分钟').max(30, '更新间隔不能超过30分钟'),
  enableWaitTimeEstimation: z.boolean(),
  removeOnTimeout: z.boolean() });

export const QRCodeSettingsSchema = z.object({
  enabled: z.boolean(),
  size: z.number().min(100, '二维码尺寸至少100像素').max(500, '二维码尺寸不能超过500像素'),
  color: z.string().regex(/^#[0-9A-F]{6}$/i, '请输入有效的颜色代码'),
  backgroundColor: z.string().regex(/^#[0-9A-F]{6}$/i, '请输入有效的颜色代码'),
  logo: z.string().optional(),
  errorCorrection: z.enum(['L', 'M', 'Q', 'H']),
  includeText: z.boolean(),
  text: z.string().max(50, '文字不能超过50个字符').optional(),
  autoRefresh: z.boolean(),
  refreshIntervalMinutes: z.number().min(1, '刷新间隔至少1分钟').max(60, '刷新间隔不能超过1小时') });

export const CheckInSettingsSchema = z.object({
  method: z.enum(['kiosk', 'staff', 'both']),
  kioskSettings: z.object({
  enabled: z.boolean(),
    welcomeMessage: z.string().max(200, '欢迎消息不能超过200个字符'),
    language: z.array(z.string()).min(1, '至少选择一种语言'),
    accessibility: z.boolean() }).optional(),
  staffSettings: z.object({
  requireStaffAssignment: z.boolean(),
    allowStaffOverride: z.boolean() }).optional() });

export const TicketTemplateSchema = z.object({
  id: z.string(),
  name: z.string().min(1, '模板名称不能为空').max(50, '模板名称不能超过50个字符'),
  template: z.string().min(1, '模板内容不能为空').max(1000, '模板内容不能超过1000个字符'),
  variables: z.array(z.string()),
  isDefault: z.boolean() });

// 复合验证模式
export const OnsiteBookingSettingsSchema = z.object({
  general: OnsiteGeneralSettingsSchema,
  workingHours: WorkingHoursSchema,
  mapSettings: MapSettingsSchema,
  notifications: NotificationSettingsSchema,
  aiScheduling: AISchedulingSettingsSchema,
  serviceTypes: z.array(ServiceTypeSchema) });

export const WalkinBookingSettingsSchema = z.object({
  general: WalkinGeneralSettingsSchema,
  workingHours: WorkingHoursSchema,
  queueSettings: QueueSettingsSchema,
  qrCodeSettings: QRCodeSettingsSchema,
  checkInSettings: CheckInSettingsSchema,
  notifications: NotificationSettingsSchema,
  ticketTemplates: z.array(TicketTemplateSchema),
  serviceFlow: z.object({
  serviceTypes: z.array(ServiceTypeSchema) }) });

// 导出类型推断
export type OnsiteBookingSettingsInput = z.infer<typeof OnsiteBookingSettingsSchema>;
export type WalkinBookingSettingsInput = z.infer<typeof WalkinBookingSettingsSchema>;
export type ServiceTypeInput = z.infer<typeof ServiceTypeSchema>;
export type NotificationSettingsInput = z.infer<typeof NotificationSettingsSchema>;