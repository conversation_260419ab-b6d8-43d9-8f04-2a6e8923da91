# iBuddy2 API文档

这个目录包含了iBuddy2系统的API接口文档，包括各服务的接口定义、参数说明和使用示例。

## 目录

1. [认证API](./auth-api.md) - 用户认证和授权接口
2. [客户API](./client-api.md) - 客户管理相关接口
3. [AI服务API](./ai-service-api.md) - AI和自动回复相关接口
4. [数据分析API](./analytics-api.md) - 数据分析和报表接口
5. [平台集成API](./platform-api.md) - 第三方平台集成接口
6. [计费API](./billing-api.md) - 订阅和计费相关接口

## API使用指南

- 所有API请求需要包含适当的认证头（Auth-API除外）
- API响应格式统一为JSON
- 错误处理遵循RESTful标准错误码
- 详细的接口参数和返回值见各接口文档

## 原始文档索引

以下是原始API文档的映射关系：

| 新文档 | 原始文档 |
|-------|---------|
| [认证API](./auth-api.md) | /AUTH_OPTIMIZATION_DEVELOPMENT_SUMMARY.md |
| [AI服务API](./ai-service-api.md) | /ai-service/README.md |
| [平台集成API](./platform-api.md) | /PLATFORM_API_FIX_REPORT.md | 