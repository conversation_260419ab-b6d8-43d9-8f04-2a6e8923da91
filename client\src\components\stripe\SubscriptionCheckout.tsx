import { useState, useEffect, useCallback } from 'react';
import { StripeProvider } from './StripeProvider';
import { PaymentForm } from './PaymentForm';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Check, Star } from 'lucide-react';
import { stripeApi, SubscriptionPlan, formatAmount } from '@/lib/stripe';

interface SubscriptionCheckoutProps {
  plan: SubscriptionPlan;
  customerEmail: string;
  customerName?: string;
  onSuccess?: (subscription: any) => void;
  onCancel?: () => void;
  className?: string;
}

export function SubscriptionCheckout({
  plan,
  customerEmail,
  customerName,
  onSuccess,
  onCancel,
  className
}: SubscriptionCheckoutProps) {
  const [clientSecret, setClientSecret] = useState<string | null>(null);
  const [customerId, setCustomerId] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const initializeCheckout = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // 1. 创建或获取客户
      const customer = await stripeApi.createCustomer({
        email: customerEmail,
        name: customerName,
        metadata: {
          planId: plan.id,
          source: 'subscription-checkout'
        }
      });

      setCustomerId(customer.customerId);

      // 2. 创建订阅
      const subscription = await stripeApi.createSubscription({
        customerId: customer.customerId,
        priceId: plan.id,
        trialDays: plan.id === 'basic' ? 7 : undefined, // 基础版提供7天试用
      });

      if (subscription.clientSecret) {
        setClientSecret(subscription.clientSecret);
      } else {
        // 如果没有 clientSecret，说明订阅已经激活（比如免费计划）
        onSuccess?.(subscription);
      }
    } catch (err: any) {
      console.error('初始化结账失败:', err);
      setError(err.message || '初始化支付失败，请重试');
    } finally {
      setLoading(false);
    }
  }, [plan.id, customerEmail, customerName, onSuccess]);

  useEffect(() => {
    initializeCheckout();
  }, [initializeCheckout]);

  const handlePaymentSuccess = (paymentIntent: any) => {
    console.log('支付成功:', paymentIntent);
    onSuccess?.({
      paymentIntentId: paymentIntent.id,
      customerId,
      planId: plan.id
    });
  };

  const handlePaymentError = (error: string) => {
    console.error('支付失败:', error);
    setError(error);
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="pt-6">
          <div className="flex items-center justify-center space-x-2">
            <div className="h-4 w-4 animate-spin" />
            <span>正在初始化支付...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="pt-6">
          <Alert variant="destructive">
            <div className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
          <div className="mt-4 flex gap-2">
            <Button onClick={initializeCheckout} variant="outline">
              重试
            </Button>
            {onCancel && (
              <Button onClick={onCancel} variant="ghost">
                取消
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* 订阅计划摘要 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>订阅摘要</span>
            {plan.id === 'professional' && (
              <Badge variant="secondary" className="bg-purple-100 text-purple-800">
                <Star className="w-3 h-3 mr-1" />
                推荐
              </Badge>
            )}
          </CardTitle>
          <CardDescription>
            确认您的订阅详情
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex justify-between items-center">
            <span className="font-medium">{plan.name}</span>
            <span className="text-lg font-bold">
              {formatAmount(plan.price, plan.currency)}/{plan.interval === 'month' ? '月' : '年'}
            </span>
          </div>
          {plan.id === 'basic' && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-3">
              <p className="text-sm text-green-800 font-medium">
                🎉 包含 7 天免费试用
              </p>
              <p className="text-xs text-green-600 mt-1">
                试用期结束后将自动开始计费
              </p>
            </div>
          )}

          <div className="space-y-2">
            <h4 className="font-medium text-sm">包含功能:</h4>
            <ul className="space-y-1">
              {plan.features.map((feature, index) => (
                <li key={index} className="flex items-center text-sm text-muted-foreground">
                  <Check className="w-3 h-3 mr-2 text-green-500" />
                  {feature}
                </li>
              ))}
            </ul>
          </div>
          <div className="border-t pt-4">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-muted-foreground">API 调用:</span>
                <span className="ml-2 font-medium">
                  {plan.limits.apiCalls === 'unlimited' ? '无限制' : plan.limits.apiCalls.toLocaleString()}
                </span>
              </div>
              <div>
                <span className="text-muted-foreground">存储空间:</span>
                <span className="ml-2 font-medium">{plan.limits.storage} GB</span>
              </div>
              <div>
                <span className="text-muted-foreground">AI 处理:</span>
                <span className="ml-2 font-medium">
                  {plan.limits.aiProcessing === 'unlimited' ? '无限制' : `${plan.limits.aiProcessing} 小时`}
                </span>
              </div>
              <div>
                <span className="text-muted-foreground">团队成员:</span>
                <span className="ml-2 font-medium">
                  {plan.limits.teamMembers === 'unlimited' ? '无限制' : plan.limits.teamMembers}
                </span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
      {/* 支付表单 */}
      {clientSecret && (
        <StripeProvider>
          <PaymentForm
            clientSecret={clientSecret}
            amount={plan.price}
            currency={plan.currency}
            onSuccess={handlePaymentSuccess}
            onError={handlePaymentError}
          />
        </StripeProvider>
      )}
    </div>
  );
}