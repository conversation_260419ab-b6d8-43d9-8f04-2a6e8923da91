# iBuddy2 架构文档

这个目录包含了iBuddy2系统的架构设计文档，帮助开发者理解系统的整体结构和设计决策。

## 目录

1. [系统概览](./system-overview.md) - 系统整体架构和服务拓扑
2. [微服务架构](./microservices.md) - 微服务设计原则和服务划分
3. [技术栈](./tech-stack.md) - 项目使用的技术栈和选型理由
4. [数据流](./data-flow.md) - 系统主要数据流向和处理流程
5. [安全架构](./security.md) - 系统安全设计和认证授权流程

## 原始文档索引

以下是原始架构相关文档的映射关系：

| 新文档 | 原始文档 |
|-------|---------|
| [系统概览](./system-overview.md) | /README.md (部分内容) |
| [微服务架构](./microservices.md) | /DEVELOPMENT_PLAN.md (架构部分) |
| [技术栈](./tech-stack.md) | /README.md (技术栈部分) | 