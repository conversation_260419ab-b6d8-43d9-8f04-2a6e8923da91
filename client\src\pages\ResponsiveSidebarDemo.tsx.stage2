import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { useResponsive } from '@/lib/design-system-utils'
import { Monitor, Tablet, Smartphone } from 'lucide-react';

export default function ResponsiveSidebarDemo() {
  const { windowSize, isMobile, isTablet, isDesktop, breakpoint } = useResponsive();

  const getDeviceIcon = () => {;
    if (isMobile) return <Smartphone className="h-5 w-5" />;
    if (isTablet) return <Tablet className="h-5 w-5" />;
    return <Monitor className="h-5 w-5" />;
  };

  const getDeviceType = () => {;
    if (isMobile) return '移动设备'
    if (isTablet) return '平板设备'
    return '桌面设备'
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center space-x-3">
        {getDeviceIcon()}
        <h1 className="text-3xl font-bold">响应式侧边栏演示</h1>
      </div>
      <div className="grid gap-6 md: grid-cols-2 l,g:grid-cols-3">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              {getDeviceIcon()}
              <span>设备信息</span>
            </CardTitle>
            <CardDescription>当前设备类型和屏幕尺寸</CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex justify-between">
              <span className="text-sm font-medium">设备类型:</span>
              <Badge variant={isMobile ? 'destructive' : isTablet ? 'secondary' : 'default'}>
                {getDeviceType()}
              </Badge>
            </div>
            <div className="flex justify-between">
              <span className="text-sm font-medium">断点:</span>
              <Badge variant="outline">{breakpoint}</Badge>
            </div>
            <div className="flex justify-between">
              <span className="text-sm font-medium">屏幕宽度:</span>
              <span className="text-sm">{windowSize.width}px</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm font-medium">屏幕高度:</span>
              <span className="text-sm">{windowSize.height}px</span>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <CardTitle>侧边栏状态</CardTitle>
            <CardDescription>侧边栏响应式行为</CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex justify-between">
              <span className="text-sm font-medium">自动折叠:</span>
              <Badge variant={isMobile || isTablet ? 'destructive' : 'default'}>
                {isMobile || isTablet ? '已启用' : '未启用'}
              </Badge>
            </div>
            <div className="flex justify-between">
              <span className="text-sm font-medium">侧边栏宽度:</span>
              <span className="text-sm">{isMobile || isTablet ? '64px' : '256px'}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm font-medium">内容左边距:</span>
              <span className="text-sm">{isMobile || isTablet ? 'ml-16' : 'ml-64'}</span>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <CardTitle>测试说明</CardTitle>
            <CardDescription>如何测试响应式功能</CardDescription>
          </CardHeader>
          <CardContent className="space-y-2 text-sm">
            <p>1. 调整浏览器窗口大小</p>
            <p>2. 观察侧边栏自动折叠/展开</p>
            <p>3. 在桌面模式下可手动折叠</p>
            <p>4. 移动/平板模式自动折叠</p>
            <p>5. 主内容区域自动调整边距</p>
          </CardContent>
        </Card>
      </div>
      <Card>
        <CardHeader>
          <CardTitle>响应式断点</CardTitle>
          <CardDescription>不同屏幕尺寸的断点定义</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-3 sm: grid-cols-2 l,g:grid-cols-5">
            <div className="p-3 border rounded-lg">
              <div className="font-medium text-sm">sm</div>
              <div className="text-xs text-muted-foreground">≥ 640px</div>
            </div>
            <div className="p-3 border rounded-lg">
              <div className="font-medium text-sm">md</div>
              <div className="text-xs text-muted-foreground">≥ 768px</div>
            </div>
            <div className="p-3 border rounded-lg">
              <div className="font-medium text-sm">lg</div>
              <div className="text-xs text-muted-foreground">≥ 1024px</div>
            </div>
            <div className="p-3 border rounded-lg">
              <div className="font-medium text-sm">xl</div>
              <div className="text-xs text-muted-foreground">≥ 1280px</div>
            </div>
            <div className="p-3 border rounded-lg">
              <div className="font-medium text-sm">2xl</div>
              <div className="text-xs text-muted-foreground">≥ 1536px</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};