"use strict";(self.webpackChunkclient=self.webpackChunkclient||[]).push([[983],{"./src/components/ui/select.stories.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{Default:()=>Default,__namedExportsOrder:()=>__namedExportsOrder,default:()=>select_stories});var react=__webpack_require__("./node_modules/react/index.js"),dist=__webpack_require__("./node_modules/@radix-ui/react-select/dist/index.mjs"),chevron_down=__webpack_require__("./node_modules/lucide-react/dist/esm/icons/chevron-down.js"),chevron_up=__webpack_require__("./node_modules/lucide-react/dist/esm/icons/chevron-up.js"),check=__webpack_require__("./node_modules/lucide-react/dist/esm/icons/check.js"),utils=__webpack_require__("./src/lib/utils.js"),visually_hidden=__webpack_require__("./src/components/ui/visually-hidden.tsx"),jsx_runtime=__webpack_require__("./node_modules/react/jsx-runtime.js");const Select=dist.bL,SelectValue=(dist.YJ,dist.WT),SelectTrigger=react.forwardRef(((_ref,ref)=>{let{className,children,error,accessibilityDescription,...props}=_ref;return(0,jsx_runtime.jsxs)(dist.l9,{ref,className:(0,utils.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",error&&"border-red-500 focus:ring-red-500",className),"aria-invalid":!!error||void 0,"aria-describedby":accessibilityDescription?"select-description":void 0,...props,children:[children,accessibilityDescription&&(0,jsx_runtime.jsx)(visually_hidden.s,{id:"select-description",children:accessibilityDescription}),(0,jsx_runtime.jsx)(dist.In,{asChild:!0,children:(0,jsx_runtime.jsx)(chevron_down.A,{className:"h-4 w-4 opacity-50","aria-hidden":"true"})})]})}));SelectTrigger.displayName=dist.l9.displayName;const SelectScrollUpButton=react.forwardRef(((_ref2,ref)=>{let{className,...props}=_ref2;return(0,jsx_runtime.jsx)(dist.PP,{ref,className:(0,utils.cn)("flex cursor-default items-center justify-center py-1",className),...props,children:(0,jsx_runtime.jsx)(chevron_up.A,{className:"h-4 w-4","aria-hidden":"true"})})}));SelectScrollUpButton.displayName=dist.PP.displayName;const SelectScrollDownButton=react.forwardRef(((_ref3,ref)=>{let{className,...props}=_ref3;return(0,jsx_runtime.jsx)(dist.wn,{ref,className:(0,utils.cn)("flex cursor-default items-center justify-center py-1",className),...props,children:(0,jsx_runtime.jsx)(chevron_down.A,{className:"h-4 w-4","aria-hidden":"true"})})}));SelectScrollDownButton.displayName=dist.wn.displayName;const SelectContent=react.forwardRef(((_ref4,ref)=>{let{className,children,position="popper",...props}=_ref4;return(0,jsx_runtime.jsx)(dist.ZL,{children:(0,jsx_runtime.jsxs)(dist.UC,{ref,className:(0,utils.cn)("relative z-50 max-h-[--radix-select-content-available-height] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-select-content-transform-origin]","popper"===position&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",className),position,"aria-label":"选择一个选项",...props,children:[(0,jsx_runtime.jsx)(SelectScrollUpButton,{}),(0,jsx_runtime.jsx)(dist.LM,{className:(0,utils.cn)("p-1","popper"===position&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children}),(0,jsx_runtime.jsx)(SelectScrollDownButton,{})]})})}));SelectContent.displayName=dist.UC.displayName;const SelectLabel=react.forwardRef(((_ref5,ref)=>{let{className,...props}=_ref5;return(0,jsx_runtime.jsx)(dist.JU,{ref,className:(0,utils.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",className),...props})}));SelectLabel.displayName=dist.JU.displayName;const SelectItem=react.forwardRef(((_ref6,ref)=>{let{className,children,iconLabel,...props}=_ref6;return(0,jsx_runtime.jsxs)(dist.q7,{ref,className:(0,utils.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",className),...props,children:[(0,jsx_runtime.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,jsx_runtime.jsxs)(dist.VF,{children:[(0,jsx_runtime.jsx)(check.A,{className:"h-4 w-4","aria-hidden":"true"}),iconLabel&&(0,jsx_runtime.jsx)(visually_hidden.s,{children:iconLabel})]})}),(0,jsx_runtime.jsx)(dist.p4,{children})]})}));SelectItem.displayName=dist.q7.displayName;const SelectSeparator=react.forwardRef(((_ref7,ref)=>{let{className,...props}=_ref7;return(0,jsx_runtime.jsx)(dist.wv,{ref,className:(0,utils.cn)("-mx-1 my-1 h-px bg-muted",className),"aria-hidden":"true",...props})}));SelectSeparator.displayName=dist.wv.displayName,SelectTrigger.__docgenInfo={description:"Select触发器 - 用于打开下拉菜单的按钮\n\n增强键盘可访问性和标签支持",methods:[],props:{error:{required:!1,tsType:{name:"boolean"},description:"错误状态，用于指示表单验证错误"},accessibilityDescription:{required:!1,tsType:{name:"string"},description:"额外的可访问性描述"}}},SelectContent.__docgenInfo={description:"Select内容 - 下拉菜单的容器\n\n支持键盘导航和辅助技术",methods:[],props:{position:{defaultValue:{value:'"popper"',computed:!1},required:!1}}},SelectLabel.__docgenInfo={description:"Select标签 - 用于分组选项的标签",methods:[]},SelectItem.__docgenInfo={description:"Select选项 - 单个选择项\n\n增强键盘可访问性和状态反馈",methods:[],props:{iconLabel:{required:!1,tsType:{name:"string"},description:"为图标部分添加的可访问性标签"}}},SelectSeparator.__docgenInfo={description:"Select分隔符 - 用于分隔选项组",methods:[]},SelectScrollUpButton.__docgenInfo={description:"Select上滚按钮",methods:[]},SelectScrollDownButton.__docgenInfo={description:"Select下滚按钮",methods:[]};const select_stories={title:"UI/Select",component:Select,tags:["autodocs"]},Default={render:()=>(0,jsx_runtime.jsx)("div",{style:{width:200},children:(0,jsx_runtime.jsxs)(Select,{children:[(0,jsx_runtime.jsx)(SelectTrigger,{children:(0,jsx_runtime.jsx)(SelectValue,{placeholder:"Select an option"})}),(0,jsx_runtime.jsxs)(SelectContent,{children:[(0,jsx_runtime.jsx)(SelectLabel,{children:"Fruits"}),(0,jsx_runtime.jsx)(SelectItem,{value:"apple",children:"Apple"}),(0,jsx_runtime.jsx)(SelectItem,{value:"banana",children:"Banana"}),(0,jsx_runtime.jsx)(SelectSeparator,{}),(0,jsx_runtime.jsx)(SelectLabel,{children:"Vegetables"}),(0,jsx_runtime.jsx)(SelectItem,{value:"carrot",children:"Carrot"}),(0,jsx_runtime.jsx)(SelectItem,{value:"lettuce",children:"Lettuce"})]})]})})},__namedExportsOrder=["Default"];Default.parameters={...Default.parameters,docs:{...Default.parameters?.docs,source:{originalSource:'{\n  render: () => <div style={{\n    width: 200\n  }}>\r\n      <Select>\r\n        <SelectTrigger>\r\n          <SelectValue placeholder="Select an option" />\r\n        </SelectTrigger>\r\n        <SelectContent>\r\n          <SelectLabel>Fruits</SelectLabel>\r\n          <SelectItem value="apple">Apple</SelectItem>\r\n          <SelectItem value="banana">Banana</SelectItem>\r\n          <SelectSeparator />\r\n          <SelectLabel>Vegetables</SelectLabel>\r\n          <SelectItem value="carrot">Carrot</SelectItem>\r\n          <SelectItem value="lettuce">Lettuce</SelectItem>\r\n        </SelectContent>\r\n      </Select>\r\n    </div>\n}',...Default.parameters?.docs?.source}}}},"./src/components/ui/visually-hidden.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.d(__webpack_exports__,{s:()=>VisuallyHidden});var react__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("./node_modules/react/index.js"),_lib_utils__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__("./src/lib/utils.js"),react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__=__webpack_require__("./node_modules/react/jsx-runtime.js");const VisuallyHidden=react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(((_ref,ref)=>{let{className,...props}=_ref;return(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)("span",{ref,className:(0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)("sr-only",className),...props})}));VisuallyHidden.displayName="VisuallyHidden",VisuallyHidden.__docgenInfo={description:"VisuallyHidden组件 - 在视觉上隐藏内容，但保持其对屏幕阅读器的可访问性\r\n用于提供额外的可访问性上下文，而不影响视觉设计",methods:[],displayName:"VisuallyHidden"}},"./src/lib/utils.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{absoluteUrl:()=>absoluteUrl,cn:()=>cn,formatPrice:()=>formatPrice});var clsx__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__("./node_modules/clsx/dist/clsx.mjs"),tailwind_merge__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("./node_modules/tailwind-merge/dist/bundle-mjs.mjs"),process=__webpack_require__("./node_modules/process/browser.js");function cn(){for(var _len=arguments.length,inputs=new Array(_len),_key=0;_key<_len;_key++)inputs[_key]=arguments[_key];return(0,tailwind_merge__WEBPACK_IMPORTED_MODULE_0__.QP)((0,clsx__WEBPACK_IMPORTED_MODULE_1__.$)(inputs))}function formatPrice(price){return new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(price)}function absoluteUrl(path){return`${process.env.NEXT_PUBLIC_APP_URL||""}${path}`}}}]);