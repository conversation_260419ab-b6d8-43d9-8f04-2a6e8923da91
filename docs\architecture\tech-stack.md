# iBuddy2 技术栈详解

本文档详细介绍了iBuddy2系统使用的技术栈，包括各个服务的主要依赖、技术选型理由以及版本要求。

## 前端技术栈 (client)

### 核心框架
- **React 18.3.x**: 用于构建用户界面的JavaScript库
- **TypeScript 4.9.x**: 类型安全的JavaScript超集

### UI/组件
- **Tailwind CSS 3.3.x**: 实用优先的CSS框架
- **shadcn/ui**: 基于Radix UI的可定制组件系统
- **Radix UI**: 无样式、可访问的UI组件原语
- **NextUI**: 现代化React UI库
- **Framer Motion**: 动画库

### 状态管理
- **React Query 5.x**: 数据获取和缓存库
- **React Context**: 轻量级状态管理
- **React Hook Form 7.x**: 表单处理库

### 路由
- **React Router 6.x**: 客户端路由库

### 工具库
- **Axios**: HTTP客户端
- **date-fns**: 日期操作库
- **zod**: TypeScript-first模式验证
- **class-variance-authority**: 组件变体管理

### 图表与可视化
- **Recharts**: React图表库
- **Chart.js/react-chartjs-2**: 灵活的图表库
- **Leaflet/react-leaflet**: 交互式地图库

### 开发工具
- **Jest**: 测试框架
- **Playwright**: 端到端测试
- **Storybook 8.x**: 组件开发环境
- **ESLint**: 代码质量检查
- **Craco**: Create React App配置扩展

## API网关技术栈 (api-gateway)

### 核心框架
- **Express.js**: Web应用框架
- **Node.js >=18.0.0**: JavaScript运行时

### 认证与安全
- **Passport.js**: 认证中间件
- **JWT**: JSON Web Token认证
- **cors**: 跨域资源共享中间件
- **helmet**: HTTP头部安全中间件

### 网关功能
- **http-proxy-middleware**: 反向代理中间件
- **express-rate-limit**: 请求限流
- **express-winston**: 日志记录

## 核心服务技术栈 (core-service)

### 核心框架
- **Express.js**: Web应用框架
- **Node.js >=18.0.0**: JavaScript运行时

### 数据存储
- **Supabase**: PostgreSQL云数据库
- **pg**: PostgreSQL客户端
- **knex**: SQL查询构建器

### 身份验证
- **Supabase Auth**: 身份验证服务
- **bcrypt**: 密码哈希

### 业务逻辑
- **moment**: 日期处理
- **lodash**: 实用工具库
- **uuid**: UUID生成

## AI服务技术栈 (ai-service)

### 核心框架
- **Express.js**: Web应用框架
- **Node.js >=18.0.0**: JavaScript运行时

### AI集成
- **Google Gemini API**: Google AI模型API
- **OpenAI API**: GPT模型API
- **OpenRouter API**: AI模型路由服务

### 上下文管理
- **Redis**: 内存数据结构存储
- **MongoDB**: 文档数据库，用于存储对话历史

### 消息队列
- **RabbitMQ**: 消息代理
- **amqplib**: AMQP客户端

### 文件处理
- **multer**: 文件上传中间件
- **pdf-parse**: PDF文本提取
- **mammoth**: Word文档转换

## 共享组件 (shared)

### 通用工具
- **dotenv**: 环境变量管理
- **winston**: 日志库
- **joi**: 验证库
- **axios**: HTTP客户端

### 监控与指标
- **prom-client**: Prometheus客户端
- **express-prometheus-middleware**: Prometheus监控中间件

## 开发和部署工具

### 开发工具
- **nodemon**: 开发时自动重启服务
- **concurrently**: 并行运行多个命令
- **cross-env**: 跨平台环境变量设置

### 部署工具
- **Docker**: 容器化
- **docker-compose**: 容器编排
- **nginx**: Web服务器和反向代理

## 版本控制和依赖管理

- **Git**: 版本控制系统
- **npm >=8.0.0**: Node.js包管理器
- **workspaces**: npm工作区，用于管理多包仓库

## 系统要求

- **Node.js**: 18.0.0或更高版本
- **npm**: 8.0.0或更高版本
- **Redis**: 6.0或更高版本
- **PostgreSQL**: 14.0或更高版本
- **RabbitMQ**: 3.9或更高版本
- **Docker**: 20.10或更高版本(用于生产部署) 