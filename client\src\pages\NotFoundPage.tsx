import React from 'react';
import { useNavigate } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Home, ArrowLeft, Search, Sparkles, Compass } from 'lucide-react';
import { Helmet } from 'react-helmet-async';

const NotFoundPage: React.FC = () => {
  const navigate = useNavigate();

  const handleGoHome = () => {
    navigate('/home');
  };

  const handleGoBack = () => {
    if (window.history.length > 1) {
      navigate(-1);
    } else {
      navigate('/home');
    }
  };

  const quickLinks = [
    { label: 'Home', path: '/', icon: <Home className="w-4 h-4" /> },
    { label: 'Login', path: '/login', icon: <Sparkles className="w-4 h-4" /> },
    { label: 'Features', path: '/#features', icon: <Compass className="w-4 h-4" /> },
  ];

  return (
    <div className="min-h-screen bg-gradient-hero flex items-center justify-center p-4">
      <Helmet>
        <title>Page Not Found - iTeraBiz</title>
        <meta name="description" content="The page you're looking for doesn't exist. Return to iTeraBiz homepage or explore our features." />
      </Helmet>
      <div className="text-center space-y-8 max-w-2xl mx-auto">
        {/* 404 Animation Effect */}
        <div className="space-y-6">
          <div className="relative">
            <h1 className="text-8xl md:text-9xl font-black text-transparent bg-gradient-primary bg-clip-text animate-pulse">
              404
            </h1>
            <div className="absolute inset-0 text-8xl md:text-9xl font-black text-primary-200 -z-10 transform translate-x-2 translate-y-2">
              404
            </div>
          </div>

          <div className="space-y-4">
            <h2 className="text-2xl md:text-3xl font-bold text-text-primary">
              Oops! Page Not Found
            </h2>
            <p className="text-text-secondary text-lg max-w-md mx-auto">
              The page you're looking for might have been moved, deleted, or doesn't exist.
            </p>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button
            onClick={handleGoHome}
            className="bg-gradient-primary hover:opacity-90 text-white px-6 py-3"
          >
            <Home className="w-5 h-5 mr-2" />
            Go Home
          </Button>
          <Button
            onClick={handleGoBack}
            variant="outline"
            className="border-primary-600 text-primary-600 hover:bg-primary-50 px-6 py-3"
          >
            <ArrowLeft className="w-5 h-5 mr-2" />
            Go Back
          </Button>
        </div>

        {/* Quick Links */}
        <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-xl">
          <CardContent className="pt-6">
            <div className="space-y-4">
              <div className="flex items-center justify-center text-text-secondary">
                <Search className="w-5 h-5 mr-2" />
                <span className="font-medium">Try these helpful links</span>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
                {quickLinks.map((link, index) => (
                  <Button
                    key={index}
                    variant="ghost"
                    onClick={() => navigate(link.path)}
                    className="flex items-center justify-center p-4 h-auto hover:bg-primary-50 hover:text-primary-600 transition-colors"
                  >
                    {link.icon}
                    <span className="ml-2">{link.label}</span>
                  </Button>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Help Information */}
        <div className="text-sm text-text-muted space-y-2">
          <p>If you believe this is an error, please contact our support team</p>
          <div className="flex items-center justify-center space-x-4">
            <span>Error Code: 404</span>
            <span>•</span>
            <span>Time: {new Date().toLocaleString()}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotFoundPage;