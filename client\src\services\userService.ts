/**
 * 用户服务
 * 使用缓存API客户端处理用户相关请求
 */
import { cachedRequest, ApiClientType, cacheManager } from '../utils/api/cachedApiClient';

// 用户接口定义
export interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  avatar?: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * 获取用户列表
 * 使用默认缓存策略(15分钟)
 */
export const getUsers = () => {
  return cachedRequest<User[]>({
    url: '/users',
    method: 'get'
  });
};

/**
 * 获取用户详情
 * 使用长期缓存策略(1天)，适用于不常变动的用户信息
 */
export const getUserById = (userId: string) => {
  return cachedRequest<User>({
    url: `/users/${userId}`,
    method: 'get'
  },
    ApiClientType.LONG_TERM
  );
};

/**
 * 获取当前登录用户信息
 * 使用默认缓存策略(15分钟)
 */
export const getCurrentUser = () => {
  return cachedRequest<User>({
    url: '/users/me',
    method: 'get'
  });
};

/**
 * 创建用户
 * 不使用缓存，并在操作成功后清除用户列表缓存
 */
export const createUser = async (userData: Omit<User, 'id' | 'createdAt' | 'updatedAt'>) => {
  const result = await cachedRequest<User>({
    url: '/users',
    method: 'post',
    data: userData
  },
    ApiClientType.NO_CACHE
  );
  
  // 清除用户列表缓存，确保下次获取时能看到新用户
  cacheManager.clearCache('/users');
  
  return result;
};

/**
 * 更新用户
 * 不使用缓存，并在操作成功后清除相关缓存
 */
export const updateUser = async (userId: string, userData: Partial<User>) => {
  const result = await cachedRequest<User>({
    url: `/users/${userId}`,
    method: 'put',
    data: userData
  },
    ApiClientType.NO_CACHE
  );
  
  // 清除相关缓存
  cacheManager.clearCache('/users');
  cacheManager.clearCache(`/users/${userId}`);
  
  // 如果更新的是当前用户，也清除当前用户缓存
  cacheManager.clearCache('/users/me');
  
  return result;
};

/**
 * 删除用户
 * 不使用缓存，并在操作成功后清除相关缓存
 */
export const deleteUser = async (userId: string) => {
  const result = await cachedRequest<{ success: boolean }>({
    url: `/users/${userId}`,
    method: 'delete'
  },
    ApiClientType.NO_CACHE
  );
  
  // 清除相关缓存
  cacheManager.clearCache('/users');
  cacheManager.clearCache(`/users/${userId}`);
  
  return result;
}; 