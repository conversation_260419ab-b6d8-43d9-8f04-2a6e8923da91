"use strict";(self.webpackChunkclient=self.webpackChunkclient||[]).push([[467],{"./src/components/ui/visually-hidden.stories.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{Default:()=>Default,__namedExportsOrder:()=>__namedExportsOrder,default:()=>__WEBPACK_DEFAULT_EXPORT__});__webpack_require__("./node_modules/react/index.js");var _visually_hidden__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__("./src/components/ui/visually-hidden.tsx"),react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__=__webpack_require__("./node_modules/react/jsx-runtime.js");const __WEBPACK_DEFAULT_EXPORT__={title:"UI/VisuallyHidden",component:_visually_hidden__WEBPACK_IMPORTED_MODULE_1__.s,tags:["autodocs"]},Default={render:()=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)("div",{children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)("p",{children:["Visible Text",(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_visually_hidden__WEBPACK_IMPORTED_MODULE_1__.s,{children:" – hidden for visual users but read by screen readers"})]})})},__namedExportsOrder=["Default"];Default.parameters={...Default.parameters,docs:{...Default.parameters?.docs,source:{originalSource:"{\n  render: () => <div>\r\n      <p>\r\n        Visible Text\r\n        <VisuallyHidden> – hidden for visual users but read by screen readers</VisuallyHidden>\r\n      </p>\r\n    </div>\n}",...Default.parameters?.docs?.source}}}},"./src/components/ui/visually-hidden.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.d(__webpack_exports__,{s:()=>VisuallyHidden});var react__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("./node_modules/react/index.js"),_lib_utils__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__("./src/lib/utils.js"),react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__=__webpack_require__("./node_modules/react/jsx-runtime.js");const VisuallyHidden=react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(((_ref,ref)=>{let{className,...props}=_ref;return(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)("span",{ref,className:(0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)("sr-only",className),...props})}));VisuallyHidden.displayName="VisuallyHidden",VisuallyHidden.__docgenInfo={description:"VisuallyHidden组件 - 在视觉上隐藏内容，但保持其对屏幕阅读器的可访问性\r\n用于提供额外的可访问性上下文，而不影响视觉设计",methods:[],displayName:"VisuallyHidden"}},"./src/lib/utils.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{absoluteUrl:()=>absoluteUrl,cn:()=>cn,formatPrice:()=>formatPrice});var clsx__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__("./node_modules/clsx/dist/clsx.mjs"),tailwind_merge__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("./node_modules/tailwind-merge/dist/bundle-mjs.mjs"),process=__webpack_require__("./node_modules/process/browser.js");function cn(){for(var _len=arguments.length,inputs=new Array(_len),_key=0;_key<_len;_key++)inputs[_key]=arguments[_key];return(0,tailwind_merge__WEBPACK_IMPORTED_MODULE_0__.QP)((0,clsx__WEBPACK_IMPORTED_MODULE_1__.$)(inputs))}function formatPrice(price){return new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(price)}function absoluteUrl(path){return`${process.env.NEXT_PUBLIC_APP_URL||""}${path}`}}}]);