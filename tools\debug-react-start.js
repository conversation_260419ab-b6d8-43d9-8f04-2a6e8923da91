#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 启动React应用调试模式...');
console.log('═'.repeat(50));

const clientPath = path.join(__dirname, '../client');
const npmStart = spawn('npm.cmd', ['start'], {
  cwd: clientPath,
  stdio: ['pipe', 'pipe', 'pipe']
});

npmStart.stdout.on('data', (data) => {
  const output = data.toString();
  console.log('[STDOUT]', output);
  
  // 检查关键信息
  if (output.includes('Starting the development server')) {
    console.log('✅ 开发服务器启动中...');
  }
  
  if (output.includes('Compiled successfully')) {
    console.log('✅ 编译成功!');
  }
  
  if (output.includes('webpack compiled')) {
    console.log('✅ Webpack编译完成!');
  }
  
  if (output.includes('Local:') && output.includes('localhost:3000')) {
    console.log('✅ 应用可访问: http://localhost:3000');
  }
});

npmStart.stderr.on('data', (data) => {
  const error = data.toString();
  console.error('[STDERR]', error);
});

npmStart.on('close', (code) => {
  console.log(`进程退出，代码: ${code}`);
});

npmStart.on('error', (err) => {
  console.error('启动失败:', err);
});

// 30秒后如果还没成功就退出
setTimeout(() => {
  console.log('⏰ 30秒超时，终止进程');
  npmStart.kill();
}, 30000);

console.log('正在监控启动过程，请等待...'); 