# iBuddy2 部署文档

这个目录包含了iBuddy2系统的部署文档，帮助运维人员和开发者在各种环境中部署和配置系统。

## 目录

1. [环境准备](./environment-setup.md) - 服务器环境配置和前置依赖
2. [开发环境部署](./development-deployment.md) - 本地开发环境搭建
3. [测试环境部署](./testing-deployment.md) - 测试环境配置和部署
4. [生产环境部署](./production-deployment.md) - 生产环境部署和优化
5. [容器化部署](./docker-deployment.md) - Docker容器部署指南
6. [CI/CD配置](./ci-cd.md) - 持续集成和持续部署配置

## 部署脚本

- [health-check.bat](../../health-check.bat) - 系统健康检查脚本
- [start-dev.bat](../../start-dev.bat) - 开发环境启动脚本
- [start-all.bat](../../start-all.bat) - 所有服务启动脚本

## 原始文档索引

以下是原始部署文档的映射关系：

| 新文档 | 原始文档 |
|-------|---------|
| [环境准备](./environment-setup.md) | /README.md (环境部分) |
| [开发环境部署](./development-deployment.md) | /DEVELOPMENT_STATUS.md |
| [容器化部署](./docker-deployment.md) | /README.md (Docker部分) | 