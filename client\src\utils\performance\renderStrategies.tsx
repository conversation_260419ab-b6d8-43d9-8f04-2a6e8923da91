import React, { ReactNode, useMemo, useCallback } from 'react';

/**
 * 渲染条件分支的选项
 */
interface RenderBranchOptions<T> {
  value: T;
  branches: Array<{
    when: T | ((value: T) => boolean);
    render: () => ReactNode;
  }>;
  default?: () => ReactNode;
}

/**
 * 条件分支渲染函数
 * 优化条件渲染，避免不必要的渲染和计算
 */
export function renderBranch<T>({ value, branches, default: defaultRender }: RenderBranchOptions<T>): ReactNode {
  // 查找匹配的分支
  for (const branch of branches) {
    const condition = typeof branch.when === 'function'
      ? (branch.when as (value: T) => boolean)(value)
      : value === branch.when;
      
    if (condition) {
      return branch.render();
    }
  }
  
  // 如果没有匹配的分支，返回默认渲染
  return defaultRender ? defaultRender() : null;
}

/**
 * 延迟渲染组件
 * 只有当shouldRender为true时才渲染内容，避免不必要的渲染
 */
interface LazyRenderProps {
  shouldRender: boolean;
  children: ReactNode;
  fallback?: ReactNode;
}

export const LazyRender: React.FC<LazyRenderProps> = ({ 
  shouldRender, 
  children, 
  fallback = null 
}) => {
  return shouldRender ? <>{children}</> : <>{fallback}</>;
};

/**
 * 渲染状态管理器
 * 用于管理不同状态下的渲染
 */
interface RenderManagerProps<T> {
  state: T;
  renderMap: Record<string, ReactNode>;
  defaultRender?: ReactNode;
}

export function RenderManager<T extends string>({ 
  state, 
  renderMap, 
  defaultRender = null 
}: RenderManagerProps<T>): React.ReactElement {
  // 获取当前状态的渲染内容或默认渲染
  const content = renderMap[state] || defaultRender;
  return <>{content}</>;
}

/**
 * 使用记忆化的条件渲染Hook
 * 当条件或渲染函数变化时才更新渲染结果
 */
export function useConditionalRender<T>(
  condition: T | (() => T),
  renderTrue: () => ReactNode,
  renderFalse?: () => ReactNode
): ReactNode {
  // 获取条件值
  const getConditionValue = useCallback(() => {
    return typeof condition === 'function' 
      ? (condition as () => T)() 
      : condition;
  }, [condition]);
  
  // 记忆化渲染结果
  return useMemo(() => {
    const conditionValue = getConditionValue();
    return conditionValue ? renderTrue() : renderFalse ? renderFalse() : null;
  }, [getConditionValue, renderTrue, renderFalse]);
}

/**
 * 异步加载组件包装器
 * 优化异步组件的加载体验
 */
interface AsyncComponentProps {
  isLoading: boolean;
  isError: boolean;
  error?: Error | null;
  children: ReactNode;
  loadingFallback?: ReactNode;
  errorFallback?: ((error: Error | null) => ReactNode) | ReactNode;
}

export const AsyncComponent: React.FC<AsyncComponentProps> = ({
  isLoading,
  isError,
  error = null,
  children,
  loadingFallback = null, 
  errorFallback = null
}) => {
  return useMemo(() => {
    if (isLoading) {
      return <>{loadingFallback}</>;
    }
    
    if (isError) {
      if (typeof errorFallback === 'function') {
        return <>{(errorFallback as (error: Error | null) => ReactNode)(error)}</>;
      }
      return <>{errorFallback}</>;
    }
    
    return <>{children}</>;
  }, [isLoading, isError, error, children, loadingFallback, errorFallback]);
};

/**
 * 使用示例:
 * 
 * // 条件分支渲染
 * renderBranch({
 *   value: status,
 *   branches: [
 *     { when: 'loading' render: () => <LoadingSpinner /> },
 *     { when: 'error' render: () => <ErrorMessage error={error} /> },
 *     { when: (s) => s === 'empty' || s === 'noData' render: () => <EmptyState /> }
 *   ],
 *   default: () => <DataTable data={data} />
 * })
 * 
 * // 延迟渲染
 * <LazyRender shouldRender={isVisible}>
 *   <ExpensiveComponent />
 * </LazyRender>
 * 
 * // 渲染状态管理器
 * <RenderManager
 *   state={loadingState}
 *   renderMap={{
 *     'loading': <LoadingSpinner />,
 *     'error': <ErrorMessage />,
 *     'success': <DataView data={data} />
 *   }}
 * />
 * 
 * // 条件渲染Hook
 * const content = useConditionalRender(
 *   isLoggedIn,
 *   () => <UserDashboard user={user} />,
 *   () => <LoginPrompt />
 * );
 * 
 * // 异步组件包装器
 * <AsyncComponent
 *   isLoading={loading}
 *   isError={!!error}
 *   error={error}
 *   loadingFallback={<Spinner />}
 *   errorFallback={(err) => <ErrorMessage message={err?.message} />}
 * >
 *   <UserProfile user={user} />
 * </AsyncComponent>
 */ 