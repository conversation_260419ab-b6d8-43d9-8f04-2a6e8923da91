/* 设置页面样式 */
.page-title {
  font-size: 1.8rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1.5rem;
}

.settings-card {
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  border: none;
}

.settings-card .card-header {
  background-color: #fff;
  border-bottom: 1px solid #eee;
  padding: 1rem 1.5rem;
}

/* 🟣 柔和紫色主题 Tab 样式优化 */
.settings-tabs .nav-link {
  color: #555;
  font-weight: 500;
  padding: 0.75rem 1.25rem;
  border: none;
  border-radius: 6px;
  margin-right: 0.5rem;
  display: flex;
  align-items: center;
  transition: all 0.2s ease-in-out;
  position: relative;
}

.settings-tabs .nav-link.active {
  color: white;
  background: linear-gradient(135deg, hsl(260 60% 65%), hsl(260 54% 58%));
  box-shadow: 
    0 2px 8px hsla(260, 60%, 65%, 0.2),
    inset 0 1px 0 hsla(260, 60%, 75%, 0.3);
}

.settings-tabs .nav-link:hover:not(.active) {
  background-color: hsl(260 60% 96%);
  color: hsl(260 60% 65%);
}

.settings-tabs .nav-link:focus {
  outline: none;
  box-shadow: 
    0 0 0 2px hsla(260, 60%, 65%, 0.3);
}

.settings-card .card-body {
  padding: 1.5rem;
}

.notification-settings h4,
.qrcode-settings h4,
.queue-settings h4,
.map-settings h4 {
  font-size: 1.4rem;
  margin-bottom: 1rem;
  color: #333;
}

.notification-settings p,
.qrcode-settings p,
.queue-settings p,
.map-settings p {
  color: #666;
  margin-bottom: 1.5rem;
}

.feature-list {
  list-style-type: none;
  padding-left: 0;
  margin-top: 1rem;
}

.feature-list li {
  padding: 8px 0;
  border-bottom: 1px solid #eee;
  position: relative;
  padding-left: 1.5rem;
}

.feature-list li:before {
  content: "✓";
  color: #4CAF50;
  position: absolute;
  left: 0;
  top: 8px;
}

.feature-list li:last-child {
  border-bottom: none;
}

.notification-features {
  background-color: #f9f9f9;
  padding: 1.5rem;
  border-radius: 6px;
  margin-top: 2rem;
}

.notification-features h5 {
  color: #333;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.form-group label {
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: #444;
}

.form-check {
  margin: 1rem 0;
}

.form-check label {
  font-weight: normal;
}

.form-group small {
  color: #888;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .settings-tabs .nav-link {
    padding: 0.5rem 0.75rem;
    font-size: 0.9rem;
  }
  
  .settings-card .card-body {
    padding: 1rem;
  }
} 