/**
 * u5b9au4e49APIu9519u8befu7c7bu7c7bu4f9du4e8eu5e94u7528u5f02u5e38u5904u7406
 */
class ApiError extends Error {
  constructor(statusCode, message, data = {}) {
    super(message);
    this.statusCode = statusCode;
    this.data = data;
    this.name = 'ApiError'
  }
}

/**
 * u5168u5c40u9519u8befu5904u7406u4e2du95f4u4ef6
 */
const errorHandler = (err, req, res, next) => {
  console.error('Error:', err);
  
  // u5904u7406APIu9519u8bef
  if (err instanceof ApiError) {
    return res.status(err.statusCode).json({
      error: err.message,
      ...err.data
    });
  }
  
  // u5904u7406u5176u4ed6u7c7bu578bu7684u9519u8bef
  let statusCode = 500;
  let message = 'u670du52a1u5668u5185u90e8u9519u8bef'
  
  // u5904u7406u7279u5b9au7c7bu578bu7684u9519u8bef
  if (err.name === 'ValidationError') {
    statusCode = 400;
    message = 'u6570u636eu9a8cu8bc1u5931u8d25'
  } else if (err.name === 'UnauthorizedError') {
    statusCode = 401;
    message = 'u672au7ecfu6388u6743'
  } else if (err.name === 'ForbiddenError') {
    statusCode = 403;
    message = 'u6ca1u6709u8bbfu95eeu6743u9650'
  } else if (err.name === 'NotFoundError') {
    statusCode = 404;
    message = 'u8d44u6e90u4e0du5b58u5728'
  }
  
  // u5728u5f00u53d1u73afu5883u8fd4u56deu66f4u591au4fe1u606fu4fbfu4e8eu8c03u8bd5
  const isDev = process.env.NODE_ENV === 'development'
  
  res.status(statusCode).json({
    error: message,
    ...(isDev ? { stack: err.stack, details: err.message } : {})
  });
};

module.exports = {
  ApiError,
  errorHandler
}; 