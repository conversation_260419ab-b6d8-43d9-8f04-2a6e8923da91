# iBuddy2 路径别名使用指南

本文档介绍了iBuddy2项目中路径别名的配置和使用方法，以简化代码中的导入路径。

## 配置说明

项目已配置以下路径别名，可在代码中直接使用：

| 别名 | 对应目录 | 用途 |
|------|---------|------|
| `@/*` | `src/*` | 所有源代码 |
| `@components/*` | `src/components/*` | UI组件 |
| `@utils/*` | `src/utils/*` | 工具函数 |
| `@services/*` | `src/services/*` | API服务 |
| `@hooks/*` | `src/hooks/*` | React钩子 |
| `@contexts/*` | `src/contexts/*` | 上下文提供者 |
| `@types/*` | `src/types/*` | 类型定义 |
| `@assets/*` | `src/assets/*` | 静态资源 |
| `@styles/*` | `src/styles/*` | 样式文件 |
| `@pages/*` | `src/pages/*` | 页面组件 |
| `@lib/*` | `src/lib/*` | 第三方库封装 |
| `@constants/*` | `src/constants/*` | 常量定义 |
| `@design-system/*` | `src/design-system/*` | 设计系统组件 |

## 使用示例

### 原有导入方式

```typescript
// 深层嵌套的相对路径
import Button from '../../../components/ui/Button';
import { formatDate } from '../../../utils/formatting';
import { UserService } from '../../../services/UserService';
```

### 使用别名的导入方式

```typescript
// 使用路径别名的绝对路径
import Button from '@components/ui/Button';
import { formatDate } from '@utils/formatting';
import { UserService } from '@services/UserService';
```

## 迁移指南

为了确保项目的稳定性，我们采用渐进式迁移策略：

1. **新代码**: 所有新编写的代码应使用路径别名
2. **修改代码**: 修改现有文件时，可以同时更新导入路径
3. **批量迁移**: 避免批量更改导入路径，以减少引入bug的风险

## IDE支持

项目已配置VSCode和WebStorm的路径别名支持：

- VSCode通过`tsconfig.json`支持路径别名自动补全和导航
- WebStorm通过识别webpack配置自动支持路径别名

## 最佳实践

- 优先使用最具体的别名。例如，使用`@components/Button`而不是`@/components/Button`
- 导入组件时使用别名，导入相邻文件时可以继续使用相对路径
- 使用IDE的自动导入功能，自动选择路径别名 