﻿import type { <PERSON>a, StoryObj } from '@storybook/react';
import { Pricing } from './pricing';

const meta: Meta<typeof Pricing> = {
  title: 'UI/Pricing',
  component: Pricing,
  parameters: {
    layout: 'fullscreen',
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof Pricing>;

const defaultPlans = [
  {
    name: "STARTER",
    price: "29",
    yearlyPrice: "23",
    period: "per month",
    features: [
      "10,000 AI-generated words/month",
      "5 Content templates",
      "Basic analytics",
      "Email support",
      "1 User account",
    ],
    description: "Perfect for individuals",
    buttonText: "Start Free Trial",
    href: "/register",
    isPopular: false,
  },
  {
    name: "PROFESSIONAL",
    price: "79",
    yearlyPrice: "63",
    period: "per month",
    features: [
      "50,000 AI-generated words/month",
      "25+ Content templates",
      "Advanced analytics",
      "Priority support",
      "5 User accounts",
      "API access",
    ],
    description: "Ideal for teams",
    buttonText: "Get Started",
    href: "/register",
    isPopular: true,
  },
  {
    name: "ENTERPRISE",
    price: "199",
    yearlyPrice: "159",
    period: "per month",
    features: [
      "Unlimited content",
      "Custom workflows",
      "Dedicated manager",
      "White-label solution",
      "Unlimited users",
    ],
    description: "For large organizations",
    buttonText: "Contact Sales",
    href: "/contact",
    isPopular: false,
  },
];

export const Default: Story = {
  args: {
    plans: defaultPlans,
  },
};
