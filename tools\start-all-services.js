#!/usr/bin/env node

/**
 * 启动所有 iBuddy2 服务的脚本
 * 包括 API Gateway、Core Service、AI Service 和 Client
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
require('dotenv').config({ path: path.resolve(__dirname, '../.env') });

// 服务配置
const services = [
  {
    name: 'API Gateway',
    cwd: path.join(__dirname, '..', 'api-gateway'),
    command: process.platform === 'win32' ? 'npm.cmd' : 'npm',
    args: ['run', 'dev'],
    port: 3001,
    color: '\x1b[34m', // 蓝色
    env: {
      PORT: '3001',
      NODE_ENV: 'development'
    }
  },
  {
    name: 'Core Service',
    cwd: path.join(__dirname, '..', 'core-service'),
    command: process.platform === 'win32' ? 'npm.cmd' : 'npm',
    args: ['run', 'dev'],
    port: 3002,
    color: '\x1b[32m', // 绿色
    env: {
      PORT: '3002',
      NODE_ENV: 'development'
    }
  },
  {
    name: 'AI Service',
    cwd: path.join(__dirname, '..', 'ai-service'),
    command: process.platform === 'win32' ? 'npm.cmd' : 'npm',
    args: ['run', 'dev'],
    port: 3003,
    color: '\x1b[33m', // 黄色
    env: {
      PORT: '3003',
      NODE_ENV: 'development'
    }
  },
  {
    name: 'Client',
    cwd: path.join(__dirname, '..', 'client'),
    command: process.platform === 'win32' ? 'npm.cmd' : 'npm',
    args: ['start'],
    port: 3000,
    color: '\x1b[35m', // 紫色
    env: {
      PORT: '3000',
      NODE_ENV: 'development',
      BROWSER: 'none' // 防止自动打开浏览器
    }
  },
  {
    name: 'Server',
    cwd: path.join(__dirname, '..', 'server'),
    command: process.platform === 'win32' ? 'npm.cmd' : 'npm',
    args: ['run', 'dev'],
    port: 3004,
    color: '\x1b[36m', // 青色
    env: {
      PORT: '3004',
      NODE_ENV: 'development'
    }
  }
];

// 颜色代码
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m'
};

// 日志函数
function log(service, message, isError = false) {
  const timestamp = new Date().toISOString().substr(11, 8);
  const serviceColor = service.color || colors.white;
  const messageColor = isError ? colors.red : colors.white;
  
  console.log(
    `${colors.dim}[${timestamp}]${colors.reset} ` +
    `${serviceColor}${service.name.padEnd(12)}${colors.reset} ` +
    `${messageColor}${message}${colors.reset}`
  );
}

// 检查服务目录是否存在
function checkServiceDirectories() {
  const missingDirs = [];
  
  services.forEach(service => {
    if (!fs.existsSync(service.cwd)) {
      missingDirs.push(service.name);
    }
  });
  
  if (missingDirs.length > 0) {
    console.log(`${colors.red}错误: 以下服务目录不存在:${colors.reset}`);
    missingDirs.forEach(dir => console.log(`  - ${dir}`));
    console.log(`\n请确保所有服务目录都已创建并包含必要的文件。`);
    process.exit(1);
  }
}

// 检查 package.json 文件
function checkPackageJson() {
  const missingPackages = [];
  
  services.forEach(service => {
    const packageJsonPath = path.join(service.cwd, 'package.json');
    if (!fs.existsSync(packageJsonPath)) {
      missingPackages.push(service.name);
    }
  });
  
  if (missingPackages.length > 0) {
    console.log(`${colors.yellow}警告: 以下服务缺少 package.json:${colors.reset}`);
    missingPackages.forEach(pkg => console.log(`  - ${pkg}`));
    console.log(`\n某些服务可能无法启动。`);
  }
}

// 端口检查函数
async function checkPort(port) {
  return new Promise((resolve) => {
    const net = require('net');
    const server = net.createServer();
    
    server.listen(port, () => {
      server.once('close', () => resolve(true));
      server.close();
    });
    
    server.on('error', () => resolve(false));
  });
}

// 检查端口可用性
async function checkPorts() {
  console.log(`${colors.cyan}检查端口可用性...${colors.reset}`);
  
  for (const service of services) {
    const isAvailable = await checkPort(service.port);
    if (!isAvailable) {
      console.log(`${colors.red}端口 ${service.port} (${service.name}) 已被占用${colors.reset}`);
      console.log(`请关闭占用该端口的进程或修改配置文件中的端口号。`);
      process.exit(1);
    }
  }
  
  console.log(`${colors.green}所有端口都可用${colors.reset}\n`);
}

// 启动单个服务
function startService(service) {
  return new Promise((resolve, reject) => {
    log(service, `启动中... (端口 ${service.port})`);
    
    const env = {
      ...process.env,
      ...service.env
    };
    
    // Windows 兼容性改进
    const spawnOptions = {
      cwd: service.cwd,
      env: env,
      stdio: 'pipe',
      shell: true  // 在 Windows 上使用 shell
    };
    
    const child = spawn(service.command, service.args, spawnOptions);
    
    // 处理标准输出
    child.stdout.on('data', (data) => {
      const output = data.toString().trim();
      if (output) {
        output.split('\n').forEach(line => {
          if (line.trim()) {
            log(service, line);
          }
        });
      }
    });
    
    // 处理错误输出
    child.stderr.on('data', (data) => {
      const output = data.toString().trim();
      if (output) {
        output.split('\n').forEach(line => {
          if (line.trim()) {
            log(service, line, true);
          }
        });
      }
    });
    
    // 处理进程退出
    child.on('close', (code) => {
      if (code !== 0) {
        log(service, `进程退出，代码: ${code}`, true);
      } else {
        log(service, '进程正常退出');
      }
    });
    
    // 处理错误
    child.on('error', (error) => {
      log(service, `启动失败: ${error.message}`, true);
      reject(error);
    });
    
    // 保存子进程引用
    service.process = child;
    
    // 给服务一些时间启动
    setTimeout(() => {
      if (child.killed || child.exitCode !== null) {
        reject(new Error(`${service.name} 启动失败`));
      } else {
        log(service, '启动成功');
        resolve(child);
      }
    }, 3000);
  });
}

// 清理函数
function cleanup() {
  console.log(`\n${colors.yellow}正在关闭所有服务...${colors.reset}`);
  
  services.forEach(service => {
    if (service.process && !service.process.killed) {
      log(service, '正在关闭...');
      service.process.kill('SIGTERM');
    }
  });
  
  // 给进程时间优雅关闭
  setTimeout(() => {
    services.forEach(service => {
      if (service.process && !service.process.killed) {
        log(service, '强制关闭', true);
        service.process.kill('SIGKILL');
      }
    });
    process.exit(0);
  }, 5000);
}

// 主函数
async function main() {
  console.log(`${colors.bright}${colors.cyan}`);
  console.log('╔══════════════════════════════════════════╗');
  console.log('║            iTeraBiz 开发服务启动器          ║');
  console.log('╚══════════════════════════════════════════╝');
  console.log(`${colors.reset}\n`);
  
  try {
    // 前置检查
    checkServiceDirectories();
    checkPackageJson();
    await checkPorts();
    
    // 启动服务
    console.log(`${colors.cyan}启动所有服务...${colors.reset}\n`);
    
    // 按顺序启动后端服务，然后启动前端
    const backendServices = services.filter(s => s.name !== 'Client');
    const clientService = services.find(s => s.name === 'Client');
    
    // 启动后端服务
    for (const service of backendServices) {
      try {
        await startService(service);
        // 服务间间隔
        await new Promise(resolve => setTimeout(resolve, 2000));
      } catch (error) {
        console.log(`${colors.red}${service.name} 启动失败: ${error.message}${colors.reset}`);
        // 继续启动其他服务
      }
    }
    
    // 启动客户端
    if (clientService) {
      try {
        await new Promise(resolve => setTimeout(resolve, 3000)); // 等待后端服务稳定
        await startService(clientService);
      } catch (error) {
        console.log(`${colors.red}客户端启动失败: ${error.message}${colors.reset}`);
      }
    }
    
    // 显示服务状态
    console.log(`\n${colors.green}${colors.bright}所有服务启动完成!${colors.reset}`);
    console.log(`${colors.cyan}服务地址:${colors.reset}`);
    services.forEach(service => {
      console.log(`  • ${service.name}: http://localhost:${service.port}`);
    });
    
    console.log(`\n${colors.yellow}按 Ctrl+C 停止所有服务${colors.reset}\n`);
    
    // 保持进程运行
    process.stdin.resume();
    
  } catch (error) {
    console.log(`${colors.red}启动失败: ${error.message}${colors.reset}`);
    cleanup();
  }
}

// 处理进程信号
process.on('SIGINT', cleanup);
process.on('SIGTERM', cleanup);
process.on('uncaughtException', (error) => {
  console.log(`${colors.red}未捕获的异常: ${error.message}${colors.reset}`);
  cleanup();
});

// 运行主函数
if (require.main === module) {
  main().catch(error => {
    console.error(`${colors.red}启动脚本错误: ${error.message}${colors.reset}`);
    process.exit(1);
  });
}

module.exports = { startService, checkPort, services }; 