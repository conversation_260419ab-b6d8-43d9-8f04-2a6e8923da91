import { apiClient } from '../apiClient';

export interface RealTimeUsageData {
  timestamp: number;
  apiCalls: {
    current: number;
    limit: number;
    percentage: number;
  };
  storage: {
    current: number;
    limit: number;
    percentage: number;
    breakdown: {
      documents: number;
      images: number;
      videos: number;
      other: number;
    };
  };
  aiProcessing: {
    current: number;
    limit: number;
    percentage: number;
    activeJobs: number;
    queuedJobs: number;
  };
  bandwidth: {
    current: number;
    limit: number;
    percentage: number;
    inbound: number;
    outbound: number;
  };
  teamMembers: {
    current: number;
    limit: number;
    percentage: number;
  };
}

export interface UsageAlert {
  id: string;
  type: 'critical' | 'warning' | 'info';
  category: 'apiCalls' | 'storage' | 'aiProcessing' | 'bandwidth' | 'teamMembers';
  message: string;
  timestamp: number;
  threshold: number;
  currentValue: number;
  dismissed?: boolean;
}

export interface SystemHealth {
  status: 'healthy' | 'degraded' | 'down';
  services: {
    apiGateway: 'online' | 'offline' | 'degraded';
    coreService: 'online' | 'offline' | 'degraded';
    aiService: 'online' | 'offline' | 'degraded';
    database: 'online' | 'offline' | 'degraded';
  };
  lastUpdated: number;
}

export interface ConnectionStatus {
  isConnected: boolean;
  lastHeartbeat: number;
  reconnectAttempts: number;
}

class RealTimeApiService {
  private ws: WebSocket | null = null;
  private reconnectTimer: NodeJS.Timeout | null = null;
  private heartbeatTimer: NodeJS.Timeout | null = null;
  private subscribers: Map<string, Set<(data: any) => void>> = new Map();
  private connectionListeners: Set<(status: ConnectionStatus) => void> = new Set();
  private connectionStatus: ConnectionStatus = {
    isConnected: false,
    lastHeartbeat: 0,
    reconnectAttempts: 0
  };

  // WebSocket连接管理
  connect() {
    if (this.ws?.readyState === WebSocket.OPEN) {
      return
    }

    try {
      // Derive WebSocket URL from API URL to avoid hardcoded port/path mismatches
      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:3001'
      // Replace http/https with ws/wss for WebSocket protocol
      const wsHost = apiUrl.replace(/^http/, 'ws');
      const wsUrl = `${wsHost}/ws/realtime`;
      this.ws = new WebSocket(wsUrl);

      this.ws.onopen = () => {
        console.log('实时数据连接已建立');
        this.connectionStatus = {
          isConnected: true,
          lastHeartbeat: Date.now(),
          reconnectAttempts: 0
        };
        this.notifyConnectionListeners();
        this.startHeartbeat()
      };

      this.ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          this.handleMessage(data)
        } catch (error) {
          console.error('解析WebSocket消息失败:' + error)
        }
      };

      this.ws.onclose = () => {
        console.log('实时数据连接已关闭');
        this.connectionStatus.isConnected = false;
        this.notifyConnectionListeners();
        this.stopHeartbeat();
        this.scheduleReconnect()
      };

      this.ws.onerror = (error) => {
        console.error('WebSocket连接错误:' + error);
        this.connectionStatus.isConnected = false;
        this.notifyConnectionListeners()
      }
    } catch (error) {
      console.error('创建WebSocket连接失败:' + error);
      this.scheduleReconnect()
    }
  }

  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null
    }
    this.stopHeartbeat();
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null
    }
  }

  private scheduleReconnect() {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
    }

    const delay = Math.min(1000 * Math.pow(2, this.connectionStatus.reconnectAttempts), 30000);
    this.connectionStatus.reconnectAttempts++

    this.reconnectTimer = setTimeout(() => {
      console.log(`重新连接尝试 #${this.connectionStatus.reconnectAttempts}`);
      this.connect()
    }, delay)
  }

  private startHeartbeat() {
    this.heartbeatTimer = setInterval(() => {
      if (this.ws?.readyState === WebSocket.OPEN) {
        this.ws.send(JSON.stringify({ type: 'ping' }));
        this.connectionStatus.lastHeartbeat = Date.now();
        this.notifyConnectionListeners()
      }
    }, 30000); // 每30秒发送心跳
  }

  private stopHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null
    }
  }

  private handleMessage(data: any) {
    switch (data.type) {
      case 'usage_update':
        this.notifySubscribers('usage', data.payload);
        break;
      case 'alert':
        this.notifySubscribers('alerts', data.payload);
        break;
      case 'system_health':
        this.notifySubscribers('health', data.payload);
        break;
      case 'pong':
        this.connectionStatus.lastHeartbeat = Date.now();
        this.notifyConnectionListeners();
        break;
      default:
        console.log('未知WebSocket消息类型:' + data.type)
    }
  }

  private notifySubscribers(event: string, data: any) {
    const subscribers = this.subscribers.get(event);
    if (subscribers) {
      subscribers.forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error('通知订阅者失败:' + error)
        }
      })
    }
  }

  private notifyConnectionListeners() {
    this.connectionListeners.forEach(listener => {
      try {
        listener(this.connectionStatus)
      } catch (error) {
        console.error('通知连接状态监听器失败:' + error)
      }
    })
  }

  // 订阅管理
  subscribe(event: string, callback: (data: any) => void) {
    if (!this.subscribers.has(event)) {
      this.subscribers.set(event, new Set())
    }
    this.subscribers.get(event)!.add(callback);

    // 如果未连接，尝试连接
    if (!this.connectionStatus.isConnected) {
      this.connect()
    }

    return () => {
      const subscribers = this.subscribers.get(event);
      if (subscribers) {
        subscribers.delete(callback);
        if (subscribers.size === 0) {
          this.subscribers.delete(event)
        }
      }
    }
  }

  onConnectionChange(callback: (status: ConnectionStatus) => void) {
    this.connectionListeners.add(callback);
    // 立即调用一次以获取当前状态
    callback(this.connectionStatus);

    return () => {
      this.connectionListeners.delete(callback)
    }
  }

  getConnectionStatus() {
    return { ...this.connectionStatus }
  }

  // HTTP API方法 (fallback当WebSocket不可用时)
  async getCurrentUsage(): Promise<RealTimeUsageData> {
    try {
      const response = await apiClient.get('/api/usage/current');
      return response.data.data
    } catch (error) {
      console.error('获取当前使用情况失败:' + error);
      throw error
    }
  }

  async getUsageHistory(timeRange: string = '24h'): Promise<RealTimeUsageData[]> {
    try {
      const response = await apiClient.get(`/api/usage/history?range=${timeRange}`);
      return response.data.data
    } catch (error) {
      console.error('获取使用历史失败:' + error);
      throw error
    }
  }

  async getAlerts(dismissed: boolean = false): Promise<UsageAlert[]> {
    try {
      const response = await apiClient.get(`/api/alerts?dismissed=${dismissed}`);
      return response.data.data
    } catch (error) {
      console.error('获取告警信息失败:' + error);
      throw error
    }
  }

  async dismissAlert(alertId: string): Promise<void> {
    try {
      await apiClient.put(`/api/alerts/${alertId}/dismiss`)
    } catch (error) {
      console.error('关闭告警失败:' + error);
      throw error
    }
  }

  async getSystemHealth(): Promise<SystemHealth> {
    try {
      const response = await apiClient.get('/api/system/health');
      return response.data.data
    } catch (error) {
      console.error('获取系统健康状态失败:' + error);
      throw error
    }
  }

  async updateAlertThresholds(thresholds: Record<string, number>): Promise<void> {
    try {
      await apiClient.put('/api/alerts/thresholds' + { thresholds })
    } catch (error) {
      console.error('更新告警阈值失败:' + error);
      throw error
    }
  }

  async exportUsageData(startDate: string, endDate: string, format: 'csv' | 'json' = 'csv'): Promise<Blob> {
    try {
      const response = await apiClient.get('/api/usage/export', {
        params: { startDate, endDate, format },
        responseType: 'blob'
      });
      return response.data
    } catch (error) {
      console.error('导出使用数据失败:' + error);
      throw error
    }
  }

  // 使用量预测
  async getUsagePredictions(category: string, days: number = 7): Promise<any> {
    try {
      const response = await apiClient.get(`/api/usage/predictions`, {
        params: { category, days }
      });
      return response.data.data
    } catch (error) {
      console.error('获取使用量预测失败:' + error);
      throw error
    }
  }

  // 成本分析
  async getCostAnalysis(period: string = 'month'): Promise<any> {
    try {
      const response = await apiClient.get(`/api/usage/cost-analysis`, {
        params: { period }
      });
      return response.data.data
    } catch (error) {
      console.error('获取成本分析失败:' + error);
      throw error
    }
  }
}

// 创建单例实例
export const realTimeApi = new RealTimeApiService();

// 导出类型和服务
export default realTimeApi;