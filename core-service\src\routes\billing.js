/**
 * Billing routes for the Core Service
 */
const express = require('express');
const router = express.Router();
const { authMiddleware, requireRole } = require('../middleware/auth');
const { asyncHandler } = require('../middleware/errorHandler');

/**
 * @route GET /billing/config
 * @desc Get Stripe configuration
 * @access Public
 */
router.get('/config', asyncHandler(async (req, res) => {
  // 从环境变量获取Stripe配置
  const stripePublishableKey = process.env.STRIPE_PUBLISHABLE_KEY;
  
  if (!stripePublishableKey) {
    console.warn('⚠️ Stripe publishable key not configured');
    return res.status(503).json({
      success: false,
      message: 'Stripe配置未完成，请联系管理员',
      error: 'Stripe publishable key not configured'
    });
  }

  console.log('✅, Stripe, configuration, requested');
  res.json({
    success: true,
    data: {
      publishableKey: stripePublishableKey,
      configured: true
    },
    message: 'Stripe配置获取成功'
  });
}));

/**
 * @route GET /billing/plans
 * @desc Get available billing plans
 * @access Public
 */
router.get('/plans', asyncHandler(async (req, res) => {
  res.json({
    success: true,
    plans: [
      {
        id: 'free',
        name: '免费版',
        price: 0,
        features: ['基础API访问', '社区支持', '基础分析']
      },
      {
        id: 'basic',
        name: '基础版',
        price: 29,
        features: ['扩展API访问', '邮件支持', '基础分析', '团队协作']
      },
      {
        id: 'professional',
        name: '专业版',
        price: 99,
        features: ['完整API访问', '优先支持', '高级分析', 'AI处理', '团队协作']
      }
    ]
  });
}));

/**
 * @route GET /billing/usage
 * @desc Get user's usage statistics
 * @access Protected
 */
router.get('/usage', authMiddleware, asyncHandler(async (req, res) => {
  // TODO: Implement usage tracking
  res.json({
    success: true,
    usage: {
      apiCalls: { current: 245, limit: 1000 },
      storage: { current: 0.5, limit: 1 },
      aiProcessing: { current: 2, limit: 10 },
      bandwidth: { current: 15, limit: 50 }
    }
  });
}));

/**
 * @route GET /billing/invoices
 * @desc Get user's billing invoices
 * @access Protected
 */
router.get('/invoices', authMiddleware, asyncHandler(async (req, res) => {
  // TODO: Implement invoice listing
  res.json({
    success: true,
    invoices: []
  });
}));

module.exports = router; 