import React from "react";
import { useTranslation } from "react-i18next";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Users, Target, TrendingDown, Clock } from "lucide-react";
import { cn } from "@/lib/utils";

interface ConversationFunnelChartProps {
  agentId?: string;
  timeRange: string;
  className?: string;
}

const ConversationFunnelChart: React.FC<ConversationFunnelChartProps> = ({
  agentId,
  timeRange, 
  className
}) => {
  const { t } = useTranslation();

  const funnelData = [
    {
      step: "Initial Contact",
      count: 1000,
      percentage: 100,
      dropoffRate: 0,
      averageTime: 0,
    },
    {
      step: "Engagement",
      count: 850,
      percentage: 85,
      dropoffRate: 15,
      averageTime: 30,
    },
    {
      step: "Information Gathering",
      count: 680,
      percentage: 68,
      dropoffRate: 20,
      averageTime: 120,
    },
    {
      step: "Problem Resolution",
      count: 544,
      percentage: 54.4,
      dropoffRate: 20,
      averageTime: 300,
    },
    {
      step: "Completion",
      count: 435,
      percentage: 43.5,
      dropoffRate: 20,
      averageTime: 450,
    }
  ];

  const overallConversionRate = (funnelData[funnelData.length - 1].count / funnelData[0].count) * 100;

  return (
    <div className={cn("space-y-6", className)}>
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2 mb-2">
              <Users className="h-4 w-4 text-blue-600" />
              <span className="text-sm font-medium">Total Entries</span>
            </div>
            <div className="text-2xl font-bold">{funnelData[0].count.toLocaleString()}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2 mb-2">
              <Target className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium">Completions</span>
            </div>
            <div className="text-2xl font-bold">{funnelData[funnelData.length - 1].count.toLocaleString()}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2 mb-2">
              <TrendingDown className="h-4 w-4 text-purple-600" />
              <span className="text-sm font-medium">Conversion Rate</span>
            </div>
            <div className="text-2xl font-bold">{overallConversionRate.toFixed(1)}%</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2 mb-2">
              <Clock className="h-4 w-4 text-yellow-600" />
              <span className="text-sm font-medium">Avg. Time</span>
            </div>
            <div className="text-2xl font-bold">7.5m</div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingDown className="h-5 w-5 text-primary" />
            {t("agents.analytics.funnel.title")}
          </CardTitle>
          <CardDescription>
            {t("agents.analytics.funnel.subtitle")}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {funnelData.map((step, index) => (
              <div key={step.step} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 rounded-full bg-primary/10 text-primary flex items-center justify-center text-sm font-medium">
                      {index + 1}
                    </div>
                    <div>
                      <h4 className="font-medium">{step.step}</h4>
                      <div className="flex items-center gap-4 text-sm text-muted-foreground">
                        <span>{step.count.toLocaleString()} users</span>
                        {step.averageTime > 0 && (
                          <span>{Math.round(step.averageTime / 60)}m avg time</span>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-medium">{step.percentage.toFixed(1)}%</div>
                    {step.dropoffRate > 0 && (
                      <Badge variant="outline" className="text-xs text-red-600">
                        -{step.dropoffRate}% drop-off
                      </Badge>
                    )}
                  </div>
                </div>
                
                <div className="relative">
                  <Progress 
                    value={step.percentage} 
                    className="h-3"
                  />
                  <div 
                    className="absolute top-0 left-0 h-3 bg-gradient-to-r from-primary to-primary/60 rounded-full transition-all duration-500"
                    style={{ width: `${step.percentage}%` }}
                  />
                </div>
                
                {index < funnelData.length - 1 && (
                  <div className="flex items-center justify-center py-2">
                    <div className="w-px h-6 bg-border" />
                  </div>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Insights & Recommendations</CardTitle>
          <CardDescription>AI-powered insights to improve your conversion funnel</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 rounded-full bg-blue-500 mt-2" />
                <div>
                  <h4 className="font-medium text-blue-900">Highest Drop-off Point</h4>
                  <p className="text-sm text-blue-700 mt-1">
                    The biggest drop-off occurs between "Engagement" and "Information Gathering" (20% drop-off). 
                    Consider simplifying the information collection process or providing clearer guidance.
                  </p>
                </div>
              </div>
            </div>
            
            <div className="p-4 bg-green-50 rounded-lg border border-green-200">
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 rounded-full bg-green-500 mt-2" />
                <div>
                  <h4 className="font-medium text-green-900">Strong Performance</h4>
                  <p className="text-sm text-green-700 mt-1">
                    Your initial engagement rate is excellent at 85%. Users are successfully connecting with your AI agent.
                  </p>
                </div>
              </div>
            </div>
            
            <div className="p-4 bg-yellow-50 rounded-lg border border-yellow-200">
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 rounded-full bg-yellow-500 mt-2" />
                <div>
                  <h4 className="font-medium text-yellow-900">Optimization Opportunity</h4>
                  <p className="text-sm text-yellow-700 mt-1">
                    Average resolution time is 7.5 minutes. Consider adding quick-answer templates for common queries 
                    to reduce time to resolution.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ConversationFunnelChart;
