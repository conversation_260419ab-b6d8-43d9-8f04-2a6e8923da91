# iBuddy2 文档中心

这是iBuddy2微服务应用的官方文档中心。为了提高可维护性和可读性，文档已按以下类别进行整理：

## 文档结构

- [架构文档](./architecture/README.md) - 系统架构、服务拓扑和技术选型
- [API文档](./api/README.md) - API接口定义、参数说明和示例
- [开发指南](./development/README.md) - 开发流程、编码规范和最佳实践
- [部署文档](./deployment/README.md) - 环境配置、部署步骤和CI/CD
- [故障排除](./troubleshooting/README.md) - 常见问题解决方案和调试技巧
- [变更日志](./changelogs/README.md) - 版本历史和功能变更记录
- [修复记录](./fixes/README.md) - 问题修复的详细记录和解决方案

## 使用指南

每个目录包含该类别的相关文档，以及一个README.md文件作为该类别的索引。请参考各目录下的索引文件以获取更详细的内容导航。

> 注意：这是一个优化过的文档结构，原始文档位于项目根目录的各个MD文件中。为保持兼容性，原始文档位置保持不变，通过符号链接或内容复制的方式实现同步。 