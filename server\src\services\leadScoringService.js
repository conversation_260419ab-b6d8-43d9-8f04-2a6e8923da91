const { LeadGeneration } = require('../models/leadGeneration');

class LeadScoringService {
  constructor() {
    // 默认评分权重配置
    this.defaultWeights = {
      demographic: 30,    // 人口统计学
      behavioral: 40,     // 行为数据
      engagement: 20,     // 互动程度
      source: 10         // 来源质量
    };

    // 评分规则映射
    this.scoringRules = {
      // 人口统计学评分规则
      demographic: {
        jobTitle: {
          'CEO': 10, 'CTO': 10, 'VP': 9, 'Director': 8, 'Manager': 7,
          'Lead': 6, 'Senior': 6, 'Developer': 5, 'Engineer': 5, 'Other': 3
        },
        companySize: {
          '1000+': 10, '500-999': 9, '100-499': 8, '50-99': 7,
          '10-49': 6, '1-9': 4, 'unknown': 3
        },
        industry: {
          'Technology': 10, 'Healthcare': 9, 'Finance': 9, 'Manufacturing': 8,
          'Education': 7, 'Retail': 6, 'Other': 5
        }
      },

      // 行为评分规则
      behavioral: {
        pageViews: {
          '10+': 10, '7-9': 8, '4-6': 6, '2-3': 4, '1': 2, '0': 0
        },
        timeOnSite: {
          '600+': 10, '300-599': 8, '120-299': 6, '60-119': 4, '30-59': 2, '0-29': 1
        },
        downloadCount: {
          '5+': 10, '3-4': 8, '2': 6, '1': 4, '0': 0
        },
        formSubmissions: {
          '3+': 10, '2': 8, '1': 5, '0': 0
        }
      },

      // 互动评分规则
      engagement: {
        emailOpens: {
          '80+': 10, '60-79': 8, '40-59': 6, '20-39': 4, '1-19': 2, '0': 0
        },
        emailClicks: {
          '50+': 10, '30-49': 8, '15-29': 6, '5-14': 4, '1-4': 2, '0': 0
        },
        socialEngagement: {
          'high': 10, 'medium': 7, 'low': 3, 'none': 0
        }
      },

      // 来源评分规则
      source: {
        'organic_search': 10,
        'direct': 9,
        'referral': 8,
        'email': 7,
        'social': 6,
        'paid_search': 5,
        'display': 4,
        'other': 3
      }
    } }

  /**
   * 计算线索评分
   * @param {Object} leadData - 线索数据
   * @param {Object} config - 评分配置
   * @returns {Object} 评分结果
   */
  async calculateScore(leadData, config = {}) {
    try {
      const weights = { ...this.defaultWeights, ...config.weights };
      const activities = leadData.activities || [];
      const customFields = leadData.customFields || {};

      // 计算各维度评分
      const scores = {
        demographic: this.calculateDemographicScore(leadData, customFields),
        behavioral: this.calculateBehavioralScore(activities),
        engagement: this.calculateEngagementScore(activities),
        source: this.calculateSourceScore(leadData.source)
      };

      // 计算加权总分
      const totalScore = Math.round(
        (scores.demographic * weights.demographic +
         scores.behavioral * weights.behavioral +
         scores.engagement * weights.engagement +
         scores.source * weights.source) / 100
      );

      // 确保评分在0-100范围内
      const finalScore = Math.max(0, Math.min(100, totalScore));

      // 计算温度等级
      const temperature = this.calculateTemperature(finalScore);

      return {
        totalScore: finalScore,
        breakdown: scores,
        weights,
        temperature,
        factors: this.getScoreFactors(leadData, activities),
        recommendations: this.getRecommendations(finalScore, scores)
      } } catch (error) {
      console.error('计算评分失败:', error);
      return {
        totalScore: 0,
        breakdown: { demographic: 0, behavioral: 0, engagement: 0, source: 0 },
        temperature: 'cold'
        error: error.message
      } }
  }

  /**
   * 计算人口统计学评分
   */
  calculateDemographicScore(leadData, customFields) {
    let score = 0;
    let factors = 0;

    // 职位评分
    if (leadData.jobTitle || customFields.jobTitle) {
      const title = (leadData.jobTitle || customFields.jobTitle).toLowerCase();
      score += this.getJobTitleScore(title);
      factors++
    }

    // 公司规模评分
    if (customFields.companySize) {
      score += this.getCompanySizeScore(customFields.companySize);
      factors++
    }

    // 行业评分
    if (customFields.industry) {
      score += this.getIndustryScore(customFields.industry);
      factors++
    }

    // 联系信息完整性
    let completeness = 0;
    if (leadData.firstName) completeness++
    if (leadData.lastName) completeness++
    if (leadData.email) completeness++
    if (leadData.phone) completeness++
    if (leadData.company) completeness++
    
    score += (completeness / 5) * 10;
    factors++

    return factors > 0 ? Math.round(score / factors) : 0;
  }

  /**
   * 计算行为评分
   */
  calculateBehavioralScore(activities) {
    const behaviorStats = this.analyzeBehaviorStats(activities);
    let score = 0;
    let factors = 0;

    // 页面浏览评分
    score += this.getPageViewsScore(behaviorStats.pageViews);
    factors++

    // 网站停留时间评分
    score += this.getTimeOnSiteScore(behaviorStats.totalTimeOnSite);
    factors++

    // 下载行为评分
    score += this.getDownloadScore(behaviorStats.downloadCount);
    factors++

    // 表单提交评分
    score += this.getFormSubmissionScore(behaviorStats.formSubmissions);
    factors++

    return factors > 0 ? Math.round(score / factors) : 0;
  }

  /**
   * 计算互动评分
   */
  calculateEngagementScore(activities) {
    const engagementStats = this.analyzeEngagementStats(activities);
    let score = 0;
    let factors = 0;

    // 邮件打开率评分
    if (engagementStats.emailStats.sent > 0) {
      const openRate = (engagementStats.emailStats.opens / engagementStats.emailStats.sent) * 100;
      score += this.getEmailOpenScore(openRate);
      factors++
    }

    // 邮件点击率评分
    if (engagementStats.emailStats.sent > 0) {
      const clickRate = (engagementStats.emailStats.clicks / engagementStats.emailStats.sent) * 100;
      score += this.getEmailClickScore(clickRate);
      factors++
    }

    // 社交媒体互动评分
    score += this.getSocialEngagementScore(engagementStats.socialEngagement);
    factors++

    return factors > 0 ? Math.round(score / factors) : 0;
  }

  /**
   * 计算来源评分
   */
  calculateSourceScore(source) {
    if (!source) return 0;
    return this.scoringRules.source[source.toLowerCase()] || 3;
  }

  /**
   * 计算温度等级
   */
  calculateTemperature(score) {
    if (score >= 75) return 'hot'
    if (score >= 50) return 'warm'
    return 'cold'
  }

  /**
   * 分析行为统计
   */
  analyzeBehaviorStats(activities) {
    const stats = {
      pageViews: 0,
      totalTimeOnSite: 0,
      downloadCount: 0,
      formSubmissions: 0,
      uniquePages: new Set()
    };

    activities.forEach(activity => {
      switch (activity.type) {
        case 'page_view':
          stats.pageViews++
          stats.uniquePages.add(activity.metadata?.url);
          break;
        case 'time_on_page':
          stats.totalTimeOnSite += activity.metadata?.timeOnPage || 0;
          break;
        case 'download':
          stats.downloadCount++
          break;
        case 'form_submit':
          stats.formSubmissions++
          break;
      }
    });

    return stats;
  }

  /**
   * 分析互动统计
   */
  analyzeEngagementStats(activities) {
    const stats = {
      emailStats: { sent: 0, opens: 0, clicks: 0 },
      socialEngagement: 'none'
    };

    activities.forEach(activity => {
      switch (activity.type) {
        case 'email_sent':
          stats.emailStats.sent++
          break;
        case 'email_open':
          stats.emailStats.opens++
          break;
        case 'email_click':
          stats.emailStats.clicks++
          break;
        case 'social_share':
        case 'social_like':
        case 'social_comment':
          if (stats.socialEngagement === 'none') stats.socialEngagement = 'low'
          if (activity.metadata?.engagement === 'high') stats.socialEngagement = 'high'
          break;
      }
    });

    return stats;
  }

  /**
   * 获取职位评分
   */
  getJobTitleScore(title) {
    for (const [keyword, score] of Object.entries(this.scoringRules.demographic.jobTitle)) {
      if (title.includes(keyword.toLowerCase())) {
        return score;
      }
    }
    return this.scoringRules.demographic.jobTitle.Other;
  }

  /**
   * 获取公司规模评分
   */
  getCompanySizeScore(size) {
    return this.scoringRules.demographic.companySize[size] || 3;
  }

  /**
   * 获取行业评分
   */
  getIndustryScore(industry) {
    return this.scoringRules.demographic.industry[industry] || 5;
  }

  /**
   * 获取页面浏览评分
   */
  getPageViewsScore(count) {
    if (count >= 10) return 10;
    if (count >= 7) return 8;
    if (count >= 4) return 6;
    if (count >= 2) return 4;
    if (count >= 1) return 2;
    return 0;
  }

  /**
   * 获取停留时间评分
   */
  getTimeOnSiteScore(seconds) {
    if (seconds >= 600) return 10;
    if (seconds >= 300) return 8;
    if (seconds >= 120) return 6;
    if (seconds >= 60) return 4;
    if (seconds >= 30) return 2;
    return 1;
  }

  /**
   * 获取下载评分
   */
  getDownloadScore(count) {
    if (count >= 5) return 10;
    if (count >= 3) return 8;
    if (count >= 2) return 6;
    if (count >= 1) return 4;
    return 0;
  }

  /**
   * 获取表单提交评分
   */
  getFormSubmissionScore(count) {
    if (count >= 3) return 10;
    if (count >= 2) return 8;
    if (count >= 1) return 5;
    return 0;
  }

  /**
   * 获取邮件打开评分
   */
  getEmailOpenScore(rate) {
    if (rate >= 80) return 10;
    if (rate >= 60) return 8;
    if (rate >= 40) return 6;
    if (rate >= 20) return 4;
    if (rate >= 1) return 2;
    return 0;
  }

  /**
   * 获取邮件点击评分
   */
  getEmailClickScore(rate) {
    if (rate >= 50) return 10;
    if (rate >= 30) return 8;
    if (rate >= 15) return 6;
    if (rate >= 5) return 4;
    if (rate >= 1) return 2;
    return 0;
  }

  /**
   * 获取社交互动评分
   */
  getSocialEngagementScore(level) {
    return this.scoringRules.engagement.socialEngagement[level] || 0;
  }

  /**
   * 获取评分因子
   */
  getScoreFactors(leadData, activities) {
    const factors = [];

    // 正面因子
    if (leadData.jobTitle?.toLowerCase().includes('ceo')) {
      factors.push({ type: 'positive' factor: '高级管理职位' impact: '+10' });
    }
    
    if (activities.filter(a => a.type === 'download').length > 0) {
      factors.push({ type: 'positive' factor: '下载了资料' impact: '+5' });
    }

    if (activities.filter(a => a.type === 'form_submit').length > 1) {
      factors.push({ type: 'positive' factor: '多次表单提交' impact: '+8' });
    }

    // 负面因子
    if (!leadData.phone) {
      factors.push({ type: 'negative' factor: '缺少电话号码' impact: '-3' });
    }

    const recentActivity = activities.filter(a => 
      new Date(a.createdAt) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
    );
    
    if (recentActivity.length === 0) {
      factors.push({ type: 'negative' factor: '近期无活动' impact: '-5' });
    }

    return factors;
  }

  /**
   * 获取改进建议
   */
  getRecommendations(score, breakdown) {
    const recommendations = [];

    if (score < 50) {
      recommendations.push({
        priority: 'high'
        category: 'overall'
        message: '该线索评分较低，建议进行更多的培育活动'
      });
    }

    if (breakdown.behavioral < 5) {
      recommendations.push({
        priority: 'medium'
        category: 'engagement'
        message: '缺乏网站互动，考虑发送相关内容引导访问'
      });
    }

    if (breakdown.demographic < 6) {
      recommendations.push({
        priority: 'medium'
        category: 'information'
        message: '人口统计学信息不完整，建议收集更多背景信息'
      });
    }

    if (breakdown.engagement < 4) {
      recommendations.push({
        priority: 'high'
        category: 'nurturing'
        message: '互动程度低，建议启动邮件培育序列'
      });
    }

    return recommendations;
  }

  /**
   * 批量重新计算评分
   */
  async recalculateScores(agentId, config = {}) {
    try {
      const leads = await LeadGeneration.findAll({
        where: { agentId },
        include: ['activities']
      });

      const results = [];
      
      for (const lead of leads) {
        const scoreResult = await this.calculateScore(lead.toJSON(), config);
        
        await lead.update({
          score: scoreResult.totalScore,
          temperature: scoreResult.temperature,
          scoreBreakdown: scoreResult.breakdown,
          lastScoreUpdate: new Date()
        });

        results.push({
          leadId: lead.id,
          oldScore: lead.score,
          newScore: scoreResult.totalScore,
          temperature: scoreResult.temperature
        });
      }

      return {
        success: true,
        processed: results.length,
        results
      } } catch (error) {
      console.error('批量重新计算评分失败:', error);
      throw error;
    }
  }

  /**
   * 获取评分趋势
   */
  async getScoreTrend(leadId, days = 30) {
    try {
      const endDate = new Date();
      const startDate = new Date(endDate.getTime() - days * 24 * 60 * 60 * 1000);

      // 这里应该从数据库获取历史评分数据
      // 简化实现，返回模拟数据
      const trend = [];
      for (let i = 0; i < days; i += 7) {
        trend.push({
          date: new Date(startDate.getTime() + i * 24 * 60 * 60 * 1000),
          score: Math.floor(Math.random() * 100)
        });
      }

      return trend;
    } catch (error) {
      console.error('获取评分趋势失败:', error);
      throw error;
    }
  }
}

module.exports = LeadScoringService; 