import React, { memo } from 'react';
import { <PERSON>le, Position, NodeProps } from 'reactflow';
import { useTranslation } from 'react-i18next';
import { 
  PlayCircle, 
  GitBranch, 
  Brain, 
  User, 
  Globe, 
  Clock, 
  Send, 
  Mail, 
  MessageSquare,
  Variable,
  CornerDownRight,
  Square,
  AlertCircle
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { FlowNodeType, NODE_TYPE_DEFINITIONS } from '@/types/flowBuilder';

interface FlowNodeData {
  label: string;
  description?: string;
  config?: Record<string, any>;
  isValid?: boolean;
  hasWarnings?: boolean
}

const FlowNodeComponent: React.FC<NodeProps<FlowNodeData>> = ({ 
  data, 
  selected, type 
}) => {
  const { t } = useTranslation();
  const nodeType = type as FlowNodeType;
  const nodeDefinition = NODE_TYPE_DEFINITIONS[nodeType];

  // 获取节点图标
  const getNodeIcon = () => {
    switch (nodeType) {
      case FlowNodeType.TRIGGER:
        return <PlayCircle className="h-4 w-4" />;
      case FlowNodeType.CONDITION:
        return <GitBranch className="h-4 w-4" />;
      case FlowNodeType.AI_RESPONSE:
        return <Brain className="h-4 w-4" />;
      case FlowNodeType.HUMAN_HANDOFF:
        return <User className="h-4 w-4" />;
      case FlowNodeType.API_CALL:
        return <Globe className="h-4 w-4" />;
      case FlowNodeType.DELAY:
        return <Clock className="h-4 w-4" />;
      case FlowNodeType.WEBHOOK:
        return <Send className="h-4 w-4" />;
      case FlowNodeType.EMAIL:
        return <Mail className="h-4 w-4" />;
      case FlowNodeType.SMS:
        return <MessageSquare className="h-4 w-4" />;
      case FlowNodeType.VARIABLE_SET:
        return <Variable className="h-4 w-4" />;
      case FlowNodeType.JUMP:
        return <CornerDownRight className="h-4 w-4" />;
      case FlowNodeType.END:
        return <Square className="h-4 w-4" />;
      default:
        return <AlertCircle className="h-4 w-4" />
}
  };

  // 获取节点状态指示器
  const getStatusIndicator = () => {
    if (data.isValid === false) {
      return (
        <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full border-2 border-white" />
      )
}
    if (data.hasWarnings) {
      return (
        <div className="absolute -top-1 -right-1 w-3 h-3 bg-yellow-500 rounded-full border-2 border-white" />
      )
}
    return null
};

  // 获取配置摘要
  const getConfigSummary = () => {
    if (!data.config) return null;

    switch (nodeType) {
      case FlowNodeType.TRIGGER:
        return data.config.triggerType ? `Trigger: ${data.config.triggerType}` : null;
      case FlowNodeType.CONDITION:
        return data.config.conditionType ? `If: ${data.config.conditionType}` : null;
      case FlowNodeType.AI_RESPONSE:
        return data.config.prompt ? `AI: ${data.config.prompt.substring(0, 30)}...` : null;
      case FlowNodeType.DELAY:
        return data.config.delayValue ? `Wait: ${data.config.delayValue} ${data.config.delayType}` : null;
      case FlowNodeType.API_CALL:
        return data.config.apiUrl ? `API: ${data.config.apiMethod} ${data.config.apiUrl}` : null;
      default:
        return null
}
  };

  const configSummary = getConfigSummary();

  return (
    <div
      className={cn(
        "relative bg-white border-2 rounded-lg shadow-sm transition-all duration-200 min-w-[150px]",
        selected ? "border-primary shadow-lg" : "border-gray-200 hover:border-gray-300",
        data.isValid === false && "border-red-300 bg-red-50",
        data.hasWarnings && "border-yellow-300 bg-yellow-50"
      )}
      style={{ backgroundColor: nodeDefinition?.color + '10' }}
    >
      {/* 输入连接点 */}
      {nodeDefinition?.inputs > 0 && (
        <Handle
          type="target"
          position={Position.Left}
          className="w-3 h-3 border-2 border-white"
          style={{ backgroundColor: nodeDefinition.color }}
        />
      )}

      {/* 节点内容 */}
      <div className="p-3">
        {/* 节点头部 */}
        <div className="flex items-center gap-2 mb-2">
          <div 
            className="p-1.5 rounded text-white"
            style={{ backgroundColor: nodeDefinition?.color }}
          >
            {getNodeIcon()}
          </div>
          <div className="flex-1 min-w-0">
            <h4 className="font-medium text-sm text-gray-900 truncate">
              {t(`agents.flowBuilder.nodeTypes.${nodeType}`) || data.label}
            </h4>
          </div>
        </div>

        {/* 配置摘要 */}
        {configSummary && (
          <div className="text-xs text-gray-600 bg-gray-50 rounded px-2 py-1 mb-2">
            {configSummary}
          </div>
        )}

        {/* 节点描述 */}
        {data.description && (
          <p className="text-xs text-gray-500 line-clamp-2">
            {data.description}
          </p>
        )}
      </div>

      {/* 输出连接点 */}
      {nodeDefinition?.outputs > 0 && (
        <>
          {nodeType === FlowNodeType.CONDITION ? (
            // 条件节点有两个输出
            <>
              <Handle
                type="source"
                position={Position.Right}
                id="true"
                className="w-3 h-3 border-2 border-white"
                style={{
                  backgroundColor: '#10B981',
                  top: '40%'
                }}
              />
              <Handle
                type="source"
                position={Position.Right}
                id="false"
                className="w-3 h-3 border-2 border-white"
                style={{ 
                  backgroundColor: '#EF4444',
                  top: '60%'
                }}
              />
              {/* 标签 */}
              <div className="absolute right-0 top-1/2 transform translate-x-full -translate-y-1/2 ml-2">
                <div className="text-xs text-green-600 mb-1">True</div>
                <div className="text-xs text-red-600">False</div>
              </div>
            </>
          ) : (
            // 普通节点单个输出
            <Handle
              type="source"
              position={Position.Right}
              className="w-3 h-3 border-2 border-white"
              style={{ backgroundColor: nodeDefinition.color }}
            />
          )}
        </>
      )}

      {/* 状态指示器 */}
      {getStatusIndicator()}

      {/* 选中状态指示器 */}
      {selected && (
        <div className="absolute inset-0 border-2 border-primary rounded-lg pointer-events-none" />
      )}
    </div>
  )
};

export default memo(FlowNodeComponent);
