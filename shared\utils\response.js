/**
 * 共享响应工具
 * 为所有微服务提供统一的API响应格式
 */

/**
 * 成功响应格式
 * @param {*} data - 响应数据
 * @param {string} message - 响应消息
 * @param {Object} meta - 元数据
 * @returns {Object} - 格式化的成功响应
 */
const successResponse = (data = null, message = '操作成功', meta = {}) => {
  const response = {
    success: true,
    message,
    data
  };
  
  if (Object.keys(meta).length > 0) {
    response.meta = meta;
  }
  
  return response;
};

/**
 * 错误响应格式
 * @param {string} message - 错误消息
 * @param {string} code - 错误代码
 * @param {*} details - 错误详情
 * @returns {Object} - 格式化的错误响应
 */
const errorResponse = (message = '操作失败', code = 'UNKNOWN_ERROR', details = null) => {
  const response = {
    success: false,
    error: {
      message,
      code
    }
  };
  
  if (details) {
    response.error.details = details;
  }
  
  return response;
};

/**
 * 分页响应格式
 * @param {Array} data - 数据数组
 * @param {number} page - 当前页码
 * @param {number} limit - 每页数量
 * @param {number} total - 总数量
 * @param {string} message - 响应消息
 * @returns {Object} - 格式化的分页响应
 */
const paginatedResponse = (data = [], page = 1, limit = 10, total = 0, message = '获取成功') => {
  const totalPages = Math.ceil(total / limit);
  
  return {
    success: true,
    message,
    data,
    pagination: {
      page: Number(page),
      limit: Number(limit),
      total: Number(total),
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1
    }
  };
};

/**
 * 创建响应格式
 * @param {*} data - 创建的数据
 * @param {string} message - 响应消息
 * @returns {Object} - 格式化的创建响应
 */
const createdResponse = (data = null, message = '创建成功') => {
  return {
    success: true,
    message,
    data
  };
};

/**
 * 更新响应格式
 * @param {*} data - 更新的数据
 * @param {string} message - 响应消息
 * @returns {Object} - 格式化的更新响应
 */
const updatedResponse = (data = null, message = '更新成功') => {
  return {
    success: true,
    message,
    data
  };
};

/**
 * 删除响应格式
 * @param {string} message - 响应消息
 * @returns {Object} - 格式化的删除响应
 */
const deletedResponse = (message = '删除成功') => {
  return {
    success: true,
    message,
    data: null
  };
};

/**
 * 验证错误响应
 * @param {Object} errors - 验证错误对象
 * @param {string} message - 错误消息
 * @returns {Object} - 格式化的验证错误响应
 */
const validationErrorResponse = (errors = {}, message = '数据验证失败') => {
  return {
    success: false,
    error: {
      message,
      code: 'VALIDATION_ERROR',
      details: errors
    }
  };
};

/**
 * 认证错误响应
 * @param {string} message - 错误消息
 * @returns {Object} - 格式化的认证错误响应
 */
const authErrorResponse = (message = '认证失败') => {
  return {
    success: false,
    error: {
      message,
      code: 'AUTHENTICATION_ERROR'
    }
  };
};

/**
 * 权限错误响应
 * @param {string} message - 错误消息
 * @returns {Object} - 格式化的权限错误响应
 */
const forbiddenResponse = (message = '权限不足') => {
  return {
    success: false,
    error: {
      message,
      code: 'AUTHORIZATION_ERROR'
    }
  };
};

/**
 * 资源未找到响应
 * @param {string} message - 错误消息
 * @returns {Object} - 格式化的未找到响应
 */
const notFoundResponse = (message = '资源未找到') => {
  return {
    success: false,
    error: {
      message,
      code: 'NOT_FOUND_ERROR'
    }
  };
};

/**
 * 冲突错误响应
 * @param {string} message - 错误消息
 * @param {*} details - 错误详情
 * @returns {Object} - 格式化的冲突错误响应
 */
const conflictResponse = (message = '资源冲突', details = null) => {
  const response = {
    success: false,
    error: {
      message,
      code: 'CONFLICT_ERROR'
    }
  };
  
  if (details) {
    response.error.details = details;
  }
  
  return response;
};

/**
 * 速率限制错误响应
 * @param {string} message - 错误消息
 * @param {number} retryAfter - 重试间隔（秒）
 * @returns {Object} - 格式化的速率限制错误响应
 */
const rateLimitResponse = (message = '请求过于频繁', retryAfter = 60) => {
  return {
    success: false,
    error: {
      message,
      code: 'RATE_LIMIT_ERROR',
      retryAfter
    }
  };
};

/**
 * 服务器错误响应
 * @param {string} message - 错误消息
 * @param {*} details - 错误详情（仅在开发环境显示）
 * @returns {Object} - 格式化的服务器错误响应
 */
const serverErrorResponse = (message = '内部服务器错误', details = null) => {
  const response = {
    success: false,
    error: {
      message,
      code: 'INTERNAL_SERVER_ERROR'
    }
  };
  
  // 只在开发环境显示详细错误信息
  if (details && process.env.NODE_ENV === 'development') {
    response.error.details = details;
  }
  
  return response;
};

/**
 * 发送JSON响应的辅助函数
 * @param {Object} res - Express响应对象
 * @param {number} statusCode - HTTP状态码
 * @param {Object} data - 响应数据
 */
const sendResponse = (res, statusCode, data) => {
  return res.status(statusCode).json(data);
};

/**
 * 发送成功响应
 * @param {Object} res - Express响应对象
 * @param {*} data - 响应数据
 * @param {string} message - 响应消息
 * @param {Object} meta - 元数据
 */
const sendSuccess = (res, data = null, message = '操作成功', meta = {}) => {
  return sendResponse(res, 200, successResponse(data, message, meta));
};

/**
 * 发送创建成功响应
 * @param {Object} res - Express响应对象
 * @param {*} data - 创建的数据
 * @param {string} message - 响应消息
 */
const sendCreated = (res, data = null, message = '创建成功') => {
  return sendResponse(res, 201, createdResponse(data, message));
};

/**
 * 发送错误响应
 * @param {Object} res - Express响应对象
 * @param {number} statusCode - HTTP状态码
 * @param {string} message - 错误消息
 * @param {string} code - 错误代码
 * @param {*} details - 错误详情
 */
const sendError = (res, statusCode = 500, message = '操作失败', code = 'UNKNOWN_ERROR', details = null) => {
  return sendResponse(res, statusCode, errorResponse(message, code, details));
};

module.exports = {
  successResponse,
  errorResponse,
  paginatedResponse,
  createdResponse,
  updatedResponse,
  deletedResponse,
  validationErrorResponse,
  authErrorResponse,
  forbiddenResponse,
  notFoundResponse,
  conflictResponse,
  rateLimitResponse,
  serverErrorResponse,
  sendResponse,
  sendSuccess,
  sendCreated,
  sendError
}; 