-- 创建模块表
CREATE TABLE IF NOT EXISTS modules (
  moduleId VARCHAR PRIMARY KEY,
  moduleName VARCHAR NOT NULL,
  description TEXT,
  isEnabled BOOLEAN DEFAULT false,
  version VARCHAR,
  lastUpdated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  highLevelConfig JSONB DEFAULT '{}'::jsonb,
  hasDetailedSettings BOOLEAN DEFAULT false
);

-- 创建模块设置表
CREATE TABLE IF NOT EXISTS module_settings (
  id SERIAL PRIMARY KEY,
  moduleId VARCHAR NOT NULL REFERENCES modules(moduleId) ON DELETE CASCADE,
  settings JSONB DEFAULT '{}'::jsonb,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(moduleId)
);

-- 创建模块设置元数据表
CREATE TABLE IF NOT EXISTS module_settings_metadata (
  id SERIAL PRIMARY KEY,
  moduleId VARCHAR NOT NULL REFERENCES modules(moduleId) ON DELETE CASCADE,
  metadata JSONB DEFAULT '{}'::jsonb,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(moduleId)
);

-- 创建初始数据的函数
CREATE OR REPLACE FUNCTION initialize_modules()
RETURNS VOID AS $$
BEGIN
  -- 只有当模块表为空时才插入初始数据
  IF (SELECT COUNT(*) FROM modules) = 0 THEN
    -- 插入模块数据
    INSERT INTO modules (moduleId, moduleName, description, isEnabled, version, lastUpdated, highLevelConfig, hasDetailedSettings)
    VALUES 
      ('ai-service', 'AI 服务', '管理与AI相关的服务，包括聊天机器人、智能客服和语音分析', true, '1.2.0', NOW(), 
       '{"maxConcurrentRequests": 50, "defaultModel": "gpt-3.5-turbo", "enableLogging": true, "fallbackMode": "rule-based"}'::jsonb, 
       true),
      ('notification-system', '通知系统', '管理系统通知，包括邮件、短信和应用内通知', true, '2.0.1', NOW(), 
       '{"emailEnabled": true, "smsEnabled": false, "pushEnabled": true, "batchingInterval": 5}'::jsonb, 
       true),
      ('data-analytics', '数据分析', '处理和分析用户行为数据，生成报表和洞察', false, '0.9.5', NOW(), 
       '{"dataRetentionDays": 90, "anonymizeUserData": true, "realTimeAnalysis": false}'::jsonb, 
       true),
      ('integration-hub', '集成中心', '管理与第三方服务和API的集成', true, '1.5.2', NOW(), 
       '{"autoRetry": true, "maxRetryAttempts": 3, "logFailedRequests": true}'::jsonb, 
       true),
      ('user-management', '用户管理', '管理系统用户，包括权限控制和角色分配', true, '2.1.0', NOW(), 
       '{"sessionTimeoutMinutes": 60, "enforceStrongPasswords": true, "maxLoginAttempts": 5}'::jsonb, 
       false);

    -- 插入模块设置数据
    INSERT INTO module_settings (moduleId, settings)
    VALUES
      ('ai-service', '{
        "apiKey": "******************************",
        "modelType": "gpt-3.5-turbo",
        "temperature": 0.7,
        "maxTokens": 2048,
        "systemPrompt": "你是一个专业的客服助手，负责解答用户关于产品和服务的问题。",
        "enableModeration": true,
        "moderationLevel": "medium",
        "customPrompt": "",
        "contextWindow": 10,
        "responseDelay": 0
      }'::jsonb),
      ('notification-system', '{
        "emailServiceProvider": "smtp",
        "smtpHost": "smtp.example.com",
        "smtpPort": 587,
        "smtpUser": "<EMAIL>",
        "smtpPassword": "******************",
        "emailFromAddress": "<EMAIL>",
        "emailFromName": "系统通知",
        "emailTemplatesPath": "/templates/email",
        "smsApiKey": "*********************",
        "smsProvider": "aliyun",
        "smsSignName": "公司名称",
        "pushCertificatePath": "/certs/push.p12",
        "defaultChannel": "email"
      }'::jsonb),
      ('data-analytics', '{
        "trackingEnabled": true,
        "collectDemographics": false,
        "collectBehavioralData": true,
        "collectPerformanceMetrics": true,
        "samplingRate": 100,
        "excludedPaths": ["/health", "/api/internal"],
        "customDimensions": [
          {"name": "plan", "source": "user.subscription_plan"},
          {"name": "region", "source": "user.region"}
        ],
        "scheduledReports": [
          {"name": "周报", "schedule": "0 9 * * 1", "recipients": ["<EMAIL>"]},
          {"name": "月报", "schedule": "0 9 1 * *", "recipients": ["<EMAIL>"]}
        ],
        "retentionPolicy": "rotate-90-days"
      }'::jsonb),
      ('integration-hub', '{
        "endpoints": [
          {"name": "CRM API", "url": "https://crm.example.com/api/v2", "authType": "oauth2"},
          {"name": "Payment Gateway", "url": "https://payments.example.com", "authType": "api-key"},
          {"name": "Logistics Service", "url": "https://logistics.example.com/api", "authType": "basic"}
        ],
        "defaultTimeout": 30000,
        "circuitBreakerThreshold": 5,
        "circuitBreakerResetTime": 60000,
        "webhookSecret": "*****************************",
        "webhookEndpoint": "/api/webhook-receiver",
        "loggingLevel": "error",
        "cacheEnabled": true,
        "cacheTTL": 300
      }'::jsonb);

    -- 插入模块设置元数据
    INSERT INTO module_settings_metadata (moduleId, metadata)
    VALUES
      ('ai-service', '{
        "apiKey": {
          "validation": {
            "required": true,
            "minLength": 30
          },
          "description": "API密钥用于访问AI服务提供商的API",
          "sensitive": true
        },
        "modelType": {
          "validation": {
            "required": true,
            "enum": ["gpt-3.5-turbo", "gpt-4", "claude-2"]
          },
          "description": "选择使用的AI语言模型"
        },
        "temperature": {
          "validation": {
            "required": true,
            "min": 0,
            "max": 1
          },
          "description": "控制生成文本的随机性（0-1），越低越确定性，越高越创造性"
        },
        "maxTokens": {
          "validation": {
            "required": true,
            "min": 1,
            "max": 4096
          },
          "description": "生成的最大令牌数量"
        },
        "systemPrompt": {
          "validation": {
            "maxLength": 1000
          },
          "description": "设置AI的系统指令"
        },
        "enableModeration": {
          "description": "是否启用内容审核"
        },
        "moderationLevel": {
          "validation": {
            "enum": ["low", "medium", "high"]
          },
          "description": "内容审核的严格程度"
        },
        "customPrompt": {
          "validation": {
            "maxLength": 2000
          },
          "description": "可选的自定义提示词"
        },
        "contextWindow": {
          "validation": {
            "min": 1,
            "max": 20
          },
          "description": "保持的对话轮数"
        },
        "responseDelay": {
          "validation": {
            "min": 0,
            "max": 10000
          },
          "description": "人工响应延迟（毫秒）"
        }
      }'::jsonb),
      ('notification-system', '{
        "emailServiceProvider": {
          "validation": {
            "required": true,
            "enum": ["smtp", "sendgrid", "mailgun"]
          },
          "description": "选择邮件服务提供商"
        },
        "smtpHost": {
          "validation": {
            "required": true,
            "dependsOn": "emailServiceProvider",
            "dependsValue": "smtp"
          },
          "description": "SMTP服务器地址"
        },
        "smtpPort": {
          "validation": {
            "required": true,
            "min": 1,
            "max": 65535,
            "dependsOn": "emailServiceProvider",
            "dependsValue": "smtp"
          },
          "description": "SMTP服务器端口"
        }
      }'::jsonb);
  END IF;
END;
$$ LANGUAGE plpgsql;

-- 执行初始化函数
SELECT initialize_modules(); 