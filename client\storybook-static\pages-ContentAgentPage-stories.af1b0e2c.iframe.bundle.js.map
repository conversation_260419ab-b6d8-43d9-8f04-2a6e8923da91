{"version": 3, "file": "pages-ContentAgentPage-stories.af1b0e2c.iframe.bundle.js", "mappings": ";;AAWA;;;;AAMA;;;;AAMA;;;;AAMA;;;AAKA;;AAIA;;;AAKA;;;;;;;AASA;;;;AAMA", "sources": ["webpack://client/./src/pages/ContentAgentPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { Card, Row, Col, Form, Input, Button, Select, Radio, Slider, Tag, Spin, message, Tabs } from 'antd';\r\nimport styled from 'styled-components';\r\nimport { CopyOutlined, CheckOutlined, HistoryOutlined, SaveOutlined } from '@ant-design/icons';\r\n\r\nconst { Option } = Select;\r\nconst { TextArea } = Input;\r\nconst { TabPane } = Tabs;\r\n\r\nconst PageContainer = styled.div`\r\n  padding: 24px;\r\n`;\r\n\r\nconst PageTitle = styled.h1`\r\n  font-size: 24px;\r\n  font-weight: 600;\r\n  margin-bottom: 8px;\r\n`;\r\n\r\nconst PageDescription = styled.p`\r\n  color: #666;\r\n  font-size: 14px;\r\n  margin-bottom: 24px;\r\n`;\r\n\r\nconst StyledCard = styled(Card)`\r\n  margin-bottom: 24px;\r\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);\r\n  border-radius: 8px;\r\n`;\r\n\r\nconst ContentWrapper = styled.div`\r\n  white-space: pre-wrap;\r\n  line-height: 1.6;\r\n`;\r\n\r\nconst ActionButton = styled(Button)`\r\n  margin-right: 8px;\r\n`;\r\n\r\nconst ContentHistory = styled.div`\r\n  max-height: 400px;\r\n  overflow-y: auto;\r\n`;\r\n\r\nconst HistoryItem = styled.div`\r\n  padding: 12px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n  cursor: pointer;\r\n  &:hover {\r\n    background: #f5f5f5;\r\n  }\r\n`;\r\n\r\nconst HistoryDate = styled.div`\r\n  font-size: 12px;\r\n  color: #999;\r\n  margin-top: 4px;\r\n`;\r\n\r\nfunction ContentAgentPage() {\r\n  const [form] = Form.useForm();\r\n  const [loading, setLoading] = useState(false);\r\n  const [generatedContent, setGeneratedContent] = useState('');\r\n  const [copiedStatus, setCopiedStatus] = useState(false);\r\n  const [activeTab, setActiveTab] = useState('1');\r\n  const [history, setHistory] = useState([]);\r\n\r\n  // 支持的语言列表\r\n  const languages = [\r\n    { value: 'en', label: '英语 (English)' },\r\n    { value: 'zh', label: '中文 (Chinese)' },\r\n    { value: 'ms', label: '马来语 (Bahasa Melayu)' },\r\n    { value: 'ja', label: '日语 (Japanese)' },\r\n    { value: 'ko', label: '韩语 (Korean)' },\r\n  ];\r\n\r\n  // 内容类型\r\n  const contentTypes = [\r\n    { value: 'blog', label: '博客文章' },\r\n    { value: 'social', label: '社交媒体帖子' },\r\n    { value: 'email', label: '电子邮件' },\r\n    { value: 'product', label: '产品描述' },\r\n    { value: 'ad', label: '广告文案' },\r\n  ];\r\n\r\n  // 语气选项\r\n  const toneOptions = [\r\n    { value: 'professional', label: '专业' },\r\n    { value: 'friendly', label: '友好' },\r\n    { value: 'casual', label: '随意' },\r\n    { value: 'enthusiastic', label: '热情' },\r\n    { value: 'formal', label: '正式' },\r\n  ];\r\n\r\n  useEffect(() => {\r\n    // 模拟加载历史记录\r\n    const mockHistory = [\r\n      {\r\n        id: 1,\r\n        title: '夏季促销活动介绍',\r\n        content: '夏季促销活动即将开始！我们为您准备了众多精彩折扣...',\r\n        createdAt: '2023-07-15 14:23',\r\n        language: 'zh',\r\n        contentType: 'marketing'\r\n      },\r\n      {\r\n        id: 2,\r\n        title: 'New Product Launch Email',\r\n        content: 'We are excited to announce our new product launch...',\r\n        createdAt: '2023-07-12 09:15',\r\n        language: 'en',\r\n        contentType: 'email'\r\n      },\r\n      {\r\n        id: 3,\r\n        title: 'Promosi Musim Panas',\r\n        content: 'Promosi musim panas akan bermula! Kami telah menyediakan pelbagai diskaun menarik...',\r\n        createdAt: '2023-07-10 16:45',\r\n        language: 'ms',\r\n        contentType: 'social'\r\n      }\r\n    ];\r\n    \r\n    setHistory(mockHistory);\r\n  }, []);\r\n\r\n  const handleGenerate = async (values) => {\r\n    setLoading(true);\r\n    try {\r\n      // 在实际应用中，这里会调用API生成内容\r\n      // const response = await api.generateContent(values);\r\n      // setGeneratedContent(response.data.content);\r\n      \r\n      // 模拟生成内容的延迟\r\n      setTimeout(() => {\r\n        // 根据不同语言生成示例内容\r\n        let sampleContent = '';\r\n        \r\n        if (values.language === 'zh') {\r\n          sampleContent = `# ${values.title}\\n\\n这是一篇关于${values.keywords}的${contentTypes.find(ct => ct.value === values.contentType)?.label || '内容'}。\\n\\n我们可以看到${values.keywords}在现代社会中扮演着重要角色。无论是在工作还是生活中，${values.keywords}都为我们带来了极大的便利。\\n\\n## 为什么${values.keywords}如此重要？\\n\\n首先，${values.keywords}提高了效率。其次，它使流程更加简化。最后，它为用户创造了更好的体验。\\n\\n## 总结\\n\\n总之，${values.keywords}是一个值得关注的领域，未来它将继续发展并改变我们的生活方式。`;\r\n        } else if (values.language === 'en') {\r\n          sampleContent = `# ${values.title}\\n\\nThis is an article about ${values.keywords}.\\n\\nWe can see that ${values.keywords} plays an important role in modern society. Whether in work or life, ${values.keywords} has brought us great convenience.\\n\\n## Why is ${values.keywords} so important?\\n\\nFirstly, ${values.keywords} improves efficiency. Secondly, it simplifies processes. Finally, it creates a better experience for users.\\n\\n## Summary\\n\\nIn conclusion, ${values.keywords} is a field worth paying attention to, and in the future it will continue to develop and change our way of life.`;\r\n        } else if (values.language === 'ms') {\r\n          sampleContent = `# ${values.title}\\n\\nIni adalah artikel tentang ${values.keywords}.\\n\\nKita dapat melihat bahawa ${values.keywords} memainkan peranan penting dalam masyarakat moden. Sama ada dalam kerja atau kehidupan, ${values.keywords} telah membawa kemudahan yang besar kepada kita.\\n\\n## Mengapa ${values.keywords} begitu penting?\\n\\nPertama, ${values.keywords} meningkatkan kecekapan. Kedua, ia mempermudahkan proses. Akhirnya, ia mencipta pengalaman yang lebih baik untuk pengguna.\\n\\n## Ringkasan\\n\\nKesimpulannya, ${values.keywords} adalah bidang yang perlu diberi perhatian, dan pada masa akan datang ia akan terus berkembang dan mengubah cara hidup kita.`;\r\n        } else {\r\n          sampleContent = `Generated content for ${values.title} in ${values.language} language about ${values.keywords}.`;\r\n        }\r\n        \r\n        setGeneratedContent(sampleContent);\r\n        \r\n        // 添加到历史记录\r\n        const newHistoryItem = {\r\n          id: history.length + 1,\r\n          title: values.title,\r\n          content: sampleContent,\r\n          createdAt: new Date().toLocaleString(),\r\n          language: values.language,\r\n          contentType: values.contentType\r\n        };\r\n        \r\n        setHistory([newHistoryItem, ...history]);\r\n        setLoading(false);\r\n      }, 1500);\r\n      \r\n    } catch (error) {\r\n      message.error('生成内容失败');\r\n      console.error(error);\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleCopy = () => {\r\n    navigator.clipboard.writeText(generatedContent).then(() => {\r\n      setCopiedStatus(true);\r\n      message.success('内容已复制到剪贴板');\r\n      setTimeout(() => setCopiedStatus(false), 2000);\r\n    }).catch(err => {\r\n      message.error('复制失败');\r\n      console.error(err);\r\n    });\r\n  };\r\n\r\n  const handleSave = () => {\r\n    message.success('内容已保存');\r\n    // 实际应用中这里会调用API保存内容\r\n  };\r\n\r\n  const handleHistoryItemClick = (item) => {\r\n    form.setFieldsValue({\r\n      title: item.title,\r\n      language: item.language,\r\n      contentType: item.contentType\r\n    });\r\n    \r\n    setGeneratedContent(item.content);\r\n    setActiveTab('1');\r\n  };\r\n\r\n  return (\r\n    <PageContainer>\r\n      <PageTitle>内容生成助手</PageTitle>\r\n      <PageDescription>使用AI助手快速生成各种类型的内容</PageDescription>\r\n      \r\n      <Tabs activeKey={activeTab} onChange={setActiveTab}>\r\n        <TabPane tab=\"生成内容\" key=\"1\">\r\n          <Row gutter={24}>\r\n            <Col xs={24} lg={10}>\r\n              <StyledCard title=\"内容参数\">\r\n                <Form\r\n                  form={form}\r\n                  layout=\"vertical\"\r\n                  onFinish={handleGenerate}\r\n                  initialValues={{\r\n                    language: 'zh',\r\n                    contentType: 'blog',\r\n                    tone: 'professional',\r\n                    length: 500\r\n                  }}\r\n                >\r\n                  <Form.Item\r\n                    name=\"title\"\r\n                    label=\"标题\"\r\n                    rules={[{ required: true, message: '请输入标题' }]}\r\n                  >\r\n                    <Input placeholder=\"输入内容标题\" />\r\n                  </Form.Item>\r\n                  \r\n                  <Form.Item\r\n                    name=\"keywords\"\r\n                    label=\"关键词\"\r\n                    rules={[{ required: true, message: '请输入关键词' }]}\r\n                  >\r\n                    <TextArea placeholder=\"输入关键词，使用逗号分隔\" rows={2} />\r\n                  </Form.Item>\r\n                  \r\n                  <Form.Item\r\n                    name=\"language\"\r\n                    label=\"语言\"\r\n                    rules={[{ required: true, message: '请选择语言' }]}\r\n                  >\r\n                    <Select>\r\n                      {languages.map(lang => (\r\n                        <Option key={lang.value} value={lang.value}>{lang.label}</Option>\r\n                      ))}\r\n                    </Select>\r\n                  </Form.Item>\r\n                  \r\n                  <Form.Item\r\n                    name=\"contentType\"\r\n                    label=\"内容类型\"\r\n                    rules={[{ required: true, message: '请选择内容类型' }]}\r\n                  >\r\n                    <Select>\r\n                      {contentTypes.map(type => (\r\n                        <Option key={type.value} value={type.value}>{type.label}</Option>\r\n                      ))}\r\n                    </Select>\r\n                  </Form.Item>\r\n                  \r\n                  <Form.Item name=\"tone\" label=\"语气\">\r\n                    <Radio.Group>\r\n                      {toneOptions.map(tone => (\r\n                        <Radio key={tone.value} value={tone.value}>{tone.label}</Radio>\r\n                      ))}\r\n                    </Radio.Group>\r\n                  </Form.Item>\r\n                  \r\n                  <Form.Item name=\"length\" label=\"内容长度\">\r\n                    <Slider\r\n                      min={100}\r\n                      max={1000}\r\n                      step={100}\r\n                      marks={{\r\n                        100: '短',\r\n                        500: '中',\r\n                        1000: '长'\r\n                      }}\r\n                    />\r\n                  </Form.Item>\r\n                  \r\n                  <Button type=\"primary\" htmlType=\"submit\" loading={loading} block>\r\n                    生成内容\r\n                  </Button>\r\n                </Form>\r\n              </StyledCard>\r\n            </Col>\r\n            \r\n            <Col xs={24} lg={14}>\r\n              <StyledCard\r\n                title=\"生成结果\"\r\n                extra={generatedContent && (\r\n                  <>\r\n                    <ActionButton icon={<SaveOutlined />} onClick={handleSave}>\r\n                      保存\r\n                    </ActionButton>\r\n                    <ActionButton \r\n                      icon={copiedStatus ? <CheckOutlined /> : <CopyOutlined />} \r\n                      onClick={handleCopy}\r\n                    >\r\n                      {copiedStatus ? '已复制' : '复制'}\r\n                    </ActionButton>\r\n                  </>\r\n                )}\r\n              >\r\n                {loading ? (\r\n                  <div style={{ textAlign: 'center', padding: '40px 0' }}>\r\n                    <Spin tip=\"正在生成内容...\" />\r\n                  </div>\r\n                ) : generatedContent ? (\r\n                  <ContentWrapper>{generatedContent}</ContentWrapper>\r\n                ) : (\r\n                  <div style={{ textAlign: 'center', color: '#999', padding: '40px 0' }}>\r\n                    填写左侧表单并点击\"生成内容\"按钮来创建内容\r\n                  </div>\r\n                )}\r\n              </StyledCard>\r\n            </Col>\r\n          </Row>\r\n        </TabPane>\r\n        \r\n        <TabPane tab=\"历史记录\" key=\"2\" icon={<HistoryOutlined />}>\r\n          <StyledCard title=\"生成历史\">\r\n            <ContentHistory>\r\n              {history.length > 0 ? (\r\n                history.map(item => (\r\n                  <HistoryItem key={item.id} onClick={() => handleHistoryItemClick(item)}>\r\n                    <div>\r\n                      <strong>{item.title}</strong>\r\n                      <Tag color=\"blue\" style={{ marginLeft: 8 }}>\r\n                        {languages.find(l => l.value === item.language)?.label.split(' ')[0] || item.language}\r\n                      </Tag>\r\n                      <Tag color=\"green\">\r\n                        {contentTypes.find(ct => ct.value === item.contentType)?.label || item.contentType}\r\n                      </Tag>\r\n                    </div>\r\n                    <div style={{ margin: '8px 0', color: '#666' }}>\r\n                      {item.content.length > 100 ? `${item.content.substring(0, 100)}...` : item.content}\r\n                    </div>\r\n                    <HistoryDate>{item.createdAt}</HistoryDate>\r\n                  </HistoryItem>\r\n                ))\r\n              ) : (\r\n                <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>\r\n                  没有历史记录\r\n                </div>\r\n              )}\r\n            </ContentHistory>\r\n          </StyledCard>\r\n        </TabPane>\r\n      </Tabs>\r\n    </PageContainer>\r\n  );\r\n}\r\n\r\nexport default ContentAgentPage; "], "names": [], "sourceRoot": ""}