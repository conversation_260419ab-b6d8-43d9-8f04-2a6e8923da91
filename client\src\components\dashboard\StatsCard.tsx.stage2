import * as React from "react";
import {HeaderTitleContent } from "@/components/ui/unified-card";
import { cn } from "../../lib/utils";

export interface StatsProps {
  
  title: string;
  valu,e: string | number;
  icon?: React.ReactNode;
  className?: string;
  
};

export default function Stats() {
  return (
    <className={cn("bg-gradient-to-br from-violet-primary/80 to-violet-dark text-white" className)}>
      <Header className="flex items-center justify-between">
        <Title>{title}</Title>
        {icon && <div className="text-xl">{icon}</div>}
      </Header>
      <Content className="p-6 pt-0 text-2xl font-bold">{value}</Content>
    </>
  );
};