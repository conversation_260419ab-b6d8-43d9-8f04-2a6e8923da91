/**
 * 代理(Agent)相关路由
 * 处理代理的创建、获取、更新和删除
 */
const express = require('express');
const router = express.Router();

/**
 * @route GET /agents
 * @desc 获取所有代理
 * @access 私有
 */
router.get('/', async (req, res) => {
  try {
    // 记录请求
    req.logger?.info('Request to get all agents received', { requestId: req.id });
    
    const supabase = req.app.locals.supabase;
    
    if (!supabase) {
      // 在开发环境中返回模拟数据
      if (process.env.NODE_ENV === 'development') {
        return res.json({
          message: 'success',
          data: [
            {
              id: '1',
              name: 'AI Auto-Reply Agent',
              agentType: 'AI_AUTO_REPLY',
              status: 'ACTIVE',
              description: 'Automatically replies to customer inquiries',
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            },
            {
              id: '2',
              name: 'Content Generator',
              agentType: 'CONTENT_GENERATOR',
              status: 'INACTIVE',
              description: 'Generates marketing content',
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            },
            {
              id: '3',
              name: 'Lead Generation Bot',
              agentType: 'LEAD_GENERATION',
              status: 'ACTIVE',
              description: 'Identifies and qualifies potential leads',
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            }
          ]
        });
      }
      
      return res.status(500).json({
        message: '数据库服务不可用'
      });
    }
    
    // 从Supabase获取所有代理
    const { data: agents, error } = await supabase
      .from('agents')
      .select('*');
    
    if (error) {
      req.logger?.error('Error fetching agents from database', { error, requestId: req.id });
      return res.status(500).json({ 
        message: 'Error fetching agents',
        error: error.message 
      });
    }
    
    // 返回带有 message 和 data 的统一响应结构
    res.json({ message: 'success', data: agents || [] });
  } catch (err) {
    req.logger?.error('Unexpected error in GET /agents', { error: err.message, requestId: req.id });
    res.status(500).json({ message: '服务器错误', error: err.message });
  }
});

/**
 * @route GET /agents/:id
 * @desc 获取单个代理
 * @access 私有
 */
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    // 记录请求
    req.logger?.info(`Request to get agent with ID ${id}`, { id, requestId: req.id });
    
    const supabase = req.app.locals.supabase;
    
    if (!supabase) {
      return res.status(500).json({
        message: '数据库服务不可用'
      });
    }
    
    // 从Supabase获取单个代理
    const { data: agent, error } = await supabase
      .from('agents')
      .select('*')
      .eq('id', id)
      .single();
    
    if (error) {
      req.logger?.error(`Error fetching agent with ID ${id}`, { error, requestId: req.id });
      return res.status(error.code === 'PGRST116' ? 404 : 500).json({ 
        message: error.code === 'PGRST116' ? '代理不存在' : '获取代理失败',
        error: error.message 
      });
    }
    
    // 返回带有 message 和 data 的统一响应结构
    res.json({ message: 'success', data: agent });
  } catch (err) {
    req.logger?.error(`Unexpected error in GET /agents/:id`, { error: err.message, requestId: req.id });
    res.status(500).json({ message: '服务器错误', error: err.message });
  }
});

/**
 * @route GET /agents/:id/settings
 * @desc 获取代理设置
 * @access 私有
 */
router.get('/:id/settings', async (req, res) => {
  const { id } = req.params;
  try {
    req.logger?.info(`Request to get settings for agent ${id}`, { requestId: req.id });
    // 尝试从 agent_settings 表中读取设置
    const supabase = req.app.locals.supabase;
    
    if (!supabase) {
      return res.status(500).json({
        message: '数据库服务不可用'
      });
    }
    
    const { data: settings, error } = await supabase
      .from('agent_settings')
      .select('*')
      .eq('agent_id', id)
      .single();

    if (error) {
      // 如果不存在记录（错误码 PGRST116），则返回空设置
      if (error.code === 'PGRST116') {
        return res.json({ message: 'success', data: {} });
      }
      req.logger?.error(`Error fetching settings for agent ${id}`, { error, requestId: req.id });
      return res.status(500).json({ message: 'Error fetching agent settings', error: error.message });
    }
    res.json({ message: 'success', data: settings || {} });
  } catch (err) {
    req.logger?.error(`Unexpected error in GET /agents/:id/settings`, { error: err.message, requestId: req.id });
    res.status(500).json({ message: '服务器错误', error: err.message });
  }
});

/**
 * @route PUT /agents/:id/settings
 * @desc 更新代理设置并返回更新后的数据
 * @access 私有
 */
router.put('/:id/settings', async (req, res) => {
  const { id } = req.params;
  const settingsUpdate = req.body;
  
  // 立即设置响应超时，防止请求挂起
  const timeoutId = setTimeout(() => {
    if (!res.headersSent) {
      req.logger?.error(`Request timeout in PUT /agents/:id/settings for agent ${id}`);
      res.status(504).json({ message: 'Request timeout', error: 'Operation took too long to complete' });
    }
  }, 15000); // 15秒超时，比客户端超时短
  
  try {
    req.logger?.info(`Request to update settings for agent ${id}`, { 
      requestId: req.id,
      body: JSON.stringify(settingsUpdate)  // 记录请求体
    });
    
    // 设置要保存的数据
    const supabase = req.app.locals.supabase;
    
    if (!supabase) {
      clearTimeout(timeoutId);
      return res.status(500).json({
        message: '数据库服务不可用'
      });
    }
    
    const dataToSave = {
      agent_id: id,
      name: settingsUpdate.name,
      description: settingsUpdate.description,
      active: settingsUpdate.active,
      config: settingsUpdate.config || {},
      updated_at: new Date().toISOString()
    };
    
    // 先检查记录是否存在
    const { data: existingSettings, error: checkError } = await supabase
      .from('agent_settings')
      .select('id')
      .eq('agent_id', id)
      .single();
      
    if (checkError && checkError.code !== 'PGRST116') {
      req.logger?.error(`Error checking agent settings existence: ${checkError.message}`);
      clearTimeout(timeoutId);
      return res.status(500).json({ 
        message: 'Error checking agent settings',
        error: checkError.message
      });
    }
    
    let result;
    
    // 根据检查结果决定是更新还是插入
    if (existingSettings) {
      req.logger?.info(`Updating existing settings for agent ${id}`);
      result = await supabase
        .from('agent_settings')
        .update(dataToSave)
        .eq('agent_id', id)
        .select();
    } else {
      req.logger?.info(`Creating new settings for agent ${id}`);
      result = await supabase
        .from('agent_settings')
        .insert({...dataToSave, created_at: new Date().toISOString()})
        .select();
    }
    
    const { data, error } = result;

    if (error) {
      req.logger?.error(`Error upserting settings for agent ${id}: ${error.message}`, { requestId: req.id });
      clearTimeout(timeoutId);
      return res.status(500).json({ message: '更新设置失败', error: error.message });
    }
    
    // 成功后清除超时
    clearTimeout(timeoutId);
    res.json({ message: '设置更新成功', data: data[0] });

  } catch (err) {
    req.logger?.error(`Unexpected error in PUT /agents/:id/settings: ${err.message}`, { requestId: req.id });
    if (!res.headersSent) {
      clearTimeout(timeoutId);
      res.status(500).json({ message: '服务器错误', error: err.message });
    }
  }
});

/**
 * @route POST /agents
 * @desc 创建新代理
 * @access 私有
 */
router.post('/', async (req, res) => {
  try {
    const { name, agentType, status, description } = req.body;
    
    // 记录请求
    req.logger?.info('Request to create a new agent', { 
      body: req.body,
      requestId: req.id 
    });

    const supabase = req.app.locals.supabase;
    if (!supabase) {
      return res.status(500).json({ message: '数据库服务不可用' });
    }
    
    const { data: newAgent, error } = await supabase
      .from('agents')
      .insert([
        { name, agent_type: agentType, status, description, created_at: new Date(), updated_at: new Date() }
      ])
      .select();

    if (error) {
      req.logger?.error(`Error creating agent: ${error.message}`, { requestId: req.id });
      return res.status(500).json({ message: '创建代理失败', error: error.message });
    }
    
    res.status(201).json({ message: '代理创建成功', data: newAgent[0] });
  } catch (err) {
    req.logger?.error(`Unexpected error in POST /agents: ${err.message}`, { requestId: req.id });
    res.status(500).json({ message: '服务器错误', error: err.message });
  }
});

/**
 * @route PUT /agents/:id
 * @desc 更新代理
 * @access 私有
 */
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, agentType, status, description } = req.body;
    
    req.logger?.info(`Request to update agent ${id}`, {
      body: req.body,
      requestId: req.id
    });

    const supabase = req.app.locals.supabase;
    if (!supabase) {
      return res.status(500).json({ message: '数据库服务不可用' });
    }

    const updates = { name, agent_type: agentType, status, description, updated_at: new Date() };
    
    const { data: updatedAgent, error } = await supabase
      .from('agents')
      .update(updates)
      .eq('id', id)
      .select();
      
    if (error) {
      req.logger?.error(`Error updating agent ${id}: ${error.message}`, { requestId: req.id });
      return res.status(500).json({ message: '更新代理失败', error: error.message });
    }
    
    if (!updatedAgent || updatedAgent.length === 0) {
      return res.status(404).json({ message: '代理不存在' });
    }
    
    res.json({ message: '代理更新成功', data: updatedAgent[0] });
  } catch (err) {
    req.logger?.error(`Unexpected error in PUT /agents/:id: ${err.message}`, { requestId: req.id });
    res.status(500).json({ message: '服务器错误', error: err.message });
  }
});

/**
 * @route DELETE /agents/:id
 * @desc 删除代理
 * @access 私有
 */
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    req.logger?.info(`Request to delete agent ${id}`, { requestId: req.id });

    const supabase = req.app.locals.supabase;
    if (!supabase) {
      return res.status(500).json({ message: '数据库服务不可用' });
    }

    const { error } = await supabase
      .from('agents')
      .delete()
      .eq('id', id);
      
    if (error) {
      req.logger?.error(`Error deleting agent ${id}: ${error.message}`, { requestId: req.id });
      return res.status(500).json({ message: '删除代理失败', error: error.message });
    }
    
    res.status(204).send();
  } catch (err) {
    req.logger?.error(`Unexpected error in DELETE /agents/:id: ${err.message}`, { requestId: req.id });
    res.status(500).json({ message: '服务器错误', error: err.message });
  }
});

/**
 * @route POST /agents/:id/run
 * @desc 运行代理（改变其状态为 'ACTIVE'）
 * @access 私有
 */
router.post('/:id/run', async (req, res) => {
  try {
    const { id } = req.params;

    req.logger?.info(`Request to run agent ${id}`, { requestId: req.id });
    
    const supabase = req.app.locals.supabase;
    if (!supabase) {
      return res.status(500).json({ message: '数据库服务不可用' });
    }

    const { data: updatedAgent, error } = await supabase
      .from('agents')
      .update({ 
          status: 'ACTIVE',
          updated_at: new Date().toISOString() 
        })
      .eq('id', id)
      .select();

    if (error) {
      req.logger?.error(`Error running agent ${id}: ${error.message}`, { requestId: req.id });
      return res.status(500).json({ message: '运行代理失败', error: error.message });
    }

    if (!updatedAgent || updatedAgent.length === 0) {
      return res.status(404).json({ message: '代理不存在' });
    }

    res.json({ message: `代理 ${id} 已成功启动`, data: updatedAgent[0] });
  } catch (err) {
    req.logger?.error(`Unexpected error in POST /agents/:id/run: ${err.message}`, { requestId: req.id });
    res.status(500).json({ message: '服务器错误', error: err.message });
  }
});

/**
 * @route GET /agents/:id/rules
 * @desc 获取代理的所有规则
 * @access 私有
 */
router.get('/:id/rules', async (req, res) => {
  try {
    const { id } = req.params;
    req.logger?.info(`Request to get rules for agent ${id}`, { requestId: req.id });

    const supabase = req.app.locals.supabase;
    if (!supabase) {
      return res.status(500).json({ message: '数据库服务不可用' });
    }

    const { data, error } = await supabase
      .from('agent_rules')
      .select('*')
      .eq('agent_id', id);

    if (error) {
      req.logger?.error(`Error fetching rules for agent ${id}: ${error.message}`, { requestId: req.id });
      return res.status(500).json({ message: '获取规则失败', error: error.message });
    }

    res.json({ message: 'success', data });
  } catch (err) {
    req.logger?.error(`Unexpected error in GET /agents/:id/rules: ${err.message}`, { requestId: req.id });
    res.status(500).json({ message: '服务器错误', error: err.message });
  }
});

/**
 * @route POST /agents/:id/rules
 * @desc 为代理添加新规则
 * @access 私有
 */
router.post('/:id/rules', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, condition, action, priority, enabled } = req.body;
    
    req.logger?.info(`Request to add rule for agent ${id}`, { body: req.body, requestId: req.id });

    const supabase = req.app.locals.supabase;
    if (!supabase) {
      return res.status(500).json({ message: '数据库服务不可用' });
    }
    
    const ruleData = { agent_id: id, name, condition, action, priority, enabled };

    const { data, error } = await supabase
      .from('agent_rules')
      .insert(ruleData)
      .select();

    if (error) {
      req.logger?.error(`Error adding rule to agent ${id}: ${error.message}`, { requestId: req.id });
      return res.status(500).json({ message: '添加规则失败', error: error.message });
    }
    
    res.status(201).json({ message: '规则添加成功', data: data[0] });
  } catch (err) {
    req.logger?.error(`Unexpected error in POST /agents/:id/rules: ${err.message}`, { requestId: req.id });
    res.status(500).json({ message: '服务器错误', error: err.message });
  }
});

/**
 * @route PUT /agents/:id/rules/:ruleId
 * @desc 更新代理的特定规则
 * @access 私有
 */
router.put('/:id/rules/:ruleId', async (req, res) => {
  try {
    const { id, ruleId } = req.params;
    const { name, condition, action, priority, enabled } = req.body;
    
    req.logger?.info(`Request to update rule ${ruleId} for agent ${id}`, { body: req.body, requestId: req.id });
    
    const supabase = req.app.locals.supabase;
    if (!supabase) {
      return res.status(500).json({ message: '数据库服务不可用' });
    }
    
    const updates = { name, condition, action, priority, enabled };

    const { data, error } = await supabase
      .from('agent_rules')
      .update(updates)
      .eq('id', ruleId)
      .eq('agent_id', id)
      .select();
  
    if (error) {
      req.logger?.error(`Error updating rule ${ruleId} for agent ${id}: ${error.message}`, { requestId: req.id });
      return res.status(500).json({ message: '更新规则失败', error: error.message });
    }
  
    if (!data || data.length === 0) {
      return res.status(404).json({ message: '规则不存在' });
    }
    
    res.json({ message: '规则更新成功', data: data[0] });
  } catch (err) {
    req.logger?.error(`Unexpected error in PUT /agents/:id/rules/:ruleId: ${err.message}`, { requestId: req.id });
    res.status(500).json({ message: '服务器错误', error: err.message });
  }
});

/**
 * @route DELETE /agents/:id/rules/:ruleId
 * @desc 删除代理的特定规则
 * @access 私有
 */
router.delete('/:id/rules/:ruleId', async (req, res) => {
  try {
    const { id, ruleId } = req.params;
    req.logger?.info(`Request to delete rule ${ruleId} for agent ${id}`, { requestId: req.id });

    const supabase = req.app.locals.supabase;
    if (!supabase) {
      return res.status(500).json({ message: '数据库服务不可用' });
    }
    
    const { error } = await supabase
      .from('agent_rules')
      .delete()
      .eq('id', ruleId)
      .eq('agent_id', id);
  
    if (error) {
      req.logger?.error(`Error deleting rule ${ruleId} for agent ${id}: ${error.message}`, { requestId: req.id });
      return res.status(500).json({ message: '删除规则失败', error: error.message });
    }
  
    res.status(204).send();
  } catch (err) {
    req.logger?.error(`Unexpected error in DELETE /agents/:id/rules/:ruleId: ${err.message}`, { requestId: req.id });
    res.status(500).json({ message: '服务器错误', error: err.message });
  }
});

/**
 * @route GET /agents/:id/logs
 * @desc 获取代理的日志
 * @access 私有
 */
router.get('/:id/logs', async (req, res) => {
  try {
    const { id } = req.params;
    const { page = 1, limit = 20 } = req.query;
    req.logger?.info(`Request to get logs for agent ${id}`, { query: req.query, requestId: req.id });

    const supabase = req.app.locals.supabase;
    if (!supabase) {
      return res.status(500).json({ message: '数据库服务不可用' });
    }
    
    const { data, error, count } = await supabase
      .from('agent_logs')
      .select('*', { count: 'exact' })
      .eq('agent_id', id)
      .range((page - 1) * limit, page * limit - 1)
      .order('timestamp', { ascending: false });
  
    if (error) {
      req.logger?.error(`Error fetching logs for agent ${id}: ${error.message}`, { requestId: req.id });
      return res.status(500).json({ message: '获取日志失败', error: error.message });
    }
  
    res.json({
      message: 'success',
      data,
      pagination: {
        total: count,
        page: Number(page),
        limit: Number(limit)
      }
    });
  } catch (err) {
    req.logger?.error(`Unexpected error in GET /agents/:id/logs: ${err.message}`, { requestId: req.id });
    res.status(500).json({ message: '服务器错误', error: err.message });
  }
});

/**
 * @route POST /agents/:id/data
 * @desc 上传代理相关数据
 * @access 私有
 */
router.post('/:id/data', async (req, res) => {
  try {
    const { id } = req.params;
    
    req.logger?.info(`Request to upload data for agent ${id}`, { body: req.body, requestId: req.id });

    if (!Array.isArray(req.body)) {
      return res.status(400).json({ message: '请求体必须是JSON数组' });
    }

    const supabase = req.app.locals.supabase;
    if (!supabase) {
      return res.status(500).json({ message: '数据库服务不可用' });
    }

    const dataEntries = req.body.map(item => ({
      agent_id: id,
      key: item.key,
      value: item.value,
      timestamp: item.timestamp || new Date().toISOString()
    }));
    
    const { data, error } = await supabase
      .from('agent_data')
      .insert(dataEntries)
      .select();
      
    if (error) {
      req.logger?.error(`Error uploading data for agent ${id}: ${error.message}`, { requestId: req.id });
      return res.status(500).json({ message: '上传数据失败', error: error.message });
    }
    
    res.status(201).json({ message: '数据上传成功', data });
  } catch (err) {
    req.logger?.error(`Unexpected error in POST /agents/:id/data: ${err.message}`, { requestId: req.id });
    res.status(500).json({ message: '服务器错误', error: err.message });
  }
});

/**
 * @route GET /agents/:id/data
 * @desc 获取代理相关数据
 * @access 私有
 */
router.get('/:id/data', async (req, res) => {
  try {
    const { id } = req.params;
    const { startDate, metric } = req.query;
    
    req.logger?.info(`Request to get data for agent ${id}`, { query: req.query, requestId: req.id });

    const supabase = req.app.locals.supabase;
    if (!supabase) {
      return res.status(500).json({ message: '数据库服务不可用' });
    }

    let query = supabase
      .from('agent_data')
      .select('*')
      .eq('agent_id', id);
      
    // 如果提供了时间范围参数，添加时间过滤
    if (startDate) {
      try {
        const date = new Date(startDate);
        if (isNaN(date.getTime())) {
          throw new Error('Invalid date format');
        }
        query = query.gte('timestamp', date.toISOString());
      } catch (e) {
        return res.status(400).json({ message: '无效的开始日期格式', error: e.message });
      }
    }
      
    // 如果提供了度量指标参数，按照指标过滤
    if (metric) {
      query = query.eq('key', metric);
    }
      
    const { data, error } = await query;
    
    if (error) {
      req.logger?.error(`Error fetching data for agent ${id}: ${error.message}`, { requestId: req.id });
      return res.status(500).json({ message: '获取数据失败', error: error.message });
    }
    
    res.json({ message: 'success', data });
  } catch (err) {
    req.logger?.error(`Unexpected error in GET /agents/:id/data`, { error: err.message, requestId: req.id });
    res.status(500).json({ message: '服务器错误', error: err.message });
  }
});

module.exports = router; 