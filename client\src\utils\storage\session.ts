/**
 * 会话存储工具函数
 */

/**
 * 存储数据到 sessionStorage
 */
export function setItem<T>(key: string, value: T): void {
  try {
    const serializedValue = JSON.stringify(value);
    sessionStorage.setItem(key, serializedValue);
  } catch (error) {
    console.error(`Error storing to sessionStorage: ${error}`);
  }
}

/**
 * 从 sessionStorage 获取数据
 */
export function getItem<T>(key: string, defaultValue: T): T {
  try {
    const item = sessionStorage.getItem(key);
    if (item === null) {
      return defaultValue;
    }
    return JSON.parse(item);
  } catch (error) {
    console.error(`Error getting from sessionStorage: ${error}`);
    return defaultValue;
  }
}

/**
 * 从 sessionStorage 删除数据
 */
export function removeItem(key: string): void {
  try {
    sessionStorage.removeItem(key);
  } catch (error) {
    console.error(`Error removing from sessionStorage: ${error}`);
  }
}

/**
 * 清空 sessionStorage
 */
export function clear(): void {
  try {
    sessionStorage.clear();
  } catch (error) {
    console.error(`Error clearing sessionStorage: ${error}`);
  }
}

/**
 * 检查 sessionStorage 是否可用
 */
export function isAvailable(): boolean {
  try {
    const testKey = '__sessionStorage_test__';
    sessionStorage.setItem(testKey, 'test');
    sessionStorage.removeItem(testKey);
    return true;
  } catch {
    return false;
  }
}

/**
 * 获取 sessionStorage 中所有的键
 */
export function getAllKeys(): string[] {
  const keys: string[] = [];
  for (let i = 0; i < sessionStorage.length; i++) {
    const key = sessionStorage.key(i);
    if (key) {
      keys.push(key);
    }
  }
  return keys;
}

/**
 * 检查键是否存在
 */
export function hasKey(key: string): boolean {
  return sessionStorage.getItem(key) !== null;
}
