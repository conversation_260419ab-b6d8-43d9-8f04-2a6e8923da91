/**
 * Supabase客户端配置
 * 为Core服务提供Supabase数据库连接
 */
const { createClient } = require('@supabase/supabase-js');

// 获取环境变量
const SUPABASE_URL = process.env.SUPABASE_URL;
// 从环境变量读取服务角色密钥，兼容 SUPABASE_SERVICE_KEY 或 SUPABASE_SERVICE_ROLE_KEY
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_KEY || process.env.SUPABASE_SERVICE_ROLE_KEY;

// 创建Supabase客户端
let supabase = null;

if (SUPABASE_URL && SUPABASE_SERVICE_KEY) {
  try {
    supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY, {
      auth: {
        autoRefreshToken: true,
        persistSession: false
      }
    });
    console.log('Supabase客户端初始化成功');
  } catch (error) {
    console.error('Supabase客户端初始化失败:', error.message);
  }
} else {
  console.error('缺少Supabase配置。请确保环境变量已正确设置:', {
    hasUrl: !!SUPABASE_URL,
    hasServiceKey: !!SUPABASE_SERVICE_KEY
  });
}

/**
 * 检查Supabase连接
 * @returns {Promise<boolean>}
 */
const checkConnection = async () => {
  if (!supabase) {
    return false;
  }
  
  try {
    // 执行简单查询来测试连接
    const { data, error } = await supabase.from('agents').select('id').limit(1);
    
    if (error) {
      console.error('Supabase连接测试失败:', error.message);
      return false;
    }
    
    return true;
  } catch (err) {
    console.error('Supabase连接测试异常:', err.message);
    return false;
  }
};

// 导出Supabase客户端实例
module.exports = supabase; 