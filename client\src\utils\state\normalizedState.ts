/**
 * 规范化状态管理工具
 * 提供统一的数据结构和操作方法
 */

export interface NormalizedEntity {
  id: string | number;
  [key: string]: any;
}

export interface NormalizedState<T extends NormalizedEntity> {
  byId: Record<string | number, T>;
  allIds: (string | number)[];
  loading: boolean;
  error: string | null;
  lastUpdated: number | null;
}

export const createInitialNormalizedState = <T extends NormalizedEntity>(): NormalizedState<T> => ({
  byId: {},
  allIds: [],
  loading: false,
  error: null,
  lastUpdated: null
});

export const normalizeData = <T extends NormalizedEntity>(
  data: T[]
): { byId: Record<string | number, T>; allIds: (string | number)[] } => {
  const byId: Record<string | number, T> = {};
  const allIds: (string | number)[] = [];

  data.forEach(item => {
    byId[item.id] = item;
    allIds.push(item.id);
  });

  return { byId, allIds };
};

export const denormalizeData = <T extends NormalizedEntity>(
  state: NormalizedState<T>
): T[] => {
  return state.allIds.map(id => state.byId[id]).filter(Boolean);
};

// 工具函数：检查数据是否需要刷新
export const shouldRefresh = (
  lastUpdated: number | null,
  ttlMs: number = 5 * 60 * 1000 // 5分钟默认TTL
): boolean => {
  if (!lastUpdated) return true;
  return Date.now() - lastUpdated > ttlMs;
};

// 工具函数：批量更新
export const batchUpdate = <T extends NormalizedEntity>(
  state: NormalizedState<T>,
  updates: Array<Partial<T> & { id: string | number }>
) => {
  updates.forEach(update => {
    const { id, ...data } = update;
    if (state.byId[id]) {
      state.byId[id] = { ...state.byId[id], ...data };
    }
  });
  state.lastUpdated = Date.now();
};

// 工具函数：合并数据（用于增量更新）
export const mergeData = <T extends NormalizedEntity>(
  state: NormalizedState<T>,
  newData: T[],
  replaceExisting: boolean = false
) => {
  if (replaceExisting) {
    const { byId, allIds } = normalizeData(newData);
    state.byId = byId;
    state.allIds = allIds;
  } else {
    newData.forEach(item => {
      if (!state.allIds.includes(item.id)) {
        state.allIds.push(item.id);
      }
      state.byId[item.id] = item;
    });
  }
  state.lastUpdated = Date.now();
};

const normalizedState = {
  createInitialNormalizedState,
  normalizeData,
  denormalizeData,
  shouldRefresh,
  batchUpdate,
  mergeData
};

export default normalizedState;
