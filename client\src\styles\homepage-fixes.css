/* 🛠️ Homepage 样式冲突修复 */

/* 修复NextUI与自定义样式的冲突 */
.homepage-container {
  /* 确保背景渐变不被覆盖 */
  background: linear-gradient(135deg, 
    hsl(var(--background)) 0%,
    hsl(var(--accent)) 50%,
    hsl(var(--background)) 100%) !important;
  
  /* 防止内容被截断 */
  overflow-x: hidden;
  overflow-y: auto;
}

/* 修复Hero Section背景效果 */
.homepage-hero {
  position: relative;
  background: transparent !important;
}

.homepage-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(249, 250, 251, 0.8) 0%,
    rgba(243, 244, 246, 0.6) 50%,
    rgba(249, 250, 251, 0.8) 100%);
  pointer-events: none;
  z-index: -1;
}

/* 修复Feature卡片样式被覆盖 */
.homepage-feature-card {
  /* 重置NextUI默认样式 */
  background: white !important;
  border: 1px solid hsl(var(--border)) !important;
  border-radius: 12px !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.homepage-feature-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
  border-color: hsl(var(--ring)) !important;
  transform: translateY(-2px) !important;
}

/* 修复按钮样式冲突 */
.homepage-cta-button {
  /* 确保渐变背景显示 */
  background: linear-gradient(135deg, 
    hsl(260, 60%, 65%) 0%,
    hsl(260, 54%, 58%) 100%) !important;
  border: none !important;
  color: white !important;
  
  /* 防止NextUI覆盖 */
  --nextui-default-hover-color: transparent !important;
  --nextui-default-color: transparent !important;
}

/* 修复统计卡片动画被阻止 */
.homepage-stat-card {
  will-change: transform !important;
  transform: translate3d(0, 0, 0) !important;
  backface-visibility: hidden !important;
}

/* 修复背景装饰元素被隐藏 */
.homepage-bg-decoration {
  position: absolute !important;
  z-index: -1 !important;
  pointer-events: none !important;
  opacity: 0.6 !important;
  
  /* 强制显示渐变 */
  background: conic-gradient(from 45deg,
    rgba(147, 51, 234, 0.15) 0deg,
    rgba(168, 85, 247, 0.1) 120deg,
    rgba(139, 92, 246, 0.15) 240deg,
    rgba(147, 51, 234, 0.1) 360deg) !important;
  
  filter: blur(3rem) !important;
  border-radius: 50% !important;
}

/* 修复Framer Motion样式被重置 */
[data-framer-appear-id] {
  will-change: transform, opacity !important;
}

/* 修复响应式断点问题 */
@media (max-width: 640px) {
  .homepage-container {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }
  
  .homepage-hero {
    padding-top: 2rem !important;
    padding-bottom: 2rem !important;
  }
}

/* 修复z-index层级问题 */
.homepage-navbar {
  z-index: 50 !important;
  position: sticky !important;
  top: 0 !important;
}

.homepage-back-to-top {
  z-index: 40 !important;
  position: fixed !important;
}

/* 确保渐变文字效果显示 */
.homepage-gradient-text {
  background: linear-gradient(135deg, 
    hsl(260, 60%, 65%) 0%,
    hsl(260, 54%, 58%) 50%,
    hsl(340, 75%, 65%) 100%) !important;
  -webkit-background-clip: text !important;
  background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  color: transparent !important;
  
  /* 确保在所有浏览器中正常显示 */
  background-attachment: fixed;
}

/* 🎭 主页特定修复和优化 */

/* 导航栏高度修复 */
.homepage-navbar {
  height: 80px !important;
  min-height: 80px !important;
}

/* NextUI Navbar wrapper 高度修复 */
nav[data-slot="base"] {
  height: 80px !important;
  min-height: 80px !important;
}

/* Logo容器优化 */
.homepage-logo-container {
  display: flex !important;
  align-items: center !important;
  height: 100% !important;
  padding: 8px 0 !important;
}

/* Logo图片优化 - 强制显示 */
.homepage-logo {
  max-height: 48px !important;
  height: auto !important;
  width: auto !important;
  object-fit: contain !important;
  opacity: 1 !important; /* 强制覆盖任何opacity设置 */
  visibility: visible !important;
  display: block !important;
}

/* 确保NextUI Image组件显示 */
img.homepage-logo,
.homepage-logo img {
  opacity: 1 !important;
  visibility: visible !important;
  display: block !important;
}

/* NextUI Image组件特定修复 */
[data-loaded="true"].homepage-logo {
  opacity: 1 !important;
  transition: opacity 0.3s ease-in-out !important;
}

/* 确保导航栏内容垂直居中 */
[data-slot="wrapper"] {
  align-items: center !important;
  height: 100% !important;
}

/* 移动端导航栏调整 */
@media (max-width: 640px) {
  .homepage-navbar {
    height: 70px !important;
    min-height: 70px !important;
  }
  
  .homepage-logo {
    max-height: 40px !important;
  }
}

/* 2025年现代化元素 */
.modern-glassmorphism {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
}

.modern-gradient-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.modern-glow-effect {
  box-shadow: 0 0 20px rgba(147, 51, 234, 0.3);
  transition: box-shadow 0.3s ease-in-out;
}

.modern-glow-effect:hover {
  box-shadow: 0 0 30px rgba(147, 51, 234, 0.5);
}

/* Pricing Section 现代化样式 */
.pricing-card-enhanced {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease-in-out;
}

.pricing-card-enhanced:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

/* Switch Toggle 现代化 */
.modern-switch {
  background: rgba(255, 255, 255, 0.2) !important;
  backdrop-filter: blur(10px) !important;
  border: 2px solid rgba(255, 255, 255, 0.3) !important;
  transition: all 0.3s ease-in-out !important;
}

.modern-switch[data-selected="true"] {
  background: linear-gradient(135deg, #9333ea, #a855f7) !important;
  box-shadow: 0 4px 20px rgba(147, 51, 234, 0.4) !important;
}

/* Icon 动画增强 */
.icon-float-animation {
  animation: iconFloat 6s ease-in-out infinite;
}

@keyframes iconFloat {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  25% { transform: translateY(-10px) rotate(2deg); }
  50% { transform: translateY(-5px) rotate(-1deg); }
  75% { transform: translateY(-15px) rotate(1deg); }
}

.icon-pulse-glow {
  animation: iconPulse 3s ease-in-out infinite;
}

@keyframes iconPulse {
  0%, 100% { 
    filter: drop-shadow(0 0 5px rgba(147, 51, 234, 0.3)); 
    transform: scale(1);
  }
  50% { 
    filter: drop-shadow(0 0 15px rgba(147, 51, 234, 0.6)); 
    transform: scale(1.05);
  }
}

/* 卡片 hover 特效增强 */
.card-hover-effect {
  position: relative;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.card-hover-effect::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease-in-out;
  z-index: 1;
}

.card-hover-effect:hover::before {
  left: 100%;
}

/* 按钮现代化样式 */
.btn-modern-primary {
  background: linear-gradient(135deg, #9333ea, #a855f7);
  border: none;
  box-shadow: 0 4px 15px rgba(147, 51, 234, 0.3);
  transition: all 0.3s ease-in-out;
}

.btn-modern-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(147, 51, 234, 0.4);
}

/* 性能优化 */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

/* 调试样式 - 可以临时启用来检查logo */
.debug-logo {
  outline: 2px solid red !important;
  background-color: yellow !important;
  opacity: 1 !important;
}

/* 确保任何带有 opacity-0 的元素如果是logo都显示 */
.homepage-logo.opacity-0 {
  opacity: 1 !important;
}

/* NextUI特定修复 */
.nextui-image.homepage-logo {
  opacity: 1 !important;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .pricing-card-enhanced {
    margin-bottom: 2rem;
  }
  
  .modern-switch {
    transform: scale(0.9);
  }
}

@media (min-width: 1920px) {
  .homepage-logo {
    max-height: 56px !important;
  }
}

/* 🎨 Enhanced 2025 Modern Design Styles */

/* Clean glassmorphism effects for cards */
.pricing-card-modern {
  backdrop-filter: blur(20px) saturate(180%);
  -webkit-backdrop-filter: blur(20px) saturate(180%);
  background: rgba(255, 255, 255, 0.72);
  border: 1px solid rgba(255, 255, 255, 0.125);
  border-radius: 16px;
}

/* Subtle animations for better performance */
.pricing-card-modern:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Clean feature item hover effects */
.feature-item {
  transition: all 0.2s ease-in-out;
}

.feature-item:hover {
  transform: translateX(4px);
  color: #9333ea;
}

/* Improved button hover states */
.modern-cta-button {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.modern-cta-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.modern-cta-button:hover::before {
  left: 100%;
}

/* Clean metric cards */
.metric-card {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.4);
  transition: all 0.2s ease;
}

.metric-card:hover {
  transform: translateY(-2px) scale(1.05);
  background: rgba(255, 255, 255, 0.8);
  box-shadow: 0 4px 12px rgba(147, 51, 234, 0.15);
}

/* Platform integration grid improvements */
.platform-grid-item {
  transition: all 0.2s ease;
  cursor: pointer;
}

.platform-grid-item:hover {
  transform: translateY(-2px);
  background: rgba(147, 51, 234, 0.05);
  border-color: rgba(147, 51, 234, 0.2);
}

.platform-grid-item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.platform-grid-item.disabled:hover {
  transform: none;
  background: rgba(156, 163, 175, 0.05);
}

/* Enhanced expandable section */
.expandable-section {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.expandable-content {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.4);
}

/* Clean toggle button improvements */
.toggle-button {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.5);
  transition: all 0.3s ease;
}

.toggle-button:hover {
  background: rgba(255, 255, 255, 0.6);
  transform: scale(1.01);
}

/* Responsive design improvements */
@media (max-width: 768px) {
  .pricing-card-modern {
    margin: 0 8px;
    min-height: 580px !important;
  }
  
  .metric-card {
    padding: 8px;
  }
  
  .platform-grid-item {
    padding: 8px;
    font-size: 10px;
  }
}

@media (max-width: 640px) {
  .pricing-card-modern {
    min-height: 520px !important;
  }
}

/* Dark mode support for future enhancement */
@media (prefers-color-scheme: dark) {
  .pricing-card-modern {
    background: rgba(30, 30, 30, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .metric-card {
    background: rgba(30, 30, 30, 0.6);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }
}

/* 🔧 修复hover-lift白色背景问题 */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease !important;
}

.hover-lift:hover {
  transform: translateY(-5px) !important;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1) !important;
  background: transparent !important;
}

/* 🔧 修复Sign In按钮hover颜色问题 */
.navbar-signin-button {
  color: #6b7280 !important;
  background: transparent !important;
  border: none !important;
}

.navbar-signin-button:hover {
  color: #9333ea !important;
  background: #f9fafb !important;
}

/* 确保NextUI Button组件的Sign In按钮样式 */
[data-component="button"][data-variant="light"]:hover {
  background-color: #f9fafb !important;
  color: #9333ea !important;
}

/* NextUI Button component override for Sign In */
.nextui-button-light:hover {
  background-color: #f9fafb !important;
  color: #9333ea !important;
}

/* 强制覆盖任何conflicting styles */
button[class*="text-gray-600"]:hover {
  color: #9333ea !important;
} 