// 🎨 Data Insight 页面统一主题配置
export const DATA_INSIGHT_THEME = {
  colors: {
    primary: '#9F7AEA',
    primaryHover: '#8B5CF6',
    primaryForeground: '#FFFFFF',
    secondary: '#C7B8E8',
    background: '#FFFFFF',
    foreground: '#1A202C',
    muted: '#F7F5FF',
    mutedForeground: '#718096',
    border: '#E5D9F5',
    success: '#22C55E',
    warning: '#F59E0B',
    error: '#EF4444',
    info: '#3B82F6',
  },
  
  chartColors: {
    primary: [
      '#9F7AEA', '#8B5CF6', '#A78BFA', '#C084FC'
    ],
    secondary: [
      '#22C55E', '#3B82F6', '#F59E0B', '#EF4444'
    ],
    multiData: [
      '#9F7AEA', '#8B5CF6', '#A78BFA', '#C084FC',
      '#22C55E', '#3B82F6', '#F59E0B', '#EF4444'
    ]
  },
  
  typography: {
    fontFamily: {
      sans: ['Inter', 'ui-sans-serif', 'system-ui', 'sans-serif'],
      mono: ['JetBrains Mono', 'ui-monospace', 'monospace']
    }
  }
};

export function getChartColor(index: number): string {
  const colors = DATA_INSIGHT_THEME.chartColors.multiData;
  return colors[index % colors.length];
}
