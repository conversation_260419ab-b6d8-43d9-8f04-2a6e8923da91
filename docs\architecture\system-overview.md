# iBuddy2 系统概览

iBuddy2 是一个基于微服务架构的智能聊天助手系统，支持多平台、多模型AI交互，具有上下文感知和意图识别能力。本文档提供系统的整体架构设计和主要组件概览。

## 系统架构图

```
ibuddy2/
├── client/               # React前端应用
├── api-gateway/          # API网关服务
├── core-service/         # 核心业务服务
├── ai-service/           # AI和自动回复服务
└── shared/               # 共享代码和配置
```

## 服务组件

### 1. 前端应用 (client)

前端应用是基于React开发的单页面应用程序，提供用户界面和交互体验。

**主要特性**:
- 响应式设计，支持桌面和移动设备
- 基于组件的UI架构
- 状态管理和API集成
- 多语言支持

### 2. API网关 (api-gateway)

API网关是系统的入口点，负责统一的请求处理和路由。

**主要功能**:
- 路由请求到相应的微服务
- 处理认证和授权
- 请求限流和监控
- 跨域资源共享(CORS)处理

### 3. 核心服务 (core-service)

核心服务处理业务逻辑和数据访问。

**主要功能**:
- 用户管理和认证
- 会话管理
- 数据库访问和持久化
- 业务规则实施

### 4. AI服务 (ai-service)

AI服务是系统的核心组件，负责处理所有AI相关的功能。

**主要功能**:
- 多模型支持 (Gemini 2.0, GPT等)
- 智能模型选择
- 意图识别
- 上下文管理
- 响应格式化

### 5. 共享服务 (shared)

共享服务提供了跨微服务使用的共同功能和工具。

**主要组件**:
- 通用工具函数
- 共享类型定义
- 消息队列集成
- 共享配置

## 关键特性

- **前后端完全分离**：独立部署的React前端和Express后端
- **API网关**：统一的请求路由、认证和监控
- **多模型AI策略**：支持多个AI模型，根据需求智能选择
- **多级上下文存储**：短期内存、中期记忆和长期知识存储
- **意图映射网络**：基于关系图的意图识别和上下文感知推理
- **多模态响应**：支持丰富的响应格式（文本、图片、表格、按钮等）
- **跨平台支持**：Web、移动、WhatsApp、Telegram等平台的专用响应格式
- **消息队列集成**：使用RabbitMQ实现服务间异步通信

## 数据流

1. 用户通过前端或第三方平台发送消息
2. 请求经由API网关验证身份
3. 核心服务处理业务逻辑和会话管理
4. AI服务进行意图识别和上下文处理
5. 选择合适的AI模型生成响应
6. 响应通过API网关返回给用户

## 技术栈

- **前端**: React, Tailwind CSS, Axios
- **API网关**: Express, Passport
- **核心服务**: Express, Supabase
- **AI服务**: Express, Google Gemini API, OpenAI API
- **消息队列**: RabbitMQ
- **缓存**: Redis 