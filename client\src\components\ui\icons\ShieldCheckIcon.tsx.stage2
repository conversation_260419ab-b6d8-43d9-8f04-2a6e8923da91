import React from 'react';

interface ShieldCheckIconProps {
  
  className?: string;
  size?: number;
  
};

export const ShieldCheckIcon: React.FC<ShieldCheckIconProps> = ({ 
  className = '' 
  size = 20 
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
      aria-hidden="true"
    >
      <path d="M20 13c0 5-3.5 7.5-8 9-4.5-1.5-8-4-8-9V6l8-3 8 3v7Z"/>
      <path d="m9 12 2 2 4-4"/>
    </svg>
  );
}; 