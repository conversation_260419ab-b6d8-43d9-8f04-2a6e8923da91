/**
 * 环境变量设置工具
 * 自动复制环境变量示例文件到各微服务目录，并提示用户配置必要的值
 */
const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// 微服务目录列表
const services = [
  { name: 'api-gateway', port: 3000 },
  { name: 'core-service', port: 3001 },
  { name: 'ai-service', port: 3002 }
];

// 复制环境变量示例文件
const copyEnvFiles = () => {
  console.log('准备复制环境变量示例文件...');
  
  services.forEach(service => {
    const srcPath = path.join(__dirname, '..', service.name, 'env.example');
    const destPath = path.join(__dirname, '..', service.name, '.env');
    
    try {
      if (fs.existsSync(srcPath)) {
        // 如果目标文件已存在，先检查是否需要覆盖
        if (fs.existsSync(destPath)) {
          console.log(`警告: ${service.name}/.env 文件已存在，跳过复制`);
          return;
        }
        
        let envContent = fs.readFileSync(srcPath, 'utf8');
        
        // 替换默认端口
        envContent = envContent.replace(/PORT=\d+/g, `PORT=${service.port}`);
        
        fs.writeFileSync(destPath, envContent);
        console.log(`✅ 已创建 ${service.name}/.env`);
      } else {
        console.log(`❌ 找不到 ${service.name}/env.example 文件`);
      }
    } catch (error) {
      console.error(`❌ 复制 ${service.name} 环境文件时出错:`, error.message);
    }
  });
};

// 询问用户配置值
const promptForValues = () => {
  const prompts = [
    { 
      key: 'SUPABASE_URL', 
      message: '请输入Supabase URL (或按Enter跳过):',
      services: ['core-service', 'ai-service']
    },
    { 
      key: 'SUPABASE_ANON_KEY', 
      message: '请输入Supabase Anon Key (或按Enter跳过):',
      services: ['core-service', 'ai-service'] 
    },
    { 
      key: 'SUPABASE_SERVICE_ROLE_KEY', 
      message: '请输入Supabase Service Role Key (或按Enter跳过):',
      services: ['core-service', 'ai-service'] 
    },
    { 
      key: 'GEMINI_API_KEY', 
      message: '请输入Google Gemini API Key (或按Enter跳过):',
      services: ['ai-service'] 
    },
    { 
      key: 'JWT_SECRET', 
      message: '请输入JWT密钥 (推荐使用复杂的随机字符串，或按Enter自动生成):',
      services: ['api-gateway', 'core-service', 'ai-service'],
      generate: () => require('crypto').randomBytes(32).toString('hex')
    }
  ];

  const answers = {};
  let currentPrompt = 0;

  const askQuestion = () => {
    if (currentPrompt >= prompts.length) {
      updateEnvFiles(answers);
      rl.close();
      return;
    }

    const prompt = prompts[currentPrompt];
    rl.question(prompt.message + ' ', (answer) => {
      if (!answer && prompt.generate) {
        answer = prompt.generate();
        console.log(`自动生成的值: ${answer}`);
      }
      
      answers[prompt.key] = answer;
      currentPrompt++;
      askQuestion();
    });
  };

  askQuestion();
};

// 更新环境变量文件
const updateEnvFiles = (values) => {
  console.log('\n正在更新环境变量文件...');
  
  services.forEach(service => {
    const envPath = path.join(__dirname, '..', service.name, '.env');
    
    try {
      if (fs.existsSync(envPath)) {
        let envContent = fs.readFileSync(envPath, 'utf8');
        
        // 遍历用户提供的值
        Object.entries(values).forEach(([key, value]) => {
          // 检查此服务是否需要此变量
          const prompt = prompts.find(p => p.key === key);
          if (prompt && prompt.services.includes(service.name) && value) {
            // 使用正则表达式替换变量值
            const regex = new RegExp(`${key}=.*`, 'g');
            if (envContent.match(regex)) {
              envContent = envContent.replace(regex, `${key}=${value}`);
            } else {
              // 如果变量不存在，添加到文件末尾
              envContent += `\n${key}=${value}`;
            }
          }
        });
        
        fs.writeFileSync(envPath, envContent);
        console.log(`✅ 已更新 ${service.name}/.env`);
      }
    } catch (error) {
      console.error(`❌ 更新 ${service.name} 环境文件时出错:`, error.message);
    }
  });
  
  console.log('\n环境设置完成!');
  console.log('你可以使用以下命令启动开发服务器:');
  console.log('npm run dev');
};

// 主函数
const main = () => {
  console.log('========================================');
  console.log('       iBuddy2 环境设置工具');
  console.log('========================================\n');
  
  copyEnvFiles();
  console.log('');
  promptForValues();
};

// 执行主函数
main(); 