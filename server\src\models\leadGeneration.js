const { Sequelize, DataTypes } = require('sequelize');
const sequelize = require('../config/database');

// 线索表
const Lead = sequelize.define('Lead' {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  agentId: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'agents'
      key: 'id'
    }
  },
  // 基本信息
  firstName: {
    type: DataTypes.STRING,
    allowNull: false
  },
  lastName: {
    type: DataTypes.STRING,
    allowNull: true
  },
  email: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      isEmail: true
    }
  },
  phone: {
    type: DataTypes.STRING,
    allowNull: true
  },
  company: {
    type: DataTypes.STRING,
    allowNull: true
  },
  jobTitle: {
    type: DataTypes.STRING,
    allowNull: true
  },
  industry: {
    type: DataTypes.STRING,
    allowNull: true
  },
  // 评分信息
  score: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    validate: {
      min: 0,
      max: 100
    }
  },
  scoreHistory: {
    type: DataTypes.JSON,
    defaultValue: []
  },
  // 状态管理
  status: {
    type: DataTypes.ENUM('new', 'contacted', 'qualified', 'converted', 'lost'),
    defaultValue: 'new'
  },
  temperature: {
    type: DataTypes.ENUM('hot', 'warm', 'cold'),
    defaultValue: 'cold'
  },
  // 来源信息
  source: {
    type: DataTypes.STRING, // website, social, email, ads, etc.
    allowNull: false
  },
  sourceDetails: {
    type: DataTypes.JSON, // page_url, utm_params, referrer, etc.
    defaultValue: {}
  },
  // 地理信息
  ipAddress: {
    type: DataTypes.STRING,
    allowNull: true
  },
  location: {
    type: DataTypes.JSON, // city, country, timezone
    defaultValue: {}
  },
  // 自定义字段数据
  customFields: {
    type: DataTypes.JSON,
    defaultValue: {}
  },
  // 分配信息
  assignedTo: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'users'
      key: 'id'
    }
  },
  assignedAt: {
    type: DataTypes.DATE,
    allowNull: true
  },
  // 最后互动时间
  lastInteractionAt: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  // 跟进信息
  nextFollowupAt: {
    type: DataTypes.DATE,
    allowNull: true
  },
  followupNotes: {
    type: DataTypes.TEXT,
    allowNull: true
  }
}, {
  tableName: 'leads'
  timestamps: true,
  paranoid: true, // 软删除
  indexes: [
    { fields: ['email'] },
    { fields: ['agentId'] },
    { fields: ['status'] },
    { fields: ['score'] },
    { fields: ['assignedTo'] },
    { fields: ['createdAt'] },
    { fields: ['lastInteractionAt'] }
  ]
});

// 线索活动记录表
const LeadActivity = sequelize.define('LeadActivity' {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  leadId: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'leads'
      key: 'id'
    }
  },
  // 活动类型
  type: {
    type: DataTypes.ENUM(
      'page_view' 'form_submit' 'email_open' 'email_click' 
      'download' 'video_view' 'chat_start' 'call_made'
      'meeting_scheduled' 'proposal_sent' 'contract_signed'
    ),
    allowNull: false
  },
  // 活动详情
  description: {
    type: DataTypes.STRING,
    allowNull: true
  },
  metadata: {
    type: DataTypes.JSON, // page_url, email_subject, file_name, etc.
    defaultValue: {}
  },
  // 评分影响
  scoreImpact: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  // 用户代理和设备信息
  userAgent: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  deviceInfo: {
    type: DataTypes.JSON,
    defaultValue: {}
  },
  // 活动来源
  source: {
    type: DataTypes.STRING,
    allowNull: true
  },
  // IP和地理位置
  ipAddress: {
    type: DataTypes.STRING,
    allowNull: true
  },
  location: {
    type: DataTypes.JSON,
    defaultValue: {}
  }
}, {
  tableName: 'lead_activities'
  timestamps: true,
  indexes: [
    { fields: ['leadId'] },
    { fields: ['type'] },
    { fields: ['createdAt'] }
  ]
});

// 线索评分历史表
const LeadScore = sequelize.define('LeadScore' {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  leadId: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'leads'
      key: 'id'
    }
  },
  // 评分详情
  previousScore: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  newScore: {
    type: DataTypes.INTEGER,
    allowNull: false
  },
  scoreChange: {
    type: DataTypes.INTEGER,
    allowNull: false
  },
  // 评分原因
  reason: {
    type: DataTypes.STRING,
    allowNull: false
  },
  // 评分分解
  behavioralScore: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  demographicScore: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  interactionScore: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  sourceScore: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  // 相关活动
  activityId: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'lead_activities'
      key: 'id'
    }
  },
  // 评分配置快照
  scoringConfig: {
    type: DataTypes.JSON,
    defaultValue: {}
  }
}, {
  tableName: 'lead_scores'
  timestamps: true,
  indexes: [
    { fields: ['leadId'] },
    { fields: ['createdAt'] },
    { fields: ['newScore'] }
  ]
});

// 线索表单提交记录
const LeadFormSubmission = sequelize.define('LeadFormSubmission' {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  leadId: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'leads'
      key: 'id'
    }
  },
  agentId: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'agents'
      key: 'id'
    }
  },
  // 表单信息
  formType: {
    type: DataTypes.STRING, // contact, newsletter, demo, download, etc.
    allowNull: false
  },
  formData: {
    type: DataTypes.JSON,
    allowNull: false
  },
  // 页面信息
  pageUrl: {
    type: DataTypes.STRING,
    allowNull: true
  },
  pageTitle: {
    type: DataTypes.STRING,
    allowNull: true
  },
  referrerUrl: {
    type: DataTypes.STRING,
    allowNull: true
  },
  // UTM参数
  utmSource: {
    type: DataTypes.STRING,
    allowNull: true
  },
  utmMedium: {
    type: DataTypes.STRING,
    allowNull: true
  },
  utmCampaign: {
    type: DataTypes.STRING,
    allowNull: true
  },
  utmTerm: {
    type: DataTypes.STRING,
    allowNull: true
  },
  utmContent: {
    type: DataTypes.STRING,
    allowNull: true
  },
  // 技术信息
  userAgent: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  ipAddress: {
    type: DataTypes.STRING,
    allowNull: true
  },
  sessionId: {
    type: DataTypes.STRING,
    allowNull: true
  }
}, {
  tableName: 'lead_form_submissions'
  timestamps: true,
  indexes: [
    { fields: ['leadId'] },
    { fields: ['agentId'] },
    { fields: ['formType'] },
    { fields: ['createdAt'] }
  ]
});

// 线索标签系统
const LeadTag = sequelize.define('LeadTag' {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  name: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true
  },
  color: {
    type: DataTypes.STRING,
    defaultValue: '#6B7280'
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  agentId: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'agents'
      key: 'id'
    }
  }
}, {
  tableName: 'lead_tags'
  timestamps: true
});

// 线索标签关联表
const LeadTagAssignment = sequelize.define('LeadTagAssignment' {
  leadId: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'leads'
      key: 'id'
    }
  },
  tagId: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'lead_tags'
      key: 'id'
    }
  },
  assignedBy: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'users'
      key: 'id'
    }
  }
}, {
  tableName: 'lead_tag_assignments'
  timestamps: true,
  indexes: [
    { fields: ['leadId'] },
    { fields: ['tagId'] },
    { unique: true, fields: ['leadId', 'tagId'] }
  ]
});

// 线索转化漏斗配置
const ConversionFunnel = sequelize.define('ConversionFunnel' {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  agentId: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'agents'
      key: 'id'
    }
  },
  name: {
    type: DataTypes.STRING,
    allowNull: false
  },
  stages: {
    type: DataTypes.JSON, // [{ name, order, criteria }]
    allowNull: false
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  }
}, {
  tableName: 'conversion_funnels'
  timestamps: true
});

// 线索转化记录
const LeadConversion = sequelize.define('LeadConversion' {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  leadId: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'leads'
      key: 'id'
    }
  },
  funnelId: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'conversion_funnels'
      key: 'id'
    }
  },
  stage: {
    type: DataTypes.STRING,
    allowNull: false
  },
  stageOrder: {
    type: DataTypes.INTEGER,
    allowNull: false
  },
  convertedAt: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  value: {
    type: DataTypes.DECIMAL(10, 2), // 转化价值
    allowNull: true
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  }
}, {
  tableName: 'lead_conversions'
  timestamps: true,
  indexes: [
    { fields: ['leadId'] },
    { fields: ['funnelId'] },
    { fields: ['stage'] },
    { fields: ['convertedAt'] }
  ]
});

// 定义关联关系
Lead.hasMany(LeadActivity, { foreignKey: 'leadId' as: 'activities' });
LeadActivity.belongsTo(Lead, { foreignKey: 'leadId' as: 'lead' });

Lead.hasMany(LeadScore, { foreignKey: 'leadId' as: 'scoreHistory' });
LeadScore.belongsTo(Lead, { foreignKey: 'leadId' as: 'lead' });

Lead.hasMany(LeadFormSubmission, { foreignKey: 'leadId' as: 'formSubmissions' });
LeadFormSubmission.belongsTo(Lead, { foreignKey: 'leadId' as: 'lead' });

Lead.belongsToMany(LeadTag, { 
  through: LeadTagAssignment, 
  foreignKey: 'leadId' 
  as: 'tags' 
});
LeadTag.belongsToMany(Lead, { 
  through: LeadTagAssignment, 
  foreignKey: 'tagId' 
  as: 'leads' 
});

Lead.hasMany(LeadConversion, { foreignKey: 'leadId' as: 'conversions' });
LeadConversion.belongsTo(Lead, { foreignKey: 'leadId' as: 'lead' });

ConversionFunnel.hasMany(LeadConversion, { foreignKey: 'funnelId' as: 'conversions' });
LeadConversion.belongsTo(ConversionFunnel, { foreignKey: 'funnelId' as: 'funnel' });

LeadScore.belongsTo(LeadActivity, { foreignKey: 'activityId' as: 'activity' });

module.exports = {
  Lead,
  LeadActivity,
  LeadScore,
  LeadFormSubmission,
  LeadTag,
  LeadTagAssignment,
  ConversionFunnel,
  LeadConversion
}; 