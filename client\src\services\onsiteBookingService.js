import axiosInstance from '../api/axiosInstance';

/**
 * 获取上门服务预约列表
 * @param {Object} filters - 筛选条件
 * @returns {Promise<Array>} - 预约列表
 */
export const getOnsiteBookings = async (filters = {}) => {
  try {
    const response = await axiosInstance.get('/api/bookings/onsite', { params: filters });
    return response.data;
  } catch (error) {
    console.error('获取上门服务预约失败:', error.response?.data || error);
    throw error.response?.data || error;
  }
};

/**
 * 获取特定日期的上门服务预约
 * @param {string} date - 日期字符串，格式 YYYY-MM-DD
 * @returns {Promise<Array>} - 预约列表
 */
export const getOnsiteBookingsByDate = async (date) => {
  try {
    const response = await axiosInstance.get(`/api/bookings/onsite/date/${date}`);
    return response.data;
  } catch (error) {
    console.error('获取指定日期上门预约失败:', error.response?.data || error);
    throw error.response?.data || error;
  }
};

/**
 * 获取上门服务预约详情
 * @param {string} bookingId - 预约ID
 * @returns {Promise<Object>} - 预约详情
 */
export const getOnsiteBookingDetails = async (bookingId) => {
  try {
    const response = await axiosInstance.get(`/api/bookings/onsite/${bookingId}`);
    return response.data;
  } catch (error) {
    console.error('获取上门预约详情失败:', error.response?.data || error);
    throw error.response?.data || error;
  }
};

/**
 * 创建上门服务预约
 * @param {Object} bookingData - 预约数据
 * @returns {Promise<Object>} - 创建的预约对象
 */
export const createOnsiteBooking = async (bookingData) => {
  try {
    const response = await axiosInstance.post('/api/bookings/onsite', bookingData);
    return response.data;
  } catch (error) {
    console.error('创建上门预约失败:', error.response?.data || error);
    throw error.response?.data || error;
  }
};

/**
 * 更新上门服务预约
 * @param {string} bookingId - 预约ID
 * @param {Object} bookingData - 预约更新数据
 * @returns {Promise<Object>} - 更新后的预约对象
 */
export const updateOnsiteBooking = async (bookingId, bookingData) => {
  try {
    const response = await axiosInstance.put(`/api/bookings/onsite/${bookingId}`, bookingData);
    return response.data;
  } catch (error) {
    console.error('更新上门预约失败:', error.response?.data || error);
    throw error.response?.data || error;
  }
};

/**
 * 更新上门服务预约状态
 * @param {string} bookingId - 预约ID
 * @param {string} status - 新状态
 * @returns {Promise<Object>} - 更新后的预约对象
 */
export const updateOnsiteBookingStatus = async (bookingId, status) => {
  try {
    const response = await axiosInstance.patch(`/api/bookings/onsite/${bookingId}/status`, { status });
    return response.data;
  } catch (error) {
    console.error('更新上门预约状态失败:', error.response?.data || error);
    throw error.response?.data || error;
  }
};

/**
 * 取消上门服务预约
 * @param {string} bookingId - 预约ID
 * @param {string} reason - 取消原因
 * @returns {Promise<Object>} - 取消后的预约对象
 */
export const cancelOnsiteBooking = async (bookingId, reason) => {
  try {
    const response = await axiosInstance.post(`/api/bookings/onsite/${bookingId}/cancel`, { reason });
    return response.data;
  } catch (error) {
    console.error('取消上门预约失败:', error.response?.data || error);
    throw error.response?.data || error;
  }
};

/**
 * 分配员工给上门服务预约
 * @param {string} bookingId - 预约ID
 * @param {string} staffId - 员工ID
 * @returns {Promise<Object>} - 更新后的预约对象
 */
export const assignStaffToOnsiteBooking = async (bookingId, staffId) => {
  try {
    const response = await axiosInstance.post(`/api/bookings/onsite/${bookingId}/assign`, { staffId });
    return response.data;
  } catch (error) {
    console.error('分配员工失败:', error.response?.data || error);
    throw error.response?.data || error;
  }
};

/**
 * 标记上门服务开始
 * @param {string} bookingId - 预约ID
 * @returns {Promise<Object>} - 更新后的预约对象
 */
export const startOnsiteService = async (bookingId) => {
  try {
    const response = await axiosInstance.post(`/api/bookings/onsite/${bookingId}/start`);
    return response.data;
  } catch (error) {
    console.error('标记服务开始失败:', error.response?.data || error);
    throw error.response?.data || error;
  }
};

/**
 * 标记上门服务完成
 * @param {string} bookingId - 预约ID
 * @returns {Promise<Object>} - 更新后的预约对象
 */
export const completeOnsiteService = async (bookingId) => {
  try {
    const response = await axiosInstance.post(`/api/bookings/onsite/${bookingId}/complete`);
    return response.data;
  } catch (error) {
    console.error('标记服务完成失败:', error.response?.data || error);
    throw error.response?.data || error;
  }
};

/**
 * 获取可用的服务时间段
 * @param {string} date - 日期字符串，格式 YYYY-MM-DD
 * @param {string} serviceId - 服务ID
 * @returns {Promise<Array>} - 可用时间段列表
 */
export const getAvailableTimeSlots = async (date, serviceId) => {
  try {
    const response = await axiosInstance.get('/api/bookings/onsite/available-slots', {
      params: { date, serviceId }
    });
    return response.data;
  } catch (error) {
    console.error('获取上门服务可用时间段失败:', error.response?.data || error);
    throw error.response?.data || error;
  }
};

/**
 * 获取服务区域信息
 * @returns {Promise<Object>} - 服务区域数据
 */
export const getServiceAreas = async () => {
  try {
    const response = await axiosInstance.get('/api/bookings/onsite/service-areas');
    return response.data;
  } catch (error) {
    console.error('获取服务区域失败:', error.response?.data || error);
    throw error.response?.data || error;
  }
};

/**
 * 验证地址是否在服务区域内
 * @param {Object} address - 地址对象
 * @returns {Promise<Object>} - 验证结果
 */
export const validateServiceAddress = async (address) => {
  try {
    const response = await axiosInstance.post('/api/bookings/onsite/validate-address', address);
    return response.data;
  } catch (error) {
    console.error('验证地址失败:', error.response?.data || error);
    throw error.response?.data || error;
  }
};

/**
 * 获取上门预约设置
 * @returns {Promise<Object>} - 设置对象
 */
export const getOnsiteSettings = async () => {
  try {
    const response = await axiosInstance.get('/api/settings/onsite');
    return response.data;
  } catch (error) {
    console.error('获取上门预约设置失败:', error.response?.data || error);
    throw error.response?.data || error;
  }
};

/**
 * 更新上门预约设置
 * @param {Object} settings - 设置对象
 * @returns {Promise<Object>} - 更新后的设置对象
 */
export const updateOnsiteSettings = async (settings) => {
  try {
    const response = await axiosInstance.put('/api/settings/onsite', settings);
    return response.data;
  } catch (error) {
    console.error('更新上门预约设置失败:', error.response?.data || error);
    throw error.response?.data || error;
  }
};

const onsiteBookingService = {
  getOnsiteBookings,
  getOnsiteBookingsByDate,
  getOnsiteBookingDetails,
  createOnsiteBooking,
  updateOnsiteBooking,
  updateOnsiteBookingStatus,
  cancelOnsiteBooking,
  assignStaffToOnsiteBooking,
  startOnsiteService,
  completeOnsiteService,
  getAvailableTimeSlots,
  getServiceAreas,
  validateServiceAddress,
  getOnsiteSettings,
  updateOnsiteSettings
};

export default onsiteBookingService;