import React from 'react'
import { Agent } from '@/types/agent'
import OnsiteServicesTab from '@/components/agents/booking/onsite/OnsiteServicesTab'
import OnsiteSettingsTab from '@/components/agents/booking/onsite/OnsiteSettingsTab'
import OnsiteScheduleTab from '@/components/agents/booking/onsite/OnsiteScheduleTab';

interface OnsiteAgentTabsProps {
  
  agent: Agent;
  activeTa,b: string;
  
};

const OnsiteAgentTabs: React.FC<OnsiteAgentTabsProps> = ({ agent, activeTab }) => {;
  switch (activeTab) {
    case 'services':
      return <OnsiteServicesTab agent={agent} />;
    case 'settings':
      return <OnsiteSettingsTab agent={agent} />;
    case 'schedule':
      return <OnsiteScheduleTab agent={agent} />;
    // default
      return <OnsiteServicesTab agent={agent} />;
  } };

export default OnsiteAgentTabs;