"use strict";(self.webpackChunkclient=self.webpackChunkclient||[]).push([[8],{"./src/api/axiosInstance.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.d(__webpack_exports__,{A:()=>__WEBPACK_DEFAULT_EXPORT__});var axios__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("./node_modules/axios/lib/axios.js");const API_BASE_URL=__webpack_require__("./node_modules/process/browser.js").env.REACT_APP_API_BASE_URL||"http://localhost:3001",axiosInstance=axios__WEBPACK_IMPORTED_MODULE_0__.A.create({baseURL:API_BASE_URL,timeout:1e4,headers:{"Content-Type":"application/json"}});axiosInstance.interceptors.request.use((config=>{const token=localStorage.getItem("auth_token");return console.log("Axios Interceptor - Reading token using key 'auth_token':",token?"Found":"Not Found"),token?(config.headers.Authorization=`Bearer ${token}`,console.log("Axios Interceptor - Setting Authorization header.")):console.log("Axios Interceptor - 'auth_token' not found in localStorage."),config}),(error=>Promise.reject(error))),axiosInstance.interceptors.response.use((response=>response),(error=>(error.response&&401===error.response.status&&console.error("Unauthorized access - 401"),Promise.reject(error))));const __WEBPACK_DEFAULT_EXPORT__=axiosInstance},"./src/hooks/useFeatureGuard.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.d(__webpack_exports__,{B:()=>useFeatureGuard});var react__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("./node_modules/react/index.js"),react_router_dom__WEBPACK_IMPORTED_MODULE_2__=__webpack_require__("./node_modules/react-router/dist/development/chunk-AYJ5UCUI.mjs"),_context_AuthContext__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__("./src/context/AuthContext.js");const planOrder=["free","proA","proB","enterprise"];function useFeatureGuard(minPlan){const navigate=(0,react_router_dom__WEBPACK_IMPORTED_MODULE_2__.Zp)(),{user}=(0,_context_AuthContext__WEBPACK_IMPORTED_MODULE_1__.As)(),userPlan=(null==user?void 0:user.plan)||"free";(0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)((()=>{planOrder.indexOf(userPlan)<planOrder.indexOf(minPlan)&&navigate("/upgrade",{replace:!0})}),[userPlan,minPlan,navigate])}},"./src/pages/AppointmentMapPage.stories.jsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{Default:()=>Default,__namedExportsOrder:()=>__namedExportsOrder,default:()=>AppointmentMapPage_stories});var react=__webpack_require__("./node_modules/react/index.js"),chunk_AYJ5UCUI=__webpack_require__("./node_modules/react-router/dist/development/chunk-AYJ5UCUI.mjs"),fa=__webpack_require__("./node_modules/react-icons/fa/index.mjs"),dist=__webpack_require__("./node_modules/react-toastify/dist/index.mjs"),hooks=(__webpack_require__("./node_modules/react-toastify/dist/ReactToastify.css"),__webpack_require__("./node_modules/react-leaflet/lib/hooks.js")),Marker=__webpack_require__("./node_modules/react-leaflet/lib/Marker.js"),MapContainer=__webpack_require__("./node_modules/react-leaflet/lib/MapContainer.js"),TileLayer=__webpack_require__("./node_modules/react-leaflet/lib/TileLayer.js"),Popup=__webpack_require__("./node_modules/react-leaflet/lib/Popup.js"),leaflet_src=__webpack_require__("./node_modules/leaflet/dist/leaflet-src.js"),leaflet_src_default=__webpack_require__.n(leaflet_src),moment=__webpack_require__("./node_modules/moment/moment.js"),moment_default=__webpack_require__.n(moment),marker_icon_2x=__webpack_require__("./node_modules/leaflet/dist/images/marker-icon-2x.png"),marker_icon=__webpack_require__("./node_modules/leaflet/dist/images/marker-icon.png"),marker_shadow=__webpack_require__("./node_modules/leaflet/dist/images/marker-shadow.png"),jsx_runtime=__webpack_require__("./node_modules/react/jsx-runtime.js");delete leaflet_src_default().Icon.Default.prototype._getIconUrl,leaflet_src_default().Icon.Default.mergeOptions({iconRetinaUrl:marker_icon_2x,iconUrl:marker_icon,shadowUrl:marker_shadow});const createCustomIcon=function(color,text){let accuracyLevel=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,markerColor=color,borderColor="#333",accuracyIndicator=null;if(accuracyLevel)switch(accuracyLevel){case"very_high":accuracyIndicator="#00c853",borderColor="#00c853";break;case"high":accuracyIndicator="#64dd17",borderColor="#64dd17";break;case"medium":accuracyIndicator="#ffd600",borderColor="#ffd600";break;case"low":accuracyIndicator="#ff9100",borderColor="#ff9100";break;case"very_low":accuracyIndicator="#ff3d00",borderColor="#ff3d00";break;default:accuracyIndicator="#757575",borderColor="#757575"}return new(leaflet_src_default().DivIcon)({className:"custom-map-marker",html:`\n      <div class="marker-container" style="border: 2px solid ${borderColor};">\n        <div class="marker-circle" style="background-color: ${markerColor}">\n          <span class="marker-text">${text}</span>\n        </div>\n        ${accuracyIndicator?`<div class="accuracy-indicator" style="background-color: ${accuracyIndicator}" title="地理编码精确度: ${getAccuracyLevelText(accuracyLevel)}"></div>`:""}\n      </div>\n    `,iconSize:[30,30],iconAnchor:[15,15]})},getAccuracyLevelText=level=>{switch(level){case"very_high":return"非常高";case"high":return"高";case"medium":return"中等";case"low":return"低";case"very_low":return"非常低";default:return"未知"}},pinIcon=leaflet_src_default().divIcon({className:"pin-map-marker",html:"<div>\n           <span>📍</span>\n         </div>",iconSize:[30,30],iconAnchor:[15,30],popupAnchor:[0,-30]}),MapUpdater=_ref=>{let{appointments}=_ref;const mapInstance=(0,hooks.ko)();return(0,react.useEffect)((()=>{const validPoints=appointments.filter((app=>app.coordinates&&app.coordinates.lat&&app.coordinates.lng));if(validPoints.length>0){const latitudes=validPoints.map((p=>p.coordinates.lat)),longitudes=validPoints.map((p=>p.coordinates.lng));try{const bounds=leaflet_src_default().latLngBounds(leaflet_src_default().latLng(Math.min(...latitudes),Math.min(...longitudes)),leaflet_src_default().latLng(Math.max(...latitudes),Math.max(...longitudes)));if(mapInstance&&bounds.isValid())mapInstance.fitBounds(bounds,{padding:[50,50]});else if(mapInstance&&1===validPoints.length){const point=validPoints[0];mapInstance.setView([point.coordinates.lat,point.coordinates.lng],13)}}catch(error){console.error("Map bounds calculation error:",error),mapInstance.setView([3.139,101.6869],11)}}else mapInstance&&mapInstance.setView([3.139,101.6869],11)}),[appointments,mapInstance]),null},LocationPicker=_ref2=>{let{onLocationSelected,active}=_ref2;const[tempMarker,setTempMarker]=(0,react.useState)(null);return(0,hooks.Po)({click:e=>{if(!active)return;const{lat,lng}=e.latlng;setTempMarker({lat,lng}),onLocationSelected&&onLocationSelected({lat,lng})}}),tempMarker&&active?(0,jsx_runtime.jsx)(Marker.p,{position:[tempMarker.lat,tempMarker.lng],icon:pinIcon}):null},buildAddressStringForPopup=address=>{if(!address)return null;return[address.line1,address.line2,address.city,address.postalCode,address.country].filter((part=>part&&""!==String(part).trim())).join(", ")},AppointmentMap_AppointmentMap=_ref3=>{let{appointments,onAppointmentSelect}=_ref3;const[selectedAppointment,setSelectedAppointment]=(0,react.useState)(null),[searchQuery,setSearchQuery]=(0,react.useState)(""),[activeFilter]=(0,react.useState)("all"),[locationPickerActive,setLocationPickerActive]=(0,react.useState)(!1),handleMarkerClick=appointment=>{setSelectedAppointment(appointment),onAppointmentSelect&&onAppointmentSelect(appointment)},filteredAppointments=(0,react.useMemo)((()=>appointments.filter((app=>{var _app$coordinates,_app$coordinates2;const props=app.extendedProps||{},searchLower=searchQuery.toLowerCase(),matchesSearch=""===searchQuery||props.clientName&&props.clientName.toLowerCase().includes(searchLower)||props.serviceName&&props.serviceName.toLowerCase().includes(searchLower)||props.teamName&&props.teamName.toLowerCase().includes(searchLower)||app.title&&app.title.toLowerCase().includes(searchLower),hasCoords=!(null===(_app$coordinates=app.coordinates)||void 0===_app$coordinates||!_app$coordinates.lat||null===(_app$coordinates2=app.coordinates)||void 0===_app$coordinates2||!_app$coordinates2.lng);return matchesSearch&&("all"===activeFilter||"mapped"===activeFilter&&hasCoords||"unmapped"===activeFilter&&!hasCoords)}))),[appointments,searchQuery,activeFilter]),validAppointments=(0,react.useMemo)((()=>filteredAppointments.filter((app=>{var _app$coordinates3,_app$coordinates4;return(null==app||null===(_app$coordinates3=app.coordinates)||void 0===_app$coordinates3?void 0:_app$coordinates3.lat)&&(null==app||null===(_app$coordinates4=app.coordinates)||void 0===_app$coordinates4?void 0:_app$coordinates4.lng)}))),[filteredAppointments]);return(0,jsx_runtime.jsx)("div",{className:"appointment-map-container",children:(0,jsx_runtime.jsxs)("div",{className:"map-content",children:[(0,jsx_runtime.jsxs)("div",{className:"appointment-list-panel",children:[(0,jsx_runtime.jsxs)("div",{className:"appointment-list-header",children:[(0,jsx_runtime.jsxs)("h4",{children:["Appointments (",filteredAppointments.length," of ",appointments.length," total, ",validAppointments.length," on map)"]}),(0,jsx_runtime.jsx)("div",{className:"appointment-search",children:(0,jsx_runtime.jsxs)("div",{className:"search-input-container",children:[(0,jsx_runtime.jsx)(fa.KSO,{className:"search-icon"}),(0,jsx_runtime.jsx)("input",{type:"text",placeholder:"Search appointments...",value:searchQuery,onChange:event=>{setSearchQuery(event.target.value)},className:"search-input"}),searchQuery&&(0,jsx_runtime.jsx)("button",{className:"clear-search-btn",onClick:()=>{setSearchQuery("")},title:"Clear search",children:(0,jsx_runtime.jsx)(fa._Hm,{})})]})})]}),0===filteredAppointments.length?(0,jsx_runtime.jsx)("p",{className:"no-appointments-message",children:searchQuery||"all"!==activeFilter?"No appointments match your filter criteria.":"No appointments found matching the selected criteria."}):(0,jsx_runtime.jsx)("ul",{children:filteredAppointments.map(((appointment,index)=>(appointment=>{const props=appointment.extendedProps||{},hasCoords=!!appointment.coordinates,address=props.address?buildAddressStringForPopup(props.address):null,contactInfo=props.contactInfo||props.phoneNumber||props.email||null;return(0,jsx_runtime.jsxs)("li",{className:`\n          ${(null==selectedAppointment?void 0:selectedAppointment.id)===appointment.id?"selected":""}\n          ${hasCoords?"has-coords":"no-coords"}\n          ${hasCoords&&props.accuracyLevel?`accuracy-${props.accuracyLevel}`:""}\n        `,onClick:()=>handleMarkerClick(appointment),children:[(0,jsx_runtime.jsxs)("div",{className:"appointment-time",children:[(0,jsx_runtime.jsx)("span",{role:"img","aria-label":"Time",children:(0,jsx_runtime.jsx)(fa.w_X,{})}),moment_default()(appointment.start).format("h:mm a")]}),(0,jsx_runtime.jsx)("div",{className:"appointment-client",children:props.clientName||"Unnamed Client"}),props.serviceName&&(0,jsx_runtime.jsxs)("div",{className:"appointment-service-name",children:[(0,jsx_runtime.jsx)("span",{role:"img","aria-label":"Service",children:(0,jsx_runtime.jsx)(fa.bfZ,{})}),props.serviceName]}),props.teamName&&(0,jsx_runtime.jsxs)("div",{className:"appointment-team-name",children:[(0,jsx_runtime.jsx)("span",{role:"img","aria-label":"Team",children:(0,jsx_runtime.jsx)(fa.x$1,{})}),props.teamName]}),(0,jsx_runtime.jsxs)("div",{className:"appointment-address",children:[(0,jsx_runtime.jsx)("span",{role:"img","aria-label":"Address",children:(0,jsx_runtime.jsx)(fa.ZYm,{})}),address||"No address provided"]}),(0,jsx_runtime.jsxs)("div",{className:"appointment-contact",children:[(0,jsx_runtime.jsx)("span",{role:"img","aria-label":"Contact",children:(0,jsx_runtime.jsx)(fa.Cab,{})}),contactInfo||"No contact information"]}),!hasCoords&&props.geocodingFailReason&&(0,jsx_runtime.jsxs)("div",{className:"geocode-failed-indicator",children:["Geocoding failed: ",props.geocodingFailReason||"Unknown error"]}),hasCoords&&void 0!==props.accuracyScore&&(0,jsx_runtime.jsx)("div",{className:"geocode-accuracy-indicator",children:(0,jsx_runtime.jsx)("span",{className:`accuracy-dot ${props.accuracyLevel||"unknown"}`,title:`Geocode accuracy: ${getAccuracyLevelText(props.accuracyLevel)} (${Math.round(100*(props.accuracyScore||0))}%)`})})]},appointment.id)})(appointment)))})]}),(0,jsx_runtime.jsx)("div",{className:"leaflet-map-container",children:(0,jsx_runtime.jsxs)(MapContainer.W,{center:[3.139,101.6869],zoom:11,className:"leaflet-map",zoomControl:!0,children:[(0,jsx_runtime.jsx)(TileLayer.e,{url:"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",attribution:'© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'}),(0,jsx_runtime.jsx)(MapUpdater,{appointments:validAppointments}),(0,jsx_runtime.jsx)(LocationPicker,{onLocationSelected:location=>{setLocationPickerActive(!1),console.log("[Pin Location] Selected location:",location)},active:locationPickerActive}),validAppointments.map(((appointment,index)=>{var _appointment$extended;let markerColor="#3498db";return appointment.extendedProps&&appointment.extendedProps.teamColour&&(markerColor=appointment.extendedProps.teamColour),(0,jsx_runtime.jsx)(Marker.p,{position:[appointment.coordinates.lat,appointment.coordinates.lng],icon:createCustomIcon(markerColor,(index+1).toString(),null===(_appointment$extended=appointment.extendedProps)||void 0===_appointment$extended?void 0:_appointment$extended.accuracyLevel),eventHandlers:{click:()=>handleMarkerClick(appointment)},children:(0,jsx_runtime.jsx)(Popup.z,{children:(0,jsx_runtime.jsxs)("div",{className:"map-info-window",children:[(0,jsx_runtime.jsx)("h3",{children:appointment.extendedProps.clientName||appointment.title}),(0,jsx_runtime.jsxs)("p",{children:[(0,jsx_runtime.jsx)("strong",{children:"Time:"})," ",moment_default()(appointment.start).format("YYYY-MM-DD HH:mm")]}),(0,jsx_runtime.jsxs)("p",{children:[(0,jsx_runtime.jsx)("strong",{children:"Service:"})," ",appointment.extendedProps.serviceName||"Not specified"]}),(0,jsx_runtime.jsxs)("p",{children:[(0,jsx_runtime.jsx)("strong",{children:"Team:"})," ",appointment.extendedProps.teamName||"Unassigned"]}),(0,jsx_runtime.jsxs)("p",{children:[(0,jsx_runtime.jsx)("strong",{children:"Address:"})," ",appointment.extendedProps.address?buildAddressStringForPopup(appointment.extendedProps.address):"No address provided"]}),appointment.extendedProps.contactInfo&&(0,jsx_runtime.jsxs)("p",{children:[(0,jsx_runtime.jsx)("strong",{children:"Contact:"})," ",appointment.extendedProps.contactInfo]}),appointment.extendedProps.phoneNumber&&(0,jsx_runtime.jsxs)("p",{children:[(0,jsx_runtime.jsx)("strong",{children:"Phone:"})," ",appointment.extendedProps.phoneNumber]}),appointment.extendedProps.email&&(0,jsx_runtime.jsxs)("p",{children:[(0,jsx_runtime.jsx)("strong",{children:"Email:"})," ",appointment.extendedProps.email]}),!appointment.extendedProps.contactInfo&&!appointment.extendedProps.phoneNumber&&!appointment.extendedProps.email&&(0,jsx_runtime.jsxs)("p",{children:[(0,jsx_runtime.jsx)("strong",{children:"Contact:"})," No contact information"]}),void 0!==appointment.extendedProps.accuracyScore&&(0,jsx_runtime.jsx)("div",{className:`geocode-accuracy ${appointment.extendedProps.accuracyLevel||"unknown"}`,children:(0,jsx_runtime.jsxs)("p",{children:[(0,jsx_runtime.jsx)("strong",{children:"Geocode Accuracy:"})," ",getAccuracyLevelText(appointment.extendedProps.accuracyLevel),"(",Math.round(100*(appointment.extendedProps.accuracyScore||0)),"%)"]})})]})})},appointment.id)}))]})})]})})},map_AppointmentMap=AppointmentMap_AppointmentMap;AppointmentMap_AppointmentMap.__docgenInfo={description:"",methods:[],displayName:"AppointmentMap"};var axiosInstance=__webpack_require__("./src/api/axiosInstance.js");const normalizeTeam=team=>({id:team.id,name:team.name,colour:team.colour||team.color,max_overlap:team.max_overlap}),fetchTeams=async()=>{console.log("[teamService] 正在从API获取团队数据...");try{const response=await axiosInstance.A.get("/teams");return console.log("[teamService] 团队数据获取成功:",response.data),Array.isArray(response.data)?response.data.map((team=>normalizeTeam(team))):(console.error("[teamService] API返回的数据格式不正确:",response.data),console.warn("[teamService] 返回空团队列表"),[])}catch(error){return console.error("[teamService] 获取团队数据失败:",error),console.warn("[teamService] API不可用，返回空团队列表"),[]}};var useFeatureGuard=__webpack_require__("./src/hooks/useFeatureGuard.js");const isValidDate=date=>date instanceof Date&&!isNaN(date.getTime()),safeISOString=date=>date&&isValidDate(date)?date.toISOString():(console.warn("Invalid date detected:",date),(new Date).toISOString()),AppointmentMapPage_AppointmentMapPage=()=>{var _teams$find2;(0,useFeatureGuard.B)("proA");const[allAppointments,setAllAppointments]=(0,react.useState)([]),[teams,setTeams]=(0,react.useState)([]),[selectedTeamId,setSelectedTeamId]=(0,react.useState)("all"),[loading,setLoading]=(0,react.useState)(!0),[loadingTeams,setLoadingTeams]=(0,react.useState)(!0),[error,setError]=(0,react.useState)(""),[dateRange,setDateRange]=(0,react.useState)({start:new Date,end:new Date((new Date).setDate((new Date).getDate()+7))});(0,react.useEffect)((()=>{(async()=>{setLoading(!0),setError("");try{const params={start_gte:safeISOString(dateRange.start),start_lte:safeISOString(dateRange.end),..."all"!==selectedTeamId&&{team_id:selectedTeamId}};console.log("[MapPage] Fetching map appointments with params:",params);const response=await axiosInstance.A.get("/appointments/map-data",{params});if(console.log("[MapPage] Fetched map appointments result:",response.data),setAllAppointments(response.data||[]),loadingTeams&&response.data&&response.data.length>0){const teamsFromAppointments=extractTeamsFromAppointments(response.data);teamsFromAppointments.length>0?(console.log("[MapPage] Extracted teams from map appointments:",teamsFromAppointments),setTeams(teamsFromAppointments),setLoadingTeams(!1)):loadTeamsFromAPI()}else loadingTeams&&loadTeamsFromAPI()}catch(error){var _error$response,_error$response$data;const errorMsg=(null===(_error$response=error.response)||void 0===_error$response||null===(_error$response$data=_error$response.data)||void 0===_error$response$data?void 0:_error$response$data.message)||error.message||"Error loading map appointment data";console.error("Error fetching map appointments:",error),setError(errorMsg),dist.oR.error("Unable to load map appointment data, please try again later"),loadingTeams&&loadTeamsFromAPI()}finally{setLoading(!1)}})()}),[dateRange,selectedTeamId,loadingTeams]);const extractTeamsFromAppointments=appointments=>{const teamMap=new Map;return appointments.forEach((appointment=>{var _appointment$extended;const teamId=null===(_appointment$extended=appointment.extendedProps)||void 0===_appointment$extended?void 0:_appointment$extended.teamId;var _appointment$extended2,_appointment$extended3;teamId&&(teamMap.has(teamId)||teamMap.set(teamId,{id:teamId,name:(null===(_appointment$extended2=appointment.extendedProps)||void 0===_appointment$extended2?void 0:_appointment$extended2.teamName)||`Team ${teamId}`,colour:(null===(_appointment$extended3=appointment.extendedProps)||void 0===_appointment$extended3?void 0:_appointment$extended3.teamColour)||"#cccccc"}))})),Array.from(teamMap.values())},loadTeamsFromAPI=async()=>{try{setLoadingTeams(!0);const fetchedTeams=await fetchTeams();fetchedTeams&&fetchedTeams.length>0?setTeams(fetchedTeams):console.log("[AppointmentMapPage] No team data available from API")}catch(err){console.error("[AppointmentMapPage] Error fetching team data:",err)}finally{setLoadingTeams(!1)}},filteredAppointments=(0,react.useMemo)((()=>"all"===selectedTeamId?allAppointments:allAppointments.filter((app=>{var _app$extendedProps;const appointmentTeamId=null===(_app$extendedProps=app.extendedProps)||void 0===_app$extendedProps?void 0:_app$extendedProps.teamId;return String(appointmentTeamId)===String(selectedTeamId)}))),[allAppointments,selectedTeamId]);(0,react.useEffect)((()=>{console.log(`[MapPage] Team filtering: Filtered ${filteredAppointments.length} appointments from ${allAppointments.length}`)}),[allAppointments.length,filteredAppointments.length]);const handleDateRangeChange=(type,value)=>{try{if(!value)return console.warn(`Date input is empty, type: ${type}`),void dist.oR.error("Date cannot be empty");const newDate=(value=>{if(!value)return console.warn("Received null value when creating date"),new Date;try{const date=new Date(value);return isValidDate(date)?date:(console.warn("Created invalid date:",value),new Date)}catch(err){return console.error("Error creating date:",err),new Date}})(value);setDateRange((prev=>"start"===type&&prev.end&&newDate>prev.end?(dist.oR.warn("Start date cannot be later than end date"),prev):"end"===type&&prev.start&&newDate<prev.start?(dist.oR.warn("End date cannot be earlier than start date"),prev):{...prev,[type]:newDate}))}catch(err){console.error("Error parsing date:",err),dist.oR.error("Invalid date format")}},formatDateForInput=date=>date&&isValidDate(date)?date.toISOString().split("T")[0]:(console.warn("Invalid date detected when formatting for input:",date),(new Date).toISOString().split("T")[0]),formatDateForDisplay=date=>date&&isValidDate(date)?date.toLocaleDateString():(console.warn("Invalid date detected when formatting for display:",date),(new Date).toLocaleDateString());return(0,jsx_runtime.jsxs)("div",{className:"appointment-map-page",children:[(0,jsx_runtime.jsx)(dist.N9,{position:"top-right",autoClose:5e3}),(0,jsx_runtime.jsxs)("div",{className:"map-page-header",children:[(0,jsx_runtime.jsxs)("div",{className:"map-page-title",children:[(0,jsx_runtime.jsxs)("h2",{children:[(0,jsx_runtime.jsx)(fa.ZYm,{})," Appointment Map View"]}),(0,jsx_runtime.jsx)("p",{children:"View and plan on-site appointment routes (using Leaflet & OpenStreetMap)"})]}),(0,jsx_runtime.jsx)("div",{className:"map-page-actions",children:(0,jsx_runtime.jsxs)(chunk_AYJ5UCUI.N_,{to:"/dashboard/configure/onsite-booking",className:"back-to-calendar",children:[(0,jsx_runtime.jsx)(fa.QVr,{})," Back to Calendar View"]})})]}),(0,jsx_runtime.jsxs)("div",{className:"map-filters-container",children:[(0,jsx_runtime.jsxs)("div",{className:"map-date-filter",children:[(0,jsx_runtime.jsxs)("div",{className:"date-filter-group",children:[(0,jsx_runtime.jsx)("label",{htmlFor:"start-date",children:"Start Date:"}),(0,jsx_runtime.jsx)("input",{type:"date",id:"start-date",value:formatDateForInput(dateRange.start),onChange:e=>handleDateRangeChange("start",e.target.value)})]}),(0,jsx_runtime.jsxs)("div",{className:"date-filter-group",children:[(0,jsx_runtime.jsx)("label",{htmlFor:"end-date",children:"End Date:"}),(0,jsx_runtime.jsx)("input",{type:"date",id:"end-date",value:formatDateForInput(dateRange.end),onChange:e=>handleDateRangeChange("end",e.target.value)})]})]}),(0,jsx_runtime.jsxs)("div",{className:"map-team-filter",children:[(0,jsx_runtime.jsx)(fa.YXz,{}),(0,jsx_runtime.jsx)("label",{htmlFor:"team-select",children:"Select Team:"}),(0,jsx_runtime.jsxs)("select",{id:"team-select",value:selectedTeamId,onChange:event=>{const newTeamId=event.target.value;console.log(`[MapPage] Team selection changed: ${selectedTeamId} -> ${newTeamId}`),setSelectedTeamId(newTeamId)},disabled:loadingTeams,children:[(0,jsx_runtime.jsx)("option",{value:"all",children:"All Teams"}),loadingTeams&&(0,jsx_runtime.jsx)("option",{disabled:!0,children:"Loading..."}),!loadingTeams&&0===teams.length&&(0,jsx_runtime.jsx)("option",{disabled:!0,children:"No teams available"}),!loadingTeams&&teams.map((team=>(0,jsx_runtime.jsx)("option",{value:team.id,children:team.name},team.id)))]})]}),(0,jsx_runtime.jsxs)("button",{className:"print-button",onClick:()=>{var _teams$find;const printContent=filteredAppointments.map((appt=>{const props=appt.extendedProps||{},formattedDate=moment_default()(appt.start).format("YYYY-MM-DD"),formattedTime=moment_default()(appt.start).format("HH:mm"),address=props.formattedAddress||"No address";return`\n        <div class="print-appointment">\n          <h3>${props.clientName||"Unnamed Client"}</h3>\n          <p><strong>Date:</strong> ${formattedDate}</p>\n          <p><strong>Time:</strong> ${formattedTime}</p>\n          <p><strong>Service:</strong> ${props.serviceName||"N/A"}</p>\n          <p><strong>Team:</strong> ${props.teamName||"N/A"}</p>\n          <p><strong>Address:</strong> ${address}</p>\n          <p><strong>Contact:</strong> ${props.phoneNumber||"N/A"}</p>\n          <p><strong>Status:</strong> ${props.status||"N/A"}</p>\n        </div>\n      `})).join("<hr />"),printWindow=window.open("","_blank");printWindow.document.write(`\n      <html>\n        <head>\n          <title>Appointment List</title>\n          <style>\n            body {\n              font-family: Arial, sans-serif;\n              margin: 0;\n              padding: 20px;\n            }\n            h1 {\n              text-align: center;\n              margin-bottom: 20px;\n            }\n            .print-header {\n              margin-bottom: 30px;\n            }\n            .print-appointment {\n              margin-bottom: 15px;\n              page-break-inside: avoid;\n            }\n            hr {\n              border: none;\n              border-top: 1px dashed #ccc;\n              margin: 20px 0;\n            }\n            p {\n              margin: 5px 0;\n            }\n            .print-footer {\n              margin-top: 30px;\n              text-align: center;\n              font-size: 12px;\n              color: #777;\n            }\n            @media print {\n              button {\n                display: none;\n              }\n            }\n          </style>\n        </head>\n        <body>\n          <button onClick="window.print();" style="position: fixed; top: 20px; right: 20px; padding: 10px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">\n            Print This Page\n          </button>\n          \n          <div class="print-header">\n            <h1>Appointment List</h1>\n            <p><strong>Date Range:</strong> ${formatDateForDisplay(dateRange.start)} to ${formatDateForDisplay(dateRange.end)}</p>\n            <p><strong>Team:</strong> ${"all"===selectedTeamId?"All Teams":(null===(_teams$find=teams.find((t=>String(t.id)===String(selectedTeamId))))||void 0===_teams$find?void 0:_teams$find.name)||"Unknown Team"}</p>\n            <p><strong>Total Appointments:</strong> ${filteredAppointments.length}</p>\n          </div>\n          \n          ${printContent}\n          \n          <div class="print-footer">\n            <p>Printed on: ${(new Date).toLocaleString()}</p>\n          </div>\n        </body>\n      </html>\n    `),printWindow.document.close()},disabled:loading||0===filteredAppointments.length,children:[(0,jsx_runtime.jsx)(fa.n4o,{})," Print Appointment List"]})]}),loading?(0,jsx_runtime.jsxs)("div",{className:"map-loading-container",children:[(0,jsx_runtime.jsx)("div",{className:"map-loading-spinner"}),(0,jsx_runtime.jsx)("p",{children:"Loading appointment data..."})]}):error?(0,jsx_runtime.jsxs)("div",{className:"map-error-container",children:[(0,jsx_runtime.jsx)("p",{children:error}),(0,jsx_runtime.jsx)("button",{onClick:()=>window.location.reload(),children:"Retry"})]}):(0,jsx_runtime.jsxs)("div",{className:"map-container",children:[(0,jsx_runtime.jsx)(map_AppointmentMap,{appointments:filteredAppointments,onAppointmentSelect:appointment=>{console.log("Selected appointment:",appointment)}}),(0,jsx_runtime.jsxs)("div",{className:"map-info-panel",children:[(0,jsx_runtime.jsx)("h3",{children:"Instructions"}),(0,jsx_runtime.jsxs)("ul",{children:[(0,jsx_runtime.jsx)("li",{children:"The left panel shows appointments for the selected team and date range; click to see location on map"}),(0,jsx_runtime.jsx)("li",{children:"Click markers on the map to view details"}),(0,jsx_runtime.jsx)("li",{children:"Use the filters above to select date range and team"}),(0,jsx_runtime.jsxs)("li",{children:[(0,jsx_runtime.jsx)(fa.$Fg,{})," ",(0,jsx_runtime.jsx)("strong",{children:"New Feature: Route Planning"}),' - Click "Plan Route" button to generate the optimal driving route between all appointment points']}),(0,jsx_runtime.jsx)("li",{children:'Check "Optimize Route" to automatically calculate the shortest path order (recommended)'}),(0,jsx_runtime.jsx)("li",{children:"Route planning shows total distance and estimated time, helping teams plan their schedule efficiently"})]}),(0,jsx_runtime.jsxs)("div",{className:"map-stats",children:[(0,jsx_runtime.jsxs)("p",{children:["Currently Showing: ",(0,jsx_runtime.jsx)("strong",{children:filteredAppointments.length})," appointments","all"===selectedTeamId?" (All Teams)":` for ${(null===(_teams$find2=teams.find((t=>String(t.id)===String(selectedTeamId))))||void 0===_teams$find2?void 0:_teams$find2.name)||"Unknown Team"}`,filteredAppointments.length<allAppointments.length&&(0,jsx_runtime.jsxs)("span",{className:"filtered-count",children:[" (filtered from ",allAppointments.length," total)"]})]}),(0,jsx_runtime.jsxs)("p",{children:["Date Range: ",(0,jsx_runtime.jsxs)("strong",{children:[formatDateForDisplay(dateRange.start)," to ",formatDateForDisplay(dateRange.end)]})]})]})]})]})]})},pages_AppointmentMapPage=AppointmentMapPage_AppointmentMapPage;AppointmentMapPage_AppointmentMapPage.__docgenInfo={description:"",methods:[],displayName:"AppointmentMapPage"};var AuthContext=__webpack_require__("./src/context/AuthContext.js");const AppointmentMapPage_stories={title:"Pages/Appointment Map Page",tags:["backend"],component:pages_AppointmentMapPage,decorators:[Story=>(0,jsx_runtime.jsx)(AuthContext.cy.Provider,{value:{user:{plan:"enterprise"}},children:(0,jsx_runtime.jsx)(chunk_AYJ5UCUI.fS,{initialEntries:["/data/walkin-booking"],children:(0,jsx_runtime.jsx)(Story,{})})})]},Default=(args=>(0,jsx_runtime.jsx)(pages_AppointmentMapPage,{})).bind({});Default.args={};const __namedExportsOrder=["Default"];Default.parameters={...Default.parameters,docs:{...Default.parameters?.docs,source:{originalSource:"args => <AppointmentMapPage />",...Default.parameters?.docs?.source}}}}}]);