import { test, expect } from '@playwright/test';

test.describe('Marketing Page Icon Cloud', () => {
  test('should display the integrations icon cloud section and have no console errors', async ({ page }) => {
    const consoleMessages: string[] = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleMessages.push(`[CONSOLE ERROR]: ${msg.text()}`);
      }
    });

    // Navigate to the marketing page.
    await page.goto('http://localhost:3000/marketing/home');

    // Locate the section we added.
    const integrationsSection = page.locator('#integrations');

    // Assert that the section is visible.
    await expect(integrationsSection).toBeVisible({ timeout: 10000 });

    // Check for the title within the component.
    const title = integrationsSection.locator('h2');
    await expect(title).toHaveText('Seamlessly Connect With Your Favorite Platforms');

    // Take a screenshot for visual confirmation.
    await page.screenshot({ path: 'screenshots/icon-cloud-check-after-bg-fix.png', fullPage: true });

    // After the test, print any console errors found.
    if (consoleMessages.length > 0) {
      console.log('Browser console errors found:');
      console.log(consoleMessages.join('\n'));
    }

    // Fail the test if there were console errors.
    expect(consoleMessages).toHaveLength(0);
  });
}); 