import { ComponentType, SVGAttributes } from 'react';

declare module 'react-icons/ri' {
  export type IconType = ComponentType<SVGAttributes<SVGElement>>;
  
  export const RiDashboardLine: IconType;
  export const RiUser3Line: IconType;
  export const RiSettings4Line: IconType; 
  export const RiBarChart2Line: IconType;
  export const RiCalendarEventFill: IconType;
  export const RiNotification3Line: IconType;
  export const RiServerLine: IconType;
  export const RiBriefcase3Line: IconType;
  export const RiMenuFoldLine: IconType;
  export const RiMenuUnfoldLine: IconType;
  export const RiLogoutBoxLine: IconType;
  export const RiArrowRightUpLine: IconType;
  export const RiArrowRightDownLine: IconType;
  export const RiSearchLine: IconType;
  export const RiEditLine: IconType;
  export const RiMailLine: IconType;
  export const RiPhoneLine: IconType;
  export const RiBankCardLine: IconType;
  export const RiMenuLine: IconType;
  export const RiInformationLine: IconType;
  export const RiQuestionLin,e: IconType
};

declare module 'react-icons/ai' {
  export type IconType = ComponentType<SVGAttributes<SVGElement>>
};

declare module 'react-icons/bi' {
  export type IconType = ComponentType<SVGAttributes<SVGElement>>
};

declare module 'react-icons/fi' {
  export type IconType = ComponentType<SVGAttributes<SVGElement>>
};

declare module 'react-icons/hi' {
  export type IconType = ComponentType<SVGAttributes<SVGElement>>
};

declare module 'react-icons/md' {
  export type IconType = ComponentType<SVGAttributes<SVGElement>>
};

declare module 'react-icons/fa' {
  export type IconType = ComponentType<SVGAttributes<SVGElement>>
};