/**
 * Enhanced Mock Data Service
 * 提供更真实和动态的模拟数据
 */

// 数据接口定义
export interface KPIMetric {
  title: string;
  value: string | number;
  change: number;
  changeType: 'positive' | 'negative' | 'neutral';
  description: string;
  icon?: string;
  trend: 'up' | 'down' | 'stable';
}

export interface ChartDataPoint {
  date: string;
  chatBot: number;
  booking: number;
  content: number;
  api: number;
  users: number;
  revenue: number;
}

export interface SystemMetric {
  title: string;
  value: string;
  status: 'healthy' | 'warning' | 'critical';
  threshold: number;
  current: number;
  description: string;
  icon: string;
  trend: 'increasing' | 'decreasing' | 'stable';
}

export interface BusinessModule {
  name: string;
  value: number;
  change: string;
  trend: 'up' | 'down' | 'stable';
  icon: string;
  description: string;
  performance: number; // 0-100
}

/**
 * Mock数据服务 - 用于生成测试数据
 */
class MockDataService {
  private static instance: MockDataService;
  private updateInterval: NodeJS.Timeout | null = null;
  private subscribers: ((data: any) => void)[] = [];

  private constructor() {
    // 私有构造函数实现单例模式
  }

  public static getInstance(): MockDataService {
    if (!MockDataService.instance) {
      MockDataService.instance = new MockDataService();
    }
    return MockDataService.instance;
  }

  /**
   * 生成时间序列数据
   */
  public generateTimeSeriesData(days: number = 30): ChartDataPoint[] {
    const data: ChartDataPoint[] = [];
    const now = Date.now();
    const oneDayMs = 24 * 60 * 60 * 1000;

    for (let i = 0; i < days; i++) {
      const timestamp = now - (days - 1 - i) * oneDayMs;
      const date = new Date(timestamp);
      
      // 模拟周末数据降低
      const isWeekend = date.getDay() === 0 || date.getDay() === 6;
      const weekendMultiplier = isWeekend ? 0.7 : 1;
      
      // 模拟月度增长趋势
      const growthFactor = 1 + (i / days) * 0.1;
      
      data.push({
        date: date.toISOString().split('T')[0],
        chatBot: Math.floor((Math.random() * 50 + 100) * weekendMultiplier * growthFactor),
        booking: Math.floor((Math.random() * 40 + 60) * weekendMultiplier * growthFactor),
        content: Math.floor((Math.random() * 30 + 50) * weekendMultiplier * growthFactor),
        api: Math.floor((Math.random() * 20 + 30) * weekendMultiplier * growthFactor),
        users: Math.floor((Math.random() * 100 + 200) * weekendMultiplier * growthFactor),
        revenue: Math.floor((Math.random() * 1000 + 2000) * weekendMultiplier * growthFactor)
      });
    }

    return data;
  }

  /**
   * 生成核心KPI指标
   */
  public generateKPIMetrics(): KPIMetric[] {
    return [
      {
        title: "总用户数",
        value: this.formatNumber(12847 + Math.floor(Math.random() * 1000)),
        change: this.generateRandomChange(),
        changeType: 'positive',
        description: "本月新增",
        trend: 'up'
      },
      {
        title: "活跃用户",
        value: this.formatNumber(8934 + Math.floor(Math.random() * 500)),
        change: this.generateRandomChange(),
        changeType: 'positive',
        description: "日活跃用户",
        trend: 'up'
      },
      {
        title: "转换率",
        value: `${(15.8 + Math.random() * 3).toFixed(1)}%`,
        change: this.generateRandomChange(-2, 5),
        changeType: 'positive',
        description: "较上月提升",
        trend: 'up'
      },
      {
        title: "系统运行时间",
        value: "99.9%",
        change: 0.1,
        changeType: 'positive',
        description: "服务可用性",
        trend: 'stable'
      }
    ];
  }

  /**
   * 生成系统监控指标
   */
  public generateSystemMetrics(): SystemMetric[] {
    return [
      {
        title: "CPU使用率",
        value: `${45 + Math.floor(Math.random() * 20)}%`,
        status: 'healthy',
        threshold: 80,
        current: 45 + Math.random() * 20,
        description: "系统负载正常",
        icon: 'cpu',
        trend: 'stable'
      },
      {
        title: "内存使用",
        value: `${68 + Math.floor(Math.random() * 15)}%`,
        status: 'healthy',
        threshold: 85,
        current: 68 + Math.random() * 15,
        description: "内存使用合理",
        icon: 'memory',
        trend: 'increasing'
      },
      {
        title: "数据库连接",
        value: `${23 + Math.floor(Math.random() * 10)}/50`,
        status: 'healthy',
        threshold: 45,
        current: 23 + Math.random() * 10,
        description: "连接池充足",
        icon: 'database',
        trend: 'stable'
      },
      {
        title: "网络延迟",
        value: `${12 + Math.floor(Math.random() * 5)}ms`,
        status: 'healthy',
        threshold: 100,
        current: 12 + Math.random() * 5,
        description: "响应速度优秀",
        icon: 'network',
        trend: 'stable'
      }
    ];
  }

  /**
   * 生成业务模块数据
   */
  public generateBusinessModules(): BusinessModule[] {
    return [
      {
        name: "聊天机器人",
        value: 1247 + Math.floor(Math.random() * 200),
        change: "+12%",
        trend: 'up',
        icon: 'bot',
        description: "AI对话交互次数",
        performance: 85 + Math.random() * 10
      },
      {
        name: "预约系统",
        value: 856 + Math.floor(Math.random() * 150),
        change: "+8%",
        trend: 'up',
        icon: 'calendar',
        description: "在线预约完成数",
        performance: 78 + Math.random() * 15
      },
      {
        name: "内容生成",
        value: 634 + Math.floor(Math.random() * 100),
        change: "+15%",
        trend: 'up',
        icon: 'file-text',
        description: "AI内容生成次数",
        performance: 92 + Math.random() * 5
      },
      {
        name: "平台API",
        value: 423 + Math.floor(Math.random() * 80),
        change: "+5%",
        trend: 'stable',
        icon: 'zap',
        description: "第三方API调用",
        performance: 88 + Math.random() * 8
      }
    ];
  }

  /**
   * 生成AI洞察数据
   */
  public generateAIInsights() {
    return [
      {
        title: "dataInsight.userEngagementChange",
        description: `dataInsight.userEngagementChangeDesc`,
        confidence: "dataInsight.highConfidence",
        impact: `dataInsight.conversionRateIncrease`,
        actionable: "dataInsight.increaseMarketingActivities",
        type: "behavior"
      },
      {
        title: "dataInsight.leadQualityTrend",
        description: `dataInsight.leadQualityTrendDesc`,
        confidence: "dataInsight.mediumConfidence",
        impact: "dataInsight.salesConversionIncrease",
        actionable: "dataInsight.prepareSalesTeam",
        type: "prediction"
      },
      {
        title: "dataInsight.churnRiskAlert",
        description: `dataInsight.churnRiskAlertDesc`,
        confidence: "dataInsight.highConfidence",
        impact: "dataInsight.potentialRevenueLoss",
        actionable: "dataInsight.implementRetentionCampaigns",
        type: "risk"
      }
    ];
  }

  /**
   * 订阅实时数据更新
   */
  public subscribe(callback: (data: any) => void): () => void {
    this.subscribers.push(callback);
    return () => {
      const index = this.subscribers.indexOf(callback);
      if (index > -1) {
        this.subscribers.splice(index, 1);
      }
    };
  }

  /**
   * 开始实时数据更新
   */
  private startRealTimeUpdates() {
    this.updateInterval = setInterval(() => {
      const updateData = {
        timestamp: new Date().toISOString(),
        metrics: this.generateKPIMetrics(),
        systemStatus: this.generateSystemMetrics(),
        businessModules: this.generateBusinessModules()
      };

      this.subscribers.forEach(callback => {
        callback(updateData);
      });
    }, 30000); // 每30秒更新一次
  }

  /**
   * 停止实时更新
   */
  public stopRealTimeUpdates() {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
  }

  /**
   * 格式化数字显示
   */
  private formatNumber(num: number): string {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toString();
  }

  /**
   * 生成随机变化百分比
   */
  private generateRandomChange(min: number = -5, max: number = 15): number {
    return Number((Math.random() * (max - min) + min).toFixed(1));
  }

  /**
   * 获取随机未来日期
   */
  private getRandomFutureDate(): string {
    const now = new Date();
    const futureDate = new Date(now.getTime() + Math.random() * 30 * 24 * 60 * 60 * 1000);
    return futureDate.toISOString().split('T')[0];
  }
}

// 导出单例实例
export const mockDataService = MockDataService.getInstance();
export default MockDataService;