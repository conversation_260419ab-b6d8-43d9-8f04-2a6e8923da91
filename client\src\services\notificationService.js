import axiosInstance from '../api/axiosInstance';

/**
 * 获取通知规则设置
 * @param {string} settingType - 设置类型，例如："walkin" "onsite"
 * @returns {Promise<Object>} - 通知规则设置对象
 */
export const getNotificationRules = async (settingType) => {
  try {
    const response = await axiosInstance.get(`/api/settings/notifications/rules/${settingType}`);
    return response.data;
  } catch (error) {
    console.error(`获取${settingType}通知规则失败:`, error.response?.data || error);
    throw error.response?.data || error;
  }
};

/**
 * 更新通知规则设置
 * @param {string} settingType - 设置类型，例如："walkin" "onsite" 
 * @param {Object} rules - 通知规则对象
 * @returns {Promise<Object>} - 更新后的通知规则设置对象
 */
export const updateNotificationRules = async (settingType, rules) => {
  try {
    const response = await axiosInstance.put(`/api/settings/notifications/rules/${settingType}`, rules);
    return response.data;
  } catch (error) {
    console.error(`更新${settingType}通知规则失败:`, error.response?.data || error);
    throw error.response?.data || error;
  }
};

/**
 * 获取通知渠道设置
 * @returns {Promise<Object>} - 通知渠道设置对象
 */
export const getNotificationChannels = async () => {
  try {
    const response = await axiosInstance.get('/api/settings/notifications/channels');
    return response.data;
  } catch (error) {
    console.error('获取通知渠道设置失败:', error.response?.data || error);
    throw error.response?.data || error;
  }
};

/**
 * 更新通知渠道设置
 * @param {Object} channels - 通知渠道设置对象
 * @returns {Promise<Object>} - 更新后的通知渠道设置对象
 */
export const updateNotificationChannels = async (channels) => {
  try {
    const response = await axiosInstance.put('/api/settings/notifications/channels', channels);
    return response.data;
  } catch (error) {
    console.error('更新通知渠道设置失败:', error.response?.data || error);
    throw error.response?.data || error;
  }
};

/**
 * 测试通知渠道
 * @param {string} channelType - 通知渠道类型，例如："sms" "email" "push"
 * @param {Object} testData - 测试数据对象
 * @returns {Promise<Object>} - 测试结果对象
 */
export const testNotificationChannel = async (channelType, testData) => {
  try {
    const response = await axiosInstance.post(`/api/settings/notifications/channels/${channelType}/test`, testData);
    return response.data;
  } catch (error) {
    console.error(`测试${channelType}通知渠道失败:`, error.response?.data || error);
    throw error.response?.data || error;
  }
};

const config = {
  getNotificationRules,
  updateNotificationRules,
  getNotificationChannels,
  updateNotificationChannels,
  testNotificationChannel
};

export default config;
 