import { test, expect } from '@playwright/test';

test.describe('Logo Hover Only Img Glow', () => {
  test('logo hover should only glow img, no button background/border', async ({ page }) => {
    await page.goto('http://localhost:3000');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(1000);

    // 定位logo图片
    const logoImg = page.locator('img[alt="iTeraBiz Logo"]').first();
    await expect(logoImg).toBeVisible();

    // 悬停logo图片
    await logoImg.hover();
    await page.waitForTimeout(500);

    // 截图用于人工/自动比对
    await page.screenshot({ path: 'test-results/logo-hover-img-glow.png', fullPage: false });

    // 检查logo父级按钮的背景色和边框
    const parentBtn = await logoImg.evaluateHandle(el => el.closest('button'));
    const bg = await parentBtn.evaluate(el => window.getComputedStyle(el).backgroundColor);
    const border = await parentBtn.evaluate(el => window.getComputedStyle(el).border);
    // 允许透明或完全无色，不能是白色/灰色/有边框
    expect(bg === 'rgba(0, 0, 0, 0)' || bg === 'transparent').toBeTruthy();
    expect(border === '0px none rgb(0, 0, 0)' || border === 'none').toBeTruthy();

    // 检查图片本身的box-shadow
    const boxShadow = await logoImg.evaluate(el => window.getComputedStyle(el).boxShadow);
    expect(boxShadow).toContain('rgb(139, 92, 246)'); // 8b5cf6
  });
}); 