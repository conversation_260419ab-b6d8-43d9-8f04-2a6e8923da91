/**
 * 通用设置类型定义
 */

export interface SettingsFormProps<T = any> {
  settings: T;
  onSettingsChange: (settings: Partial<T>) => void;
  onSave: () => Promise<void>;
  isLoading: boolean;
  isSaving: boolean;
}

export interface BaseSettings {
  id?: string;
  name: string;
  description?: string;
  enabled: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export interface WorkingHours {
  enabled: boolean;
  timezone: string;
  schedule: {
    [key: string]: {
      start: string;
      end: string;
      closed: boolean;
    };
  };
}

export interface NotificationTemplate {
  enabled: boolean;
  subject: string;
  content: string;
}

export interface NotificationSettings {
  email: {
    enabled: boolean;
    provider: 'smtp' | 'sendgrid' | 'mailgun' | 'ses';
    fromEmail: string;
    fromName: string;
    smtpHost?: string;
    smtpPort?: number;
    smtpUser?: string;
    smtpPassword?: string;
    apiKey?: string;
  };
  sms: {
    enabled: boolean;
    provider: 'twilio' | 'aliyun' | 'tencent' | 'local';
    apiKey?: string;
    signature?: string;
    templateId?: string;
  };
  push: {
    enabled: boolean;
    fcmServerKey?: string;
    apnsKeyId?: string;
    apnsTeamId?: string;
  };
  templates: {
    [key: string]: NotificationTemplate;
  };
}

export interface Staff {
  id: string;
  name: string;
  email: string;
  phone?: string;
  skills: string[];
  availability: WorkingHours;
  isActive: boolean;
}

export interface ServiceType {
  id: string;
  name: string;
  description?: string;
  duration: number; // 分钟
  price?: number;
  isActive: boolean;
  icon?: string;
}

export interface SettingsTabProps {
  label: string;
  value: string;
  icon?: React.ComponentType<any>;
}

export interface SettingsSection {
  id: string;
  title: string;
  description?: string;
  component: React.ComponentType<any>;
  props?: Record<string, any>;
}

export type SettingsValidationError = {
  field: string;
  message: string;
};

export interface SettingsFormState<T> {
  values: T;
  errors: Record<string, string>;
  touched: Record<string, boolean>;
  isValid: boolean;
  isDirty: boolean;
}