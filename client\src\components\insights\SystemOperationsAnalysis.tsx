import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
// import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
// import { Tabs, Tabs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Activity, AlertTriangle, Clock, Server, Cpu, HardDrive, Database } from 'lucide-react';
import { Skeleton } from '../ui/skeleton';

interface SystemOperationsAnalysisProps {
  timeRange?: string;
  className?: string;
}

// Simple KPI Card component
interface KPICardProps {
  title: string;
  value: string;
  change: { value: string; type: 'positive' | 'negative' };
  icon: React.ReactNode;
  description: string;
}

const KPICard: React.FC<KPICardProps> = ({ title, value, change, icon, description }) => (
  <Card>
    <CardContent className="p-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          {icon}
          <span className="text-sm font-medium text-muted-foreground">{title}</span>
        </div>
        <span className={`text-xs ${change.type === 'positive' ? 'text-green-600' : 'text-red-600'}`}>
          {change.value}
        </span>
      </div>
      <div className="mt-2">
        <span className="text-2xl font-bold">{value}</span>
        <p className="text-xs text-muted-foreground mt-1">{description}</p>
      </div>
    </CardContent>
  </Card>
);

export function SystemOperationsAnalysis({ 
  timeRange = 'last7days', 
  className = '' 
}: SystemOperationsAnalysisProps) {
  const [operationsData, setOperationsData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        // Simulate API call
        setTimeout(() => {
          setOperationsData(getMockSystemOperationsData());
          setIsLoading(false);
        }, 1000);
      } catch (err) {
        console.error('Failed to fetch system operations data:', err);
        setError('Failed to load data, please try again later');
        setIsLoading(false);
      }
    };

    fetchData();
  }, [timeRange]);

  // Loading state
  if (isLoading) {
    return (
      <div className={className}>
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <Card key={i}>
                <CardContent className="p-6">
                  <Skeleton className="h-4 w-20 mb-2" />
                  <Skeleton className="h-8 w-16 mb-2" />
                  <Skeleton className="h-3 w-24" />
                </CardContent>
              </Card>
            ))}
          </div>
          <Skeleton className="h-[400px] w-full" />
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className={className}>
        <div className="rounded-lg border border-red-200 bg-red-50 p-4">
          <div className="flex items-center">
            <AlertTriangle className="h-4 w-4 text-red-600 mr-2" />
            <span className="text-red-800">{error}</span>
          </div>
        </div>
      </div>
    );
  }

  // No data state
  if (!operationsData) {
    return (
      <div className={className}>
        <Card>
          <CardHeader>
            <CardTitle>System Operations Analysis</CardTitle>
            <CardDescription>No system operations data available</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-center py-10 text-muted-foreground">
              No relevant system operations data found
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const { systemHealth } = operationsData;

  return (
    <div className={className}>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold tracking-tight">System Operations Analysis</h2>
            <p className="text-muted-foreground">Monitor system performance and operations</p>
          </div>
        </div>
        
        {/* KPI Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <KPICard
            title="System Uptime"
            value={`${systemHealth.uptime}%`}
            change={{ value: '+0.1%', type: 'positive' }}
            icon={<Server className="h-5 w-5" />}
            description="Last 30 days"
          />
          <KPICard
            title="API Calls"
            value={operationsData.totalApiCalls?.toLocaleString() || '0'}
            change={{ value: '+15.2%', type: 'positive' }}
            icon={<Activity className="h-5 w-5" />}
            description="Last 24 hours"
          />
          <KPICard
            title="Response Time"
            value={`${systemHealth.avgResponseTime}ms`}
            change={{ value: '-8.3%', type: 'positive' }}
            icon={<Clock className="h-5 w-5" />}
            description="Average"
          />
          <KPICard
            title="Active Modules"
            value={Object.keys(operationsData.moduleUsage).length.toString()}
            change={{ value: '+2', type: 'positive' }}
            icon={<Activity className="h-5 w-5" />}
            description="Currently running"
          />
        </div>

        {/* System Status */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm font-medium">CPU Usage</CardTitle>
                <Cpu className="h-4 w-4 text-muted-foreground" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex items-baseline gap-2">
                  <span className="text-2xl font-bold">{systemHealth.cpuUsage}%</span>
                  <Badge variant="secondary" className="bg-green-100 text-green-700">
                    Normal
                  </Badge>
                </div>
                <Progress value={systemHealth.cpuUsage} className="h-2" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm font-medium">Memory Usage</CardTitle>
                <HardDrive className="h-4 w-4 text-muted-foreground" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex items-baseline gap-2">
                  <span className="text-2xl font-bold">{systemHealth.memoryUsage}%</span>
                  <Badge variant="secondary" className="bg-yellow-100 text-yellow-700">
                    Warning
                  </Badge>
                </div>
                <Progress value={systemHealth.memoryUsage} className="h-2" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm font-medium">Database</CardTitle>
                <Database className="h-4 w-4 text-muted-foreground" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex items-baseline gap-2">
                  <span className="text-2xl font-bold">{systemHealth.dbConnections}</span>
                  <Badge variant="secondary" className="bg-green-100 text-green-700">
                    Active
                  </Badge>
                </div>
                <p className="text-xs text-muted-foreground">Database connections</p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Performance Summary */}
        <Card>
          <CardHeader>
            <CardTitle>System Performance Summary</CardTitle>
            <CardDescription>Key performance indicators</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {operationsData.cpuTrend[operationsData.cpuTrend.length - 1]}%
                </div>
                <div className="text-sm text-muted-foreground">Current CPU</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {operationsData.memoryTrend[operationsData.memoryTrend.length - 1]}%
                </div>
                <div className="text-sm text-muted-foreground">Current Memory</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">
                  {operationsData.dbTrend[operationsData.dbTrend.length - 1]}
                </div>
                <div className="text-sm text-muted-foreground">DB Connections</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {systemHealth.avgResponseTime}ms
                </div>
                <div className="text-sm text-muted-foreground">Avg Response</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

function getMockSystemOperationsData() {
  return {
    systemHealth: {
      uptime: 99.9,
      cpuUsage: 45,
      memoryUsage: 68,
      dbConnections: 24,
      avgResponseTime: 245
    },
    totalApiCalls: 1200000,
    moduleUsage: {
      chat: 85, analytics: 92, booking: 78, content: 88
    },
    cpuTrend: [40, 45, 42, 48, 44, 46, 45],
    memoryTrend: [65, 68, 70, 72, 69, 67, 68],
    dbTrend: [20, 24, 22, 26, 24, 23, 24]
  };
} 