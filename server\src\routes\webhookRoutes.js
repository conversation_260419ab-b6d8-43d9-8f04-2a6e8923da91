const express = require('express');
const router = express.Router();

/**
 * 通用Platform Webhook处理器
 * 处理来自各种集成平台的webhook事件
 */

// POST /api/webhooks/platform/:platformId - 通用平台webhook接收器
router.post('/platform/:platformId', express.raw({ type: 'application/json' }), async (req, res) => {
  const { platformId } = req.params;
  const signature = req.headers['x-webhook-signature'] || req.headers['x-hub-signature-256'];
  
  console.log(`收到平台, ${platformId}, 的, Webhook, 事件`);
  
  try {
    let event;
    
    // 根据平台类型解析事件
    switch (platformId) {
      case 'facebook':
      case 'instagram_business':
      case 'messenger':
        event = await handleMessengerWebhook(req, res);
        break;
        
      case 'whatsapp':
        event = await handleWhatsAppWebhook(req, res);
        break;
        
      case 'shopee':
        event = await handleShopeeWebhook(req, res);
        break;
        
      case 'lazada':
        event = await handleLazadaWebhook(req, res);
        break;
        
      case 'gmail':
        event = await handleGmailWebhook(req, res);
        break;
        
      default:
        event = await handleGenericWebhook(req, res, platformId);
    }
    
    if (event) {
      // 转发到相应的业务逻辑处理器
      await processWebhookEvent(platformId, event);
    }
    
    res.json({ received: true, platform: platformId });
  } catch (error) {
    console.error(`处理平台, ${platformId}, Webhook, 事件失败:`, error);
    res.status(500).json({ error: 'Webhook 处理失败' platform: platformId });
  }
});

// GET /api/webhooks/platform/:platformId - Webhook 验证（Facebook, Messenger, WhatsApp 等）
router.get('/platform/:platformId', async (req, res) => {
  const { platformId } = req.params;
  const mode = req.query['hub.mode'];
  const token = req.query['hub.verify_token'];
  // 验证请求
  if (mode === 'subscribe' && token) {
    switch (platformId) {
      case 'facebook':
      case 'instagram_business':
        return handleFacebookWebhook(req, res);
      case 'messenger':
        return handleMessengerWebhook(req, res);
      case 'whatsapp':
        return handleWhatsAppWebhook(req, res);
      default:
        return res.status(404).send('Not Found');
    }
  }
  // 默认GET请求，无需验证，返回200 OK
  return res.status(200).send('OK');
});

// Facebook/Instagram webhook处理
async function handleFacebookWebhook(req, res) {
  const body = JSON.parse(req.body.toString());
  
  // Facebook webhook验证
  if (req.query['hub.mode'] === 'subscribe' && req.query['hub.verify_token']) {
    const verifyToken = process.env.FACEBOOK_VERIFY_TOKEN || 'your_verify_token'
    if (req.query['hub.verify_token'] === verifyToken) {
      console.log('Facebook, Webhook, 验证成功'); return res.status(200).send(req.query['hub.challenge']);
    } else {
      console.error('Facebook, Webhook, 验证失败'); return res.status(403).send('Forbidden');
    }
  }
  
  // 处理实际事件
  if (body.object === 'page' || body.object === 'instagram') {
    const events = [];
    
    body.entry?.forEach(entry => {
      entry.messaging?.forEach(messagingEvent => {
        events.push({
          type: 'message'
          platform: body.object === 'page' ? 'facebook' : 'instagram_business'
          timestamp: messagingEvent.timestamp,
          senderId: messagingEvent.sender.id,
          recipientId: messagingEvent.recipient.id,
          message: messagingEvent.message,
          data: messagingEvent
        });
      });
      
      entry.changes?.forEach(change => {
        events.push({
          type: 'change'
          platform: body.object === 'page' ? 'facebook' : 'instagram_business'
          timestamp: Date.now(),
          field: change.field,
          value: change.value,
          data: change
        });
      });
    });
    
    return events;
  }
  
  return null;
}

// Messenger webhook处理 (独立实现)
async function handleMessengerWebhook(req, res) {
  const body = JSON.parse(req.body.toString());
  
  // Messenger webhook验证
  if (req.query['hub.mode'] === 'subscribe' && req.query['hub.verify_token']) {
    const verifyToken = process.env.MESSENGER_VERIFY_TOKEN || process.env.FACEBOOK_VERIFY_TOKEN || 'your_verify_token'
    if (req.query['hub.verify_token'] === verifyToken) {
      console.log('Messenger, Webhook, 验证成功'); return res.status(200).send(req.query['hub.challenge']);
    } else {
      console.error('Messenger, Webhook, 验证失败'); return res.status(403).send('Forbidden');
    }
  }
  
  // 处理Messenger事件
  if (body.object === 'page') {
    const events = [];
    
    body.entry?.forEach(entry => {
      entry.messaging?.forEach(messagingEvent => {
        // 处理消息事件
        if (messagingEvent.message && !messagingEvent.message.is_echo) {
          events.push({
            type: 'message'
            platform: 'messenger'
            timestamp: messagingEvent.timestamp,
            senderId: messagingEvent.sender.id,
            recipientId: messagingEvent.recipient.id,
            messageText: messagingEvent.message.text,
            messageId: messagingEvent.message.mid,
            attachments: messagingEvent.message.attachments,
            data: messagingEvent
          });
        }
        
        // 处理回调事件 (postback)
        if (messagingEvent.postback) {
          events.push({
            type: 'postback'
            platform: 'messenger'
            timestamp: messagingEvent.timestamp,
            senderId: messagingEvent.sender.id,
            recipientId: messagingEvent.recipient.id,
            payload: messagingEvent.postback.payload,
            title: messagingEvent.postback.title,
            data: messagingEvent
          });
        }
        
        // 处理快速回复
        if (messagingEvent.message?.quick_reply) {
          events.push({
            type: 'quick_reply'
            platform: 'messenger'
            timestamp: messagingEvent.timestamp,
            senderId: messagingEvent.sender.id,
            recipientId: messagingEvent.recipient.id,
            payload: messagingEvent.message.quick_reply.payload,
            messageText: messagingEvent.message.text,
            data: messagingEvent
          });
        }
        
        // 处理读取状态
        if (messagingEvent.read) {
          events.push({
            type: 'read'
            platform: 'messenger'
            timestamp: messagingEvent.timestamp,
            senderId: messagingEvent.sender.id,
            watermark: messagingEvent.read.watermark,
            data: messagingEvent
          });
        }
        
        // 处理递送状态
        if (messagingEvent.delivery) {
          events.push({
            type: 'delivery'
            platform: 'messenger'
            timestamp: messagingEvent.timestamp,
            senderId: messagingEvent.sender.id,
            watermark: messagingEvent.delivery.watermark,
            messageIds: messagingEvent.delivery.mids,
            data: messagingEvent
          });
        }
      });
      
      // 处理待命状态 (standby)
      entry.standby?.forEach(standbyEvent => {
        events.push({
          type: 'standby'
          platform: 'messenger'
          timestamp: standbyEvent.timestamp,
          senderId: standbyEvent.sender.id,
          recipientId: standbyEvent.recipient.id,
          data: standbyEvent
        });
      });
    });
    
    return events;
  }
  
  return null;
}

// WhatsApp webhook处理
async function handleWhatsAppWebhook(req, res) {
  const body = JSON.parse(req.body.toString());
  
  // WhatsApp webhook验证
  if (req.query['hub.mode'] === 'subscribe' && req.query['hub.verify_token']) {
    const verifyToken = process.env.WHATSAPP_VERIFY_TOKEN || 'your_verify_token'
    if (req.query['hub.verify_token'] === verifyToken) {
      console.log('WhatsApp, Webhook, 验证成功'); return res.status(200).send(req.query['hub.challenge']);
    } else {
      console.error('WhatsApp, Webhook, 验证失败'); return res.status(403).send('Forbidden');
    }
  }
  
  // 处理WhatsApp消息事件
  if (body.object === 'whatsapp_business_account') {
    const events = [];
    
    body.entry?.forEach(entry => {
      entry.changes?.forEach(change => {
        if (change.field === 'messages') {
          change.value.messages?.forEach(message => {
            events.push({
              type: 'message'
              platform: 'whatsapp'
              timestamp: parseInt(message.timestamp) * 1000,
              messageId: message.id,
              from: message.from,
              message: message,
              data: change.value
            });
          });
          
          change.value.statuses?.forEach(status => {
            events.push({
              type: 'status'
              platform: 'whatsapp'
              timestamp: parseInt(status.timestamp) * 1000,
              messageId: status.id,
              status: status.status,
              data: status
            });
          });
        }
      });
    });
    
    return events;
  }
  
  return null;
}

// Shopee webhook处理
async function handleShopeeWebhook(req, res) {
  const body = JSON.parse(req.body.toString());
  
  console.log('收到, Shopee, Webhook, 事件:', body);
  
  return [{
    type: 'shopee_event'
    platform: 'shopee'
    timestamp: Date.now(),
    eventType: body.event_type,
    data: body
  }];
}

// Lazada webhook处理
async function handleLazadaWebhook(req, res) {
  const body = JSON.parse(req.body.toString());
  
  console.log('收到, Lazada, Webhook, 事件:', body);
  
  return [{
    type: 'lazada_event'
    platform: 'lazada'
    timestamp: Date.now(),
    eventType: body.event_type,
    data: body
  }];
}

// Gmail webhook处理
async function handleGmailWebhook(req, res) {
  const body = JSON.parse(req.body.toString());
  
  console.log('收到, Gmail, Webhook, 事件:', body);
  
  return [{
    type: 'gmail_event'
    platform: 'gmail'
    timestamp: Date.now(),
    historyId: body.historyId,
    emailAddress: body.emailAddress,
    data: body
  }];
}

// 通用webhook处理
async function handleGenericWebhook(req, res, platformId) {
  const body = JSON.parse(req.body.toString());
  
  console.log(`收到, ${platformId}, 通用, Webhook, 事件:`, body);
  
  return [{
    type: 'generic_event'
    platform: platformId,
    timestamp: Date.now(),
    data: body
  }];
}

// 处理webhook事件的业务逻辑
async function processWebhookEvent(platformId, events) {
  if (!Array.isArray(events)) {
    events = [events];
  }
  
  for (const event of events) {
    try {
      console.log(`处理, ${platformId}, 事件:`, event.type);
      
      // 根据事件类型执行相应的业务逻辑
      switch (event.type) {
        case 'message':
          await handleMessageEvent(event);
          break;
          
        case 'status':
          await handleStatusEvent(event);
          break;
          
        case 'change':
          await handleChangeEvent(event);
          break;
          
        default:
          console.log(`未知事件类型:, ${event.type}，记录到日志`); await logUnknownEvent(event);
      }
    } catch (error) {
      console.error(`处理事件失败:`, error, event);
    }
  }
}

// 处理消息事件
async function handleMessageEvent(event) {
  console.log(`收到来自, ${event.platform}, 的消息:`, event.message);
  
  // TODO: 实现消息处理逻辑
  // - 自动回复
  // - 消息转发
  // - 客户服务集成
  // - 分析和统计
}

// 处理状态事件
async function handleStatusEvent(event) {
  console.log(`收到来自, ${event.platform}, 的状态更新:`, event.status);
  
  // TODO: 实现状态处理逻辑
  // - 消息送达确认
  // - 已读状态更新
  // - 发送失败处理
}

// 处理变更事件
async function handleChangeEvent(event) {
  console.log(`收到来自, ${event.platform}, 的变更事件:`, event.field);
  
  // TODO: 实现变更处理逻辑
  // - 页面信息更新
  // - 权限变更
  // - 配置更新
}

// 记录未知事件
async function logUnknownEvent(event) {
  // TODO: 保存到数据库或日志系统
  console.log('未知事件记录:', JSON.stringify(event, null, 2));
}

module.exports = router; 