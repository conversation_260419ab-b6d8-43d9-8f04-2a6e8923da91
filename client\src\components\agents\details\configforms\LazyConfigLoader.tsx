import React, { Suspense, lazy, useState, useEffect } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { 
  Loader2, 
  AlertCircle, 
  RefreshCw
} from 'lucide-react'
import { cn } from '@/lib/utils'

// 懒加载配置组件
const EnhancedAiAutoReplyConfigForm = lazy(() => 
  import('./EnhancedAiAutoReplyConfigForm').then(module => ({
    default: module.default
  }))
);

const BookingOnsiteConfigForm = lazy(() => 
  import('./BookingOnsiteConfigForm').then(module => ({
    default: module.default
  }))
);

const BookingWalkinConfigForm = lazy(() => 
  import('./BookingWalkinConfigForm').then(module => ({
    default: module.default
  }))
);

const ContentGeneratorConfigForm = lazy(() => 
  import('./ContentGeneratorConfigForm').then(module => ({
    default: module.default
  }))
);

const LeadGenerationConfigForm = lazy(() => 
  import('./LeadGenerationConfigForm').then(module => ({
    default: module.default
  }))
);

interface LazyConfigLoaderProps {
  agentType: 'ai_auto_reply' | 'booking_onsite' | 'booking_walkin' | 'content_generator' | 'lead_generation';
  config: Record<string, any>;
  onConfigChange: (key: string, value: any) => void;
  className?: string;
}

// 错误边界组件
interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

class ConfigErrorBoundary extends React.Component<
  React.PropsWithChildren<{ onRetry: () => void }>,
  ErrorBoundaryState
> {
  constructor(props: React.PropsWithChildren<{ onRetry: () => void }>) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Config form error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <Card className="border-red-200">
          <CardContent className="pt-6">
            <div className="text-center space-y-4">
              <AlertCircle className="h-12 w-12 text-red-500 mx-auto" />
              <div>
                <h3 className="text-lg font-semibold text-red-900">Configuration Error</h3>
                <p className="text-sm text-red-700 mt-1">
                  Failed to load the configuration form. This might be due to a network issue or component error.
                </p>
                {this.state.error && (
                  <details className="mt-2 text-xs text-red-600">
                    <summary className="cursor-pointer">Error Details</summary>
                    <pre className="mt-1 text-left bg-red-50 p-2 rounded overflow-auto">
                      {this.state.error.message}
                    </pre>
                  </details>
                )}
              </div>
              <Button 
                onClick={() => {
                  this.setState({ hasError: false, error: undefined });
                  this.props.onRetry();
                }}
                className="flex items-center gap-2"
              >
                <RefreshCw className="h-4 w-4" />
                Retry Loading
              </Button>
            </div>
          </CardContent>
        </Card>
      );
    }

    return this.props.children;
  }
}

// 加载状态组件
const LoadingFallback: React.FC<{ agentType: string }> = ({ agentType }) => {
  const [loadingProgress, setLoadingProgress] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setLoadingProgress(prev => {
        if (prev >= 90) return prev;
        return prev + Math.random() * 15;
      });
    }, 200);

    return () => clearInterval(interval);
  }, []);

  return (
    <Card>
      <CardContent className="pt-6">
        <div className="text-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin mx-auto text-primary" />
          <div>
            <h3 className="text-lg font-semibold">Loading Configuration</h3>
            <p className="text-sm text-muted-foreground">
              Preparing {agentType.replace('_', ' ')} configuration form...
            </p>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-primary h-2 rounded-full transition-all duration-300"
              style={{ width: `${loadingProgress}%` }}
            />
          </div>
          <p className="text-xs text-muted-foreground">
            {Math.round(loadingProgress)}% loaded
          </p>
        </div>
      </CardContent>
    </Card>
  );
};

const LazyConfigLoader: React.FC<LazyConfigLoaderProps> = ({
  agentType,
  config,
  onConfigChange,
  className
}) => {
  const [retryKey, setRetryKey] = useState(0);

  const handleRetry = () => {
    setRetryKey(prev => prev + 1);
  };

  // 根据代理类型选择对应的组件
  const getConfigComponent = () => {
    switch (agentType) {
      case 'ai_auto_reply':
        return EnhancedAiAutoReplyConfigForm;
      case 'booking_onsite':
        return BookingOnsiteConfigForm;
      case 'booking_walkin':
        return BookingWalkinConfigForm;
      case 'content_generator':
        return ContentGeneratorConfigForm;
      case 'lead_generation':
        return LeadGenerationConfigForm;
      default:
        throw new Error(`Unknown agent type: ${agentType}`);
    }
  };

  const ConfigComponent = getConfigComponent();

  return (
    <div className={cn("space-y-4", className)} key={retryKey}>
      <ConfigErrorBoundary onRetry={handleRetry}>
        <Suspense fallback={<LoadingFallback agentType={agentType} />}>
          <ConfigComponent
            config={config}
            onConfigChange={onConfigChange}
          />
        </Suspense>
      </ConfigErrorBoundary>
    </div>
  );
};

export default LazyConfigLoader;
