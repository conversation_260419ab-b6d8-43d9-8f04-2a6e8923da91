-- ====================================================
-- Supabase ibuddy2 项目数据库表创建脚本
-- ====================================================

-- 1. 创建 agents 主表
CREATE TABLE IF NOT EXISTS public.agents (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    name text NOT NULL,
    agent_type text NOT NULL CHECK (agent_type IN (
        'AI_AUTO_REPLY', 
        'CONTENT_GENERATOR', 
        'LEAD_GENERATION',
        'CUSTOMER_SERVICE',
        'DATA_ANALYST'
    )),
    status text NOT NULL DEFAULT 'DRAFT' CHECK (status IN (
        'DRAFT', 
        'ACTIVE', 
        'INACTIVE', 
        'MAINTENANCE'
    )),
    description text,
    configuration jsonb DEFAULT '{}',
    metadata jsonb DEFAULT '{}',
    created_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- 2. 创建 agent_settings 表
CREATE TABLE IF NOT EXISTS public.agent_settings (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    agent_id uuid NOT NULL REFERENCES public.agents(id) ON DELETE CASCADE,
    name text,
    description text,
    platforms jsonb DEFAULT '[]',
    config jsonb DEFAULT '{}',
    deployment_preferences jsonb DEFAULT '{}',
    created_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
    UNIQUE(agent_id)
);

-- 3. 创建 agent_data 表（用于性能数据）
CREATE TABLE IF NOT EXISTS public.agent_data (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    agent_id uuid NOT NULL REFERENCES public.agents(id) ON DELETE CASCADE,
    timestamp timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
    key text NOT NULL,
    value numeric,
    metadata jsonb DEFAULT '{}',
    created_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- 4. 创建 agent_logs 表（用于日志）
CREATE TABLE IF NOT EXISTS public.agent_logs (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    agent_id uuid NOT NULL REFERENCES public.agents(id) ON DELETE CASCADE,
    timestamp timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
    level text NOT NULL CHECK (level IN ('DEBUG', 'INFO', 'WARNING', 'ERROR')),
    message text NOT NULL,
    metadata jsonb DEFAULT '{}',
    created_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- ====================================================
-- 索引优化
-- ====================================================

-- agents 表索引
CREATE INDEX IF NOT EXISTS idx_agents_status ON public.agents(status);
CREATE INDEX IF NOT EXISTS idx_agents_agent_type ON public.agents(agent_type);
CREATE INDEX IF NOT EXISTS idx_agents_created_at ON public.agents(created_at);

-- agent_settings 表索引
CREATE INDEX IF NOT EXISTS idx_agent_settings_agent_id ON public.agent_settings(agent_id);

-- agent_data 表索引
CREATE INDEX IF NOT EXISTS idx_agent_data_agent_id ON public.agent_data(agent_id);
CREATE INDEX IF NOT EXISTS idx_agent_data_timestamp ON public.agent_data(timestamp);
CREATE INDEX IF NOT EXISTS idx_agent_data_key ON public.agent_data(key);

-- agent_logs 表索引
CREATE INDEX IF NOT EXISTS idx_agent_logs_agent_id ON public.agent_logs(agent_id);
CREATE INDEX IF NOT EXISTS idx_agent_logs_timestamp ON public.agent_logs(timestamp);
CREATE INDEX IF NOT EXISTS idx_agent_logs_level ON public.agent_logs(level);

-- ====================================================
-- 触发器：自动更新 updated_at 字段
-- ====================================================

-- 创建更新时间戳函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = timezone('utc'::text, now());
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为 agents 表添加触发器
DROP TRIGGER IF EXISTS update_agents_updated_at ON public.agents;
CREATE TRIGGER update_agents_updated_at
    BEFORE UPDATE ON public.agents
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 为 agent_settings 表添加触发器
DROP TRIGGER IF EXISTS update_agent_settings_updated_at ON public.agent_settings;
CREATE TRIGGER update_agent_settings_updated_at
    BEFORE UPDATE ON public.agent_settings
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- ====================================================
-- 行级安全策略 (RLS)
-- ====================================================

-- 启用行级安全
ALTER TABLE public.agents ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.agent_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.agent_data ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.agent_logs ENABLE ROW LEVEL SECURITY;

-- agents 表策略
CREATE POLICY "Enable read access for all users" ON public.agents
    FOR SELECT USING (true);

CREATE POLICY "Enable insert for authenticated users only" ON public.agents
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Enable update for authenticated users only" ON public.agents
    FOR UPDATE USING (auth.role() = 'authenticated');

CREATE POLICY "Enable delete for authenticated users only" ON public.agents
    FOR DELETE USING (auth.role() = 'authenticated');

-- agent_settings 表策略
CREATE POLICY "Enable read access for all users" ON public.agent_settings
    FOR SELECT USING (true);

CREATE POLICY "Enable all for authenticated users" ON public.agent_settings
    FOR ALL USING (auth.role() = 'authenticated');

-- agent_data 表策略
CREATE POLICY "Enable read access for all users" ON public.agent_data
    FOR SELECT USING (true);

CREATE POLICY "Enable all for authenticated users" ON public.agent_data
    FOR ALL USING (auth.role() = 'authenticated');

-- agent_logs 表策略
CREATE POLICY "Enable read access for all users" ON public.agent_logs
    FOR SELECT USING (true);

CREATE POLICY "Enable all for authenticated users" ON public.agent_logs
    FOR ALL USING (auth.role() = 'authenticated');

-- ====================================================
-- 插入初始测试数据
-- ====================================================

-- 插入测试代理
INSERT INTO public.agents (id, name, agent_type, status, description) VALUES
('e41595bf-4f41-4b14-8efd-3543abb17c58', 'AI Auto-Reply Agent', 'AI_AUTO_REPLY', 'ACTIVE', 'Automatically replies to customer inquiries'),
('f51696c0-5f52-5c25-9f0e-4654bcc28d69', 'Content Generator', 'CONTENT_GENERATOR', 'INACTIVE', 'Generates marketing content'),
('g61797d1-6g63-6d36-a01f-5765cdd39e7a', 'Lead Generation Bot', 'LEAD_GENERATION', 'ACTIVE', 'Identifies and qualifies potential leads')
ON CONFLICT (id) DO NOTHING;

-- 插入测试设置
INSERT INTO public.agent_settings (agent_id, name, description, platforms, config) VALUES
('e41595bf-4f41-4b14-8efd-3543abb17c58', 'AI Auto-Reply Agent', 'Automatically replies to customer inquiries', 
 '["email", "chat", "social"]'::jsonb, 
 '{"response_time": "fast", "tone": "professional"}'::jsonb)
ON CONFLICT (agent_id) DO NOTHING;

-- ====================================================
-- 权限设置
-- ====================================================

-- 为 authenticated 角色授予权限
GRANT ALL ON public.agents TO authenticated;
GRANT ALL ON public.agent_settings TO authenticated;
GRANT ALL ON public.agent_data TO authenticated;
GRANT ALL ON public.agent_logs TO authenticated;

-- 为 anon 角色授予只读权限
GRANT SELECT ON public.agents TO anon;
GRANT SELECT ON public.agent_settings TO anon;
GRANT SELECT ON public.agent_data TO anon;
GRANT SELECT ON public.agent_logs TO anon; 