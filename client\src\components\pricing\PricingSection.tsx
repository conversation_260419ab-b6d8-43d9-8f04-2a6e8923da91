import React, { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { <PERSON>, Button } from '@/components/compat/react-bootstrap-compat';
import { Users, Building, Crown } from 'lucide-react';

export interface PricingSectionProps {
  onSelectPlan?: (planId: string) => void;
}

interface PricingPlan {
  id: string;
  name: string;
  description: string;
  monthlyPrice: number;
  yearlyPrice: number;
  yearlyDiscount: string;
  popular: boolean;
  icon: React.ElementType;
  color: string; // Tailwind gradient colors
}

const PricingSection: React.FC<PricingSectionProps> = ({ onSelectPlan }) => {
  const { t } = useTranslation();
  const [isYearly, setIsYearly] = useState(false);

  const plans: PricingPlan[] = useMemo(
    () => [
      {
        id: 'starter',
        name: t('pricing.plans.starter.name', 'Starter'),
        description: t('pricing.plans.starter.description', 'Best for getting started'),
      monthlyPrice: 39,
      yearlyPrice: 390,
        yearlyDiscount: t('pricing.yearlyDiscount', '1 month free'),
        popular: false,
        icon: Users,
        color: 'from-slate-500 to-slate-600',
      },
      {
        id: 'professional',
        name: t('pricing.plans.professional.name', 'Professional'),
        description: t('pricing.plans.professional.description', 'For growing teams'),
      monthlyPrice: 99,
      yearlyPrice: 990,
        yearlyDiscount: t('pricing.yearlyDiscount', '1 month free'),
        popular: true,
        icon: Building,
        color: 'from-purple-500 to-purple-600',
      },
      {
        id: 'enterprise',
        name: t('pricing.plans.enterprise.name', 'Enterprise'),
        description: t('pricing.plans.enterprise.description', 'Advanced features & support'),
      monthlyPrice: 249,
      yearlyPrice: 2490,
        yearlyDiscount: t('pricing.yearlyDiscount', '1 month free'),
        popular: false,
        icon: Crown,
        color: 'from-purple-600 to-pink-600',
      },
    ],
    [t],
  );

  const handleSelect = (planId: string) => {
    if (onSelectPlan) onSelectPlan(planId);
  };

  return (
    <section className="py-12" id="pricing">
      <div className="container mx-auto px-4">
        <h2 className="text-3xl font-bold text-center mb-8">
          {t('pricing.title', 'Simple, transparent pricing')}
        </h2>

        <div className="flex items-center justify-center mb-10 gap-3">
          <span className={`text-sm font-medium ${!isYearly ? 'text-purple-600' : 'text-gray-500'}`}>
            {t('pricing.billing.monthly', 'Monthly')}
          </span>
          <label className="inline-flex relative items-center cursor-pointer">
            <input
              type="checkbox"
              className="sr-only peer"
              checked={isYearly}
              onChange={() => setIsYearly((prev) => !prev)}
            />
            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600" />
          </label>
          <span className={`text-sm font-medium ${isYearly ? 'text-purple-600' : 'text-gray-500'}`}>
            {t('pricing.billing.yearly', 'Yearly')}
          </span>
        </div>

        <div className="grid gap-6 md:grid-cols-3">
          {plans.map((plan) => {
            const Icon = plan.icon;
            const price = isYearly
              ? Math.round((plan.yearlyPrice / 12) * 10) / 10
              : plan.monthlyPrice;

            return (
              <Card
                key={plan.id}
                className={`relative overflow-hidden border-none shadow-lg hover:shadow-xl transition-all duration-300 bg-gradient-to-br ${plan.color} text-white`}
              >
                {plan.popular && (
                  <span className="absolute top-3 right-3 bg-white/10 text-xs px-2 py-1 rounded-full">
                    {t('pricing.mostPopular', 'Most Popular')}
                </span>
                )}

                <div className="p-6 flex flex-col h-full justify-between backdrop-blur">
                      <div>
                    <Icon className="w-8 h-8 mb-4" />
                    <h3 className="text-xl font-semibold mb-2">{plan.name}</h3>
                    <p className="text-sm opacity-90 mb-6">{plan.description}</p>

                    <div className="flex items-baseline gap-1 mb-6">
                      <span className="text-lg">$</span>
                      <span className="text-4xl font-bold">{price}</span>
                      <span className="text-sm opacity-80">/mo</span>
                    </div>
                  </div>

                  <Button variant="secondary" className="w-full mt-auto" onClick={() => handleSelect(plan.id)}>
                    {t('pricing.choosePlan', 'Choose Plan')}
                  </Button>
              </div>
              </Card>
            );
          })}
            </div>
      </div>
    </section>
  );
};

export default PricingSection; 