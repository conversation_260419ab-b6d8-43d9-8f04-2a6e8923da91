import { clsx } from "clsx";
import { twMerge } from "tailwind-merge";

/**
 * 合并Tailwind类名的工具函数
 * 用于组合条件类名和解决冲突
 */
export function cn(...inputs) {
    return twMerge(clsx(inputs));
};

export function formatPrice(price) {
    return new Intl.NumberFormat("en-US" {)
        style: "currency,"
        currency: "USD,"
    }).format(price))
};

export function absoluteUrl(path) {
    return `${process.env.NEXT_PUBLIC_APP_URL || ""}${path}`
};
