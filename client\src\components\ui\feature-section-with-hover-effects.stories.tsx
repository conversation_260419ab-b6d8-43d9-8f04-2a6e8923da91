import type { Meta, StoryObj } from '@storybook/react';
import { FeaturesSectionWithHoverEffects } from './feature-section-with-hover-effects';

const meta: Meta<typeof FeaturesSectionWithHoverEffects> = {
  title: 'UI/FeaturesSectionWithHoverEffects',
  component: FeaturesSectionWithHoverEffects,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component: 'A modern features section with hover effects and grid layout. Features include animated hover states, responsive design, and Tabler icons integration.',
      },
    },
  },
  tags: ['autodocs'],
  decorators: [
    (Story) => (
      <div className="min-h-screen bg-white dark:bg-neutral-900 py-20">
        <div className="text-center mb-16 px-4">
          <h2 className="text-3xl sm:text-4xl font-bold text-neutral-800 dark:text-neutral-100 mb-4">
            Powerful Features for Modern Content Creation
          </h2>
          <p className="text-xl text-neutral-600 dark:text-neutral-300 max-w-2xl mx-auto">
            Everything you need to create, optimize, and distribute content that engages your audience and drives results.
          </p>
        </div>
        <Story />
      </div>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof FeaturesSectionWithHoverEffects>;

// Default story
export const Default: Story = {};

// Dark mode story
export const DarkMode: Story = {
  parameters: {
    backgrounds: { default: 'dark' },
  },
  decorators: [
    (Story) => (
      <div className="min-h-screen bg-neutral-900 py-20">
        <div className="text-center mb-16 px-4">
          <h2 className="text-3xl sm:text-4xl font-bold text-neutral-100 mb-4">
            Powerful Features for Modern Content Creation
          </h2>
          <p className="text-xl text-neutral-300 max-w-2xl mx-auto">
            Everything you need to create, optimize, and distribute content that engages your audience and drives results.
          </p>
        </div>
        <Story />
      </div>
    ),
  ],
};

// iTeraBiz Branded
export const iTeraBizBranded: Story = {
  decorators: [
    (Story) => (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 to-white py-20">
        <div className="text-center mb-16 px-4">
          <h2 className="text-3xl sm:text-4xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-4">
            iTeraBiz AI Platform Features
          </h2>
          <p className="text-xl text-gray-700 max-w-2xl mx-auto">
            Revolutionary AI-powered content creation tools designed for modern businesses and creators.
          </p>
        </div>
        <Story />
      </div>
    ),
  ],
}; 