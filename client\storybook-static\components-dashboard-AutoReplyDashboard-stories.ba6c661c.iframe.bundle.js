"use strict";(self.webpackChunkclient=self.webpackChunkclient||[]).push([[628],{"./src/api/axiosInstance.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.d(__webpack_exports__,{A:()=>__WEBPACK_DEFAULT_EXPORT__});var axios__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("./node_modules/axios/lib/axios.js");const API_BASE_URL=__webpack_require__("./node_modules/process/browser.js").env.REACT_APP_API_BASE_URL||"http://localhost:3001",axiosInstance=axios__WEBPACK_IMPORTED_MODULE_0__.A.create({baseURL:API_BASE_URL,timeout:1e4,headers:{"Content-Type":"application/json"}});axiosInstance.interceptors.request.use((config=>{const token=localStorage.getItem("auth_token");return console.log("Axios Interceptor - Reading token using key 'auth_token':",token?"Found":"Not Found"),token?(config.headers.Authorization=`Bearer ${token}`,console.log("Axios Interceptor - Setting Authorization header.")):console.log("Axios Interceptor - 'auth_token' not found in localStorage."),config}),(error=>Promise.reject(error))),axiosInstance.interceptors.response.use((response=>response),(error=>(error.response&&401===error.response.status&&console.error("Unauthorized access - 401"),Promise.reject(error))));const __WEBPACK_DEFAULT_EXPORT__=axiosInstance},"./src/components/dashboard/AutoReplyDashboard.stories.jsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{Default:()=>Default,__namedExportsOrder:()=>__namedExportsOrder,default:()=>AutoReplyDashboard_stories});var react=__webpack_require__("./node_modules/react/index.js"),useFeatureGuard=__webpack_require__("./src/hooks/useFeatureGuard.js"),axiosInstance=__webpack_require__("./src/api/axiosInstance.js"),jsx_runtime=__webpack_require__("./node_modules/react/jsx-runtime.js");function StatusCard(_ref){let{totalRules,activeRules,disabledRules,status,lastUpdated,onRefresh}=_ref;const statusInfo=(status=>{switch(status){case"normal":return{class:"status-normal",text:"System Normal"};case"warning":return{class:"status-warning",text:"Performance Issues"};case"error":return{class:"status-error",text:"System Error"};case"maintenance":return{class:"status-maintenance",text:"Maintenance Mode"};default:return{class:"status-unknown",text:"Status Unknown"}}})(status);return(0,jsx_runtime.jsxs)("div",{className:"dashboard-card status-card",children:[(0,jsx_runtime.jsxs)("div",{className:"dashboard-card-header",children:[(0,jsx_runtime.jsx)("h3",{children:"System Status"}),(0,jsx_runtime.jsx)("button",{onClick:onRefresh,className:"refresh-button","aria-label":"Refresh status",children:(0,jsx_runtime.jsx)("i",{className:"fa fa-refresh"})})]}),(0,jsx_runtime.jsx)("div",{className:"status-badge-container",children:(0,jsx_runtime.jsxs)("span",{className:`status-badge ${statusInfo.class}`,children:[(0,jsx_runtime.jsx)("i",{className:"fa fa-circle"})," ",statusInfo.text]})}),(0,jsx_runtime.jsxs)("div",{className:"rules-count-container",children:[(0,jsx_runtime.jsxs)("div",{className:"rule-stat",children:[(0,jsx_runtime.jsx)("span",{className:"rule-count",children:totalRules}),(0,jsx_runtime.jsx)("span",{className:"rule-label",children:"Total Rules"})]}),(0,jsx_runtime.jsxs)("div",{className:"rule-stat",children:[(0,jsx_runtime.jsx)("span",{className:"rule-count",children:activeRules}),(0,jsx_runtime.jsx)("span",{className:"rule-label",children:"Active Rules"})]}),(0,jsx_runtime.jsxs)("div",{className:"rule-stat",children:[(0,jsx_runtime.jsx)("span",{className:"rule-count",children:disabledRules}),(0,jsx_runtime.jsx)("span",{className:"rule-label",children:"Disabled Rules"})]})]}),(0,jsx_runtime.jsxs)("div",{className:"last-updated",children:[(0,jsx_runtime.jsx)("span",{className:"last-updated-label",children:"Last Updated:"}),(0,jsx_runtime.jsx)("span",{className:"last-updated-time",children:(dateString=>{if(!dateString)return"Never";const date=new Date(dateString);return new Intl.DateTimeFormat("default",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(date)})(lastUpdated)})]}),(0,jsx_runtime.jsx)("div",{className:"status-footer",children:(0,jsx_runtime.jsxs)("a",{href:"/platform/api-management",className:"settings-link",children:[(0,jsx_runtime.jsx)("i",{className:"fa fa-cog"})," Manage Rules in API Platform"]})})]})}StatusCard.defaultProps={totalRules:0,activeRules:0,disabledRules:0,status:"unknown",lastUpdated:null};const components_StatusCard=StatusCard;StatusCard.__docgenInfo={description:"",methods:[],displayName:"StatusCard",props:{totalRules:{defaultValue:{value:"0",computed:!1},description:"",type:{name:"number"},required:!1},activeRules:{defaultValue:{value:"0",computed:!1},description:"",type:{name:"number"},required:!1},disabledRules:{defaultValue:{value:"0",computed:!1},description:"",type:{name:"number"},required:!1},status:{defaultValue:{value:"'unknown'",computed:!1},description:"",type:{name:"enum",value:[{value:"'normal'",computed:!1},{value:"'warning'",computed:!1},{value:"'error'",computed:!1},{value:"'maintenance'",computed:!1},{value:"'unknown'",computed:!1}]},required:!1},lastUpdated:{defaultValue:{value:"null",computed:!1},description:"",type:{name:"string"},required:!1},onRefresh:{description:"",type:{name:"func"},required:!0}}};const MockChart=_ref=>{let{data,type}=_ref;return(0,jsx_runtime.jsx)("div",{className:"mock-chart",style:{height:"150px",background:"#f5f7fa",borderRadius:"4px",padding:"10px"},children:(0,jsx_runtime.jsxs)("div",{style:{textAlign:"center",paddingTop:"60px",color:"#888"},children:[type," Chart - ",data.length," data points"]})})};function StatsOverview(_ref2){let{dailyTriggers,topRules}=_ref2;const[timeRange,setTimeRange]=(0,react.useState)("week"),getTimeRangeText=()=>{switch(timeRange){case"day":return"Today";case"week":return"This Week";case"month":return"This Month";default:return"Custom Range"}};return(0,jsx_runtime.jsxs)("div",{className:"dashboard-card stats-overview",children:[(0,jsx_runtime.jsxs)("div",{className:"dashboard-card-header",children:[(0,jsx_runtime.jsx)("h3",{children:"Usage Statistics"}),(0,jsx_runtime.jsx)("div",{className:"time-range-selector",children:(0,jsx_runtime.jsxs)("select",{value:timeRange,onChange:e=>setTimeRange(e.target.value),className:"time-range-select",children:[(0,jsx_runtime.jsx)("option",{value:"day",children:"Today"}),(0,jsx_runtime.jsx)("option",{value:"week",children:"This Week"}),(0,jsx_runtime.jsx)("option",{value:"month",children:"This Month"})]})})]}),(0,jsx_runtime.jsxs)("div",{className:"chart-section",children:[(0,jsx_runtime.jsx)("h4",{children:"Trigger Frequency"}),(0,jsx_runtime.jsx)(MockChart,{data:dailyTriggers,type:"Line"})]}),(0,jsx_runtime.jsxs)("div",{className:"top-rules-section",children:[(0,jsx_runtime.jsxs)("h4",{children:["Top Rules (",getTimeRangeText(),")"]}),topRules.length>0?(0,jsx_runtime.jsx)("ul",{className:"top-rules-list",children:topRules.slice(0,5).map(((rule,index)=>(0,jsx_runtime.jsxs)("li",{className:"top-rule-item",children:[(0,jsx_runtime.jsx)("div",{className:"top-rule-rank",children:index+1}),(0,jsx_runtime.jsxs)("div",{className:"top-rule-info",children:[(0,jsx_runtime.jsx)("div",{className:"top-rule-name",children:rule.name||`Rule #${rule.id}`}),(0,jsx_runtime.jsx)("div",{className:"top-rule-keyword",children:rule.keyword})]}),(0,jsx_runtime.jsxs)("div",{className:"top-rule-count",children:[rule.triggerCount," times"]})]},rule.id||index)))}):(0,jsx_runtime.jsxs)("div",{className:"no-data-message",children:["No rule trigger data available for ",getTimeRangeText().toLowerCase(),"."]})]}),(0,jsx_runtime.jsx)("div",{className:"stats-footer",children:(0,jsx_runtime.jsxs)("a",{href:"/analytics/auto-reply",className:"view-all-link",children:["View Detailed Analytics ",(0,jsx_runtime.jsx)("i",{className:"fa fa-arrow-right"})]})})]})}StatsOverview.defaultProps={dailyTriggers:[],topRules:[]};const components_StatsOverview=StatsOverview;StatsOverview.__docgenInfo={description:"",methods:[],displayName:"StatsOverview",props:{dailyTriggers:{defaultValue:{value:"[]",computed:!1},description:"",type:{name:"arrayOf",value:{name:"shape",value:{date:{name:"string",required:!1},count:{name:"number",required:!1}}}},required:!1},topRules:{defaultValue:{value:"[]",computed:!1},description:"",type:{name:"arrayOf",value:{name:"shape",value:{id:{name:"union",value:[{name:"string"},{name:"number"}],required:!1},name:{name:"string",required:!1},keyword:{name:"string",required:!1},triggerCount:{name:"number",required:!1}}}},required:!1}}};var index_es=__webpack_require__("./node_modules/@fortawesome/react-fontawesome/index.es.js"),free_solid_svg_icons=__webpack_require__("./node_modules/@fortawesome/free-solid-svg-icons/index.mjs");function TestConsole_TestConsole(){const[inputMessage,setInputMessage]=(0,react.useState)(""),[platform,setPlatform]=(0,react.useState)("web"),[testResult,setTestResult]=(0,react.useState)(null),[loading,setLoading]=(0,react.useState)(!1),[error,setError]=(0,react.useState)(null),[userContextProps,setUserContextProps]=(0,react.useState)({userId:"test-user-123",userName:"Test User",userType:"customer",language:"en",previousInteractions:0}),handleContextPropChange=(prop,value)=>{setUserContextProps({...userContextProps,[prop]:value})};return(0,jsx_runtime.jsxs)("div",{className:"test-console",children:[(0,jsx_runtime.jsxs)("div",{className:"test-console-header",children:[(0,jsx_runtime.jsx)("h3",{children:"Auto Reply Test Console"}),(0,jsx_runtime.jsx)("p",{children:"Test your auto-reply rules with different message inputs and contexts"})]}),error&&(0,jsx_runtime.jsxs)("div",{className:"error-message",children:[(0,jsx_runtime.jsx)(index_es.g,{icon:free_solid_svg_icons.iW_})," ",error]}),(0,jsx_runtime.jsxs)("div",{className:"test-console-container",children:[(0,jsx_runtime.jsx)("div",{className:"test-input-panel",children:(0,jsx_runtime.jsxs)("form",{onSubmit:async e=>{if(e.preventDefault(),inputMessage.trim()){setLoading(!0),setError(null),setTestResult(null);try{const response=await axiosInstance.A.post("/api/auto-reply/process-message",{message:{text:inputMessage,platform,sender:{id:userContextProps.userId,name:userContextProps.userName,type:userContextProps.userType},timestamp:(new Date).toISOString(),metadata:{language:userContextProps.language,previousInteractions:parseInt(userContextProps.previousInteractions,10)||0}}});setTestResult(response.data)}catch(err){console.error("Error testing message:",err),setError("Failed to process test message. Please try again.")}finally{setLoading(!1)}}else setError("Please enter a message to test")},children:[(0,jsx_runtime.jsxs)("div",{className:"form-group",children:[(0,jsx_runtime.jsx)("label",{htmlFor:"testMessage",children:"Test Message"}),(0,jsx_runtime.jsx)("textarea",{id:"testMessage",value:inputMessage,onChange:e=>{setInputMessage(e.target.value)},placeholder:"Enter a message to test auto-reply rules...",rows:4,required:!0})]}),(0,jsx_runtime.jsxs)("div",{className:"form-row",children:[(0,jsx_runtime.jsxs)("div",{className:"form-group",children:[(0,jsx_runtime.jsx)("label",{htmlFor:"platform",children:"Platform"}),(0,jsx_runtime.jsxs)("select",{id:"platform",value:platform,onChange:e=>setPlatform(e.target.value),children:[(0,jsx_runtime.jsx)("option",{value:"web",children:"Website"}),(0,jsx_runtime.jsx)("option",{value:"mobile",children:"Mobile App"}),(0,jsx_runtime.jsx)("option",{value:"whatsapp",children:"WhatsApp"}),(0,jsx_runtime.jsx)("option",{value:"messenger",children:"Messenger"}),(0,jsx_runtime.jsx)("option",{value:"telegram",children:"Telegram"}),(0,jsx_runtime.jsx)("option",{value:"email",children:"Email"})]})]}),(0,jsx_runtime.jsxs)("div",{className:"form-group",children:[(0,jsx_runtime.jsx)("label",{htmlFor:"language",children:"Language"}),(0,jsx_runtime.jsxs)("select",{id:"language",value:userContextProps.language,onChange:e=>handleContextPropChange("language",e.target.value),children:[(0,jsx_runtime.jsx)("option",{value:"en",children:"English"}),(0,jsx_runtime.jsx)("option",{value:"zh",children:"Chinese"}),(0,jsx_runtime.jsx)("option",{value:"ms",children:"Malay"})]})]})]}),(0,jsx_runtime.jsxs)("div",{className:"form-row",children:[(0,jsx_runtime.jsxs)("div",{className:"form-group",children:[(0,jsx_runtime.jsx)("label",{htmlFor:"userType",children:"User Type"}),(0,jsx_runtime.jsxs)("select",{id:"userType",value:userContextProps.userType,onChange:e=>handleContextPropChange("userType",e.target.value),children:[(0,jsx_runtime.jsx)("option",{value:"customer",children:"Customer"}),(0,jsx_runtime.jsx)("option",{value:"prospect",children:"Prospect"}),(0,jsx_runtime.jsx)("option",{value:"agent",children:"Agent"})]})]}),(0,jsx_runtime.jsxs)("div",{className:"form-group",children:[(0,jsx_runtime.jsx)("label",{htmlFor:"previousInteractions",children:"Previous Interactions"}),(0,jsx_runtime.jsx)("input",{type:"number",id:"previousInteractions",value:userContextProps.previousInteractions,onChange:e=>handleContextPropChange("previousInteractions",e.target.value),min:"0"})]})]}),(0,jsx_runtime.jsx)("button",{type:"submit",className:"test-button",disabled:loading,children:loading?(0,jsx_runtime.jsxs)(jsx_runtime.Fragment,{children:[(0,jsx_runtime.jsx)(index_es.g,{icon:free_solid_svg_icons.z1G,spin:!0})," Processing..."]}):"Test Message"})]})}),(0,jsx_runtime.jsxs)("div",{className:"test-result-panel",children:[(0,jsx_runtime.jsx)("h4",{children:"Test Result"}),loading&&(0,jsx_runtime.jsx)("div",{className:"loading-indicator",children:"Processing your message..."}),!loading&&!testResult&&!error&&(0,jsx_runtime.jsx)("div",{className:"no-result-message",children:"Submit a test message to see the reply here"}),testResult&&(0,jsx_runtime.jsxs)("div",{className:"result-container",children:[(0,jsx_runtime.jsxs)("div",{className:"response-preview",children:[(0,jsx_runtime.jsx)("div",{className:"response-header",children:"Auto Reply:"}),(0,jsx_runtime.jsx)("div",{className:"response-text",children:testResult.text})]}),testResult?(0,jsx_runtime.jsxs)("div",{className:"match-details",children:[(0,jsx_runtime.jsx)("h4",{children:"Match Details"}),testResult.ruleId&&(0,jsx_runtime.jsxs)("div",{className:"match-detail-item",children:[(0,jsx_runtime.jsx)("span",{className:"detail-label",children:"Rule ID:"}),(0,jsx_runtime.jsx)("span",{className:"detail-value",children:testResult.ruleId})]}),testResult.intentId&&(0,jsx_runtime.jsxs)("div",{className:"match-detail-item",children:[(0,jsx_runtime.jsx)("span",{className:"detail-label",children:"Intent ID:"}),(0,jsx_runtime.jsx)("span",{className:"detail-value",children:testResult.intentId})]}),(0,jsx_runtime.jsxs)("div",{className:"match-detail-item",children:[(0,jsx_runtime.jsx)("span",{className:"detail-label",children:"Match Type:"}),(0,jsx_runtime.jsxs)("span",{className:"detail-value match-type",children:["direct"===testResult.matchType&&(0,jsx_runtime.jsxs)(jsx_runtime.Fragment,{children:[(0,jsx_runtime.jsx)(index_es.g,{icon:free_solid_svg_icons.SGM,className:"match-direct"})," Direct"]}),"intent"===testResult.matchType&&(0,jsx_runtime.jsxs)(jsx_runtime.Fragment,{children:[(0,jsx_runtime.jsx)(index_es.g,{icon:free_solid_svg_icons.GQu,className:"match-intent"})," Intent"]}),"fallback"===testResult.matchType&&(0,jsx_runtime.jsxs)(jsx_runtime.Fragment,{children:[(0,jsx_runtime.jsx)(index_es.g,{icon:free_solid_svg_icons.wRm,className:"match-fallback"})," Fallback"]}),!testResult.matchType&&"None"]})]}),testResult.confidence&&(0,jsx_runtime.jsxs)("div",{className:"match-detail-item",children:[(0,jsx_runtime.jsx)("span",{className:"detail-label",children:"Confidence:"}),(0,jsx_runtime.jsxs)("span",{className:"detail-value",children:[(100*testResult.confidence).toFixed(1),"%"]})]}),testResult.processingTime&&(0,jsx_runtime.jsxs)("div",{className:"match-detail-item",children:[(0,jsx_runtime.jsx)("span",{className:"detail-label",children:"Processing Time:"}),(0,jsx_runtime.jsxs)("span",{className:"detail-value",children:[testResult.processingTime,"ms"]})]})]}):null]})]})]})]})}const components_TestConsole=TestConsole_TestConsole;TestConsole_TestConsole.__docgenInfo={description:"",methods:[],displayName:"TestConsole"};var Button=__webpack_require__("./node_modules/react-bootstrap/esm/Button.js"),Modal=__webpack_require__("./node_modules/react-bootstrap/esm/Modal.js"),Form=__webpack_require__("./node_modules/react-bootstrap/esm/Form.js");function RuleFormModal(_ref){let{show,mode,ruleData,onSave,onDelete,onHide}=_ref;const[name,setName]=(0,react.useState)(""),[keyword,setKeyword]=(0,react.useState)(""),[responseText,setResponseText]=(0,react.useState)(""),[isRegex,setIsRegex]=(0,react.useState)(!1),[loading,setLoading]=(0,react.useState)(!1);(0,react.useEffect)((()=>{"edit"===mode&&ruleData?(setName(ruleData.name||""),setKeyword(ruleData.keyword||""),setResponseText(ruleData.response||""),setIsRegex(!!ruleData.is_regex)):(setName(""),setKeyword(""),setResponseText(""),setIsRegex(!1))}),[mode,ruleData]);return(0,jsx_runtime.jsxs)(Modal.A,{show,onHide,centered:!0,children:[(0,jsx_runtime.jsx)(Modal.A.Header,{closeButton:!0,children:(0,jsx_runtime.jsx)(Modal.A.Title,{children:"edit"===mode?"编辑规则":"新建规则"})}),(0,jsx_runtime.jsx)(Modal.A.Body,{children:(0,jsx_runtime.jsxs)(Form.A,{children:[(0,jsx_runtime.jsxs)(Form.A.Group,{className:"mb-3",controlId:"ruleName",children:[(0,jsx_runtime.jsx)(Form.A.Label,{children:"规则名称"}),(0,jsx_runtime.jsx)(Form.A.Control,{type:"text",value:name,onChange:e=>setName(e.target.value),placeholder:"自定义规则名称",disabled:loading})]}),(0,jsx_runtime.jsxs)(Form.A.Group,{className:"mb-3",controlId:"ruleKeyword",children:[(0,jsx_runtime.jsx)(Form.A.Label,{children:"关键词/模式"}),(0,jsx_runtime.jsx)(Form.A.Control,{type:"text",value:keyword,onChange:e=>setKeyword(e.target.value),placeholder:"匹配文本或正则",disabled:loading})]}),(0,jsx_runtime.jsx)(Form.A.Group,{className:"mb-3",controlId:"ruleIsRegex",children:(0,jsx_runtime.jsx)(Form.A.Check,{type:"checkbox",label:"正则表达式",checked:isRegex,onChange:e=>setIsRegex(e.target.checked),disabled:loading})}),(0,jsx_runtime.jsxs)(Form.A.Group,{className:"mb-3",controlId:"ruleResponse",children:[(0,jsx_runtime.jsx)(Form.A.Label,{children:"回复内容"}),(0,jsx_runtime.jsx)(Form.A.Control,{as:"textarea",rows:3,value:responseText,onChange:e=>setResponseText(e.target.value),placeholder:"自动回复内容",disabled:loading})]})]})}),(0,jsx_runtime.jsxs)(Modal.A.Footer,{children:["edit"===mode&&(0,jsx_runtime.jsx)(Button.A,{variant:"danger",onClick:async()=>{if(ruleData&&window.confirm("确认删除此规则？")){setLoading(!0);try{await axiosInstance.A.delete(`/auto-reply/rules/${ruleData.id}`),onDelete(ruleData),onHide()}catch(err){console.error("Rule delete error:",err)}finally{setLoading(!1)}}},disabled:loading,children:"删除"}),(0,jsx_runtime.jsx)(Button.A,{variant:"secondary",onClick:onHide,disabled:loading,children:"取消"}),(0,jsx_runtime.jsx)(Button.A,{variant:"primary",onClick:async()=>{setLoading(!0);try{const payload={name,keyword,response:responseText,is_regex:isRegex};let result;result="edit"===mode?await axiosInstance.A.put(`/auto-reply/rules/${ruleData.id}`,payload):await axiosInstance.A.post("/auto-reply/rules",payload),onSave(result.data),onHide()}catch(err){console.error("Rule save error:",err)}finally{setLoading(!1)}},disabled:loading,children:"edit"===mode?"保存":"创建"})]})]})}function RuleBrowser(){const[showModal,setShowModal]=(0,react.useState)(!1),[modalMode,setModalMode]=(0,react.useState)("create"),[modalRule,setModalRule]=(0,react.useState)(null),[rules,setRules]=(0,react.useState)([]),[filteredRules,setFilteredRules]=(0,react.useState)([]),[selectedRule,setSelectedRule]=(0,react.useState)(null),[loading,setLoading]=(0,react.useState)(!0),[error,setError]=(0,react.useState)(null),[searchTerm,setSearchTerm]=(0,react.useState)(""),[filters,setFilters]=(0,react.useState)({status:"all",type:"all",platform:"all"}),fetchRules=(0,react.useCallback)((async()=>{setLoading(!0),setError(null);try{const response=await axiosInstance.A.get("/api/auto-reply/rules");setRules(response.data||[]),setFilteredRules(response.data||[])}catch(err){console.error("Error fetching rules:",err),setError("Failed to load rules. Please try again later."),setRules([]),setFilteredRules([])}finally{setLoading(!1)}}),[]),applyFilters=(0,react.useCallback)((()=>{let result=[...rules];if(searchTerm){const term=searchTerm.toLowerCase();result=result.filter((rule=>rule.keyword&&rule.keyword.toLowerCase().includes(term)||rule.name&&rule.name.toLowerCase().includes(term)||rule.response&&rule.response.toLowerCase().includes(term)))}if("all"!==filters.status){const isActive="active"===filters.status;result=result.filter((rule=>rule.is_active===isActive))}"all"!==filters.type&&(result="regex"===filters.type?result.filter((rule=>rule.is_regex)):"intent"===filters.type?result.filter((rule=>rule.intent_id)):result.filter((rule=>!rule.is_regex&&!rule.intent_id))),"all"!==filters.platform&&(result=result.filter((rule=>!rule.platforms||rule.platforms.includes(filters.platform)))),setFilteredRules(result),selectedRule&&!result.find((r=>r.id===selectedRule.id))&&setSelectedRule(null)}),[rules,searchTerm,filters,selectedRule]);(0,react.useEffect)((()=>{fetchRules()}),[fetchRules]),(0,react.useEffect)((()=>{applyFilters()}),[applyFilters]);const handleFilterChange=(filterName,value)=>{setFilters({...filters,[filterName]:value})},getMatchTypeLabel=rule=>{if(rule.is_regex)return"Regex";if(rule.intent_id)return"Intent";switch(rule.match_type){case"exact":return"Exact Match";case"contains":return"Contains";case"starts_with":return"Starts With";case"ends_with":return"Ends With";case"fuzzy":return"Fuzzy Match";default:return"Unknown"}};return(0,jsx_runtime.jsxs)("div",{className:"rule-browser",children:[(0,jsx_runtime.jsxs)("div",{className:"rule-browser-header",children:[(0,jsx_runtime.jsx)("h3",{children:"Rule Browser"}),(0,jsx_runtime.jsx)("p",{children:"View and manage your auto-reply rules"})]}),error&&(0,jsx_runtime.jsx)("div",{className:"error-message",children:error}),(0,jsx_runtime.jsxs)("div",{className:"rule-browser-toolbar",children:[(0,jsx_runtime.jsx)(Button.A,{variant:"success",onClick:()=>{setModalMode("create"),setModalRule(null),setShowModal(!0)},className:"me-3",children:"New Rule"}),(0,jsx_runtime.jsx)("div",{className:"search-box",children:(0,jsx_runtime.jsx)("input",{type:"text",placeholder:"Search rules...",value:searchTerm,onChange:e=>{setSearchTerm(e.target.value)}})}),(0,jsx_runtime.jsxs)("div",{className:"filter-controls",children:[(0,jsx_runtime.jsxs)("div",{className:"filter-item",children:[(0,jsx_runtime.jsx)("label",{htmlFor:"statusFilter",children:"Status:"}),(0,jsx_runtime.jsxs)("select",{id:"statusFilter",value:filters.status,onChange:e=>handleFilterChange("status",e.target.value),children:[(0,jsx_runtime.jsx)("option",{value:"all",children:"All"}),(0,jsx_runtime.jsx)("option",{value:"active",children:"Active"}),(0,jsx_runtime.jsx)("option",{value:"disabled",children:"Disabled"})]})]}),(0,jsx_runtime.jsxs)("div",{className:"filter-item",children:[(0,jsx_runtime.jsx)("label",{htmlFor:"typeFilter",children:"Type:"}),(0,jsx_runtime.jsxs)("select",{id:"typeFilter",value:filters.type,onChange:e=>handleFilterChange("type",e.target.value),children:[(0,jsx_runtime.jsx)("option",{value:"all",children:"All"}),(0,jsx_runtime.jsx)("option",{value:"keyword",children:"Keyword"}),(0,jsx_runtime.jsx)("option",{value:"regex",children:"Regex"}),(0,jsx_runtime.jsx)("option",{value:"intent",children:"Intent"})]})]}),(0,jsx_runtime.jsxs)("div",{className:"filter-item",children:[(0,jsx_runtime.jsx)("label",{htmlFor:"platformFilter",children:"Platform:"}),(0,jsx_runtime.jsxs)("select",{id:"platformFilter",value:filters.platform,onChange:e=>handleFilterChange("platform",e.target.value),children:[(0,jsx_runtime.jsx)("option",{value:"all",children:"All"}),(0,jsx_runtime.jsx)("option",{value:"web",children:"Web"}),(0,jsx_runtime.jsx)("option",{value:"mobile",children:"Mobile"}),(0,jsx_runtime.jsx)("option",{value:"whatsapp",children:"WhatsApp"}),(0,jsx_runtime.jsx)("option",{value:"messenger",children:"Messenger"}),(0,jsx_runtime.jsx)("option",{value:"telegram",children:"Telegram"})]})]})]})]}),(0,jsx_runtime.jsxs)("div",{className:"rule-browser-content",children:[(0,jsx_runtime.jsx)("div",{className:"rules-list-panel",children:loading?(0,jsx_runtime.jsx)("div",{className:"loading-indicator",children:"Loading rules..."}):(0,jsx_runtime.jsxs)(jsx_runtime.Fragment,{children:[(0,jsx_runtime.jsxs)("div",{className:"rules-count",children:["Showing ",filteredRules.length," of ",rules.length," rules"]}),0===filteredRules.length?(0,jsx_runtime.jsx)("div",{className:"no-rules-message",children:searchTerm||"all"!==filters.status||"all"!==filters.type||"all"!==filters.platform?"No rules match your current filters.":"No rules have been defined yet."}):(0,jsx_runtime.jsx)("ul",{className:"rules-list",children:filteredRules.map((rule=>(0,jsx_runtime.jsxs)("li",{className:"rule-list-item "+(selectedRule&&selectedRule.id===rule.id?"selected":""),onClick:()=>(rule=>{setSelectedRule(rule)})(rule),onDoubleClick:()=>(rule=>{setModalMode("edit"),setModalRule(rule),setShowModal(!0)})(rule),title:"Double-click to edit",children:[(0,jsx_runtime.jsxs)("div",{className:"rule-list-item-header",children:[(0,jsx_runtime.jsx)("span",{className:"rule-name",children:rule.name||`Rule #${rule.id}`}),(0,jsx_runtime.jsx)("span",{className:"rule-status-dot "+(rule.is_active?"active":"inactive")})]}),(0,jsx_runtime.jsx)("div",{className:"rule-pattern",children:rule.keyword}),(0,jsx_runtime.jsxs)("div",{className:"rule-meta",children:[(0,jsx_runtime.jsx)("span",{className:"rule-type",children:getMatchTypeLabel(rule)}),rule.stats&&(0,jsx_runtime.jsxs)("span",{className:"rule-triggers",children:[rule.stats.matches||0," matches"]})]})]},rule.id)))})]})}),(0,jsx_runtime.jsx)("div",{className:"rule-details-panel",children:loading?(0,jsx_runtime.jsx)("div",{className:"loading-indicator",children:"Loading rule details..."}):selectedRule?selectedRule?(0,jsx_runtime.jsxs)("div",{className:"rule-details",children:[(0,jsx_runtime.jsx)("h4",{children:selectedRule.name||`Rule #${selectedRule.id}`}),(0,jsx_runtime.jsx)("div",{className:"rule-status",children:(0,jsx_runtime.jsx)("span",{className:"status-indicator "+(selectedRule.is_active?"active":"inactive"),children:selectedRule.is_active?"Active":"Disabled"})}),(0,jsx_runtime.jsxs)("div",{className:"rule-detail-section",children:[(0,jsx_runtime.jsx)("h5",{children:"Pattern"}),(0,jsx_runtime.jsxs)("div",{className:"pattern-display",children:[(0,jsx_runtime.jsxs)("div",{className:"pattern-label",children:[getMatchTypeLabel(selectedRule),":"]}),(0,jsx_runtime.jsx)("div",{className:"pattern-value",children:selectedRule.keyword})]})]}),(0,jsx_runtime.jsxs)("div",{className:"rule-detail-section",children:[(0,jsx_runtime.jsx)("h5",{children:"Response"}),(0,jsx_runtime.jsx)("div",{className:"response-display",children:selectedRule.response})]}),selectedRule.conditions&&selectedRule.conditions.length>0&&(0,jsx_runtime.jsxs)("div",{className:"rule-detail-section",children:[(0,jsx_runtime.jsx)("h5",{children:"Conditions"}),(0,jsx_runtime.jsx)("ul",{className:"conditions-list",children:selectedRule.conditions.map(((condition,index)=>(0,jsx_runtime.jsxs)("li",{className:"condition-item",children:[condition.type,": ",condition.description||JSON.stringify(condition.parameters)]},index)))})]}),selectedRule.stats&&(0,jsx_runtime.jsxs)("div",{className:"rule-detail-section",children:[(0,jsx_runtime.jsx)("h5",{children:"Statistics"}),(0,jsx_runtime.jsxs)("div",{className:"stats-grid",children:[(0,jsx_runtime.jsxs)("div",{className:"stat-item",children:[(0,jsx_runtime.jsx)("span",{className:"stat-label",children:"Matches:"}),(0,jsx_runtime.jsx)("span",{className:"stat-value",children:selectedRule.stats.matches||0})]}),(0,jsx_runtime.jsxs)("div",{className:"stat-item",children:[(0,jsx_runtime.jsx)("span",{className:"stat-label",children:"Today:"}),(0,jsx_runtime.jsx)("span",{className:"stat-value",children:selectedRule.stats.today||0})]}),(0,jsx_runtime.jsxs)("div",{className:"stat-item",children:[(0,jsx_runtime.jsx)("span",{className:"stat-label",children:"Last 7 days:"}),(0,jsx_runtime.jsx)("span",{className:"stat-value",children:selectedRule.stats.week||0})]}),(0,jsx_runtime.jsxs)("div",{className:"stat-item",children:[(0,jsx_runtime.jsx)("span",{className:"stat-label",children:"Last active:"}),(0,jsx_runtime.jsx)("span",{className:"stat-value",children:selectedRule.stats.lastActive?new Date(selectedRule.stats.lastActive).toLocaleDateString():"Never"})]})]})]})]}):null:(0,jsx_runtime.jsx)("div",{className:"no-selection-message",children:"Select a rule from the list to view its details"})})]}),(0,jsx_runtime.jsx)(RuleFormModal,{show:showModal,mode:modalMode,ruleData:modalRule,onSave:savedRule=>{"create"===modalMode?(setRules((prev=>[...prev,savedRule])),setFilteredRules((prev=>[...prev,savedRule]))):(setRules((prev=>prev.map((r=>r.id===savedRule.id?savedRule:r)))),setFilteredRules((prev=>prev.map((r=>r.id===savedRule.id?savedRule:r)))),selectedRule&&selectedRule.id===savedRule.id&&setSelectedRule(savedRule))},onDelete:deletedRule=>{setRules((prev=>prev.filter((r=>r.id!==deletedRule.id)))),setFilteredRules((prev=>prev.filter((r=>r.id!==deletedRule.id)))),selectedRule&&selectedRule.id===deletedRule.id&&setSelectedRule(null)},onHide:()=>setShowModal(!1)})]})}RuleFormModal.__docgenInfo={description:"",methods:[],displayName:"RuleFormModal"};const components_RuleBrowser=RuleBrowser;function RecentActivity(){const[activities,setActivities]=(0,react.useState)([]),[loading,setLoading]=(0,react.useState)(!0),[error,setError]=(0,react.useState)(null),generateRandomMessage=(0,react.useCallback)((()=>{const messages=["Hello, can you help me?","What are your business hours?","I need information about shipping","How can I track my order?","Is there a discount code available?","When will my order arrive?","Do you have this product in stock?","I have a problem with my account"];return messages[Math.floor(Math.random()*messages.length)]}),[]),generateRandomResponse=(0,react.useCallback)((()=>{const responses=["Hi there! Of course, I'm here to help!","Our business hours are Monday-Friday, 9AM-5PM.","We offer standard and expedited shipping options. Standard shipping takes 3-5 business days.","You can track your order in your account or using the tracking number we sent in your confirmation email.","You can use WELCOME10 for 10% off your first purchase.","Orders typically arrive within 3-5 business days.","Let me check our inventory. Please provide the product name or code.","I'm sorry to hear that. Could you please provide more details about the issue?"];return responses[Math.floor(Math.random()*responses.length)]}),[]),generateMockActivities=(0,react.useCallback)((()=>{const platforms=["web","mobile","whatsapp","messenger"],results=["matched","unmatched"],now=new Date;return Array(8).fill(0).map(((_,i)=>{const timestamp=new Date(now);return timestamp.setMinutes(now.getMinutes()-15*i-Math.floor(10*Math.random())),{id:`act-${i}`,type:"message_processed",timestamp:timestamp.toISOString(),platform:platforms[Math.floor(Math.random()*platforms.length)],result:results[Math.floor(Math.random()*results.length)],messagePreview:generateRandomMessage(),responsePreview:"matched"===results[0]?generateRandomResponse():null,ruleId:"matched"===results[0]?Math.floor(10*Math.random())+1:null}}))}),[generateRandomMessage,generateRandomResponse]),fetchRecentActivities=(0,react.useCallback)((async()=>{setLoading(!0),setError(null);try{const response=await axiosInstance.A.get("/api/auto-reply/activities");setActivities(response.data||generateMockActivities())}catch(err){console.error("Error fetching recent activities:",err),setActivities(generateMockActivities())}finally{setLoading(!1)}}),[generateMockActivities]);(0,react.useEffect)((()=>{fetchRecentActivities()}),[fetchRecentActivities]);const formatTime=timestamp=>{try{const date=new Date(timestamp);return new Intl.DateTimeFormat("default",{hour:"2-digit",minute:"2-digit"}).format(date)}catch(e){return"Unknown time"}},getPlatformIcon=platform=>{switch(platform){case"web":return"fa-globe";case"mobile":return"fa-mobile";case"whatsapp":return"fa-whatsapp";case"messenger":return"fa-facebook-messenger";default:return"fa-comment"}};return(0,jsx_runtime.jsxs)("div",{className:"dashboard-card recent-activity-card",children:[(0,jsx_runtime.jsxs)("div",{className:"dashboard-card-header",children:[(0,jsx_runtime.jsx)("h3",{children:"Recent Activity"}),(0,jsx_runtime.jsx)("button",{className:"refresh-button",onClick:fetchRecentActivities,children:(0,jsx_runtime.jsx)("i",{className:"fa fa-refresh"})})]}),(0,jsx_runtime.jsx)("div",{className:"activity-list-container",children:loading?(0,jsx_runtime.jsx)("div",{className:"loading-indicator",children:"Loading activities..."}):error?(0,jsx_runtime.jsx)("div",{className:"error-message",children:error}):0===activities.length?(0,jsx_runtime.jsx)("div",{className:"no-activity-message",children:"No recent activities found"}):(0,jsx_runtime.jsx)("ul",{className:"activity-list",children:activities.map((activity=>(0,jsx_runtime.jsxs)("li",{className:"activity-item",children:[(0,jsx_runtime.jsx)("div",{className:"activity-time",children:formatTime(activity.timestamp)}),(0,jsx_runtime.jsx)("div",{className:`activity-platform-icon ${activity.platform}`,children:(0,jsx_runtime.jsx)("i",{className:`fa ${getPlatformIcon(activity.platform)}`})}),(0,jsx_runtime.jsxs)("div",{className:"activity-content",children:[(0,jsx_runtime.jsx)("div",{className:"activity-message-preview",children:activity.messagePreview}),(0,jsx_runtime.jsx)("div",{className:`activity-result ${activity.result}`,children:"matched"===activity.result?(0,jsx_runtime.jsxs)(jsx_runtime.Fragment,{children:[(0,jsx_runtime.jsx)("i",{className:"fa fa-check-circle"})," Matched Rule #",activity.ruleId]}):(0,jsx_runtime.jsxs)(jsx_runtime.Fragment,{children:[(0,jsx_runtime.jsx)("i",{className:"fa fa-times-circle"})," No Match"]})})]})]},activity.id)))})}),(0,jsx_runtime.jsx)("div",{className:"card-footer",children:(0,jsx_runtime.jsxs)("a",{href:"/analytics/activities",className:"view-all-link",children:["View All Activities ",(0,jsx_runtime.jsx)("i",{className:"fa fa-arrow-right"})]})})]})}RuleBrowser.__docgenInfo={description:"",methods:[],displayName:"RuleBrowser"};const components_RecentActivity=RecentActivity;RecentActivity.__docgenInfo={description:"",methods:[],displayName:"RecentActivity"};const GaugeChart=_ref=>{let{value,maxValue,label,color}=_ref;return(0,jsx_runtime.jsxs)("div",{className:"gauge-chart",children:[(0,jsx_runtime.jsxs)("div",{className:"gauge-value",style:{color},children:[value,(0,jsx_runtime.jsx)("span",{className:"gauge-unit",children:"ms"})]}),(0,jsx_runtime.jsx)("div",{className:"gauge-label",children:label}),(0,jsx_runtime.jsx)("div",{className:"gauge-background",children:(0,jsx_runtime.jsx)("div",{className:"gauge-fill",style:{width:`${Math.min(100,value/maxValue*100)}%`,backgroundColor:color}})})]})};function PerformanceMetrics(){const[metrics,setMetrics]=(0,react.useState)({avgResponseTime:135,p95ResponseTime:310,successRate:99.7,errorRate:.3,apiCalls:{total:2508,today:283},aiCalls:{total:985,today:94}}),[timeRange,setTimeRange]=(0,react.useState)("day"),[loading,setLoading]=(0,react.useState)(!0),[error,setError]=(0,react.useState)(null),fetchPerformanceMetrics=(0,react.useCallback)((async()=>{setLoading(!0),setError(null);try{const response=await axiosInstance.A.get(`/auto-reply/metrics?timeRange=${timeRange}`);setMetrics(response.data||generateMockMetrics())}catch(err){console.error("Error fetching performance metrics:",err),setMetrics(generateMockMetrics())}finally{setLoading(!1)}}),[timeRange]);(0,react.useEffect)((()=>{fetchPerformanceMetrics()}),[timeRange]);const generateMockMetrics=()=>{const multiplier="day"===timeRange?1:"week"===timeRange?1.2:1.5;return{avgResponseTime:Math.round(120+30*Math.random()*multiplier),p95ResponseTime:Math.round(280+60*Math.random()*multiplier),successRate:(99.5+.5*Math.random()).toFixed(1),errorRate:(.1+.4*Math.random()).toFixed(1),apiCalls:{total:Math.round(2e3+1e3*Math.random()*multiplier),today:Math.round(200+100*Math.random())},aiCalls:{total:Math.round(800+400*Math.random()*multiplier),today:Math.round(50+50*Math.random())}}},getResponseTimeColor=time=>time<150?"#4caf50":time<300?"#ff9800":"#f44336";return(0,jsx_runtime.jsxs)("div",{className:"dashboard-card performance-metrics-card",children:[(0,jsx_runtime.jsxs)("div",{className:"dashboard-card-header",children:[(0,jsx_runtime.jsx)("h3",{children:"Performance Metrics"}),(0,jsx_runtime.jsx)("div",{className:"time-range-selector",children:(0,jsx_runtime.jsxs)("select",{value:timeRange,onChange:e=>setTimeRange(e.target.value),className:"time-range-select",children:[(0,jsx_runtime.jsx)("option",{value:"day",children:"Today"}),(0,jsx_runtime.jsx)("option",{value:"week",children:"This Week"}),(0,jsx_runtime.jsx)("option",{value:"month",children:"This Month"})]})})]}),loading?(0,jsx_runtime.jsx)("div",{className:"loading-indicator",children:"Loading metrics..."}):error?(0,jsx_runtime.jsx)("div",{className:"error-message",children:error}):(0,jsx_runtime.jsxs)("div",{className:"metrics-container",children:[(0,jsx_runtime.jsxs)("div",{className:"response-time-section",children:[(0,jsx_runtime.jsxs)("h4",{children:["Response Time (",(()=>{switch(timeRange){case"day":return"Today";case"week":return"This Week";case"month":return"This Month";default:return""}})(),")"]}),(0,jsx_runtime.jsxs)("div",{className:"gauge-charts",children:[(0,jsx_runtime.jsx)(GaugeChart,{value:metrics.avgResponseTime,maxValue:500,label:"Average",color:getResponseTimeColor(metrics.avgResponseTime)}),(0,jsx_runtime.jsx)(GaugeChart,{value:metrics.p95ResponseTime,maxValue:500,label:"95th Percentile",color:getResponseTimeColor(metrics.p95ResponseTime)})]})]}),(0,jsx_runtime.jsxs)("div",{className:"success-rate-section",children:[(0,jsx_runtime.jsxs)("div",{className:"rate-stat success-rate",children:[(0,jsx_runtime.jsxs)("span",{className:"rate-value",children:[metrics.successRate,"%"]}),(0,jsx_runtime.jsx)("span",{className:"rate-label",children:"Success Rate"})]}),(0,jsx_runtime.jsxs)("div",{className:"rate-stat error-rate",children:[(0,jsx_runtime.jsxs)("span",{className:"rate-value",children:[metrics.errorRate,"%"]}),(0,jsx_runtime.jsx)("span",{className:"rate-label",children:"Error Rate"})]})]}),(0,jsx_runtime.jsxs)("div",{className:"api-usage-section",children:[(0,jsx_runtime.jsx)("h4",{children:"API Usage"}),(0,jsx_runtime.jsxs)("div",{className:"usage-stats",children:[(0,jsx_runtime.jsxs)("div",{className:"usage-stat",children:[(0,jsx_runtime.jsx)("div",{className:"usage-label",children:"Total API Calls:"}),(0,jsx_runtime.jsx)("div",{className:"usage-value",children:metrics.apiCalls.total.toLocaleString()})]}),(0,jsx_runtime.jsxs)("div",{className:"usage-stat",children:[(0,jsx_runtime.jsx)("div",{className:"usage-label",children:"Today's API Calls:"}),(0,jsx_runtime.jsx)("div",{className:"usage-value",children:metrics.apiCalls.today.toLocaleString()})]}),(0,jsx_runtime.jsxs)("div",{className:"usage-stat",children:[(0,jsx_runtime.jsx)("div",{className:"usage-label",children:"Total AI Calls:"}),(0,jsx_runtime.jsx)("div",{className:"usage-value",children:metrics.aiCalls.total.toLocaleString()})]}),(0,jsx_runtime.jsxs)("div",{className:"usage-stat",children:[(0,jsx_runtime.jsx)("div",{className:"usage-label",children:"Today's AI Calls:"}),(0,jsx_runtime.jsx)("div",{className:"usage-value",children:metrics.aiCalls.today.toLocaleString()})]})]})]})]}),(0,jsx_runtime.jsx)("div",{className:"card-footer",children:(0,jsx_runtime.jsxs)("a",{href:"/analytics/performance",className:"view-all-link",children:["View Detailed Performance ",(0,jsx_runtime.jsx)("i",{className:"fa fa-arrow-right"})]})})]})}const components_PerformanceMetrics=PerformanceMetrics;function AutoReplyLogs_AutoReplyLogs(){const[logs,setLogs]=(0,react.useState)([]),[loading,setLoading]=(0,react.useState)(!0),[error,setError]=(0,react.useState)(null),[filters,setFilters]=(0,react.useState)({dateFrom:function getDefaultDateFrom(){const date=new Date;return date.setDate(date.getDate()-7),formatDate(date)}(),dateTo:formatDate(new Date),platform:"all",matchType:"all",searchTerm:""}),[pagination,setPagination]=(0,react.useState)({page:1,totalPages:1,totalItems:0});function formatDate(date){return`${date.getFullYear()}-${String(date.getMonth()+1).padStart(2,"0")}-${String(date.getDate()).padStart(2,"0")}`}const fetchLogs=(0,react.useCallback)((async()=>{setLoading(!0),setError(null);try{const response=await axiosInstance.A.get("/api/auto-reply/logs",{params:{page:pagination.page,limit:10,dateFrom:filters.dateFrom,dateTo:filters.dateTo,platform:"all"!==filters.platform?filters.platform:void 0,matchType:"all"!==filters.matchType?filters.matchType:void 0,search:filters.searchTerm||void 0}});setLogs(response.data.logs),setPagination({page:response.data.page,totalPages:response.data.totalPages,totalItems:response.data.totalItems})}catch(err){console.error("Error fetching auto-reply logs:",err),setError("Failed to load logs. Please try again.")}finally{setLoading(!1)}}),[filters.dateFrom,filters.dateTo,filters.platform,filters.matchType,filters.searchTerm,pagination.page]);(0,react.useEffect)((()=>{fetchLogs()}),[fetchLogs]);const handleFilterChange=(key,value)=>{setFilters({...filters,[key]:value}),"searchTerm"!==key&&setPagination({...pagination,page:1})},renderMatchTypeIcon=matchType=>{switch(matchType){case"direct":return(0,jsx_runtime.jsx)(index_es.g,{icon:free_solid_svg_icons.SGM,className:"match-direct",title:"Direct Match"});case"intent":return(0,jsx_runtime.jsx)(index_es.g,{icon:free_solid_svg_icons.GQu,className:"match-intent",title:"Intent Match"});case"fallback":return(0,jsx_runtime.jsx)(index_es.g,{icon:free_solid_svg_icons.wRm,className:"match-fallback",title:"Fallback"});default:return null}},goToPage=page=>{page<1||page>pagination.totalPages||setPagination({...pagination,page})};return(0,jsx_runtime.jsxs)("div",{className:"auto-reply-logs",children:[(0,jsx_runtime.jsxs)("div",{className:"logs-header",children:[(0,jsx_runtime.jsx)("h3",{children:"Auto Reply Logs"}),(0,jsx_runtime.jsxs)("button",{onClick:fetchLogs,className:"refresh-button",children:[(0,jsx_runtime.jsx)(index_es.g,{icon:free_solid_svg_icons.VNe})," Refresh"]})]}),(0,jsx_runtime.jsx)("div",{className:"logs-filters",children:(0,jsx_runtime.jsxs)("form",{onSubmit:e=>{e.preventDefault(),setPagination({...pagination,page:1}),fetchLogs()},children:[(0,jsx_runtime.jsxs)("div",{className:"filters-row",children:[(0,jsx_runtime.jsxs)("div",{className:"filter-group",children:[(0,jsx_runtime.jsxs)("label",{children:[(0,jsx_runtime.jsx)(index_es.g,{icon:free_solid_svg_icons.BEE})," From"]}),(0,jsx_runtime.jsx)("input",{type:"date",value:filters.dateFrom,onChange:e=>handleFilterChange("dateFrom",e.target.value)})]}),(0,jsx_runtime.jsxs)("div",{className:"filter-group",children:[(0,jsx_runtime.jsxs)("label",{children:[(0,jsx_runtime.jsx)(index_es.g,{icon:free_solid_svg_icons.BEE})," To"]}),(0,jsx_runtime.jsx)("input",{type:"date",value:filters.dateTo,onChange:e=>handleFilterChange("dateTo",e.target.value)})]}),(0,jsx_runtime.jsxs)("div",{className:"filter-group",children:[(0,jsx_runtime.jsxs)("label",{children:[(0,jsx_runtime.jsx)(index_es.g,{icon:free_solid_svg_icons.mRM})," Platform"]}),(0,jsx_runtime.jsxs)("select",{value:filters.platform,onChange:e=>handleFilterChange("platform",e.target.value),children:[(0,jsx_runtime.jsx)("option",{value:"all",children:"All Platforms"}),(0,jsx_runtime.jsx)("option",{value:"web",children:"Website"}),(0,jsx_runtime.jsx)("option",{value:"mobile",children:"Mobile App"}),(0,jsx_runtime.jsx)("option",{value:"whatsapp",children:"WhatsApp"}),(0,jsx_runtime.jsx)("option",{value:"messenger",children:"Messenger"}),(0,jsx_runtime.jsx)("option",{value:"telegram",children:"Telegram"}),(0,jsx_runtime.jsx)("option",{value:"email",children:"Email"})]})]}),(0,jsx_runtime.jsxs)("div",{className:"filter-group",children:[(0,jsx_runtime.jsxs)("label",{children:[(0,jsx_runtime.jsx)(index_es.g,{icon:free_solid_svg_icons.mRM})," Match Type"]}),(0,jsx_runtime.jsxs)("select",{value:filters.matchType,onChange:e=>handleFilterChange("matchType",e.target.value),children:[(0,jsx_runtime.jsx)("option",{value:"all",children:"All Types"}),(0,jsx_runtime.jsx)("option",{value:"direct",children:"Direct Match"}),(0,jsx_runtime.jsx)("option",{value:"intent",children:"Intent Match"}),(0,jsx_runtime.jsx)("option",{value:"fallback",children:"Fallback"})]})]})]}),(0,jsx_runtime.jsxs)("div",{className:"search-bar",children:[(0,jsx_runtime.jsx)("input",{type:"text",placeholder:"Search messages...",value:filters.searchTerm,onChange:e=>handleFilterChange("searchTerm",e.target.value)}),(0,jsx_runtime.jsxs)("button",{type:"submit",children:[(0,jsx_runtime.jsx)(index_es.g,{icon:free_solid_svg_icons.MjD})," Search"]})]})]})}),error&&(0,jsx_runtime.jsx)("div",{className:"logs-error",children:error}),(0,jsx_runtime.jsx)("div",{className:"logs-table-container",children:loading?(0,jsx_runtime.jsxs)("div",{className:"logs-loading",children:[(0,jsx_runtime.jsx)(index_es.g,{icon:free_solid_svg_icons.z1G,spin:!0}),(0,jsx_runtime.jsx)("p",{children:"Loading logs..."})]}):0===logs.length?(0,jsx_runtime.jsx)("div",{className:"no-logs-message",children:(0,jsx_runtime.jsx)("p",{children:"No logs found for the selected filters."})}):(0,jsx_runtime.jsxs)(jsx_runtime.Fragment,{children:[(0,jsx_runtime.jsxs)("table",{className:"logs-table",children:[(0,jsx_runtime.jsx)("thead",{children:(0,jsx_runtime.jsxs)("tr",{children:[(0,jsx_runtime.jsx)("th",{children:"Time"}),(0,jsx_runtime.jsx)("th",{children:"Platform"}),(0,jsx_runtime.jsx)("th",{children:"User"}),(0,jsx_runtime.jsx)("th",{children:"Message"}),(0,jsx_runtime.jsx)("th",{children:"Response"}),(0,jsx_runtime.jsx)("th",{children:"Match"}),(0,jsx_runtime.jsx)("th",{children:"Confidence"})]})}),(0,jsx_runtime.jsx)("tbody",{children:logs.map((log=>{var _log$sender,_log$sender2,_log$response,_log$response2,_log$response3,_log$response4,_log$response4$matchT,_log$response5,_log$response5$matchT,_log$response6,timestamp;return(0,jsx_runtime.jsxs)("tr",{children:[(0,jsx_runtime.jsx)("td",{className:"timestamp-cell",children:(timestamp=log.timestamp,new Date(timestamp).toLocaleString())}),(0,jsx_runtime.jsx)("td",{className:"platform-cell",children:(0,jsx_runtime.jsx)("span",{className:`platform-badge platform-${log.platform}`,children:log.platform})}),(0,jsx_runtime.jsx)("td",{className:"user-cell",children:(null===(_log$sender=log.sender)||void 0===_log$sender?void 0:_log$sender.name)||(null===(_log$sender2=log.sender)||void 0===_log$sender2?void 0:_log$sender2.id)||"Anonymous"}),(0,jsx_runtime.jsx)("td",{className:"message-cell",children:log.text}),(0,jsx_runtime.jsx)("td",{className:"response-cell",children:null===(_log$response=log.response)||void 0===_log$response?void 0:_log$response.text}),(0,jsx_runtime.jsx)("td",{className:"match-cell",children:(0,jsx_runtime.jsxs)("span",{className:`match-badge match-${null===(_log$response2=log.response)||void 0===_log$response2?void 0:_log$response2.matchType}`,children:[renderMatchTypeIcon(null===(_log$response3=log.response)||void 0===_log$response3?void 0:_log$response3.matchType),(0,jsx_runtime.jsx)("span",{className:"match-type-text",children:(null===(_log$response4=log.response)||void 0===_log$response4||null===(_log$response4$matchT=_log$response4.matchType)||void 0===_log$response4$matchT?void 0:_log$response4$matchT.charAt(0).toUpperCase())+(null===(_log$response5=log.response)||void 0===_log$response5||null===(_log$response5$matchT=_log$response5.matchType)||void 0===_log$response5$matchT?void 0:_log$response5$matchT.slice(1))||"None"})]})}),(0,jsx_runtime.jsx)("td",{className:"confidence-cell",children:null!==(_log$response6=log.response)&&void 0!==_log$response6&&_log$response6.confidence?`${(100*log.response.confidence).toFixed(1)}%`:"N/A"})]},log.id)}))})]}),(0,jsx_runtime.jsxs)("div",{className:"pagination",children:[(0,jsx_runtime.jsx)("button",{onClick:()=>goToPage(1),disabled:1===pagination.page,className:"pagination-button",children:"First"}),(0,jsx_runtime.jsx)("button",{onClick:()=>goToPage(pagination.page-1),disabled:1===pagination.page,className:"pagination-button",children:"Previous"}),(0,jsx_runtime.jsxs)("span",{className:"pagination-info",children:["Page ",pagination.page," of ",pagination.totalPages||1,pagination.totalItems>0&&` (${pagination.totalItems} total logs)`]}),(0,jsx_runtime.jsx)("button",{onClick:()=>goToPage(pagination.page+1),disabled:pagination.page===pagination.totalPages,className:"pagination-button",children:"Next"}),(0,jsx_runtime.jsx)("button",{onClick:()=>goToPage(pagination.totalPages),disabled:pagination.page===pagination.totalPages,className:"pagination-button",children:"Last"})]})]})})]})}PerformanceMetrics.__docgenInfo={description:"",methods:[],displayName:"PerformanceMetrics"};const components_AutoReplyLogs=AutoReplyLogs_AutoReplyLogs;function AutoReplyDashboard_AutoReplyDashboard(){(0,useFeatureGuard.B)("proA");const[stats,setStats]=(0,react.useState)({totalRules:0,activeRules:0,disabledRules:0,lastUpdated:null,status:"normal",dailyTriggers:[],topRules:[],unmatchedStats:{count:0,examples:[]}}),[loading,setLoading]=(0,react.useState)(!0),[error,setError]=(0,react.useState)(null),[activeTab,setActiveTab]=(0,react.useState)("overview");(0,react.useEffect)((()=>{fetchDashboardData()}),[]);const fetchDashboardData=async()=>{setLoading(!0),setError(null);try{const response=await axiosInstance.A.get("/api/auto-reply/stats");setStats(response.data||{totalRules:0,activeRules:0,disabledRules:0,lastUpdated:(new Date).toISOString(),status:"normal",dailyTriggers:[],topRules:[],unmatchedStats:{count:0,examples:[]}})}catch(err){console.error("Error fetching dashboard data:",err),setError("Failed to load dashboard data. Please try again later.")}finally{setLoading(!1)}};return(0,jsx_runtime.jsxs)("div",{className:"auto-reply-dashboard",children:[(0,jsx_runtime.jsxs)("div",{className:"dashboard-header",children:[(0,jsx_runtime.jsx)("h2",{children:"Auto Reply Control Center"}),(0,jsx_runtime.jsx)("div",{className:"api-management-link",children:(0,jsx_runtime.jsxs)("a",{href:"/platform/api-management",className:"btn btn-outline-primary",children:[(0,jsx_runtime.jsx)(index_es.g,{icon:free_solid_svg_icons.dB})," Manage Settings in API Platform"]})})]}),error&&(0,jsx_runtime.jsxs)("div",{className:"alert alert-danger",role:"alert",children:[(0,jsx_runtime.jsx)(index_es.g,{icon:free_solid_svg_icons.zpE})," ",error]}),(0,jsx_runtime.jsxs)("div",{className:"dashboard-tabs",children:[(0,jsx_runtime.jsxs)("button",{className:"tab-button "+("overview"===activeTab?"active":""),onClick:()=>setActiveTab("overview"),children:[(0,jsx_runtime.jsx)(index_es.g,{icon:free_solid_svg_icons.xiI})," Dashboard Overview"]}),(0,jsx_runtime.jsxs)("button",{className:"tab-button "+("test"===activeTab?"active":""),onClick:()=>setActiveTab("test"),children:[(0,jsx_runtime.jsx)(index_es.g,{icon:free_solid_svg_icons.a2R})," Test Console"]}),(0,jsx_runtime.jsxs)("button",{className:"tab-button "+("rules"===activeTab?"active":""),onClick:()=>setActiveTab("rules"),children:[(0,jsx_runtime.jsx)(index_es.g,{icon:free_solid_svg_icons.hhY})," Rule Browser"]}),(0,jsx_runtime.jsxs)("button",{className:"tab-button "+("logs"===activeTab?"active":""),onClick:()=>setActiveTab("logs"),children:[(0,jsx_runtime.jsx)(index_es.g,{icon:free_solid_svg_icons.Int})," Response Logs"]})]}),(0,jsx_runtime.jsx)("div",{className:"dashboard-content",children:loading&&"overview"===activeTab?(0,jsx_runtime.jsxs)("div",{className:"loading-spinner",children:[(0,jsx_runtime.jsx)("div",{className:"spinner"}),(0,jsx_runtime.jsx)("p",{children:"Loading dashboard data..."})]}):(()=>{switch(activeTab){case"overview":return(0,jsx_runtime.jsxs)("div",{className:"dashboard-overview",children:[(0,jsx_runtime.jsxs)("div",{className:"dashboard-row",children:[(0,jsx_runtime.jsx)(components_StatusCard,{totalRules:stats.totalRules,activeRules:stats.activeRules,disabledRules:stats.disabledRules,status:stats.status,lastUpdated:stats.lastUpdated,onRefresh:fetchDashboardData}),(0,jsx_runtime.jsx)(components_StatsOverview,{dailyTriggers:stats.dailyTriggers,topRules:stats.topRules})]}),(0,jsx_runtime.jsxs)("div",{className:"dashboard-row",children:[(0,jsx_runtime.jsx)(components_RecentActivity,{}),(0,jsx_runtime.jsx)(components_PerformanceMetrics,{})]})]});case"test":return(0,jsx_runtime.jsx)(components_TestConsole,{});case"rules":return(0,jsx_runtime.jsx)(components_RuleBrowser,{});case"logs":return(0,jsx_runtime.jsx)(components_AutoReplyLogs,{});default:return(0,jsx_runtime.jsx)("div",{children:"Select a tab to view content"})}})()})]})}AutoReplyLogs_AutoReplyLogs.__docgenInfo={description:"",methods:[],displayName:"AutoReplyLogs"};const dashboard_AutoReplyDashboard=AutoReplyDashboard_AutoReplyDashboard;AutoReplyDashboard_AutoReplyDashboard.__docgenInfo={description:"",methods:[],displayName:"AutoReplyDashboard"};var AuthContext=__webpack_require__("./src/context/AuthContext.js"),chunk_AYJ5UCUI=__webpack_require__("./node_modules/react-router/dist/development/chunk-AYJ5UCUI.mjs");const AutoReplyDashboard_stories={title:"Pages/Auto Reply Dashboard",tags:["backend"],component:dashboard_AutoReplyDashboard,decorators:[Story=>(0,jsx_runtime.jsx)(AuthContext.cy.Provider,{value:{user:{plan:"enterprise"}},children:(0,jsx_runtime.jsx)(chunk_AYJ5UCUI.fS,{initialEntries:["/data/agent-reply"],children:(0,jsx_runtime.jsx)(Story,{})})})]},Default=(args=>(0,jsx_runtime.jsx)(dashboard_AutoReplyDashboard,{})).bind({});Default.args={};const __namedExportsOrder=["Default"];Default.parameters={...Default.parameters,docs:{...Default.parameters?.docs,source:{originalSource:"args => <AutoReplyDashboard />",...Default.parameters?.docs?.source}}}},"./src/hooks/useFeatureGuard.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.d(__webpack_exports__,{B:()=>useFeatureGuard});var react__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("./node_modules/react/index.js"),react_router_dom__WEBPACK_IMPORTED_MODULE_2__=__webpack_require__("./node_modules/react-router/dist/development/chunk-AYJ5UCUI.mjs"),_context_AuthContext__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__("./src/context/AuthContext.js");const planOrder=["free","proA","proB","enterprise"];function useFeatureGuard(minPlan){const navigate=(0,react_router_dom__WEBPACK_IMPORTED_MODULE_2__.Zp)(),{user}=(0,_context_AuthContext__WEBPACK_IMPORTED_MODULE_1__.As)(),userPlan=(null==user?void 0:user.plan)||"free";(0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)((()=>{planOrder.indexOf(userPlan)<planOrder.indexOf(minPlan)&&navigate("/upgrade",{replace:!0})}),[userPlan,minPlan,navigate])}}}]);