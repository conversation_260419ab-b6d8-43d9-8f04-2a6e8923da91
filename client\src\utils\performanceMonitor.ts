/**
 * 性能监控工具
 * 监控应用性能指标并提供优化建议
 */

export interface PerformanceMetrics {
  fps: number;
  memoryUsage: number;
  loadTime: number;
  renderTime: number;
  networkLatency: number;
  bundleSize: number;
}

export interface PerformanceThresholds {
  fps: number;
  memoryUsage: number;
  loadTime: number;
  renderTime: number;
  networkLatency: number;
}

export class PerformanceMonitor {
  private metrics: PerformanceMetrics;
  private thresholds: PerformanceThresholds;
  private observers: PerformanceObserver[] = [];
  private isMonitoring: boolean = false;

  constructor() {
    this.metrics = {
      fps: 0,
      memoryUsage: 0,
      loadTime: 0,
      renderTime: 0,
      networkLatency: 0,
      bundleSize: 0
    };

    this.thresholds = {
      fps: 30,
      memoryUsage: 100, // MB
      loadTime: 3000, // ms
      renderTime: 16, // ms (60fps)
      networkLatency: 1000 // ms
    };
  }

  public startMonitoring(): void {
    if (this.isMonitoring) return;
    
    this.isMonitoring = true;
    this.setupPerformanceObservers();
    this.monitorFPS();
    this.monitorMemoryUsage();
    
    console.log('🚀 性能监控已启动');
  }

  public stopMonitoring(): void {
    this.isMonitoring = false;
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
    
    console.log('⏹️ 性能监控已停止');
  }

  private setupPerformanceObservers(): void {
    // 监控导航性能
    if ('PerformanceObserver' in window) {
      const navigationObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          if (entry.entryType === 'navigation') {
            const navEntry = entry as PerformanceNavigationTiming;
            this.metrics.loadTime = navEntry.loadEventEnd - navEntry.fetchStart;
            console.log('📊 页面加载时间:', this.metrics.loadTime.toFixed(2) + 'ms');
          }
        });
      });

      try {
        navigationObserver.observe({ entryTypes: ['navigation'] });
        this.observers.push(navigationObserver);
      } catch (e) {
        console.warn('无法监控导航性能:', e);
      }

      // 监控资源加载性能
      const resourceObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          if (entry.entryType === 'resource') {
            const resourceEntry = entry as PerformanceResourceTiming;
            if (resourceEntry.name.includes('.js') || resourceEntry.name.includes('.css')) {
              console.log('📦 资源加载:', resourceEntry.name, resourceEntry.duration.toFixed(2) + 'ms');
            }
          }
        });
      });

      try {
        resourceObserver.observe({ entryTypes: ['resource'] });
        this.observers.push(resourceObserver);
      } catch (e) {
        console.warn('无法监控资源性能:', e);
      }

      // 监控长任务
      const longTaskObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          if (entry.entryType === 'longtask') {
            console.warn('⚠️ 检测到长任务:', entry.duration.toFixed(2) + 'ms');
          }
        });
      });

      try {
        longTaskObserver.observe({ entryTypes: ['longtask'] });
        this.observers.push(longTaskObserver);
      } catch (e) {
        console.warn('无法监控长任务:', e);
      }
    }
  }

  private monitorFPS(): void {
    let frames = 0;
    let lastTime = performance.now();

    const countFrames = () => {
      if (!this.isMonitoring) return;

      frames++;
      const currentTime = performance.now();

      if (currentTime >= lastTime + 1000) {
        this.metrics.fps = Math.round((frames * 1000) / (currentTime - lastTime));
        frames = 0;
        lastTime = currentTime;

        if (this.metrics.fps < this.thresholds.fps) {
          console.warn('⚠️ FPS过低:', this.metrics.fps);
        }
      }

      requestAnimationFrame(countFrames);
    };

    requestAnimationFrame(countFrames);
  }

  private monitorMemoryUsage(): void {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      const usedMemory = memory.usedJSHeapSize / 1024 / 1024; // Convert to MB
      this.metrics.memoryUsage = usedMemory;

      if (usedMemory > this.thresholds.memoryUsage) {
        console.log('💾 Memory Usage:', usedMemory.toFixed(2) + ' MB');
      }

      // 定期检查内存使用
      setTimeout(() => {
        if (this.isMonitoring) {
          this.monitorMemoryUsage();
        }
      }, 5000);
    }
  }

  private isElementInViewport(element: Element): boolean {
    const rect = element.getBoundingClientRect();
    return (
      rect.top >= 0 &&
      rect.left >= 0 &&
      rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
      rect.right <= (window.innerWidth || document.documentElement.clientWidth)
    );
  }

  public measureOperation(name: string, operation: () => void): void {
    performance.mark(`${name}-start`);
    operation();
    performance.mark(`${name}-end`);
    performance.measure(name, `${name}-start`, `${name}-end`);

    const measure = performance.getEntriesByName(name)[0];
    console.log(`⏱️ ${name}:`, measure.duration + 'ms');

    // 清理标记
    performance.clearMarks();
    performance.clearMeasures();
  }

  public getPerformanceReport(): object {
    return {
      metrics: this.metrics,
      thresholds: this.thresholds,
      recommendations: this.generateRecommendations(),
      timestamp: new Date().toISOString()
    };
  }

  private generateRecommendations(): string[] {
    const recommendations: string[] = [];

    if (this.metrics.fps < this.thresholds.fps) {
      recommendations.push('考虑减少动画复杂度或使用CSS动画代替JavaScript动画');
    }

    if (this.metrics.memoryUsage > this.thresholds.memoryUsage) {
      recommendations.push('检查内存泄漏，考虑使用虚拟化列表或懒加载');
    }

    if (this.metrics.loadTime > this.thresholds.loadTime) {
      recommendations.push('优化图片大小，启用代码分割，使用CDN');
    }

    return recommendations;
  }

  public setThresholds(newThresholds: Partial<PerformanceThresholds>): void {
    this.thresholds = { ...this.thresholds, ...newThresholds };
  }

  public getCurrentMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }
}

// 全局性能监控器实例
export const performanceMonitor = new PerformanceMonitor();

export default performanceMonitor;