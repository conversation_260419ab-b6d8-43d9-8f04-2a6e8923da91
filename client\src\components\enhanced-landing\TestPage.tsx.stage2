/**
 * Test Page for Ultimate CTA and Footer Components
 * Use this to test the components independently during development
 */

import React from 'react';
import UltimateCTA from './UltimateCTA';
import UltimateFooter from './UltimateFooter';
import UltimateCtaFooterSection from './UltimateCtaFooterSection';

const TestPage: React.FC = () => {
  const handleStartTrial = () => {
    console.log('Test: Start trial clicked');
    alert('Start trial functionality would be implemented here');
  };

  const handleWatchDemo = () => {
    console.log('Test: Watch demo clicked');
    alert('Watch demo functionality would be implemented here');
  };

  return (
    <div className="min-h-screen">
      {/* Header */}
      <div className="bg-white py-8 px-4 text-center border-b">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Ultimate CTA & Footer Components Test
        </h1>
        <p className="text-gray-600">
          Testing the enhanced landing page components with multiple animation libraries
        </p>
      </div>
      
      {/* Hero placeholder */}
      <div className="bg-gradient-to-br from-purple-50 to-blue-50 py-20 px-4 text-center">
        <h2 className="text-2xl font-semibold text-gray-800 mb-4">
          Sample Content Above CTA
        </h2>
        <p className="text-gray-600 max-w-2xl mx-auto">
          This is a placeholder content section to demonstrate how the Ultimate CTA 
          and Footer components integrate with existing page content. Scroll down to 
          see the enhanced components in action.
        </p>
      </div>
      
      {/* Test Individual Components */}
      <div className="bg-white py-12 px-4">
        <div className="max-w-4xl mx-auto">
          <h3 className="text-xl font-semibold text-gray-800 mb-8 text-center">
            Individual Component Testing
          </h3>
          
          {/* Individual CTA Test */}
          <div className="mb-16 p-6 border border-gray-200 rounded-lg">
            <h4 className="text-lg font-medium text-gray-700 mb-6 text-center">Ultimate CTA Component</h4>
            <UltimateCTA
              onStartTrial={handleStartTrial}
              onWatchDemo={handleWatchDemo}
            />
          </div>
          
          {/* Individual Footer Test */}
          <div className="mb-16 border border-gray-200 rounded-lg overflow-hidden">
            <h4 className="text-lg font-medium text-gray-700 mb-6 text-center p-6 bg-gray-50">Ultimate Footer Component</h4>
            <UltimateFooter />
          </div>
        </div>
      </div>
      
      {/* Test Combined Component */}
      <div className="bg-gray-50 py-12 px-4">
        <div className="max-w-4xl mx-auto text-center mb-8">
          <h3 className="text-xl font-semibold text-gray-800 mb-4">
            Combined CTA + Footer Section
          </h3>
          <p className="text-gray-600">
            This demonstrates the seamless integration of both components:
          </p>
        </div>
        
        {/* Combined Component */}
        <UltimateCtaFooterSection
          onStartTrial={handleStartTrial}
          onWatchDemo={handleWatchDemo}
        />
      </div>
      
      {/* Debug Info */}
      <div className="bg-gray-900 text-white py-8 px-4 text-center text-sm">
        <p>🧪 Test Page - Check browser console for click events</p>
        <p>📱 Try different screen sizes to test responsiveness</p>
        <p>✨ Hover over buttons and links to see animations</p>
      </div>
    </div>
  );
};

export default TestPage;