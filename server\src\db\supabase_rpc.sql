-- u521bu5efaRPCu51fdu6570u6765u6267u884cu4efbu610fSQL
-- u6ce8u610fuff1au6b64u51fdu6570u9700u8981u4f7fu7528u670du52a1u89d2u8272u5bc6u94a5u6267u884cuff0cu5e76u4e14u53eau5e94u5728u53d7u63a7u73afu5883u4e2du4f7fu7528
CREATE OR REPLACE FUNCTION exec_sql(sql_str TEXT)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER -- u4f7fu7528u521bu5efau8005u7684u6743u9650u6267u884c
AS $$
DECLARE
  result JSONB;
BEGIN
  -- u6267u884cSQLu8bedu53e5
  EXECUTE sql_str;
  
  -- u8fd4u56deu6210u529fu6d88u606f
  result := jsonb_build_object('success', true, 'message', 'SQLu6267u884cu6210u529f');
  
  RETURN result;
EXCEPTION WHEN OTHERS THEN
  -- u5f53u51fau73b0u9519u8befu65f6u8fd4u56deu9519u8befu4fe1u606f
  result := jsonb_build_object(
    'success', false,
    'error', SQLERRM,
    'error_detail', SQLSTATE
  );
  
  RETURN result;
END;
$$; 