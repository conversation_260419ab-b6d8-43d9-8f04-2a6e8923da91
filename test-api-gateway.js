const axios = require('axios');

console.log('🧪 测试API Gateway POST请求...');

const testData = {
  name: 'API Gateway Test Agent',
  agentType: 'AI_AUTO_REPLY',
  description: 'Test through API Gateway',
  status: 'DRAFT'
};

console.log('📝 准备发送数据:', testData);

// 测试通过API Gateway
axios.post('http://localhost:3001/api/core/agents', testData, {
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})
.then(response => {
  console.log('✅ API Gateway请求成功:');
  console.log('状态码:', response.status);
  console.log('响应数据:', response.data);
})
.catch(error => {
  console.error('❌ API Gateway请求失败:');
  if (error.response) {
    console.error('状态码:', error.response.status);
    console.error('响应数据:', error.response.data);
  } else if (error.request) {
    console.error('请求错误:', error.request);
  } else {
    console.error('配置错误:', error.message);
  }
  console.error('完整错误:', error);
}); 