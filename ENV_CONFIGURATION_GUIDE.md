﻿# ========================================
# iBuddy2 环境变量配置完整指南
# ========================================

##  概述
本指南详细说明了如何为 iBuddy2 项目的各个服务配置环境变量。

##  已恢复的配置文件

###  服务状态
- **Server** (.env):  已创建完整配置
- **AI Service** (.env):  已创建完整配置  
- **Core Service** (.env):  已创建完整配置
- **API Gateway** (.env):  已创建完整配置

##  快速开始

### 1. 必需的最小配置
以下是每个服务正常运行所需的最小配置：

#### Server (.env)
`
PORT=3004
NODE_ENV=development
BYPASS_AUTH=true
ENABLE_TEST_RESPONSES=true
`

#### AI Service (.env)  
`
PORT=3003
NODE_ENV=development
ENABLE_TEST_RESPONSES=true
DISABLE_CACHE=true
`

#### Core Service (.env)
`
PORT=3002  
NODE_ENV=development
ENABLE_TEST_RESPONSES=true
`

#### API Gateway (.env)
`
PORT=3001
NODE_ENV=development
ENABLE_TEST_RESPONSES=true
`

### 2. 生产环境配置

#### 关键安全配置
- **JWT_SECRET**: 更改为强密码
- **ENCRYPTION_KEY**: 32字符加密密钥
- **SESSION_SECRET**: 会话密钥
- **WEBHOOK_SECRET**: Webhook验证密钥

#### 数据库配置 (Supabase)
`
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
`

#### AI 服务配置
`
# OpenAI
OPENAI_API_KEY=sk-your-key

# Google Gemini
GEMINI_API_KEY=your-gemini-key

# OpenRouter (备选)
OPENROUTER_API_KEY=your-openrouter-key
`

#### 社交媒体平台集成
`
# Facebook/Instagram
FACEBOOK_APP_ID=your-app-id
FACEBOOK_APP_SECRET=your-app-secret
INSTAGRAM_APP_ID=your-instagram-app-id
INSTAGRAM_APP_SECRET=your-instagram-app-secret

# TikTok
TIKTOK_CLIENT_KEY=your-client-key
TIKTOK_CLIENT_SECRET=your-client-secret

# Twitter/X
TWITTER_API_KEY=your-api-key
TWITTER_API_SECRET=your-api-secret
TWITTER_BEARER_TOKEN=your-bearer-token

# LinkedIn
LINKEDIN_CLIENT_ID=your-client-id
LINKEDIN_CLIENT_SECRET=your-client-secret
`

#### 电商平台集成
`
# Shopee
SHOPEE_PARTNER_ID=your-partner-id
SHOPEE_PARTNER_KEY=your-partner-key

# Lazada
LAZADA_APP_KEY=your-app-key
LAZADA_APP_SECRET=your-app-secret
`

#### 支付配置 (Stripe)
`
STRIPE_SECRET_KEY=sk_live_your-secret-key
STRIPE_PUBLISHABLE_KEY=pk_live_your-publishable-key
STRIPE_WEBHOOK_SECRET=whsec_your-webhook-secret
`

##  配置步骤

### 步骤 1: 复制环境变量文件
`ash
# 所有服务已经有 .env 文件，直接编辑即可
`

### 步骤 2: 配置基础服务
1. 设置 Supabase 数据库连接
2. 配置 JWT 密钥
3. 设置 AI 服务 API 密钥

### 步骤 3: 配置平台集成
1. 申请各社交媒体平台的开发者账号
2. 获取 API 密钥和应用凭据
3. 配置回调 URL

### 步骤 4: 配置支付系统
1. 设置 Stripe 账号
2. 获取 API 密钥
3. 配置 Webhook

### 步骤 5: 测试配置
`ash
npm run dev
`

##  安全注意事项

1. **绝不要** 将 .env 文件提交到版本控制
2. **定期更换** JWT 和加密密钥
3. **使用强密码** 为所有密钥和密码
4. **启用双因素认证** 为所有第三方服务
5. **定期审核** API 密钥使用情况

##  故障排除

### 常见问题
1. **服务启动失败**: 检查端口是否被占用
2. **API 调用失败**: 验证 API 密钥是否正确
3. **数据库连接失败**: 检查 Supabase 配置
4. **认证失败**: 验证 JWT 密钥配置

### 调试技巧
1. 设置 DEBUG=true 启用详细日志
2. 使用 ENABLE_TEST_RESPONSES=true 跳过外部服务
3. 设置 BYPASS_AUTH=true 跳过认证（仅开发环境）

##  支持
如遇到配置问题，请检查：
1. 环境变量文件是否存在
2. 变量名称是否正确
3. 值是否包含正确的格式
4. 服务是否能够读取环境变量

---
配置完成时间: 2025-06-23 21:50:14
