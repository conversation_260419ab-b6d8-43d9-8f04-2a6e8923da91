/**
 * 性能监控系统
 * 提供组件渲染追踪和性能指标收集功能
 */
import React from 'react';

// 存储渲染次数统计
const renderCounts: Record<string, number> = {};

// 存储性能标记点
const perfMarks: Record<string, number[]> = {};

// 调试模式标志
const isDebugMode = process.env.NODE_ENV === 'development'

/**
 * 记录组件渲染
 * @param componentName 组件名称
 * @returns 当前渲染次数
 */
export function trackComponentRender(componentName: string): number {
  if (!renderCounts[componentName]) {
    renderCounts[componentName] = 0
};
  
  renderCounts[componentName]++
  
  if (isDebugMode) {
    console.log(`[性能监控], 组件渲染: ${componentName} (第${renderCounts[componentName]}次)`)
};
  
  return renderCounts[componentName]
};

/**
 * 重置组件渲染计数
 * @param componentName 组件名称，不提供则重置所有
 */
export function resetRenderCount(componentName?: string): void {
  if (componentName) {
    renderCounts[componentName] = 0;
    if (isDebugMode) {
      console.log(`[性能监控], 重置组件计数: ${componentName}`);
    }
  } else {
    Object.keys(renderCounts).forEach(key => {
      renderCounts[key] = 0;
    });
    if (isDebugMode) {
      console.log('[性能监控] 重置所有组件计数');
    }
  }
};

/**
 * 获取组件渲染次数
 * @param componentName 组件名称
 * @returns 渲染次数
 */
export function getRenderCount(componentName: string): number {
  return renderCounts[componentName] || 0;
};

/**
 * 获取所有组件的渲染统计
 * @returns 渲染统计对象
 */
export function getAllRenderCounts(): Record<string, number> {
  return { ...renderCounts };
};

/**
 * 创建性能标记
 * @param name 标记名称
 */
export function markPerformance(name: string): void {
  const timestamp = performance.now();
  
  if (!perfMarks[name]) {
    perfMarks[name] = [];
  }
  
  perfMarks[name].push(timestamp);
  
  if (isDebugMode) {
    console.log(`[性能监控], 标记: ${name} @ ${timestamp.toFixed(2)}ms`);
  }
  
  // 同时使用浏览器内置性能API
  try {
    performance.mark(`${name}_${perfMarks[name].length}`);
  } catch (e) {
    // 忽略错误
  }
};

/**
 * 测量两个标记点之间的时间
 * @param startName 起始标记名称
 * @param endName 结束标记名称
 * @param measureName 测量名称
 * @returns 测量结果(毫秒)，失败返回-1
 */
export function measurePerformance(startName: string, endName: string, measureName: string): number {
  if (!perfMarks[startName] || !perfMarks[startName].length || 
      !perfMarks[endName] || !perfMarks[endName].length) {
    if (isDebugMode) {
      console.warn(`[性能监控], 无法测量: 缺少标记点 ${startName} 或 ${endName}`);
    }
    return -1;
  }
  
  const startTime = perfMarks[startName][perfMarks[startName].length - 1];
  const endTime = perfMarks[endName][perfMarks[endName].length - 1];
  const duration = endTime - startTime;
  
  if (isDebugMode) {
    console.log(`[性能监控] 测量, ${measureName}: ${duration.toFixed(2)}ms`);
  }
  
  // 同时使用浏览器内置性能API
  try {
    performance.measure(
      measureName,
      `${startName}_${perfMarks[startName].length}`,
      `${endName}_${perfMarks[endName].length}`
    );
  } catch (e) {
    // 忽略错误
  }
  
  return duration;
};

/**
 * 获取页面性能指标
 * @returns 页面性能指标对象
 */
export function getPagePerformanceMetrics(): Record<string, number> {
  const metrics: Record<string, number> = {};
  
  // 从浏览器性能API获取关键指标
  try {
    const perfEntries = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    
    if (perfEntries) {
      metrics.domInteractive = perfEntries.domInteractive;
      metrics.domContentLoaded = perfEntries.domContentLoadedEventEnd;
      metrics.loadComplete = perfEntries.loadEventEnd;
      metrics.firstByte = perfEntries.responseStart;
      metrics.dnsLookup = perfEntries.domainLookupEnd - perfEntries.domainLookupStart;
      metrics.tcpConnection = perfEntries.connectEnd - perfEntries.connectStart;
      metrics.requestTime = perfEntries.responseStart - perfEntries.requestStart;
      metrics.responseTime = perfEntries.responseEnd - perfEntries.responseStart;
      metrics.domProcessing = perfEntries.domComplete - perfEntries.domInteractive;
    }
  } catch (e) {
    console.error('[性能监控], 获取性能指标失败:', e);
  }
  
  return metrics;
};

/**
 * 创建React组件性能追踪HOC
 * 装饰器版本
 */
export function trackRender<Props extends Record<string, any>>(
  Component: React.ComponentType<Props>,
  name?: string
): React.FC<Props> {
  const displayName = name || Component.displayName || Component.name || 'UnknownComponent';
  
  const WrappedComponent: React.FC<Props> = (props) => {
    trackComponentRender(displayName);
    return React.createElement(Component, props);
  };
  
  WrappedComponent.displayName = `Tracked(${displayName})`;
  return WrappedComponent;
};

/**
 * 重置所有性能监控数据
 */
export function resetAllPerformanceData(): void {
  // 重置渲染计数
  Object.keys(renderCounts).forEach(key => {
    renderCounts[key] = 0;
  });
  
  // 重置性能标记
  Object.keys(perfMarks).forEach(key => {
    perfMarks[key] = [];
  });
  
  // 清除浏览器性能条目
  try {
    performance.clearMarks();
    performance.clearMeasures();
  } catch (e) {
    // 忽略错误
  }
  
  if (isDebugMode) {
    console.log('[性能监控] 已重置所有性能数据');
  }
};

/**
 * 创建自定义组件性能跟踪Hook
 * @param componentName 组件名称
 * @returns 渲染次数统计对象
 */
export function useRenderTracking(componentName: string): { 
  renderCount: number;
  logRender: (message?: string) => void;
} {
  const renderCount = trackComponentRender(componentName);
  
  const logRender = (message?: string) => {
    if (isDebugMode) {
      console.log(`[性能监控] ${componentName} 渲染 #${renderCount}${message ? ': ' + message : ''}`);
    }
  };
  
  return { renderCount, logRender };
};

const performanceMonitor = {
  trackComponentRender,
  resetRenderCount,
  getRenderCount,
  getAllRenderCounts,
  markPerformance,
  measurePerformance,
  getPagePerformanceMetrics,
  trackRender,
  resetAllPerformanceData,
  // useRenderTracking
};

export default performanceMonitor;