-- 为 appointments 表添加通知相关列
-- 使用匿名 PL/pgSQL 代码块确保幂等性（可重复执行）

DO $$ 
BEGIN
    -- 添加 booked_via_channel 列
    IF NOT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'appointments' 
        AND column_name = 'booked_via_channel'
    ) THEN
        ALTER TABLE public.appointments ADD COLUMN booked_via_channel TEXT;
        COMMENT ON COLUMN public.appointments.booked_via_channel IS '预约创建的渠道，如：walkin, onsite_portal, messenger, instagram, whatsapp, email, manual';
        RAISE NOTICE '已添加 booked_via_channel 列';
        
        -- 从现有的 source 字段复制数据到 booked_via_channel
        UPDATE public.appointments 
        SET booked_via_channel = source 
        WHERE source IS NOT NULL AND booked_via_channel IS NULL;
        
        RAISE NOTICE '已将 source 值迁移到 booked_via_channel 列';
    ELSE
        RAISE NOTICE 'booked_via_channel 列已存在，跳过';
    END IF;
    
    -- 添加 notification_status 列
    IF NOT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'appointments' 
        AND column_name = 'notification_status'
    ) THEN
        ALTER TABLE public.appointments ADD COLUMN notification_status TEXT;
        COMMENT ON COLUMN public.appointments.notification_status IS '通知状态，如：pending, sent, error';
        RAISE NOTICE '已添加 notification_status 列';
    ELSE
        RAISE NOTICE 'notification_status 列已存在，跳过';
    END IF;

    -- 添加 last_notification_sent 列
    IF NOT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'appointments' 
        AND column_name = 'last_notification_sent'
    ) THEN
        ALTER TABLE public.appointments ADD COLUMN last_notification_sent TIMESTAMPTZ;
        COMMENT ON COLUMN public.appointments.last_notification_sent IS '最近一次通知发送时间';
        RAISE NOTICE '已添加 last_notification_sent 列';
    ELSE
        RAISE NOTICE 'last_notification_sent 列已存在，跳过';
    END IF;
END $$; 