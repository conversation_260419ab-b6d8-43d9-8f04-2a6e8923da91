const express = require('express');
const router = express.Router();
const supabase = require('../config/supabaseClient'); // Import the supabase client

// Remove in-memory store as we will use Supabase
// let agentsStore = [...]; 

/**
 * @route   GET /api/agents
 * @desc    Get a list of agents from Supabase
 * @access  Private
 */
router.get('/', async (req, res) => {
  if (!supabase) {
    return res.status(500).json({ message: 'Supabase client not initialized. Check server configuration.' });
  }
  try {
    const { type } = req.query; // 获取类型过滤参数
    
    let query = supabase.from('agents').select('*');
    
    // 如果指定了类型过滤，添加过滤条件
    if (type) {
      query = query.eq('agentType', type);
    }
    
    const { data: agents, error } = await query;

    if (error) {
      console.error('[Server] Error fetching agents from Supabase:', error); 
      return res.status(500).json({ message: 'Failed to fetch agents.', error: error.message });
    }

    // 🎨 如果没有找到agents且请求的是lead-generation类型，返回模拟数据
    if ((!agents || agents.length === 0) && type === 'lead-generation') {
      const mockLeadGenerationAgents = [
        {
          id: 'agent-lead-1',
          name: 'Lead Capture Bot',
          agentType: 'lead-generation',
          description: 'Intelligent lead capture and qualification assistant',
          status: 'ACTIVE',
          createdAt: new Date().toISOString(),
          keyMetrics: {
            leadsGenerated: 287,
            conversionRate: 28.5,
            averageScore: 73
          }
        },
        {
          id: 'agent-lead-2',
          name: 'Qualification Assistant',
          agentType: 'lead-generation',
          description: 'Advanced lead scoring and qualification system',
          status: 'ACTIVE',
          createdAt: new Date().toISOString(),
          keyMetrics: {
            leadsGenerated: 156,
            conversionRate: 31.2,
            averageScore: 78
          }
        }
      ];
      
      console.log('[Server] GET /api/agents - Returning mock lead-generation agents');
      return res.json({ 
        message: 'Successfully fetched lead-generation agents (mock data)',
        data: mockLeadGenerationAgents
      });
    }

    console.log('[Server] GET /api/agents - Fetched from Supabase. Count:', agents ? agents.length : 0);
    res.json({ 
      message: 'Successfully fetched agents from Supabase',
      data: agents || [] 
    });
  } catch (err) {
    console.error('[Server] Unexpected error in GET /api/agents:', err); 
    res.status(500).json({ message: 'An unexpected error occurred.', error: err.message });
  }
});

/**
 * @route   GET /api/agents/:id
 * @desc    Get a single agent by ID from Supabase
 * @access  Private
 */
router.get('/:id', async (req, res) => {
  if (!supabase) {
    return res.status(500).json({ message: 'Supabase client not initialized.' });
  }
  try {
    const agentId = req.params.id;
    const { data: agent, error } = await supabase
      .from('agents')
      .select('*')
      .eq('id', agentId)
      .single(); // Use .single() if you expect at most one row

    if (error) {
      if (error.code === 'PGRST116' || error.details?.includes('matched 0 rows')) { // PGRST116: Row not found
        return res.status(404).json({ message: `Agent with ID: ${agentId} not found.` });
      }
      console.error(`[Server] Error fetching agent ${agentId} from Supabase:`, error);
      return res.status(500).json({ message: 'Failed to fetch agent.', error: error.message });
    }

    if (agent) {
      res.json({ 
        message: `Successfully fetched agent with ID: ${agentId} from Supabase`,
        data: agent 
      });
    } else {
      // This case should ideally be caught by .single() error handling for not found
      res.status(404).json({ message: `Agent with ID: ${agentId} not found.` });
    }
  } catch (err) {
    console.error('[Server] Unexpected error in GET /api/agents/:id:', err); 
    res.status(500).json({ message: 'An unexpected error occurred.', error: err.message });
  }
});

/**
 * @route   POST /api/agents
 * @desc    Create a new agent in Supabase
 * @access  Private
 */
router.post('/', async (req, res) => {
  if (!supabase) {
    return res.status(500).json({ message: 'Supabase client not initialized.' });
  }
  try {
    const { name, agentType, description, status } = req.body;

    if (!name || !agentType) {
      return res.status(400).json({ message: 'Name and agentType are required.' });
    }

    // Prepare the object to insert. Ensure your Supabase table columns match these fields.
    // Supabase typically handles 'createdAt' and 'updatedAt' automatically if configured with default values (e.g., now()).
    // The 'id' is usually a UUID generated by Supabase by default for new rows if it's a primary key.
    const agentToInsert = {
      name,
      agentType, // Ensure this type matches your Supabase column type (e.g., text or an enum)
      description: description || null, // Use null for empty optional fields if your DB expects it
      status: status || 'DRAFT', // Ensure this type matches your Supabase column type
      // keyMetrics might be a JSONB column, send an empty object or null as appropriate
      // keyMetrics: {}, 
    };

    const { data: newAgent, error } = await supabase
      .from('agents')
      .insert([agentToInsert])
      .select() // Return the newly created record(s)
      .single(); // Assuming we want the single created record back

    if (error) {
      console.error('[Server] Error creating agent in Supabase:', error);
      // You might want to inspect error.code or error.details for more specific error handling
      // e.g., unique constraint violation, foreign key violation, etc.
      return res.status(500).json({ message: 'Failed to create agent.', error: error.message });
    }

    console.log('[Server] POST /api/agents - Agent added to Supabase. ID:', newAgent.id); 
    res.status(201).json({
      message: 'Agent created successfully in Supabase',
      data: newAgent 
    });

  } catch (err) {
    console.error('[Server] Unexpected error in POST /api/agents:', err); 
    res.status(500).json({ message: 'An unexpected error occurred.', error: err.message });
  }
});

/**
 * @route   DELETE /api/agents/:id
 * @desc    Delete an agent by ID from Supabase
 * @access  Private
 */
router.delete('/:id', async (req, res) => {
  if (!supabase) {
    return res.status(500).json({ message: 'Supabase client not initialized.' });
  }
  try {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json({ message: 'Agent ID is required.' });
    }

    // Attempt to delete the agent
    // .select() after .delete() can be used to get the deleted record(s) if needed, though often not required.
    const { error, count } = await supabase
      .from('agents')
      .delete()
      .eq('id', id); // or .eq('id', id)

    if (error) {
      console.error(`[Server] Error deleting agent ${id} from Supabase:`, error);
      return res.status(500).json({ message: 'Failed to delete agent.', error: error.message });
    }

    if (count === 0) { // If count is available and indicates no rows were deleted
        return res.status(404).json({ message: `Agent with ID: ${id} not found for deletion.` });
    }

    console.log(`[Server] DELETE /api/agents/${id} - Agent deleted from Supabase.`); 
    res.status(200).json({ 
      message: `Agent with ID: ${id} successfully deleted from Supabase.`,
      agentId: id 
    });
  } catch (err) {
    console.error('[Server] Unexpected error in DELETE /api/agents/:id:', err); 
    res.status(500).json({ message: 'An unexpected error occurred.', error: err.message });
  }
});

/**
 * @route   PATCH /api/agents/:id/status
 * @desc    Update agent status in Supabase
 * @access  Private
 */
router.patch('/:id/status', async (req, res) => {
  if (!supabase) {
    return res.status(500).json({ message: 'Supabase client not initialized.' });
  }
  try {
    const { id } = req.params;
    const { status } = req.body;

    if (!id) {
      return res.status(400).json({ message: 'Agent ID is required.' });
    }

    // Validate status - adjust as per your allowed AgentStatus values
    const allowedStatuses = ['ACTIVE', 'INACTIVE', 'DRAFT', 'ERROR', 'PENDING']; 
    if (!status || !allowedStatuses.includes(status)) {
      return res.status(400).json({ 
        message: `Invalid status value. Must be one of: ${allowedStatuses.join(' ')}` 
      });
    }

    const { data: updatedAgent, error } = await supabase
      .from('agents')
      .update({ status, updatedAt: new Date().toISOString() }) // Also update updatedAt timestamp
      .eq('id', id)
      .select()
      .single();

    if (error) {
      if (error.code === 'PGRST116' || error.details?.includes('matched 0 rows')) { // PGRST116: Row not found
        return res.status(404).json({ message: `Agent with ID: ${id} not found for status update.` });
      }
      console.error(`[Server] Error updating status for agent ${id} in Supabase:`, error);
      return res.status(500).json({ message: 'Failed to update agent status.', error: error.message });
    }
    
    if (!updatedAgent) { // Should be caught by error handling above ideally
        return res.status(404).json({ message: `Agent with ID: ${id} not found after attempting update.` });
    }

    console.log(`[Server] PATCH /api/agents/${id}/status - Status updated to ${status}`); 
    res.status(200).json({
      message: `Agent ${id} status updated to ${status}`,
      data: updatedAgent 
    });

  } catch (err) {
    console.error('[Server] Unexpected error in PATCH /api/agents/:id/status:', err); 
    res.status(500).json({ message: 'An unexpected error occurred.', error: err.message });
  }
});

/**
 * @route   GET /api/agents/:id/settings
 * @desc    Get agent settings (name, description, status as active, config)
 * @access  Private
 */
router.get('/:id/settings', async (req, res) => {
  if (!supabase) {
    return res.status(500).json({ message: 'Supabase client not initialized.' });
  }
  try {
    const { id } = req.params;
    const { data: agent, error } = await supabase
      .from('agents')
      .select('id, name, description, status, config') // Select specific fields including the new config column
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116' || error.details?.includes('matched 0 rows')) {
        return res.status(404).json({ message: `Agent with ID: ${id} not found for settings.` });
      }
      console.error(`[Server] Error fetching settings for agent ${id}:`, error);
      return res.status(500).json({ message: 'Failed to fetch agent settings.', error: error.message });
    }

    if (agent) {
      // Transform status to active for frontend compatibility if AgentSettingsTab expects 'active' boolean
      const settingsData = {
        ...agent,
        active: agent.status === 'ACTIVE'
      };
      // delete settingsData.status; // Optionally remove original status if only active is needed by tab
      res.json({ 
        message: `Successfully fetched settings for agent ${id}`,
        data: settingsData 
      });
    } else {
      res.status(404).json({ message: `Agent with ID: ${id} not found.` });
    }
  } catch (err) {
    console.error('[Server] Unexpected error in GET /api/agents/:id/settings:', err); 
    res.status(500).json({ message: 'An unexpected error occurred.', error: err.message });
  }
});

/**
 * @route   PUT /api/agents/:id/settings
 * @desc    Update agent settings (name, description, status via active, config)
 * @access  Private
 */
router.put('/:id/settings', async (req, res) => {
  if (!supabase) {
    return res.status(500).json({ message: 'Supabase client not initialized.' });
  }
  try {
    const { id } = req.params;
    const { name, description, active, config } = req.body; // 'active' is boolean from frontend
    
    console.log('[Server] PUT /api/agents/:id/settings - Received request:', {
      agentId: id,
      body: req.body
    });

    // Validate payload as needed
    if (typeof name === 'undefined' && typeof description === 'undefined' && typeof active === 'undefined' && typeof config === 'undefined') {
        console.log('[Server] PUT /api/agents/:id/settings - No settings provided to update.'); 
        return res.status(400).json({ message: 'No settings provided to update.' });
    }

    const updatePayload = {
        updatedAt: new Date().toISOString(),
    };

    if (typeof name !== 'undefined') updatePayload.name = name;
    if (typeof description !== 'undefined') updatePayload.description = description;
    if (typeof active !== 'undefined') {
        // Convert boolean 'active' back to AgentStatus enum for Supabase
        updatePayload.status = active ? 'ACTIVE' : 'INACTIVE';
    }
    if (typeof config !== 'undefined') updatePayload.config = config;
    
    console.log('[Server] PUT /api/agents/:id/settings - Update payload:', updatePayload);

    const { data: updatedSettings, error } = await supabase
      .from('agents')
      .update(updatePayload)
      .eq('id', id)
      .select('id, name, description, status, config') // Return the updated fields
      .single();
    
    console.log('[Server] PUT /api/agents/:id/settings - Supabase response:', {
      success: !error,
      data: updatedSettings,
      error: error ? {
        code: error.code,
        message: error.message,
        details: error.details
      } : null
    });

    if (error) {
      if (error.code === 'PGRST116' || error.details?.includes('matched 0 rows')) {
        return res.status(404).json({ message: `Agent with ID: ${id} not found for update.` });
      }
      console.error(`[Server] Error updating settings for agent ${id}:`, error);
      return res.status(500).json({ message: 'Failed to update agent settings.', error: error.message });
    }

    if (updatedSettings) {
      const responseData = {
          ...updatedSettings,
          active: updatedSettings.status === 'ACTIVE'
      };
      console.log('[Server] PUT /api/agents/:id/settings - Success sending response:', responseData);
      res.json({
        message: `Successfully updated settings for agent ${id}`,
        data: responseData 
      });
    } else {
      console.log('[Server] PUT /api/agents/:id/settings - No settings found after update'); 
      res.status(404).json({ message: `Agent with ID: ${id} not found (post-update check).` }); // Should be rare
    }

  } catch (err) {
    console.error('[Server] Unexpected error in PUT /api/agents/:id/settings:', err); 
    res.status(500).json({ message: 'An unexpected error occurred.', error: err.message });
  }
});

/**
 * @route   GET /api/agents/:id/data
 * @desc    Get agent performance data for charts/analytics
 * @access  Private
 */
router.get('/:id/data', async (req, res) => {
  if (!supabase) {
    return res.status(500).json({ message: 'Supabase client not initialized.' });
  }
  try {
    const { id } = req.params;
    const { timeRange } = req.query; // Optional: 'day', 'week', 'month', 'year'

    // First verify if the agent exists
    const { data: agent, error: agentError } = await supabase
      .from('agents')
      .select('id, name, agentType')
      .eq('id', id)
      .single();

    if (agentError) {
      console.error(`[Server] Error verifying agent ${id} existence:`, agentError);
      return res.status(500).json({ message: 'Failed to verify agent.', error: agentError.message });
    }

    // In a production app, you would query actual performance data from a dedicated table
    // For now, we'll generate mock data based on agent type

    // Generate mock summary data
    let summary = {
      totalInteractions: Math.floor(Math.random() * 1000),
      successRate: Math.floor(Math.random() * 30) + 70, // 70-100%
      avgResponseTime: (Math.random() * 2 + 0.5).toFixed(1), // 0.5-2.5 seconds
      activeUsers: Math.floor(Math.random() * 200),
    };

    // Add agent type-specific summary metrics
    if (agent.agentType === 'AI_AUTO_REPLY') {
      summary = {
        ...summary,
        totalMessages: Math.floor(Math.random() * 5000),
        automatedReplies: Math.floor(Math.random() * 3000),
        humanEscalations: Math.floor(Math.random() * 500),
      };
    } else if (agent.agentType === 'BOOKING_ONSITE' || agent.agentType === 'BOOKING_WALKIN') {
      summary = {
        ...summary,
        totalBookings: Math.floor(Math.random() * 300),
        cancelledBookings: Math.floor(Math.random() * 50),
        avgBookingValue: Math.floor(Math.random() * 500) + 100,
      };
    }

    // Generate mock chart data - last 7 days
    const now = new Date();
    const dailyData = Array.from({ length: 7 }, (_, i) => {
      const date = new Date();
      date.setDate(now.getDate() - 6 + i);
      return {
        date: date.toISOString().split('T')[0], // YYYY-MM-DD
        interactions: Math.floor(Math.random() * 200),
        successRate: Math.floor(Math.random() * 30) + 70,
        responseTime: (Math.random() * 2 + 0.5).toFixed(1),
      };
    });

    // Generate mock hourly data for today
    const hourlyData = Array.from({ length: 24 }, (_, i) => {
      return {
        date: `${now.toISOString().split('T')[0]}T${String(i).padStart(2, '0')}:00:00Z`,
        interactions: Math.floor(Math.random() * 30),
        successRate: Math.floor(Math.random() * 30) + 70,
      };
    });

    // Final response structure
    const agentData = {
      summary,
      chartsData: {
        daily: dailyData,
        hourly: hourlyData,
      }
    };

    res.json({ 
      message: `Successfully fetched data for agent ${id}`,
      data: agentData
    });
  } catch (err) {
    console.error('[Server] Unexpected error in GET /api/agents/:id/data:', err); 
    res.status(500).json({ message: 'An unexpected error occurred.', error: err.message });
  }
});

/**
 * @route   GET /api/agents/:id/logs
 * @desc    Get agent logs with pagination and filtering
 * @access  Private
 */
router.get('/:id/logs', async (req, res) => {
  if (!supabase) {
    return res.status(500).json({ message: 'Supabase client not initialized.' });
  }
  try {
    const { id } = req.params;
    const { 
      page = 1, 
      limit = 20, 
      level, 
      searchTerm 
    } = req.query;

    // Validate pagination params
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);
    if (isNaN(pageNum) || isNaN(limitNum) || pageNum < 1 || limitNum < 1 || limitNum > 100) {
      return res.status(400).json({ message: 'Invalid pagination parameters.' });
    }

    // First verify if the agent exists
    const { data: agent, error: agentError } = await supabase
      .from('agents')
      .select('id')
      .eq('id', id)
      .single();

    if (agentError) {
      if (agentError.code === 'PGRST116' || agentError.details?.includes('matched 0 rows')) {
        return res.status(404).json({ message: `Agent with ID: ${id} not found.` });
      }
      console.error(`[Server] Error verifying agent ${id} existence:`, agentError);
      return res.status(500).json({ message: 'Failed to verify agent.', error: agentError.message });
    }

    // In a real app, you would query logs from a database table
    // Here we'll generate mock log data

    // Generate mock log entries
    const logLevels = ['INFO', 'WARNING', 'ERROR'];
    const commonMessages = {
      INFO: [
        'User message received',
        'Response sent to user',
        'Agent configuration updated',
        'Session started',
        'Session ended successfully'
      ],
      WARNING: [
        'Slow response time detected',
        'Missing context in user query',
        'Fallback response used',
        'Rate limit approaching',
        'Low confidence in response'
      ],
      ERROR: [
        'Failed to connect to external service',
        'Response generation failed',
        'Database query error',
        'Authentication failure',
        'Rate limit exceeded'
      ]
    };

    // Generate a more realistic set of logs (newest first)
    const totalLogs = 100; // Total number of mock logs
    const mockLogs = Array.from({ length: totalLogs }, (_, i) => {
      // Distribute levels: 70% INFO, 20% WARNING, 10% ERROR
      const levelIndex = Math.random() < 0.7 ? 0 : (Math.random() < 0.67 ? 1 : 2);
      const level = logLevels[levelIndex];
      
      // Generate timestamp (more recent for lower index)
      const timestamp = new Date();
      timestamp.setMinutes(timestamp.getMinutes() - i * 10); // Each log is ~10 minutes apart
      
      // Get a random message for the level
      const messages = commonMessages[level];
      const message = messages[Math.floor(Math.random() * messages.length)];
      
      // Add some details
      const details = {
        sessionId: `session-${Math.floor(Math.random() * 1000)}`,
        userId: `user-${Math.floor(Math.random() * 100)}`,
        processingTime: Math.floor(Math.random() * 2000),
      };
      
      // Add error specific details
      if (level === 'ERROR') {
        details.errorCode = `ERR-${Math.floor(Math.random() * 100)}`;
        details.stackTrace = 'Error: ' + message + '\n    at AgentHandler.processRequest (/app/handlers/agent.js:42:12)'
      }
      
      return {
        id: `log-${i + 1}`,
        agentId: id,
        timestamp: timestamp.toISOString(),
        level,
        message,
        details
      };
    });

    // Filter logs based on query params
    let filteredLogs = [...mockLogs];
    
    if (level) {
      filteredLogs = filteredLogs.filter(log => log.level === level.toUpperCase());
    }
    
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filteredLogs = filteredLogs.filter(log => 
        log.message.toLowerCase().includes(term) || 
        JSON.stringify(log.details).toLowerCase().includes(term)
      );
    }
    
    // Apply pagination
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = startIndex + limitNum;
    const paginatedLogs = filteredLogs.slice(startIndex, endIndex);
    
    // Response with pagination metadata
    res.json({
      message: `Successfully fetched logs for agent ${id}`,
      data: {
        logs: paginatedLogs,
        total: filteredLogs.length,
        page: pageNum,
        limit: limitNum,
        totalPages: Math.ceil(filteredLogs.length / limitNum)
      }
    });
    
  } catch (err) {
    console.error('[Server] Unexpected error in GET /api/agents/:id/logs:', err); 
    res.status(500).json({ message: 'An unexpected error occurred.', error: err.message });
  }
});

/**
 * @route   GET /api/agents/:id/rules
 * @desc    Get all rules for a specific agent from Supabase
 * @access  Private
 */
router.get('/:id/rules', async (req, res) => {
  if (!supabase) {
    return res.status(500).json({ message: 'Supabase client not initialized.' });
  }
  try {
    const { id: agentId } = req.params;

    // First, verify the agent exists (optional, but good practice)
    const { data: agent, error: agentError } = await supabase
      .from('agents')
      .select('id')
      .eq('id', agentId)
      .single();

    if (agentError || !agent) {
      return res.status(404).json({ message: `Agent with ID: ${agentId} not found.` });
    }

    const { data: rules, error: rulesError } = await supabase
      .from('agent_rules') // Ensure this table name matches your Supabase table
      .select('*')
      .eq('agent_id', agentId)
      .order('priority', { ascending: true }); // Order by priority

    if (rulesError) {
      console.error(`[Server], Error, fetching, rules, for, agent, ${agentId}:`, rulesError);
      return res.status(500).json({ message: 'Failed to fetch agent rules.', error: rulesError.message });
    }

    res.json({ 
      message: `Successfully fetched rules for agent ${agentId}`,
      data: rules || [] 
    });

  } catch (err) {
    console.error('[Server], Unexpected, error, in, GET, /api/agents/:id/rules:', err); 
    res.status(500).json({ message: 'An unexpected error occurred.', error: err.message });
  }
});

/**
 * @route   POST /api/agents/:id/rules
 * @desc    Create a new rule for a specific agent in Supabase
 * @access  Private
 */
router.post('/:id/rules', async (req, res) => {
  if (!supabase) {
    return res.status(500).json({ message: 'Supabase client not initialized.' });
  }
  try {
    const { id: agentId } = req.params;
    const { 
      pattern, 
      response, 
      priority = 0, 
      isRegex = false, 
      isActive = true 
    } = req.body;

    if (!pattern || !response) {
      return res.status(400).json({ message: 'Pattern and response are required for a rule.' });
    }

    // Optional: Verify agent exists before adding a rule
    const { data: agent, error: agentError } = await supabase
      .from('agents')
      .select('id')
      .eq('id', agentId)
      .single();

    if (agentError || !agent) {
      return res.status(404).json({ message: `Agent with ID: ${agentId} not found, cannot create rule.` });
    }

    const ruleToInsert = {
      agent_id: agentId,
      pattern,
      response,
      priority,
      is_regex: isRegex, // Snake case for DB
      is_active: isActive, // Snake case for DB
      // Supabase handles created_at and updated_at by default if columns are set up correctly
    };

    const { data: newRule, error: insertError } = await supabase
      .from('agent_rules')
      .insert([ruleToInsert])
      .select()
      .single(); 

    if (insertError) {
      console.error(`[Server], Error, creating, rule, for, agent, ${agentId}:`, insertError);
      // TODO: Handle specific DB errors like unique constraints if any
      return res.status(500).json({ message: 'Failed to create agent rule.', error: insertError.message });
    }

    console.log(`[Server], POST, /api/agents/${agentId}/rules, -, Rule, added., ID:`, newRule.id);
    res.status(201).json({
      message: 'Agent rule created successfully',
      data: newRule 
    });

  } catch (err) {
    console.error('[Server], Unexpected, error, in, POST, /api/agents/:id/rules:', err); 
    res.status(500).json({ message: 'An unexpected error occurred.', error: err.message });
  }
});

/**
 * @route   PUT /api/agents/:id/rules/:ruleId
 * @desc    Update an existing rule for a specific agent in Supabase
 * @access  Private
 */
router.put('/:id/rules/:ruleId', async (req, res) => {
  if (!supabase) {
    return res.status(500).json({ message: 'Supabase client not initialized.' });
  }
  try {
    const { id: agentId, ruleId } = req.params;
    const { 
      pattern, 
      response, 
      priority, 
      isRegex, 
      isActive 
    } = req.body;

    // Construct an update object only with provided fields
    const ruleToUpdate = {};
    if (pattern !== undefined) ruleToUpdate.pattern = pattern;
    if (response !== undefined) ruleToUpdate.response = response;
    if (priority !== undefined) ruleToUpdate.priority = priority;
    if (isRegex !== undefined) ruleToUpdate.is_regex = isRegex; // Snake case
    if (isActive !== undefined) ruleToUpdate.is_active = isActive; // Snake case
    
    if (Object.keys(ruleToUpdate).length === 0) {
        return res.status(400).json({ message: 'No fields provided to update.' });
    }

    ruleToUpdate.updated_at = new Date().toISOString(); // Manually update updated_at

    const { data: updatedRule, error: updateError } = await supabase
      .from('agent_rules')
      .update(ruleToUpdate)
      .eq('id', ruleId)
      .eq('agent_id', agentId) // Ensure the rule belongs to the agent
      .select()
      .single();

    if (updateError) {
      if (updateError.code === 'PGRST116') { // PostgREST error for "טים שורות לא נמצאו"
        return res.status(404).json({ message: `Rule with ID: ${ruleId} for agent ID: ${agentId} not found or not matching.` });
      }
      console.error(`[Server], Error, updating, rule, ${ruleId}, for, agent, ${agentId}:`, updateError);
      return res.status(500).json({ message: 'Failed to update agent rule.', error: updateError.message });
    }
    
    if (!updatedRule) { // Should be caught by PGRST116, but as a fallback
        return res.status(404).json({ message: `Rule with ID: ${ruleId} not found after attempting update.` });
    }

    console.log(`[Server], PUT, /api/agents/${agentId}/rules/${ruleId}, -, Rule, updated.`); 
    res.status(200).json({
      message: 'Agent rule updated successfully',
      data: updatedRule 
    });

  } catch (err) {
    console.error('[Server], Unexpected, error, in, PUT, /api/agents/:id/rules/:ruleId:', err); 
    res.status(500).json({ message: 'An unexpected error occurred.', error: err.message });
  }
});

/**
 * @route   DELETE /api/agents/:id/rules/:ruleId
 * @desc    Delete a rule for a specific agent from Supabase
 * @access  Private
 */
router.delete('/:id/rules/:ruleId', async (req, res) => {
  if (!supabase) {
    return res.status(500).json({ message: 'Supabase client not initialized.' });
  }
  try {
    const { id: agentId, ruleId } = req.params;

    const { error: deleteError, count } = await supabase
      .from('agent_rules')
      .delete()
      .eq('id', ruleId)
      .eq('agent_id', agentId); // Ensure deleting rule that belongs to the agent

    if (deleteError) {
      console.error(`[Server], Error, deleting, rule, ${ruleId}, for, agent, ${agentId}:`, deleteError);
      return res.status(500).json({ message: 'Failed to delete agent rule.', error: deleteError.message });
    }

    if (count === 0) {
      return res.status(404).json({ message: `Rule with ID: ${ruleId} for agent ID: ${agentId} not found for deletion.` });
    }

    console.log(`[Server], DELETE, /api/agents/${agentId}/rules/${ruleId}, -, Rule, deleted.`); 
    res.status(200).json({ 
      message: `Agent rule with ID: ${ruleId} successfully deleted.`,
      ruleId: ruleId
    });

  } catch (err) {
    console.error('[Server], Unexpected, error, in, DELETE, /api/agents/:id/rules/:ruleId:', err); 
    res.status(500).json({ message: 'An unexpected error occurred.', error: err.message });
  }
});

module.exports = router; 