#!/usr/bin/env node

const http = require('http');

console.log('╔══════════════════════════════════════════╗');
console.log('║       iTeraBiz 应用访问测试               ║');
console.log('╚══════════════════════════════════════════╝');

function testEndpoint(name, port, path) {
  return new Promise((resolve) => {
    const options = {
      hostname: 'localhost',
      port: port,
      path: path,
      method: 'GET',
      timeout: 5000
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        resolve({
          name,
          port,
          path,
          status: res.statusCode === 200 ? '✅ 成功' : `❌ 错误 (${res.statusCode})`,
          statusCode: res.statusCode
        });
      });
    });

    req.on('error', (err) => {
      resolve({
        name,
        port,
        path,
        status: '❌ 连接失败',
        error: err.message
      });
    });

    req.on('timeout', () => {
      resolve({
        name,
        port,
        path,
        status: '⏰ 超时'
      });
    });

    req.end();
  });
}

async function testApplication() {
  console.log('\n正在测试应用访问...\n');
  
  const tests = [
    { name: 'React客户端首页', port: 3000, path: '/' },
    { name: 'API Gateway健康检查', port: 3001, path: '/health' },
    { name: 'Core Service健康检查', port: 3002, path: '/health' },
    { name: 'Core Service状态', port: 3002, path: '/api/status' }
  ];
  
  for (const test of tests) {
    const result = await testEndpoint(test.name, test.port, test.path);
    console.log(`[${result.name}] ${result.status}`);
    console.log(`   URL: http://localhost:${result.port}${result.path}`);
    if (result.error) {
      console.log(`   错误: ${result.error}`);
    }
    console.log('');
  }
  
  console.log('═══════════════════════════════════════════');
  console.log('🎉 应用测试完成！');
  console.log('');
  console.log('如果React客户端显示✅成功，您可以在浏览器中访问：');
  console.log('👉 http://localhost:3000');
  console.log('');
}

testApplication().catch(console.error); 