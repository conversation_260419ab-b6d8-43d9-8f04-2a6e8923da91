import '@testing-library/jest-dom';
import { server } from './mocks/server';

// Jest全局函数类型声明
declare global {
  const beforeAll: (fn: () => void) => void;
  const afterEach: (fn: () => void) => void;
  const afterAll: (fn: () => void) => void;
}

// 测试运行前设置，启动 MSW 服务器
beforeAll(() => server.listen());

// 每个测试后重置任何请求处理程序以保持独立性
afterEach(() => server.resetHandlers());

// 测试完成后关闭服务器
afterAll(() => server.close()); 