// API请求工具

// API配置
// const BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';

// 请求方法
// const request = async (endpoint, method = 'GET', data = null, headers = {}) => {
//   const url = `${BASE_URL}${endpoint}`;
//   
//   const options = {
//     method,
//     headers: {
//       'Content-Type': 'application/json',
//       ...headers
//     },
//     credentials: 'include' // 包含cookies
//   };
//   
//   if (data) {
//     options.body = JSON.stringify(data);
//   }
//   
//   try {
//     const response = await fetch(url, options);
//     
//     // 检查响应状态
//     if (!response.ok) {
//       const errorData = await response.json().catch(() => ({}));
//       throw new Error(errorData.message || `HTTP error! Status: ${response.status}`);
//     }
//     
//     // 检查内容类型并解析响应
//     const contentType = response.headers.get('content-type');
//     if (contentType && contentType.includes('application/json')) {
//       return await response.json();
//     }
//     
//     return await response.text();
//   } catch (error) {
//     console.error('API请求错误:', error);
//     throw error;
//   }
// };

// API方法
// const api = {
//   // 内容生成器
//   getContentGeneratorSettings: () => request('/content/settings'),
//   updateContentGeneratorSettings: (data) => request('/content/settings', 'PUT', data),
//   generateContent: (params) => request('/content/generate', 'POST', params),
//   
//   // 任务管理
//   getTasks: () => request('/tasks'),
//   getTask: (id) => request(`/tasks/${id}`),
//   createTask: (data) => request('/tasks', 'POST', data),
//   updateTask: (id, data) => request(`/tasks/${id}`, 'PUT', data),
//   deleteTask: (id) => request(`/tasks/${id}`, 'DELETE'),
//   
//   // 用户认证相关
//   login: (credentials) => request('/auth/login', 'POST', credentials),
//   register: (userData) => request('/auth/register', 'POST', userData),
//   logout: () => request('/auth/logout', 'POST'),
//   getCurrentUser: () => request('/auth/me'),
//   
//   // 数据分析
//   getAnalyticsData: (params) => request('/analytics/data', 'GET', null, { params })
// };

// export default api;

export { default as api } from './axiosInstance';
export { default as chatApi } from './chatApi';
