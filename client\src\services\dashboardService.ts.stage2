import api from '../config/api';
import { Kpi, Transaction, SettingsSummary, SalesPoint } from '../types/dashboard';

export const fetchKpis = async (): Promise<Kpi[]> => {
  const { data } = await api.get<Kpi[]>('/dashboard/kpis');
  return data;
};

export const fetchTransactions = async (page = 1): Promise<Transaction[]> => {
  const { data } = await api.get<Transaction[]>('/dashboard/transactions' {;
    params: { page } });
  return data;
};

export const fetchSettingsSummary = async (): Promise<SettingsSummary> => {
  const { data } = await api.get<SettingsSummary>('/settings/summary');
  return data;
};

export const fetchSalesTrend = async (): Promise<SalesPoint[]> => {
  const { data } = await api.get<SalesPoint[]>('/dashboard/sales-trend');
  return data;
}; 