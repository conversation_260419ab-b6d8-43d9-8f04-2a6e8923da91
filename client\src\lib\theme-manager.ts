/**
 * 主题管理器 - 处理主题切换和状态管理
 */

type ThemeMode = 'light' | 'dark' | 'system';
type ThemeListener = (theme: ThemeMode) => void;

class ThemeManager {
  private currentTheme: ThemeMode = 'light';
  private listeners: ThemeListener[] = [];
  private mediaQuery: MediaQueryList | null = null;

  constructor() {
    this.init();
  }

  private init() {
    if (typeof window === 'undefined') return;

    // 获取系统主题偏好
    this.mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    
    // 从localStorage获取保存的主题
    const savedTheme = localStorage.getItem('theme') as ThemeMode;
    
    if (savedTheme) {
      this.currentTheme = savedTheme;
    } else {
      this.currentTheme = 'system';
    }

    // 应用主题
    this.applyTheme();

    // 监听系统主题变化
    this.mediaQuery.addEventListener('change', this.handleSystemThemeChange);
  }

  private handleSystemThemeChange = () => {
    if (this.currentTheme === 'system') {
      this.applyTheme();
    }
  };

  private applyTheme() {
    if (typeof window === 'undefined') return;

    const root = document.documentElement;
    
    let effectiveTheme: 'light' | 'dark';
    
    if (this.currentTheme === 'system') {
      effectiveTheme = this.mediaQuery?.matches ? 'dark' : 'light';
    } else {
      effectiveTheme = this.currentTheme as 'light' | 'dark';
    }

    // 更新DOM类
    root.classList.remove('light', 'dark');
    root.classList.add(effectiveTheme);

    // 更新meta主题色
    const metaThemeColor = document.querySelector('meta[name="theme-color"]');
    if (metaThemeColor) {
      metaThemeColor.setAttribute(
        'content',
        effectiveTheme === 'dark' ? '#0f172a' : '#ffffff'
      );
    }

    // 通知监听器
    this.notifyListeners(effectiveTheme);
  }

  private notifyListeners(theme: 'light' | 'dark') {
    this.listeners.forEach(listener => listener(theme));
  }

  getTheme(): 'light' | 'dark' {
    if (this.currentTheme === 'system') {
      return this.mediaQuery?.matches ? 'dark' : 'light';
    }
    return this.currentTheme as 'light' | 'dark';
  }

  setTheme(theme: ThemeMode) {
    this.currentTheme = theme;
    
    if (theme === 'system') {
      localStorage.removeItem('theme');
    } else {
      localStorage.setItem('theme', theme);
    }
    
    this.applyTheme();
  }

  toggleTheme() {
    const currentEffective = this.getTheme();
    const newTheme = currentEffective === 'light' ? 'dark' : 'light';
    this.setTheme(newTheme);
  }

  subscribe(listener: ThemeListener): () => void {
    this.listeners.push(listener);
    
    // 返回取消订阅函数
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  destroy() {
    if (this.mediaQuery) {
      this.mediaQuery.removeEventListener('change', this.handleSystemThemeChange);
    }
    this.listeners = [];
  }
}

// 导出单例实例
export const themeManager = new ThemeManager();
export default themeManager; 