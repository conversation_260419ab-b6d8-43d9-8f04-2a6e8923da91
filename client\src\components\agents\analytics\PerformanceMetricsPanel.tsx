import React from "react";
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Clock, Activity, Shield, Brain, CheckCircle, TrendingUp, AlertTriangle } from "lucide-react";
import { cn } from "@/lib/utils";

interface PerformanceMetricsPanelProps {
  agentId?: string;
  timeRange: string;
  className?: string;
}

const PerformanceMetricsPanel: React.FC<PerformanceMetricsPanelProps> = ({
  agentId,
  timeRange,
  className
}) => {
  const performanceData = {
    responseTime: {
      average: 2.3,
      p50: 1.8,
      p95: 4.2,
      p99: 6.8,
    },
    throughput: {
      messagesPerSecond: 12.5,
      conversationsPerHour: 450,
      peakLoad: 78,
    },
    availability: {
      uptime: 99.8,
      downtime: 0.2,
      lastIncident: "2023-11-15",
    },
    aiMetrics: {
      accuracy: 89.2,
      confidence: 94.1,
      resolutionRate: 87.5,
    },
  };

  const getStatusBadge = (
    value: number,
    thresholds: { good: number; warning: number }
  ) => {
    if (value >= thresholds.good)
      return { color: "bg-green-100 text-green-800", label: "Excellent" };
    if (value >= thresholds.warning)
      return { color: "bg-yellow-100 text-yellow-800", label: "Good" };
    return { color: "bg-red-100 text-red-800", label: "Needs Attention" };
  };

  return (
    <div className={cn("space-y-6", className)}>
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2 mb-2">
              <Clock className="h-4 w-4 text-blue-600" />
              <span className="text-sm font-medium">Avg Response</span>
            </div>
            <div className="text-2xl font-bold">{performanceData.responseTime.average}s</div>
            <Badge
              className={
                getStatusBadge(performanceData.responseTime.average, { good: 3, warning: 5 }).color
              }
              variant="secondary"
            >
              {performanceData.responseTime.average <= 3 ? "Fast" : "Slow"}
            </Badge>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2 mb-2">
              <Activity className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium">Throughput</span>
            </div>
            <div className="text-2xl font-bold">{performanceData.throughput.messagesPerSecond}/s</div>
            <Badge className="bg-green-100 text-green-800" variant="secondary">
              High
            </Badge>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2 mb-2">
              <Shield className="h-4 w-4 text-purple-600" />
              <span className="text-sm font-medium">Uptime</span>
            </div>
            <div className="text-2xl font-bold">{performanceData.availability.uptime}%</div>
            <Badge className="bg-green-100 text-green-800" variant="secondary">
              Excellent
            </Badge>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2 mb-2">
              <Brain className="h-4 w-4 text-orange-600" />
              <span className="text-sm font-medium">AI Accuracy</span>
            </div>
            <div className="text-2xl font-bold">{performanceData.aiMetrics.accuracy}%</div>
            <Badge className="bg-green-100 text-green-800" variant="secondary">
              Good
            </Badge>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5 text-primary" />
            Response Time Analysis
          </CardTitle>
          <CardDescription>
            Detailed breakdown of response time percentiles
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Average (P50)</span>
                  <span className="font-medium">{performanceData.responseTime.p50}s</span>
                </div>
                <Progress value={(performanceData.responseTime.p50 / 10) * 100} className="h-2" />
              </div>
              
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>95th Percentile</span>
                  <span className="font-medium">{performanceData.responseTime.p95}s</span>
                </div>
                <Progress value={(performanceData.responseTime.p95 / 10) * 100} className="h-2" />
              </div>
              
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>99th Percentile</span>
                  <span className="font-medium">{performanceData.responseTime.p99}s</span>
                </div>
                <Progress value={(performanceData.responseTime.p99 / 10) * 100} className="h-2" />
              </div>
            </div>
            
            <div className="space-y-4">
              <div className="p-4 bg-green-50 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="font-medium text-green-900">Performance Status</span>
                </div>
                <p className="text-sm text-green-700">
                  Response times are within acceptable ranges. 95% of requests complete under 5 seconds.
                </p>
              </div>
              
              <div className="p-4 bg-blue-50 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <TrendingUp className="h-4 w-4 text-blue-600" />
                  <span className="font-medium text-blue-900">Optimization Tip</span>
                </div>
                <p className="text-sm text-blue-700">
                  Consider caching frequently requested data to improve P99 response times.
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5 text-primary" />
            Throughput & Load
          </CardTitle>
          <CardDescription>
            System capacity and load distribution
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">
                {performanceData.throughput.messagesPerSecond}
              </div>
              <div className="text-sm text-blue-700">Messages/Second</div>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {performanceData.throughput.conversationsPerHour.toLocaleString()}
              </div>
              <div className="text-sm text-green-700">Conversations/Hour</div>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">
                {performanceData.throughput.peakLoad}%
              </div>
              <div className="text-sm text-purple-700">Peak Load</div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5 text-primary" />
            AI Performance Metrics
          </CardTitle>
          <CardDescription>
            AI model accuracy and effectiveness
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <h4 className="font-medium">Accuracy Rate</h4>
                  <p className="text-sm text-muted-foreground">Correct responses vs total</p>
                </div>
                <div className="text-right">
                  <div className="text-lg font-bold text-green-600">
                    {performanceData.aiMetrics.accuracy}%
                  </div>
                  <Badge className="bg-green-100 text-green-800" variant="secondary">
                    Good
                  </Badge>
                </div>
              </div>
              
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <h4 className="font-medium">Confidence Score</h4>
                  <p className="text-sm text-muted-foreground">AI response confidence</p>
                </div>
                <div className="text-right">
                  <div className="text-lg font-bold text-green-600">
                    {performanceData.aiMetrics.confidence}%
                  </div>
                  <Badge className="bg-green-100 text-green-800" variant="secondary">
                    High
                  </Badge>
                </div>
              </div>
              
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <h4 className="font-medium">Resolution Rate</h4>
                  <p className="text-sm text-muted-foreground">Successfully resolved queries</p>
                </div>
                <div className="text-right">
                  <div className="text-lg font-bold text-green-600">
                    {performanceData.aiMetrics.resolutionRate}%
                  </div>
                  <Badge className="bg-green-100 text-green-800" variant="secondary">
                    Good
                  </Badge>
                </div>
              </div>
            </div>
            
            <div className="space-y-4">
              <div className="p-4 bg-yellow-50 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <AlertTriangle className="h-4 w-4 text-yellow-600" />
                  <span className="font-medium text-yellow-900">Improvement Area</span>
                </div>
                <p className="text-sm text-yellow-700">
                  AI accuracy could be improved by adding more training data for edge cases.
                </p>
              </div>
              
              <div className="p-4 bg-blue-50 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <Brain className="h-4 w-4 text-blue-600" />
                  <span className="font-medium text-blue-900">Model Status</span>
                </div>
                <p className="text-sm text-blue-700">
                  Model is performing well within expected parameters. Regular retraining scheduled monthly.
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PerformanceMetricsPanel;
