/*! For license information please see 42.0c1389e8.iframe.bundle.js.LICENSE.txt */
"use strict";(self.webpackChunkclient=self.webpackChunkclient||[]).push([[42],{"./node_modules/@fortawesome/free-solid-svg-icons/index.mjs":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.d(__webpack_exports__,{BEE:()=>faCalendarAlt,GQu:()=>faBrain,Int:()=>faHistory,MjD:()=>faSearch,SGM:()=>faCheckCircle,VFr:()=>faUserCircle,VNe:()=>faSync,a2R:()=>faVial,dB:()=>faCog,hhY:()=>faListAlt,iW_:()=>faInfoCircle,mRM:()=>faFilter,wRm:()=>faQuestionCircle,xiI:()=>faTachometerAlt,z1G:()=>faSpinner,zpE:()=>faExclamationTriangle});const faCalendarDays={prefix:"fas",iconName:"calendar-days",icon:[448,512,["calendar-alt"],"f073","M128 0c17.7 0 32 14.3 32 32l0 32 128 0 0-32c0-17.7 14.3-32 32-32s32 14.3 32 32l0 32 48 0c26.5 0 48 21.5 48 48l0 48L0 160l0-48C0 85.5 21.5 64 48 64l48 0 0-32c0-17.7 14.3-32 32-32zM0 192l448 0 0 272c0 26.5-21.5 48-48 48L48 512c-26.5 0-48-21.5-48-48L0 192zm64 80l0 32c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-32c0-8.8-7.2-16-16-16l-32 0c-8.8 0-16 7.2-16 16zm128 0l0 32c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-32c0-8.8-7.2-16-16-16l-32 0c-8.8 0-16 7.2-16 16zm144-16c-8.8 0-16 7.2-16 16l0 32c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-32c0-8.8-7.2-16-16-16l-32 0zM64 400l0 32c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-32c0-8.8-7.2-16-16-16l-32 0c-8.8 0-16 7.2-16 16zm144-16c-8.8 0-16 7.2-16 16l0 32c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-32c0-8.8-7.2-16-16-16l-32 0zm112 16l0 32c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-32c0-8.8-7.2-16-16-16l-32 0c-8.8 0-16 7.2-16 16z"]},faCalendarAlt=faCalendarDays,faVial={prefix:"fas",iconName:"vial",icon:[512,512,[129514],"f492","M342.6 9.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l9.4 9.4L28.1 342.6C10.1 360.6 0 385 0 410.5L0 416c0 53 43 96 96 96l5.5 0c25.5 0 49.9-10.1 67.9-28.1L448 205.3l9.4 9.4c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3l-32-32-96-96-32-32zM205.3 256L352 109.3 402.7 160l-96 96-101.5 0z"]},faCircleCheck={prefix:"fas",iconName:"circle-check",icon:[512,512,[61533,"check-circle"],"f058","M256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512zM369 209L241 337c-9.4 9.4-24.6 9.4-33.9 0l-64-64c-9.4-9.4-9.4-24.6 0-33.9s24.6-9.4 33.9 0l47 47L335 175c9.4-9.4 24.6-9.4 33.9 0s9.4 24.6 0 33.9z"]},faCheckCircle=faCircleCheck,faArrowsRotate={prefix:"fas",iconName:"arrows-rotate",icon:[512,512,[128472,"refresh","sync"],"f021","M105.1 202.6c7.7-21.8 20.2-42.3 37.8-59.8c62.5-62.5 163.8-62.5 226.3 0L386.3 160 352 160c-17.7 0-32 14.3-32 32s14.3 32 32 32l111.5 0c0 0 0 0 0 0l.4 0c17.7 0 32-14.3 32-32l0-112c0-17.7-14.3-32-32-32s-32 14.3-32 32l0 35.2L414.4 97.6c-87.5-87.5-229.3-87.5-316.8 0C73.2 122 55.6 150.7 44.8 181.4c-5.9 16.7 2.9 34.9 19.5 40.8s34.9-2.9 40.8-19.5zM39 289.3c-5 1.5-9.8 4.2-13.7 8.2c-4 4-6.7 8.8-8.1 14c-.3 1.2-.6 2.5-.8 3.8c-.3 1.7-.4 3.4-.4 5.1L16 432c0 17.7 14.3 32 32 32s32-14.3 32-32l0-35.1 17.6 17.5c0 0 0 0 0 0c87.5 87.4 229.3 87.4 316.7 0c24.4-24.4 42.1-53.1 52.9-83.8c5.9-16.7-2.9-34.9-19.5-40.8s-34.9 2.9-40.8 19.5c-7.7 21.8-20.2 42.3-37.8 59.8c-62.5 62.5-163.8 62.5-226.3 0l-.1-.1L125.6 352l34.4 0c17.7 0 32-14.3 32-32s-14.3-32-32-32L48.4 288c-1.6 0-3.2 .1-4.8 .3s-3.1 .5-4.6 1z"]},faSync=faArrowsRotate,faFilter={prefix:"fas",iconName:"filter",icon:[512,512,[],"f0b0","M3.9 54.9C10.5 40.9 24.5 32 40 32l432 0c15.5 0 29.5 8.9 36.1 22.9s4.6 30.5-5.2 42.5L320 320.9 320 448c0 12.1-6.8 23.2-17.7 28.6s-23.8 4.3-33.5-3l-64-48c-8.1-6-12.8-15.5-12.8-25.6l0-79.1L9 97.3C-.7 85.4-2.8 68.8 3.9 54.9z"]},faCircleQuestion={prefix:"fas",iconName:"circle-question",icon:[512,512,[62108,"question-circle"],"f059","M256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512zM169.8 165.3c7.9-22.3 29.1-37.3 52.8-37.3l58.3 0c34.9 0 63.1 28.3 63.1 63.1c0 22.6-12.1 43.5-31.7 54.8L280 264.4c-.2 13-10.9 23.6-24 23.6c-13.3 0-24-10.7-24-24l0-13.5c0-8.6 4.6-16.5 12.1-20.8l44.3-25.4c4.7-2.7 7.6-7.7 7.6-13.1c0-8.4-6.8-15.1-15.1-15.1l-58.3 0c-3.4 0-6.4 2.1-7.5 5.3l-.4 1.2c-4.4 12.5-18.2 19-30.6 14.6s-19-18.2-14.6-30.6l.4-1.2zM224 352a32 32 0 1 1 64 0 32 32 0 1 1 -64 0z"]},faQuestionCircle=faCircleQuestion,faCircleInfo={prefix:"fas",iconName:"circle-info",icon:[512,512,["info-circle"],"f05a","M256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512zM216 336l24 0 0-64-24 0c-13.3 0-24-10.7-24-24s10.7-24 24-24l48 0c13.3 0 24 10.7 24 24l0 88 8 0c13.3 0 24 10.7 24 24s-10.7 24-24 24l-80 0c-13.3 0-24-10.7-24-24s10.7-24 24-24zm40-208a32 32 0 1 1 0 64 32 32 0 1 1 0-64z"]},faInfoCircle=faCircleInfo,faRectangleList={prefix:"fas",iconName:"rectangle-list",icon:[576,512,["list-alt"],"f022","M0 96C0 60.7 28.7 32 64 32l448 0c35.3 0 64 28.7 64 64l0 320c0 35.3-28.7 64-64 64L64 480c-35.3 0-64-28.7-64-64L0 96zM128 288a32 32 0 1 0 0-64 32 32 0 1 0 0 64zm32-128a32 32 0 1 0 -64 0 32 32 0 1 0 64 0zM128 384a32 32 0 1 0 0-64 32 32 0 1 0 0 64zm96-248c-13.3 0-24 10.7-24 24s10.7 24 24 24l224 0c13.3 0 24-10.7 24-24s-10.7-24-24-24l-224 0zm0 96c-13.3 0-24 10.7-24 24s10.7 24 24 24l224 0c13.3 0 24-10.7 24-24s-10.7-24-24-24l-224 0zm0 96c-13.3 0-24 10.7-24 24s10.7 24 24 24l224 0c13.3 0 24-10.7 24-24s-10.7-24-24-24l-224 0z"]},faListAlt=faRectangleList,faGear={prefix:"fas",iconName:"gear",icon:[512,512,[9881,"cog"],"f013","M495.9 166.6c3.2 8.7 .5 18.4-6.4 24.6l-43.3 39.4c1.1 8.3 1.7 16.8 1.7 25.4s-.6 17.1-1.7 25.4l43.3 39.4c6.9 6.2 9.6 15.9 6.4 24.6c-4.4 11.9-9.7 23.3-15.8 34.3l-4.7 8.1c-6.6 11-14 21.4-22.1 31.2c-5.9 7.2-15.7 9.6-24.5 6.8l-55.7-17.7c-13.4 10.3-28.2 18.9-44 25.4l-12.5 57.1c-2 9.1-9 16.3-18.2 17.8c-13.8 2.3-28 3.5-42.5 3.5s-28.7-1.2-42.5-3.5c-9.2-1.5-16.2-8.7-18.2-17.8l-12.5-57.1c-15.8-6.5-30.6-15.1-44-25.4L83.1 425.9c-8.8 2.8-18.6 .3-24.5-6.8c-8.1-9.8-15.5-20.2-22.1-31.2l-4.7-8.1c-6.1-11-11.4-22.4-15.8-34.3c-3.2-8.7-.5-18.4 6.4-24.6l43.3-39.4C64.6 273.1 64 264.6 64 256s.6-17.1 1.7-25.4L22.4 191.2c-6.9-6.2-9.6-15.9-6.4-24.6c4.4-11.9 9.7-23.3 15.8-34.3l4.7-8.1c6.6-11 14-21.4 22.1-31.2c5.9-7.2 15.7-9.6 24.5-6.8l55.7 17.7c13.4-10.3 28.2-18.9 44-25.4l12.5-57.1c2-9.1 9-16.3 18.2-17.8C227.3 1.2 241.5 0 256 0s28.7 1.2 42.5 3.5c9.2 1.5 16.2 8.7 18.2 17.8l12.5 57.1c15.8 6.5 30.6 15.1 44 25.4l55.7-17.7c8.8-2.8 18.6-.3 24.5 6.8c8.1 9.8 15.5 20.2 22.1 31.2l4.7 8.1c6.1 11 11.4 22.4 15.8 34.3zM256 336a80 80 0 1 0 0-160 80 80 0 1 0 0 160z"]},faCog=faGear,faGaugeHigh={prefix:"fas",iconName:"gauge-high",icon:[512,512,[62461,"tachometer-alt","tachometer-alt-fast"],"f625","M0 256a256 256 0 1 1 512 0A256 256 0 1 1 0 256zM288 96a32 32 0 1 0 -64 0 32 32 0 1 0 64 0zM256 416c35.3 0 64-28.7 64-64c0-17.4-6.9-33.1-18.1-44.6L366 161.7c5.3-12.1-.2-26.3-12.3-31.6s-26.3 .2-31.6 12.3L257.9 288c-.6 0-1.3 0-1.9 0c-35.3 0-64 28.7-64 64s28.7 64 64 64zM176 144a32 32 0 1 0 -64 0 32 32 0 1 0 64 0zM96 288a32 32 0 1 0 0-64 32 32 0 1 0 0 64zm352-32a32 32 0 1 0 -64 0 32 32 0 1 0 64 0z"]},faTachometerAlt=faGaugeHigh,faMagnifyingGlass={prefix:"fas",iconName:"magnifying-glass",icon:[512,512,[128269,"search"],"f002","M416 208c0 45.9-14.9 88.3-40 122.7L502.6 457.4c12.5 12.5 12.5 32.8 0 45.3s-32.8 12.5-45.3 0L330.7 376c-34.4 25.2-76.8 40-122.7 40C93.1 416 0 322.9 0 208S93.1 0 208 0S416 93.1 416 208zM208 352a144 144 0 1 0 0-288 144 144 0 1 0 0 288z"]},faSearch=faMagnifyingGlass,faCircleUser={prefix:"fas",iconName:"circle-user",icon:[512,512,[62142,"user-circle"],"f2bd","M399 384.2C376.9 345.8 335.4 320 288 320l-64 0c-47.4 0-88.9 25.8-111 64.2c35.2 39.2 86.2 63.8 143 63.8s107.8-24.7 143-63.8zM0 256a256 256 0 1 1 512 0A256 256 0 1 1 0 256zm256 16a72 72 0 1 0 0-144 72 72 0 1 0 0 144z"]},faUserCircle=faCircleUser,faSpinner={prefix:"fas",iconName:"spinner",icon:[512,512,[],"f110","M304 48a48 48 0 1 0 -96 0 48 48 0 1 0 96 0zm0 416a48 48 0 1 0 -96 0 48 48 0 1 0 96 0zM48 304a48 48 0 1 0 0-96 48 48 0 1 0 0 96zm464-48a48 48 0 1 0 -96 0 48 48 0 1 0 96 0zM142.9 437A48 48 0 1 0 75 369.1 48 48 0 1 0 142.9 437zm0-294.2A48 48 0 1 0 75 75a48 48 0 1 0 67.9 67.9zM369.1 437A48 48 0 1 0 437 369.1 48 48 0 1 0 369.1 437z"]},faClockRotateLeft={prefix:"fas",iconName:"clock-rotate-left",icon:[512,512,["history"],"f1da","M75 75L41 41C25.9 25.9 0 36.6 0 57.9L0 168c0 13.3 10.7 24 24 24l110.1 0c21.4 0 32.1-25.9 17-41l-30.8-30.8C155 85.5 203 64 256 64c106 0 192 86 192 192s-86 192-192 192c-40.8 0-78.6-12.7-109.7-34.4c-14.5-10.1-34.4-6.6-44.6 7.9s-6.6 34.4 7.9 44.6C151.2 495 201.7 512 256 512c141.4 0 256-114.6 256-256S397.4 0 256 0C185.3 0 121.3 28.7 75 75zm181 53c-13.3 0-24 10.7-24 24l0 104c0 6.4 2.5 12.5 7 17l72 72c9.4 9.4 24.6 9.4 33.9 0s9.4-24.6 0-33.9l-65-65 0-94.1c0-13.3-10.7-24-24-24z"]},faHistory=faClockRotateLeft,faTriangleExclamation={prefix:"fas",iconName:"triangle-exclamation",icon:[512,512,[9888,"exclamation-triangle","warning"],"f071","M256 32c14.2 0 27.3 7.5 34.5 19.8l216 368c7.3 12.4 7.3 27.7 .2 40.1S486.3 480 472 480L40 480c-14.3 0-27.6-7.7-34.7-20.1s-7-27.8 .2-40.1l216-368C228.7 39.5 241.8 32 256 32zm0 128c-13.3 0-24 10.7-24 24l0 112c0 13.3 10.7 24 24 24s24-10.7 24-24l0-112c0-13.3-10.7-24-24-24zm32 224a32 32 0 1 0 -64 0 32 32 0 1 0 64 0z"]},faExclamationTriangle=faTriangleExclamation,faBrain={prefix:"fas",iconName:"brain",icon:[512,512,[129504],"f5dc","M184 0c30.9 0 56 25.1 56 56l0 400c0 30.9-25.1 56-56 56c-28.9 0-52.7-21.9-55.7-50.1c-5.2 1.4-10.7 2.1-16.3 2.1c-35.3 0-64-28.7-64-64c0-7.4 1.3-14.6 3.6-21.2C21.4 367.4 0 338.2 0 304c0-31.9 18.7-59.5 45.8-72.3C37.1 220.8 32 207 32 192c0-30.7 21.6-56.3 50.4-62.6C80.8 123.9 80 118 80 112c0-29.9 20.6-55.1 48.3-62.1C131.3 21.9 155.1 0 184 0zM328 0c28.9 0 52.6 21.9 55.7 49.9c27.8 7 48.3 32.1 48.3 62.1c0 6-.8 11.9-2.4 17.4c28.8 6.2 50.4 31.9 50.4 62.6c0 15-5.1 28.8-13.8 39.7C493.3 244.5 512 272.1 512 304c0 34.2-21.4 63.4-51.6 74.8c2.3 6.6 3.6 13.8 3.6 21.2c0 35.3-28.7 64-64 64c-5.6 0-11.1-.7-16.3-2.1c-3 28.2-26.8 50.1-55.7 50.1c-30.9 0-56-25.1-56-56l0-400c0-30.9 25.1-56 56-56z"]}},"./node_modules/@fortawesome/react-fontawesome/index.es.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{function _defineProperty(e,r,t){return(r=function _toPropertyKey(t){var i=function _toPrimitive(t,r){if("object"!=typeof t||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var i=e.call(t,r||"default");if("object"!=typeof i)return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(t)}(t,"string");return"symbol"==typeof i?i:i+""}(r))in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e}function ownKeys(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);r&&(o=o.filter((function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable}))),t.push.apply(t,o)}return t}function _objectSpread2(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?ownKeys(Object(t),!0).forEach((function(r){_defineProperty(e,r,t[r])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):ownKeys(Object(t)).forEach((function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))}))}return e}__webpack_require__.d(__webpack_exports__,{g:()=>FontAwesomeIcon});const noop=()=>{};let _WINDOW={},_DOCUMENT={},_MUTATION_OBSERVER=null,_PERFORMANCE={mark:noop,measure:noop};try{"undefined"!=typeof window&&(_WINDOW=window),"undefined"!=typeof document&&(_DOCUMENT=document),"undefined"!=typeof MutationObserver&&(_MUTATION_OBSERVER=MutationObserver),"undefined"!=typeof performance&&(_PERFORMANCE=performance)}catch(e){}const{userAgent=""}=_WINDOW.navigator||{},WINDOW=_WINDOW,DOCUMENT=_DOCUMENT,MUTATION_OBSERVER=_MUTATION_OBSERVER,PERFORMANCE=_PERFORMANCE,IS_DOM=(WINDOW.document,!!DOCUMENT.documentElement&&!!DOCUMENT.head&&"function"==typeof DOCUMENT.addEventListener&&"function"==typeof DOCUMENT.createElement),IS_IE=~userAgent.indexOf("MSIE")||~userAgent.indexOf("Trident/");var S={classic:{fa:"solid",fas:"solid","fa-solid":"solid",far:"regular","fa-regular":"regular",fal:"light","fa-light":"light",fat:"thin","fa-thin":"thin",fab:"brands","fa-brands":"brands"},duotone:{fa:"solid",fad:"solid","fa-solid":"solid","fa-duotone":"solid",fadr:"regular","fa-regular":"regular",fadl:"light","fa-light":"light",fadt:"thin","fa-thin":"thin"},sharp:{fa:"solid",fass:"solid","fa-solid":"solid",fasr:"regular","fa-regular":"regular",fasl:"light","fa-light":"light",fast:"thin","fa-thin":"thin"},"sharp-duotone":{fa:"solid",fasds:"solid","fa-solid":"solid",fasdr:"regular","fa-regular":"regular",fasdl:"light","fa-light":"light",fasdt:"thin","fa-thin":"thin"}},P=["fa-classic","fa-duotone","fa-sharp","fa-sharp-duotone"],s="classic",t="duotone",L=[s,t,"sharp","sharp-duotone"],pt=new Map([["classic",{defaultShortPrefixId:"fas",defaultStyleId:"solid",styleIds:["solid","regular","light","thin","brands"],futureStyleIds:[],defaultFontWeight:900}],["sharp",{defaultShortPrefixId:"fass",defaultStyleId:"solid",styleIds:["solid","regular","light","thin"],futureStyleIds:[],defaultFontWeight:900}],["duotone",{defaultShortPrefixId:"fad",defaultStyleId:"solid",styleIds:["solid","regular","light","thin"],futureStyleIds:[],defaultFontWeight:900}],["sharp-duotone",{defaultShortPrefixId:"fasds",defaultStyleId:"solid",styleIds:["solid","regular","light","thin"],futureStyleIds:[],defaultFontWeight:900}]]),Ft=["fak","fa-kit","fakd","fa-kit-duotone"],St_kit={fak:"kit","fa-kit":"kit"},St_kit_duotone={fakd:"kit-duotone","fa-kit-duotone":"kit-duotone"},Lt=["fak","fakd"],Et_kit={kit:"fak"},Et_kit_duotone={"kit-duotone":"fakd"},t$1={GROUP:"duotone-group",SWAP_OPACITY:"swap-opacity",PRIMARY:"primary",SECONDARY:"secondary"},bt$1=["fak","fa-kit","fakd","fa-kit-duotone"],ga={classic:{fab:"fa-brands",fad:"fa-duotone",fal:"fa-light",far:"fa-regular",fas:"fa-solid",fat:"fa-thin"},duotone:{fadr:"fa-regular",fadl:"fa-light",fadt:"fa-thin"},sharp:{fass:"fa-solid",fasr:"fa-regular",fasl:"fa-light",fast:"fa-thin"},"sharp-duotone":{fasds:"fa-solid",fasdr:"fa-regular",fasdl:"fa-light",fasdt:"fa-thin"}},Ia=["fa","fas","far","fal","fat","fad","fadr","fadl","fadt","fab","fass","fasr","fasl","fast","fasds","fasdr","fasdl","fasdt","fa-classic","fa-duotone","fa-sharp","fa-sharp-duotone","fa-solid","fa-regular","fa-light","fa-thin","fa-duotone","fa-brands"],c$1=[1,2,3,4,5,6,7,8,9,10],F$1=c$1.concat([11,12,13,14,15,16,17,18,19,20]),ma=[...Object.keys({classic:["fas","far","fal","fat","fad"],duotone:["fadr","fadl","fadt"],sharp:["fass","fasr","fasl","fast"],"sharp-duotone":["fasds","fasdr","fasdl","fasdt"]}),"solid","regular","light","thin","duotone","brands","2xs","xs","sm","lg","xl","2xl","beat","border","fade","beat-fade","bounce","flip-both","flip-horizontal","flip-vertical","flip","fw","inverse","layers-counter","layers-text","layers","li","pull-left","pull-right","pulse","rotate-180","rotate-270","rotate-90","rotate-by","shake","spin-pulse","spin-reverse","spin","stack-1x","stack-2x","stack","ul",t$1.GROUP,t$1.SWAP_OPACITY,t$1.PRIMARY,t$1.SECONDARY].concat(c$1.map((a=>"".concat(a,"x")))).concat(F$1.map((a=>"w-".concat(a))));const UNITS_IN_GRID=16,TAGNAMES_TO_SKIP_FOR_PSEUDOELEMENTS=["HTML","HEAD","STYLE","SCRIPT"],PRODUCTION=(()=>{try{return!0}catch(e$$1){return!1}})();function familyProxy(obj){return new Proxy(obj,{get:(target,prop)=>prop in target?target[prop]:target[s]})}const _PREFIX_TO_STYLE=_objectSpread2({},S);_PREFIX_TO_STYLE[s]=_objectSpread2(_objectSpread2(_objectSpread2(_objectSpread2({},{"fa-duotone":"duotone"}),S[s]),St_kit),St_kit_duotone);const PREFIX_TO_STYLE=familyProxy(_PREFIX_TO_STYLE),_STYLE_TO_PREFIX=_objectSpread2({},{classic:{solid:"fas",regular:"far",light:"fal",thin:"fat",brands:"fab"},duotone:{solid:"fad",regular:"fadr",light:"fadl",thin:"fadt"},sharp:{solid:"fass",regular:"fasr",light:"fasl",thin:"fast"},"sharp-duotone":{solid:"fasds",regular:"fasdr",light:"fasdl",thin:"fasdt"}});_STYLE_TO_PREFIX[s]=_objectSpread2(_objectSpread2(_objectSpread2(_objectSpread2({},{duotone:"fad"}),_STYLE_TO_PREFIX[s]),Et_kit),Et_kit_duotone);const STYLE_TO_PREFIX=familyProxy(_STYLE_TO_PREFIX),_PREFIX_TO_LONG_STYLE=_objectSpread2({},ga);_PREFIX_TO_LONG_STYLE[s]=_objectSpread2(_objectSpread2({},_PREFIX_TO_LONG_STYLE[s]),{fak:"fa-kit"});const PREFIX_TO_LONG_STYLE=familyProxy(_PREFIX_TO_LONG_STYLE),_LONG_STYLE_TO_PREFIX=_objectSpread2({},{classic:{"fa-brands":"fab","fa-duotone":"fad","fa-light":"fal","fa-regular":"far","fa-solid":"fas","fa-thin":"fat"},duotone:{"fa-regular":"fadr","fa-light":"fadl","fa-thin":"fadt"},sharp:{"fa-solid":"fass","fa-regular":"fasr","fa-light":"fasl","fa-thin":"fast"},"sharp-duotone":{"fa-solid":"fasds","fa-regular":"fasdr","fa-light":"fasdl","fa-thin":"fasdt"}});_LONG_STYLE_TO_PREFIX[s]=_objectSpread2(_objectSpread2({},_LONG_STYLE_TO_PREFIX[s]),{"fa-kit":"fak"});familyProxy(_LONG_STYLE_TO_PREFIX);const ICON_SELECTION_SYNTAX_PATTERN=/fa(s|r|l|t|d|dr|dl|dt|b|k|kd|ss|sr|sl|st|sds|sdr|sdl|sdt)?[\-\ ]/,FONT_FAMILY_PATTERN=/Font ?Awesome ?([56 ]*)(Solid|Regular|Light|Thin|Duotone|Brands|Free|Pro|Sharp Duotone|Sharp|Kit)?.*/i,ATTRIBUTES_WATCHED_FOR_MUTATION=(familyProxy(_objectSpread2({},{classic:{900:"fas",400:"far",normal:"far",300:"fal",100:"fat"},duotone:{900:"fad",400:"fadr",300:"fadl",100:"fadt"},sharp:{900:"fass",400:"fasr",300:"fasl",100:"fast"},"sharp-duotone":{900:"fasds",400:"fasdr",300:"fasdl",100:"fasdt"}})),["class","data-prefix","data-icon","data-fa-transform","data-fa-mask"]),DUOTONE_CLASSES={GROUP:"duotone-group",SWAP_OPACITY:"swap-opacity",PRIMARY:"primary",SECONDARY:"secondary"},RESERVED_CLASSES=["kit",...ma],initial=WINDOW.FontAwesomeConfig||{};if(DOCUMENT&&"function"==typeof DOCUMENT.querySelector){[["data-family-prefix","familyPrefix"],["data-css-prefix","cssPrefix"],["data-family-default","familyDefault"],["data-style-default","styleDefault"],["data-replacement-class","replacementClass"],["data-auto-replace-svg","autoReplaceSvg"],["data-auto-add-css","autoAddCss"],["data-auto-a11y","autoA11y"],["data-search-pseudo-elements","searchPseudoElements"],["data-observe-mutations","observeMutations"],["data-mutate-approach","mutateApproach"],["data-keep-original-source","keepOriginalSource"],["data-measure-performance","measurePerformance"],["data-show-missing-icons","showMissingIcons"]].forEach((_ref=>{let[attr,key]=_ref;const val=function coerce(val){return""===val||"false"!==val&&("true"===val||val)}(function getAttrConfig(attr){var element=DOCUMENT.querySelector("script["+attr+"]");if(element)return element.getAttribute(attr)}(attr));null!=val&&(initial[key]=val)}))}const _default={styleDefault:"solid",familyDefault:s,cssPrefix:"fa",replacementClass:"svg-inline--fa",autoReplaceSvg:!0,autoAddCss:!0,autoA11y:!0,searchPseudoElements:!1,observeMutations:!0,mutateApproach:"async",keepOriginalSource:!0,measurePerformance:!1,showMissingIcons:!0};initial.familyPrefix&&(initial.cssPrefix=initial.familyPrefix);const _config=_objectSpread2(_objectSpread2({},_default),initial);_config.autoReplaceSvg||(_config.observeMutations=!1);const config={};Object.keys(_default).forEach((key=>{Object.defineProperty(config,key,{enumerable:!0,set:function(val){_config[key]=val,_onChangeCb.forEach((cb=>cb(config)))},get:function(){return _config[key]}})})),Object.defineProperty(config,"familyPrefix",{enumerable:!0,set:function(val){_config.cssPrefix=val,_onChangeCb.forEach((cb=>cb(config)))},get:function(){return _config.cssPrefix}}),WINDOW.FontAwesomeConfig=config;const _onChangeCb=[];const d$2=UNITS_IN_GRID,meaninglessTransform={size:16,x:0,y:0,rotate:0,flipX:!1,flipY:!1};function nextUniqueId(){let size=12,id="";for(;size-- >0;)id+="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"[62*Math.random()|0];return id}function toArray(obj){const array=[];for(let i=(obj||[]).length>>>0;i--;)array[i]=obj[i];return array}function classArray(node){return node.classList?toArray(node.classList):(node.getAttribute("class")||"").split(" ").filter((i=>i))}function htmlEscape(str){return"".concat(str).replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&#39;").replace(/</g,"&lt;").replace(/>/g,"&gt;")}function joinStyles(styles){return Object.keys(styles||{}).reduce(((acc,styleName)=>acc+"".concat(styleName,": ").concat(styles[styleName].trim(),";")),"")}function transformIsMeaningful(transform){return transform.size!==meaninglessTransform.size||transform.x!==meaninglessTransform.x||transform.y!==meaninglessTransform.y||transform.rotate!==meaninglessTransform.rotate||transform.flipX||transform.flipY}function css(){const drc="svg-inline--fa",fp=config.cssPrefix,rc=config.replacementClass;let s=':root, :host {\n  --fa-font-solid: normal 900 1em/1 "Font Awesome 6 Free";\n  --fa-font-regular: normal 400 1em/1 "Font Awesome 6 Free";\n  --fa-font-light: normal 300 1em/1 "Font Awesome 6 Pro";\n  --fa-font-thin: normal 100 1em/1 "Font Awesome 6 Pro";\n  --fa-font-duotone: normal 900 1em/1 "Font Awesome 6 Duotone";\n  --fa-font-duotone-regular: normal 400 1em/1 "Font Awesome 6 Duotone";\n  --fa-font-duotone-light: normal 300 1em/1 "Font Awesome 6 Duotone";\n  --fa-font-duotone-thin: normal 100 1em/1 "Font Awesome 6 Duotone";\n  --fa-font-brands: normal 400 1em/1 "Font Awesome 6 Brands";\n  --fa-font-sharp-solid: normal 900 1em/1 "Font Awesome 6 Sharp";\n  --fa-font-sharp-regular: normal 400 1em/1 "Font Awesome 6 Sharp";\n  --fa-font-sharp-light: normal 300 1em/1 "Font Awesome 6 Sharp";\n  --fa-font-sharp-thin: normal 100 1em/1 "Font Awesome 6 Sharp";\n  --fa-font-sharp-duotone-solid: normal 900 1em/1 "Font Awesome 6 Sharp Duotone";\n  --fa-font-sharp-duotone-regular: normal 400 1em/1 "Font Awesome 6 Sharp Duotone";\n  --fa-font-sharp-duotone-light: normal 300 1em/1 "Font Awesome 6 Sharp Duotone";\n  --fa-font-sharp-duotone-thin: normal 100 1em/1 "Font Awesome 6 Sharp Duotone";\n}\n\nsvg:not(:root).svg-inline--fa, svg:not(:host).svg-inline--fa {\n  overflow: visible;\n  box-sizing: content-box;\n}\n\n.svg-inline--fa {\n  display: var(--fa-display, inline-block);\n  height: 1em;\n  overflow: visible;\n  vertical-align: -0.125em;\n}\n.svg-inline--fa.fa-2xs {\n  vertical-align: 0.1em;\n}\n.svg-inline--fa.fa-xs {\n  vertical-align: 0em;\n}\n.svg-inline--fa.fa-sm {\n  vertical-align: -0.0714285705em;\n}\n.svg-inline--fa.fa-lg {\n  vertical-align: -0.2em;\n}\n.svg-inline--fa.fa-xl {\n  vertical-align: -0.25em;\n}\n.svg-inline--fa.fa-2xl {\n  vertical-align: -0.3125em;\n}\n.svg-inline--fa.fa-pull-left {\n  margin-right: var(--fa-pull-margin, 0.3em);\n  width: auto;\n}\n.svg-inline--fa.fa-pull-right {\n  margin-left: var(--fa-pull-margin, 0.3em);\n  width: auto;\n}\n.svg-inline--fa.fa-li {\n  width: var(--fa-li-width, 2em);\n  top: 0.25em;\n}\n.svg-inline--fa.fa-fw {\n  width: var(--fa-fw-width, 1.25em);\n}\n\n.fa-layers svg.svg-inline--fa {\n  bottom: 0;\n  left: 0;\n  margin: auto;\n  position: absolute;\n  right: 0;\n  top: 0;\n}\n\n.fa-layers-counter, .fa-layers-text {\n  display: inline-block;\n  position: absolute;\n  text-align: center;\n}\n\n.fa-layers {\n  display: inline-block;\n  height: 1em;\n  position: relative;\n  text-align: center;\n  vertical-align: -0.125em;\n  width: 1em;\n}\n.fa-layers svg.svg-inline--fa {\n  transform-origin: center center;\n}\n\n.fa-layers-text {\n  left: 50%;\n  top: 50%;\n  transform: translate(-50%, -50%);\n  transform-origin: center center;\n}\n\n.fa-layers-counter {\n  background-color: var(--fa-counter-background-color, #ff253a);\n  border-radius: var(--fa-counter-border-radius, 1em);\n  box-sizing: border-box;\n  color: var(--fa-inverse, #fff);\n  line-height: var(--fa-counter-line-height, 1);\n  max-width: var(--fa-counter-max-width, 5em);\n  min-width: var(--fa-counter-min-width, 1.5em);\n  overflow: hidden;\n  padding: var(--fa-counter-padding, 0.25em 0.5em);\n  right: var(--fa-right, 0);\n  text-overflow: ellipsis;\n  top: var(--fa-top, 0);\n  transform: scale(var(--fa-counter-scale, 0.25));\n  transform-origin: top right;\n}\n\n.fa-layers-bottom-right {\n  bottom: var(--fa-bottom, 0);\n  right: var(--fa-right, 0);\n  top: auto;\n  transform: scale(var(--fa-layers-scale, 0.25));\n  transform-origin: bottom right;\n}\n\n.fa-layers-bottom-left {\n  bottom: var(--fa-bottom, 0);\n  left: var(--fa-left, 0);\n  right: auto;\n  top: auto;\n  transform: scale(var(--fa-layers-scale, 0.25));\n  transform-origin: bottom left;\n}\n\n.fa-layers-top-right {\n  top: var(--fa-top, 0);\n  right: var(--fa-right, 0);\n  transform: scale(var(--fa-layers-scale, 0.25));\n  transform-origin: top right;\n}\n\n.fa-layers-top-left {\n  left: var(--fa-left, 0);\n  right: auto;\n  top: var(--fa-top, 0);\n  transform: scale(var(--fa-layers-scale, 0.25));\n  transform-origin: top left;\n}\n\n.fa-1x {\n  font-size: 1em;\n}\n\n.fa-2x {\n  font-size: 2em;\n}\n\n.fa-3x {\n  font-size: 3em;\n}\n\n.fa-4x {\n  font-size: 4em;\n}\n\n.fa-5x {\n  font-size: 5em;\n}\n\n.fa-6x {\n  font-size: 6em;\n}\n\n.fa-7x {\n  font-size: 7em;\n}\n\n.fa-8x {\n  font-size: 8em;\n}\n\n.fa-9x {\n  font-size: 9em;\n}\n\n.fa-10x {\n  font-size: 10em;\n}\n\n.fa-2xs {\n  font-size: 0.625em;\n  line-height: 0.1em;\n  vertical-align: 0.225em;\n}\n\n.fa-xs {\n  font-size: 0.75em;\n  line-height: 0.0833333337em;\n  vertical-align: 0.125em;\n}\n\n.fa-sm {\n  font-size: 0.875em;\n  line-height: 0.0714285718em;\n  vertical-align: 0.0535714295em;\n}\n\n.fa-lg {\n  font-size: 1.25em;\n  line-height: 0.05em;\n  vertical-align: -0.075em;\n}\n\n.fa-xl {\n  font-size: 1.5em;\n  line-height: 0.0416666682em;\n  vertical-align: -0.125em;\n}\n\n.fa-2xl {\n  font-size: 2em;\n  line-height: 0.03125em;\n  vertical-align: -0.1875em;\n}\n\n.fa-fw {\n  text-align: center;\n  width: 1.25em;\n}\n\n.fa-ul {\n  list-style-type: none;\n  margin-left: var(--fa-li-margin, 2.5em);\n  padding-left: 0;\n}\n.fa-ul > li {\n  position: relative;\n}\n\n.fa-li {\n  left: calc(-1 * var(--fa-li-width, 2em));\n  position: absolute;\n  text-align: center;\n  width: var(--fa-li-width, 2em);\n  line-height: inherit;\n}\n\n.fa-border {\n  border-color: var(--fa-border-color, #eee);\n  border-radius: var(--fa-border-radius, 0.1em);\n  border-style: var(--fa-border-style, solid);\n  border-width: var(--fa-border-width, 0.08em);\n  padding: var(--fa-border-padding, 0.2em 0.25em 0.15em);\n}\n\n.fa-pull-left {\n  float: left;\n  margin-right: var(--fa-pull-margin, 0.3em);\n}\n\n.fa-pull-right {\n  float: right;\n  margin-left: var(--fa-pull-margin, 0.3em);\n}\n\n.fa-beat {\n  animation-name: fa-beat;\n  animation-delay: var(--fa-animation-delay, 0s);\n  animation-direction: var(--fa-animation-direction, normal);\n  animation-duration: var(--fa-animation-duration, 1s);\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  animation-timing-function: var(--fa-animation-timing, ease-in-out);\n}\n\n.fa-bounce {\n  animation-name: fa-bounce;\n  animation-delay: var(--fa-animation-delay, 0s);\n  animation-direction: var(--fa-animation-direction, normal);\n  animation-duration: var(--fa-animation-duration, 1s);\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.28, 0.84, 0.42, 1));\n}\n\n.fa-fade {\n  animation-name: fa-fade;\n  animation-delay: var(--fa-animation-delay, 0s);\n  animation-direction: var(--fa-animation-direction, normal);\n  animation-duration: var(--fa-animation-duration, 1s);\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1));\n}\n\n.fa-beat-fade {\n  animation-name: fa-beat-fade;\n  animation-delay: var(--fa-animation-delay, 0s);\n  animation-direction: var(--fa-animation-direction, normal);\n  animation-duration: var(--fa-animation-duration, 1s);\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1));\n}\n\n.fa-flip {\n  animation-name: fa-flip;\n  animation-delay: var(--fa-animation-delay, 0s);\n  animation-direction: var(--fa-animation-direction, normal);\n  animation-duration: var(--fa-animation-duration, 1s);\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  animation-timing-function: var(--fa-animation-timing, ease-in-out);\n}\n\n.fa-shake {\n  animation-name: fa-shake;\n  animation-delay: var(--fa-animation-delay, 0s);\n  animation-direction: var(--fa-animation-direction, normal);\n  animation-duration: var(--fa-animation-duration, 1s);\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  animation-timing-function: var(--fa-animation-timing, linear);\n}\n\n.fa-spin {\n  animation-name: fa-spin;\n  animation-delay: var(--fa-animation-delay, 0s);\n  animation-direction: var(--fa-animation-direction, normal);\n  animation-duration: var(--fa-animation-duration, 2s);\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  animation-timing-function: var(--fa-animation-timing, linear);\n}\n\n.fa-spin-reverse {\n  --fa-animation-direction: reverse;\n}\n\n.fa-pulse,\n.fa-spin-pulse {\n  animation-name: fa-spin;\n  animation-direction: var(--fa-animation-direction, normal);\n  animation-duration: var(--fa-animation-duration, 1s);\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  animation-timing-function: var(--fa-animation-timing, steps(8));\n}\n\n@media (prefers-reduced-motion: reduce) {\n  .fa-beat,\n.fa-bounce,\n.fa-fade,\n.fa-beat-fade,\n.fa-flip,\n.fa-pulse,\n.fa-shake,\n.fa-spin,\n.fa-spin-pulse {\n    animation-delay: -1ms;\n    animation-duration: 1ms;\n    animation-iteration-count: 1;\n    transition-delay: 0s;\n    transition-duration: 0s;\n  }\n}\n@keyframes fa-beat {\n  0%, 90% {\n    transform: scale(1);\n  }\n  45% {\n    transform: scale(var(--fa-beat-scale, 1.25));\n  }\n}\n@keyframes fa-bounce {\n  0% {\n    transform: scale(1, 1) translateY(0);\n  }\n  10% {\n    transform: scale(var(--fa-bounce-start-scale-x, 1.1), var(--fa-bounce-start-scale-y, 0.9)) translateY(0);\n  }\n  30% {\n    transform: scale(var(--fa-bounce-jump-scale-x, 0.9), var(--fa-bounce-jump-scale-y, 1.1)) translateY(var(--fa-bounce-height, -0.5em));\n  }\n  50% {\n    transform: scale(var(--fa-bounce-land-scale-x, 1.05), var(--fa-bounce-land-scale-y, 0.95)) translateY(0);\n  }\n  57% {\n    transform: scale(1, 1) translateY(var(--fa-bounce-rebound, -0.125em));\n  }\n  64% {\n    transform: scale(1, 1) translateY(0);\n  }\n  100% {\n    transform: scale(1, 1) translateY(0);\n  }\n}\n@keyframes fa-fade {\n  50% {\n    opacity: var(--fa-fade-opacity, 0.4);\n  }\n}\n@keyframes fa-beat-fade {\n  0%, 100% {\n    opacity: var(--fa-beat-fade-opacity, 0.4);\n    transform: scale(1);\n  }\n  50% {\n    opacity: 1;\n    transform: scale(var(--fa-beat-fade-scale, 1.125));\n  }\n}\n@keyframes fa-flip {\n  50% {\n    transform: rotate3d(var(--fa-flip-x, 0), var(--fa-flip-y, 1), var(--fa-flip-z, 0), var(--fa-flip-angle, -180deg));\n  }\n}\n@keyframes fa-shake {\n  0% {\n    transform: rotate(-15deg);\n  }\n  4% {\n    transform: rotate(15deg);\n  }\n  8%, 24% {\n    transform: rotate(-18deg);\n  }\n  12%, 28% {\n    transform: rotate(18deg);\n  }\n  16% {\n    transform: rotate(-22deg);\n  }\n  20% {\n    transform: rotate(22deg);\n  }\n  32% {\n    transform: rotate(-12deg);\n  }\n  36% {\n    transform: rotate(12deg);\n  }\n  40%, 100% {\n    transform: rotate(0deg);\n  }\n}\n@keyframes fa-spin {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n.fa-rotate-90 {\n  transform: rotate(90deg);\n}\n\n.fa-rotate-180 {\n  transform: rotate(180deg);\n}\n\n.fa-rotate-270 {\n  transform: rotate(270deg);\n}\n\n.fa-flip-horizontal {\n  transform: scale(-1, 1);\n}\n\n.fa-flip-vertical {\n  transform: scale(1, -1);\n}\n\n.fa-flip-both,\n.fa-flip-horizontal.fa-flip-vertical {\n  transform: scale(-1, -1);\n}\n\n.fa-rotate-by {\n  transform: rotate(var(--fa-rotate-angle, 0));\n}\n\n.fa-stack {\n  display: inline-block;\n  vertical-align: middle;\n  height: 2em;\n  position: relative;\n  width: 2.5em;\n}\n\n.fa-stack-1x,\n.fa-stack-2x {\n  bottom: 0;\n  left: 0;\n  margin: auto;\n  position: absolute;\n  right: 0;\n  top: 0;\n  z-index: var(--fa-stack-z-index, auto);\n}\n\n.svg-inline--fa.fa-stack-1x {\n  height: 1em;\n  width: 1.25em;\n}\n.svg-inline--fa.fa-stack-2x {\n  height: 2em;\n  width: 2.5em;\n}\n\n.fa-inverse {\n  color: var(--fa-inverse, #fff);\n}\n\n.sr-only,\n.fa-sr-only {\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  margin: -1px;\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0);\n  white-space: nowrap;\n  border-width: 0;\n}\n\n.sr-only-focusable:not(:focus),\n.fa-sr-only-focusable:not(:focus) {\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  margin: -1px;\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0);\n  white-space: nowrap;\n  border-width: 0;\n}\n\n.svg-inline--fa .fa-primary {\n  fill: var(--fa-primary-color, currentColor);\n  opacity: var(--fa-primary-opacity, 1);\n}\n\n.svg-inline--fa .fa-secondary {\n  fill: var(--fa-secondary-color, currentColor);\n  opacity: var(--fa-secondary-opacity, 0.4);\n}\n\n.svg-inline--fa.fa-swap-opacity .fa-primary {\n  opacity: var(--fa-secondary-opacity, 0.4);\n}\n\n.svg-inline--fa.fa-swap-opacity .fa-secondary {\n  opacity: var(--fa-primary-opacity, 1);\n}\n\n.svg-inline--fa mask .fa-primary,\n.svg-inline--fa mask .fa-secondary {\n  fill: black;\n}';if("fa"!==fp||rc!==drc){const dPatt=new RegExp("\\.".concat("fa","\\-"),"g"),customPropPatt=new RegExp("\\--".concat("fa","\\-"),"g"),rPatt=new RegExp("\\.".concat(drc),"g");s=s.replace(dPatt,".".concat(fp,"-")).replace(customPropPatt,"--".concat(fp,"-")).replace(rPatt,".".concat(rc))}return s}let _cssInserted=!1;function ensureCss(){config.autoAddCss&&!_cssInserted&&(!function insertCss(css){if(!css||!IS_DOM)return;const style=DOCUMENT.createElement("style");style.setAttribute("type","text/css"),style.innerHTML=css;const headChildren=DOCUMENT.head.childNodes;let beforeChild=null;for(let i=headChildren.length-1;i>-1;i--){const child=headChildren[i],tagName=(child.tagName||"").toUpperCase();["STYLE","LINK"].indexOf(tagName)>-1&&(beforeChild=child)}return DOCUMENT.head.insertBefore(style,beforeChild),css}(css()),_cssInserted=!0)}var InjectCSS={mixout:()=>({dom:{css,insertCss:ensureCss}}),hooks:()=>({beforeDOMElementCreation(){ensureCss()},beforeI2svg(){ensureCss()}})};const w=WINDOW||{};w.___FONT_AWESOME___||(w.___FONT_AWESOME___={}),w.___FONT_AWESOME___.styles||(w.___FONT_AWESOME___.styles={}),w.___FONT_AWESOME___.hooks||(w.___FONT_AWESOME___.hooks={}),w.___FONT_AWESOME___.shims||(w.___FONT_AWESOME___.shims=[]);var namespace=w.___FONT_AWESOME___;const functions=[],listener=function(){DOCUMENT.removeEventListener("DOMContentLoaded",listener),loaded=1,functions.map((fn=>fn()))};let loaded=!1;function toHtml(abstractNodes){const{tag,attributes={},children=[]}=abstractNodes;return"string"==typeof abstractNodes?htmlEscape(abstractNodes):"<".concat(tag," ").concat(function joinAttributes(attributes){return Object.keys(attributes||{}).reduce(((acc,attributeName)=>acc+"".concat(attributeName,'="').concat(htmlEscape(attributes[attributeName]),'" ')),"").trim()}(attributes),">").concat(children.map(toHtml).join(""),"</").concat(tag,">")}function iconFromMapping(mapping,prefix,iconName){if(mapping&&mapping[prefix]&&mapping[prefix][iconName])return{prefix,iconName,icon:mapping[prefix][iconName]}}IS_DOM&&(loaded=(DOCUMENT.documentElement.doScroll?/^loaded|^c/:/^loaded|^i|^c/).test(DOCUMENT.readyState),loaded||DOCUMENT.addEventListener("DOMContentLoaded",listener));var reduce=function fastReduceObject(subject,fn,initialValue,thisContext){var i,key,result,keys=Object.keys(subject),length=keys.length,iterator=void 0!==thisContext?function bindInternal4(func,thisContext){return function(a,b,c,d){return func.call(thisContext,a,b,c,d)}}(fn,thisContext):fn;for(void 0===initialValue?(i=1,result=subject[keys[0]]):(i=0,result=initialValue);i<length;i++)result=iterator(result,subject[key=keys[i]],key,subject);return result};function toHex(unicode){const decoded=function ucs2decode(string){const output=[];let counter=0;const length=string.length;for(;counter<length;){const value=string.charCodeAt(counter++);if(value>=55296&&value<=56319&&counter<length){const extra=string.charCodeAt(counter++);56320==(64512&extra)?output.push(((1023&value)<<10)+(1023&extra)+65536):(output.push(value),counter--)}else output.push(value)}return output}(unicode);return 1===decoded.length?decoded[0].toString(16):null}function normalizeIcons(icons){return Object.keys(icons).reduce(((acc,iconName)=>{const icon=icons[iconName];return!!icon.icon?acc[icon.iconName]=icon.icon:acc[iconName]=icon,acc}),{})}function defineIcons(prefix,icons){let params=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const{skipHooks=!1}=params,normalized=normalizeIcons(icons);"function"!=typeof namespace.hooks.addPack||skipHooks?namespace.styles[prefix]=_objectSpread2(_objectSpread2({},namespace.styles[prefix]||{}),normalized):namespace.hooks.addPack(prefix,normalizeIcons(icons)),"fas"===prefix&&defineIcons("fa",icons)}const{styles,shims}=namespace,FAMILY_NAMES=Object.keys(PREFIX_TO_LONG_STYLE),PREFIXES_FOR_FAMILY=FAMILY_NAMES.reduce(((acc,familyId)=>(acc[familyId]=Object.keys(PREFIX_TO_LONG_STYLE[familyId]),acc)),{});let _defaultUsablePrefix=null,_byUnicode={},_byLigature={},_byOldName={},_byOldUnicode={},_byAlias={};function getIconName(cssPrefix,cls){const parts=cls.split("-"),prefix=parts[0],iconName=parts.slice(1).join("-");return prefix!==cssPrefix||""===iconName||function isReserved(name){return~RESERVED_CLASSES.indexOf(name)}(iconName)?null:iconName}const build=()=>{const lookup=reducer=>reduce(styles,((o$$1,style,prefix)=>(o$$1[prefix]=reduce(style,reducer,{}),o$$1)),{});_byUnicode=lookup(((acc,icon,iconName)=>{if(icon[3]&&(acc[icon[3]]=iconName),icon[2]){icon[2].filter((a$$1=>"number"==typeof a$$1)).forEach((alias=>{acc[alias.toString(16)]=iconName}))}return acc})),_byLigature=lookup(((acc,icon,iconName)=>{if(acc[iconName]=iconName,icon[2]){icon[2].filter((a$$1=>"string"==typeof a$$1)).forEach((alias=>{acc[alias]=iconName}))}return acc})),_byAlias=lookup(((acc,icon,iconName)=>{const aliases=icon[2];return acc[iconName]=iconName,aliases.forEach((alias=>{acc[alias]=iconName})),acc}));const hasRegular="far"in styles||config.autoFetchSvg,shimLookups=reduce(shims,((acc,shim)=>{const maybeNameMaybeUnicode=shim[0];let prefix=shim[1];const iconName=shim[2];return"far"!==prefix||hasRegular||(prefix="fas"),"string"==typeof maybeNameMaybeUnicode&&(acc.names[maybeNameMaybeUnicode]={prefix,iconName}),"number"==typeof maybeNameMaybeUnicode&&(acc.unicodes[maybeNameMaybeUnicode.toString(16)]={prefix,iconName}),acc}),{names:{},unicodes:{}});_byOldName=shimLookups.names,_byOldUnicode=shimLookups.unicodes,_defaultUsablePrefix=getCanonicalPrefix(config.styleDefault,{family:config.familyDefault})};function byUnicode(prefix,unicode){return(_byUnicode[prefix]||{})[unicode]}function byAlias(prefix,alias){return(_byAlias[prefix]||{})[alias]}function byOldName(name){return _byOldName[name]||{prefix:null,iconName:null}}function getDefaultUsablePrefix(){return _defaultUsablePrefix}!function onChange(cb){return _onChangeCb.push(cb),()=>{_onChangeCb.splice(_onChangeCb.indexOf(cb),1)}}((c$$1=>{_defaultUsablePrefix=getCanonicalPrefix(c$$1.styleDefault,{family:config.familyDefault})})),build();function getCanonicalPrefix(styleOrPrefix){let params=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{family=s}=params,style=PREFIX_TO_STYLE[family][styleOrPrefix];if(family===t&&!styleOrPrefix)return"fad";const prefix=STYLE_TO_PREFIX[family][styleOrPrefix]||STYLE_TO_PREFIX[family][style],defined=styleOrPrefix in namespace.styles?styleOrPrefix:null;return prefix||defined||null}function sortedUniqueValues(arr){return arr.sort().filter(((value,index,arr)=>arr.indexOf(value)===index))}function getCanonicalIcon(values){let params=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{skipLookups=!1}=params;let givenPrefix=null;const faCombinedClasses=Ia.concat(bt$1),faStyleOrFamilyClasses=sortedUniqueValues(values.filter((cls=>faCombinedClasses.includes(cls)))),nonStyleOrFamilyClasses=sortedUniqueValues(values.filter((cls=>!Ia.includes(cls)))),faStyles=faStyleOrFamilyClasses.filter((cls=>(givenPrefix=cls,!P.includes(cls)))),[styleFromValues=null]=faStyles,family=function getFamilyId(values){let family=s;const famProps=FAMILY_NAMES.reduce(((acc,familyId)=>(acc[familyId]="".concat(config.cssPrefix,"-").concat(familyId),acc)),{});return L.forEach((familyId=>{(values.includes(famProps[familyId])||values.some((v$$1=>PREFIXES_FOR_FAMILY[familyId].includes(v$$1))))&&(family=familyId)})),family}(faStyleOrFamilyClasses),canonical=_objectSpread2(_objectSpread2({},function moveNonFaClassesToRest(classNames){let rest=[],iconName=null;return classNames.forEach((cls=>{const result=getIconName(config.cssPrefix,cls);result?iconName=result:cls&&rest.push(cls)})),{iconName,rest}}(nonStyleOrFamilyClasses)),{},{prefix:getCanonicalPrefix(styleFromValues,{family})});return _objectSpread2(_objectSpread2(_objectSpread2({},canonical),function getDefaultCanonicalPrefix(prefixOptions){const{values,family,canonical,givenPrefix="",styles={},config:config$$1={}}=prefixOptions,isDuotoneFamily=family===t,valuesHasDuotone=values.includes("fa-duotone")||values.includes("fad"),defaultFamilyIsDuotone="duotone"===config$$1.familyDefault,canonicalPrefixIsDuotone="fad"===canonical.prefix||"fa-duotone"===canonical.prefix;!isDuotoneFamily&&(valuesHasDuotone||defaultFamilyIsDuotone||canonicalPrefixIsDuotone)&&(canonical.prefix="fad");(values.includes("fa-brands")||values.includes("fab"))&&(canonical.prefix="fab");if(!canonical.prefix&&newCanonicalFamilies.includes(family)){if(Object.keys(styles).find((key=>newCanonicalStyles.includes(key)))||config$$1.autoFetchSvg){const defaultPrefix=pt.get(family).defaultShortPrefixId;canonical.prefix=defaultPrefix,canonical.iconName=byAlias(canonical.prefix,canonical.iconName)||canonical.iconName}}"fa"!==canonical.prefix&&"fa"!==givenPrefix||(canonical.prefix=getDefaultUsablePrefix()||"fas");return canonical}({values,family,styles,config,canonical,givenPrefix})),function applyShimAndAlias(skipLookups,givenPrefix,canonical){let{prefix,iconName}=canonical;if(skipLookups||!prefix||!iconName)return{prefix,iconName};const shim="fa"===givenPrefix?byOldName(iconName):{},aliasIconName=byAlias(prefix,iconName);iconName=shim.iconName||aliasIconName||iconName,prefix=shim.prefix||prefix,"far"!==prefix||styles.far||!styles.fas||config.autoFetchSvg||(prefix="fas");return{prefix,iconName}}(skipLookups,givenPrefix,canonical))}const newCanonicalFamilies=L.filter((familyId=>familyId!==s||familyId!==t)),newCanonicalStyles=Object.keys(ga).filter((key=>key!==s)).map((key=>Object.keys(ga[key]))).flat();let _plugins=[],_hooks={};const providers={},defaultProviderKeys=Object.keys(providers);function chainHooks(hook,accumulator){for(var _len=arguments.length,args=new Array(_len>2?_len-2:0),_key=2;_key<_len;_key++)args[_key-2]=arguments[_key];return(_hooks[hook]||[]).forEach((hookFn=>{accumulator=hookFn.apply(null,[accumulator,...args])})),accumulator}function callHooks(hook){for(var _len2=arguments.length,args=new Array(_len2>1?_len2-1:0),_key2=1;_key2<_len2;_key2++)args[_key2-1]=arguments[_key2];(_hooks[hook]||[]).forEach((hookFn=>{hookFn.apply(null,args)}))}function callProvided(){const hook=arguments[0],args=Array.prototype.slice.call(arguments,1);return providers[hook]?providers[hook].apply(null,args):void 0}function findIconDefinition(iconLookup){"fa"===iconLookup.prefix&&(iconLookup.prefix="fas");let{iconName}=iconLookup;const prefix=iconLookup.prefix||getDefaultUsablePrefix();if(iconName)return iconName=byAlias(prefix,iconName)||iconName,iconFromMapping(library.definitions,prefix,iconName)||iconFromMapping(namespace.styles,prefix,iconName)}const library=new class Library{constructor(){this.definitions={}}add(){for(var _len=arguments.length,definitions=new Array(_len),_key=0;_key<_len;_key++)definitions[_key]=arguments[_key];const additions=definitions.reduce(this._pullDefinitions,{});Object.keys(additions).forEach((key=>{this.definitions[key]=_objectSpread2(_objectSpread2({},this.definitions[key]||{}),additions[key]),defineIcons(key,additions[key]);const longPrefix=PREFIX_TO_LONG_STYLE[s][key];longPrefix&&defineIcons(longPrefix,additions[key]),build()}))}reset(){this.definitions={}}_pullDefinitions(additions,definition){const normalized=definition.prefix&&definition.iconName&&definition.icon?{0:definition}:definition;return Object.keys(normalized).map((key=>{const{prefix,iconName,icon}=normalized[key],aliases=icon[2];additions[prefix]||(additions[prefix]={}),aliases.length>0&&aliases.forEach((alias=>{"string"==typeof alias&&(additions[prefix][alias]=icon)})),additions[prefix][iconName]=icon})),additions}},dom={i2svg:function(){let params=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return IS_DOM?(callHooks("beforeI2svg",params),callProvided("pseudoElements2svg",params),callProvided("i2svg",params)):Promise.reject(new Error("Operation requires a DOM of some kind."))},watch:function(){let params=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const{autoReplaceSvgRoot}=params;!1===config.autoReplaceSvg&&(config.autoReplaceSvg=!0),config.observeMutations=!0,function domready(fn){IS_DOM&&(loaded?setTimeout(fn,0):functions.push(fn))}((()=>{autoReplace({autoReplaceSvgRoot}),callHooks("watch",params)}))}},parse={icon:icon=>{if(null===icon)return null;if("object"==typeof icon&&icon.prefix&&icon.iconName)return{prefix:icon.prefix,iconName:byAlias(icon.prefix,icon.iconName)||icon.iconName};if(Array.isArray(icon)&&2===icon.length){const iconName=0===icon[1].indexOf("fa-")?icon[1].slice(3):icon[1],prefix=getCanonicalPrefix(icon[0]);return{prefix,iconName:byAlias(prefix,iconName)||iconName}}if("string"==typeof icon&&(icon.indexOf("".concat(config.cssPrefix,"-"))>-1||icon.match(ICON_SELECTION_SYNTAX_PATTERN))){const canonicalIcon=getCanonicalIcon(icon.split(" "),{skipLookups:!0});return{prefix:canonicalIcon.prefix||getDefaultUsablePrefix(),iconName:byAlias(canonicalIcon.prefix,canonicalIcon.iconName)||canonicalIcon.iconName}}if("string"==typeof icon){const prefix=getDefaultUsablePrefix();return{prefix,iconName:byAlias(prefix,icon)||icon}}}},api={noAuto:()=>{config.autoReplaceSvg=!1,config.observeMutations=!1,callHooks("noAuto")},config,dom,parse,library,findIconDefinition,toHtml},autoReplace=function(){let params=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const{autoReplaceSvgRoot=DOCUMENT}=params;(Object.keys(namespace.styles).length>0||config.autoFetchSvg)&&IS_DOM&&config.autoReplaceSvg&&api.dom.i2svg({node:autoReplaceSvgRoot})};function domVariants(val,abstractCreator){return Object.defineProperty(val,"abstract",{get:abstractCreator}),Object.defineProperty(val,"html",{get:function(){return val.abstract.map((a=>toHtml(a)))}}),Object.defineProperty(val,"node",{get:function(){if(!IS_DOM)return;const container=DOCUMENT.createElement("div");return container.innerHTML=val.html,container.children}}),val}function makeInlineSvgAbstract(params){const{icons:{main,mask},prefix,iconName,transform,symbol,title,maskId,titleId,extra,watchable=!1}=params,{width,height}=mask.found?mask:main,isUploadedIcon=Lt.includes(prefix),attrClass=[config.replacementClass,iconName?"".concat(config.cssPrefix,"-").concat(iconName):""].filter((c$$1=>-1===extra.classes.indexOf(c$$1))).filter((c$$1=>""!==c$$1||!!c$$1)).concat(extra.classes).join(" ");let content={children:[],attributes:_objectSpread2(_objectSpread2({},extra.attributes),{},{"data-prefix":prefix,"data-icon":iconName,class:attrClass,role:extra.attributes.role||"img",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 ".concat(width," ").concat(height)})};const uploadedIconWidthStyle=isUploadedIcon&&!~extra.classes.indexOf("fa-fw")?{width:"".concat(width/height*16*.0625,"em")}:{};watchable&&(content.attributes["data-fa-i2svg"]=""),title&&(content.children.push({tag:"title",attributes:{id:content.attributes["aria-labelledby"]||"title-".concat(titleId||nextUniqueId())},children:[title]}),delete content.attributes.title);const args=_objectSpread2(_objectSpread2({},content),{},{prefix,iconName,main,mask,maskId,transform,symbol,styles:_objectSpread2(_objectSpread2({},uploadedIconWidthStyle),extra.styles)}),{children,attributes}=mask.found&&main.found?callProvided("generateAbstractMask",args)||{children:[],attributes:{}}:callProvided("generateAbstractIcon",args)||{children:[],attributes:{}};return args.children=children,args.attributes=attributes,symbol?function asSymbol(_ref){let{prefix,iconName,children,attributes,symbol}=_ref;const id=!0===symbol?"".concat(prefix,"-").concat(config.cssPrefix,"-").concat(iconName):symbol;return[{tag:"svg",attributes:{style:"display: none;"},children:[{tag:"symbol",attributes:_objectSpread2(_objectSpread2({},attributes),{},{id}),children}]}]}(args):function asIcon(_ref){let{children,main,mask,attributes,styles,transform}=_ref;if(transformIsMeaningful(transform)&&main.found&&!mask.found){const{width,height}=main,offset={x:width/height/2,y:.5};attributes.style=joinStyles(_objectSpread2(_objectSpread2({},styles),{},{"transform-origin":"".concat(offset.x+transform.x/16,"em ").concat(offset.y+transform.y/16,"em")}))}return[{tag:"svg",attributes,children}]}(args)}function makeLayersTextAbstract(params){const{content,width,height,transform,title,extra,watchable=!1}=params,attributes=_objectSpread2(_objectSpread2(_objectSpread2({},extra.attributes),title?{title}:{}),{},{class:extra.classes.join(" ")});watchable&&(attributes["data-fa-i2svg"]="");const styles=_objectSpread2({},extra.styles);transformIsMeaningful(transform)&&(styles.transform=function transformForCss(_ref2){let{transform,width=UNITS_IN_GRID,height=UNITS_IN_GRID,startCentered=!1}=_ref2,val="";return val+=startCentered&&IS_IE?"translate(".concat(transform.x/d$2-width/2,"em, ").concat(transform.y/d$2-height/2,"em) "):startCentered?"translate(calc(-50% + ".concat(transform.x/d$2,"em), calc(-50% + ").concat(transform.y/d$2,"em)) "):"translate(".concat(transform.x/d$2,"em, ").concat(transform.y/d$2,"em) "),val+="scale(".concat(transform.size/d$2*(transform.flipX?-1:1),", ").concat(transform.size/d$2*(transform.flipY?-1:1),") "),val+="rotate(".concat(transform.rotate,"deg) "),val}({transform,startCentered:!0,width,height}),styles["-webkit-transform"]=styles.transform);const styleString=joinStyles(styles);styleString.length>0&&(attributes.style=styleString);const val=[];return val.push({tag:"span",attributes,children:[content]}),title&&val.push({tag:"span",attributes:{class:"sr-only"},children:[title]}),val}const{styles:styles$1}=namespace;function asFoundIcon(icon){const width=icon[0],height=icon[1],[vectorData]=icon.slice(4);let element=null;return element=Array.isArray(vectorData)?{tag:"g",attributes:{class:"".concat(config.cssPrefix,"-").concat(DUOTONE_CLASSES.GROUP)},children:[{tag:"path",attributes:{class:"".concat(config.cssPrefix,"-").concat(DUOTONE_CLASSES.SECONDARY),fill:"currentColor",d:vectorData[0]}},{tag:"path",attributes:{class:"".concat(config.cssPrefix,"-").concat(DUOTONE_CLASSES.PRIMARY),fill:"currentColor",d:vectorData[1]}}]}:{tag:"path",attributes:{fill:"currentColor",d:vectorData}},{found:!0,width,height,icon:element}}const missingIconResolutionMixin={found:!1,width:512,height:512};function findIcon(iconName,prefix){let givenPrefix=prefix;return"fa"===prefix&&null!==config.styleDefault&&(prefix=getDefaultUsablePrefix()),new Promise(((resolve,reject)=>{if("fa"===givenPrefix){const shim=byOldName(iconName)||{};iconName=shim.iconName||iconName,prefix=shim.prefix||prefix}if(iconName&&prefix&&styles$1[prefix]&&styles$1[prefix][iconName]){return resolve(asFoundIcon(styles$1[prefix][iconName]))}!function maybeNotifyMissing(iconName,prefix){PRODUCTION||config.showMissingIcons||!iconName||console.error('Icon with name "'.concat(iconName,'" and prefix "').concat(prefix,'" is missing.'))}(iconName,prefix),resolve(_objectSpread2(_objectSpread2({},missingIconResolutionMixin),{},{icon:config.showMissingIcons&&iconName&&callProvided("missingIconAbstract")||{}}))}))}const noop$1=()=>{},p$2=config.measurePerformance&&PERFORMANCE&&PERFORMANCE.mark&&PERFORMANCE.measure?PERFORMANCE:{mark:noop$1,measure:noop$1},end=name=>{p$2.mark("".concat('FA "6.7.2"'," ").concat(name," ends")),p$2.measure("".concat('FA "6.7.2"'," ").concat(name),"".concat('FA "6.7.2"'," ").concat(name," begins"),"".concat('FA "6.7.2"'," ").concat(name," ends"))};var perf_begin=name=>(p$2.mark("".concat('FA "6.7.2"'," ").concat(name," begins")),()=>end(name));const noop$2=()=>{};function isWatched(node){return"string"==typeof(node.getAttribute?node.getAttribute("data-fa-i2svg"):null)}function createElementNS(tag){return DOCUMENT.createElementNS("http://www.w3.org/2000/svg",tag)}function createElement(tag){return DOCUMENT.createElement(tag)}function convertSVG(abstractObj){let params=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{ceFn="svg"===abstractObj.tag?createElementNS:createElement}=params;if("string"==typeof abstractObj)return DOCUMENT.createTextNode(abstractObj);const tag=ceFn(abstractObj.tag);Object.keys(abstractObj.attributes||[]).forEach((function(key){tag.setAttribute(key,abstractObj.attributes[key])}));return(abstractObj.children||[]).forEach((function(child){tag.appendChild(convertSVG(child,{ceFn}))})),tag}const mutators={replace:function(mutation){const node=mutation[0];if(node.parentNode)if(mutation[1].forEach((abstract=>{node.parentNode.insertBefore(convertSVG(abstract),node)})),null===node.getAttribute("data-fa-i2svg")&&config.keepOriginalSource){let comment=DOCUMENT.createComment(function nodeAsComment(node){let comment=" ".concat(node.outerHTML," ");return comment="".concat(comment,"Font Awesome fontawesome.com "),comment}(node));node.parentNode.replaceChild(comment,node)}else node.remove()},nest:function(mutation){const node=mutation[0],abstract=mutation[1];if(~classArray(node).indexOf(config.replacementClass))return mutators.replace(mutation);const forSvg=new RegExp("".concat(config.cssPrefix,"-.*"));if(delete abstract[0].attributes.id,abstract[0].attributes.class){const splitClasses=abstract[0].attributes.class.split(" ").reduce(((acc,cls)=>(cls===config.replacementClass||cls.match(forSvg)?acc.toSvg.push(cls):acc.toNode.push(cls),acc)),{toNode:[],toSvg:[]});abstract[0].attributes.class=splitClasses.toSvg.join(" "),0===splitClasses.toNode.length?node.removeAttribute("class"):node.setAttribute("class",splitClasses.toNode.join(" "))}const newInnerHTML=abstract.map((a=>toHtml(a))).join("\n");node.setAttribute("data-fa-i2svg",""),node.innerHTML=newInnerHTML}};function performOperationSync(op){op()}function perform(mutations,callback){const callbackFunction="function"==typeof callback?callback:noop$2;if(0===mutations.length)callbackFunction();else{let frame=performOperationSync;"async"===config.mutateApproach&&(frame=WINDOW.requestAnimationFrame||performOperationSync),frame((()=>{const mutator=function getMutator(){return!0===config.autoReplaceSvg?mutators.replace:mutators[config.autoReplaceSvg]||mutators.replace}(),mark=perf_begin("mutate");mutations.map(mutator),mark(),callbackFunction()}))}}let disabled=!1;function disableObservation(){disabled=!0}function enableObservation(){disabled=!1}let mo=null;function observe(options){if(!MUTATION_OBSERVER)return;if(!config.observeMutations)return;const{treeCallback=noop$2,nodeCallback=noop$2,pseudoElementsCallback=noop$2,observeMutationsRoot=DOCUMENT}=options;mo=new MUTATION_OBSERVER((objects=>{if(disabled)return;const defaultPrefix=getDefaultUsablePrefix();toArray(objects).forEach((mutationRecord=>{if("childList"===mutationRecord.type&&mutationRecord.addedNodes.length>0&&!isWatched(mutationRecord.addedNodes[0])&&(config.searchPseudoElements&&pseudoElementsCallback(mutationRecord.target),treeCallback(mutationRecord.target)),"attributes"===mutationRecord.type&&mutationRecord.target.parentNode&&config.searchPseudoElements&&pseudoElementsCallback(mutationRecord.target.parentNode),"attributes"===mutationRecord.type&&isWatched(mutationRecord.target)&&~ATTRIBUTES_WATCHED_FOR_MUTATION.indexOf(mutationRecord.attributeName))if("class"===mutationRecord.attributeName&&function hasPrefixAndIcon(node){const prefix=node.getAttribute?node.getAttribute("data-prefix"):null,icon=node.getAttribute?node.getAttribute("data-icon"):null;return prefix&&icon}(mutationRecord.target)){const{prefix,iconName}=getCanonicalIcon(classArray(mutationRecord.target));mutationRecord.target.setAttribute("data-prefix",prefix||defaultPrefix),iconName&&mutationRecord.target.setAttribute("data-icon",iconName)}else(function hasBeenReplaced(node){return node&&node.classList&&node.classList.contains&&node.classList.contains(config.replacementClass)})(mutationRecord.target)&&nodeCallback(mutationRecord.target)}))})),IS_DOM&&mo.observe(observeMutationsRoot,{childList:!0,attributes:!0,characterData:!0,subtree:!0})}function classParser(node){const existingPrefix=node.getAttribute("data-prefix"),existingIconName=node.getAttribute("data-icon"),innerText=void 0!==node.innerText?node.innerText.trim():"";let val=getCanonicalIcon(classArray(node));return val.prefix||(val.prefix=getDefaultUsablePrefix()),existingPrefix&&existingIconName&&(val.prefix=existingPrefix,val.iconName=existingIconName),val.iconName&&val.prefix||(val.prefix&&innerText.length>0&&(val.iconName=function byLigature(prefix,ligature){return(_byLigature[prefix]||{})[ligature]}(val.prefix,node.innerText)||byUnicode(val.prefix,toHex(node.innerText))),!val.iconName&&config.autoFetchSvg&&node.firstChild&&node.firstChild.nodeType===Node.TEXT_NODE&&(val.iconName=node.firstChild.data)),val}function parseMeta(node){let parser=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{styleParser:!0};const{iconName,prefix,rest:extraClasses}=classParser(node),extraAttributes=function attributesParser(node){const extraAttributes=toArray(node.attributes).reduce(((acc,attr)=>("class"!==acc.name&&"style"!==acc.name&&(acc[attr.name]=attr.value),acc)),{}),title=node.getAttribute("title"),titleId=node.getAttribute("data-fa-title-id");return config.autoA11y&&(title?extraAttributes["aria-labelledby"]="".concat(config.replacementClass,"-title-").concat(titleId||nextUniqueId()):(extraAttributes["aria-hidden"]="true",extraAttributes.focusable="false")),extraAttributes}(node),pluginMeta=chainHooks("parseNodeAttributes",{},node);let extraStyles=parser.styleParser?function styleParser(node){const style=node.getAttribute("style");let val=[];return style&&(val=style.split(";").reduce(((acc,style)=>{const styles=style.split(":"),prop=styles[0],value=styles.slice(1);return prop&&value.length>0&&(acc[prop]=value.join(":").trim()),acc}),{})),val}(node):[];return _objectSpread2({iconName,title:node.getAttribute("title"),titleId:node.getAttribute("data-fa-title-id"),prefix,transform:meaninglessTransform,mask:{iconName:null,prefix:null,rest:[]},maskId:null,symbol:!1,extra:{classes:extraClasses,styles:extraStyles,attributes:extraAttributes}},pluginMeta)}const{styles:styles$2}=namespace;function generateMutation(node){const nodeMeta="nest"===config.autoReplaceSvg?parseMeta(node,{styleParser:!1}):parseMeta(node);return~nodeMeta.extra.classes.indexOf("fa-layers-text")?callProvided("generateLayersText",node,nodeMeta):callProvided("generateSvgReplacementMutation",node,nodeMeta)}function onTree(root){let callback=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(!IS_DOM)return Promise.resolve();const htmlClassList=DOCUMENT.documentElement.classList,hclAdd=suffix=>htmlClassList.add("".concat("fontawesome-i2svg","-").concat(suffix)),hclRemove=suffix=>htmlClassList.remove("".concat("fontawesome-i2svg","-").concat(suffix)),prefixes=config.autoFetchSvg?function getKnownPrefixes(){return[...Ft,...Ia]}():P.concat(Object.keys(styles$2));prefixes.includes("fa")||prefixes.push("fa");const prefixesDomQuery=[".".concat("fa-layers-text",":not([").concat("data-fa-i2svg","])")].concat(prefixes.map((p$$1=>".".concat(p$$1,":not([").concat("data-fa-i2svg","])")))).join(", ");if(0===prefixesDomQuery.length)return Promise.resolve();let candidates=[];try{candidates=toArray(root.querySelectorAll(prefixesDomQuery))}catch(e$$1){}if(!(candidates.length>0))return Promise.resolve();hclAdd("pending"),hclRemove("complete");const mark=perf_begin("onTree"),mutations=candidates.reduce(((acc,node)=>{try{const mutation=generateMutation(node);mutation&&acc.push(mutation)}catch(e$$1){PRODUCTION||"MissingIcon"===e$$1.name&&console.error(e$$1)}return acc}),[]);return new Promise(((resolve,reject)=>{Promise.all(mutations).then((resolvedMutations=>{perform(resolvedMutations,(()=>{hclAdd("active"),hclAdd("complete"),hclRemove("pending"),"function"==typeof callback&&callback(),mark(),resolve()}))})).catch((e$$1=>{mark(),reject(e$$1)}))}))}function onNode(node){let callback=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;generateMutation(node).then((mutation=>{mutation&&perform([mutation],callback)}))}const render=function(iconDefinition){let params=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{transform=meaninglessTransform,symbol=!1,mask=null,maskId=null,title=null,titleId=null,classes=[],attributes={},styles={}}=params;if(!iconDefinition)return;const{prefix,iconName,icon}=iconDefinition;return domVariants(_objectSpread2({type:"icon"},iconDefinition),(()=>(callHooks("beforeDOMElementCreation",{iconDefinition,params}),config.autoA11y&&(title?attributes["aria-labelledby"]="".concat(config.replacementClass,"-title-").concat(titleId||nextUniqueId()):(attributes["aria-hidden"]="true",attributes.focusable="false")),makeInlineSvgAbstract({icons:{main:asFoundIcon(icon),mask:mask?asFoundIcon(mask.icon):{found:!1,width:null,height:null,icon:{}}},prefix,iconName,transform:_objectSpread2(_objectSpread2({},meaninglessTransform),transform),symbol,title,maskId,titleId,extra:{attributes,styles,classes}}))))};var ReplaceElements={mixout(){return{icon:(next=render,function(maybeIconDefinition){let params=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const iconDefinition=(maybeIconDefinition||{}).icon?maybeIconDefinition:findIconDefinition(maybeIconDefinition||{});let{mask}=params;return mask&&(mask=(mask||{}).icon?mask:findIconDefinition(mask||{})),next(iconDefinition,_objectSpread2(_objectSpread2({},params),{},{mask}))})};var next},hooks:()=>({mutationObserverCallbacks:accumulator=>(accumulator.treeCallback=onTree,accumulator.nodeCallback=onNode,accumulator)}),provides(providers$$1){providers$$1.i2svg=function(params){const{node=DOCUMENT,callback=()=>{}}=params;return onTree(node,callback)},providers$$1.generateSvgReplacementMutation=function(node,nodeMeta){const{iconName,title,titleId,prefix,transform,symbol,mask,maskId,extra}=nodeMeta;return new Promise(((resolve,reject)=>{Promise.all([findIcon(iconName,prefix),mask.iconName?findIcon(mask.iconName,mask.prefix):Promise.resolve({found:!1,width:512,height:512,icon:{}})]).then((_ref=>{let[main,mask]=_ref;resolve([node,makeInlineSvgAbstract({icons:{main,mask},prefix,iconName,transform,symbol,maskId,title,titleId,extra,watchable:!0})])})).catch(reject)}))},providers$$1.generateAbstractIcon=function(_ref2){let{children,attributes,main,transform,styles}=_ref2;const styleString=joinStyles(styles);let nextChild;return styleString.length>0&&(attributes.style=styleString),transformIsMeaningful(transform)&&(nextChild=callProvided("generateAbstractTransformGrouping",{main,transform,containerWidth:main.width,iconWidth:main.width})),children.push(nextChild||main.icon),{children,attributes}}}},Layers={mixout:()=>({layer(assembler){let params=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{classes=[]}=params;return domVariants({type:"layer"},(()=>{callHooks("beforeDOMElementCreation",{assembler,params});let children=[];return assembler((args=>{Array.isArray(args)?args.map((a=>{children=children.concat(a.abstract)})):children=children.concat(args.abstract)})),[{tag:"span",attributes:{class:["".concat(config.cssPrefix,"-layers"),...classes].join(" ")},children}]}))}})},LayersCounter={mixout:()=>({counter(content){let params=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{title=null,classes=[],attributes={},styles={}}=params;return domVariants({type:"counter",content},(()=>(callHooks("beforeDOMElementCreation",{content,params}),function makeLayersCounterAbstract(params){const{content,title,extra}=params,attributes=_objectSpread2(_objectSpread2(_objectSpread2({},extra.attributes),title?{title}:{}),{},{class:extra.classes.join(" ")}),styleString=joinStyles(extra.styles);styleString.length>0&&(attributes.style=styleString);const val=[];return val.push({tag:"span",attributes,children:[content]}),title&&val.push({tag:"span",attributes:{class:"sr-only"},children:[title]}),val}({content:content.toString(),title,extra:{attributes,styles,classes:["".concat(config.cssPrefix,"-layers-counter"),...classes]}}))))}})},LayersText={mixout:()=>({text(content){let params=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{transform=meaninglessTransform,title=null,classes=[],attributes={},styles={}}=params;return domVariants({type:"text",content},(()=>(callHooks("beforeDOMElementCreation",{content,params}),makeLayersTextAbstract({content,transform:_objectSpread2(_objectSpread2({},meaninglessTransform),transform),title,extra:{attributes,styles,classes:["".concat(config.cssPrefix,"-layers-text"),...classes]}}))))}}),provides(providers$$1){providers$$1.generateLayersText=function(node,nodeMeta){const{title,transform,extra}=nodeMeta;let width=null,height=null;if(IS_IE){const computedFontSize=parseInt(getComputedStyle(node).fontSize,10),boundingClientRect=node.getBoundingClientRect();width=boundingClientRect.width/computedFontSize,height=boundingClientRect.height/computedFontSize}return config.autoA11y&&!title&&(extra.attributes["aria-hidden"]="true"),Promise.resolve([node,makeLayersTextAbstract({content:node.innerHTML,width,height,transform,title,extra,watchable:!0})])}}};const CLEAN_CONTENT_PATTERN=new RegExp('"',"ug"),SECONDARY_UNICODE_RANGE=[1105920,1112319],_FONT_FAMILY_WEIGHT_TO_PREFIX=_objectSpread2(_objectSpread2(_objectSpread2(_objectSpread2({},{FontAwesome:{normal:"fas",400:"fas"}}),{"Font Awesome 6 Free":{900:"fas",400:"far"},"Font Awesome 6 Pro":{900:"fas",400:"far",normal:"far",300:"fal",100:"fat"},"Font Awesome 6 Brands":{400:"fab",normal:"fab"},"Font Awesome 6 Duotone":{900:"fad",400:"fadr",normal:"fadr",300:"fadl",100:"fadt"},"Font Awesome 6 Sharp":{900:"fass",400:"fasr",normal:"fasr",300:"fasl",100:"fast"},"Font Awesome 6 Sharp Duotone":{900:"fasds",400:"fasdr",normal:"fasdr",300:"fasdl",100:"fasdt"}}),{"Font Awesome 5 Free":{900:"fas",400:"far"},"Font Awesome 5 Pro":{900:"fas",400:"far",normal:"far",300:"fal"},"Font Awesome 5 Brands":{400:"fab",normal:"fab"},"Font Awesome 5 Duotone":{900:"fad"}}),{"Font Awesome Kit":{400:"fak",normal:"fak"},"Font Awesome Kit Duotone":{400:"fakd",normal:"fakd"}}),FONT_FAMILY_WEIGHT_TO_PREFIX=Object.keys(_FONT_FAMILY_WEIGHT_TO_PREFIX).reduce(((acc,key)=>(acc[key.toLowerCase()]=_FONT_FAMILY_WEIGHT_TO_PREFIX[key],acc)),{}),FONT_FAMILY_WEIGHT_FALLBACK=Object.keys(FONT_FAMILY_WEIGHT_TO_PREFIX).reduce(((acc,fontFamily)=>{const weights=FONT_FAMILY_WEIGHT_TO_PREFIX[fontFamily];return acc[fontFamily]=weights[900]||[...Object.entries(weights)][0][1],acc}),{});function replaceForPosition(node,position){const pendingAttribute="".concat("data-fa-pseudo-element-pending").concat(position.replace(":","-"));return new Promise(((resolve,reject)=>{if(null!==node.getAttribute(pendingAttribute))return resolve();const alreadyProcessedPseudoElement=toArray(node.children).filter((c$$1=>c$$1.getAttribute("data-fa-pseudo-element")===position))[0],styles=WINDOW.getComputedStyle(node,position),fontFamily=styles.getPropertyValue("font-family"),fontFamilyMatch=fontFamily.match(FONT_FAMILY_PATTERN),fontWeight=styles.getPropertyValue("font-weight"),content=styles.getPropertyValue("content");if(alreadyProcessedPseudoElement&&!fontFamilyMatch)return node.removeChild(alreadyProcessedPseudoElement),resolve();if(fontFamilyMatch&&"none"!==content&&""!==content){const content=styles.getPropertyValue("content");let prefix=function getPrefix(fontFamily,fontWeight){const fontFamilySanitized=fontFamily.replace(/^['"]|['"]$/g,"").toLowerCase(),fontWeightInteger=parseInt(fontWeight),fontWeightSanitized=isNaN(fontWeightInteger)?"normal":fontWeightInteger;return(FONT_FAMILY_WEIGHT_TO_PREFIX[fontFamilySanitized]||{})[fontWeightSanitized]||FONT_FAMILY_WEIGHT_FALLBACK[fontFamilySanitized]}(fontFamily,fontWeight);const{value:hexValue,isSecondary}=function hexValueFromContent(content){const cleaned=content.replace(CLEAN_CONTENT_PATTERN,""),codePoint=function codePointAt(string,index){const size=string.length;let second,first=string.charCodeAt(index);return first>=55296&&first<=56319&&size>index+1&&(second=string.charCodeAt(index+1),second>=56320&&second<=57343)?1024*(first-55296)+second-56320+65536:first}(cleaned,0),isPrependTen=codePoint>=SECONDARY_UNICODE_RANGE[0]&&codePoint<=SECONDARY_UNICODE_RANGE[1],isDoubled=2===cleaned.length&&cleaned[0]===cleaned[1];return{value:toHex(isDoubled?cleaned[0]:cleaned),isSecondary:isPrependTen||isDoubled}}(content),isV4=fontFamilyMatch[0].startsWith("FontAwesome");let iconName=byUnicode(prefix,hexValue),iconIdentifier=iconName;if(isV4){const iconName4=function byOldUnicode(unicode){const oldUnicode=_byOldUnicode[unicode],newUnicode=byUnicode("fas",unicode);return oldUnicode||(newUnicode?{prefix:"fas",iconName:newUnicode}:null)||{prefix:null,iconName:null}}(hexValue);iconName4.iconName&&iconName4.prefix&&(iconName=iconName4.iconName,prefix=iconName4.prefix)}if(!iconName||isSecondary||alreadyProcessedPseudoElement&&alreadyProcessedPseudoElement.getAttribute("data-prefix")===prefix&&alreadyProcessedPseudoElement.getAttribute("data-icon")===iconIdentifier)resolve();else{node.setAttribute(pendingAttribute,iconIdentifier),alreadyProcessedPseudoElement&&node.removeChild(alreadyProcessedPseudoElement);const meta=function blankMeta(){return{iconName:null,title:null,titleId:null,prefix:null,transform:meaninglessTransform,symbol:!1,mask:{iconName:null,prefix:null,rest:[]},maskId:null,extra:{classes:[],styles:{},attributes:{}}}}(),{extra}=meta;extra.attributes["data-fa-pseudo-element"]=position,findIcon(iconName,prefix).then((main=>{const abstract=makeInlineSvgAbstract(_objectSpread2(_objectSpread2({},meta),{},{icons:{main,mask:{prefix:null,iconName:null,rest:[]}},prefix,iconName:iconIdentifier,extra,watchable:!0})),element=DOCUMENT.createElementNS("http://www.w3.org/2000/svg","svg");"::before"===position?node.insertBefore(element,node.firstChild):node.appendChild(element),element.outerHTML=abstract.map((a$$1=>toHtml(a$$1))).join("\n"),node.removeAttribute(pendingAttribute),resolve()})).catch(reject)}}else resolve()}))}function replace(node){return Promise.all([replaceForPosition(node,"::before"),replaceForPosition(node,"::after")])}function processable(node){return!(node.parentNode===document.head||~TAGNAMES_TO_SKIP_FOR_PSEUDOELEMENTS.indexOf(node.tagName.toUpperCase())||node.getAttribute("data-fa-pseudo-element")||node.parentNode&&"svg"===node.parentNode.tagName)}function searchPseudoElements(root){if(IS_DOM)return new Promise(((resolve,reject)=>{const operations=toArray(root.querySelectorAll("*")).filter(processable).map(replace),end=perf_begin("searchPseudoElements");disableObservation(),Promise.all(operations).then((()=>{end(),enableObservation(),resolve()})).catch((()=>{end(),enableObservation(),reject()}))}))}var PseudoElements={hooks:()=>({mutationObserverCallbacks:accumulator=>(accumulator.pseudoElementsCallback=searchPseudoElements,accumulator)}),provides(providers){providers.pseudoElements2svg=function(params){const{node=DOCUMENT}=params;config.searchPseudoElements&&searchPseudoElements(node)}}};let _unwatched=!1;var MutationObserver$1={mixout:()=>({dom:{unwatch(){disableObservation(),_unwatched=!0}}}),hooks:()=>({bootstrap(){observe(chainHooks("mutationObserverCallbacks",{}))},noAuto(){!function disconnect(){mo&&mo.disconnect()}()},watch(params){const{observeMutationsRoot}=params;_unwatched?enableObservation():observe(chainHooks("mutationObserverCallbacks",{observeMutationsRoot}))}})};const parseTransformString=transformString=>transformString.toLowerCase().split(" ").reduce(((acc,n)=>{const parts=n.toLowerCase().split("-"),first=parts[0];let rest=parts.slice(1).join("-");if(first&&"h"===rest)return acc.flipX=!0,acc;if(first&&"v"===rest)return acc.flipY=!0,acc;if(rest=parseFloat(rest),isNaN(rest))return acc;switch(first){case"grow":acc.size=acc.size+rest;break;case"shrink":acc.size=acc.size-rest;break;case"left":acc.x=acc.x-rest;break;case"right":acc.x=acc.x+rest;break;case"up":acc.y=acc.y-rest;break;case"down":acc.y=acc.y+rest;break;case"rotate":acc.rotate=acc.rotate+rest}return acc}),{size:16,x:0,y:0,flipX:!1,flipY:!1,rotate:0});var PowerTransforms={mixout:()=>({parse:{transform:transformString=>parseTransformString(transformString)}}),hooks:()=>({parseNodeAttributes(accumulator,node){const transformString=node.getAttribute("data-fa-transform");return transformString&&(accumulator.transform=parseTransformString(transformString)),accumulator}}),provides(providers){providers.generateAbstractTransformGrouping=function(_ref){let{main,transform,containerWidth,iconWidth}=_ref;const outer={transform:"translate(".concat(containerWidth/2," 256)")},innerTranslate="translate(".concat(32*transform.x,", ").concat(32*transform.y,") "),innerScale="scale(".concat(transform.size/16*(transform.flipX?-1:1),", ").concat(transform.size/16*(transform.flipY?-1:1),") "),innerRotate="rotate(".concat(transform.rotate," 0 0)"),operations={outer,inner:{transform:"".concat(innerTranslate," ").concat(innerScale," ").concat(innerRotate)},path:{transform:"translate(".concat(iconWidth/2*-1," -256)")}};return{tag:"g",attributes:_objectSpread2({},operations.outer),children:[{tag:"g",attributes:_objectSpread2({},operations.inner),children:[{tag:main.icon.tag,children:main.icon.children,attributes:_objectSpread2(_objectSpread2({},main.icon.attributes),operations.path)}]}]}}}};const ALL_SPACE={x:0,y:0,width:"100%",height:"100%"};function fillBlack(abstract){let force=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];return abstract.attributes&&(abstract.attributes.fill||force)&&(abstract.attributes.fill="black"),abstract}var Masks={hooks:()=>({parseNodeAttributes(accumulator,node){const maskData=node.getAttribute("data-fa-mask"),mask=maskData?getCanonicalIcon(maskData.split(" ").map((i=>i.trim()))):{prefix:null,iconName:null,rest:[]};return mask.prefix||(mask.prefix=getDefaultUsablePrefix()),accumulator.mask=mask,accumulator.maskId=node.getAttribute("data-fa-mask-id"),accumulator}}),provides(providers){providers.generateAbstractMask=function(_ref){let{children,attributes,main,mask,maskId:explicitMaskId,transform}=_ref;const{width:mainWidth,icon:mainPath}=main,{width:maskWidth,icon:maskPath}=mask,trans=function transformForSvg(_ref){let{transform,containerWidth,iconWidth}=_ref;const outer={transform:"translate(".concat(containerWidth/2," 256)")},innerTranslate="translate(".concat(32*transform.x,", ").concat(32*transform.y,") "),innerScale="scale(".concat(transform.size/16*(transform.flipX?-1:1),", ").concat(transform.size/16*(transform.flipY?-1:1),") "),innerRotate="rotate(".concat(transform.rotate," 0 0)");return{outer,inner:{transform:"".concat(innerTranslate," ").concat(innerScale," ").concat(innerRotate)},path:{transform:"translate(".concat(iconWidth/2*-1," -256)")}}}({transform,containerWidth:maskWidth,iconWidth:mainWidth}),maskRect={tag:"rect",attributes:_objectSpread2(_objectSpread2({},ALL_SPACE),{},{fill:"white"})},maskInnerGroupChildrenMixin=mainPath.children?{children:mainPath.children.map(fillBlack)}:{},maskInnerGroup={tag:"g",attributes:_objectSpread2({},trans.inner),children:[fillBlack(_objectSpread2({tag:mainPath.tag,attributes:_objectSpread2(_objectSpread2({},mainPath.attributes),trans.path)},maskInnerGroupChildrenMixin))]},maskOuterGroup={tag:"g",attributes:_objectSpread2({},trans.outer),children:[maskInnerGroup]},maskId="mask-".concat(explicitMaskId||nextUniqueId()),clipId="clip-".concat(explicitMaskId||nextUniqueId()),maskTag={tag:"mask",attributes:_objectSpread2(_objectSpread2({},ALL_SPACE),{},{id:maskId,maskUnits:"userSpaceOnUse",maskContentUnits:"userSpaceOnUse"}),children:[maskRect,maskOuterGroup]},defs={tag:"defs",children:[{tag:"clipPath",attributes:{id:clipId},children:(abstract=maskPath,"g"===abstract.tag?abstract.children:[abstract])},maskTag]};var abstract;return children.push(defs,{tag:"rect",attributes:_objectSpread2({fill:"currentColor","clip-path":"url(#".concat(clipId,")"),mask:"url(#".concat(maskId,")")},ALL_SPACE)}),{children,attributes}}}},MissingIconIndicator={provides(providers){let reduceMotion=!1;WINDOW.matchMedia&&(reduceMotion=WINDOW.matchMedia("(prefers-reduced-motion: reduce)").matches),providers.missingIconAbstract=function(){const gChildren=[],FILL={fill:"currentColor"},ANIMATION_BASE={attributeType:"XML",repeatCount:"indefinite",dur:"2s"};gChildren.push({tag:"path",attributes:_objectSpread2(_objectSpread2({},FILL),{},{d:"M156.5,447.7l-12.6,29.5c-18.7-9.5-35.9-21.2-51.5-34.9l22.7-22.7C127.6,430.5,141.5,440,156.5,447.7z M40.6,272H8.5 c1.4,21.2,5.4,41.7,11.7,61.1L50,321.2C45.1,305.5,41.8,289,40.6,272z M40.6,240c1.4-18.8,5.2-37,11.1-54.1l-29.5-12.6 C14.7,194.3,10,216.7,8.5,240H40.6z M64.3,156.5c7.8-14.9,17.2-28.8,28.1-41.5L69.7,92.3c-13.7,15.6-25.5,32.8-34.9,51.5 L64.3,156.5z M397,419.6c-13.9,12-29.4,22.3-46.1,30.4l11.9,29.8c20.7-9.9,39.8-22.6,56.9-37.6L397,419.6z M115,92.4 c13.9-12,29.4-22.3,46.1-30.4l-11.9-29.8c-20.7,9.9-39.8,22.6-56.8,37.6L115,92.4z M447.7,355.5c-7.8,14.9-17.2,28.8-28.1,41.5 l22.7,22.7c13.7-15.6,25.5-32.9,34.9-51.5L447.7,355.5z M471.4,272c-1.4,18.8-5.2,37-11.1,54.1l29.5,12.6 c7.5-21.1,12.2-43.5,13.6-66.8H471.4z M321.2,462c-15.7,5-32.2,8.2-49.2,9.4v32.1c21.2-1.4,41.7-5.4,61.1-11.7L321.2,462z M240,471.4c-18.8-1.4-37-5.2-54.1-11.1l-12.6,29.5c21.1,7.5,43.5,12.2,66.8,13.6V471.4z M462,190.8c5,15.7,8.2,32.2,9.4,49.2h32.1 c-1.4-21.2-5.4-41.7-11.7-61.1L462,190.8z M92.4,397c-12-13.9-22.3-29.4-30.4-46.1l-29.8,11.9c9.9,20.7,22.6,39.8,37.6,56.9 L92.4,397z M272,40.6c18.8,1.4,36.9,5.2,54.1,11.1l12.6-29.5C317.7,14.7,295.3,10,272,8.5V40.6z M190.8,50 c15.7-5,32.2-8.2,49.2-9.4V8.5c-21.2,1.4-41.7,5.4-61.1,11.7L190.8,50z M442.3,92.3L419.6,115c12,13.9,22.3,29.4,30.5,46.1 l29.8-11.9C470,128.5,457.3,109.4,442.3,92.3z M397,92.4l22.7-22.7c-15.6-13.7-32.8-25.5-51.5-34.9l-12.6,29.5 C370.4,72.1,384.4,81.5,397,92.4z"})});const OPACITY_ANIMATE=_objectSpread2(_objectSpread2({},ANIMATION_BASE),{},{attributeName:"opacity"}),dot={tag:"circle",attributes:_objectSpread2(_objectSpread2({},FILL),{},{cx:"256",cy:"364",r:"28"}),children:[]};return reduceMotion||dot.children.push({tag:"animate",attributes:_objectSpread2(_objectSpread2({},ANIMATION_BASE),{},{attributeName:"r",values:"28;14;28;28;14;28;"})},{tag:"animate",attributes:_objectSpread2(_objectSpread2({},OPACITY_ANIMATE),{},{values:"1;0;1;1;0;1;"})}),gChildren.push(dot),gChildren.push({tag:"path",attributes:_objectSpread2(_objectSpread2({},FILL),{},{opacity:"1",d:"M263.7,312h-16c-6.6,0-12-5.4-12-12c0-71,77.4-63.9,77.4-107.8c0-20-17.8-40.2-57.4-40.2c-29.1,0-44.3,9.6-59.2,28.7 c-3.9,5-11.1,6-16.2,2.4l-13.1-9.2c-5.6-3.9-6.9-11.8-2.6-17.2c21.2-27.2,46.4-44.7,91.2-44.7c52.3,0,97.4,29.8,97.4,80.2 c0,67.6-77.4,63.5-77.4,107.8C275.7,306.6,270.3,312,263.7,312z"}),children:reduceMotion?[]:[{tag:"animate",attributes:_objectSpread2(_objectSpread2({},OPACITY_ANIMATE),{},{values:"1;0;0;0;0;1;"})}]}),reduceMotion||gChildren.push({tag:"path",attributes:_objectSpread2(_objectSpread2({},FILL),{},{opacity:"0",d:"M232.5,134.5l7,168c0.3,6.4,5.6,11.5,12,11.5h9c6.4,0,11.7-5.1,12-11.5l7-168c0.3-6.8-5.2-12.5-12-12.5h-23 C237.7,122,232.2,127.7,232.5,134.5z"}),children:[{tag:"animate",attributes:_objectSpread2(_objectSpread2({},OPACITY_ANIMATE),{},{values:"0;0;1;1;0;0;"})}]}),{tag:"g",attributes:{class:"missing"},children:gChildren}}}};!function registerPlugins(nextPlugins,_ref){let{mixoutsTo:obj}=_ref;return _plugins=nextPlugins,_hooks={},Object.keys(providers).forEach((k=>{-1===defaultProviderKeys.indexOf(k)&&delete providers[k]})),_plugins.forEach((plugin=>{const mixout=plugin.mixout?plugin.mixout():{};if(Object.keys(mixout).forEach((tk=>{"function"==typeof mixout[tk]&&(obj[tk]=mixout[tk]),"object"==typeof mixout[tk]&&Object.keys(mixout[tk]).forEach((sk=>{obj[tk]||(obj[tk]={}),obj[tk][sk]=mixout[tk][sk]}))})),plugin.hooks){const hooks=plugin.hooks();Object.keys(hooks).forEach((hook=>{_hooks[hook]||(_hooks[hook]=[]),_hooks[hook].push(hooks[hook])}))}plugin.provides&&plugin.provides(providers)})),obj}([InjectCSS,ReplaceElements,Layers,LayersCounter,LayersText,PseudoElements,MutationObserver$1,PowerTransforms,Masks,MissingIconIndicator,{hooks:()=>({parseNodeAttributes(accumulator,node){const symbolData=node.getAttribute("data-fa-symbol"),symbol=null!==symbolData&&(""===symbolData||symbolData);return accumulator.symbol=symbol,accumulator}})}],{mixoutsTo:api});const parse$1=api.parse,icon=api.icon;var prop_types=__webpack_require__("./node_modules/prop-types/index.js"),prop_types_default=__webpack_require__.n(prop_types),react=__webpack_require__("./node_modules/react/index.js");function index_es_ownKeys(object,enumerableOnly){var keys=Object.keys(object);if(Object.getOwnPropertySymbols){var symbols=Object.getOwnPropertySymbols(object);enumerableOnly&&(symbols=symbols.filter((function(sym){return Object.getOwnPropertyDescriptor(object,sym).enumerable}))),keys.push.apply(keys,symbols)}return keys}function index_es_objectSpread2(target){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?index_es_ownKeys(Object(source),!0).forEach((function(key){index_es_defineProperty(target,key,source[key])})):Object.getOwnPropertyDescriptors?Object.defineProperties(target,Object.getOwnPropertyDescriptors(source)):index_es_ownKeys(Object(source)).forEach((function(key){Object.defineProperty(target,key,Object.getOwnPropertyDescriptor(source,key))}))}return target}function _typeof(obj){return _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(obj){return typeof obj}:function(obj){return obj&&"function"==typeof Symbol&&obj.constructor===Symbol&&obj!==Symbol.prototype?"symbol":typeof obj},_typeof(obj)}function index_es_defineProperty(obj,key,value){return key in obj?Object.defineProperty(obj,key,{value,enumerable:!0,configurable:!0,writable:!0}):obj[key]=value,obj}function _objectWithoutProperties(source,excluded){if(null==source)return{};var key,i,target=function _objectWithoutPropertiesLoose(source,excluded){if(null==source)return{};var key,i,target={},sourceKeys=Object.keys(source);for(i=0;i<sourceKeys.length;i++)key=sourceKeys[i],excluded.indexOf(key)>=0||(target[key]=source[key]);return target}(source,excluded);if(Object.getOwnPropertySymbols){var sourceSymbolKeys=Object.getOwnPropertySymbols(source);for(i=0;i<sourceSymbolKeys.length;i++)key=sourceSymbolKeys[i],excluded.indexOf(key)>=0||Object.prototype.propertyIsEnumerable.call(source,key)&&(target[key]=source[key])}return target}function _toConsumableArray(arr){return function _arrayWithoutHoles(arr){if(Array.isArray(arr))return _arrayLikeToArray(arr)}(arr)||function _iterableToArray(iter){if("undefined"!=typeof Symbol&&null!=iter[Symbol.iterator]||null!=iter["@@iterator"])return Array.from(iter)}(arr)||function _unsupportedIterableToArray(o,minLen){if(!o)return;if("string"==typeof o)return _arrayLikeToArray(o,minLen);var n=Object.prototype.toString.call(o).slice(8,-1);"Object"===n&&o.constructor&&(n=o.constructor.name);if("Map"===n||"Set"===n)return Array.from(o);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return _arrayLikeToArray(o,minLen)}(arr)||function _nonIterableSpread(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function _arrayLikeToArray(arr,len){(null==len||len>arr.length)&&(len=arr.length);for(var i=0,arr2=new Array(len);i<len;i++)arr2[i]=arr[i];return arr2}function camelize(string){return function _isNumerical(obj){return(obj-=0)==obj}(string)?string:(string=string.replace(/[\-_\s]+(.)?/g,(function(match,chr){return chr?chr.toUpperCase():""}))).substr(0,1).toLowerCase()+string.substr(1)}var _excluded=["style"];var index_es_PRODUCTION=!1;try{index_es_PRODUCTION=!0}catch(e){}function normalizeIconArgs(icon){return icon&&"object"===_typeof(icon)&&icon.prefix&&icon.iconName&&icon.icon?icon:parse$1.icon?parse$1.icon(icon):null===icon?null:icon&&"object"===_typeof(icon)&&icon.prefix&&icon.iconName?icon:Array.isArray(icon)&&2===icon.length?{prefix:icon[0],iconName:icon[1]}:"string"==typeof icon?{prefix:"fas",iconName:icon}:void 0}function objectWithKey(key,value){return Array.isArray(value)&&value.length>0||!Array.isArray(value)&&value?index_es_defineProperty({},key,value):{}}var defaultProps={border:!1,className:"",mask:null,maskId:null,fixedWidth:!1,inverse:!1,flip:!1,icon:null,listItem:!1,pull:null,pulse:!1,rotation:null,size:null,spin:!1,spinPulse:!1,spinReverse:!1,beat:!1,fade:!1,beatFade:!1,bounce:!1,shake:!1,symbol:!1,title:"",titleId:null,transform:null,swapOpacity:!1},FontAwesomeIcon=react.forwardRef((function(props,ref){var allProps=index_es_objectSpread2(index_es_objectSpread2({},defaultProps),props),iconArgs=allProps.icon,maskArgs=allProps.mask,symbol=allProps.symbol,className=allProps.className,title=allProps.title,titleId=allProps.titleId,maskId=allProps.maskId,iconLookup=normalizeIconArgs(iconArgs),classes=objectWithKey("classes",[].concat(_toConsumableArray(function classList(props){var _classes,beat=props.beat,fade=props.fade,beatFade=props.beatFade,bounce=props.bounce,shake=props.shake,flash=props.flash,spin=props.spin,spinPulse=props.spinPulse,spinReverse=props.spinReverse,pulse=props.pulse,fixedWidth=props.fixedWidth,inverse=props.inverse,border=props.border,listItem=props.listItem,flip=props.flip,size=props.size,rotation=props.rotation,pull=props.pull,classes=(index_es_defineProperty(_classes={"fa-beat":beat,"fa-fade":fade,"fa-beat-fade":beatFade,"fa-bounce":bounce,"fa-shake":shake,"fa-flash":flash,"fa-spin":spin,"fa-spin-reverse":spinReverse,"fa-spin-pulse":spinPulse,"fa-pulse":pulse,"fa-fw":fixedWidth,"fa-inverse":inverse,"fa-border":border,"fa-li":listItem,"fa-flip":!0===flip,"fa-flip-horizontal":"horizontal"===flip||"both"===flip,"fa-flip-vertical":"vertical"===flip||"both"===flip},"fa-".concat(size),null!=size),index_es_defineProperty(_classes,"fa-rotate-".concat(rotation),null!=rotation&&0!==rotation),index_es_defineProperty(_classes,"fa-pull-".concat(pull),null!=pull),index_es_defineProperty(_classes,"fa-swap-opacity",props.swapOpacity),_classes);return Object.keys(classes).map((function(key){return classes[key]?key:null})).filter((function(key){return key}))}(allProps)),_toConsumableArray((className||"").split(" ")))),transform=objectWithKey("transform","string"==typeof allProps.transform?parse$1.transform(allProps.transform):allProps.transform),mask=objectWithKey("mask",normalizeIconArgs(maskArgs)),renderedIcon=icon(iconLookup,index_es_objectSpread2(index_es_objectSpread2(index_es_objectSpread2(index_es_objectSpread2({},classes),transform),mask),{},{symbol,title,titleId,maskId}));if(!renderedIcon)return function log(){var _console;!index_es_PRODUCTION&&console&&"function"==typeof console.error&&(_console=console).error.apply(_console,arguments)}("Could not find icon",iconLookup),null;var abstract=renderedIcon.abstract,extraProps={ref};return Object.keys(allProps).forEach((function(key){defaultProps.hasOwnProperty(key)||(extraProps[key]=allProps[key])})),convertCurry(abstract[0],extraProps)}));FontAwesomeIcon.displayName="FontAwesomeIcon",FontAwesomeIcon.propTypes={beat:prop_types_default().bool,border:prop_types_default().bool,beatFade:prop_types_default().bool,bounce:prop_types_default().bool,className:prop_types_default().string,fade:prop_types_default().bool,flash:prop_types_default().bool,mask:prop_types_default().oneOfType([prop_types_default().object,prop_types_default().array,prop_types_default().string]),maskId:prop_types_default().string,fixedWidth:prop_types_default().bool,inverse:prop_types_default().bool,flip:prop_types_default().oneOf([!0,!1,"horizontal","vertical","both"]),icon:prop_types_default().oneOfType([prop_types_default().object,prop_types_default().array,prop_types_default().string]),listItem:prop_types_default().bool,pull:prop_types_default().oneOf(["right","left"]),pulse:prop_types_default().bool,rotation:prop_types_default().oneOf([0,90,180,270]),shake:prop_types_default().bool,size:prop_types_default().oneOf(["2xs","xs","sm","lg","xl","2xl","1x","2x","3x","4x","5x","6x","7x","8x","9x","10x"]),spin:prop_types_default().bool,spinPulse:prop_types_default().bool,spinReverse:prop_types_default().bool,symbol:prop_types_default().oneOfType([prop_types_default().bool,prop_types_default().string]),title:prop_types_default().string,titleId:prop_types_default().string,transform:prop_types_default().oneOfType([prop_types_default().string,prop_types_default().object]),swapOpacity:prop_types_default().bool};var convertCurry=function convert(createElement,element){var extraProps=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if("string"==typeof element)return element;var children=(element.children||[]).map((function(child){return convert(createElement,child)})),mixins=Object.keys(element.attributes||{}).reduce((function(acc,key){var val=element.attributes[key];switch(key){case"class":acc.attrs.className=val,delete element.attributes.class;break;case"style":acc.attrs.style=function styleToObject(style){return style.split(";").map((function(s){return s.trim()})).filter((function(s){return s})).reduce((function(acc,pair){var i=pair.indexOf(":"),prop=camelize(pair.slice(0,i)),value=pair.slice(i+1).trim();return prop.startsWith("webkit")?acc[function capitalize(val){return val.charAt(0).toUpperCase()+val.slice(1)}(prop)]=value:acc[prop]=value,acc}),{})}(val);break;default:0===key.indexOf("aria-")||0===key.indexOf("data-")?acc.attrs[key.toLowerCase()]=val:acc.attrs[camelize(key)]=val}return acc}),{attrs:{}}),_extraProps$style=extraProps.style,existingStyle=void 0===_extraProps$style?{}:_extraProps$style,remaining=_objectWithoutProperties(extraProps,_excluded);return mixins.attrs.style=index_es_objectSpread2(index_es_objectSpread2({},mixins.attrs.style),existingStyle),createElement.apply(void 0,[element.tag,index_es_objectSpread2(index_es_objectSpread2({},mixins.attrs),remaining)].concat(_toConsumableArray(children)))}.bind(null,react.createElement)},"./node_modules/@restart/ui/esm/DataKey.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.d(__webpack_exports__,{sE:()=>dataAttr});const ATTRIBUTE_PREFIX="data-rr-ui-";function dataAttr(property){return`${ATTRIBUTE_PREFIX}${property}`}},"./node_modules/@restart/ui/esm/useWindow.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.d(__webpack_exports__,{A:()=>useWindow});var react__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("./node_modules/react/index.js"),dom_helpers_canUseDOM__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__("./node_modules/dom-helpers/esm/canUseDOM.js");const Context=(0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(dom_helpers_canUseDOM__WEBPACK_IMPORTED_MODULE_1__.A?window:void 0);Context.Provider;function useWindow(){return(0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(Context)}},"./node_modules/dom-helpers/esm/contains.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{function contains(context,node){return context.contains?context.contains(node):context.compareDocumentPosition?context===node||!!(16&context.compareDocumentPosition(node)):void 0}__webpack_require__.d(__webpack_exports__,{A:()=>contains})},"./node_modules/dom-helpers/esm/querySelectorAll.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.d(__webpack_exports__,{A:()=>qsa});var toArray=Function.prototype.bind.call(Function.prototype.call,[].slice);function qsa(element,selector){return toArray(element.querySelectorAll(selector))}}}]);