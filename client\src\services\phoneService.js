import axiosInstance from '../api/axiosInstance';

/**
 * 手机服务
 */
class PhoneService {
  constructor() {
    this.baseURL = '/api/phone';
  }

  /**
   * 发起语音通话
   * @param {string} phoneNumber - 电话号码
   * @param {Object} options - 通话选项
   * @returns {Promise<Object>} 通话结果
   */
  async makeCall(phoneNumber, options = {}) {
    try {
      const response = await axiosInstance.post(`${this.baseURL}/call`, {
        phoneNumber,
        ...options
      });
      return response.data;
    } catch (error) {
      console.error('发起通话失败:', error.response?.data || error);
      throw error.response?.data || error;
    }
  }

  /**
   * 结束通话
   * @param {string} callId - 通话ID
   * @returns {Promise<Object>} 操作结果
   */
  async endCall(callId) {
    try {
      const response = await axiosInstance.post(`${this.baseURL}/call/${callId}/end`);
      return response.data;
    } catch (error) {
      console.error('结束通话失败:', error.response?.data || error);
      throw error.response?.data || error;
    }
  }

  /**
   * 暂停通话
   * @param {string} callId - 通话ID
   * @returns {Promise<Object>} 操作结果
   */
  async holdCall(callId) {
    try {
      const response = await axiosInstance.post(`${this.baseURL}/call/${callId}/hold`);
      return response.data;
    } catch (error) {
      console.error('暂停通话失败:', error.response?.data || error);
      throw error.response?.data || error;
    }
  }

  /**
   * 恢复通话
   * @param {string} callId - 通话ID
   * @returns {Promise<Object>} 操作结果
   */
  async resumeCall(callId) {
    try {
      const response = await axiosInstance.post(`${this.baseURL}/call/${callId}/resume`);
      return response.data;
    } catch (error) {
      console.error('恢复通话失败:', error.response?.data || error);
      throw error.response?.data || error;
    }
  }

  /**
   * 转接通话
   * @param {string} callId - 通话ID
   * @param {string} targetNumber - 目标号码
   * @returns {Promise<Object>} 操作结果
   */
  async transferCall(callId, targetNumber) {
    try {
      const response = await axiosInstance.post(`${this.baseURL}/call/${callId}/transfer`, {
        targetNumber
      });
      return response.data;
    } catch (error) {
      console.error('转接通话失败:', error.response?.data || error);
      throw error.response?.data || error;
    }
  }

  /**
   * 发送短信
   * @param {string} phoneNumber - 电话号码
   * @param {string} message - 短信内容
   * @param {Object} options - 短信选项
   * @returns {Promise<Object>} 发送结果
   */
  async sendSMS(phoneNumber, message, options = {}) {
    try {
      const response = await axiosInstance.post(`${this.baseURL}/sms`, {
        phoneNumber,
        message,
        ...options
      });
      return response.data;
    } catch (error) {
      console.error('发送短信失败:', error.response?.data || error);
      throw error.response?.data || error;
    }
  }

  /**
   * 获取通话记录
   * @param {Object} filters - 筛选条件
   * @returns {Promise<Array>} 通话记录列表
   */
  async getCallHistory(filters = {}) {
    try {
      const response = await axiosInstance.get(`${this.baseURL}/call-history`, {
        params: filters
      });
      return response.data;
    } catch (error) {
      console.error('获取通话记录失败:', error.response?.data || error);
      throw error.response?.data || error;
    }
  }

  /**
   * 获取短信记录
   * @param {Object} filters - 筛选条件
   * @returns {Promise<Array>} 短信记录列表
   */
  async getSMSHistory(filters = {}) {
    try {
      const response = await axiosInstance.get(`${this.baseURL}/sms-history`, {
        params: filters
      });
      return response.data;
    } catch (error) {
      console.error('获取短信记录失败:', error.response?.data || error);
      throw error.response?.data || error;
    }
  }

  /**
   * 获取电话配置
   * @returns {Promise<Object>} 电话配置
   */
  async getPhoneConfig() {
    try {
      const response = await axiosInstance.get(`${this.baseURL}/config`);
      return response.data;
    } catch (error) {
      console.error('获取电话配置失败:', error.response?.data || error);
      throw error.response?.data || error;
    }
  }

  /**
   * 更新电话配置
   * @param {Object} config - 配置数据
   * @returns {Promise<Object>} 更新结果
   */
  async updatePhoneConfig(config) {
    try {
      const response = await axiosInstance.put(`${this.baseURL}/config`, config);
      return response.data;
    } catch (error) {
      console.error('更新电话配置失败:', error.response?.data || error);
      throw error.response?.data || error;
    }
  }

  /**
   * 验证电话号码
   * @param {string} phoneNumber - 电话号码
   * @returns {Promise<Object>} 验证结果
   */
  async validatePhoneNumber(phoneNumber) {
    try {
      const response = await axiosInstance.post(`${this.baseURL}/validate`, {
        phoneNumber
      });
      return response.data;
    } catch (error) {
      console.error('验证电话号码失败:', error.response?.data || error);
      throw error.response?.data || error;
    }
  }

  /**
   * 获取通话状态
   * @param {string} callId - 通话ID
   * @returns {Promise<Object>} 通话状态
   */
  async getCallStatus(callId) {
    try {
      const response = await axiosInstance.get(`${this.baseURL}/call/${callId}/status`);
      return response.data;
    } catch (error) {
      console.error('获取通话状态失败:', error.response?.data || error);
      throw error.response?.data || error;
    }
  }

  /**
   * 录音开始
   * @param {string} callId - 通话ID
   * @returns {Promise<Object>} 操作结果
   */
  async startRecording(callId) {
    try {
      const response = await axiosInstance.post(`${this.baseURL}/call/${callId}/record/start`);
      return response.data;
    } catch (error) {
      console.error('开始录音失败:', error.response?.data || error);
      throw error.response?.data || error;
    }
  }

  /**
   * 停止录音
   * @param {string} callId - 通话ID
   * @returns {Promise<Object>} 操作结果
   */
  async stopRecording(callId) {
    try {
      const response = await axiosInstance.post(`${this.baseURL}/call/${callId}/record/stop`);
      return response.data;
    } catch (error) {
      console.error('停止录音失败:', error.response?.data || error);
      throw error.response?.data || error;
    }
  }

  /**
   * 获取录音文件
   * @param {string} callId - 通话ID
   * @returns {Promise<Object>} 录音文件信息
   */
  async getRecording(callId) {
    try {
      const response = await axiosInstance.get(`${this.baseURL}/call/${callId}/recording`);
      return response.data;
    } catch (error) {
      console.error('获取录音文件失败:', error.response?.data || error);
      throw error.response?.data || error;
    }
  }

  /**
   * 批量发送短信
   * @param {Array} phoneNumbers - 电话号码列表
   * @param {string} message - 短信内容
   * @param {Object} options - 短信选项
   * @returns {Promise<Object>} 发送结果
   */
  async sendBulkSMS(phoneNumbers, message, options = {}) {
    try {
      const response = await axiosInstance.post(`${this.baseURL}/sms/bulk`, {
        phoneNumbers,
        message,
        ...options
      });
      return response.data;
    } catch (error) {
      console.error('批量发送短信失败:', error.response?.data || error);
      throw error.response?.data || error;
    }
  }

  /**
   * 获取通话统计
   * @param {Object} filters - 筛选条件
   * @returns {Promise<Object>} 统计数据
   */
  async getCallStatistics(filters = {}) {
    try {
      const response = await axiosInstance.get(`${this.baseURL}/statistics/calls`, {
        params: filters
      });
      return response.data;
    } catch (error) {
      console.error('获取通话统计失败:', error.response?.data || error);
      throw error.response?.data || error;
    }
  }

  /**
   * 获取短信统计
   * @param {Object} filters - 筛选条件
   * @returns {Promise<Object>} 统计数据
   */
  async getSMSStatistics(filters = {}) {
    try {
      const response = await axiosInstance.get(`${this.baseURL}/statistics/sms`, {
        params: filters
      });
      return response.data;
    } catch (error) {
      console.error('获取短信统计失败:', error.response?.data || error);
      throw error.response?.data || error;
    }
  }
}

// 创建实例
const phoneService = new PhoneService();

export default phoneService; 