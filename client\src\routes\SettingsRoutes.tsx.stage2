import React from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { OnSiteBookingSettingsPage } from '../pages/settings/OnSiteBookingSettingsPage'
import { WalkinBookingSettingsPage } from '../pages/settings/WalkinBookingSettingsPage'

/**
 * 设置模块路由配置
 * 将所有设置相关页面集中管理，实现代码分割
 * 
 * 注意：Platform API 管理页面已迁移到主路由 /platform-api
 */
const SettingsRoutes = () => {
  return (
    <Routes>
      <Route index element={<Navigate to="/platform-api" replace />} />
      <Route path="platform-api" element={<Navigate to="/platform-api" replace />} />
      <Route path="onsite-booking" element={<OnSiteBookingSettingsPage />} />
      <Route path="walkin-booking" element={<WalkinBookingSettingsPage />} />
      <Route path="*" element={<Navigate to="/platform-api" replace />} />
    </Routes>;
  );
};

export default SettingsRoutes;