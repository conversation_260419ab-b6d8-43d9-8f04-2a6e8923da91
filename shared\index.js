/**
 * 共享模块主入口
 * 导出所有共享组件，以便各微服务使用
 */

// 导出配置
const config = require('./config');

// 导出日志工具
const logger = require('./utils/logger');

// 导出错误处理
const errorHandler = require('./middleware/errorHandler');

// 导出缓存服务
const CacheService = require('./services/cacheService');

// 导出消息队列服务
const MessageQueueService = require('./services/messageQueue');

// 导出Supabase客户端工具
const supabaseClient = require('./utils/supabaseClient');

// 导出所有组件
module.exports = {
  config,
  logger,
  errorHandler,
  CacheService,
  MessageQueueService,
  supabaseClient
}; 