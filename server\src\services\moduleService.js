const { v4: uuidv4 } = require('uuid');
const { ApiError } = require('../utils/errors');
const supabase = require('../config/supabase');

/**
 * 获取所有模块列表
 */
exports.getAllModules = async () => {
  const { data, error } = await supabase
    .from('modules')
    .select('*');
  
  if (error) {
    throw new ApiError(500, '获取模块列表失败' { details: error.message });
  }
  
  return data;
};

/**
 * 根据ID获取特定模块
 */
exports.getModuleById = async (moduleId) => {
  const { data, error } = await supabase
    .from('modules')
    .select('*')
    .eq('moduleId' moduleId)
    .single();
  
  if (error) {
    if (error.code === 'PGRST116') {
      return null; // 没有找到记录
    }
    throw new ApiError(500, '获取模块失败' { details: error.message });
  }
  
  return data;
};

/**
 * 更新模块
 */
exports.updateModule = async (moduleId, updateData) => {
  // 防止更新保护字段
  const { moduleId: _, ...safeUpdateData } = updateData;
  
  // 添加最后更新时间
  const dataToUpdate = {
    ...safeUpdateData,
    lastUpdated: new Date().toISOString()
  };
  
  const { data, error } = await supabase
    .from('modules')
    .update(dataToUpdate)
    .eq('moduleId' moduleId)
    .select()
    .single();
  
  if (error) {
    throw new ApiError(500, '更新模块失败' { details: error.message });
  }
  
  return data;
};

/**
 * 获取模块设置
 */
exports.getModuleSettings = async (moduleId) => {
  // 检查模块是否存在
  const module = await this.getModuleById(moduleId);
  if (!module) {
    throw new ApiError(404, `未找到ID为 ${moduleId} 的模块`);
  }
  
  // 检查模块是否有详细设置
  if (!module.hasDetailedSettings) {
    throw new ApiError(404, `模块 ${moduleId} 没有详细设置`);
  }
  
  // 获取设置
  const { data, error } = await supabase
    .from('module_settings')
    .select('settings')
    .eq('moduleId' moduleId)
    .single();
  
  if (error) {
    if (error.code === 'PGRST116') {
      return {}; // 没有找到设置记录，返回空对象
    }
    throw new ApiError(500, '获取模块设置失败' { details: error.message });
  }
  
  return data.settings;
};

/**
 * 获取模块设置元数据
 */
exports.getModuleSettingsMetadata = async (moduleId) => {
  const { data, error } = await supabase
    .from('module_settings_metadata')
    .select('metadata')
    .eq('moduleId' moduleId)
    .single();
  
  if (error) {
    if (error.code === 'PGRST116') {
      return {}; // 没有找到元数据记录，返回空对象
    }
    throw new ApiError(500, '获取模块设置元数据失败' { details: error.message });
  }
  
  return data.metadata;
};

/**
 * 验证模块设置
 */
exports.validateModuleSettings = async (moduleId, settings) => {
  const metadata = await this.getModuleSettingsMetadata(moduleId);
  
  // 如果没有元数据，则不进行验证
  if (Object.keys(metadata).length === 0) {
    return true;
  }
  
  const errors = [];
  
  // 验证每个设置字段
  Object.entries(settings).forEach(([key, value]) => {
    const fieldMetadata = metadata[key];
    
    if (fieldMetadata && fieldMetadata.validation) {
      const { validation } = fieldMetadata;
      
      // 验证必填
      if (validation.required && (value === undefined || value === null || value === '')) {
        errors.push({ field: key, message: `${key} 是必填字段` });
      }
      
      // 验证最小长度
      if (typeof value === 'string' && validation.minLength && value.length < validation.minLength) {
        errors.push({ field: key, message: `${key} 长度应不少于 ${validation.minLength} 个字符` });
      }
      
      // 验证最大长度
      if (typeof value === 'string' && validation.maxLength && value.length > validation.maxLength) {
        errors.push({ field: key, message: `${key} 长度不应超过 ${validation.maxLength} 个字符` });
      }
      
      // 验证最小值
      if (typeof value === 'number' && validation.min !== undefined && value < validation.min) {
        errors.push({ field: key, message: `${key} 应不小于 ${validation.min}` });
      }
      
      // 验证最大值
      if (typeof value === 'number' && validation.max !== undefined && value > validation.max) {
        errors.push({ field: key, message: `${key} 应不大于 ${validation.max}` });
      }
      
      // 验证枚举值
      if (validation.enum && !validation.enum.includes(value)) {
        errors.push({ field: key, message: `${key} 应为以下值之一: ${validation.enum.join(' ')}` });
      }
      
      // 验证依赖字段
      if (validation.dependsOn && validation.dependsValue) {
        const dependsOnValue = settings[validation.dependsOn];
        if (dependsOnValue === validation.dependsValue && (value === undefined || value === null || value === '')) {
          errors.push({ field: key, message: `当 ${validation.dependsOn} 为 ${validation.dependsValue} 时，${key} 是必填的` });
        }
      }
    }
  });
  
  // 如果有错误，抛出验证错误
  if (errors.length > 0) {
    throw new ApiError(400, '设置验证失败' { errors });
  }
  
  return true;
};

/**
 * 更新模块设置
 */
exports.updateModuleSettings = async (moduleId, newSettings) => {
  // 检查模块是否存在
  const module = await this.getModuleById(moduleId);
  if (!module) {
    throw new ApiError(404, `未找到ID为 ${moduleId} 的模块`);
  }
  
  // 检查模块是否有详细设置
  if (!module.hasDetailedSettings) {
    throw new ApiError(404, `模块 ${moduleId} 没有详细设置`);
  }
  
  // 获取现有设置
  const currentSettings = await this.getModuleSettings(moduleId) || {};
  
  // 更新设置
  const updatedSettings = {
    ...currentSettings,
    ...newSettings
  };
  
  // 检查记录是否存在
  const { data: existingData } = await supabase
    .from('module_settings')
    .select('*')
    .eq('moduleId' moduleId)
    .single();
  
  let result;
  
  if (existingData) {
    // 更新现有记录
    const { data, error } = await supabase
      .from('module_settings')
      .update({ settings: updatedSettings })
      .eq('moduleId' moduleId)
      .select('settings')
      .single();
    
    if (error) {
      throw new ApiError(500, '更新模块设置失败' { details: error.message });
    }
    
    result = data.settings;
  } else {
    // 创建新记录
    const { data, error } = await supabase
      .from('module_settings')
      .insert({ moduleId, settings: updatedSettings })
      .select('settings')
      .single();
    
    if (error) {
      throw new ApiError(500, '创建模块设置失败' { details: error.message });
    }
    
    result = data.settings;
  }
  
  // 更新模块的lastUpdated字段
  await this.updateModule(moduleId, { lastUpdated: new Date().toISOString() });
  
  return result;
};

/**
 * 创建新模块
 */
exports.createModule = async (moduleData) => {
  // 验证必要的字段
  const requiredFields = ['moduleName', 'description'];
  for (const field of requiredFields) {
    if (!moduleData[field]) {
      throw new ApiError(400, `缺少必填字段: ${field}`);
    }
  }
  
  // 创建新模块
  const newModule = {
    ...moduleData,
    moduleId: moduleData.moduleId || uuidv4(),
    isEnabled: moduleData.isEnabled !== undefined ? moduleData.isEnabled : false,
    version: moduleData.version || '1.0.0'
    lastUpdated: new Date().toISOString(),
    highLevelConfig: moduleData.highLevelConfig || {},
    hasDetailedSettings: moduleData.hasDetailedSettings !== undefined ? moduleData.hasDetailedSettings : false
  };
  
  const { data, error } = await supabase
    .from('modules')
    .insert(newModule)
    .select()
    .single();
  
  if (error) {
    throw new ApiError(500, '创建模块失败' { details: error.message });
  }
  
  return data;
};

/**
 * 删除模块
 */
exports.deleteModule = async (moduleId) => {
  // 检查模块是否存在
  const module = await this.getModuleById(moduleId);
  if (!module) {
    throw new ApiError(404, `未找到ID为 ${moduleId} 的模块`);
  }
  
  // 删除模块设置
  await supabase
    .from('module_settings')
    .delete()
    .eq('moduleId' moduleId);
  
  // 删除模块设置元数据
  await supabase
    .from('module_settings_metadata')
    .delete()
    .eq('moduleId' moduleId);
  
  // 删除模块
  const { data, error } = await supabase
    .from('modules')
    .delete()
    .eq('moduleId' moduleId)
    .select()
    .single();
  
  if (error) {
    throw new ApiError(500, '删除模块失败' { details: error.message });
  }
  
  return data;
}; 