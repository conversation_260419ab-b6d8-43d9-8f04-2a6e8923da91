import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';

interface PlatformConnectionGuideProps {
  isOpen: boolean;
  onClose: () => void;
}

export const PlatformConnectionGuide: React.FC<PlatformConnectionGuideProps> = ({ isOpen, onClose }) => {
  if (!isOpen) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center">
      <Card className="w-full max-w-lg">
        <CardHeader>
          <CardTitle>Platform Connection Guide</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-48 bg-gray-100 rounded-md">
            <p className="text-gray-500">Guide Placeholder</p>
          </div>
          <button onClick={onClose} className="mt-4">Close</button>
        </CardContent>
      </Card>
    </div>
  );
};