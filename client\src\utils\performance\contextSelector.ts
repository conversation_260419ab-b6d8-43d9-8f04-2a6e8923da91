import React, { createContext, useContext, ReactNode, useMemo } from 'react';

/**
 * 创建支持选择器的Context
 * 允许组件只订阅特定的状态片段，减少不必要的重渲染
 * @param initialValue 初始上下文值
 * @returns 包含Provider、选择器Hook和Context的对象
 */
export function createSelectiveContext<T extends Record<string, any>>(initialValue: T) {
  // 创建Context
  const Context = createContext<T>(initialValue);

  // 提供带选择器的上下文Hook
  function useContextSelector<S>(selector: (state: T) => S): S {
    // 获取整个上下文值
    const value = useContext(Context);
    
    // 使用选择器获取特定部分
    const selectedValue = selector(value);
    
    // 使用记忆化缓存选择器结果，只有选择的值发生变化时才更新
    // eslint-disable-next-line react-hooks/exhaustive-deps
    return useMemo(() => selectedValue, [JSON.stringify(selectedValue)]);
  }
  
  // 获取整个上下文值的Hook
  function useContextValue(): T {
    return useContext(Context);
  }
  
  // 创建Provider组件
  function Provider(props: { children: ReactNode; value: T }) {
    return React.createElement(
      Context.Provider,
      { value: props.value },
      props.children
    );
  }
  
  // 返回Context、Provider和Hooks
  return {
    Context,
    Provider,
    useContextSelector,
    useContextValue
  };
}

export default createSelectiveContext;

/**
 * 使用示例:
 * 
 * // 创建Context
 * const { Provider, useContextSelector, useContextValue, Context } = createSelectiveContext({
 *   count: 0,
 *   user: null,
 *   theme: 'light'
 * });
 * 
 * // 使用Provider
 * function App() {
 *   const [state, setState] = useState({ count: 0, user: null, theme: 'light' });
 *   return (
 *     <Provider value={state}>
 *       <Counter />
 *       <ThemeSelector />
 *     </Provider>
 *   );
 * }
 * 
 * // 使用选择器Hook - 只有count变化时才会重新渲染
 * function Counter() {
 *   const count = useContextSelector(state => state.count);
 *   return <div>{count}</div>;
 * }
 * 
 * // 使用选择器Hook - 只有theme变化时才会重新渲染
 * function ThemeSelector() {
 *   const theme = useContextSelector(state => state.theme);
 *   return <div>{theme}</div>;
 * }
 */ 