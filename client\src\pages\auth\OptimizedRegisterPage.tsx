import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import RegisterCard from '../../components/features/auth/RegisterCard';
import { Sparkles } from 'lucide-react';

export function OptimizedRegisterPage() {
  return (
    <div className="min-h-screen bg-gradient-hero flex items-center justify-center p-4">
      <Helmet>
        <title>Register - iTeraBiz</title>
        <meta name="description" content="Create your iTeraBiz account and start your AI-powered content creation journey." />
      </Helmet>

      <div className="w-full max-w-6xl flex items-center justify-center">
        {/* Left side - Branding */}
        <div className="hidden lg:flex lg:w-1/2 flex-col justify-center p-12">
          <div className="max-w-md">
            <Link to="/" className="flex items-center space-x-2 mb-8">
              <div className="w-10 h-10 bg-gradient-primary rounded-lg flex items-center justify-center">
                <Sparkles className="w-6 h-6 text-white" />
              </div>
              <span className="text-2xl font-bold text-text-primary">iTeraBiz</span>
            </Link>

            <h1 className="text-4xl font-bold text-text-primary mb-6">
              Join the content creation revolution
            </h1>

            <p className="text-xl text-text-secondary mb-8">
              Create your account and unlock the power of AI-driven content creation. Start your free trial today.
            </p>

            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-primary-500 rounded-full"></div>
                <span className="text-text-secondary">Free 30-day trial</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-primary-500 rounded-full"></div>
                <span className="text-text-secondary">No credit card required</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-primary-500 rounded-full"></div>
                <span className="text-text-secondary">Cancel anytime</span>
              </div>
            </div>
          </div>
        </div>

        {/* Right side - Register Form */}
        <div className="w-full lg:w-1/2 flex justify-center">
          <div className="w-full max-w-md">
            <RegisterCard />

            <div className="mt-6 text-center">
              <p className="text-sm text-text-muted">
                Already have an account?{' '}
                <Link
                  to="/login"
                  className="text-primary-600 hover:text-primary-500 font-medium"
                >
                  Sign in here
                </Link>
              </p>
            </div>

            <div className="mt-8 text-center lg:hidden">
              <Link
                to="/"
                className="text-sm text-text-muted hover:text-text-secondary"
              >
                ← Back to home
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default OptimizedRegisterPage;