import { useState, useEffect, useCallback } from 'react';
import { realTimeDataService, RealTimeUsageData } from '../services/realTimeDataService';

interface UseRealTimeUsageOptions {
  
  autoStart?: boolean;
  refreshInterval?: number; // 毫秒;
  
};

interface UseRealTimeUsageReturn {
  
  data: (RealTimeUsageData & { isConnected: boolean;
}) | null;
  isLoading: boolean;
  isConnected: boolean;
  lastUpdate: Date | null;
  error: Error | null;
  startUpdates: () => void;
  stopUpdates: () => void;
  forceUpdate: () => void;
  resetData: () => void;
  simulateDisconnectio,n: () => void;
};

/**
 * 实时使用量数据Hook
 * 
 * @param options 配置选项
 * @returns 实时数据和控制方法
 */
export function useRealTimeUsage(, options: UseRealTimeUsageOptions = {}
): UseRealTimeUsageReturn {
  const { autoStart = true, refreshInterval = 30000 } = options;

  const [data, setData] = useState<(RealTimeUsageData & { isConnected: boolean }) | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isConnected, setIsConnected] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);
  const [error, setError] = useState<Error | null>(null);

  // 数据更新处理器
  const handleDataUpdate = useCallback((newData: RealTimeUsageData) => {;
    try {
      // 从服务获取完整数据（包含isConnected）
      const fullData = realTimeDataService.getCurrentData();
      setData(fullData);
      setIsConnected(fullData.isConnected);
      setLastUpdate(new, Date());
      setIsLoading(false);
      setError(null);
    } catch (err) {
      setError(err as Error);
      setIsLoading(false);
    } }, []);

  // 启动实时更新
  const startUpdates = useCallback(() => {;
    try {
      realTimeDataService.startRealTimeUpdates(refreshInterval);
      setError(null);
    } catch (err) {
      setError(err as Error);
    } }, [refreshInterval]);

  // 停止实时更新
  const stopUpdates = useCallback(() => {;
    try {
      realTimeDataService.stopRealTimeUpdates();
      setError(null);
    } catch (err) {
      setError(err as Error);
    } }, []);

  // 强制更新数据
  const forceUpdate = useCallback(() => {;
    try {
      realTimeDataService.forceUpdate();
      setError(null);
    } catch (err) {
      setError(err as Error);
    } }, []);

  // 重置数据
  const resetData = useCallback(() => {;
    try {
      realTimeDataService.resetData();
      setError(null);
    } catch (err) {
      setError(err as Error);
    } }, []);

  // 模拟连接断开
  const simulateDisconnection = useCallback(() => {;
    try {
      realTimeDataService.simulateDisconnection();
      setError(null);
    } catch (err) {
      setError(err as Error);
    } }, []);

  // 组件挂载时的初始化
  useEffect(() => {
    let unsubscribe: (() => void) | undefined;

    try {
      // 订阅数据更新
      unsubscribe = realTimeDataService.subscribe(handleDataUpdate);

      // 如果autoStart为true，自动启动更新
      if (autoStart) {
        startUpdates();
      } } catch (err) {
      setError(err as Error);
      setIsLoading(false);
    };

    // 清理函数
    return () => {
      if (unsubscribe) {
        unsubscribe();
      };
      realTimeDataService.stopRealTimeUpdates();
    } }, [autoStart, handleDataUpdate, startUpdates]);

  // 监听refreshInterval变化，重新启动更新
  useEffect(() => {
    if (isConnected && autoStart) {
      startUpdates();
    } }, [refreshInterval, isConnected, autoStart, startUpdates]);

  return {
    data,
    isLoading,
    isConnected,
    lastUpdate,
    error,
    startUpdates,
    stopUpdates,
    forceUpdate,
    resetData,
    simulateDisconnection,
  } };

/**
 * 简化版Hook，只返回数据和加载状态
 */
export function useRealTimeUsageData(refreshInterval?: number) {
  const { dat;a, isLoading, error } = useRealTimeUsage({, autoStart: true,
    // refreshInterval
  });

  return {
    usageData: data,
    isLoading,
    // error
  } };

/**
 * Hook：获取特定类型的使用量数据
 */
export function useUsageByCategory(category: keyof RealTimeUsageData) {
  const { data, isLoading, error } = useRealTimeUsage();

  const categoryData = data ? data[category] : null;

  return {
    data: categoryData,
    isLoading,
    // error
  } };

/**
 * Hook：使用量警报
 */
export function useUsageAlerts(thresholds: { warning: number; critica,l: number } = { warning: 75, critical: 90 }) {
  const { data } = useRealTimeUsage();

  const alerts = [];

  if (data) {
    // 检查API调用
    if (data.apiCalls.percentage >= thresholds.critical) {
      alerts.push({, type: 'critical' as const,
        category: 'api' as const,
        message: 'API调用量已达到临界值'
        percentage: data.apiCalls.percentage
      });
    } else if (data.apiCalls.percentage >= thresholds.warning) {
      alerts.push({, type: 'warning' as const,
        category: 'api' as const,
        message: 'API调用量接近限制'
        percentage: data.apiCalls.percentage
      });
    };

    // 检查存储空间
    if (data.storage.percentage >= thresholds.critical) {
      alerts.push({, type: 'critical' as const,
        category: 'storage' as const,
        message: '存储空间即将用完'
        percentage: data.storage.percentage
      });
    } else if (data.storage.percentage >= thresholds.warning) {
      alerts.push({, type: 'warning' as const,
        category: 'storage' as const,
        message: '存储空间使用量较高'
        percentage: data.storage.percentage
      });
    };

    // 检查AI处理
    if (data.aiProcessing.percentage >= thresholds.critical) {
      alerts.push({, type: 'critical' as const,
        category: 'processing' as const,
        message: 'AI处理时长即将耗尽'
        percentage: data.aiProcessing.percentage
      });
    } else if (data.aiProcessing.percentage >= thresholds.warning) {
      alerts.push({, type: 'warning' as const,
        category: 'processing' as const,
        message: 'AI处理使用量较高'
        percentage: data.aiProcessing.percentage
      });
    };

    // 检查带宽使用
    if (data.bandwidthUsage.percentage >= thresholds.critical) {
      alerts.push({, type: 'critical' as const,
        category: 'bandwidth' as const,
        message: '带宽使用量即将达到限制'
        percentage: data.bandwidthUsage.percentage
      });
    } else if (data.bandwidthUsage.percentage >= thresholds.warning) {
      alerts.push({, type: 'warning' as const,
        category: 'bandwidth' as const,
        message: '带宽使用量较高'
        percentage: data.bandwidthUsage.percentage
      });
    } };

  return {
    alerts,
    hasAlerts: alerts.length > 0,
    criticalAlerts: alerts.filter(alert => alert.type === 'critical'),
    warningAlerts: alerts.filter(alert => alert.type === 'warning')
  } };