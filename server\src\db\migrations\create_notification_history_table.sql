-- Create notification_history table for storing notification rule execution history
CREATE TABLE IF NOT EXISTS notification_history (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  rule_id UUID NOT NULL REFERENCES notification_rules(id) ON DELETE CASCADE,
  appointment_id UUID NOT NULL,
  appointment_service_name VARCHAR(255),
  appointment_time TIMESTAMPTZ,
  client_name VARCHAR(255),
  notification_channel VARCHAR(50) NOT NULL,
  status VARCHAR(50) NOT NULL, -- 'sent', 'failed', 'pending', 'cancelled'
  error_message TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Add indexes for efficient querying
CREATE INDEX IF NOT EXISTS notification_history_rule_id_idx ON notification_history(rule_id);
CREATE INDEX IF NOT EXISTS notification_history_appointment_id_idx ON notification_history(appointment_id);
CREATE INDEX IF NOT EXISTS notification_history_status_idx ON notification_history(status);
CREATE INDEX IF NOT EXISTS notification_history_created_at_idx ON notification_history(created_at);

-- Add comment to the table
COMMENT ON TABLE notification_history IS 'Stores execution history of notification rules'; 