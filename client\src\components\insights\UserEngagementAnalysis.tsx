import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Progress } from '../ui/progress';
import { Skeleton } from '../ui/skeleton';
import { Users, Activity, MousePointer, Clock } from 'lucide-react';

interface UserEngagementAnalysisProps {
  timeRange?: string;
  className?: string;
}

export function UserEngagementAnalysis({ 
  timeRange = 'last7days', 
  className = '' 
}: UserEngagementAnalysisProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [engagementData, setEngagementData] = useState<any>(null);

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      setError(null);
      try {
        // Mock data for demonstration
        setTimeout(() => {
          setEngagementData(getMockUserEngagementData());
          setIsLoading(false);
        }, 1000);
      } catch (err) {
        console.error('Failed to fetch user engagement data:', err);
        setError('Failed to load data, please try again later');
        setIsLoading(false);
      }
    };

    fetchData();
  }, [timeRange]);

  // Loading state
  if (isLoading) {
    return (
      <div className={className}>
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <Card key={i}>
                <CardContent className="p-6">
                  <Skeleton className="h-4 w-20 mb-2" />
                  <Skeleton className="h-8 w-16 mb-2" />
                  <Skeleton className="h-3 w-24" />
                </CardContent>
              </Card>
            ))}
          </div>
          <Skeleton className="h-[400px] w-full" />
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className={className}>
        <div className="rounded-lg border border-red-200 bg-red-50 p-4">
          <span className="text-red-800">{error}</span>
        </div>
      </div>
    );
  }

  // No data state
  if (!engagementData) {
    return (
      <div className={className}>
        <Card>
          <CardHeader>
            <CardTitle>User Engagement Analysis</CardTitle>
            <CardDescription>No user engagement data available</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-center py-10 text-muted-foreground">
              No relevant user engagement data found
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const { summary } = engagementData;

  return (
    <div className={className}>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold tracking-tight">User Engagement Analysis</h2>
            <p className="text-muted-foreground">Monitor user activity and engagement patterns</p>
          </div>
        </div>
        
        {/* KPI Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Users className="h-5 w-5 text-blue-600" />
                  <span className="text-sm font-medium text-muted-foreground">Total Users</span>
                </div>
                <span className="text-xs text-green-600">+12.8%</span>
              </div>
              <div className="mt-2">
                <span className="text-2xl font-bold">{summary.totalUsers?.toLocaleString() || '0'}</span>
                <p className="text-xs text-muted-foreground mt-1">Registered users</p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Activity className="h-5 w-5 text-green-600" />
                  <span className="text-sm font-medium text-muted-foreground">Monthly Active</span>
                </div>
                <span className="text-xs text-green-600">+8.4%</span>
              </div>
              <div className="mt-2">
                <span className="text-2xl font-bold">{summary.monthlyActiveUsers?.toLocaleString() || '0'}</span>
                <p className="text-xs text-muted-foreground mt-1">Active in last 30 days</p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Clock className="h-5 w-5 text-orange-600" />
                  <span className="text-sm font-medium text-muted-foreground">Session Duration</span>
                </div>
                <span className="text-xs text-green-600">+15.2%</span>
              </div>
              <div className="mt-2">
                <span className="text-2xl font-bold">{summary.avgSessionDuration}min</span>
                <p className="text-xs text-muted-foreground mt-1">Average per session</p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <MousePointer className="h-5 w-5 text-purple-600" />
                  <span className="text-sm font-medium text-muted-foreground">Retention Rate</span>
                </div>
                <span className="text-xs text-green-600">+3.1%</span>
              </div>
              <div className="mt-2">
                <span className="text-2xl font-bold">{summary.retentionRate7d}%</span>
                <p className="text-xs text-muted-foreground mt-1">7-day retention</p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Engagement Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>User Activity</CardTitle>
              <CardDescription>Daily active users breakdown</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm">New Users</span>
                  <div className="flex items-center gap-2">
                    <Progress value={35} className="w-20 h-2" />
                    <span className="text-sm font-medium">35%</span>
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Returning Users</span>
                  <div className="flex items-center gap-2">
                    <Progress value={65} className="w-20 h-2" />
                    <span className="text-sm font-medium">65%</span>
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Power Users</span>
                  <div className="flex items-center gap-2">
                    <Progress value={20} className="w-20 h-2" />
                    <span className="text-sm font-medium">20%</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Engagement Quality</CardTitle>
              <CardDescription>User interaction metrics</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm">High Engagement</span>
                  <Badge className="bg-green-100 text-green-700">
                    {summary.highEngagement}%
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Medium Engagement</span>
                  <Badge className="bg-yellow-100 text-yellow-700">
                    {summary.mediumEngagement}%
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Low Engagement</span>
                  <Badge className="bg-red-100 text-red-700">
                    {summary.lowEngagement}%
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}

function getMockUserEngagementData() {
  return {
    summary: {
      totalUsers: 15847,
      monthlyActiveUsers: 12654,
      avgSessionDuration: 18.5,
      retentionRate7d: 72.3,
      highEngagement: 28,
      mediumEngagement: 45,
      lowEngagement: 27
    }
  };
} 