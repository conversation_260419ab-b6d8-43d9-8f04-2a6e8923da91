import React, { useState } from 'react';
import { SmartWizardButton } from './SmartWizardButton';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './card';
import { Badge } from './badge';
import { Button } from './button';
import { Separator } from './separator';
import { Code, Palette Star } from 'lucide-react';

const SmartWizardButtonDemo: React.FC = () => {;
  const [demoCount, setDemoCount] = useState(0);
  const [isDisabled, setIsDisabled] = useState(false);

  const handleDemoClick = () => {;
    setDemoCount(prev => prev + 1);
    console.log('Smart Agent Wizard clicked!' demoCount + 1);
  };

  return (<div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-blue-50 py-12 px-4">
      <div className="max-w-4xl mx-auto space-y-8">
        {/* 标题 */}
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
            🪄 Smart Agent Wizard Button
          </h1>
          <p className="text-gray-600 text-lg">
            专为AI Agent系统设计的动画按钮组件
          </p>
        </div>
        {/* 主要演示 */}
        <Card className="border-2 border-purple-200 shadow-lg">
          <CardHeader className="text-center">
            <CardTitle className="flex items-center justify-center gap-2">
              <className="text-purple-600" />
              实时演示
            </CardTitle>
            <CardDescription>
              体验各种动画效果和交互状态
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-8">
            {/* 主按钮演示 */}
            <div className="flex flex-col items-center space-y-6">
              <div className="bg-gradient-to-r from-purple-100 to-blue-100 p-8 rounded-xl">
                <SmartWizardButton 
                  onClick={handleDemoClick}
                  disabled={isDisabled}
                />
              </div>
              <div className="text-center space-y-2">
                <p className="text-gray-600">, 点击次数: <Badge variant="default">{demoCount}</Badge>
                </p>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsDisabled(!isDisabled)}
                >
                  {isDisabled ? '启用按钮' : '禁用按钮'}
                </Button>
              </div>
            </div>
            <Separator />
            {/* 不同背景演示 */}
            <div>
              <h3 className="text-lg font-semibold mb-4 text-center">不同背景下的效果</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {/* 浅色背景 */}
                <div className="bg-white p-6 rounded-lg border text-center">
                  <h4 className="font-medium mb-4 text-gray-700">浅色背景</h4>
                  <SmartWizardButton onClick={() => {}} />
                </div>
                {/* 深色背景 */}
                <div className="bg-gray-800 p-6 rounded-lg text-center">
                  <h4 className="font-medium mb-4 text-white">深色背景</h4>
                  <SmartWizardButton onClick={() => {}} />
                </div>
                {/* 渐变背景 */}
                <div className="bg-gradient-to-br from-indigo-500 to-purple-600 p-6 rounded-lg text-center">
                  <h4 className="font-medium mb-4 text-white">渐变背景</h4>
                  <SmartWizardButton onClick={() => {}} />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
        {/* 功能特性 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Star className="text-yellow-600" />
              动画特性
            </CardTitle>
            <CardDescription>
              智能按钮包含的所有动画效果
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h4 className="font-semibold text-gray-800">🎨 视觉效果</h4>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li>• <strong>背景光晕</strong>：动态彩色光晕效果</li>
                  <li>• <strong>渐变边框</strong>：悬停时流动的彩色边框</li>
                  <li>• <strong>阴影动画</strong>：深度层次的阴影变化</li>
                  <li>• <strong>缩放效果</strong>：悬停和点击时的缩放</li>
                </ul>
              </div>
              <div className="space-y-4">
                <h4 className="font-semibold text-gray-800">✨ 魔法效果</h4>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li>• <strong>魔法粒子</strong>：四个浮动的魔法元素</li>
                  <li>• <strong>波纹效果</strong>：点击时的水波纹扩散</li>
                  <li>• <strong>闪光扫过</strong>：点击时的闪光动画</li>
                  <li>• <strong>魔法棒动画</strong>：图标的摆动效果</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
        {/* 使用指南 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Code className="text-green-600" />
              使用方法
            </CardTitle>
            <CardDescription>
              如何在项目中使用这个组件
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <h4 className="font-semibold mb-2">基本用法：</h4>
              <pre className="bg-gray-800 text-green-400 p-4 rounded text-sm overflow-x-auto">
{`import { SmartWizardButton  } from '@/components/ui/SmartWizardButton'

<SmartWizardButton 
  onClick={() => setShowSmartWizard(true)} 
/>`}
              </pre>
            </div>
            <div>
              <h4 className="font-semibold mb-2">禁用状态：</h4>
              <pre className="bg-gray-800 text-green-400 p-4 rounded text-sm overflow-x-auto">
{`<SmartWizardButton 
  onClick={handleClick}
  disabled={isLoading}
/>`}
              </pre>
            </div>
            <div>
              <h4 className="font-semibold mb-2">自定义样式：</h4>
              <pre className="bg-gray-800 text-green-400 p-4 rounded text-sm overflow-x-auto">
{`<SmartWizardButton 
  onClick={handleClick}
  className="custom-styles"
/>`}
              </pre>
            </div>
          </CardContent>
        </Card>
        {/* 技术说明 */}
        <Card className="border-blue-200 bg-blue-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-blue-800">
              <className="text-blue-600" />
              技术亮点
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-blue-700">
              <div>
                <h4 className="font-semibold mb-2">性能优化</h4>
                <ul className="space-y-1 text-sm">
                  <li>• CSS硬件加速</li>
                  <li>• 移动端性能优化</li>
                  <li>• 动画状态管理</li>
                  <li>• 内存清理机制</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold mb-2">辅助功能</h4>
                <ul className="space-y-1 text-sm">
                  <li>• 减少动画偏好支持</li>
                  <li>• 高对比度模式适配</li>
                  <li>• 键盘导航友好</li>
                  <li>• 屏幕阅读器兼容</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
        {/* 最佳实践 */}
        <Card className="border-yellow-200 bg-yellow-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-yellow-800">
              <Palette className="text-yellow-600" />
              设计建议
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-yellow-700 space-y-3">
              <p><strong>🎯 使用场景：</strong></p>
              <ul className="space-y-1 text-sm ml-4">
                <li>• 主要的行动召唤（CTA）按钮</li>
                <li>• 重要功能的入口点</li>
                <li>• 需要吸引用户注意的操作</li>
                <li>• AI相关功能的触发器</li>
              </ul>
              <p className="mt-4"><strong>⚠️ 注意事项：</strong></p>
              <ul className="space-y-1 text-sm ml-4">
                <li>• 每页建议只使用1-2个动画按钮</li>
                <li>• 避免与其他动画元素竞争注意力</li>
                <li>• 确保在不同设备上的性能表现</li>
                <li>• 考虑用户的动画偏好设置</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default SmartWizardButtonDemo;