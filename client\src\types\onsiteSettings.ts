import { BaseSettings, WorkingHours, NotificationSettings, Staff, ServiceType } from './settingsCommon';

/**
 * 上门预约设置类型定义
 */

export interface OnsiteGeneralSettings extends BaseSettings {
  serviceName: string;
  maxDailyBookings: number;
  requireConfirmation: boolean;
  advanceBookingDays: number;
  appointmentTimeSlotMinutes: number;
  cancellationPolicy?: string;
  bufferTime: number; // 预约间隔时间（分钟）
  allowSameDayBooking: boolean;
  requireClientPhone: boolean;
  requireClientAddress: boolean;
}

export interface MapSettings {
  centerLatitude: number;
  centerLongitude: number;
  defaultZoomLevel: number;
  serviceRadiusKm: number;
  displayServiceArea: boolean;
  displayEstimatedTravelTime: boolean;
  mapProvider: 'google' | 'amap' | 'baidu';
  apiKey?: string;
}

export interface StaffAssignmentSettings {
  assignmentStrategy: 'manual' | 'automatic' | 'client-preference';
  allowClientToChooseStaff: boolean;
  autoAssignBySkills: boolean;
  autoAssignByAvailability: boolean;
  autoAssignByLocation: boolean;
}

export interface ResourceAllocation {
  teams: Array<{
    id: string;
    name: string;
    members: Staff[];
    serviceTypes: string[];
    isActive: boolean;
  }>;
  staffManagement: {
    staff: Staff[];
    assignmentSettings: StaffAssignmentSettings;
  };
}

export interface AISchedulingSettings {
  enabled: boolean;
  prioritizationFactors: {
    customerImportance: number; // 1-10
    serviceUrgency: number; // 1-10
    teamEfficiency: number; // 1-10
    travelDistance: number; // 1-10
    timeSlotOptimization: number; // 1-10
  };
  constraints: {
    maxTravelTime: number; // 分钟
    minRestTime: number; // 分钟
    serviceQualityThreshold: number; // 1-10
  };
  optimizationGoals: {
    maximizeTeamUtilization: boolean;
    minimizeTravelTime: boolean;
    balanceWorkload: boolean;
    prioritizeCustomerPreference: boolean;
  };
}

export interface OnsiteBookingSettings {
  general: OnsiteGeneralSettings;
  workingHours: WorkingHours;
  mapSettings: MapSettings;
  resourceAllocation: ResourceAllocation;
  notifications: NotificationSettings;
  aiScheduling: AISchedulingSettings;
  serviceTypes: ServiceType[];
}

export interface OnsiteSettingsFormData {
  generalSettings: Partial<OnsiteGeneralSettings>;
  mapSettings: Partial<MapSettings>;
  workingHours: Partial<WorkingHours>;
  staffSettings: Partial<StaffAssignmentSettings>;
  notificationSettings: Partial<NotificationSettings>;
  aiSchedulingSettings: Partial<AISchedulingSettings>;
}

// 默认设置模板
export const defaultOnsiteSettings: OnsiteBookingSettings = {
  general: {
    name: '上门服务',
    description: '专业的上门服务预约',
    enabled: true,
    serviceName: '上门服务',
    maxDailyBookings: 20,
    requireConfirmation: true,
    advanceBookingDays: 7,
    appointmentTimeSlotMinutes: 60,
    bufferTime: 15,
    allowSameDayBooking: false,
    requireClientPhone: true,
    requireClientAddress: true
  },
  workingHours: {
    enabled: true,
    timezone: 'Asia/Shanghai',
    schedule: {
      monday: { start: '09:00', end: '18:00', closed: false },
      tuesday: { start: '09:00', end: '18:00', closed: false },
      wednesday: { start: '09:00', end: '18:00', closed: false },
      thursday: { start: '09:00', end: '18:00', closed: false },
      friday: { start: '09:00', end: '18:00', closed: false },
      saturday: { start: '10:00', end: '16:00', closed: false },
      sunday: { start: '10:00', end: '16:00', closed: true }
    }
  },
  mapSettings: {
    centerLatitude: 39.9042,
    centerLongitude: 116.4074,
    defaultZoomLevel: 10,
    serviceRadiusKm: 20,
    displayServiceArea: true,
    displayEstimatedTravelTime: true,
    mapProvider: 'google'
  },
  resourceAllocation: {
    teams: [],
    staffManagement: {
      staff: [],
      assignmentSettings: {
        assignmentStrategy: 'automatic',
        allowClientToChooseStaff: false,
        autoAssignBySkills: true,
        autoAssignByAvailability: true,
        autoAssignByLocation: true
      }
    }
  },
  notifications: {
    email: {
      enabled: true,
      provider: 'smtp',
      fromEmail: '<EMAIL>',
      fromName: '客服中心',
      smtpHost: '',
      smtpPort: 587
    },
    sms: {
      enabled: false,
      provider: 'local'
    },
    push: {
      enabled: false
    },
    templates: {
      booking_confirmed: {
        enabled: true,
        subject: '预约确认通知',
        content: '您的上门服务预约已确认，我们将按时为您提供服务。'
      },
      booking_reminder: {
        enabled: true,
        subject: '服务提醒',
        content: '您的上门服务将在1小时后开始，请准备好相关物品。'
      },
      staff_assigned: {
        enabled: true,
        subject: '员工分配通知',
        content: '您的服务员工已确定，将准时为您提供专业服务。'
      },
      service_started: {
        enabled: true,
        subject: '服务开始',
        content: '我们的员工已到达现场，服务正在进行中。'
      },
      service_completed: {
        enabled: true,
        subject: '服务完成',
        content: '本次服务已完成，感谢您的信任与支持！'
      },
      booking_cancelled: {
        enabled: true,
        subject: '预约取消通知',
        content: '您的预约已成功取消，如有疑问请联系客服。'
      }
    }
  },
  aiScheduling: {
    enabled: false,
    prioritizationFactors: {
      customerImportance: 5,
      serviceUrgency: 7,
      teamEfficiency: 6,
      travelDistance: 8,
      timeSlotOptimization: 7
    },
    constraints: {
      maxTravelTime: 60,
      minRestTime: 15,
      serviceQualityThreshold: 7
    },
    optimizationGoals: {
      maximizeTeamUtilization: true,
      minimizeTravelTime: true,
      balanceWorkload: true,
      prioritizeCustomerPreference: false
    }
  },
  serviceTypes: []
}; 