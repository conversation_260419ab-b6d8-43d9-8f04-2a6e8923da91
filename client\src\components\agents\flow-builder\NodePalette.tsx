import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Search,
  PlayCircle, 
  GitBranch, 
  Brain, 
  User, 
  Globe, 
  Clock, 
  Send, 
  Mail, 
  MessageSquare,
  Variable,
  CornerDownRight,
  Square,
  Zap,
  Settings,
  Puzzle,
  Wrench
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { FlowNodeType, NODE_TYPE_DEFINITIONS, NodeTypeDefinition } from '@/types/flowBuilder';

interface NodePaletteProps {
  onAddNode: (nodeType: FlowNodeType, position?: { x: number; y: number }) => void;
  className?: string
}

const NodePalette: React.FC<NodePaletteProps> = ({ onAddNode, className }) => {
  const { t } = useTranslation();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  // 获取节点图标
  const getNodeIcon = (nodeType: FlowNodeType) => {
    switch (nodeType) {
      case FlowNodeType.TRIGGER:
        return <PlayCircle className="h-4 w-4" />;
      case FlowNodeType.CONDITION:
        return <GitBranch className="h-4 w-4" />;
      case FlowNodeType.AI_RESPONSE:
        return <Brain className="h-4 w-4" />;
      case FlowNodeType.HUMAN_HANDOFF:
        return <User className="h-4 w-4" />;
      case FlowNodeType.API_CALL:
        return <Globe className="h-4 w-4" />;
      case FlowNodeType.DELAY:
        return <Clock className="h-4 w-4" />;
      case FlowNodeType.WEBHOOK:
        return <Send className="h-4 w-4" />;
      case FlowNodeType.EMAIL:
        return <Mail className="h-4 w-4" />;
      case FlowNodeType.SMS:
        return <MessageSquare className="h-4 w-4" />;
      case FlowNodeType.VARIABLE_SET:
        return <Variable className="h-4 w-4" />;
      case FlowNodeType.JUMP:
        return <CornerDownRight className="h-4 w-4" />;
      case FlowNodeType.END:
        return <Square className="h-4 w-4" />;
      default:
        return <Square className="h-4 w-4" />
}
  };

  // 获取分类图标
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'trigger':
        return <Zap className="h-4 w-4" />;
      case 'logic':
        return <GitBranch className="h-4 w-4" />;
      case 'action':
        return <Settings className="h-4 w-4" />;
      case 'integration':
        return <Puzzle className="h-4 w-4" />;
      case 'utility':
        return <Wrench className="h-4 w-4" />;
      default:
        return <Square className="h-4 w-4" />
}
  };

  // 按分类分组节点
  const nodesByCategory = Object.values(NODE_TYPE_DEFINITIONS).reduce((acc, nodeDefinition) => {
    const category = nodeDefinition.category;
    if (!acc[category]) {
      acc[category] = []
}
    acc[category].push(nodeDefinition);
    return acc
}, {} as Record<string, NodeTypeDefinition[]>);

  // 过滤节点
  const filteredNodes = Object.entries(nodesByCategory).reduce((acc, [category, nodes]) => {
    if (selectedCategory !== 'all' && category !== selectedCategory) {
      return acc
}

    const filtered = nodes.filter(node => 
      node.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
      node.description.toLowerCase().includes(searchTerm.toLowerCase())
    );

    if (filtered.length > 0) {
      acc[category] = filtered
}

    return acc
}, {} as Record<string, NodeTypeDefinition[]>);

  // 处理节点拖拽开始
  const handleDragStart = (event: React.DragEvent, nodeType: FlowNodeType) => {
    event.dataTransfer.setData('application/reactflow', nodeType);
    event.dataTransfer.effectAllowed = 'move'
};

  // 节点卡片组件
  const NodeCard: React.FC<{ nodeDefinition: NodeTypeDefinition }> = ({ nodeDefinition }) => (
    <div
      draggable
      onDragStart={(e) => handleDragStart(e, nodeDefinition.type)}
      onClick={() => onAddNode(nodeDefinition.type)}
      className={cn(
        "p-3 border rounded-lg cursor-pointer transition-all duration-200",
        "hover:shadow-md hover:border-primary/50 active:scale-95",
        "bg-white border-gray-200"
      )}
    >
      <div className="flex items-start gap-3">
        <div 
          className="p-2 rounded text-white flex-shrink-0"
          style={{ backgroundColor: nodeDefinition.color }}
        >
          {getNodeIcon(nodeDefinition.type)}
        </div>
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <h4 className="font-medium text-sm text-gray-900 truncate">
              {t(`agents.flowBuilder.nodeTypes.${nodeDefinition.type}`) || nodeDefinition.label}
            </h4>
            {nodeDefinition.premium && (
              <Badge variant="secondary" className="text-xs">Pro</Badge>
            )}
            {nodeDefinition.deprecated && (
              <Badge variant="destructive" className="text-xs">Deprecated</Badge>
            )}
          </div>
          <p className="text-xs text-gray-500 line-clamp-2">
            {nodeDefinition.description}
          </p>
          <div className="flex items-center gap-2 mt-2">
            <span className="text-xs text-gray-400">
              {nodeDefinition.inputs}→{nodeDefinition.outputs}
            </span>
            {nodeDefinition.configurable && (
              <Badge variant="outline" className="text-xs">Configurable</Badge>
            )}
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className={cn("h-full flex flex-col", className)}>
      <CardHeader className="pb-3">
        <CardTitle className="text-sm">Node Palette</CardTitle>
        <div className="space-y-2">
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search nodes..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-8 h-9"
            />
          </div>
        </div>
      </CardHeader>

      <CardContent className="flex-1 p-0">
        <Tabs value={selectedCategory} onValueChange={setSelectedCategory} className="h-full flex flex-col">
          <TabsList className="grid grid-cols-3 mx-4 mb-4">
            <TabsTrigger value="all" className="text-xs">All</TabsTrigger>
            <TabsTrigger value="trigger" className="text-xs">Triggers</TabsTrigger>
            <TabsTrigger value="action" className="text-xs">Actions</TabsTrigger>
          </TabsList>

          <ScrollArea className="flex-1 px-4">
            <TabsContent value="all" className="mt-0">
              <div className="space-y-4">
                {Object.entries(filteredNodes).map(([category, nodes]) => (
                  <div key={category}>
                    <div className="flex items-center gap-2 mb-2">
                      {getCategoryIcon(category)}
                      <h3 className="font-medium text-sm text-gray-700">
                        {t(`agents.flowBuilder.nodeCategories.${category}`) || category}
                      </h3>
                      <Badge variant="outline" className="text-xs">
                        {nodes.length}
                      </Badge>
                    </div>
                    <div className="space-y-2">
                      {nodes.map((nodeDefinition) => (
                        <NodeCard key={nodeDefinition.type} nodeDefinition={nodeDefinition} />
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="trigger" className="mt-0">
              <div className="space-y-2">
                {filteredNodes.trigger?.map((nodeDefinition) => (
                  <NodeCard key={nodeDefinition.type} nodeDefinition={nodeDefinition} />
                ))}
              </div>
            </TabsContent>

            <TabsContent value="action" className="mt-0">
              <div className="space-y-2">
                {filteredNodes.action?.map((nodeDefinition) => (
                  <NodeCard key={nodeDefinition.type} nodeDefinition={nodeDefinition} />
                ))}
              </div>
            </TabsContent>
          </ScrollArea>
        </Tabs>
      </CardContent>

      {/* 使用提示 */}
      <div className="p-4 border-t bg-muted/30">
        <p className="text-xs text-muted-foreground">
          Drag nodes to canvas or click to add at random position
        </p>
      </div>
    </div>
  )
};

export default NodePalette;
