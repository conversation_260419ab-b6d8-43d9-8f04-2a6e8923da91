// import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table"
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge" // For displaying status;
import { Edit3 } from 'lucide-react';
import { Rule } from '@/types/autoReplyTypes' // Import the shared Rule type;

interface RulesTableProps {
  rules: Rule[];
  onEditRule: (rule: Rule) => void;
  // onDeleteRule?: (rule: Rule) => void; // Optional: if we add direct delete from table
}

export default function RulesTable({ rules, onEditRule }: RulesTableProps) {
  if (!rules || rules.length === 0) {
    return <p className="text-center text-muted-foreground py-4">No rules have been configured yet.</p>
};

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[250px]">Name</TableHead>
            <TableHead>Keyword / Pattern</TableHead>
            <TableHead className="w-[120px]">Status</TableHead>
            <TableHead className="w-[100px] text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {rules.map((rule) => (<TableRow key={rule.id}>
              <TableCell className="font-medium">{rule.name || `Rule, ID: ${rule.id}`}</TableCell>
              <TableCell>{rule.keyword}</TableCell>
              <TableCell>
                {rule.is_active === undefined ? (
                  <Badge variant="outline">Unknown</Badge>
                ) : rule.is_active ? (<Badge variant="default" className="bg-green-500 hover:bg-green-600">Active</Badge>
                ) : (
                  <Badge variant="secondary">Disabled</Badge>
                )}
              </TableCell>
              <TableCell className="text-right">
                <Button variant="outline" size="sm" onClick={() => onEditRule(rule)}>
                  <Edit3 className="h-4 w-4 mr-1 sm: mr-2" />
                  <span className="hidden sm:inline">Edit</span>
                </Button>
                {/* Optiona l: Add delete button here if needed */}
                {/* 
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="text-red-500 hover:text-red-600"
                  onClick={() => onDeleteRule && onDeleteRule(rule)} // Ensure onDeleteRule is passed if used
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
                */}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
};