/**
 * 主题系统统一入口
 * 导出所有主题配置、工具和组件
 */

// 导入必要的类型
import type { DesignTokens, ThemeMode } from '../types/design-system';

// 创建主题配置
import { designTokens } from './design-tokens';

// 导出现有的主题tokens
export * from './design-tokens';
export * from './tokens';
export {
  chartColors,
  chartTheme,
  getChartColor,
  getTemperatureColor,
  getStatusColor,
  getResponsiveChartConfig
} from './chart-theme';
export * from './dashboard-theme';
export * from './data-insight-theme';
export * from './marketing-tokens';

// 导出主题类型
export type {
  ThemeProviderProps,
  UseThemeReturn,
  ThemeVariables,
  ThemeConfiguration,
  ThemeContextValue,
  ThemeMetadata,
  CustomTheme,
  ThemeManager,
  ThemePreset,
  ResponsiveTheme,
  ThemeTransition
} from '../types/theme';

// 重新导出设计系统核心
export { designTokens, tailwindExtension } from './design-tokens';

// 默认主题配置
export const defaultThemeConfiguration = {
  light: designTokens,
  dark: {
    ...designTokens,
    colors: {
      ...designTokens.colors,
      // 暗色主题的颜色调整
      background: {
        primary: '#0f172a',
        secondary: '#1e293b',
        accent: '#334155',
        muted: '#475569',
        hover: '#64748b'
      },
      text: {
        primary: '#f8fafc',
        secondary: '#e2e8f0',
        muted: '#cbd5e1',
        accent: '#a78bfa',
        inverse: '#0f172a'
      },
      border: {
        light: '#334155',
        default: '#475569',
        dark: '#64748b',
        accent: '#a78bfa'
      }
    }
  }
} as const;

// 主题工具函数
export const createTheme = (customTokens: Partial<DesignTokens>): DesignTokens => {
  return {
    ...designTokens,
    ...customTokens,
    colors: {
      ...designTokens.colors,
      ...customTokens.colors
    },
    spacing: {
      ...designTokens.spacing,
      ...customTokens.spacing
    },
    typography: {
      ...designTokens.typography,
      ...customTokens.typography
    }
  };
};

// 获取主题tokens
export const getThemeTokens = (mode: ThemeMode = 'light'): DesignTokens => {
  return defaultThemeConfiguration[mode];
};

// 主题切换状态管理
export const createThemeState = () => {
  let currentTheme: ThemeMode = 'light';
  const listeners: Array<(theme: ThemeMode) => void> = [];

  return {
    getCurrentTheme: () => currentTheme,
    setTheme: (theme: ThemeMode) => {
      currentTheme = theme;
      listeners.forEach(listener => listener(theme));
    },
    subscribe: (listener: (theme: ThemeMode) => void) => {
      listeners.push(listener);
      return () => {
        const index = listeners.indexOf(listener);
        if (index > -1) {
          listeners.splice(index, 1);
        }
      };
    },
    toggle: () => {
      const newTheme = currentTheme === 'light' ? 'dark' : 'light';
      currentTheme = newTheme;
      listeners.forEach(listener => listener(newTheme));
      return newTheme;
    }
  };
};

// 响应式主题工具
export const createResponsiveTheme = (
  base: DesignTokens,
  overrides: {
    mobile?: Partial<DesignTokens>;
    tablet?: Partial<DesignTokens>;
    desktop?: Partial<DesignTokens>;
  }
) => {
  return {
    base,
    mobile: base && overrides.mobile ? { ...base, ...overrides.mobile } : base,
    tablet: base && overrides.tablet ? { ...base, ...overrides.tablet } : base,
    desktop: base && overrides.desktop ? { ...base, ...overrides.desktop } : base,
  };
};

// CSS变量生成器
export const generateCSSVariables = (tokens: DesignTokens): Record<string, string> => {
  const cssVars: Record<string, string> = {};

  // 颜色变量
  Object.entries(tokens.colors.primary).forEach(([key, value]) => {
    cssVars[`--color-primary-${key}`] = value;
  });

  Object.entries(tokens.colors.background).forEach(([key, value]) => {
    cssVars[`--color-background-${key}`] = value;
  });

  Object.entries(tokens.colors.text).forEach(([key, value]) => {
    cssVars[`--color-text-${key}`] = value;
  });

  Object.entries(tokens.colors.border).forEach(([key, value]) => {
    cssVars[`--color-border-${key}`] = value;
  });

  // 间距变量
  Object.entries(tokens.spacing).forEach(([key, value]) => {
    cssVars[`--spacing-${key}`] = value;
  });

  // 圆角变量
  Object.entries(tokens.borderRadius).forEach(([key, value]) => {
    cssVars[`--border-radius-${key}`] = value;
  });

  // 阴影变量
  Object.entries(tokens.shadows).forEach(([key, value]) => {
    cssVars[`--shadow-${key}`] = value;
  });

  return cssVars;
};

// 命名导出以避免 anonymous default export 警告
export const themeSystem = {
  tokens: designTokens,
  configuration: defaultThemeConfiguration,
  createTheme,
  getThemeTokens,
  createThemeState,
  createResponsiveTheme,
  generateCSSVariables
};

// 默认导出
export default themeSystem; 