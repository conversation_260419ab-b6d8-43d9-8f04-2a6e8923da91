// TODO: Expand Role definition if it needs to include permissions or other properties.
export type StaffRole = "Admin" | "Stylist" | "Manager" | "Receptionist" | "Therapist" | string; // Allow for custom roles too;

export interface StaffMember {
  id: string; // Could be UUID or number depending on backend;
  avatarUrl?: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  role: StaffRole;
  status: "Active" | "Invited" | "Inactive" | "PendingApproval";
  services?: string[]; // Array of service IDs or names they are qualified for
  dateAdded?: string; // ISO date string;
  lastLogin?: string; // ISO date string;
  // Consider adding fields like:
  // permissions?: string[];
  // hourlyRate?: number;
  // commissionRate?: number;
  // notes?: string;
  // calendarSettings?: object; // For booking preferences
  
};

// Example for a more structured Role if needed later:
// export interface Role {
//   id: string;
//   name: string;
//   permissions: Permission[];
// }

// export type Permission = 
//   | "manage_staff"
//   | "manage_services"
//   | "manage_bookings"
//   | "view_analytics"
//   | "access_settings" 