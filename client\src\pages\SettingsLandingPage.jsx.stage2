import React from 'react'
import { useNavigate } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { Card, Button, Container, Row, Col } from '@/components/compat/react-bootstrap-compat'

export default function SettingsLandingPage() {
  const navigate = useNavigate();
  const { t } = useTranslation();

  return (
    <Container className="mt-4">)
      <h2 className="mb-4">{t('settingsLanding.title')}</h2>
      <Row>
        <Col md={6}>
          <Card className="mb-4">
            <Card.Body>
              <Card.Title>{t('settingsLanding.autoReplyTitle')}</Card.Title>
              <Card.Text>{t('settingsLanding.autoReplyDesc')}</Card.Text>
              <Button variant="primary" onClick={() => navigate('agent-reply')}>{t('settingsLanding.goToAutoReply')}</Button>
            </Card.Body>
          </Card>
        </Col>
        <Col md={6}>
          <Card className="mb-4">
            <Card.Body>
              <Card.Title>{t('settingsLanding.walkinBookingTitle')}</Card.Title>
              <Card.Text>{t('settingsLanding.walkinBookingDesc')}</Card.Text>
              <Button variant="primary" onClick={() => navigate('walkin-booking')}>{t('settingsLanding.goToWalkinBooking')}</Button>
            </Card.Body>
          </Card>
        </Col>
        <Col md={6}>
          <Card className="mb-4">
            <Card.Body>
              <Card.Title>{t('settingsLanding.onsiteBookingTitle')}</Card.Title>
              <Card.Text>{t('settingsLanding.onsiteBookingDesc')}</Card.Text>
              <Button variant="primary" onClick={() => navigate('onsite-booking')}>{t('settingsLanding.goToOnsiteBooking')}</Button>
            </Card.Body>
          </Card>
        </Col>
        <Col md={6}>
          <Card className="mb-4">
            <Card.Body>
              <Card.Title>{t('settingsLanding.contentGeneratorTitle')}</Card.Title>
              <Card.Text>{t('settingsLanding.contentGeneratorDesc')}</Card.Text>
              <Button variant="primary" onClick={() => navigate('content-generator')}>{t('settingsLanding.goToContentGenerator')}</Button>
            </Card.Body>
          </Card>
        </Col>
        <Col md={6}>
          <Card className="mb-4">
            <Card.Body>
              <Card.Title>{t('settingsLanding.platformApiTitle')}</Card.Title>
              <Card.Text>{t('settingsLanding.platformApiDesc')}</Card.Text>
              <Button variant="primary" onClick={() => navigate('platform-api')}>{t('settingsLanding.goToPlatformApi')}</Button>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Container>
  );
};