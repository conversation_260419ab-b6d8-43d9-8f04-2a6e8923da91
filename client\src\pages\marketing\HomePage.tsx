import React, { useState, useEffect } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import { Helmet } from 'react-helmet-async'
import Button from '../../components/marketing/ui/Button'
import { Card, CardContent } from '@/components/ui/unified-card'
import { CustomerReviews } from '../../components/ui/reviews'
import UltimateCtaFooterMinimal from '../../components/enhanced-landing/UltimateCtaFooterMinimal'
import { BarChart3, ShieldCheck, Menu, X, Sparkles, Rocket, Brain, Globe, Clock } from 'lucide-react'

// 导航链接
const navLinks = [{
  href: '#features', label: 'Features' },
  { href: '#pricing', label: 'Pricing' },
  { href: '#testimonials', label: 'Testimonials' },
  { href: '#contact', label: 'Contact' }
];

// 特性数据
const features = [{
  icon: <Brain className="w-8 h-8" />, title: 'AI-Powered Content Generation', description: 'Advanced AI algorithms to create high-quality, engaging content that resonates with your audience.', gradient: 'from-purple-500 to-pink-500'
},
{
  icon: <BarChart3 className="w-8 h-8" />, title: 'Real-time Analytics', description: 'Comprehensive insights and analytics to track performance and optimize your content strategy.', gradient: 'from-blue-500 to-cyan-500'
},
{
  icon: <Globe className="w-8 h-8" />, title: 'Multi-platform Distribution', description: 'Seamlessly distribute your content across multiple platforms with one-click publishing.', gradient: 'from-green-500 to-emerald-500'
},
{
  icon: <Rocket className="w-8 h-8" />, title: 'Smart Targeting', description: 'Intelligent audience targeting to ensure your content reaches the right people at the right time.', gradient: 'from-orange-500 to-red-500'
},
{
  icon: <Clock className="w-8 h-8" />, title: 'Automated Scheduling', description: 'Schedule and automate your content distribution for maximum engagement and reach.', gradient: 'from-indigo-500 to-purple-500'
},
{
  icon: <ShieldCheck className="w-8 h-8" />, title: 'Enterprise Security', description: 'Bank-level security with end-to-end encryption to protect your valuable content and data.', gradient: 'from-gray-600 to-gray-800'
},
];

// 定价方案
const pricingPlans = [{
  name: 'Starter', price: '$29', period: '/month', description: 'Perfect for individuals and small teams getting started with AI content creation.', features: [
    '10,000 AI-generated words/month',
    '5 Content templates',
    'Basic analytics',
    'Email support',
    '1 User account',
  ], popular: false, cta: 'Start Free Trial'
},
{
  name: 'Professional', price: '$79', period: '/month', description: 'Ideal for growing businesses that need more advanced features and higher limits.', features: [
    '50,000 AI-generated words/month',
    '25+ Content templates',
    'Advanced analytics & insights',
    'Priority support',
    '5 User accounts',
    'API access',
    'Custom branding',
  ], popular: true, cta: 'Start Free Trial'
},
{
  name: 'Enterprise', price: 'Custom', period: '', description: 'Tailored solutions for large organizations with specific requirements.', features: [
    'Unlimited AI-generated content',
    'Custom templates & workflows',
    'Dedicated account manager',
    'White-label solution',
    'Unlimited users',
    'Advanced API & integrations',
    'SLA guarantee',
  ], popular: false, cta: 'Contact Sales'
},
];

export default function HomePage() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY;
      setScrolled(scrollPosition > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
    setMobileMenuOpen(false);
  };

  const handleStartTrial = () => {
    navigate('/register');
  };
  
  const handleWatchDemo = () => {
    scrollToSection('features');
  };
  
  return (
    <div className="min-h-screen bg-gradient-hero">
      <Helmet>
        <title>iTeraBiz - AI-Powered Content Creation Platform</title>
        <meta name="description" content="Transform your content creation with AI-powered tools. Create, optimize, and distribute engaging content across multiple platforms." />
      </Helmet>
      {/* Navigation */}
      <nav className={`fixed top-0 w-full z-50 transition-all duration-300 ${
        scrolled ? 'bg-white/80 backdrop-blur-lg shadow-marketing-card' : 'bg-transparent'
      }`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo */}
            <div className="flex-shrink-0">
              <Link to="/" className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center">
                  <Sparkles className="w-5 h-5 text-white" />
                </div>
                <span className="text-xl font-bold text-text-primary">iTeraBiz</span>
              </Link>
            </div>
            {/* Desktop Navigation */}
            <div className="hidden md:block">
              <div className="ml-10 flex items-baseline space-x-8">
                {navLinks.map((link) => (
                  <button
                    key={link.href}
                    onClick={() => scrollToSection(link.href.slice(1))}
                    className="text-text-secondary hover:text-text-accent transition-colors duration-200 px-3 py-2 text-sm font-medium"
                    aria-label={`Navigate to ${link.label} section`}
                  >
                    {link.label}
                  </button>
                ))}
              </div>
            </div>
            {/* CTA Buttons */}
            <div className="hidden md:flex items-center space-x-4">
              <Button 
                variant="ghost" 
                size="sm"
                onClick={() => navigate('/login')}
                aria-label="Sign in to your account"
              >
                Sign In
              </Button>
              <Button 
                variant="gradient" 
                size="sm"
                onClick={() => navigate('/register')}
                aria-label="Get started with free registration"
              >
                Get Started
              </Button>
            </div>
            {/* Mobile menu button */}
            <div className="md:hidden">
              <button
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
                className="text-text-primary hover:text-text-accent p-2"
                aria-label={mobileMenuOpen ? "Close navigation menu" : "Open navigation menu"}
              >
                {mobileMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
              </button>
            </div>
          </div>
        </div>
        {/* Mobile Navigation */}
        {mobileMenuOpen && (<div className="md:hidden bg-white/95 backdrop-blur-lg border-t border-border-light">
            <div className="px-2 pt-2 pb-3 space-y-1">
              {navLinks.map((link) => (
                <button
                  key={link.href}
                  onClick={() => scrollToSection(link.href.slice(1))}
                  className="block px-3 py-2 text-base font-medium text-text-secondary hover:text-text-accent w-full text-left"
                  aria-label={`Navigate to ${link.label} section`}
                >
                  {link.label}
                </button>
              ))}
              <div className="flex flex-col space-y-2 pt-4 px-3">
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => navigate('/login')}
                  className="justify-center"
                  aria-label="Sign in to your account"
                >
                  Sign In
                </Button>
                <Button 
                  variant="gradient" 
                  size="sm"
                  onClick={() => navigate('/register')}
                  className="justify-center"
                  aria-label="Get started with free registration"
                >
                  Get Started
                </Button>
              </div>
            </div>
          </div>
        )}
      </nav>
      {/* Hero Section */}
      <section className="pt-32 pb-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center">
            <div className="animate-fade-in-down">
              <span className="inline-flex items-center px-4 py-2 rounded-full bg-primary-100 text-primary-700 text-sm font-medium mb-6">
                <Sparkles className="w-4 h-4 mr-2" />
                New: Advanced AI Content Templates Available
              </span>
            </div>
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-text-primary mb-6 animate-fade-in-up">
              Create Amazing Content with{' '}
              <span className="text-gradient-primary">AI Power</span>
            </h1>
            <p className="text-xl text-text-secondary max-w-3xl mx-auto mb-8 animate-fade-in-up" style={{ animationDelay: '200ms' }}>
              Transform your content creation process with our advanced AI platform. 
              Generate, optimize, and distribute engaging content across multiple channels in minutes, not hours.
            </p>
            <div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-4 animate-fade-in-up" style={{ animationDelay: '400ms' }}>
              <Button 
                variant="gradient" 
                size="lg"
                icon={<Rocket />}
                onClick={() => navigate('/register')}
                className="w-full sm:w-auto"
                aria-label="Start your free trial today"
              >
                Start Free Trial
              </Button>
              <Button 
                variant="outline" 
                size="lg"
                onClick={() => scrollToSection('features')}
                className="w-full sm:w-auto"
                aria-label="Watch product demo video"
              >
                Watch Demo
              </Button>
            </div>
            <div className="mt-12 animate-fade-in-up" style={{ animationDelay: '600ms' }}>
              <p className="text-sm text-text-muted mb-4">Trusted by 10,000+ content creators worldwide</p>
              <div className="flex items-center justify-center space-x-8 opacity-60">
                {/* Company logos would go here */}
                <div className="text-text-muted font-medium">TechCorp</div>
                <div className="text-text-muted font-medium">StartupLabs</div>
                <div className="text-text-muted font-medium">CreativeAgency</div>
                <div className="text-text-muted font-medium">MediaFlow</div>
              </div>
            </div>
          </div>
        </div>
      </section>
      {/* Features Section */}
      <section id="features" className="py-20 px-4 sm:px-6 lg:px-8 bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-bold text-text-primary mb-4">
              Powerful Features for Modern Content Creation
            </h2>
            <p className="text-xl text-text-secondary max-w-2xl mx-auto">
              Everything you need to create, optimize, and distribute content that engages your audience and drives results.
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (<Card 
                key={index} 
                className="group animate-fade-in-up" 
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <CardContent className="p-8">
                  <div className={`inline-flex p-3 rounded-lg bg-gradient-to-r ${feature.gradient} text-white mb-4 group-hover:scale-110 transition-transform duration-300`}>
                    {feature.icon}
                  </div>
                  <h3 className="text-xl font-semibold text-text-primary mb-3">
                    {feature.title}
                  </h3>
                  <p className="text-text-secondary leading-relaxed">
                    {feature.description}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>
      {/* Pricing Section */}
      <section id="pricing" className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-hero">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-bold text-text-primary mb-4">
              Choose Your Perfect Plan
            </h2>
            <p className="text-xl text-text-secondary max-w-2xl mx-auto">
              Start with our free trial and scale as you grow. No hidden fees, cancel anytime.
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            {pricingPlans.map((plan, index) => (<Card 
                key={index}
                variant={plan.popular ? "elevated" : "default"}
                className={`relative animate-fade-in-up ${plan.popular ? 'ring-2 ring-primary-500 scale-105' : ''}`}
                style={{ animationDelay: `${index * 200}ms` }}
              >
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <span className="bg-gradient-primary text-white px-4 py-1 rounded-full text-sm font-medium">
                      Most Popular
                    </span>
                  </div>
                )}
                
                <CardContent className="p-8">
                  <div className="text-center mb-8">
                    <h3 className="text-2xl font-bold text-text-primary mb-2">{plan.name}</h3>
                    <div className="mb-4">
                      <span className="text-4xl font-bold text-text-primary">{plan.price}</span>
                      {plan.period && <span className="text-text-secondary">{plan.period}</span>}
                    </div>
                    <p className="text-text-secondary">{plan.description}</p>
                  </div>
                  <ul className="space-y-4 mb-8">
                    {plan.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-start">
                        <ShieldCheck className="w-5 h-5 text-primary-500 mt-0.5 mr-3 flex-shrink-0" />
                        <span className="text-text-secondary">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  <Button 
                    variant={plan.popular ? "gradient" : "outline"}
                    size="lg"
                    className="w-full"
                    onClick={() => plan.cta === 'Contact Sales' ? navigate('/contact/sales') : navigate('/register')}
                    aria-label={`${plan.cta} for ${plan.name} plan - ${plan.price}${plan.period || ''}`}
                  >
                    {plan.cta}
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>
      {/* Testimonials Section */}
      <CustomerReviews 
        showControls={true}
        autoSlide={true}
        slideInterval={6000}
      />
      {/* Ultimate CTA and Footer Section */}
      <UltimateCtaFooterMinimal
        onStartTrial={handleStartTrial}
        onWatchDemo={handleWatchDemo}
      />
    </div>
  )
};
