import React, { useState, useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'
import { 
  Plus, 
  Search, 
  Activity, 
  AlertCircle, 
  CheckCircle, 
  Clock,
  Zap,
  TrendingUp,
  Users,
  Building,
  HeadphonesIcon,
  BarChart3
} from 'lucide-react'
import { cn } from '@/lib/utils'

import { CRM_PROVIDERS, CRMConnection } from '@/types/crmIntegration'
import CRMProviderCard from './CRMProviderCard'
import CRMConnectionCard from './CRMConnectionCard'
import CRMSetupWizard from './CRMSetupWizard'
import CRMConnectionDetails from './CRMConnectionDetails'

interface CRMIntegrationHubProps {
  connections?: CRMConnection[];
  onAddConnection?: (providerId: string) => void;
  onUpdateConnection?: (connectionId: string, updates: Partial<CRMConnection>) => void;
  onDeleteConnection?: (connectionId: string) => void;
  className?: string;
}

const CRMIntegrationHub: React.FC<CRMIntegrationHubProps> = ({
  connections = [],
  onAddConnection,
  onUpdateConnection,
  onDeleteConnection,
  className
}) => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState('overview');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [showSetupWizard, setShowSetupWizard] = useState(false);
  const [selectedProvider, setSelectedProvider] = useState<string | null>(null);
  const [selectedConnection, setSelectedConnection] = useState<string | null>(null);

  // 获取分类图标
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'crm':
        return <Users className="h-4 w-4" />;
      case 'marketing':
        return <TrendingUp className="h-4 w-4" />;
      case 'sales':
        return <Zap className="h-4 w-4" />;
      case 'support':
        return <HeadphonesIcon className="h-4 w-4" />;
      case 'analytics':
        return <BarChart3 className="h-4 w-4" />;
      default:
        return <Building className="h-4 w-4" />;
    }
  };

  // 过滤提供商
  const filteredProviders = useMemo(() => {
    return Object.values(CRM_PROVIDERS).filter(provider => {
      const matchesSearch = provider.displayName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           provider.description.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesCategory = selectedCategory === 'all' || provider.category === selectedCategory;
      return matchesSearch && matchesCategory;
    });
  }, [searchTerm, selectedCategory]);

  // 连接统计
  const connectionStats = useMemo(() => {
    const total = connections.length;
    const connected = connections.filter(c => c.status === 'connected').length;
    const errors = connections.filter(c => c.status === 'error').length;
    const syncing = connections.filter(c => c.status === 'pending').length;

    return { total, connected, errors, syncing };
  }, [connections]);

  // 处理添加集成
  const handleAddIntegration = (providerId: string) => {
    setSelectedProvider(providerId);
    setShowSetupWizard(true);
  };

  // 处理查看连接详情
  const handleViewConnection = (connectionId: string) => {
    setSelectedConnection(connectionId);
    setActiveTab('connections');
  };

  return (
    <div className={cn("space-y-6", className)}>
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">{t('agents.crmIntegration.title')}</h2>
          <p className="text-muted-foreground">{t('agents.crmIntegration.subtitle')}</p>
        </div>
        <Button onClick={() => setShowSetupWizard(true)} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          {t('agents.crmIntegration.addIntegration')}
        </Button>
      </div>

      {/* 统计概览 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2">
              <Building className="h-4 w-4 text-blue-600" />
              <span className="text-sm font-medium">Total Integrations</span>
            </div>
            <div className="text-2xl font-bold mt-2">{connectionStats.total}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium">Connected</span>
            </div>
            <div className="text-2xl font-bold mt-2 text-green-600">{connectionStats.connected}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2">
              <AlertCircle className="h-4 w-4 text-red-600" />
              <span className="text-sm font-medium">Errors</span>
            </div>
            <div className="text-2xl font-bold mt-2 text-red-600">{connectionStats.errors}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-yellow-600" />
              <span className="text-sm font-medium">Syncing</span>
            </div>
            <div className="text-2xl font-bold mt-2 text-yellow-600">{connectionStats.syncing}</div>
          </CardContent>
        </Card>
      </div>

      {/* 主要内容区域 */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="available">Available Integrations</TabsTrigger>
          <TabsTrigger value="connections">My Connections</TabsTrigger>
          <TabsTrigger value="logs">Sync Logs</TabsTrigger>
        </TabsList>

        {/* 概览标签页 */}
        <TabsContent value="overview" className="space-y-6">
          {/* 最近连接 */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Connections</CardTitle>
              <CardDescription>Your most recently added integrations</CardDescription>
            </CardHeader>
            <CardContent>
              {connections.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {connections.slice(0, 6).map((connection) => (
                    <CRMConnectionCard
                      key={connection.id}
                      connection={connection}
                      provider={CRM_PROVIDERS[connection.providerId]}
                      onView={() => handleViewConnection(connection.id)}
                      onConfigure={() => {}}
                      onSync={() => {}}
                      compact
                    />
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Building className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No integrations yet</h3>
                  <p className="text-muted-foreground mb-4">
                    Connect your first CRM or business tool to get started
                  </p>
                  <Button onClick={() => setActiveTab('available')}>
                    Browse Integrations
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>

          {/* 推荐集成 */}
          <Card>
            <CardHeader>
              <CardTitle>Recommended Integrations</CardTitle>
              <CardDescription>Popular integrations that work great with iBuddy2</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {['salesforce', 'hubspot', 'zapier'].map((providerId) => {
                  const provider = CRM_PROVIDERS[providerId];
                  const isConnected = connections.some(c => c.providerId === providerId);
                  
                  return (
                    <CRMProviderCard
                      key={providerId}
                      provider={provider}
                      isConnected={isConnected}
                      onConnect={() => handleAddIntegration(providerId)}
                      compact
                    />
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 可用集成标签页 */}
        <TabsContent value="available" className="space-y-4">
          {/* 搜索和过滤 */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search integrations..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex gap-2">
              {['all', 'crm', 'marketing', 'sales', 'support', 'analytics'].map((category) => (
                <Button
                  key={category}
                  variant={selectedCategory === category ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedCategory(category)}
                  className="flex items-center gap-1"
                >
                  {getCategoryIcon(category)}
                  {t(`agents.crmIntegration.categories.${category}`)}
                </Button>
              ))}
            </div>
          </div>

          {/* 提供商网格 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredProviders.map((provider) => {
              const isConnected = connections.some(c => c.providerId === provider.id);
              
              return (
                <CRMProviderCard
                  key={provider.id}
                  provider={provider}
                  isConnected={isConnected}
                  onConnect={() => handleAddIntegration(provider.id)}
                />
              );
            })}
          </div>

          {filteredProviders.length === 0 && (
            <div className="text-center py-12">
              <Search className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No integrations found</h3>
              <p className="text-muted-foreground">
                Try adjusting your search or filter criteria
              </p>
            </div>
          )}
        </TabsContent>

        {/* 我的连接标签页 */}
        <TabsContent value="connections" className="space-y-4">
          {selectedConnection ? (
            <CRMConnectionDetails
              connectionId={selectedConnection}
              onBack={() => setSelectedConnection(null)}
              onUpdate={onUpdateConnection}
              onDelete={onDeleteConnection}
            />
          ) : (
            <div className="space-y-4">
              {connections.length > 0 ? (
                <div className="grid grid-cols-1 gap-4">
                  {connections.map((connection) => (
                    <CRMConnectionCard
                      key={connection.id}
                      connection={connection}
                      provider={CRM_PROVIDERS[connection.providerId]}
                      onView={() => setSelectedConnection(connection.id)}
                      onConfigure={() => {}}
                      onSync={() => {}}
                    />
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <Building className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No connections yet</h3>
                  <p className="text-muted-foreground mb-4">
                    Add your first integration to start syncing data
                  </p>
                  <Button onClick={() => setActiveTab('available')}>
                    Browse Integrations
                  </Button>
                </div>
              )}
            </div>
          )}
        </TabsContent>

        {/* 同步日志标签页 */}
        <TabsContent value="logs" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Sync Logs</CardTitle>
              <CardDescription>Recent synchronization activity across all connections</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                <Activity className="h-8 w-8 mx-auto mb-2" />
                <p>Sync logs will appear here once you have active connections</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 设置向导 */}
      {showSetupWizard && (
        <CRMSetupWizard
          providerId={selectedProvider}
          onComplete={(connection) => {
            setShowSetupWizard(false);
            setSelectedProvider(null);
            if (onAddConnection) {
              onAddConnection(connection.providerId);
            }
          }}
          onCancel={() => {
            setShowSetupWizard(false);
            setSelectedProvider(null);
          }}
        />
      )}
    </div>
  );
};

export default CRMIntegrationHub;
