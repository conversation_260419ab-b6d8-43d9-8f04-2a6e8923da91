@echo off
title ibuddy2 System Health Check
echo.
echo ================================================
echo    🏥 ibuddy2 系统健康检查
echo ================================================
echo.

echo 📋 检查系统环境...
echo.

REM 检查 Node.js
where node >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Node.js 已安装
    node --version
) else (
    echo ❌ Node.js 未安装
)

REM 检查 npm
where npm >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ npm 已安装
    npm --version
) else (
    echo ❌ npm 未安装
)

echo.
echo 📦 检查项目依赖...
echo.

REM 检查根目录
if exist "package.json" (
    echo ✅ 根目录 package.json 存在
    if exist "node_modules" (
        echo ✅ 根目录依赖已安装
    ) else (
        echo ⚠️ 根目录依赖未安装
    )
) else (
    echo ❌ 根目录 package.json 不存在
)

REM 检查客户端
if exist "client\package.json" (
    echo ✅ 客户端 package.json 存在
    if exist "client\node_modules" (
        echo ✅ 客户端依赖已安装
    ) else (
        echo ⚠️ 客户端依赖未安装
    )
) else (
    echo ❌ 客户端 package.json 不存在
)

REM 检查 API Gateway
if exist "api-gateway\package.json" (
    echo ✅ API Gateway package.json 存在
    if exist "api-gateway\node_modules" (
        echo ✅ API Gateway 依赖已安装
    ) else (
        echo ⚠️ API Gateway 依赖未安装
    )
) else (
    echo ❌ API Gateway package.json 不存在
)

REM 检查 Core Service
if exist "core-service\package.json" (
    echo ✅ Core Service package.json 存在
    if exist "core-service\node_modules" (
        echo ✅ Core Service 依赖已安装
    ) else (
        echo ⚠️ Core Service 依赖未安装
    )
) else (
    echo ❌ Core Service package.json 不存在
)

REM 检查 AI Service
if exist "ai-service\package.json" (
    echo ✅ AI Service package.json 存在
    if exist "ai-service\node_modules" (
        echo ✅ AI Service 依赖已安装
    ) else (
        echo ⚠️ AI Service 依赖未安装
    )
) else (
    echo ❌ AI Service package.json 不存在
)

echo.
echo 🌐 检查服务可用性...
echo.

REM 检查端口占用情况
netstat -an | find "3000" >nul 2>&1
if %errorlevel% == 0 (
    echo 🔧 端口 3000 (客户端) 已被占用
) else (
    echo ✅ 端口 3000 (客户端) 可用
)

netstat -an | find "3001" >nul 2>&1
if %errorlevel% == 0 (
    echo 🔧 端口 3001 (API Gateway) 已被占用
) else (
    echo ✅ 端口 3001 (API Gateway) 可用
)

netstat -an | find "3002" >nul 2>&1
if %errorlevel% == 0 (
    echo 🔧 端口 3002 (Core Service) 已被占用
) else (
    echo ✅ 端口 3002 (Core Service) 可用
)

netstat -an | find "3003" >nul 2>&1
if %errorlevel% == 0 (
    echo 🔧 端口 3003 (AI Service) 已被占用
) else (
    echo ✅ 端口 3003 (AI Service) 可用
)

echo.
echo 📁 检查关键文件...
echo.

if exist "client\src\App.tsx" (
    echo ✅ 客户端主文件存在
) else (
    echo ❌ 客户端主文件缺失
)

if exist "client\src\pages\DashboardHome.tsx" (
    echo ✅ 仪表板页面存在
) else (
    echo ❌ 仪表板页面缺失
)

if exist "client\src\components\common\RealTimeIndicator.tsx" (
    echo ✅ 实时指示器组件存在
) else (
    echo ❌ 实时指示器组件缺失
)

if exist "client\src\hooks\useEnhancedAnalytics.ts" (
    echo ✅ 增强分析Hook存在
) else (
    echo ❌ 增强分析Hook缺失
)

if exist "client\src\services\mockDataService.ts" (
    echo ✅ 数据模拟服务存在
) else (
    echo ❌ 数据模拟服务缺失
)

if exist "client\src\utils\exportHelpers.ts" (
    echo ✅ 导出工具存在
) else (
    echo ❌ 导出工具缺失
)

echo.
echo ================================================
echo    📊 健康检查完成
echo ================================================
echo.
echo 💡 如发现问题，请运行 start-dev.bat 自动修复依赖
echo.
pause 