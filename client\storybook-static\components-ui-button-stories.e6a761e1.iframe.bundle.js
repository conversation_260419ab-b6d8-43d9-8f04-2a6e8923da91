/*! For license information please see components-ui-button-stories.e6a761e1.iframe.bundle.js.LICENSE.txt */
(self.webpackChunkclient=self.webpackChunkclient||[]).push([[187],{"./node_modules/@radix-ui/react-compose-refs/dist/index.js":(module,__unused_webpack_exports,__webpack_require__)=>{"use strict";var mod,__create=Object.create,__defProp=Object.defineProperty,__getOwnPropDesc=Object.getOwnPropertyDescriptor,__getOwnPropNames=Object.getOwnPropertyNames,__getProtoOf=Object.getPrototypeOf,__hasOwnProp=Object.prototype.hasOwnProperty,__copyProps=(to,from,except,desc)=>{if(from&&"object"==typeof from||"function"==typeof from)for(let key of __getOwnPropNames(from))__hasOwnProp.call(to,key)||key===except||__defProp(to,key,{get:()=>from[key],enumerable:!(desc=__getOwnPropDesc(from,key))||desc.enumerable});return to},index_exports={};((target,all)=>{for(var name in all)__defProp(target,name,{get:all[name],enumerable:!0})})(index_exports,{composeRefs:()=>composeRefs,useComposedRefs:()=>useComposedRefs}),module.exports=(mod=index_exports,__copyProps(__defProp({},"__esModule",{value:!0}),mod));var React=((mod,isNodeMode,target)=>(target=null!=mod?__create(__getProtoOf(mod)):{},__copyProps(!isNodeMode&&mod&&mod.__esModule?target:__defProp(target,"default",{value:mod,enumerable:!0}),mod)))(__webpack_require__("./node_modules/react/index.js"));function setRef(ref,value){if("function"==typeof ref)return ref(value);null!=ref&&(ref.current=value)}function composeRefs(...refs){return node=>{let hasCleanup=!1;const cleanups=refs.map((ref=>{const cleanup=setRef(ref,node);return hasCleanup||"function"!=typeof cleanup||(hasCleanup=!0),cleanup}));if(hasCleanup)return()=>{for(let i=0;i<cleanups.length;i++){const cleanup=cleanups[i];"function"==typeof cleanup?cleanup():setRef(refs[i],null)}}}}function useComposedRefs(...refs){return React.useCallback(composeRefs(...refs),refs)}},"./node_modules/@radix-ui/react-slot/dist/index.js":(module,__unused_webpack_exports,__webpack_require__)=>{"use strict";var mod,__create=Object.create,__defProp=Object.defineProperty,__getOwnPropDesc=Object.getOwnPropertyDescriptor,__getOwnPropNames=Object.getOwnPropertyNames,__getProtoOf=Object.getPrototypeOf,__hasOwnProp=Object.prototype.hasOwnProperty,__copyProps=(to,from,except,desc)=>{if(from&&"object"==typeof from||"function"==typeof from)for(let key of __getOwnPropNames(from))__hasOwnProp.call(to,key)||key===except||__defProp(to,key,{get:()=>from[key],enumerable:!(desc=__getOwnPropDesc(from,key))||desc.enumerable});return to},index_exports={};((target,all)=>{for(var name in all)__defProp(target,name,{get:all[name],enumerable:!0})})(index_exports,{Root:()=>Slot,Slot:()=>Slot,Slottable:()=>Slottable,createSlot:()=>createSlot,createSlottable:()=>createSlottable}),module.exports=(mod=index_exports,__copyProps(__defProp({},"__esModule",{value:!0}),mod));var React=((mod,isNodeMode,target)=>(target=null!=mod?__create(__getProtoOf(mod)):{},__copyProps(!isNodeMode&&mod&&mod.__esModule?target:__defProp(target,"default",{value:mod,enumerable:!0}),mod)))(__webpack_require__("./node_modules/react/index.js")),import_react_compose_refs=__webpack_require__("./node_modules/@radix-ui/react-compose-refs/dist/index.js"),import_jsx_runtime=__webpack_require__("./node_modules/react/jsx-runtime.js");function createSlot(ownerName){const SlotClone=createSlotClone(ownerName),Slot2=React.forwardRef(((props,forwardedRef)=>{const{children,...slotProps}=props,childrenArray=React.Children.toArray(children),slottable=childrenArray.find(isSlottable);if(slottable){const newElement=slottable.props.children,newChildren=childrenArray.map((child=>child===slottable?React.Children.count(newElement)>1?React.Children.only(null):React.isValidElement(newElement)?newElement.props.children:null:child));return(0,import_jsx_runtime.jsx)(SlotClone,{...slotProps,ref:forwardedRef,children:React.isValidElement(newElement)?React.cloneElement(newElement,void 0,newChildren):null})}return(0,import_jsx_runtime.jsx)(SlotClone,{...slotProps,ref:forwardedRef,children})}));return Slot2.displayName=`${ownerName}.Slot`,Slot2}var Slot=createSlot("Slot");function createSlotClone(ownerName){const SlotClone=React.forwardRef(((props,forwardedRef)=>{const{children,...slotProps}=props,childrenRef=React.isValidElement(children)?function getElementRef(element){let getter=Object.getOwnPropertyDescriptor(element.props,"ref")?.get,mayWarn=getter&&"isReactWarning"in getter&&getter.isReactWarning;if(mayWarn)return element.ref;if(getter=Object.getOwnPropertyDescriptor(element,"ref")?.get,mayWarn=getter&&"isReactWarning"in getter&&getter.isReactWarning,mayWarn)return element.props.ref;return element.props.ref||element.ref}(children):void 0,ref=(0,import_react_compose_refs.useComposedRefs)(childrenRef,forwardedRef);if(React.isValidElement(children)){const props2=function mergeProps(slotProps,childProps){const overrideProps={...childProps};for(const propName in childProps){const slotPropValue=slotProps[propName],childPropValue=childProps[propName];/^on[A-Z]/.test(propName)?slotPropValue&&childPropValue?overrideProps[propName]=(...args)=>{const result=childPropValue(...args);return slotPropValue(...args),result}:slotPropValue&&(overrideProps[propName]=slotPropValue):"style"===propName?overrideProps[propName]={...slotPropValue,...childPropValue}:"className"===propName&&(overrideProps[propName]=[slotPropValue,childPropValue].filter(Boolean).join(" "))}return{...slotProps,...overrideProps}}(slotProps,children.props);return children.type!==React.Fragment&&(props2.ref=ref),React.cloneElement(children,props2)}return React.Children.count(children)>1?React.Children.only(null):null}));return SlotClone.displayName=`${ownerName}.SlotClone`,SlotClone}var SLOTTABLE_IDENTIFIER=Symbol("radix.slottable");function createSlottable(ownerName){const Slottable2=({children})=>(0,import_jsx_runtime.jsx)(import_jsx_runtime.Fragment,{children});return Slottable2.displayName=`${ownerName}.Slottable`,Slottable2.__radixId=SLOTTABLE_IDENTIFIER,Slottable2}var Slottable=createSlottable("Slottable");function isSlottable(child){return React.isValidElement(child)&&"function"==typeof child.type&&"__radixId"in child.type&&child.type.__radixId===SLOTTABLE_IDENTIFIER}},"./node_modules/class-variance-authority/dist/index.js":(__unused_webpack_module,exports,__webpack_require__)=>{"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),function _export(target,all){for(var name in all)Object.defineProperty(target,name,{enumerable:!0,get:all[name]})}(exports,{cva:function(){return cva},cx:function(){return cx}});const _clsx=__webpack_require__("./node_modules/clsx/dist/clsx.js"),falsyToString=value=>"boolean"==typeof value?`${value}`:0===value?"0":value,cx=_clsx.clsx,cva=(base,config)=>props=>{var _config_compoundVariants;if(null==(null==config?void 0:config.variants))return cx(base,null==props?void 0:props.class,null==props?void 0:props.className);const{variants,defaultVariants}=config,getVariantClassNames=Object.keys(variants).map((variant=>{const variantProp=null==props?void 0:props[variant],defaultVariantProp=null==defaultVariants?void 0:defaultVariants[variant];if(null===variantProp)return null;const variantKey=falsyToString(variantProp)||falsyToString(defaultVariantProp);return variants[variant][variantKey]})),propsWithoutUndefined=props&&Object.entries(props).reduce(((acc,param)=>{let[key,value]=param;return void 0===value||(acc[key]=value),acc}),{}),getCompoundVariantClassNames=null==config||null===(_config_compoundVariants=config.compoundVariants)||void 0===_config_compoundVariants?void 0:_config_compoundVariants.reduce(((acc,param)=>{let{class:cvClass,className:cvClassName,...compoundVariantOptions}=param;return Object.entries(compoundVariantOptions).every((param=>{let[key,value]=param;return Array.isArray(value)?value.includes({...defaultVariants,...propsWithoutUndefined}[key]):{...defaultVariants,...propsWithoutUndefined}[key]===value}))?[...acc,cvClass,cvClassName]:acc}),[]);return cx(base,getVariantClassNames,getCompoundVariantClassNames,null==props?void 0:props.class,null==props?void 0:props.className)}},"./node_modules/clsx/dist/clsx.js":module=>{function r(e){var o,t,f="";if("string"==typeof e||"number"==typeof e)f+=e;else if("object"==typeof e)if(Array.isArray(e)){var n=e.length;for(o=0;o<n;o++)e[o]&&(t=r(e[o]))&&(f&&(f+=" "),f+=t)}else for(t in e)e[t]&&(f&&(f+=" "),f+=t);return f}function e(){for(var e,o,t=0,f="",n=arguments.length;t<n;t++)(e=arguments[t])&&(o=r(e))&&(f&&(f+=" "),f+=o);return f}module.exports=e,module.exports.clsx=e},"./node_modules/lucide-react/dist/esm/createLucideIcon.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{"use strict";__webpack_require__.d(__webpack_exports__,{A:()=>createLucideIcon});var react=__webpack_require__("./node_modules/react/index.js");const toPascalCase=string=>{const camelCase=(string=>string.replace(/^([A-Z])|[\s-_]+(\w)/g,((match,p1,p2)=>p2?p2.toUpperCase():p1.toLowerCase())))(string);return camelCase.charAt(0).toUpperCase()+camelCase.slice(1)},mergeClasses=(...classes)=>classes.filter(((className,index,array)=>Boolean(className)&&""!==className.trim()&&array.indexOf(className)===index)).join(" ").trim(),hasA11yProp=props=>{for(const prop in props)if(prop.startsWith("aria-")||"role"===prop||"title"===prop)return!0};var defaultAttributes={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};const Icon=(0,react.forwardRef)((({color="currentColor",size=24,strokeWidth=2,absoluteStrokeWidth,className="",children,iconNode,...rest},ref)=>(0,react.createElement)("svg",{ref,...defaultAttributes,width:size,height:size,stroke:color,strokeWidth:absoluteStrokeWidth?24*Number(strokeWidth)/Number(size):strokeWidth,className:mergeClasses("lucide",className),...!children&&!hasA11yProp(rest)&&{"aria-hidden":"true"},...rest},[...iconNode.map((([tag,attrs])=>(0,react.createElement)(tag,attrs))),...Array.isArray(children)?children:[children]]))),createLucideIcon=(iconName,iconNode)=>{const Component=(0,react.forwardRef)((({className,...props},ref)=>{return(0,react.createElement)(Icon,{ref,iconNode,className:mergeClasses(`lucide-${string=toPascalCase(iconName),string.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()}`,`lucide-${iconName}`,className),...props});var string}));return Component.displayName=toPascalCase(iconName),Component}},"./src/components/ui/button.js":function(__unused_webpack_module,exports,__webpack_require__){var __assign=this&&this.__assign||function(){return __assign=Object.assign||function(t){for(var s,i=1,n=arguments.length;i<n;i++)for(var p in s=arguments[i])Object.prototype.hasOwnProperty.call(s,p)&&(t[p]=s[p]);return t},__assign.apply(this,arguments)},__rest=this&&this.__rest||function(s,e){var t={};for(var p in s)Object.prototype.hasOwnProperty.call(s,p)&&e.indexOf(p)<0&&(t[p]=s[p]);if(null!=s&&"function"==typeof Object.getOwnPropertySymbols){var i=0;for(p=Object.getOwnPropertySymbols(s);i<p.length;i++)e.indexOf(p[i])<0&&Object.prototype.propertyIsEnumerable.call(s,p[i])&&(t[p[i]]=s[p[i]])}return t};exports.__esModule=!0,exports.buttonVariants=exports.Button=void 0;var React=__webpack_require__("./node_modules/react/index.js"),react_slot_1=__webpack_require__("./node_modules/@radix-ui/react-slot/dist/index.js"),class_variance_authority_1=__webpack_require__("./node_modules/class-variance-authority/dist/index.js"),utils_1=__webpack_require__("./src/lib/utils.js"),buttonVariants=(0,class_variance_authority_1.cva)("inline-flex items-center justify-center rounded-md text-sm font-bold tracking-wide ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-violet-primary focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 text-violet-primary dark:text-violet-light",{variants:{variant:{default:"bg-violet-primary text-white hover:bg-violet-dark dark:bg-violet-primary dark:hover:bg-violet-dark",destructive:"bg-status-error text-white hover:bg-red-600 dark:bg-status-error dark:hover:bg-red-700",outline:"border-2 border-violet-light bg-white dark:border-violet-dark dark:bg-slate-900 hover:bg-violet-primary hover:text-white dark:hover:bg-violet-primary dark:hover:text-white font-semibold",secondary:"bg-violet-secondary text-white hover:bg-violet-secondary/90 dark:bg-violet-secondary dark:hover:bg-violet-secondary/80",ghost:"hover:bg-violet-light/20 hover:text-violet-dark dark:hover:bg-violet-dark/20 dark:hover:text-violet-light",link:"text-violet-primary underline-offset-4 hover:underline dark:text-violet-light",accent:"bg-status-success text-white hover:bg-status-success/90 dark:bg-status-success dark:hover:bg-status-success/80"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}});exports.buttonVariants=buttonVariants;var Button=React.forwardRef((function(_a,ref){var className=_a.className,variant=_a.variant,size=_a.size,_b=_a.asChild,asChild=void 0!==_b&&_b,isLoading=_a.isLoading,props=__rest(_a,["className","variant","size","asChild","isLoading"]),Comp=asChild?react_slot_1.Slot:"button",isDisabled=props.disabled||isLoading,restProps=__rest(props,["disabled"]),_c=React.useState(!1),isHovered=_c[0],setIsHovered=_c[1],inlineStyle="outline"===variant&&isHovered?{backgroundColor:"#8B5CF6",color:"white"}:{};return React.createElement(Comp,__assign({className:(0,utils_1.cn)(buttonVariants({variant,size,className})),ref,disabled:isDisabled,style:inlineStyle,onMouseEnter:function(){setIsHovered(!0)},onMouseLeave:function(){setIsHovered(!1)}},restProps))}));exports.Button=Button,Button.displayName="Button",Button.__docgenInfo={description:"Button component - Used to trigger actions\r\n\r\nFollows WAI-ARIA button design pattern, supports keyboard navigation and screen readers",methods:[],displayName:"Button"}},"./src/components/ui/button.stories.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{"use strict";__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{AsChild:()=>AsChild,Default:()=>Default,Destructive:()=>Destructive,Ghost:()=>Ghost,Icon:()=>Icon,Large:()=>Large,Link:()=>Link,Loading:()=>Loading,Outline:()=>Outline,Secondary:()=>Secondary,Small:()=>Small,WithIcon:()=>WithIcon,__namedExportsOrder:()=>__namedExportsOrder,default:()=>button_stories});var dist=__webpack_require__("./node_modules/@storybook/test/dist/index.mjs"),ui_button=__webpack_require__("./src/components/ui/button.js");const Mail=(0,__webpack_require__("./node_modules/lucide-react/dist/esm/createLucideIcon.js").A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]]);var jsx_runtime=__webpack_require__("./node_modules/react/jsx-runtime.js");const button_stories={title:"UI/Button",component:ui_button.Button,parameters:{layout:"centered"},tags:["autodocs"],argTypes:{variant:{control:{type:"select"},options:["default","destructive","outline","secondary","ghost","link"]},size:{control:{type:"select"},options:["default","sm","lg","icon"]},asChild:{control:"boolean"}},args:{onClick:(0,dist.fn)(),disabled:!1}},Default={args:{variant:"default",children:"Button"}},Destructive={args:{variant:"destructive",children:"Destructive"}},Outline={args:{variant:"outline",children:"Outline"}},Secondary={args:{variant:"secondary",children:"Secondary"}},Ghost={args:{variant:"ghost",children:"Ghost"}},Link={args:{variant:"link",children:"Link"}},Icon={args:{variant:"outline",size:"icon",children:(0,jsx_runtime.jsx)(Mail,{className:"h-4 w-4"}),"aria-label":"Send Email"}},WithIcon={args:{variant:"default",children:(0,jsx_runtime.jsxs)(jsx_runtime.Fragment,{children:[(0,jsx_runtime.jsx)(Mail,{className:"mr-2 h-4 w-4"})," Login with Email"]})}},Loading={args:{variant:"secondary",disabled:!0,children:(0,jsx_runtime.jsxs)(jsx_runtime.Fragment,{children:[(0,jsx_runtime.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,jsx_runtime.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,jsx_runtime.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Please wait"]}),"aria-busy":!0,"aria-live":"polite"}},Small={args:{size:"sm",children:"Small Button"}},Large={args:{size:"lg",children:"Large Button"}},AsChild={args:{asChild:!0,children:(0,jsx_runtime.jsx)("button",{children:"Login"})}},__namedExportsOrder=["Default","Destructive","Outline","Secondary","Ghost","Link","Icon","WithIcon","Loading","Small","Large","AsChild"];Default.parameters={...Default.parameters,docs:{...Default.parameters?.docs,source:{originalSource:"{\n  args: {\n    variant: 'default',\n    children: 'Button'\n  }\n}",...Default.parameters?.docs?.source}}},Destructive.parameters={...Destructive.parameters,docs:{...Destructive.parameters?.docs,source:{originalSource:"{\n  args: {\n    variant: 'destructive',\n    children: 'Destructive'\n  }\n}",...Destructive.parameters?.docs?.source}}},Outline.parameters={...Outline.parameters,docs:{...Outline.parameters?.docs,source:{originalSource:"{\n  args: {\n    variant: 'outline',\n    children: 'Outline'\n  }\n}",...Outline.parameters?.docs?.source}}},Secondary.parameters={...Secondary.parameters,docs:{...Secondary.parameters?.docs,source:{originalSource:"{\n  args: {\n    variant: 'secondary',\n    children: 'Secondary'\n  }\n}",...Secondary.parameters?.docs?.source}}},Ghost.parameters={...Ghost.parameters,docs:{...Ghost.parameters?.docs,source:{originalSource:"{\n  args: {\n    variant: 'ghost',\n    children: 'Ghost'\n  }\n}",...Ghost.parameters?.docs?.source}}},Link.parameters={...Link.parameters,docs:{...Link.parameters?.docs,source:{originalSource:"{\n  args: {\n    variant: 'link',\n    children: 'Link'\n  }\n}",...Link.parameters?.docs?.source}}},Icon.parameters={...Icon.parameters,docs:{...Icon.parameters?.docs,source:{originalSource:"{\n  args: {\n    variant: 'outline',\n    size: 'icon',\n    children: <Mail className=\"h-4 w-4\" />,\n    'aria-label': 'Send Email' // Add aria-label for icon buttons\n  }\n}",...Icon.parameters?.docs?.source}}},WithIcon.parameters={...WithIcon.parameters,docs:{...WithIcon.parameters?.docs,source:{originalSource:"{\n  args: {\n    variant: 'default',\n    children: <>\r\n        <Mail className=\"mr-2 h-4 w-4\" /> Login with Email\r\n      </>\n  }\n}",...WithIcon.parameters?.docs?.source}}},Loading.parameters={...Loading.parameters,docs:{...Loading.parameters?.docs,source:{originalSource:'{\n  args: {\n    variant: \'secondary\',\n    disabled: true,\n    // Typically loading state implies disabled\n    children: <>\r\n        {/* Replace with an actual LoadingSpinner component if available */}\r\n        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" // Example spinner\n      xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">\r\n          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>\r\n          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>\r\n        </svg>\r\n        Please wait\r\n      </>,\n    \'aria-busy\': true,\n    \'aria-live\': \'polite\'\n  }\n}',...Loading.parameters?.docs?.source}}},Small.parameters={...Small.parameters,docs:{...Small.parameters?.docs,source:{originalSource:"{\n  args: {\n    size: 'sm',\n    children: 'Small Button'\n  }\n}",...Small.parameters?.docs?.source}}},Large.parameters={...Large.parameters,docs:{...Large.parameters?.docs,source:{originalSource:"{\n  args: {\n    size: 'lg',\n    children: 'Large Button'\n  }\n}",...Large.parameters?.docs?.source}}},AsChild.parameters={...AsChild.parameters,docs:{...AsChild.parameters?.docs,source:{originalSource:"{\n  args: {\n    asChild: true,\n    children: <button>Login</button> // Use button for accessibility instead of invalid anchor\n  }\n}",...AsChild.parameters?.docs?.source}}}},"./src/lib/utils.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{"use strict";__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{absoluteUrl:()=>absoluteUrl,cn:()=>cn,formatPrice:()=>formatPrice});var clsx__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__("./node_modules/clsx/dist/clsx.mjs"),tailwind_merge__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("./node_modules/tailwind-merge/dist/bundle-mjs.mjs"),process=__webpack_require__("./node_modules/process/browser.js");function cn(){for(var _len=arguments.length,inputs=new Array(_len),_key=0;_key<_len;_key++)inputs[_key]=arguments[_key];return(0,tailwind_merge__WEBPACK_IMPORTED_MODULE_0__.QP)((0,clsx__WEBPACK_IMPORTED_MODULE_1__.$)(inputs))}function formatPrice(price){return new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(price)}function absoluteUrl(path){return`${process.env.NEXT_PUBLIC_APP_URL||""}${path}`}}}]);