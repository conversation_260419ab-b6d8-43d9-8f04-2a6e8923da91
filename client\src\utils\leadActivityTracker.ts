interface ActivityEvent {
  type: string;
  description?: string;
  metadata?: Record<string, any>;
  pageUrl?: string;
  timestamp?: number;
}

interface TrackingConfig {
  leadId?: string;
  agentId?: string;
  apiEndpoint?: string;
  enablePageTracking?: boolean;
  enableClickTracking?: boolean;
  enableFormTracking?: boolean;
  enableDownloadTracking?: boolean;
  enableVideoTracking?: boolean;
  debounceTime?: number;
}

class LeadActivityTracker {
  private config: TrackingConfig;
  private queue: ActivityEvent[] = [];
  private isOnline: boolean = navigator.onLine;
  private debounceTimer: NodeJS.Timeout | null = null;
  private sessionId: string;
  private startTime: number;

  constructor(config: TrackingConfig) {
    this.config = {
      enablePageTracking: true,
      enableClickTracking: true,
      enableFormTracking: true,
      enableDownloadTracking: true,
      enableVideoTracking: true,
      debounceTime: 1000,
      apiEndpoint: '/api/lead-generation/leads',
      ...config
    };

    this.sessionId = this.generateSessionId();
    this.startTime = Date.now();
    
    this.initializeTracking();
    this.setupEventListeners();
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private initializeTracking(): void {
    // 监听在线状态变化
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.flushQueue();
    });

    window.addEventListener('offline', () => {
      this.isOnline = false;
    });

    // 页面卸载时发送剩余数据
    window.addEventListener('beforeunload', () => {
      this.flushQueue(true);
    });

    // 页面可见性变化
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'visible') {
        this.track('page_focus', '页面获得焦点');
      } else {
        this.track('page_blur', '页面失去焦点');
      }
    });
  }

  private setupEventListeners(): void {
    // 页面浏览跟踪
    if (this.config.enablePageTracking) {
      this.trackPageView();
    }

    // 点击跟踪
    if (this.config.enableClickTracking) {
      this.setupClickTracking();
    }

    // 表单跟踪
    if (this.config.enableFormTracking) {
      this.setupFormTracking();
    }

    // 下载跟踪
    if (this.config.enableDownloadTracking) {
      this.setupDownloadTracking();
    }

    // 视频跟踪
    if (this.config.enableVideoTracking) {
      this.setupVideoTracking();
    }
  }

  private trackPageView(): void {
    this.track('page_view', '页面浏览', {
      url: window.location.href,
      title: document.title,
      referrer: document.referrer,
      userAgent: navigator.userAgent,
      screen: {
        width: window.screen?.width || 0,
        height: window.screen?.height || 0
      },
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight,
        screenWidth: window.screen?.width || 0,
        screenHeight: window.screen?.height || 0
      }
    });

    // 跟踪页面停留时间
    setInterval(() => {
      const timeOnPage = Math.floor((Date.now() - this.startTime) / 1000);
      if (timeOnPage > 0 && timeOnPage % 30 === 0) { // 每30秒记录一次
        this.track('time_on_page', `页面停留${timeOnPage}秒`, {
          timeOnPage,
          url: window.location.href
        });
      }
    }, 30000);
  }

  private setupClickTracking(): void {
    document.addEventListener('click', (event) => {
      const target = event.target as HTMLElement;
      const tagName = target.tagName.toLowerCase();
      
      // 跟踪重要元素的点击
      if (['a', 'button', 'input[type="submit"]', 'input[type="button"]'].includes(tagName) ||
          target.classList.contains('trackable') ||
          target.closest('[data-track="true"]')) {
        
        this.track('click', '元素点击', {
          element: tagName,
          text: target.textContent?.slice(0, 100),
          href: (target as HTMLAnchorElement).href,
          className: target.className,
          position: {
            x: event.clientX,
            y: event.clientY
          }
        });
      }
    });
  }

  private setupFormTracking(): void {
    // 表单开始填写
    document.addEventListener('focusin', (event) => {
      const target = event.target as HTMLElement;
      if (target.tagName.toLowerCase() === 'input' || target.tagName.toLowerCase() === 'textarea') {
        const form = target.closest('form');
        if (form) {
          this.track('form_start', '开始填写表单', {
            formId: form.id,
            formAction: (form as HTMLFormElement).action,
            fieldName: (target as HTMLInputElement).name,
            fieldType: (target as HTMLInputElement).type
          });
        }
      }
    });

    // 表单提交
    document.addEventListener('submit', (event) => {
      const form = event.target as HTMLFormElement;
      const formData = new FormData(form);
      const fields: Record<string, any> = {};
      
      formData.forEach((value, key) => {
        fields[key] = value;
      });

      this.track('form_submit', '表单提交', {
        formId: form.id,
        formAction: form.action,
        fieldCount: Object.keys(fields).length,
        fields: Object.keys(fields) // 只记录字段名，不记录值
      });
    });
  }

  private setupDownloadTracking(): void {
    document.addEventListener('click', (event) => {
      const target = event.target as HTMLAnchorElement;
      if (target.tagName.toLowerCase() === 'a' && target.href) {
        const url = new URL(target.href, window.location.origin);
        const extension = url.pathname.split('.').pop()?.toLowerCase();
        
        // 常见下载文件扩展名
        const downloadExtensions = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'zip', 'rar', 'mp4', 'mp3'];
        
        if (extension && downloadExtensions.includes(extension)) {
          this.track('download', '文件下载', {
            fileName: url.pathname.split('/').pop(),
            fileType: extension,
            fileUrl: target.href
          });
        }
      }
    });
  }

  private setupVideoTracking(): void {
    document.addEventListener('play', (event) => {
      const target = event.target as HTMLVideoElement;
      if (target.tagName.toLowerCase() === 'video') {
        this.track('video_play', '视频播放', {
          videoSrc: target.src,
          currentTime: target.currentTime,
          duration: target.duration
        });
      }
    }, true);

    document.addEventListener('pause', (event) => {
      const target = event.target as HTMLVideoElement;
      if (target.tagName.toLowerCase() === 'video') {
        this.track('video_pause', '视频暂停', {
          videoSrc: target.src,
          currentTime: target.currentTime,
          duration: target.duration
        });
      }
    }, true);

    document.addEventListener('ended', (event) => {
      const target = event.target as HTMLVideoElement;
      if (target.tagName.toLowerCase() === 'video') {
        this.track('video_ended', '视频结束', {
          videoSrc: target.src,
          duration: target.duration
        });
      }
    }, true);
  }

  public track(type: string, description?: string, metadata?: Record<string, any>): void {
    const event: ActivityEvent = {
      type,
      description,
      metadata,
      pageUrl: window.location.href,
      timestamp: Date.now()
    };

    this.queue.push(event);
    this.scheduleFlush();
  }

  private scheduleFlush(): void {
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
    }

    this.debounceTimer = setTimeout(() => {
      this.flushQueue();
    }, this.config.debounceTime || 1000);
  }

  private flushQueue(immediate: boolean = false): void {
    if (this.queue.length === 0) return;

    const events = [...this.queue];
    this.queue = [];

    if (this.isOnline) {
      this.sendActivities(events);
    } else {
      this.storeActivitiesLocally(events);
    }

    if (immediate && this.debounceTimer) {
      clearTimeout(this.debounceTimer);
      this.debounceTimer = null;
    }
  }

  private async sendActivities(events: ActivityEvent[]): Promise<void> {
    try {
      const response = await fetch(this.config.apiEndpoint || '/api/lead-generation/leads', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          leadId: this.config.leadId,
          agentId: this.config.agentId,
          sessionId: this.sessionId,
          activities: events
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
    } catch (error) {
      console.error('发送活动数据失败:', error);
      this.storeActivitiesLocally(events);
    }
  }

  private storeActivitiesLocally(events: ActivityEvent[]): void {
    try {
      const stored = localStorage.getItem('leadActivities');
      const activities = stored ? JSON.parse(stored) : [];
      activities.push(...events);
      
      // 限制存储的事件数量，避免localStorage过大
      if (activities.length > 1000) {
        activities.splice(0, activities.length - 1000);
      }
      
      localStorage.setItem('leadActivities', JSON.stringify(activities));
    } catch (error) {
      console.error('本地存储活动数据失败:', error);
    }
  }

  public setLeadId(leadId: string): void {
    this.config.leadId = leadId;
    this.sendStoredActivities();
  }

  private sendStoredActivities(): void {
    try {
      const stored = localStorage.getItem('leadActivities');
      if (stored) {
        const activities = JSON.parse(stored);
        if (activities.length > 0) {
          this.sendActivities(activities);
          localStorage.removeItem('leadActivities');
        }
      }
    } catch (error) {
      console.error('发送存储的活动数据失败:', error);
    }
  }

  public destroy(): void {
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
    }
    this.flushQueue(true);
  }
}

export default LeadActivityTracker;