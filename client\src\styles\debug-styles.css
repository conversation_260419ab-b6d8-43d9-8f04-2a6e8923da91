/* 🔍 样式调试工具 - 帮助识别UI效果覆盖问题 */

/* 添加边框来可视化元素边界 */
.debug-outline * {
  outline: 1px solid rgba(255, 0, 0, 0.3) !important;
  outline-offset: -1px !important;
}

/* 显示z-index层级 */
.debug-z-index::before {
  content: attr(data-z-index);
  position: absolute;
  top: 0;
  left: 0;
  background: rgba(255, 255, 0, 0.8);
  color: black;
  font-size: 10px;
  padding: 2px 4px;
  z-index: 9999;
}

/* 检查渐变背景是否被覆盖 */
.debug-gradient {
  background: linear-gradient(45deg, 
    rgba(255, 0, 0, 0.3) 0%, 
    rgba(0, 255, 0, 0.3) 50%, 
    rgba(0, 0, 255, 0.3) 100%) !important;
}

/* 强制显示背景效果 */
.force-bg-visible {
  background: conic-gradient(from 45deg, 
    rgba(139, 92, 246, 0.1) 0deg,
    rgba(168, 85, 247, 0.15) 120deg,
    rgba(147, 51, 234, 0.1) 240deg,
    rgba(139, 92, 246, 0.1) 360deg) !important;
  backdrop-filter: blur(1px) !important;
  border: 1px solid rgba(139, 92, 246, 0.2) !important;
}

/* 重置可能冲突的样式 */
.reset-conflicts {
  background: unset !important;
  background-color: unset !important;
  background-image: unset !important;
  border: unset !important;
  transform: unset !important;
}

/* 检查Framer Motion样式是否生效 */
.debug-motion[data-framer-appear-id] {
  border: 2px dashed orange !important;
}

/* 检查Tailwind类是否被覆盖 */
.debug-tailwind.bg-gradient-to-br {
  position: relative;
}

.debug-tailwind.bg-gradient-to-br::after {
  content: "✓ Tailwind gradient working";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 255, 0, 0.8);
  color: white;
  padding: 4px 8px;
  font-size: 12px;
  border-radius: 4px;
  pointer-events: none;
  z-index: 9999;
} 