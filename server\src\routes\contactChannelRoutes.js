const express = require('express');
const contactChannelController = require('../controllers/contactChannelController');
const router = express.Router();

// 获取特定客户的所有联系渠道
router.get('/client/:clientId', contactChannelController.getClientContactChannels);

// 获取、更新和删除特定联系渠道
router.get('/:id', contactChannelController.getContactChannelById);
router.put('/:id', contactChannelController.updateContactChannel);
router.delete('/:id', contactChannelController.deleteContactChannel);

// 创建新的联系渠道
router.post('/', contactChannelController.createContactChannel);

// 设置主要联系渠道
router.patch('/:id/set-primary', contactChannelController.setPrimaryContactChannel);

// 搜索联系渠道
router.get('/search', contactChannelController.searchContactChannels);

module.exports = router; 