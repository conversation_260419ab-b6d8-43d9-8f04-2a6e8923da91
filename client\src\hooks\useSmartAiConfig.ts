import { useState, useCallback, useEffect, useMemo } from 'react'
import { debounce } from 'lodash'

export interface SmartAiConfig {
  // 基础配置
  agentName?: string;
  systemPrompt?: string;
  responseStyle?: 'professional' | 'friendly' | 'casual'
  
  // 行为配置
  temperature?: number;
  maxTokens?: number;
  humanEscalationThreshold?: number;
  responseMode?: 'direct' | 'draft' | 'collaborative'
  
  // 高级配置
  serviceCategories?: string;
  priorityKeywords?: string;
  knowledgeBaseSources?: 'integrated' | 'external' | 'hybrid'
  collectCustomerSatisfaction?: boolean;
  logConversationsForTraining?: boolean;
  enableMultilingualSupport?: boolean;
  
  // 消息模板
  responseTemplate?: string;
  fallbackMessage?: string;
  humanEscalationMessage?: string
}

export interface ValidationResult {
  isValid: boolean;
  errors: Record<string, string>;
  warnings: Record<string, string>
}

export interface SmartSuggestion {
  type: 'info' | 'warning' | 'error'
  message: string;
  field?: string;
  action?: () => void
}

export const useSmartAiConfig = (initialConfig: Partial<SmartAiConfig> = {}) => {
  const [config, setConfig] = useState<SmartAiConfig>(initialConfig);
  const [isDirty, setIsDirty] = useState(false);
  const [isValidating, setIsValidating] = useState(false);
  const [validationResult, setValidationResult] = useState<ValidationResult>({
    isValid: true,
    errors: {},
    warnings: {}
  });

  // 更新配置的函数
  const updateConfig = useCallback((key: string, value: any) => {
    setConfig(prev => {
      const newConfig = { ...prev, [key]: value };
      setIsDirty(true);
      return newConfig
    });
  }, []);

  // 批量更新配置
  const updateMultipleConfig = useCallback((updates: Partial<SmartAiConfig>) => {
    setConfig(prev => {
      const newConfig = { ...prev, ...updates };
      setIsDirty(true);
      return newConfig
    });
  }, []);

  // 重置配置
  const resetConfig = useCallback(() => {
    setConfig(initialConfig);
    setIsDirty(false);
    setValidationResult({ isValid: true, errors: {}, warnings: {} })
  }, [initialConfig]);

  // 配置验证逻辑
  const validateConfig = useCallback((configToValidate: SmartAiConfig): ValidationResult => {
    const errors: Record<string, string> = {};
    const warnings: Record<string, string> = {};

    // 必填字段验证
    if (!configToValidate.systemPrompt?.trim()) {
      errors.systemPrompt = 'System prompt is required'
    }

    if (!configToValidate.agentName?.trim()) {
      errors.agentName = 'Agent name is required'
    }

    // 数值范围验证
    if (configToValidate.temperature !== undefined) {
      if (configToValidate.temperature < 0 || configToValidate.temperature > 1) {
        errors.temperature = 'Temperature must be between 0 and 1'
      } else if (configToValidate.temperature > 0.8) {
        warnings.temperature = 'High temperature may result in inconsistent responses'
      }
    }

    if (configToValidate.maxTokens !== undefined) {
      if (configToValidate.maxTokens < 50 || configToValidate.maxTokens > 4000) {
        errors.maxTokens = 'Max tokens must be between 50 and 4000'
      }
    }

    if (configToValidate.humanEscalationThreshold !== undefined) {
      if (configToValidate.humanEscalationThreshold < 0 || configToValidate.humanEscalationThreshold > 1) {
        errors.humanEscalationThreshold = 'Escalation threshold must be between 0 and 1'
      }
    }

    // 内容长度验证
    if (configToValidate.systemPrompt && configToValidate.systemPrompt.length > 2000) {
      warnings.systemPrompt = 'System prompt is quite long, consider shortening for better performance'
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors,
      warnings
    }
  }, []);

  // 防抖验证
  const debouncedValidation = useMemo(
    () => debounce((configToValidate: SmartAiConfig) => {
      setIsValidating(true);
      const result = validateConfig(configToValidate);
      setValidationResult(result);
      setIsValidating(false)
    }, 300),
    [validateConfig]
  );

  // 当配置改变时触发验证
  useEffect(() => {
    debouncedValidation(config);
    return () => {
      debouncedValidation.cancel()
    }
  }, [config, debouncedValidation]);

  // 智能建议生成
  const smartSuggestions = useMemo((): SmartSuggestion[] => {
    const suggestions: SmartSuggestion[] = [];

    // 基于验证结果生成建议
    Object.entries(validationResult.errors).forEach(([field, message]) => {
      suggestions.push({
        type: 'error',
        message,
        field
      })
    });

    Object.entries(validationResult.warnings).forEach(([field, message]) => {
      suggestions.push({
        type: 'warning',
        message,
        field
      })
    });

    // 智能优化建议
    if (!config.serviceCategories) {
      suggestions.push({
        type: 'info',
        message: 'Consider adding service categories to improve query routing',
        field: 'serviceCategories'
      })
    }

    if (!config.priorityKeywords) {
      suggestions.push({
        type: 'info',
        message: 'Add priority keywords to identify urgent customer queries',
        field: 'priorityKeywords'
      })
    }

    if (config.temperature && config.temperature < 0.2) {
      suggestions.push({
        type: 'info',
        message: 'Very low creativity setting may result in repetitive responses',
        field: 'temperature'
      })
    }

    if (config.humanEscalationThreshold && config.humanEscalationThreshold > 0.9) {
      suggestions.push({
        type: 'warning',
        message: 'High escalation threshold may prevent necessary human handoffs',
        field: 'humanEscalationThreshold'
      })
    }

    return suggestions
  }, [config, validationResult]);

  // 配置完成度计算
  const completionPercentage = useMemo(() => {
    const requiredFields = ['agentName', 'systemPrompt', 'responseStyle'];
    const optionalFields = ['temperature', 'maxTokens', 'humanEscalationThreshold'];
    
    const requiredCompleted = requiredFields.filter(field => 
      config[field as keyof SmartAiConfig] !== undefined && 
      config[field as keyof SmartAiConfig] !== ''
    ).length;
    
    const optionalCompleted = optionalFields.filter(field => 
      config[field as keyof SmartAiConfig] !== undefined
    ).length;

    const requiredWeight = 0.7;
    const optionalWeight = 0.3;
    
    const requiredScore = (requiredCompleted / requiredFields.length) * requiredWeight;
    const optionalScore = (optionalCompleted / optionalFields.length) * optionalWeight;
    
    return Math.round((requiredScore + optionalScore) * 100)
  }, [config]);

  // 预设配置应用
  const applyPreset = useCallback((preset: 'customer_service' | 'sales_support' | 'technical_support') => {
    const presets = {
      customer_service: {
        systemPrompt: 'You are a professional customer service representative. Please maintain a polite, professional, and concise tone.',
        responseStyle: 'professional' as const,
        temperature: 0.3,
        maxTokens: 200,
        humanEscalationThreshold: 0.8,
        responseMode: 'direct' as const
      },
      sales_support: {
        systemPrompt: 'You are a friendly sales consultant focused on understanding customer needs and providing suitable product recommendations.',
        responseStyle: 'friendly' as const,
        temperature: 0.5,
        maxTokens: 300,
        humanEscalationThreshold: 0.6,
        responseMode: 'direct' as const
      },
      technical_support: {
        systemPrompt: 'You are a technical support expert providing accurate technical solutions and detailed operational guidance.',
        responseStyle: 'professional' as const,
        temperature: 0.2,
        maxTokens: 400,
        humanEscalationThreshold: 0.9,
        responseMode: 'collaborative' as const
      }
    };

    const presetConfig = presets[preset];
    if (presetConfig) {
      updateMultipleConfig(presetConfig);
    }
  }, [updateMultipleConfig]);

  // 导出配置为JSON
  const exportConfig = useCallback(() => {
    return JSON.stringify(config, null, 2)
  }, [config]);

  // 从JSON导入配置
  const importConfig = useCallback((jsonString: string) => {
    try {
      const importedConfig = JSON.parse(jsonString);
      updateMultipleConfig(importedConfig);
      return { success: true }
    } catch (error) {
      return { success: false, error: 'Invalid JSON format' }
    }
  }, [updateMultipleConfig]);

  return {
    // 状态
    config,
    isDirty,
    isValidating,
    validationResult,
    smartSuggestions,
    completionPercentage,
    
    // 操作函数
    updateConfig,
    updateMultipleConfig,
    resetConfig,
    validateConfig,
    applyPreset,
    exportConfig,
    importConfig,
    
    // 计算属性
    isValid: validationResult.isValid,
    hasErrors: Object.keys(validationResult.errors).length > 0,
    hasWarnings: Object.keys(validationResult.warnings).length > 0
  }
};

export default useSmartAiConfig;
