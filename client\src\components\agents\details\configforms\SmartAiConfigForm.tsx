import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  Lightbulb, 
  Settings, 
  Zap, 
  Brain, 
  MessageSquare, 
  CheckCircle,
  ArrowRight,
  HelpCircle,
  Sparkles
} from 'lucide-react'

interface SmartAiConfigFormProps {
  config: Record<string, any>;
  onConfigChange: (key: string, value: any) => void;
}

// 业务场景模板
const BUSINESS_SCENARIOS = [
  {
    id: 'customer_service', name: '客户服务', description: '处理客户咨询、投诉和售后问题', icon: MessageSquare, color: 'bg-blue-500', defaults: {
      systemPrompt: '你是一位专业的客户服务代表，请保持礼貌、专业和简洁的语调。',
      responseMode: 'direct', temperature: 0.3,
      maxTokens: 200,
      humanEscalationThreshold: 0.8
    }
  },
  {
    id: 'sales_support', name: '销售支持', description: '产品介绍、价格咨询和销售引导', icon: Zap, color: 'bg-green-500', defaults: {
      systemPrompt: '你是一位友好的销售顾问，专注于了解客户需求并提供合适的产品建议。',
      responseMode: 'direct', temperature: 0.5,
      maxTokens: 300,
      humanEscalationThreshold: 0.6
    }
  },
  {
    id: 'technical_support', name: '技术支持', description: '技术问题解答和故障排除', icon: Settings, color: 'bg-purple-500', defaults: {
      systemPrompt: '你是一位技术支持专家，提供准确的技术解决方案和详细的操作指导。',
      responseMode: 'collaborative', temperature: 0.2,
      maxTokens: 400,
      humanEscalationThreshold: 0.9
    }
  }
];

// 设置步骤定义
const SETUP_STEPS = [
  { id: 'scenario', title: '选择场景', description: '选择最符合您业务的应用场景' },
  { id: 'basic', title: '基础设置', description: '配置AI助手的基本信息' },
  { id: 'behavior', title: '行为调优', description: '调整AI的回复风格和行为' },
  { id: 'advanced', title: '高级选项', description: '专业用户的高级配置' }
];

const SmartAiConfigForm: React.FC<SmartAiConfigFormProps> = ({ config, onConfigChange }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [selectedScenario, setSelectedScenario] = useState<string | null>(null);
  const [isSmartMode, setIsSmartMode] = useState(true);
  const [setupProgress, setSetupProgress] = useState(0);

  // 计算设置完成度
  useEffect(() => {
    const requiredFields = ['systemPrompt', 'responseMode', 'temperature'];
    const completedFields = requiredFields.filter(field => config[field]);
    setSetupProgress((completedFields.length / requiredFields.length) * 100);
  }, [config]);

  // 应用场景模板
  const applyScenarioTemplate = (scenarioId: string) => {
    const scenario = BUSINESS_SCENARIOS.find(s => s.id === scenarioId);
    if (scenario) {
      Object.entries(scenario.defaults).forEach(([key, value]) => {
        onConfigChange(key, value);
      });
      setSelectedScenario(scenarioId);
      setCurrentStep(1);
    }
  };

  // 智能建议系统
  const getSmartSuggestions = () => {
    const suggestions = [];
    
    if (!config.systemPrompt) {
      suggestions.push({
        type: 'warning', message: '建议设置系统提示词来定义AI的角色和行为'
      });
    }
    
    if (config.temperature > 0.8) {
      suggestions.push({
        type: 'info', message: '创意度较高，回复可能更多样但一致性较低'
      });
    }
    
    return suggestions;
  };

  return (
    <div className="space-y-6">
      {/* 智能模式切换 */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <Brain className="h-5 w-5 text-purple-600" />
            AI助手配置
          </h3>
          <p className="text-sm text-muted-foreground">
            {isSmartMode ? '智能引导模式 - 简化配置流程' : '专家模式 - 完整配置选项'}
          </p>
        </div>
        <Button
          variant="outline"
          onClick={() => setIsSmartMode(!isSmartMode)}
          className="flex items-center gap-2"
        >
          {isSmartMode ? <Settings className="h-4 w-4" /> : <Sparkles className="h-4 w-4" />}
          {isSmartMode ? '切换到专家模式' : '切换到智能模式'}
        </Button>
      </div>

      {/* 进度指示器 */}
      {isSmartMode && (
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium">设置完成度</span>
              <span className="text-sm text-muted-foreground">{Math.round(setupProgress)}%</span>
            </div>
            <Progress value={setupProgress} className="h-2" />
          </CardContent>
        </Card>
      )}

      {isSmartMode ? (
        // 智能引导模式
        <Tabs value={SETUP_STEPS[currentStep]?.id} className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            {SETUP_STEPS.map((step, index) => (
              <TabsTrigger 
                key={step.id} 
                value={step.id}
                disabled={index > currentStep}
                className="flex items-center gap-2"
              >
                {index < currentStep && <CheckCircle className="h-4 w-4" />}
                <span className="hidden sm:inline">{step.title}</span>
                <span className="sm:hidden">{index + 1}</span>
              </TabsTrigger>
            ))}
          </TabsList>

          {/* 步骤1：场景选择 */}
          <TabsContent value="scenario" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Lightbulb className="h-5 w-5 text-yellow-500" />
                  选择业务场景
                </CardTitle>
                <CardDescription>
                  选择最符合您业务需求的场景，我们将为您预配置最佳设置
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {BUSINESS_SCENARIOS.map((scenario) => {
                    const Icon = scenario.icon;
                    return (
                      <Card 
                        key={scenario.id}
                        className={`cursor-pointer transition-all hover:shadow-md ${
                          selectedScenario === scenario.id ? 'ring-2 ring-purple-500' : ''
                        }`}
                        onClick={() => applyScenarioTemplate(scenario.id)}
                      >
                        <CardContent className="p-4">
                          <div className="flex items-center gap-3 mb-2">
                            <div className={`p-2 rounded-lg ${scenario.color} text-white`}>
                              <Icon className="h-4 w-4" />
                            </div>
                            <h4 className="font-medium">{scenario.name}</h4>
                          </div>
                          <p className="text-sm text-muted-foreground">
                            {scenario.description}
                          </p>
                        </CardContent>
                      </Card>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 步骤2：基础设置 */}
          <TabsContent value="basic" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>基础设置</CardTitle>
                <CardDescription>配置AI助手的基本信息和行为</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="agentName">助手名称</Label>
                  <Input
                    id="agentName"
                    value={config.agentName || ''}
                    onChange={(e) => onConfigChange('agentName', e.target.value)}
                    placeholder="例如：小助手、客服小美"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="systemPrompt">角色定义</Label>
                  <Textarea
                    id="systemPrompt"
                    value={config.systemPrompt || ''}
                    onChange={(e) => onConfigChange('systemPrompt', e.target.value)}
                    placeholder="描述AI助手的角色和职责..."
                    rows={3}
                  />
                  <p className="text-xs text-muted-foreground">
                    告诉AI它是谁，应该如何与客户交流
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="responseStyle">回复风格</Label>
                  <Select
                    value={config.responseStyle || 'professional'}
                    onValueChange={(value) => onConfigChange('responseStyle', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择回复风格" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="professional">专业正式</SelectItem>
                      <SelectItem value="friendly">友好亲切</SelectItem>
                      <SelectItem value="casual">轻松随意</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex justify-end">
                  <Button onClick={() => setCurrentStep(2)} className="flex items-center gap-2">
                    下一步 <ArrowRight className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      ) : (
        // 专家模式 - 显示所有配置选项
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>专家配置</CardTitle>
              <CardDescription>完整的AI配置选项</CardDescription>
            </CardHeader>
            <CardContent>
              {/* 原有的配置表单内容将在这里 */}
            </CardContent>
          </Card>
        </div>
      )}

      {/* 智能建议面板 */}
      {getSmartSuggestions().length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <HelpCircle className="h-5 w-5 text-blue-500" />
              智能建议
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {getSmartSuggestions().map((suggestion, index) => (
                <div key={index} className="flex items-start gap-2">
                  <Badge variant={suggestion.type === 'warning' ? 'destructive' : 'default'}>
                    {suggestion.type === 'warning' ? '注意' : '提示'}
                  </Badge>
                  <p className="text-sm">{suggestion.message}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
};

export default SmartAiConfigForm;
