import api from './api';
import { KPI, Report, Trend } from '@/types/dataInsight';

export const getKPIs = async (): Promise<KPI[]> => {
  const { data } = await api.get('/data-insights/kpis');
  return data;
};

export const getReport = async (
  reportName: string,
  params?: { startDate?: string; endDate?: string; segment?: string; }
): Promise<Report> => {
  const { data } = await api.get(`/data-insights/reports/${reportName}`, { params });
  return data;
};

export const getTrends = async (params?: { type?: string; period?: string; }): Promise<Trend[]> => {
  const { data } = await api.get('/data-insights/trends', { params });
  return data;
};

// 新增: 获取代理性能数据
export const getAgentPerformance = async (params?: { timeRange?: string; agentId?: string; }): Promise<any> => {
  try {
    const { data } = await api.get('/data-insights/agents-performance', { params });
    return data;
  } catch (error) {
    console.error('Error fetching agent performance data:', error);
    throw error;
  }
};

// 新增: 获取用户参与度数据
export const getUserEngagement = async (params?: { timeRange?: string; segmentBy?: string; }): Promise<any> => {
  try {
    const { data } = await api.get('/data-insights/user-engagement', { params });
    return data;
  } catch (error) {
    console.error('Error fetching user engagement data:', error);
    throw error;
  }
};

// 新增: 获取系统运营数据
export const getSystemOperations = async (params?: { timeRange?: string; metric?: string[]; }): Promise<any> => {
  try {
    const { data } = await api.get('/data-insights/system-operations', { params });
    return data;
  } catch (error) {
    console.error('Error fetching system operations data:', error);
    throw error;
  }
}; 