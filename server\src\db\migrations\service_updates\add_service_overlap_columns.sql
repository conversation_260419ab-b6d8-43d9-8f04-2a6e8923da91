-- 为 services 表添加特定类型的最大重叠数列
-- 使用匿名 PL/pgSQL 代码块确保幂等性（可重复执行）

DO $$ 
BEGIN
    -- 添加 max_overlap_walkin 列
    IF NOT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'services' 
        AND column_name = 'max_overlap_walkin'
    ) THEN
        ALTER TABLE public.services ADD COLUMN max_overlap_walkin INTEGER;
        COMMENT ON COLUMN public.services.max_overlap_walkin IS '现场服务预约的最大重叠次数';
        RAISE NOTICE '已添加 max_overlap_walkin 列';

        -- 设置初始值为现有的 max_overlap 值
        UPDATE public.services 
        SET max_overlap_walkin = max_overlap 
        WHERE max_overlap IS NOT NULL;
        
        RAISE NOTICE '已将 max_overlap 值复制到 max_overlap_walkin 列';
    ELSE
        RAISE NOTICE 'max_overlap_walkin 列已存在，跳过';
    END IF;

    -- 添加 max_overlap_onsite 列
    IF NOT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'services' 
        AND column_name = 'max_overlap_onsite'
    ) THEN
        ALTER TABLE public.services ADD COLUMN max_overlap_onsite INTEGER;
        COMMENT ON COLUMN public.services.max_overlap_onsite IS '上门服务预约的最大重叠次数';
        RAISE NOTICE '已添加 max_overlap_onsite 列';

        -- 设置初始值为现有的 max_overlap 值
        UPDATE public.services 
        SET max_overlap_onsite = max_overlap 
        WHERE max_overlap IS NOT NULL;
        
        RAISE NOTICE '已将 max_overlap 值复制到 max_overlap_onsite 列';
    ELSE
        RAISE NOTICE 'max_overlap_onsite 列已存在，跳过';
    END IF;
END $$; 