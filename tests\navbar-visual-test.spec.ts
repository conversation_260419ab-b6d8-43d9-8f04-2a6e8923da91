import { test, expect } from '@playwright/test';

test.describe('TubeLight Navbar Visual and Functional Tests', () => {
  test.beforeEach(async ({ page }) => {
    // 导航到首页
    await page.goto('http://localhost:3000');
    // 等待navbar加载
    await page.waitForSelector('[data-testid="navbar"], .fixed.top-6', { timeout: 10000 });
  });

  test('navbar应该正确渲染并居中显示', async ({ page }) => {
    // 检查navbar是否存在并正确定位
    const navbar = page.locator('.fixed.top-6');
    await expect(navbar).toBeVisible();

    // 检查navbar是否居中
    const navbarBox = await navbar.boundingBox();
    const viewportSize = page.viewportSize();
    
    if (navbarBox && viewportSize) {
      const navbarCenter = navbarBox.x + navbarBox.width / 2;
      const viewportCenter = viewportSize.width / 2;
      const centerDiff = Math.abs(navbarCenter - viewportCenter);
      
      // 允许小的偏差（5px内）
      expect(centerDiff).toBeLessThan(5);
    }

    // 截图验证视觉效果
    await page.screenshot({ 
      path: 'tests/screenshots/navbar-centered.png',
      fullPage: false,
      clip: { x: 0, y: 0, width: 1200, height: 150 }
    });
  });

  test('navbar项目应该正确显示并具有hover效果', async ({ page }) => {
    // 检查所有导航项
    const navItems = ['Home', 'Features', 'Pricing', 'About', 'Dashboard'];
    
    for (const item of navItems) {
      const navItem = page.locator(`button:has-text("${item}")`);
      await expect(navItem).toBeVisible();
    }

    // 测试hover效果
    const homeButton = page.locator('button:has-text("Home")');
    await homeButton.hover();
    
    // 等待hover动画完成
    await page.waitForTimeout(500);
    
    // 截图记录hover效果
    await page.screenshot({ 
      path: 'tests/screenshots/navbar-home-hover.png',
      fullPage: false,
      clip: { x: 0, y: 0, width: 1200, height: 150 }
    });
  });

  test('active状态应该显示亮点效果在文字中心', async ({ page }) => {
    // 等待页面加载完成
    await page.waitForLoadState('networkidle');
    
    // Home应该默认为active状态
    const homeButton = page.locator('button:has-text("Home")');
    await expect(homeButton).toHaveClass(/text-primary/);

    // 检查亮点元素是否存在
    const lampEffect = page.locator('[layoutid="lamp"]');
    await expect(lampEffect).toBeVisible();

    // 检查主题色亮点
    const highlight = page.locator('.absolute.-top-3.left-1\\/2');
    await expect(highlight).toBeVisible();

    // 截图记录active状态的亮点效果
    await page.screenshot({ 
      path: 'tests/screenshots/navbar-active-highlight.png',
      fullPage: false,
      clip: { x: 0, y: 0, width: 1200, height: 150 }
    });
  });

  test('点击Pricing应该滚动到定价section并更新active状态', async ({ page }) => {
    // 点击Pricing
    const pricingButton = page.locator('button:has-text("Pricing")');
    await pricingButton.click();

    // 等待滚动动画
    await page.waitForTimeout(1000);

    // 检查Pricing现在是active状态
    await expect(pricingButton).toHaveClass(/text-primary/);

    // 检查是否滚动到pricing section
    const pricingSection = page.locator('#pricing');
    await expect(pricingSection).toBeInViewport();

    // 截图记录Pricing active状态
    await page.screenshot({ 
      path: 'tests/screenshots/navbar-pricing-active.png',
      fullPage: false,
      clip: { x: 0, y: 0, width: 1200, height: 150 }
    });
  });

  test('暗黑模式切换应该正常工作', async ({ page }) => {
    // 找到暗黑模式切换按钮
    const darkModeToggle = page.locator('button[aria-label="Toggle dark mode"]');
    await expect(darkModeToggle).toBeVisible();

    // 点击切换暗黑模式
    await darkModeToggle.click();
    await page.waitForTimeout(500);

    // 检查文档是否添加了dark类
    const htmlElement = page.locator('html');
    await expect(htmlElement).toHaveClass(/dark/);

    // 截图记录暗黑模式
    await page.screenshot({ 
      path: 'tests/screenshots/navbar-dark-mode.png',
      fullPage: false,
      clip: { x: 0, y: 0, width: 1200, height: 150 }
    });

    // 切换回亮色模式
    await darkModeToggle.click();
    await page.waitForTimeout(500);
    
    await expect(htmlElement).not.toHaveClass(/dark/);
  });

  test('响应式设计：移动端显示图标', async ({ page }) => {
    // 设置移动端视口
    await page.setViewportSize({ width: 375, height: 667 });
    await page.reload();
    await page.waitForSelector('.fixed.top-6');

    // 在移动端，文字应该隐藏，图标应该显示
    const homeText = page.locator('button span.hidden.md\\:inline:has-text("Home")');
    const homeIcon = page.locator('button span.md\\:hidden svg');

    await expect(homeText).not.toBeVisible();
    await expect(homeIcon).toBeVisible();

    // 截图记录移动端效果
    await page.screenshot({ 
      path: 'tests/screenshots/navbar-mobile.png',
      fullPage: false,
      clip: { x: 0, y: 0, width: 375, height: 150 }
    });
  });

  test('navbar优先级：应该在最上层，不被其他元素覆盖', async ({ page }) => {
    // 检查navbar的z-index
    const navbar = page.locator('.fixed.top-6');
    const zIndex = await navbar.evaluate(el => window.getComputedStyle(el).zIndex);
    
    // z-[100] 应该转换为 z-index: 100
    expect(parseInt(zIndex)).toBeGreaterThanOrEqual(100);

    // 滚动页面检查navbar是否始终可见
    await page.evaluate(() => window.scrollTo(0, 1000));
    await page.waitForTimeout(500);
    
    await expect(navbar).toBeVisible();

    // 截图记录滚动后的navbar
    await page.screenshot({ 
      path: 'tests/screenshots/navbar-after-scroll.png',
      fullPage: false,
      clip: { x: 0, y: 0, width: 1200, height: 150 }
    });
  });

  test('主题色验证：亮点应该使用项目的紫色主题', async ({ page }) => {
    // 等待active状态
    await page.waitForLoadState('networkidle');
    
    // 检查亮点的颜色
    const highlight = page.locator('.absolute.-top-3.left-1\\/2.bg-primary');
    await expect(highlight).toBeVisible();

    // 获取计算后的颜色
    const backgroundColor = await highlight.evaluate(el => {
      return window.getComputedStyle(el).backgroundColor;
    });

    // 打印颜色信息用于调试
    console.log('亮点背景色:', backgroundColor);

    // 截图用于视觉验证
    await page.screenshot({ 
      path: 'tests/screenshots/navbar-theme-color.png',
      fullPage: false,
      clip: { x: 0, y: 0, width: 1200, height: 150 }
    });
  });
}); 