"use strict";(self.webpackChunkclient=self.webpackChunkclient||[]).push([[284],{"./node_modules/axios/lib/axios.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.d(__webpack_exports__,{A:()=>lib_axios});var common_utils_namespaceObject={};function bind(fn,thisArg){return function wrap(){return fn.apply(thisArg,arguments)}}__webpack_require__.r(common_utils_namespaceObject),__webpack_require__.d(common_utils_namespaceObject,{hasBrowserEnv:()=>hasBrowserEnv,hasStandardBrowserEnv:()=>hasStandardBrowserEnv,hasStandardBrowserWebWorkerEnv:()=>hasStandardBrowserWebWorkerEnv,navigator:()=>_navigator,origin:()=>origin});var process=__webpack_require__("./node_modules/process/browser.js");const{toString:utils_toString}=Object.prototype,{getPrototypeOf}=Object,{iterator,toStringTag}=Symbol,kindOf=(cache=Object.create(null),thing=>{const str=utils_toString.call(thing);return cache[str]||(cache[str]=str.slice(8,-1).toLowerCase())});var cache;const kindOfTest=type=>(type=type.toLowerCase(),thing=>kindOf(thing)===type),typeOfTest=type=>thing=>typeof thing===type,{isArray}=Array,isUndefined=typeOfTest("undefined");const isArrayBuffer=kindOfTest("ArrayBuffer");const isString=typeOfTest("string"),isFunction=typeOfTest("function"),isNumber=typeOfTest("number"),isObject=thing=>null!==thing&&"object"==typeof thing,isPlainObject=val=>{if("object"!==kindOf(val))return!1;const prototype=getPrototypeOf(val);return!(null!==prototype&&prototype!==Object.prototype&&null!==Object.getPrototypeOf(prototype)||toStringTag in val||iterator in val)},isDate=kindOfTest("Date"),isFile=kindOfTest("File"),isBlob=kindOfTest("Blob"),isFileList=kindOfTest("FileList"),isURLSearchParams=kindOfTest("URLSearchParams"),[isReadableStream,isRequest,isResponse,isHeaders]=["ReadableStream","Request","Response","Headers"].map(kindOfTest);function forEach(obj,fn,{allOwnKeys=!1}={}){if(null==obj)return;let i,l;if("object"!=typeof obj&&(obj=[obj]),isArray(obj))for(i=0,l=obj.length;i<l;i++)fn.call(null,obj[i],i,obj);else{const keys=allOwnKeys?Object.getOwnPropertyNames(obj):Object.keys(obj),len=keys.length;let key;for(i=0;i<len;i++)key=keys[i],fn.call(null,obj[key],key,obj)}}function findKey(obj,key){key=key.toLowerCase();const keys=Object.keys(obj);let _key,i=keys.length;for(;i-- >0;)if(_key=keys[i],key===_key.toLowerCase())return _key;return null}const _global="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:__webpack_require__.g,isContextDefined=context=>!isUndefined(context)&&context!==_global;const isTypedArray=(TypedArray="undefined"!=typeof Uint8Array&&getPrototypeOf(Uint8Array),thing=>TypedArray&&thing instanceof TypedArray);var TypedArray;const isHTMLForm=kindOfTest("HTMLFormElement"),utils_hasOwnProperty=(({hasOwnProperty})=>(obj,prop)=>hasOwnProperty.call(obj,prop))(Object.prototype),isRegExp=kindOfTest("RegExp"),reduceDescriptors=(obj,reducer)=>{const descriptors=Object.getOwnPropertyDescriptors(obj),reducedDescriptors={};forEach(descriptors,((descriptor,name)=>{let ret;!1!==(ret=reducer(descriptor,name,obj))&&(reducedDescriptors[name]=ret||descriptor)})),Object.defineProperties(obj,reducedDescriptors)};const isAsyncFn=kindOfTest("AsyncFunction"),_setImmediate=(setImmediateSupported="function"==typeof setImmediate,postMessageSupported=isFunction(_global.postMessage),setImmediateSupported?setImmediate:postMessageSupported?(token=`axios@${Math.random()}`,callbacks=[],_global.addEventListener("message",(({source,data})=>{source===_global&&data===token&&callbacks.length&&callbacks.shift()()}),!1),cb=>{callbacks.push(cb),_global.postMessage(token,"*")}):cb=>setTimeout(cb));var setImmediateSupported,postMessageSupported,token,callbacks;const asap="undefined"!=typeof queueMicrotask?queueMicrotask.bind(_global):void 0!==process&&process.nextTick||_setImmediate,utils={isArray,isArrayBuffer,isBuffer:function isBuffer(val){return null!==val&&!isUndefined(val)&&null!==val.constructor&&!isUndefined(val.constructor)&&isFunction(val.constructor.isBuffer)&&val.constructor.isBuffer(val)},isFormData:thing=>{let kind;return thing&&("function"==typeof FormData&&thing instanceof FormData||isFunction(thing.append)&&("formdata"===(kind=kindOf(thing))||"object"===kind&&isFunction(thing.toString)&&"[object FormData]"===thing.toString()))},isArrayBufferView:function isArrayBufferView(val){let result;return result="undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(val):val&&val.buffer&&isArrayBuffer(val.buffer),result},isString,isNumber,isBoolean:thing=>!0===thing||!1===thing,isObject,isPlainObject,isReadableStream,isRequest,isResponse,isHeaders,isUndefined,isDate,isFile,isBlob,isRegExp,isFunction,isStream:val=>isObject(val)&&isFunction(val.pipe),isURLSearchParams,isTypedArray,isFileList,forEach,merge:function merge(){const{caseless}=isContextDefined(this)&&this||{},result={},assignValue=(val,key)=>{const targetKey=caseless&&findKey(result,key)||key;isPlainObject(result[targetKey])&&isPlainObject(val)?result[targetKey]=merge(result[targetKey],val):isPlainObject(val)?result[targetKey]=merge({},val):isArray(val)?result[targetKey]=val.slice():result[targetKey]=val};for(let i=0,l=arguments.length;i<l;i++)arguments[i]&&forEach(arguments[i],assignValue);return result},extend:(a,b,thisArg,{allOwnKeys}={})=>(forEach(b,((val,key)=>{thisArg&&isFunction(val)?a[key]=bind(val,thisArg):a[key]=val}),{allOwnKeys}),a),trim:str=>str.trim?str.trim():str.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:content=>(65279===content.charCodeAt(0)&&(content=content.slice(1)),content),inherits:(constructor,superConstructor,props,descriptors)=>{constructor.prototype=Object.create(superConstructor.prototype,descriptors),constructor.prototype.constructor=constructor,Object.defineProperty(constructor,"super",{value:superConstructor.prototype}),props&&Object.assign(constructor.prototype,props)},toFlatObject:(sourceObj,destObj,filter,propFilter)=>{let props,i,prop;const merged={};if(destObj=destObj||{},null==sourceObj)return destObj;do{for(props=Object.getOwnPropertyNames(sourceObj),i=props.length;i-- >0;)prop=props[i],propFilter&&!propFilter(prop,sourceObj,destObj)||merged[prop]||(destObj[prop]=sourceObj[prop],merged[prop]=!0);sourceObj=!1!==filter&&getPrototypeOf(sourceObj)}while(sourceObj&&(!filter||filter(sourceObj,destObj))&&sourceObj!==Object.prototype);return destObj},kindOf,kindOfTest,endsWith:(str,searchString,position)=>{str=String(str),(void 0===position||position>str.length)&&(position=str.length),position-=searchString.length;const lastIndex=str.indexOf(searchString,position);return-1!==lastIndex&&lastIndex===position},toArray:thing=>{if(!thing)return null;if(isArray(thing))return thing;let i=thing.length;if(!isNumber(i))return null;const arr=new Array(i);for(;i-- >0;)arr[i]=thing[i];return arr},forEachEntry:(obj,fn)=>{const _iterator=(obj&&obj[iterator]).call(obj);let result;for(;(result=_iterator.next())&&!result.done;){const pair=result.value;fn.call(obj,pair[0],pair[1])}},matchAll:(regExp,str)=>{let matches;const arr=[];for(;null!==(matches=regExp.exec(str));)arr.push(matches);return arr},isHTMLForm,hasOwnProperty:utils_hasOwnProperty,hasOwnProp:utils_hasOwnProperty,reduceDescriptors,freezeMethods:obj=>{reduceDescriptors(obj,((descriptor,name)=>{if(isFunction(obj)&&-1!==["arguments","caller","callee"].indexOf(name))return!1;const value=obj[name];isFunction(value)&&(descriptor.enumerable=!1,"writable"in descriptor?descriptor.writable=!1:descriptor.set||(descriptor.set=()=>{throw Error("Can not rewrite read-only method '"+name+"'")}))}))},toObjectSet:(arrayOrString,delimiter)=>{const obj={},define=arr=>{arr.forEach((value=>{obj[value]=!0}))};return isArray(arrayOrString)?define(arrayOrString):define(String(arrayOrString).split(delimiter)),obj},toCamelCase:str=>str.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,(function replacer(m,p1,p2){return p1.toUpperCase()+p2})),noop:()=>{},toFiniteNumber:(value,defaultValue)=>null!=value&&Number.isFinite(value=+value)?value:defaultValue,findKey,global:_global,isContextDefined,isSpecCompliantForm:function isSpecCompliantForm(thing){return!!(thing&&isFunction(thing.append)&&"FormData"===thing[toStringTag]&&thing[iterator])},toJSONObject:obj=>{const stack=new Array(10),visit=(source,i)=>{if(isObject(source)){if(stack.indexOf(source)>=0)return;if(!("toJSON"in source)){stack[i]=source;const target=isArray(source)?[]:{};return forEach(source,((value,key)=>{const reducedValue=visit(value,i+1);!isUndefined(reducedValue)&&(target[key]=reducedValue)})),stack[i]=void 0,target}}return source};return visit(obj,0)},isAsyncFn,isThenable:thing=>thing&&(isObject(thing)||isFunction(thing))&&isFunction(thing.then)&&isFunction(thing.catch),setImmediate:_setImmediate,asap,isIterable:thing=>null!=thing&&isFunction(thing[iterator])};function AxiosError(message,code,config,request,response){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack,this.message=message,this.name="AxiosError",code&&(this.code=code),config&&(this.config=config),request&&(this.request=request),response&&(this.response=response,this.status=response.status?response.status:null)}utils.inherits(AxiosError,Error,{toJSON:function toJSON(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:utils.toJSONObject(this.config),code:this.code,status:this.status}}});const AxiosError_prototype=AxiosError.prototype,descriptors={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach((code=>{descriptors[code]={value:code}})),Object.defineProperties(AxiosError,descriptors),Object.defineProperty(AxiosError_prototype,"isAxiosError",{value:!0}),AxiosError.from=(error,code,config,request,response,customProps)=>{const axiosError=Object.create(AxiosError_prototype);return utils.toFlatObject(error,axiosError,(function filter(obj){return obj!==Error.prototype}),(prop=>"isAxiosError"!==prop)),AxiosError.call(axiosError,error.message,code,config,request,response),axiosError.cause=error,axiosError.name=error.name,customProps&&Object.assign(axiosError,customProps),axiosError};const core_AxiosError=AxiosError;function isVisitable(thing){return utils.isPlainObject(thing)||utils.isArray(thing)}function removeBrackets(key){return utils.endsWith(key,"[]")?key.slice(0,-2):key}function renderKey(path,key,dots){return path?path.concat(key).map((function each(token,i){return token=removeBrackets(token),!dots&&i?"["+token+"]":token})).join(dots?".":""):key}const predicates=utils.toFlatObject(utils,{},null,(function filter(prop){return/^is[A-Z]/.test(prop)}));const helpers_toFormData=function toFormData(obj,formData,options){if(!utils.isObject(obj))throw new TypeError("target must be an object");formData=formData||new FormData;const metaTokens=(options=utils.toFlatObject(options,{metaTokens:!0,dots:!1,indexes:!1},!1,(function defined(option,source){return!utils.isUndefined(source[option])}))).metaTokens,visitor=options.visitor||defaultVisitor,dots=options.dots,indexes=options.indexes,useBlob=(options.Blob||"undefined"!=typeof Blob&&Blob)&&utils.isSpecCompliantForm(formData);if(!utils.isFunction(visitor))throw new TypeError("visitor must be a function");function convertValue(value){if(null===value)return"";if(utils.isDate(value))return value.toISOString();if(!useBlob&&utils.isBlob(value))throw new core_AxiosError("Blob is not supported. Use a Buffer instead.");return utils.isArrayBuffer(value)||utils.isTypedArray(value)?useBlob&&"function"==typeof Blob?new Blob([value]):Buffer.from(value):value}function defaultVisitor(value,key,path){let arr=value;if(value&&!path&&"object"==typeof value)if(utils.endsWith(key,"{}"))key=metaTokens?key:key.slice(0,-2),value=JSON.stringify(value);else if(utils.isArray(value)&&function isFlatArray(arr){return utils.isArray(arr)&&!arr.some(isVisitable)}(value)||(utils.isFileList(value)||utils.endsWith(key,"[]"))&&(arr=utils.toArray(value)))return key=removeBrackets(key),arr.forEach((function each(el,index){!utils.isUndefined(el)&&null!==el&&formData.append(!0===indexes?renderKey([key],index,dots):null===indexes?key:key+"[]",convertValue(el))})),!1;return!!isVisitable(value)||(formData.append(renderKey(path,key,dots),convertValue(value)),!1)}const stack=[],exposedHelpers=Object.assign(predicates,{defaultVisitor,convertValue,isVisitable});if(!utils.isObject(obj))throw new TypeError("data must be an object");return function build(value,path){if(!utils.isUndefined(value)){if(-1!==stack.indexOf(value))throw Error("Circular reference detected in "+path.join("."));stack.push(value),utils.forEach(value,(function each(el,key){!0===(!(utils.isUndefined(el)||null===el)&&visitor.call(formData,el,utils.isString(key)?key.trim():key,path,exposedHelpers))&&build(el,path?path.concat(key):[key])})),stack.pop()}}(obj),formData};function encode(str){const charMap={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(str).replace(/[!'()~]|%20|%00/g,(function replacer(match){return charMap[match]}))}function AxiosURLSearchParams(params,options){this._pairs=[],params&&helpers_toFormData(params,this,options)}const AxiosURLSearchParams_prototype=AxiosURLSearchParams.prototype;AxiosURLSearchParams_prototype.append=function append(name,value){this._pairs.push([name,value])},AxiosURLSearchParams_prototype.toString=function toString(encoder){const _encode=encoder?function(value){return encoder.call(this,value,encode)}:encode;return this._pairs.map((function each(pair){return _encode(pair[0])+"="+_encode(pair[1])}),"").join("&")};const helpers_AxiosURLSearchParams=AxiosURLSearchParams;function buildURL_encode(val){return encodeURIComponent(val).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function buildURL(url,params,options){if(!params)return url;const _encode=options&&options.encode||buildURL_encode;utils.isFunction(options)&&(options={serialize:options});const serializeFn=options&&options.serialize;let serializedParams;if(serializedParams=serializeFn?serializeFn(params,options):utils.isURLSearchParams(params)?params.toString():new helpers_AxiosURLSearchParams(params,options).toString(_encode),serializedParams){const hashmarkIndex=url.indexOf("#");-1!==hashmarkIndex&&(url=url.slice(0,hashmarkIndex)),url+=(-1===url.indexOf("?")?"?":"&")+serializedParams}return url}const core_InterceptorManager=class InterceptorManager{constructor(){this.handlers=[]}use(fulfilled,rejected,options){return this.handlers.push({fulfilled,rejected,synchronous:!!options&&options.synchronous,runWhen:options?options.runWhen:null}),this.handlers.length-1}eject(id){this.handlers[id]&&(this.handlers[id]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(fn){utils.forEach(this.handlers,(function forEachHandler(h){null!==h&&fn(h)}))}},defaults_transitional={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},browser={isBrowser:!0,classes:{URLSearchParams:"undefined"!=typeof URLSearchParams?URLSearchParams:helpers_AxiosURLSearchParams,FormData:"undefined"!=typeof FormData?FormData:null,Blob:"undefined"!=typeof Blob?Blob:null},protocols:["http","https","file","blob","url","data"]},hasBrowserEnv="undefined"!=typeof window&&"undefined"!=typeof document,_navigator="object"==typeof navigator&&navigator||void 0,hasStandardBrowserEnv=hasBrowserEnv&&(!_navigator||["ReactNative","NativeScript","NS"].indexOf(_navigator.product)<0),hasStandardBrowserWebWorkerEnv="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"==typeof self.importScripts,origin=hasBrowserEnv&&window.location.href||"http://localhost",platform={...common_utils_namespaceObject,...browser};const helpers_formDataToJSON=function formDataToJSON(formData){function buildPath(path,value,target,index){let name=path[index++];if("__proto__"===name)return!0;const isNumericKey=Number.isFinite(+name),isLast=index>=path.length;if(name=!name&&utils.isArray(target)?target.length:name,isLast)return utils.hasOwnProp(target,name)?target[name]=[target[name],value]:target[name]=value,!isNumericKey;target[name]&&utils.isObject(target[name])||(target[name]=[]);return buildPath(path,value,target[name],index)&&utils.isArray(target[name])&&(target[name]=function arrayToObject(arr){const obj={},keys=Object.keys(arr);let i;const len=keys.length;let key;for(i=0;i<len;i++)key=keys[i],obj[key]=arr[key];return obj}(target[name])),!isNumericKey}if(utils.isFormData(formData)&&utils.isFunction(formData.entries)){const obj={};return utils.forEachEntry(formData,((name,value)=>{buildPath(function parsePropPath(name){return utils.matchAll(/\w+|\[(\w*)]/g,name).map((match=>"[]"===match[0]?"":match[1]||match[0]))}(name),value,obj,0)})),obj}return null};const defaults={transitional:defaults_transitional,adapter:["xhr","http","fetch"],transformRequest:[function transformRequest(data,headers){const contentType=headers.getContentType()||"",hasJSONContentType=contentType.indexOf("application/json")>-1,isObjectPayload=utils.isObject(data);isObjectPayload&&utils.isHTMLForm(data)&&(data=new FormData(data));if(utils.isFormData(data))return hasJSONContentType?JSON.stringify(helpers_formDataToJSON(data)):data;if(utils.isArrayBuffer(data)||utils.isBuffer(data)||utils.isStream(data)||utils.isFile(data)||utils.isBlob(data)||utils.isReadableStream(data))return data;if(utils.isArrayBufferView(data))return data.buffer;if(utils.isURLSearchParams(data))return headers.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),data.toString();let isFileList;if(isObjectPayload){if(contentType.indexOf("application/x-www-form-urlencoded")>-1)return function toURLEncodedForm(data,options){return helpers_toFormData(data,new platform.classes.URLSearchParams,Object.assign({visitor:function(value,key,path,helpers){return platform.isNode&&utils.isBuffer(value)?(this.append(key,value.toString("base64")),!1):helpers.defaultVisitor.apply(this,arguments)}},options))}(data,this.formSerializer).toString();if((isFileList=utils.isFileList(data))||contentType.indexOf("multipart/form-data")>-1){const _FormData=this.env&&this.env.FormData;return helpers_toFormData(isFileList?{"files[]":data}:data,_FormData&&new _FormData,this.formSerializer)}}return isObjectPayload||hasJSONContentType?(headers.setContentType("application/json",!1),function stringifySafely(rawValue,parser,encoder){if(utils.isString(rawValue))try{return(parser||JSON.parse)(rawValue),utils.trim(rawValue)}catch(e){if("SyntaxError"!==e.name)throw e}return(encoder||JSON.stringify)(rawValue)}(data)):data}],transformResponse:[function transformResponse(data){const transitional=this.transitional||defaults.transitional,forcedJSONParsing=transitional&&transitional.forcedJSONParsing,JSONRequested="json"===this.responseType;if(utils.isResponse(data)||utils.isReadableStream(data))return data;if(data&&utils.isString(data)&&(forcedJSONParsing&&!this.responseType||JSONRequested)){const strictJSONParsing=!(transitional&&transitional.silentJSONParsing)&&JSONRequested;try{return JSON.parse(data)}catch(e){if(strictJSONParsing){if("SyntaxError"===e.name)throw core_AxiosError.from(e,core_AxiosError.ERR_BAD_RESPONSE,this,null,this.response);throw e}}}return data}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:platform.classes.FormData,Blob:platform.classes.Blob},validateStatus:function validateStatus(status){return status>=200&&status<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};utils.forEach(["delete","get","head","post","put","patch"],(method=>{defaults.headers[method]={}}));const lib_defaults=defaults,ignoreDuplicateOf=utils.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),$internals=Symbol("internals");function normalizeHeader(header){return header&&String(header).trim().toLowerCase()}function normalizeValue(value){return!1===value||null==value?value:utils.isArray(value)?value.map(normalizeValue):String(value)}function matchHeaderValue(context,value,header,filter,isHeaderNameFilter){return utils.isFunction(filter)?filter.call(this,value,header):(isHeaderNameFilter&&(value=header),utils.isString(value)?utils.isString(filter)?-1!==value.indexOf(filter):utils.isRegExp(filter)?filter.test(value):void 0:void 0)}class AxiosHeaders{constructor(headers){headers&&this.set(headers)}set(header,valueOrRewrite,rewrite){const self=this;function setHeader(_value,_header,_rewrite){const lHeader=normalizeHeader(_header);if(!lHeader)throw new Error("header name must be a non-empty string");const key=utils.findKey(self,lHeader);(!key||void 0===self[key]||!0===_rewrite||void 0===_rewrite&&!1!==self[key])&&(self[key||_header]=normalizeValue(_value))}const setHeaders=(headers,_rewrite)=>utils.forEach(headers,((_value,_header)=>setHeader(_value,_header,_rewrite)));if(utils.isPlainObject(header)||header instanceof this.constructor)setHeaders(header,valueOrRewrite);else if(utils.isString(header)&&(header=header.trim())&&!/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(header.trim()))setHeaders((rawHeaders=>{const parsed={};let key,val,i;return rawHeaders&&rawHeaders.split("\n").forEach((function parser(line){i=line.indexOf(":"),key=line.substring(0,i).trim().toLowerCase(),val=line.substring(i+1).trim(),!key||parsed[key]&&ignoreDuplicateOf[key]||("set-cookie"===key?parsed[key]?parsed[key].push(val):parsed[key]=[val]:parsed[key]=parsed[key]?parsed[key]+", "+val:val)})),parsed})(header),valueOrRewrite);else if(utils.isObject(header)&&utils.isIterable(header)){let dest,key,obj={};for(const entry of header){if(!utils.isArray(entry))throw TypeError("Object iterator must return a key-value pair");obj[key=entry[0]]=(dest=obj[key])?utils.isArray(dest)?[...dest,entry[1]]:[dest,entry[1]]:entry[1]}setHeaders(obj,valueOrRewrite)}else null!=header&&setHeader(valueOrRewrite,header,rewrite);return this}get(header,parser){if(header=normalizeHeader(header)){const key=utils.findKey(this,header);if(key){const value=this[key];if(!parser)return value;if(!0===parser)return function parseTokens(str){const tokens=Object.create(null),tokensRE=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let match;for(;match=tokensRE.exec(str);)tokens[match[1]]=match[2];return tokens}(value);if(utils.isFunction(parser))return parser.call(this,value,key);if(utils.isRegExp(parser))return parser.exec(value);throw new TypeError("parser must be boolean|regexp|function")}}}has(header,matcher){if(header=normalizeHeader(header)){const key=utils.findKey(this,header);return!(!key||void 0===this[key]||matcher&&!matchHeaderValue(0,this[key],key,matcher))}return!1}delete(header,matcher){const self=this;let deleted=!1;function deleteHeader(_header){if(_header=normalizeHeader(_header)){const key=utils.findKey(self,_header);!key||matcher&&!matchHeaderValue(0,self[key],key,matcher)||(delete self[key],deleted=!0)}}return utils.isArray(header)?header.forEach(deleteHeader):deleteHeader(header),deleted}clear(matcher){const keys=Object.keys(this);let i=keys.length,deleted=!1;for(;i--;){const key=keys[i];matcher&&!matchHeaderValue(0,this[key],key,matcher,!0)||(delete this[key],deleted=!0)}return deleted}normalize(format){const self=this,headers={};return utils.forEach(this,((value,header)=>{const key=utils.findKey(headers,header);if(key)return self[key]=normalizeValue(value),void delete self[header];const normalized=format?function formatHeader(header){return header.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,((w,char,str)=>char.toUpperCase()+str))}(header):String(header).trim();normalized!==header&&delete self[header],self[normalized]=normalizeValue(value),headers[normalized]=!0})),this}concat(...targets){return this.constructor.concat(this,...targets)}toJSON(asStrings){const obj=Object.create(null);return utils.forEach(this,((value,header)=>{null!=value&&!1!==value&&(obj[header]=asStrings&&utils.isArray(value)?value.join(", "):value)})),obj}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map((([header,value])=>header+": "+value)).join("\n")}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(thing){return thing instanceof this?thing:new this(thing)}static concat(first,...targets){const computed=new this(first);return targets.forEach((target=>computed.set(target))),computed}static accessor(header){const accessors=(this[$internals]=this[$internals]={accessors:{}}).accessors,prototype=this.prototype;function defineAccessor(_header){const lHeader=normalizeHeader(_header);accessors[lHeader]||(!function buildAccessors(obj,header){const accessorName=utils.toCamelCase(" "+header);["get","set","has"].forEach((methodName=>{Object.defineProperty(obj,methodName+accessorName,{value:function(arg1,arg2,arg3){return this[methodName].call(this,header,arg1,arg2,arg3)},configurable:!0})}))}(prototype,_header),accessors[lHeader]=!0)}return utils.isArray(header)?header.forEach(defineAccessor):defineAccessor(header),this}}AxiosHeaders.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),utils.reduceDescriptors(AxiosHeaders.prototype,(({value},key)=>{let mapped=key[0].toUpperCase()+key.slice(1);return{get:()=>value,set(headerValue){this[mapped]=headerValue}}})),utils.freezeMethods(AxiosHeaders);const core_AxiosHeaders=AxiosHeaders;function transformData(fns,response){const config=this||lib_defaults,context=response||config,headers=core_AxiosHeaders.from(context.headers);let data=context.data;return utils.forEach(fns,(function transform(fn){data=fn.call(config,data,headers.normalize(),response?response.status:void 0)})),headers.normalize(),data}function isCancel(value){return!(!value||!value.__CANCEL__)}function CanceledError(message,config,request){core_AxiosError.call(this,null==message?"canceled":message,core_AxiosError.ERR_CANCELED,config,request),this.name="CanceledError"}utils.inherits(CanceledError,core_AxiosError,{__CANCEL__:!0});const cancel_CanceledError=CanceledError;function settle(resolve,reject,response){const validateStatus=response.config.validateStatus;response.status&&validateStatus&&!validateStatus(response.status)?reject(new core_AxiosError("Request failed with status code "+response.status,[core_AxiosError.ERR_BAD_REQUEST,core_AxiosError.ERR_BAD_RESPONSE][Math.floor(response.status/100)-4],response.config,response.request,response)):resolve(response)}const helpers_speedometer=function speedometer(samplesCount,min){samplesCount=samplesCount||10;const bytes=new Array(samplesCount),timestamps=new Array(samplesCount);let firstSampleTS,head=0,tail=0;return min=void 0!==min?min:1e3,function push(chunkLength){const now=Date.now(),startedAt=timestamps[tail];firstSampleTS||(firstSampleTS=now),bytes[head]=chunkLength,timestamps[head]=now;let i=tail,bytesCount=0;for(;i!==head;)bytesCount+=bytes[i++],i%=samplesCount;if(head=(head+1)%samplesCount,head===tail&&(tail=(tail+1)%samplesCount),now-firstSampleTS<min)return;const passed=startedAt&&now-startedAt;return passed?Math.round(1e3*bytesCount/passed):void 0}};const helpers_throttle=function throttle(fn,freq){let lastArgs,timer,timestamp=0,threshold=1e3/freq;const invoke=(args,now=Date.now())=>{timestamp=now,lastArgs=null,timer&&(clearTimeout(timer),timer=null),fn.apply(null,args)};return[(...args)=>{const now=Date.now(),passed=now-timestamp;passed>=threshold?invoke(args,now):(lastArgs=args,timer||(timer=setTimeout((()=>{timer=null,invoke(lastArgs)}),threshold-passed)))},()=>lastArgs&&invoke(lastArgs)]},progressEventReducer=(listener,isDownloadStream,freq=3)=>{let bytesNotified=0;const _speedometer=helpers_speedometer(50,250);return helpers_throttle((e=>{const loaded=e.loaded,total=e.lengthComputable?e.total:void 0,progressBytes=loaded-bytesNotified,rate=_speedometer(progressBytes);bytesNotified=loaded;listener({loaded,total,progress:total?loaded/total:void 0,bytes:progressBytes,rate:rate||void 0,estimated:rate&&total&&loaded<=total?(total-loaded)/rate:void 0,event:e,lengthComputable:null!=total,[isDownloadStream?"download":"upload"]:!0})}),freq)},progressEventDecorator=(total,throttled)=>{const lengthComputable=null!=total;return[loaded=>throttled[0]({lengthComputable,total,loaded}),throttled[1]]},asyncDecorator=fn=>(...args)=>utils.asap((()=>fn(...args))),isURLSameOrigin=platform.hasStandardBrowserEnv?((origin,isMSIE)=>url=>(url=new URL(url,platform.origin),origin.protocol===url.protocol&&origin.host===url.host&&(isMSIE||origin.port===url.port)))(new URL(platform.origin),platform.navigator&&/(msie|trident)/i.test(platform.navigator.userAgent)):()=>!0,cookies=platform.hasStandardBrowserEnv?{write(name,value,expires,path,domain,secure){const cookie=[name+"="+encodeURIComponent(value)];utils.isNumber(expires)&&cookie.push("expires="+new Date(expires).toGMTString()),utils.isString(path)&&cookie.push("path="+path),utils.isString(domain)&&cookie.push("domain="+domain),!0===secure&&cookie.push("secure"),document.cookie=cookie.join("; ")},read(name){const match=document.cookie.match(new RegExp("(^|;\\s*)("+name+")=([^;]*)"));return match?decodeURIComponent(match[3]):null},remove(name){this.write(name,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}};function buildFullPath(baseURL,requestedURL,allowAbsoluteUrls){let isRelativeUrl=!function isAbsoluteURL(url){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(url)}(requestedURL);return baseURL&&(isRelativeUrl||0==allowAbsoluteUrls)?function combineURLs(baseURL,relativeURL){return relativeURL?baseURL.replace(/\/?\/$/,"")+"/"+relativeURL.replace(/^\/+/,""):baseURL}(baseURL,requestedURL):requestedURL}const headersToObject=thing=>thing instanceof core_AxiosHeaders?{...thing}:thing;function mergeConfig(config1,config2){config2=config2||{};const config={};function getMergedValue(target,source,prop,caseless){return utils.isPlainObject(target)&&utils.isPlainObject(source)?utils.merge.call({caseless},target,source):utils.isPlainObject(source)?utils.merge({},source):utils.isArray(source)?source.slice():source}function mergeDeepProperties(a,b,prop,caseless){return utils.isUndefined(b)?utils.isUndefined(a)?void 0:getMergedValue(void 0,a,0,caseless):getMergedValue(a,b,0,caseless)}function valueFromConfig2(a,b){if(!utils.isUndefined(b))return getMergedValue(void 0,b)}function defaultToConfig2(a,b){return utils.isUndefined(b)?utils.isUndefined(a)?void 0:getMergedValue(void 0,a):getMergedValue(void 0,b)}function mergeDirectKeys(a,b,prop){return prop in config2?getMergedValue(a,b):prop in config1?getMergedValue(void 0,a):void 0}const mergeMap={url:valueFromConfig2,method:valueFromConfig2,data:valueFromConfig2,baseURL:defaultToConfig2,transformRequest:defaultToConfig2,transformResponse:defaultToConfig2,paramsSerializer:defaultToConfig2,timeout:defaultToConfig2,timeoutMessage:defaultToConfig2,withCredentials:defaultToConfig2,withXSRFToken:defaultToConfig2,adapter:defaultToConfig2,responseType:defaultToConfig2,xsrfCookieName:defaultToConfig2,xsrfHeaderName:defaultToConfig2,onUploadProgress:defaultToConfig2,onDownloadProgress:defaultToConfig2,decompress:defaultToConfig2,maxContentLength:defaultToConfig2,maxBodyLength:defaultToConfig2,beforeRedirect:defaultToConfig2,transport:defaultToConfig2,httpAgent:defaultToConfig2,httpsAgent:defaultToConfig2,cancelToken:defaultToConfig2,socketPath:defaultToConfig2,responseEncoding:defaultToConfig2,validateStatus:mergeDirectKeys,headers:(a,b,prop)=>mergeDeepProperties(headersToObject(a),headersToObject(b),0,!0)};return utils.forEach(Object.keys(Object.assign({},config1,config2)),(function computeConfigValue(prop){const merge=mergeMap[prop]||mergeDeepProperties,configValue=merge(config1[prop],config2[prop],prop);utils.isUndefined(configValue)&&merge!==mergeDirectKeys||(config[prop]=configValue)})),config}const resolveConfig=config=>{const newConfig=mergeConfig({},config);let contentType,{data,withXSRFToken,xsrfHeaderName,xsrfCookieName,headers,auth}=newConfig;if(newConfig.headers=headers=core_AxiosHeaders.from(headers),newConfig.url=buildURL(buildFullPath(newConfig.baseURL,newConfig.url,newConfig.allowAbsoluteUrls),config.params,config.paramsSerializer),auth&&headers.set("Authorization","Basic "+btoa((auth.username||"")+":"+(auth.password?unescape(encodeURIComponent(auth.password)):""))),utils.isFormData(data))if(platform.hasStandardBrowserEnv||platform.hasStandardBrowserWebWorkerEnv)headers.setContentType(void 0);else if(!1!==(contentType=headers.getContentType())){const[type,...tokens]=contentType?contentType.split(";").map((token=>token.trim())).filter(Boolean):[];headers.setContentType([type||"multipart/form-data",...tokens].join("; "))}if(platform.hasStandardBrowserEnv&&(withXSRFToken&&utils.isFunction(withXSRFToken)&&(withXSRFToken=withXSRFToken(newConfig)),withXSRFToken||!1!==withXSRFToken&&isURLSameOrigin(newConfig.url))){const xsrfValue=xsrfHeaderName&&xsrfCookieName&&cookies.read(xsrfCookieName);xsrfValue&&headers.set(xsrfHeaderName,xsrfValue)}return newConfig},xhr="undefined"!=typeof XMLHttpRequest&&function(config){return new Promise((function dispatchXhrRequest(resolve,reject){const _config=resolveConfig(config);let requestData=_config.data;const requestHeaders=core_AxiosHeaders.from(_config.headers).normalize();let onCanceled,uploadThrottled,downloadThrottled,flushUpload,flushDownload,{responseType,onUploadProgress,onDownloadProgress}=_config;function done(){flushUpload&&flushUpload(),flushDownload&&flushDownload(),_config.cancelToken&&_config.cancelToken.unsubscribe(onCanceled),_config.signal&&_config.signal.removeEventListener("abort",onCanceled)}let request=new XMLHttpRequest;function onloadend(){if(!request)return;const responseHeaders=core_AxiosHeaders.from("getAllResponseHeaders"in request&&request.getAllResponseHeaders());settle((function _resolve(value){resolve(value),done()}),(function _reject(err){reject(err),done()}),{data:responseType&&"text"!==responseType&&"json"!==responseType?request.response:request.responseText,status:request.status,statusText:request.statusText,headers:responseHeaders,config,request}),request=null}request.open(_config.method.toUpperCase(),_config.url,!0),request.timeout=_config.timeout,"onloadend"in request?request.onloadend=onloadend:request.onreadystatechange=function handleLoad(){request&&4===request.readyState&&(0!==request.status||request.responseURL&&0===request.responseURL.indexOf("file:"))&&setTimeout(onloadend)},request.onabort=function handleAbort(){request&&(reject(new core_AxiosError("Request aborted",core_AxiosError.ECONNABORTED,config,request)),request=null)},request.onerror=function handleError(){reject(new core_AxiosError("Network Error",core_AxiosError.ERR_NETWORK,config,request)),request=null},request.ontimeout=function handleTimeout(){let timeoutErrorMessage=_config.timeout?"timeout of "+_config.timeout+"ms exceeded":"timeout exceeded";const transitional=_config.transitional||defaults_transitional;_config.timeoutErrorMessage&&(timeoutErrorMessage=_config.timeoutErrorMessage),reject(new core_AxiosError(timeoutErrorMessage,transitional.clarifyTimeoutError?core_AxiosError.ETIMEDOUT:core_AxiosError.ECONNABORTED,config,request)),request=null},void 0===requestData&&requestHeaders.setContentType(null),"setRequestHeader"in request&&utils.forEach(requestHeaders.toJSON(),(function setRequestHeader(val,key){request.setRequestHeader(key,val)})),utils.isUndefined(_config.withCredentials)||(request.withCredentials=!!_config.withCredentials),responseType&&"json"!==responseType&&(request.responseType=_config.responseType),onDownloadProgress&&([downloadThrottled,flushDownload]=progressEventReducer(onDownloadProgress,!0),request.addEventListener("progress",downloadThrottled)),onUploadProgress&&request.upload&&([uploadThrottled,flushUpload]=progressEventReducer(onUploadProgress),request.upload.addEventListener("progress",uploadThrottled),request.upload.addEventListener("loadend",flushUpload)),(_config.cancelToken||_config.signal)&&(onCanceled=cancel=>{request&&(reject(!cancel||cancel.type?new cancel_CanceledError(null,config,request):cancel),request.abort(),request=null)},_config.cancelToken&&_config.cancelToken.subscribe(onCanceled),_config.signal&&(_config.signal.aborted?onCanceled():_config.signal.addEventListener("abort",onCanceled)));const protocol=function parseProtocol(url){const match=/^([-+\w]{1,25})(:?\/\/|:)/.exec(url);return match&&match[1]||""}(_config.url);protocol&&-1===platform.protocols.indexOf(protocol)?reject(new core_AxiosError("Unsupported protocol "+protocol+":",core_AxiosError.ERR_BAD_REQUEST,config)):request.send(requestData||null)}))},helpers_composeSignals=(signals,timeout)=>{const{length}=signals=signals?signals.filter(Boolean):[];if(timeout||length){let aborted,controller=new AbortController;const onabort=function(reason){if(!aborted){aborted=!0,unsubscribe();const err=reason instanceof Error?reason:this.reason;controller.abort(err instanceof core_AxiosError?err:new cancel_CanceledError(err instanceof Error?err.message:err))}};let timer=timeout&&setTimeout((()=>{timer=null,onabort(new core_AxiosError(`timeout ${timeout} of ms exceeded`,core_AxiosError.ETIMEDOUT))}),timeout);const unsubscribe=()=>{signals&&(timer&&clearTimeout(timer),timer=null,signals.forEach((signal=>{signal.unsubscribe?signal.unsubscribe(onabort):signal.removeEventListener("abort",onabort)})),signals=null)};signals.forEach((signal=>signal.addEventListener("abort",onabort)));const{signal}=controller;return signal.unsubscribe=()=>utils.asap(unsubscribe),signal}},streamChunk=function*(chunk,chunkSize){let len=chunk.byteLength;if(!chunkSize||len<chunkSize)return void(yield chunk);let end,pos=0;for(;pos<len;)end=pos+chunkSize,yield chunk.slice(pos,end),pos=end},readStream=async function*(stream){if(stream[Symbol.asyncIterator])return void(yield*stream);const reader=stream.getReader();try{for(;;){const{done,value}=await reader.read();if(done)break;yield value}}finally{await reader.cancel()}},trackStream=(stream,chunkSize,onProgress,onFinish)=>{const iterator=async function*(iterable,chunkSize){for await(const chunk of readStream(iterable))yield*streamChunk(chunk,chunkSize)}(stream,chunkSize);let done,bytes=0,_onFinish=e=>{done||(done=!0,onFinish&&onFinish(e))};return new ReadableStream({async pull(controller){try{const{done,value}=await iterator.next();if(done)return _onFinish(),void controller.close();let len=value.byteLength;if(onProgress){let loadedBytes=bytes+=len;onProgress(loadedBytes)}controller.enqueue(new Uint8Array(value))}catch(err){throw _onFinish(err),err}},cancel:reason=>(_onFinish(reason),iterator.return())},{highWaterMark:2})},isFetchSupported="function"==typeof fetch&&"function"==typeof Request&&"function"==typeof Response,isReadableStreamSupported=isFetchSupported&&"function"==typeof ReadableStream,encodeText=isFetchSupported&&("function"==typeof TextEncoder?(encoder=new TextEncoder,str=>encoder.encode(str)):async str=>new Uint8Array(await new Response(str).arrayBuffer()));var encoder;const test=(fn,...args)=>{try{return!!fn(...args)}catch(e){return!1}},supportsRequestStream=isReadableStreamSupported&&test((()=>{let duplexAccessed=!1;const hasContentType=new Request(platform.origin,{body:new ReadableStream,method:"POST",get duplex(){return duplexAccessed=!0,"half"}}).headers.has("Content-Type");return duplexAccessed&&!hasContentType})),supportsResponseStream=isReadableStreamSupported&&test((()=>utils.isReadableStream(new Response("").body))),resolvers={stream:supportsResponseStream&&(res=>res.body)};var res;isFetchSupported&&(res=new Response,["text","arrayBuffer","blob","formData","stream"].forEach((type=>{!resolvers[type]&&(resolvers[type]=utils.isFunction(res[type])?res=>res[type]():(_,config)=>{throw new core_AxiosError(`Response type '${type}' is not supported`,core_AxiosError.ERR_NOT_SUPPORT,config)})})));const resolveBodyLength=async(headers,body)=>{const length=utils.toFiniteNumber(headers.getContentLength());return null==length?(async body=>{if(null==body)return 0;if(utils.isBlob(body))return body.size;if(utils.isSpecCompliantForm(body)){const _request=new Request(platform.origin,{method:"POST",body});return(await _request.arrayBuffer()).byteLength}return utils.isArrayBufferView(body)||utils.isArrayBuffer(body)?body.byteLength:(utils.isURLSearchParams(body)&&(body+=""),utils.isString(body)?(await encodeText(body)).byteLength:void 0)})(body):length},knownAdapters={http:null,xhr,fetch:isFetchSupported&&(async config=>{let{url,method,data,signal,cancelToken,timeout,onDownloadProgress,onUploadProgress,responseType,headers,withCredentials="same-origin",fetchOptions}=resolveConfig(config);responseType=responseType?(responseType+"").toLowerCase():"text";let request,composedSignal=helpers_composeSignals([signal,cancelToken&&cancelToken.toAbortSignal()],timeout);const unsubscribe=composedSignal&&composedSignal.unsubscribe&&(()=>{composedSignal.unsubscribe()});let requestContentLength;try{if(onUploadProgress&&supportsRequestStream&&"get"!==method&&"head"!==method&&0!==(requestContentLength=await resolveBodyLength(headers,data))){let contentTypeHeader,_request=new Request(url,{method:"POST",body:data,duplex:"half"});if(utils.isFormData(data)&&(contentTypeHeader=_request.headers.get("content-type"))&&headers.setContentType(contentTypeHeader),_request.body){const[onProgress,flush]=progressEventDecorator(requestContentLength,progressEventReducer(asyncDecorator(onUploadProgress)));data=trackStream(_request.body,65536,onProgress,flush)}}utils.isString(withCredentials)||(withCredentials=withCredentials?"include":"omit");const isCredentialsSupported="credentials"in Request.prototype;request=new Request(url,{...fetchOptions,signal:composedSignal,method:method.toUpperCase(),headers:headers.normalize().toJSON(),body:data,duplex:"half",credentials:isCredentialsSupported?withCredentials:void 0});let response=await fetch(request);const isStreamResponse=supportsResponseStream&&("stream"===responseType||"response"===responseType);if(supportsResponseStream&&(onDownloadProgress||isStreamResponse&&unsubscribe)){const options={};["status","statusText","headers"].forEach((prop=>{options[prop]=response[prop]}));const responseContentLength=utils.toFiniteNumber(response.headers.get("content-length")),[onProgress,flush]=onDownloadProgress&&progressEventDecorator(responseContentLength,progressEventReducer(asyncDecorator(onDownloadProgress),!0))||[];response=new Response(trackStream(response.body,65536,onProgress,(()=>{flush&&flush(),unsubscribe&&unsubscribe()})),options)}responseType=responseType||"text";let responseData=await resolvers[utils.findKey(resolvers,responseType)||"text"](response,config);return!isStreamResponse&&unsubscribe&&unsubscribe(),await new Promise(((resolve,reject)=>{settle(resolve,reject,{data:responseData,headers:core_AxiosHeaders.from(response.headers),status:response.status,statusText:response.statusText,config,request})}))}catch(err){if(unsubscribe&&unsubscribe(),err&&"TypeError"===err.name&&/Load failed|fetch/i.test(err.message))throw Object.assign(new core_AxiosError("Network Error",core_AxiosError.ERR_NETWORK,config,request),{cause:err.cause||err});throw core_AxiosError.from(err,err&&err.code,config,request)}})};utils.forEach(knownAdapters,((fn,value)=>{if(fn){try{Object.defineProperty(fn,"name",{value})}catch(e){}Object.defineProperty(fn,"adapterName",{value})}}));const renderReason=reason=>`- ${reason}`,isResolvedHandle=adapter=>utils.isFunction(adapter)||null===adapter||!1===adapter,adapters_getAdapter=adapters=>{adapters=utils.isArray(adapters)?adapters:[adapters];const{length}=adapters;let nameOrAdapter,adapter;const rejectedReasons={};for(let i=0;i<length;i++){let id;if(nameOrAdapter=adapters[i],adapter=nameOrAdapter,!isResolvedHandle(nameOrAdapter)&&(adapter=knownAdapters[(id=String(nameOrAdapter)).toLowerCase()],void 0===adapter))throw new core_AxiosError(`Unknown adapter '${id}'`);if(adapter)break;rejectedReasons[id||"#"+i]=adapter}if(!adapter){const reasons=Object.entries(rejectedReasons).map((([id,state])=>`adapter ${id} `+(!1===state?"is not supported by the environment":"is not available in the build")));let s=length?reasons.length>1?"since :\n"+reasons.map(renderReason).join("\n"):" "+renderReason(reasons[0]):"as no adapter specified";throw new core_AxiosError("There is no suitable adapter to dispatch the request "+s,"ERR_NOT_SUPPORT")}return adapter};function throwIfCancellationRequested(config){if(config.cancelToken&&config.cancelToken.throwIfRequested(),config.signal&&config.signal.aborted)throw new cancel_CanceledError(null,config)}function dispatchRequest(config){throwIfCancellationRequested(config),config.headers=core_AxiosHeaders.from(config.headers),config.data=transformData.call(config,config.transformRequest),-1!==["post","put","patch"].indexOf(config.method)&&config.headers.setContentType("application/x-www-form-urlencoded",!1);return adapters_getAdapter(config.adapter||lib_defaults.adapter)(config).then((function onAdapterResolution(response){return throwIfCancellationRequested(config),response.data=transformData.call(config,config.transformResponse,response),response.headers=core_AxiosHeaders.from(response.headers),response}),(function onAdapterRejection(reason){return isCancel(reason)||(throwIfCancellationRequested(config),reason&&reason.response&&(reason.response.data=transformData.call(config,config.transformResponse,reason.response),reason.response.headers=core_AxiosHeaders.from(reason.response.headers))),Promise.reject(reason)}))}const validators={};["object","boolean","number","function","string","symbol"].forEach(((type,i)=>{validators[type]=function validator(thing){return typeof thing===type||"a"+(i<1?"n ":" ")+type}}));const deprecatedWarnings={};validators.transitional=function transitional(validator,version,message){function formatMessage(opt,desc){return"[Axios v1.9.0] Transitional option '"+opt+"'"+desc+(message?". "+message:"")}return(value,opt,opts)=>{if(!1===validator)throw new core_AxiosError(formatMessage(opt," has been removed"+(version?" in "+version:"")),core_AxiosError.ERR_DEPRECATED);return version&&!deprecatedWarnings[opt]&&(deprecatedWarnings[opt]=!0,console.warn(formatMessage(opt," has been deprecated since v"+version+" and will be removed in the near future"))),!validator||validator(value,opt,opts)}},validators.spelling=function spelling(correctSpelling){return(value,opt)=>(console.warn(`${opt} is likely a misspelling of ${correctSpelling}`),!0)};const validator={assertOptions:function assertOptions(options,schema,allowUnknown){if("object"!=typeof options)throw new core_AxiosError("options must be an object",core_AxiosError.ERR_BAD_OPTION_VALUE);const keys=Object.keys(options);let i=keys.length;for(;i-- >0;){const opt=keys[i],validator=schema[opt];if(validator){const value=options[opt],result=void 0===value||validator(value,opt,options);if(!0!==result)throw new core_AxiosError("option "+opt+" must be "+result,core_AxiosError.ERR_BAD_OPTION_VALUE)}else if(!0!==allowUnknown)throw new core_AxiosError("Unknown option "+opt,core_AxiosError.ERR_BAD_OPTION)}},validators},Axios_validators=validator.validators;class Axios{constructor(instanceConfig){this.defaults=instanceConfig||{},this.interceptors={request:new core_InterceptorManager,response:new core_InterceptorManager}}async request(configOrUrl,config){try{return await this._request(configOrUrl,config)}catch(err){if(err instanceof Error){let dummy={};Error.captureStackTrace?Error.captureStackTrace(dummy):dummy=new Error;const stack=dummy.stack?dummy.stack.replace(/^.+\n/,""):"";try{err.stack?stack&&!String(err.stack).endsWith(stack.replace(/^.+\n.+\n/,""))&&(err.stack+="\n"+stack):err.stack=stack}catch(e){}}throw err}}_request(configOrUrl,config){"string"==typeof configOrUrl?(config=config||{}).url=configOrUrl:config=configOrUrl||{},config=mergeConfig(this.defaults,config);const{transitional,paramsSerializer,headers}=config;void 0!==transitional&&validator.assertOptions(transitional,{silentJSONParsing:Axios_validators.transitional(Axios_validators.boolean),forcedJSONParsing:Axios_validators.transitional(Axios_validators.boolean),clarifyTimeoutError:Axios_validators.transitional(Axios_validators.boolean)},!1),null!=paramsSerializer&&(utils.isFunction(paramsSerializer)?config.paramsSerializer={serialize:paramsSerializer}:validator.assertOptions(paramsSerializer,{encode:Axios_validators.function,serialize:Axios_validators.function},!0)),void 0!==config.allowAbsoluteUrls||(void 0!==this.defaults.allowAbsoluteUrls?config.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:config.allowAbsoluteUrls=!0),validator.assertOptions(config,{baseUrl:Axios_validators.spelling("baseURL"),withXsrfToken:Axios_validators.spelling("withXSRFToken")},!0),config.method=(config.method||this.defaults.method||"get").toLowerCase();let contextHeaders=headers&&utils.merge(headers.common,headers[config.method]);headers&&utils.forEach(["delete","get","head","post","put","patch","common"],(method=>{delete headers[method]})),config.headers=core_AxiosHeaders.concat(contextHeaders,headers);const requestInterceptorChain=[];let synchronousRequestInterceptors=!0;this.interceptors.request.forEach((function unshiftRequestInterceptors(interceptor){"function"==typeof interceptor.runWhen&&!1===interceptor.runWhen(config)||(synchronousRequestInterceptors=synchronousRequestInterceptors&&interceptor.synchronous,requestInterceptorChain.unshift(interceptor.fulfilled,interceptor.rejected))}));const responseInterceptorChain=[];let promise;this.interceptors.response.forEach((function pushResponseInterceptors(interceptor){responseInterceptorChain.push(interceptor.fulfilled,interceptor.rejected)}));let len,i=0;if(!synchronousRequestInterceptors){const chain=[dispatchRequest.bind(this),void 0];for(chain.unshift.apply(chain,requestInterceptorChain),chain.push.apply(chain,responseInterceptorChain),len=chain.length,promise=Promise.resolve(config);i<len;)promise=promise.then(chain[i++],chain[i++]);return promise}len=requestInterceptorChain.length;let newConfig=config;for(i=0;i<len;){const onFulfilled=requestInterceptorChain[i++],onRejected=requestInterceptorChain[i++];try{newConfig=onFulfilled(newConfig)}catch(error){onRejected.call(this,error);break}}try{promise=dispatchRequest.call(this,newConfig)}catch(error){return Promise.reject(error)}for(i=0,len=responseInterceptorChain.length;i<len;)promise=promise.then(responseInterceptorChain[i++],responseInterceptorChain[i++]);return promise}getUri(config){return buildURL(buildFullPath((config=mergeConfig(this.defaults,config)).baseURL,config.url,config.allowAbsoluteUrls),config.params,config.paramsSerializer)}}utils.forEach(["delete","get","head","options"],(function forEachMethodNoData(method){Axios.prototype[method]=function(url,config){return this.request(mergeConfig(config||{},{method,url,data:(config||{}).data}))}})),utils.forEach(["post","put","patch"],(function forEachMethodWithData(method){function generateHTTPMethod(isForm){return function httpMethod(url,data,config){return this.request(mergeConfig(config||{},{method,headers:isForm?{"Content-Type":"multipart/form-data"}:{},url,data}))}}Axios.prototype[method]=generateHTTPMethod(),Axios.prototype[method+"Form"]=generateHTTPMethod(!0)}));const core_Axios=Axios;class CancelToken{constructor(executor){if("function"!=typeof executor)throw new TypeError("executor must be a function.");let resolvePromise;this.promise=new Promise((function promiseExecutor(resolve){resolvePromise=resolve}));const token=this;this.promise.then((cancel=>{if(!token._listeners)return;let i=token._listeners.length;for(;i-- >0;)token._listeners[i](cancel);token._listeners=null})),this.promise.then=onfulfilled=>{let _resolve;const promise=new Promise((resolve=>{token.subscribe(resolve),_resolve=resolve})).then(onfulfilled);return promise.cancel=function reject(){token.unsubscribe(_resolve)},promise},executor((function cancel(message,config,request){token.reason||(token.reason=new cancel_CanceledError(message,config,request),resolvePromise(token.reason))}))}throwIfRequested(){if(this.reason)throw this.reason}subscribe(listener){this.reason?listener(this.reason):this._listeners?this._listeners.push(listener):this._listeners=[listener]}unsubscribe(listener){if(!this._listeners)return;const index=this._listeners.indexOf(listener);-1!==index&&this._listeners.splice(index,1)}toAbortSignal(){const controller=new AbortController,abort=err=>{controller.abort(err)};return this.subscribe(abort),controller.signal.unsubscribe=()=>this.unsubscribe(abort),controller.signal}static source(){let cancel;return{token:new CancelToken((function executor(c){cancel=c})),cancel}}}const cancel_CancelToken=CancelToken;const HttpStatusCode={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(HttpStatusCode).forEach((([key,value])=>{HttpStatusCode[value]=key}));const helpers_HttpStatusCode=HttpStatusCode;const axios=function createInstance(defaultConfig){const context=new core_Axios(defaultConfig),instance=bind(core_Axios.prototype.request,context);return utils.extend(instance,core_Axios.prototype,context,{allOwnKeys:!0}),utils.extend(instance,context,null,{allOwnKeys:!0}),instance.create=function create(instanceConfig){return createInstance(mergeConfig(defaultConfig,instanceConfig))},instance}(lib_defaults);axios.Axios=core_Axios,axios.CanceledError=cancel_CanceledError,axios.CancelToken=cancel_CancelToken,axios.isCancel=isCancel,axios.VERSION="1.9.0",axios.toFormData=helpers_toFormData,axios.AxiosError=core_AxiosError,axios.Cancel=axios.CanceledError,axios.all=function all(promises){return Promise.all(promises)},axios.spread=function spread(callback){return function wrap(arr){return callback.apply(null,arr)}},axios.isAxiosError=function isAxiosError(payload){return utils.isObject(payload)&&!0===payload.isAxiosError},axios.mergeConfig=mergeConfig,axios.AxiosHeaders=core_AxiosHeaders,axios.formToJSON=thing=>helpers_formDataToJSON(utils.isHTMLForm(thing)?new FormData(thing):thing),axios.getAdapter=adapters_getAdapter,axios.HttpStatusCode=helpers_HttpStatusCode,axios.default=axios;const lib_axios=axios}}]);