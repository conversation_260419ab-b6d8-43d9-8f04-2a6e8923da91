import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { Helmet } from 'react-helmet-async'
import { CustomerReviews } from '@/components/ui/reviews/CustomerReviews'
import UltimateCtaFooterMinimal from '@/components/enhanced-landing/UltimateCtaFooterMinimal'
import { HeroSection } from '@/components/ui/hero-section-dark'
import { FeaturesSectionWithHoverEffects } from '@/components/ui/feature-section-with-hover-effects'
import { NavBar } from '@/components/ui/tubelight-navbar'
import { Pricing } from '@/components/ui/pricing'
import EnhancedBackgroundPaths from '@/components/ui/modern-background-paths'
import { Home, User, Briefcase, FileText, Phone } from 'lucide-react'
import '@/styles/notfound-animations.css'
import { RainbowButton } from "@/components/ui/rainbow-button";

// 导航项目
const navItems = [
  { name: 'Home', url: '#hero', icon: Home },
  { name: 'Features', url: '#features', icon: Briefcase },
  { name: 'Pricing', url: '#pricing', icon: FileText },
  { name: 'About', url: '#testimonials', icon: User },
  { name: 'Contact', url: '#cta', icon: Phone }
];

// iTeraBiz 定价方案
const iTeraBizPlans = [
  {
    name: "STARTER",
    price: "29",
    yearlyPrice: "23",
    period: "per month",
    features: [
      "10,000 AI-generated words/month",
      "5 Content templates",
      "Basic analytics",
      "Email support",
      "1 User account",
      "Standard content quality",
    ],
    description: "Perfect for individuals and small content creators",
    buttonText: "Start Free Trial",
    href: "/register",
    isPopular: false,
  },
  {
    name: "PROFESSIONAL", 
    price: "79",
    yearlyPrice: "63",
    period: "per month",
    features: [
      "50,000 AI-generated words/month",
      "25+ Content templates",
      "Advanced analytics & insights",
      "Priority support",
      "5 User accounts",
      "API access",
      "Custom branding",
      "Multi-platform publishing",
    ],
    description: "Ideal for growing teams and content agencies",
    buttonText: "Get Started",
    href: "/register",
    isPopular: true,
  },
  {
    name: "ENTERPRISE",
    price: "199",
    yearlyPrice: "159",
    period: "per month",
    features: [
      "Unlimited AI-generated content",
      "Custom templates & workflows",
      "Dedicated account manager",
      "White-label solution",
      "Unlimited users",
      "Advanced API & integrations",
      "SLA guarantee",
      "Custom AI model training",
      "Priority feature requests",
    ],
    description: "For large organizations with enterprise needs",
    buttonText: "Contact Sales",
    href: "/contact",
    isPopular: false,
  },
];

export default function HomePage() {
  const [isDarkMode, setIsDarkMode] = useState(() => {
    // Check if dark mode is already enabled
    return document.documentElement.classList.contains('dark');
  });
  const navigate = useNavigate();

  const handleDarkModeToggle = (darkMode: boolean) => {
    setIsDarkMode(darkMode);
  };

  return (
    <div className="min-h-screen bg-gradient-hero dark:bg-neutral-900">
      {/* 左上角Logo - 独立于navbar */}
      <div className="fixed top-6 left-16 z-[101] pointer-events-auto">
        <button
          onClick={() => navigate('/')}
          className="group flex items-center transition-transform duration-300 ease-out
            bg-transparent border-none shadow-none p-0 m-0
            focus:outline-none focus:ring-0 appearance-none"
          style={{
            lineHeight: 0,
            background: 'none',
            border: 'none',
            borderWidth: 0,
            borderStyle: 'none',
            boxShadow: 'none',
            padding: 0,
            margin: 0,
            appearance: 'none',
            WebkitAppearance: 'none',
            MozAppearance: 'none',
          }}
          aria-label="Home"
        >
          <img
            src="/images/iterabiz-logo.png"
            alt="iTeraBiz Logo"
            className="h-12 w-auto select-none transition-transform duration-300 ease-out group-hover:scale-110"
            style={{ display: 'block', border: 'none', boxShadow: 'none' }}
            draggable={false}
          />
        </button>
      </div>

      {/* 右上角Get Started按钮 */}
      <div className="fixed top-6 right-6 z-[101]">
        <RainbowButton onClick={() => navigate('/register')}>
          Get Started
        </RainbowButton>
      </div>

      <Helmet>
        <title>iTeraBiz - AI-Powered Content Creation Platform</title>
        <meta name="description" content="Transform your content creation with AI-powered tools. Create, optimize, and distribute engaging content across multiple platforms." />
      </Helmet>

      {/* Modern Tubelight Navigation */}
      <NavBar 
        items={navItems}
        isDarkMode={isDarkMode}
        onDarkModeToggle={handleDarkModeToggle}
      />

      {/* Modern Hero Section */}
      <section id="hero">
        <HeroSection
          title="AI-Powered Content Creation Platform"
          subtitle={{
            regular: "Transform your ideas into ",
            gradient: "beautiful digital experiences",
          }}
          description="Transform your content creation process with our advanced AI platform. Generate, optimize, and distribute engaging content across multiple channels in minutes, not hours."
          ctaText="Get Started Free"
          onCtaClick={() => navigate('/register')}
          bottomImage={{
            light: "https://images.unsplash.com/photo-1551434678-e076c223a692?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2340&q=80",
            dark: "https://images.unsplash.com/photo-1518709268805-4e9042af2176?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2340&q=80",
          }}
          gridOptions={{
            angle: 65,
            opacity: 0.4,
            cellSize: 50,
            lightLineColor: "#4a4a4a",
            darkLineColor: "#2a2a2a",
          }}
        />
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 px-4 sm:px-6 lg:px-8 bg-white dark:bg-neutral-900">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-bold text-neutral-800 dark:text-neutral-100 mb-4">
              Powerful Features for Modern Content Creation
            </h2>
            <p className="text-xl text-neutral-600 dark:text-neutral-300 max-w-2xl mx-auto">
              Everything you need to create, optimize, and distribute content that engages your audience and drives results.
            </p>
          </div>
          <FeaturesSectionWithHoverEffects />
        </div>
      </section>

      {/* Modern Pricing Section */}
      <section id="pricing" className="bg-neutral-50 dark:bg-neutral-800">
        <Pricing 
          plans={iTeraBizPlans.map(plan =>
            plan.buttonText === "Contact Sales"
              ? { ...plan, href: "/contact" }
              : plan
          )}
          title="Choose Your Perfect Plan"
          description="Scale your content creation with our AI-powered platform\nAll plans include access to our advanced AI tools, analytics, and dedicated support."
        />
      </section>

      {/* Testimonials Section */}
      <section id="testimonials" className="py-20 px-4 sm:px-6 lg:px-8 bg-white dark:bg-neutral-900">
        <div className="max-w-7xl mx-auto">
          <CustomerReviews />
        </div>
      </section>

      {/* Modern CTA Section with Dynamic Background (moved below testimonials) */}
      <section id="cta" className="relative">
        <EnhancedBackgroundPaths
          title="Ready to Transform?"
        />
      </section>

      {/* Footer */}
      <UltimateCtaFooterMinimal />
    </div>
  );
}