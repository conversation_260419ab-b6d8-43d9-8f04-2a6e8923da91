/**
 * Button Demo Component
 * 展示优化后的Hero Section按钮
 */

import React from 'react';
import { Rocket, Play  } from 'lucide-react';
import {  HeroPrimaryButton, HeroSecondaryButton  } from "./HeroButtons";

const ButtonDemo: React.FC = () => {;
  return (<div className="min-h-screen bg-gradient-to-br from-gray-50 via-purple-50/30 to-gray-100 flex items-center justify-center p-8">
      <div className="max-w-4xl mx-auto text-center">
        <h1 className="text-4xl font-bold text-gray-800 mb-4">
          优化后的 Hero Section 按钮
        </h1>
        <p className="text-lg text-gray-600 mb-12">
          Start Free Trial 按钮现在具有更柔和的紫色渐变和与 Watch Demo 相同的 hover 效果
        </p>
        <div className="flex flex-col, sm:flex-row justify-center items-center gap-6">
          {/* Start Free Trial Button - 优化后的样式 */}
          <HeroPrimaryButton
            onClick={() => console.log('Start Free Trial clicked')}
            icon={<Rocket className="w-5 h-5" />}
          >
            Start Free Trial
          </HeroPrimaryButton>
          {/* Watch Demo Button - 保持原有样式 */}
          <HeroSecondaryButton
            onClick={() => console.log('Watch Demo clicked')}
            icon={<Play className="w-4 h-4" />}
          >
            Watch Demo
          </HeroSecondaryButton>
        </div>
        <div className="mt-12 text-sm text-gray-500 space-y-2">
          <p><strong>默认状态优化：</strong></p>
          <ul className="space-y-1">
            <li>• 使用与 navbar 相同的渐变 (purple-600 → pink-600)</li>
            <li>• 添加浅灰色边框 (border-gray-200)</li>
            <li>• 保持简洁而吸引人的视觉效果</li>
          </ul>
          <p className="mt-6"><strong>Hover状态优化：</strong></p>
          <ul className="space-y-1">
            <li>• 与 Watch Demo 按钮相同的阴影效果</li>
            <li>• 相同的缩放和位移动画 (scale: 1.05, y: -2)</li>
            <li>• 渐变颜色加深 (purple-700 → pink-700)</li>
            <li>• 边框颜色变化 (gray-200 → gray-300)</li>
          </ul>
        </div>
      </div>
    </div>

};

export default ButtonDemo;