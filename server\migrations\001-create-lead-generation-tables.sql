-- Lead Generation 数据库表创建脚本

-- 1. 线索表
CREATE TABLE IF NOT EXISTS leads (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  agent_id UUID NOT NULL REFERENCES agents(id) ON DELETE CASCADE,
  
  -- 基本信息
  first_name VA<PERSON>HAR(255) NOT NULL,
  last_name <PERSON><PERSON><PERSON><PERSON>(255),
  email VARCHAR(255) NOT NULL,
  phone VARCHAR(20),
  company VARCHAR(255),
  job_title VARCHAR(255),
  industry VARCHAR(100),
  
  -- 评分信息
  score INTEGER DEFAULT 0 CHECK (score >= 0 AND score <= 100),
  score_history JSONB DEFAULT '[]'::jsonb,
  
  -- 状态管理
  status VARCHAR(20) DEFAULT 'new' CHECK (status IN ('new', 'contacted', 'qualified', 'converted', 'lost')),
  temperature VARCHAR(10) DEFAULT 'cold' CHECK (temperature IN ('hot', 'warm', 'cold')),
  
  -- 来源信息
  source VARCHAR(100) NOT NULL,
  source_details JSONB DEFAULT '{}'::jsonb,
  
  -- 地理信息
  ip_address INET,
  location JSONB DEFAULT '{}'::jsonb,
  
  -- 自定义字段数据
  custom_fields JSONB DEFAULT '{}'::jsonb,
  
  -- 分配信息
  assigned_to UUID REFERENCES users(id),
  assigned_at TIMESTAMP WITH TIME ZONE,
  
  -- 最后互动时间
  last_interaction_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- 跟进信息
  next_followup_at TIMESTAMP WITH TIME ZONE,
  followup_notes TEXT,
  
  -- 时间戳
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  deleted_at TIMESTAMP WITH TIME ZONE
);

-- 创建索引
CREATE INDEX idx_leads_email ON leads(email);
CREATE INDEX idx_leads_agent_id ON leads(agent_id);
CREATE INDEX idx_leads_status ON leads(status);
CREATE INDEX idx_leads_score ON leads(score);
CREATE INDEX idx_leads_assigned_to ON leads(assigned_to);
CREATE INDEX idx_leads_created_at ON leads(created_at);
CREATE INDEX idx_leads_last_interaction_at ON leads(last_interaction_at);

-- 2. 线索活动记录表
CREATE TABLE IF NOT EXISTS lead_activities (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  lead_id UUID NOT NULL REFERENCES leads(id) ON DELETE CASCADE,
  
  -- 活动类型
  type VARCHAR(50) NOT NULL CHECK (type IN (
    'page_view', 'form_submit', 'email_open', 'email_click', 
    'download', 'video_view', 'chat_start', 'call_made',
    'meeting_scheduled', 'proposal_sent', 'contract_signed'
  )),
  
  -- 活动详情
  description VARCHAR(500),
  metadata JSONB DEFAULT '{}'::jsonb,
  
  -- 评分影响
  score_impact INTEGER DEFAULT 0,
  
  -- 用户代理和设备信息
  user_agent TEXT,
  device_info JSONB DEFAULT '{}'::jsonb,
  
  -- 活动来源
  source VARCHAR(100),
  
  -- IP和地理位置
  ip_address INET,
  location JSONB DEFAULT '{}'::jsonb,
  
  -- 时间戳
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_lead_activities_lead_id ON lead_activities(lead_id);
CREATE INDEX idx_lead_activities_type ON lead_activities(type);
CREATE INDEX idx_lead_activities_created_at ON lead_activities(created_at);

-- 3. 线索评分历史表
CREATE TABLE IF NOT EXISTS lead_scores (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  lead_id UUID NOT NULL REFERENCES leads(id) ON DELETE CASCADE,
  
  -- 评分详情
  previous_score INTEGER DEFAULT 0,
  new_score INTEGER NOT NULL,
  score_change INTEGER NOT NULL,
  
  -- 评分原因
  reason VARCHAR(500) NOT NULL,
  
  -- 评分分解
  behavioral_score INTEGER DEFAULT 0,
  demographic_score INTEGER DEFAULT 0,
  interaction_score INTEGER DEFAULT 0,
  source_score INTEGER DEFAULT 0,
  
  -- 相关活动
  activity_id UUID REFERENCES lead_activities(id),
  
  -- 评分配置快照
  scoring_config JSONB DEFAULT '{}'::jsonb,
  
  -- 时间戳
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_lead_scores_lead_id ON lead_scores(lead_id);
CREATE INDEX idx_lead_scores_created_at ON lead_scores(created_at);
CREATE INDEX idx_lead_scores_new_score ON lead_scores(new_score);

-- 4. 线索表单提交记录
CREATE TABLE IF NOT EXISTS lead_form_submissions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  lead_id UUID NOT NULL REFERENCES leads(id) ON DELETE CASCADE,
  agent_id UUID NOT NULL REFERENCES agents(id) ON DELETE CASCADE,
  
  -- 表单信息
  form_type VARCHAR(100) NOT NULL,
  form_data JSONB NOT NULL,
  
  -- 页面信息
  page_url TEXT,
  page_title VARCHAR(500),
  referrer_url TEXT,
  
  -- UTM参数
  utm_source VARCHAR(100),
  utm_medium VARCHAR(100),
  utm_campaign VARCHAR(100),
  utm_term VARCHAR(100),
  utm_content VARCHAR(100),
  
  -- 技术信息
  user_agent TEXT,
  ip_address INET,
  session_id VARCHAR(100),
  
  -- 时间戳
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_lead_form_submissions_lead_id ON lead_form_submissions(lead_id);
CREATE INDEX idx_lead_form_submissions_agent_id ON lead_form_submissions(agent_id);
CREATE INDEX idx_lead_form_submissions_form_type ON lead_form_submissions(form_type);
CREATE INDEX idx_lead_form_submissions_created_at ON lead_form_submissions(created_at);

-- 5. 线索标签系统
CREATE TABLE IF NOT EXISTS lead_tags (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(100) UNIQUE NOT NULL,
  color VARCHAR(7) DEFAULT '#6B7280',
  description TEXT,
  agent_id UUID NOT NULL REFERENCES agents(id) ON DELETE CASCADE,
  
  -- 时间戳
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. 线索标签关联表
CREATE TABLE IF NOT EXISTS lead_tag_assignments (
  lead_id UUID NOT NULL REFERENCES leads(id) ON DELETE CASCADE,
  tag_id UUID NOT NULL REFERENCES lead_tags(id) ON DELETE CASCADE,
  assigned_by UUID REFERENCES users(id),
  
  -- 时间戳
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  PRIMARY KEY (lead_id, tag_id)
);

-- 创建索引
CREATE INDEX idx_lead_tag_assignments_lead_id ON lead_tag_assignments(lead_id);
CREATE INDEX idx_lead_tag_assignments_tag_id ON lead_tag_assignments(tag_id);

-- 7. 线索转化漏斗配置
CREATE TABLE IF NOT EXISTS conversion_funnels (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  agent_id UUID NOT NULL REFERENCES agents(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  stages JSONB NOT NULL,
  is_active BOOLEAN DEFAULT true,
  
  -- 时间戳
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 8. 线索转化记录
CREATE TABLE IF NOT EXISTS lead_conversions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  lead_id UUID NOT NULL REFERENCES leads(id) ON DELETE CASCADE,
  funnel_id UUID NOT NULL REFERENCES conversion_funnels(id) ON DELETE CASCADE,
  stage VARCHAR(100) NOT NULL,
  stage_order INTEGER NOT NULL,
  converted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  value DECIMAL(10, 2),
  notes TEXT,
  
  -- 时间戳
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_lead_conversions_lead_id ON lead_conversions(lead_id);
CREATE INDEX idx_lead_conversions_funnel_id ON lead_conversions(funnel_id);
CREATE INDEX idx_lead_conversions_stage ON lead_conversions(stage);
CREATE INDEX idx_lead_conversions_converted_at ON lead_conversions(converted_at);

-- 创建更新时间戳的触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- 为所有表创建更新时间戳触发器
CREATE TRIGGER update_leads_updated_at BEFORE UPDATE ON leads FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_lead_activities_updated_at BEFORE UPDATE ON lead_activities FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_lead_scores_updated_at BEFORE UPDATE ON lead_scores FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_lead_form_submissions_updated_at BEFORE UPDATE ON lead_form_submissions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_lead_tags_updated_at BEFORE UPDATE ON lead_tags FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_lead_tag_assignments_updated_at BEFORE UPDATE ON lead_tag_assignments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_conversion_funnels_updated_at BEFORE UPDATE ON conversion_funnels FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_lead_conversions_updated_at BEFORE UPDATE ON lead_conversions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column(); 