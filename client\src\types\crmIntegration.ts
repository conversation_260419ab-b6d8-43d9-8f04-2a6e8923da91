export interface CRMProvider {
  id: string;
  name: string;
  displayName: string;
  description: string;
  logo: string;
  category: 'crm' | 'marketing' | 'sales' | 'support' | 'analytics';
  tier: 'free' | 'premium' | 'enterprise';
  status: 'active' | 'beta' | 'deprecated' | 'coming_soon';
  features: CRMFeature[];
  authType: 'oauth2' | 'api_key' | 'basic_auth' | 'custom';
  webhookSupport: boolean;
  rateLimits: {
    requests: number;
    period: 'minute' | 'hour' | 'day';
  };
  documentation: {
    setupGuide: string;
    apiReference: string;
    examples: string;
  };
}

export interface CRMFeature {
  id: string;
  name: string;
  description: string;
  supported: boolean;
  premium?: boolean;
}

export interface CRMConnection {
  id: string;
  providerId: string;
  name: string;
  status: 'connected' | 'disconnected' | 'error' | 'pending';
  createdAt: string;
  lastSyncAt?: string;
  config: CRMConnectionConfig;
  credentials: CRMCredentials;
  syncSettings: CRMSyncSettings;
  healthCheck: CRMHealthCheck;
  usage: CRMUsageStats;
}

export interface CRMConnectionConfig {
  instanceUrl?: string;
  apiVersion?: string;
  environment?: 'production' | 'sandbox' | 'development';
  customFields: Record<string, any>;
  mappings: CRMFieldMapping[];
  filters: CRMFilter[];
  webhookUrl?: string;
  webhookSecret?: string;
}

export interface CRMCredentials {
  type: 'oauth2' | 'api_key' | 'basic_auth';
  accessToken?: string;
  refreshToken?: string;
  apiKey?: string;
  username?: string;
  password?: string;
  clientId?: string;
  clientSecret?: string;
  expiresAt?: string;
  scopes?: string[];
}

export interface CRMSyncSettings {
  enabled: boolean;
  direction: 'bidirectional' | 'to_crm' | 'from_crm';
  frequency: 'real_time' | 'hourly' | 'daily' | 'weekly' | 'manual';
  batchSize: number;
  conflictResolution: 'crm_wins' | 'ibuddy_wins' | 'manual_review' | 'merge';
  syncObjects: CRMSyncObject[];
  lastSyncAt?: string;
  nextSyncAt?: string;
}

export interface CRMSyncObject {
  objectType: 'contact' | 'lead' | 'account' | 'opportunity' | 'case' | 'custom';
  enabled: boolean;
  direction: 'bidirectional' | 'to_crm' | 'from_crm';
  mapping: CRMFieldMapping[];
  filters: CRMFilter[];
}

export interface CRMFieldMapping {
  id: string;
  sourceField: string;
  targetField: string;
  transformation?: CRMFieldTransformation;
  required: boolean;
  direction: 'bidirectional' | 'to_crm' | 'from_crm';
}

export interface CRMFieldTransformation {
  type: 'format' | 'lookup' | 'calculation' | 'conditional';
  config: Record<string, any>;
}

export interface CRMFilter {
  id: string;
  field: string;
  operator: 'equals' | 'not_equals' | 'contains' | 'starts_with' | 'ends_with' | 'greater_than' | 'less_than' | 'in' | 'not_in';
  value: any;
  logicalOperator?: 'AND' | 'OR';
}

export interface CRMHealthCheck {
  status: 'healthy' | 'warning' | 'error';
  lastCheckedAt: string;
  issues: CRMHealthIssue[];
  metrics: {
    responseTime: number;
    successRate: number;
    errorRate: number;
  };
}

export interface CRMHealthIssue {
  type: 'authentication' | 'rate_limit' | 'api_error' | 'mapping_error' | 'sync_error';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  details?: string;
  timestamp: string;
  resolved: boolean;
}

export interface CRMUsageStats {
  period: 'today' | 'week' | 'month';
  apiCalls: number;
  syncedRecords: number;
  errors: number;
  dataTransferred: number; // in bytes
  costs?: {
    amount: number;
    currency: string;
  };
}

export interface CRMSyncLog {
  id: string;
  connectionId: string;
  type: 'sync' | 'webhook' | 'manual';
  status: 'success' | 'partial' | 'failed';
  startedAt: string;
  completedAt?: string;
  duration?: number;
  recordsProcessed: number;
  recordsSucceeded: number;
  recordsFailed: number;
  errors: CRMSyncError[];
  summary: string;
}

export interface CRMSyncError {
  recordId?: string;
  field?: string;
  error: string;
  details?: string;
  retryable: boolean;
}

export interface CRMWebhookEvent {
  id: string;
  connectionId: string;
  eventType: string;
  objectType: string;
  objectId: string;
  action: 'created' | 'updated' | 'deleted';
  data: Record<string, any>;
  receivedAt: string;
  processedAt?: string;
  status: 'pending' | 'processed' | 'failed';
  retryCount: number;
}

// 预定义的CRM提供商
export const CRM_PROVIDERS: Record<string, CRMProvider> = {
  salesforce: {
    id: 'salesforce',
    name: 'salesforce',
    displayName: 'Salesforce',
    description: 'World\'s #1 CRM platform with comprehensive sales, service, and marketing tools',
    logo: '/logos/salesforce.svg',
    category: 'crm',
    tier: 'premium',
    status: 'active',
    authType: 'oauth2',
    webhookSupport: true,
    rateLimits: {
      requests: 100000,
      period: 'day'
    },
    features: [
      { id: 'contacts', name: 'Contact Management', description: 'Sync customer contacts', supported: true },
      { id: 'leads', name: 'Lead Management', description: 'Sync sales leads', supported: true },
      { id: 'opportunities', name: 'Opportunity Tracking', description: 'Sync sales opportunities', supported: true },
      { id: 'accounts', name: 'Account Management', description: 'Sync company accounts', supported: true },
      { id: 'cases', name: 'Case Management', description: 'Sync support cases', supported: true },
      { id: 'custom_objects', name: 'Custom Objects', description: 'Sync custom Salesforce objects', supported: true, premium: true },
      { id: 'workflows', name: 'Workflow Automation', description: 'Trigger Salesforce workflows', supported: true, premium: true }
    ],
    documentation: {
      setupGuide: '/docs/integrations/salesforce/setup',
      apiReference: '/docs/integrations/salesforce/api',
      examples: '/docs/integrations/salesforce/examples'
    }
  },
  hubspot: {
    id: 'hubspot',
    name: 'hubspot',
    displayName: 'HubSpot',
    description: 'Inbound marketing, sales, and service platform with free CRM',
    logo: '/logos/hubspot.svg',
    category: 'crm',
    tier: 'free',
    status: 'active',
    authType: 'oauth2',
    webhookSupport: true,
    rateLimits: {
      requests: 40000,
      period: 'day'
    },
    features: [
      { id: 'contacts', name: 'Contact Management', description: 'Sync customer contacts', supported: true },
      { id: 'companies', name: 'Company Management', description: 'Sync company records', supported: true },
      { id: 'deals', name: 'Deal Tracking', description: 'Sync sales deals', supported: true },
      { id: 'tickets', name: 'Ticket Management', description: 'Sync support tickets', supported: true },
      { id: 'marketing', name: 'Marketing Automation', description: 'Trigger marketing workflows', supported: true, premium: true },
      { id: 'analytics', name: 'Analytics & Reporting', description: 'Access HubSpot analytics', supported: true, premium: true }
    ],
    documentation: {
      setupGuide: '/docs/integrations/hubspot/setup',
      apiReference: '/docs/integrations/hubspot/api',
      examples: '/docs/integrations/hubspot/examples'
    }
  },
  pipedrive: {
    id: 'pipedrive',
    name: 'pipedrive',
    displayName: 'Pipedrive',
    description: 'Sales-focused CRM designed to help small and medium businesses',
    logo: '/logos/pipedrive.svg',
    category: 'sales',
    tier: 'premium',
    status: 'active',
    authType: 'oauth2',
    webhookSupport: true,
    rateLimits: {
      requests: 10000,
      period: 'day'
    },
    features: [
      { id: 'persons', name: 'Person Management', description: 'Sync person records', supported: true },
      { id: 'organizations', name: 'Organization Management', description: 'Sync organization records', supported: true },
      { id: 'deals', name: 'Deal Management', description: 'Sync sales deals', supported: true },
      { id: 'activities', name: 'Activity Tracking', description: 'Sync activities and tasks', supported: true },
      { id: 'pipelines', name: 'Pipeline Management', description: 'Manage sales pipelines', supported: true, premium: true }
    ],
    documentation: {
      setupGuide: '/docs/integrations/pipedrive/setup',
      apiReference: '/docs/integrations/pipedrive/api',
      examples: '/docs/integrations/pipedrive/examples'
    }
  },
  zapier: {
    id: 'zapier',
    name: 'zapier',
    displayName: 'Zapier',
    description: 'Connect with 5000+ apps through Zapier\'s automation platform',
    logo: '/logos/zapier.svg',
    category: 'analytics',
    tier: 'free',
    status: 'active',
    authType: 'api_key',
    webhookSupport: true,
    rateLimits: {
      requests: 1000,
      period: 'hour'
    },
    features: [
      { id: 'triggers', name: 'Trigger Events', description: 'Send events to Zapier', supported: true },
      { id: 'actions', name: 'Receive Actions', description: 'Receive actions from Zapier', supported: true },
      { id: 'webhooks', name: 'Webhook Support', description: 'Real-time webhook integration', supported: true },
      { id: 'multi_step', name: 'Multi-step Zaps', description: 'Complex automation workflows', supported: true, premium: true }
    ],
    documentation: {
      setupGuide: '/docs/integrations/zapier/setup',
      apiReference: '/docs/integrations/zapier/api',
      examples: '/docs/integrations/zapier/examples'
    }
  }
};
