# iBuddy2 环境准备指南

本文档提供设置iBuddy2开发和生产环境所需的全面指南。

## 系统要求

在开始之前，请确保您的系统满足以下最低要求：

- **操作系统**: Windows 10/11, macOS 12+, Ubuntu 20.04+
- **Node.js**: 18.0.0或更高版本
- **npm**: 8.0.0或更高版本
- **内存**: 最少4GB，推荐8GB+
- **存储**: 最少10GB可用空间
- **网络**: 稳定的互联网连接
- **Docker**: 20.10或更高版本(用于生产部署)

## 前置依赖安装

### 1. Node.js和npm

#### Windows
1. 从[Node.js官方网站](https://nodejs.org/)下载安装程序
2. 运行安装程序并按照向导完成安装
3. 验证安装：
   ```bash
   node --version
   npm --version
   ```

#### macOS
使用Homebrew安装：
```bash
brew install node
```

#### Linux (Ubuntu/Debian)
```bash
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs
```

### 2. 数据库

#### PostgreSQL (通过Supabase)
对于开发环境，您可以使用Supabase提供的云数据库或本地安装：

1. 创建[Supabase账户](https://supabase.com/)
2. 创建新项目
3. 记录数据库连接信息

#### Redis
##### Windows
1. 使用[Windows Subsystem for Linux (WSL)](https://docs.microsoft.com/en-us/windows/wsl/install)
2. 在WSL中安装Redis:
   ```bash
   sudo apt update
   sudo apt install redis-server
   ```

##### macOS
```bash
brew install redis
brew services start redis
```

##### Linux
```bash
sudo apt update
sudo apt install redis-server
sudo systemctl enable redis-server
```

### 3. RabbitMQ

#### Windows
1. 从[RabbitMQ官方网站](https://www.rabbitmq.com/download.html)下载安装程序
2. 运行安装程序并按照向导完成安装

#### macOS
```bash
brew install rabbitmq
brew services start rabbitmq
```

#### Linux
```bash
sudo apt update
sudo apt install rabbitmq-server
sudo systemctl enable rabbitmq-server
```

## 环境变量配置

每个服务需要配置自己的环境变量。以下是每个服务的环境变量模板。

### API网关 (.env)

```
# 服务配置
PORT=3001
NODE_ENV=development

# 认证
JWT_SECRET=your-jwt-secret
JWT_EXPIRATION=24h

# 服务URL
CORE_SERVICE_URL=http://localhost:3002
AI_SERVICE_URL=http://localhost:3003
CLIENT_URL=http://localhost:3000

# 监控与日志
ENABLE_REQUEST_LOGGING=true
```

### 核心服务 (.env)

```
# 服务配置
PORT=3002
NODE_ENV=development

# 数据库
SUPABASE_URL=your-supabase-url
SUPABASE_KEY=your-supabase-key
DATABASE_URL=your-database-url

# 认证
JWT_SECRET=your-jwt-secret
JWT_EXPIRATION=24h

# 文件上传
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=50mb
```

### AI服务 (.env)

```
# 服务配置
PORT=3003
NODE_ENV=development

# API密钥
GEMINI_API_KEY=your-gemini-api-key
OPENAI_API_KEY=your-openai-api-key
OPENROUTER_API_KEY=your-openrouter-api-key

# 模型配置
ENABLE_MODEL_FALLBACK=true
DEFAULT_AI_MODEL=gemini-2.0-flash-lite
ENABLE_AUTO_MODEL_SELECTION=true

# 上下文配置
SHORT_TERM_WINDOW_SIZE=5
MID_TERM_WINDOW_SIZE=15
LONG_TERM_WINDOW_SIZE=50
ENABLE_CONTEXT_COMPRESSION=true

# 消息队列
ENABLE_MESSAGE_QUEUE=true
RABBITMQ_URL=amqp://localhost:5672

# 缓存
REDIS_URL=redis://localhost:6379
```

### 前端 (.env)

```
REACT_APP_API_URL=http://localhost:3001
REACT_APP_SUPABASE_URL=your-supabase-url
REACT_APP_SUPABASE_ANON_KEY=your-supabase-anon-key
```

## 验证环境

完成安装后，运行以下命令验证环境：

```bash
# 验证Node.js
node --version  # 应显示v18.x.x或更高

# 验证npm
npm --version  # 应显示8.x.x或更高

# 验证Redis
redis-cli ping  # 应返回PONG

# 验证RabbitMQ
rabbitmqctl status  # 应显示RabbitMQ状态信息
```

## 下一步

环境设置完成后，请参考[开发环境部署](./development-deployment.md)文档，了解如何启动iBuddy2的开发环境。 