// 地理位置
export interface GeoLocation {
  latitude: number;
  longitude: number;
  address: string;
  city?: string;
  region?: string;
  postalCode?: string;
  country?: string;
  formattedAddress?: string;
}

// 时间段
export interface TimeRange {
  start: string; // ISO格式时间或HH:MM格式
  end: string; // ISO格式时间或HH:MM格式
}

// 日期范围
export interface DateRange {
  startDate: string; // YYYY-MM-DD格式
  endDate: string; // YYYY-MM-DD格式
}

// 分页参数
export interface PaginationParams {
  page: number;
  limit: number;
  total?: number;
}

// 排序参数
export interface SortParams {
  field: string;
  order: 'asc' | 'desc';
}

// 过滤参数
export interface FilterParams {
  field: string;
  operator: 'eq' | 'neq' | 'gt' | 'gte' | 'lt' | 'lte' | 'contains' | 'startsWith' | 'endsWith';
  value: any;
}