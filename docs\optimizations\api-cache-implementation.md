# API请求缓存实施计划

## 概述

API请求缓存是一种优化技术，它可以存储API请求的响应数据，并在后续相同请求时直接返回缓存数据，而不是重新发起网络请求。这种方法可以显著减少网络请求数量，提高应用响应速度，并减轻服务器负担。

## 优化目标

1. 减少重复API请求，提高应用响应速度
2. 在网络不稳定情况下提供更好的用户体验
3. 减轻服务器负担
4. 在离线状态下提供基本功能支持

## 实施策略

### 1. 使用axios-cache-adapter

[axios-cache-adapter](https://github.com/RasCarlito/axios-cache-adapter)是一个专为Axios设计的缓存适配器，它提供了灵活的缓存配置和管理功能。

```javascript
import { setupCache } from 'axios-cache-adapter';
import axios from 'axios';

// 创建缓存实例
const cache = setupCache({
  maxAge: 15 * 60 * 1000, // 15分钟缓存
  exclude: { query: false }, // 包含查询参数的请求也缓存
  key: req => {
    // 自定义缓存键生成函数
    return req.url + JSON.stringify(req.params || {});
  }
});

// 创建axios实例，使用缓存适配器
const api = axios.create({
  adapter: cache.adapter
});

export default api;
```

### 2. 按请求类型设置缓存策略

不同类型的API请求需要不同的缓存策略：

- **GET请求**：适合缓存，特别是配置信息、用户信息等不常变动的数据
- **POST/PUT/DELETE请求**：通常不缓存，但需要在操作后清除相关GET请求的缓存
- **实时数据**：设置较短的缓存时间，或使用条件缓存

### 3. 缓存配置分级

根据数据更新频率设置不同的缓存时间：

- **静态数据**：长时间缓存（1天或更长）
- **半静态数据**：中等时间缓存（1小时）
- **动态数据**：短时间缓存（5-15分钟）
- **实时数据**：极短时间或不缓存

### 4. 缓存失效机制

实现有效的缓存失效机制：

- **基于时间**：设置合理的`maxAge`
- **基于操作**：当数据发生变化时（如POST/PUT/DELETE请求后）主动清除相关缓存
- **手动刷新**：提供用户手动刷新数据的选项

## 实施步骤

### 第一步：集成axios-cache-adapter

1. 安装依赖：

```bash
npm install axios-cache-adapter
```

2. 创建缓存配置文件 `src/utils/api/cacheConfig.ts`：

```typescript
import { setupCache, buildMemoryStorage } from 'axios-cache-adapter';

// 创建内存存储实例
const memoryStorage = buildMemoryStorage();

// 基本缓存配置
export const defaultCache = setupCache({
  maxAge: 15 * 60 * 1000, // 默认15分钟缓存
  exclude: {
    // 只缓存GET请求
    methods: ['post', 'put', 'delete', 'patch']
  },
  key: req => req.url + JSON.stringify(req.params || {}),
  storage: memoryStorage,
  debug: process.env.NODE_ENV !== 'production'
});

// 长时间缓存配置（用于静态数据）
export const longTermCache = setupCache({
  maxAge: 24 * 60 * 60 * 1000, // 1天
  exclude: {
    methods: ['post', 'put', 'delete', 'patch']
  },
  key: req => `long_${req.url + JSON.stringify(req.params || {})}`,
  storage: memoryStorage,
  debug: process.env.NODE_ENV !== 'production'
});

// 不缓存配置
export const noCache = setupCache({
  maxAge: 0,
  storage: memoryStorage,
  debug: process.env.NODE_ENV !== 'production'
});

// 缓存管理工具
export const cacheManager = {
  // 清除指定URL的缓存
  clearCache: (url: string, params?: object) => {
    const key = url + JSON.stringify(params || {});
    memoryStorage.removeItem(key);
    memoryStorage.removeItem(`long_${key}`);
  },
  
  // 清除所有缓存
  clearAllCache: () => {
    memoryStorage.clear();
  }
};
```

### 第二步：创建缓存API客户端

创建 `src/utils/api/cachedApiClient.ts`：

```typescript
import axios, { AxiosRequestConfig } from 'axios';
import { defaultCache, longTermCache, noCache } from './cacheConfig';
import { API_BASE_URL } from '../../config';

// 创建API客户端类型
export enum ApiClientType {
  DEFAULT = 'default',
  LONG_TERM = 'longTerm',
  NO_CACHE = 'noCache'
}

// 获取指定类型的API客户端
export const getApiClient = (type: ApiClientType = ApiClientType.DEFAULT) => {
  switch (type) {
    case ApiClientType.LONG_TERM:
      return axios.create({
        baseURL: API_BASE_URL,
        adapter: longTermCache.adapter
      });
    
    case ApiClientType.NO_CACHE:
      return axios.create({
        baseURL: API_BASE_URL,
        adapter: noCache.adapter
      });
    
    case ApiClientType.DEFAULT:
    default:
      return axios.create({
        baseURL: API_BASE_URL,
        adapter: defaultCache.adapter
      });
  }
};

// 默认导出标准缓存客户端
export default getApiClient();

// 带缓存控制的请求函数
export const cachedRequest = async <T>(
  config: AxiosRequestConfig, 
  cacheType: ApiClientType = ApiClientType.DEFAULT
): Promise<T> => {
  const client = getApiClient(cacheType);
  const response = await client(config);
  return response.data as T;
};
```

### 第三步：创建服务层函数使用缓存API

创建 `src/services/userService.ts` 示例：

```typescript
import { cachedRequest, ApiClientType } from '../utils/api/cachedApiClient';
import { cacheManager } from '../utils/api/cacheConfig';

// 用户接口
interface User {
  id: string;
  name: string;
  email: string;
  // ...其他属性
}

// 获取用户列表（使用默认缓存）
export const getUsers = () => {
  return cachedRequest<User[]>({
    url: '/users',
    method: 'get'
  });
};

// 获取用户详情（使用长期缓存）
export const getUserById = (userId: string) => {
  return cachedRequest<User>(
    {
      url: `/users/${userId}`,
      method: 'get'
    },
    ApiClientType.LONG_TERM
  );
};

// 创建用户（不缓存，并清除用户列表缓存）
export const createUser = async (userData: Omit<User, 'id'>) => {
  const result = await cachedRequest<User>(
    {
      url: '/users',
      method: 'post',
      data: userData
    },
    ApiClientType.NO_CACHE
  );
  
  // 清除用户列表缓存
  cacheManager.clearCache('/users');
  
  return result;
};

// 更新用户（不缓存，并清除相关缓存）
export const updateUser = async (userId: string, userData: Partial<User>) => {
  const result = await cachedRequest<User>(
    {
      url: `/users/${userId}`,
      method: 'put',
      data: userData
    },
    ApiClientType.NO_CACHE
  );
  
  // 清除相关缓存
  cacheManager.clearCache('/users');
  cacheManager.clearCache(`/users/${userId}`);
  
  return result;
};
```

### 第四步：实现自定义Hook简化使用

创建 `src/hooks/useApi.ts`：

```typescript
import { useState, useEffect, useCallback } from 'react';
import { cachedRequest, ApiClientType } from '../utils/api/cachedApiClient';
import { AxiosRequestConfig } from 'axios';

interface UseApiOptions {
  cacheType?: ApiClientType;
  manual?: boolean; // 是否手动触发
  initialData?: any;
  onSuccess?: (data: any) => void;
  onError?: (error: any) => void;
}

export function useApi<T>(
  config: AxiosRequestConfig,
  options: UseApiOptions = {}
) {
  const {
    cacheType = ApiClientType.DEFAULT,
    manual = false,
    initialData = null,
    onSuccess,
    onError
  } = options;

  const [data, setData] = useState<T | null>(initialData);
  const [loading, setLoading] = useState(!manual);
  const [error, setError] = useState<any>(null);

  const execute = useCallback(async (newConfig?: AxiosRequestConfig) => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await cachedRequest<T>(newConfig || config, cacheType);
      setData(result);
      onSuccess && onSuccess(result);
      return result;
    } catch (err) {
      setError(err);
      onError && onError(err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [config, cacheType, onSuccess, onError]);

  useEffect(() => {
    if (!manual) {
      execute();
    }
  }, [manual, execute]);

  const refresh = useCallback(() => {
    return execute({
      ...config,
      headers: {
        ...config.headers,
        'x-refresh': 'true' // 添加头部强制刷新缓存
      }
    });
  }, [config, execute]);

  return { data, loading, error, execute, refresh };
}
```

## 缓存策略参考

下面是一些常见API请求的建议缓存策略：

| 数据类型 | 缓存时间 | 缓存类型 | 示例 |
|---------|---------|---------|------|
| 用户配置 | 1天 | LONG_TERM | 主题设置、语言偏好 |
| 下拉选项 | 1天 | LONG_TERM | 状态列表、类别选项 |
| 用户信息 | 1小时 | DEFAULT | 个人资料、权限 |
| 仪表板数据 | 15分钟 | DEFAULT | 统计摘要、图表数据 |
| 列表数据 | 5分钟 | DEFAULT | 用户列表、产品列表 |
| 实时数据 | 不缓存 | NO_CACHE | 实时通知、消息 |
| 写操作 | 不缓存 | NO_CACHE | 创建/更新/删除操作 |

## 测试计划

1. **缓存功能测试**：验证相同请求是否使用缓存
2. **缓存时间测试**：验证不同缓存策略的过期时间
3. **缓存清除测试**：验证写操作后是否正确清除相关缓存
4. **手动刷新测试**：验证用户强制刷新功能
5. **性能测试**：比较启用缓存前后的响应时间

## 性能监控

实现简单的性能监控，记录以下指标：

1. 缓存命中率
2. 平均响应时间（缓存vs非缓存）
3. 网络请求总数
4. 缓存数据大小

## 后续优化方向

1. 实现持久化缓存，在用户刷新页面后仍保留缓存
2. 实现离线模式支持，在断网情况下使用缓存数据
3. 实现预取策略，提前缓存可能需要的数据
4. 实现更智能的缓存失效策略，基于数据变化概率 