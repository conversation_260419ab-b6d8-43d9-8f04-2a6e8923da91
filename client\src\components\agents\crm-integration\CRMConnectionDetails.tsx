import React from 'react'
import { useTranslation } from 'react-i18next'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  ArrowLeft, 
  Settings, 
  Trash2, 
  RefreshCw,
  Activity,
  AlertCircle,
  CheckCircle
} from 'lucide-react'
import { CRMConnection } from '@/types/crmIntegration'

interface CRMConnectionDetailsProps {
  connectionId: string;
  onBack: () => void;
  onUpdate?: (connectionId: string, updates: Partial<CRMConnection>) => void;
  onDelete?: (connectionId: string) => void;
}

const CRMConnectionDetails: React.FC<CRMConnectionDetailsProps> = ({
  connectionId,
  onBack,
  onUpdate,
  onDelete
}) => {
  const { t } = useTranslation();

  // 模拟连接数据
  const connection: CRMConnection = {
    id: connectionId,
    providerId: 'salesforce', name: 'Salesforce Production', status: 'connected',
    createdAt: '2024-01-15T10:00:00Z',
    lastSyncAt: '2024-01-20T14:30:00Z', config: {
      instanceUrl: 'https://company.salesforce.com',
      customFields: {}, mappings: [], filters: []
    }, credentials: {
      type: 'oauth2',
      accessToken: '***',
      refreshToken: '***'
    },
    syncSettings: {
      enabled: true, direction: 'bidirectional', frequency: 'hourly',
      batchSize: 100,
      conflictResolution: 'crm_wins',
      syncObjects: []
    },
    healthCheck: {
      status: 'healthy',
      lastCheckedAt: '2024-01-20T15:00:00Z', issues: [], metrics: {
        responseTime: 150,
        successRate: 0.98,
        errorRate: 0.02
      }
    }, usage: {
      period: 'today',
      apiCalls: 1247,
      syncedRecords: 856, errors: 3,
      dataTransferred: 2048000
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected':
        return 'bg-green-100 text-green-800';
      case 'error':
        return 'bg-red-100 text-red-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={onBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h2 className="text-xl font-semibold">{connection.name}</h2>
            <p className="text-muted-foreground">Connection details and management</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Badge className={getStatusColor(connection.status)}>
            {connection.status === 'connected' && <CheckCircle className="h-3 w-3 mr-1" />}
            {connection.status === 'error' && <AlertCircle className="h-3 w-3 mr-1" />}
            {t(`agents.crmIntegration.status.${connection.status}`)}
          </Badge>
        </div>
      </div>

      {/* Connection Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2 mb-2">
              <Activity className="h-4 w-4 text-blue-600" />
              <span className="text-sm font-medium">API Calls Today</span>
            </div>
            <div className="text-2xl font-bold">{connection.usage.apiCalls.toLocaleString()}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2 mb-2">
              <RefreshCw className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium">Records Synced</span>
            </div>
            <div className="text-2xl font-bold">{connection.usage.syncedRecords.toLocaleString()}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2 mb-2">
              <AlertCircle className="h-4 w-4 text-red-600" />
              <span className="text-sm font-medium">Errors</span>
            </div>
            <div className="text-2xl font-bold">{connection.usage.errors}</div>
          </CardContent>
        </Card>
      </div>

      {/* Health Status */}
      <Card>
        <CardHeader>
          <CardTitle>Health Status</CardTitle>
          <CardDescription>Connection health and performance metrics</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {Math.round(connection.healthCheck.metrics.responseTime)}ms
              </div>
              <div className="text-sm text-green-700">Response Time</div>
            </div>
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">
                {Math.round(connection.healthCheck.metrics.successRate * 100)}%
              </div>
              <div className="text-sm text-blue-700">Success Rate</div>
            </div>
            <div className="text-center p-4 bg-yellow-50 rounded-lg">
              <div className="text-2xl font-bold text-yellow-600">
                {Math.round(connection.healthCheck.metrics.errorRate * 100)}%
              </div>
              <div className="text-sm text-yellow-700">Error Rate</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Sync Settings */}
      <Card>
        <CardHeader>
          <CardTitle>Sync Settings</CardTitle>
          <CardDescription>Configure how data is synchronized</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <span className="text-sm font-medium text-muted-foreground">Direction:</span>
              <div className="font-medium">{connection.syncSettings.direction}</div>
            </div>
            <div>
              <span className="text-sm font-medium text-muted-foreground">Frequency:</span>
              <div className="font-medium">{connection.syncSettings.frequency}</div>
            </div>
            <div>
              <span className="text-sm font-medium text-muted-foreground">Batch Size:</span>
              <div className="font-medium">{connection.syncSettings.batchSize}</div>
            </div>
            <div>
              <span className="text-sm font-medium text-muted-foreground">Conflict Resolution:</span>
              <div className="font-medium">{connection.syncSettings.conflictResolution}</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Actions</CardTitle>
          <CardDescription>Manage this connection</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2">
            <Button variant="outline">
              <Settings className="h-4 w-4 mr-2" />
              Configure
            </Button>
            <Button variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Sync Now
            </Button>
            <Button variant="outline">
              <Activity className="h-4 w-4 mr-2" />
              View Logs
            </Button>
            <Button 
              variant="destructive" 
              onClick={() => onDelete?.(connectionId)}
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Delete
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
};

export default CRMConnectionDetails;
