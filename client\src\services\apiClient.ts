/**
 * API客户端服务
 * 更新为使用新的缓存管理系统，保持向后兼容性
 */
import { apiCacheManager } from '../utils/api/apiCacheManager';

// API基础配置
// const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:3001/api';

// 向后兼容的API客户端
export const apiClient = {
  // GET请求（自动缓存）
  get: async <T = any>(endpoint: string, config?: any): Promise<T> => {
    const url = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
    return apiCacheManager.get<T>(url, config);
  },

  // POST请求（不缓存，会清除相关缓存）
  post: async <T = any>(endpoint: string, data?: any, config?: any): Promise<T> => {
    const url = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
    return apiCacheManager.post<T>(url, data, config);
  },

  // PUT请求（不缓存，会清除相关缓存）
  put: async <T = any>(endpoint: string, data?: any, config?: any): Promise<T> => {
    const url = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
    return apiCacheManager.put<T>(url, data, config);
  },

  // DELETE请求（不缓存，会清除相关缓存）
  delete: async <T = any>(endpoint: string, config?: any): Promise<T> => {
    const url = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
    return apiCacheManager.delete<T>(url, config);
  },

  // 手动清除缓存
  clearCache: (endpoint?: string) => {
    if (endpoint) {
      apiCacheManager.invalidateRelated(endpoint);
    } else {
      apiCacheManager.clearAll();
    }
  },

  // 获取缓存统计
  getCacheStats: () => {
    return apiCacheManager.getStats();
  }
};

// 导出兼容性别名
export default apiClient;

// 导出新的API管理器（推荐使用）
export { apiCacheManager };

// 常用API端点常量
export const API_ENDPOINTS = {
  // 用户相关
  USERS: '/users',
  USER_PROFILE: '/users/profile',
  USER_PREFERENCES: '/users/preferences',
  
  // 仪表板相关
  DASHBOARD_METRICS: '/dashboard/metrics',
  DASHBOARD_KPI: '/dashboard/kpi',
  
  // 分析相关
  ANALYTICS_OVERVIEW: '/analytics/overview',
  ANALYTICS_MODULES: '/analytics/modules',
  
  // AI代理相关
  AGENTS: '/agents',
  AGENT_SETTINGS: '/agents/settings',
  
  // 预约相关
  BOOKINGS: '/bookings',
  BOOKING_SETTINGS: '/booking/settings',
  
  // 内容相关
  CONTENT: '/content',
  CONTENT_GENERATE: '/content/generate',
  
  // 平台相关
  PLATFORMS: '/platforms',
  PLATFORM_INTEGRATION: '/platforms/integration',
  
  // 实时数据
  REALTIME_STATUS: '/realtime/status',
  REALTIME_NOTIFICATIONS: '/realtime/notifications',
  
  // 监控相关
  MONITORING_HEALTH: '/monitoring/health',
  MONITORING_PERFORMANCE: '/monitoring/performance',
  
  // 支付相关
  PAYMENTS: '/payments',
  BILLING: '/billing',
  SUBSCRIPTIONS: '/subscriptions'
};

// 请求类型枚举
export enum RequestMethod {
  GET = 'GET',
  POST = 'POST',
  PUT = 'PUT',
  DELETE = 'DELETE'
}

// 错误处理类型
export interface ApiError {
  message: string;
  status?: number;
  code?: string;
  details?: any;
}

// API响应类型
export interface ApiResponse<T = any> {
  data: T;
  success: boolean;
  message?: string;
  timestamp?: string;
}

// 分页响应类型
export interface PaginatedResponse<T = any> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  success: boolean;
  message?: string;
}

// 创建类型化的API客户端
export function createTypedApiClient<T>() {
  return {
    get: (endpoint: string, config?: any): Promise<T> => 
      apiClient.get<T>(endpoint, config),
    
    post: (endpoint: string, data?: any, config?: any): Promise<T> => 
      apiClient.post<T>(endpoint, data, config),
    
    put: (endpoint: string, data?: any, config?: any): Promise<T> => 
      apiClient.put<T>(endpoint, data, config),
    
    delete: (endpoint: string, config?: any): Promise<T> => 
      apiClient.delete<T>(endpoint, config)
  };
}

// 批量请求工具
export async function batchApiRequests<T extends Record<string, any>>(
  requests: Record<keyof T, { endpoint: string; method?: RequestMethod; data?: any; }>
): Promise<Record<keyof T, any>> {
  const promises = Object.entries(requests).map(async ([key, request]) => {
    const { endpoint, method = RequestMethod.GET, data } = request;
    
    try {
      let result;
      switch (method) {
        case RequestMethod.GET:
          result = await apiClient.get(endpoint);
          break;
        case RequestMethod.POST:
          result = await apiClient.post(endpoint, data);
          break;
        case RequestMethod.PUT:
          result = await apiClient.put(endpoint, data);
          break;
        case RequestMethod.DELETE:
          result = await apiClient.delete(endpoint);
          break;
        default:
          throw new Error(`Unsupported method: ${method}`);
      }
      
      return [key, result];
    } catch (error) {
      console.error(`[Batch API] Failed request for ${key}:`, error);
      return [key, null];
    }
  });
  
  const results = await Promise.all(promises);
  return Object.fromEntries(results);
}

// 重试请求工具
export async function retryApiRequest<T>(
  requestFn: () => Promise<T>,
  maxRetries = 3,
  delay = 1000
): Promise<T> {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await requestFn();
    } catch (error) {
      if (attempt === maxRetries) {
        throw error;
      }
      
      console.warn(`[API Retry] Attempt ${attempt} failed, retrying in ${delay}ms...`);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw new Error('All retry attempts failed');
}

// API健康检查
export async function checkApiHealth(): Promise<{ status: string; timestamp: string }> {
  try {
    // const response = await apiClient.get('/health');
    return {
      status: 'healthy',
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      timestamp: new Date().toISOString()
    };
  }
}

// API错误检查辅助函数
export const isApiError = (error: any): error is ApiError => {
  return error && typeof error.message === 'string';
};

export const isNetworkError = (error: any): boolean => {
  return error && error.code === 'NETWORK_ERROR';
};

export const getErrorMessage = (error: any): string => {
  if (isApiError(error)) {
    return error.message;
  }
  if (error instanceof Error) {
    return error.message;
  }
  return 'Unknown error occurred';
};

// API状态监控
export class ApiStatusMonitor {
  private statusChecks: { [endpoint: string]: { lastCheck: Date; isHealthy: boolean } } = {};
  
  async checkEndpoint(endpoint: string): Promise<boolean> {
    try {
      await apiClient.get(endpoint);
      this.statusChecks[endpoint] = {
        lastCheck: new Date(),
        isHealthy: true
      };
      return true;
    } catch (error) {
      this.statusChecks[endpoint] = {
        lastCheck: new Date(),
        isHealthy: false
      };
      return false;
    }
  }
  
  getEndpointStatus(endpoint: string): { lastCheck?: Date; isHealthy?: boolean } {
    return this.statusChecks[endpoint] || {};
  }
  
  getAllStatuses(): typeof this.statusChecks {
    return { ...this.statusChecks };
  }
}

// 创建默认的API状态监控实例
export const apiStatusMonitor = new ApiStatusMonitor(); 