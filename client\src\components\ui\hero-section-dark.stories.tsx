import type { <PERSON><PERSON>, StoryObj } from '@storybook/react'
import { HeroSection } from './hero-section-dark'

const meta: Meta<typeof HeroSection> = {
  title: 'Components/UI/HeroSection',
  component: HeroSection,
  parameters: {
    layout: 'fullscreen',
  },
  argTypes: {
    title: {
      control: 'text',
      description: 'Small title text at the top',
    },
    subtitle: {
      control: 'object',
      description: 'Main heading with regular and gradient text',
    },
    description: {
      control: 'text',
      description: 'Description text below the main heading',
    },
    ctaText: {
      control: 'text',
      description: 'Call-to-action button text',
    },
    ctaHref: {
      control: 'text',
      description: 'Call-to-action button link',
    },
    bottomImage: {
      control: 'object',
      description: 'Images for light and dark mode',
    },
    gridOptions: {
      control: 'object',
      description: 'Grid animation configuration',
    },
  },
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    title: "Build products for everyone",
    subtitle: {
      regular: "Designing your projects faster with ",
      gradient: "the largest figma UI kit.",
    },
    description: "Sed ut perspiciatis unde omnis iste natus voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae.",
    ctaText: "Browse courses",
    ctaHref: "#",
    bottomImage: {
      light: "https://farmui.vercel.app/dashboard-light.png",
      dark: "https://farmui.vercel.app/dashboard.png",
    },
    gridOptions: {
      angle: 65,
      opacity: 0.5,
      cellSize: 60,
      lightLineColor: "gray",
      darkLineColor: "gray",
    },
  },
}

export const iTeraBiz: Story = {
  args: {
    title: "AI-Powered Content Creation Platform",
    subtitle: {
      regular: "Transform your ideas into ",
      gradient: "beautiful digital experiences",
    },
    description: "Transform your content creation process with our advanced AI platform. Generate, optimize, and distribute engaging content across multiple channels in minutes, not hours.",
    ctaText: "Get Started Free",
    ctaHref: "/register",
    bottomImage: {
      light: "https://images.unsplash.com/photo-1551434678-e076c223a692?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2340&q=80",
      dark: "https://images.unsplash.com/photo-1518709268805-4e9042af2176?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2340&q=80",
    },
    gridOptions: {
      angle: 65,
      opacity: 0.4,
      cellSize: 50,
      lightLineColor: "#4a4a4a",
      darkLineColor: "#2a2a2a",
    },
  },
} 