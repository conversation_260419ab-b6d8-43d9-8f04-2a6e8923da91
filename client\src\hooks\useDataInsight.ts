import { useState, useCallback } from 'react';

export interface DataInsightConfig {
  endpoint: string;
  initialFilters?: Record<string, any>;
}

export interface DataInsightState<T = any> {
  data: T | null;
  isLoading: boolean;
  error: Error | null;
}

export interface DataInsightActions {
  refresh: () => Promise<void>;
  updateFilters: (filters: Record<string, any>) => void;
}

export function useDataInsight<T = any>(
  config: DataInsightConfig
): [DataInsightState<T>, DataInsightActions] {
  const [state, setState] = useState<DataInsightState<T>>({
    data: null,
    isLoading: false,
    error: null,
  });

  const refresh = useCallback(async () => {
    console.log('Refreshing data for:', config.endpoint);
    setState(s => ({ ...s, isLoading: true }));
    await new Promise(resolve => setTimeout(resolve, 1000));
    setState(s => ({ ...s, isLoading: false, data: {} as T }));
  }, [config.endpoint]);

  const updateFilters = useCallback((filters: Record<string, any>) => {
    console.log('Updating filters:', filters);
  }, []);

  return [state, { refresh, updateFilters }];
}