import React, { useState, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import ReactFlow, {
  Node,
  Edge,
  addEdge,
  Connection,
  useNodesState,
  useEdgesState,
  Controls,
  MiniMap,
  Background,
  BackgroundVariant,
  ReactFlowProvider,
  Panel
} from 'reactflow';
import 'reactflow/dist/style.css';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Save,
  Play,
  Download,
  Grid3X3,
  Map,
  Plus
} from 'lucide-react';
import { cn } from '@/lib/utils';

import { FlowNode, FlowEdge, FlowNodeType, NODE_TYPE_DEFINITIONS } from '@/types/flowBuilder';
import FlowNodeComponent from './FlowNodeComponent';
import NodePalette from './NodePalette';
import NodeConfigPanel from './NodeConfigPanel';
import FlowValidation from './FlowValidation';

interface VisualFlowBuilderProps {
  initialNodes?: FlowNode[];
  initialEdges?: FlowEdge[];
  onSave?: (nodes: FlowNode[], edges: FlowEdge[]) => void;
  onTest?: (nodes: FlowNode[], edges: FlowEdge[]) => void;
  className?: string;
}

// 自定义节点类型映射
const nodeTypes = {
  trigger: FlowNodeComponent,
  condition: FlowNodeComponent,
  ai_response: FlowNodeComponent,
  human_handoff: FlowNodeComponent,
  api_call: FlowNodeComponent,
  delay: FlowNodeComponent,
  webhook: FlowNodeComponent,
  email: FlowNodeComponent,
  sms: FlowNodeComponent,
  variable_set: FlowNodeComponent,
  jump: FlowNodeComponent,
  end: FlowNodeComponent
};

const VisualFlowBuilder: React.FC<VisualFlowBuilderProps> = ({
  initialNodes = [],
  initialEdges = [],
  onSave,
  onTest,
  className
}) => {
  const { t } = useTranslation();
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);
  const [selectedNode, setSelectedNode] = useState<string | null>(null);
  const [flowName, setFlowName] = useState('');
  const [flowDescription, setFlowDescription] = useState('');
  const [showMiniMap, setShowMiniMap] = useState(true);
  const [showGrid, setShowGrid] = useState(true);
  const [isDirty, setIsDirty] = useState(false);

  // 连接处理
  const onConnect = useCallback(
    (params: Connection) => {
      const newEdge: Edge = {
        ...params,
        id: `edge-${Date.now()}`,
        type: 'smoothstep',
        animated: true
};
      setEdges((eds) => addEdge(newEdge, eds));
      setIsDirty(true);
},
    [setEdges]
  );

  // 节点选择处理
  const onNodeClick = useCallback((event: React.MouseEvent, node: Node) => {
    setSelectedNode(node.id);
  }, []);

  // 添加新节点
  const addNode = useCallback((nodeType: FlowNodeType, position?: { x: number; y: number }) => {
    const nodeDefinition = NODE_TYPE_DEFINITIONS[nodeType];
    const newNode: FlowNode = {
      id: `node-${Date.now()}`,
      type: nodeType,
      position: position || { x: Math.random() * 400, y: Math.random() * 400 },
      data: {
        label: nodeDefinition.label,
        description: nodeDefinition.description,
        config: {}
      },
      style: {
        backgroundColor: nodeDefinition.color,
        color: 'white',
        border: '2px solid transparent',
        borderRadius: '8px',
        padding: '10px',
        minWidth: '150px',
        textAlign: 'center'
      }
    };

    setNodes((nds) => [...nds, newNode]);
    setSelectedNode(newNode.id);
    setIsDirty(true);
  }, [setNodes]);

  // 删除选中节点
  const deleteSelectedNode = useCallback(() => {
    if (selectedNode) {
      setNodes((nds) => nds.filter((node) => node.id !== selectedNode));
      setEdges((eds) => eds.filter((edge) => 
        edge.source !== selectedNode && edge.target !== selectedNode
      ));
      setSelectedNode(null);
      setIsDirty(true);
    }
  }, [selectedNode, setNodes, setEdges]);

  // 更新节点配置
  const updateNodeConfig = useCallback((nodeId: string, config: any) => {
    setNodes((nds) =>
      nds.map((node) =>
        node.id === nodeId
          ? { ...node, data: { ...node.data, config: { ...node.data.config, ...config } } }
          : node
      )
    );
    setIsDirty(true);
  }, [setNodes]);

  // 保存流程
  const handleSave = useCallback(() => {
    if (onSave) {
      onSave(nodes, edges);
      setIsDirty(false);
    }
  }, [nodes, edges, onSave]);

  // 测试流程
  const handleTest = useCallback(() => {
    if (onTest) {
      onTest(nodes, edges);
    }
  }, [nodes, edges, onTest]);

  // 导出流程
  const handleExport = useCallback(() => {
    const flowData = {
      name: flowName,
      description: flowDescription,
      nodes,
      edges,
      metadata: {
        version: '1.0.0',
        createdAt: new Date().toISOString(),
        nodeCount: nodes.length,
        edgeCount: edges.length
      }
    };

    const dataStr = JSON.stringify(flowData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${flowName || 'flow'}.json`;
    link.click();
    URL.revokeObjectURL(url);
  }, [flowName, flowDescription, nodes, edges]);

  // 获取选中的节点数据
  const selectedNodeData = useMemo(() => {
    return selectedNode ? nodes.find(node => node.id === selectedNode) : null;
  }, [selectedNode, nodes]);

  return (
    <div className={cn("h-full flex flex-col", className)}>
      {/* 工具栏 */}
      <div className="flex items-center justify-between p-4 border-b bg-background">
        <div className="flex items-center gap-4">
          <div className="flex flex-col gap-1">
            <Input
              value={flowName}
              onChange={(e) => setFlowName(e.target.value)}
              placeholder={t('agents.flowBuilder.flowNamePlaceholder')}
              className="w-48"
            />
            <Input
              value={flowDescription}
              onChange={(e) => setFlowDescription(e.target.value)}
              placeholder={t('agents.flowBuilder.flowDescriptionPlaceholder')}
              className="w-48 text-xs"
            />
          </div>
          {isDirty && (
            <Badge variant="secondary" className="text-xs">
              Unsaved changes
            </Badge>
          )}
        </div>

        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={() => setShowGrid(!showGrid)}>
            <Grid3X3 className="h-4 w-4" />
          </Button>
          <Button variant="outline" size="sm" onClick={() => setShowMiniMap(!showMiniMap)}>
            <Map className="h-4 w-4" />
          </Button>
          <Separator orientation="vertical" className="h-6" />
          <Button variant="outline" size="sm" onClick={handleExport}>
            <Download className="h-4 w-4 mr-1" />
            {t('agents.flowBuilder.exportFlow')}
          </Button>
          <Button variant="outline" size="sm" onClick={handleTest}>
            <Play className="h-4 w-4 mr-1" />
            {t('agents.flowBuilder.testFlow')}
          </Button>
          <Button onClick={handleSave} disabled={!isDirty}>
            <Save className="h-4 w-4 mr-1" />
            {t('agents.flowBuilder.saveFlow')}
          </Button>
        </div>
      </div>

      <div className="flex-1 flex">
        {/* 节点面板 */}
        <div className="w-64 border-r bg-muted/30">
          <NodePalette onAddNode={addNode} />
        </div>

        {/* 主画布区域 */}
        <div className="flex-1 relative">
          <ReactFlow
            nodes={nodes}
            edges={edges}
            onNodesChange={onNodesChange}
            onEdgesChange={onEdgesChange}
            onConnect={onConnect}
            onNodeClick={onNodeClick}
            nodeTypes={nodeTypes}
            fitView
            attributionPosition="bottom-left"
          >
            <Controls />
            {showMiniMap && (
              <MiniMap 
                nodeColor={(node) => {
                  const nodeType = node.type as FlowNodeType;
                  return NODE_TYPE_DEFINITIONS[nodeType]?.color || '#6B7280';
                }}
                nodeStrokeWidth={3}
                zoomable
                pannable
              />
            )}
            {showGrid && (
              <Background 
                variant={BackgroundVariant.Dots} 
                gap={20} 
                size={1}
                color="#E5E7EB"
              />
            )}
            
            {/* 快速添加按钮 */}
            <Panel position="top-left">
              <Button
                size="sm"
                onClick={() => addNode(FlowNodeType.TRIGGER)}
                className="mb-2"
              >
                <Plus className="h-4 w-4 mr-1" />
                Add Trigger
              </Button>
            </Panel>
          </ReactFlow>
        </div>

        {/* 配置面板 */}
        <div className="w-80 border-l bg-background">
          {selectedNodeData ? (
            <NodeConfigPanel
              node={selectedNodeData}
              onUpdateConfig={(config) => updateNodeConfig(selectedNodeData.id, config)}
              onDelete={deleteSelectedNode}
            />
          ) : (
            <div className="p-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Flow Properties</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="flowName">{t('agents.flowBuilder.flowName')}</Label>
                    <Input
                      id="flowName"
                      value={flowName}
                      onChange={(e) => setFlowName(e.target.value)}
                      placeholder={t('agents.flowBuilder.flowNamePlaceholder')}
                    />
                  </div>
                  <div>
                    <Label htmlFor="flowDescription">{t('agents.flowBuilder.flowDescription')}</Label>
                    <Textarea
                      id="flowDescription"
                      value={flowDescription}
                      onChange={(e) => setFlowDescription(e.target.value)}
                      placeholder={t('agents.flowBuilder.flowDescriptionPlaceholder')}
                      rows={3}
                    />
                  </div>
                  <FlowValidation nodes={nodes} edges={edges} />
                </CardContent>
              </Card>
            </div>
          )}
        </div>
      </div>
    </div>
  )
};

// 包装组件以提供ReactFlow上下文
const VisualFlowBuilderWrapper: React.FC<VisualFlowBuilderProps> = (props) => {
  return (
    <ReactFlowProvider>
      <VisualFlowBuilder {...props} />
    </ReactFlowProvider>
  )
};

export default VisualFlowBuilderWrapper;
