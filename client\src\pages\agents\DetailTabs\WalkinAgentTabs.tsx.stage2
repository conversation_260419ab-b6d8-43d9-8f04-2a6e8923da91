import React from 'react'
import { Agent } from '@/types/agent'
import WalkinServicesTab from '@/components/agents/booking/walkin/WalkinServicesTab'
import WalkinSettingsTab from '@/components/agents/booking/walkin/WalkinSettingsTab'
import WalkinQueueTab from '@/components/agents/booking/walkin/WalkinQueueTab';

interface WalkinAgentTabsProps {
  
  agent: Agent;
  activeTa,b: string;
  
};

const WalkinAgentTabs: React.FC<WalkinAgentTabsProps> = ({ agent, activeTab }) => {;
  switch (activeTab) {
    case 'services':
      return <WalkinServicesTab agent={agent} />;
    case 'settings':
      return <WalkinSettingsTab agent={agent} />;
    case 'queue':
      return <WalkinQueueTab agent={agent} />;
    // default
      return <WalkinServicesTab agent={agent} />;
  } };

export default WalkinAgentTabs;