import React from 'react'
import { useTranslation } from 'react-i18next'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  CheckCircle, 
  AlertCircle, 
  Clock, 
  Settings, 
  RefreshCw, 
  Eye,
  Activity,
  Wifi,
  WifiOff,
  AlertTriangle
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { CRMConnection, CRMProvider } from '@/types/crmIntegration'

interface CRMConnectionCardProps {
  connection: CRMConnection;
  provider: CRMProvider;
  onView: () => void;
  onConfigure: () => void;
  onSync: () => void;
  compact?: boolean;
  className?: string;
}

const CRMConnectionCard: React.FC<CRMConnectionCardProps> = ({
  connection,
  provider,
  onView,
  onConfigure,
  onSync,
  compact = false,
  className
}) => {
  const { t } = useTranslation();

  // 获取状态图标和颜色
  const getStatusInfo = () => {
    switch (connection.status) {
      case 'connected':
        return {
          icon: <CheckCircle className="h-4 w-4" />, color: 'text-green-600',
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200', label: t('agents.crmIntegration.status.connected')
        };
      case 'error':
        return {
          icon: <AlertCircle className="h-4 w-4" />, color: 'text-red-600',
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200', label: t('agents.crmIntegration.status.error')
        };
      case 'pending':
        return {
          icon: <Clock className="h-4 w-4" />, color: 'text-yellow-600',
          bgColor: 'bg-yellow-50',
          borderColor: 'border-yellow-200', label: t('agents.crmIntegration.status.pending')
        };
      case 'disconnected':
        return {
          icon: <WifiOff className="h-4 w-4" />, color: 'text-gray-600',
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200', label: t('agents.crmIntegration.status.disconnected')
        };
      default:
        return {
          icon: <AlertTriangle className="h-4 w-4" />, color: 'text-gray-600',
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200', label: 'Unknown'
        };
    }
  };

  // 获取健康状态信息
  const getHealthInfo = () => {
    if (!connection.healthCheck) return null;

    switch (connection.healthCheck.status) {
      case 'healthy':
        return {
          icon: <Wifi className="h-3 w-3" />, color: 'text-green-600', label: t('agents.crmIntegration.health.healthy')
        };
      case 'warning':
        return {
          icon: <AlertTriangle className="h-3 w-3" />, color: 'text-yellow-600', label: t('agents.crmIntegration.health.warning')
        };
      case 'error':
        return {
          icon: <AlertCircle className="h-3 w-3" />, color: 'text-red-600', label: t('agents.crmIntegration.health.error')
        };
      default:
        return null;
    }
  };

  // 格式化相对时间
  const formatRelativeTime = (dateString?: string) => {
    if (!dateString) return 'Never';
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    return `${diffDays}d ago`;
  };

  const statusInfo = getStatusInfo();
  const healthInfo = getHealthInfo();

  return (
    <Card className={cn(
      "transition-all duration-200 hover:shadow-md",
      statusInfo.borderColor,
      className
    )}>
      <CardHeader className={cn("pb-3", compact && "pb-2")}>
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            {/* 提供商Logo */}
            <div className="w-10 h-10 rounded-lg bg-gray-100 flex items-center justify-center">
              {/* 这里应该是实际的logo图片 */}
              <Activity className="h-5 w-5 text-gray-600" />
            </div>
            <div>
              <CardTitle className={cn("text-base", compact && "text-sm")}>
                {connection.name}
              </CardTitle>
              <CardDescription className="text-xs">
                {provider.displayName}
              </CardDescription>
            </div>
          </div>
          
          {/* 状态指示器 */}
          <div className="flex items-center gap-2">
            {healthInfo && (
              <div className={cn("flex items-center gap-1", healthInfo.color)}>
                {healthInfo.icon}
              </div>
            )}
            <Badge className={cn(statusInfo.bgColor, statusInfo.color)} variant="secondary">
              {statusInfo.icon}
              <span className="ml-1">{statusInfo.label}</span>
            </Badge>
          </div>
        </div>
      </CardHeader>

      <CardContent className={cn("space-y-4", compact && "space-y-2")}>
        {/* 连接信息 */}
        {!compact && (
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-muted-foreground">Created:</span>
              <div className="font-medium">{formatRelativeTime(connection.createdAt)}</div>
            </div>
            <div>
              <span className="text-muted-foreground">Last Sync:</span>
              <div className="font-medium">{formatRelativeTime(connection.lastSyncAt)}</div>
            </div>
          </div>
        )}

        {/* 同步设置 */}
        {connection.syncSettings && !compact && (
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="font-medium">Sync Settings</span>
              <Badge variant="outline" className="text-xs">
                {connection.syncSettings.direction}
              </Badge>
            </div>
            <div className="flex items-center gap-4 text-xs text-muted-foreground">
              <div className="flex items-center gap-1">
                <RefreshCw className="h-3 w-3" />
                {connection.syncSettings.frequency}
              </div>
              <div className="flex items-center gap-1">
                <Activity className="h-3 w-3" />
                {connection.syncSettings.syncObjects.filter(obj => obj.enabled).length} objects
              </div>
            </div>
          </div>
        )}

        {/* 使用统计 */}
        {connection.usage && !compact && (
          <div className="grid grid-cols-3 gap-2 text-xs">
            <div className="text-center p-2 bg-gray-50 rounded">
              <div className="font-medium">{connection.usage.apiCalls.toLocaleString()}</div>
              <div className="text-muted-foreground">API Calls</div>
            </div>
            <div className="text-center p-2 bg-gray-50 rounded">
              <div className="font-medium">{connection.usage.syncedRecords.toLocaleString()}</div>
              <div className="text-muted-foreground">Records</div>
            </div>
            <div className="text-center p-2 bg-gray-50 rounded">
              <div className="font-medium">{connection.usage.errors}</div>
              <div className="text-muted-foreground">Errors</div>
            </div>
          </div>
        )}

        {/* 健康检查详情 */}
        {connection.healthCheck && !compact && (
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="font-medium">Health Check</span>
              <span className="text-xs text-muted-foreground">
                {formatRelativeTime(connection.healthCheck.lastCheckedAt)}
              </span>
            </div>
            <div className="grid grid-cols-3 gap-2 text-xs">
              <div>
                <span className="text-muted-foreground">Response:</span>
                <div className="font-medium">{connection.healthCheck.metrics.responseTime}ms</div>
              </div>
              <div>
                <span className="text-muted-foreground">Success:</span>
                <div className="font-medium">{Math.round(connection.healthCheck.metrics.successRate * 100)}%</div>
              </div>
              <div>
                <span className="text-muted-foreground">Errors:</span>
                <div className="font-medium">{Math.round(connection.healthCheck.metrics.errorRate * 100)}%</div>
              </div>
            </div>
          </div>
        )}

        {/* 错误信息 */}
        {connection.healthCheck?.issues.length > 0 && !compact && (
          <div className="space-y-1">
            {connection.healthCheck.issues.slice(0, 2).map((issue, index) => (
              <div key={index} className="flex items-start gap-2 p-2 bg-red-50 rounded text-xs">
                <AlertCircle className="h-3 w-3 text-red-500 mt-0.5 flex-shrink-0" />
                <span className="text-red-700">{issue.message}</span>
              </div>
            ))}
            {connection.healthCheck.issues.length > 2 && (
              <div className="text-xs text-muted-foreground">
                +{connection.healthCheck.issues.length - 2} more issues
              </div>
            )}
          </div>
        )}

        {/* 操作按钮 */}
        <div className="flex gap-2">
          <Button variant="outline" size="sm" onClick={onView} className="flex-1">
            <Eye className="h-4 w-4 mr-1" />
            {compact ? 'View' : 'View Details'}
          </Button>
          
          {!compact && (
            <>
              <Button variant="outline" size="sm" onClick={onConfigure}>
                <Settings className="h-4 w-4 mr-1" />
                Configure
              </Button>
              
              <Button 
                variant="outline" 
                size="sm" 
                onClick={onSync}
                disabled={connection.status !== 'connected'}
              >
                <RefreshCw className="h-4 w-4 mr-1" />
                Sync
              </Button>
            </>
          )}
        </div>

        {/* 紧凑模式的额外信息 */}
        {compact && (
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <span>Last sync: {formatRelativeTime(connection.lastSyncAt)}</span>
            {connection.usage && (
              <span>{connection.usage.syncedRecords.toLocaleString()} records</span>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
};

export default CRMConnectionCard;
