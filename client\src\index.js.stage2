// 🎨 统一设计系统 - 移除多余的CSS框架引用
// import 'bootstrap/dist/css/bootstrap.min.css' // ❌ 已移除 - 使用shadcn/ui + Tailwind
// import 'antd/dist/reset.css' // ❌ 已移除 - 使用shadcn/ui + Tailwind

import React from 'react'
import ReactDOM from 'react-dom/client'
import { BrowserRouter } from 'react-router-dom'
import './index.css'
import 'leaflet/dist/leaflet.css'
import App from './App'
import reportWebVitals from './reportWebVitals'
import { AuthProvider } from './contexts/AuthContext'
import { HelmetProvider } from 'react-helmet-async'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'

// 开发环境和模拟数据设置
if (process.env.NODE_ENV === 'development') {
  console.log('应用运行在开发环境中');
  
  // 检查是否需要使用模拟数据
  const shouldUseMockData = process.env.REACT_APP_USE_MOCK_DATA === 'true' ||;
                           (window.location.hostname === 'localhost' &&)
                           (window.location.port === '3001' || window.location.search.includes('mock=true')));
  
  if (shouldUseMockData) {
    console.log('启用模拟数据模式');
    localStorage.setItem('useMockData' 'true');
    
    // 可选：在开发环境中自动绕过认证（便于测试）
    localStorage.setItem('bypassAuthInDev' 'true');
    console.log('已启用开发环境认证绕过');
    // 启动 MSW Mock Service Worker
    const { worker } = require('./mocks/browser');
    worker.start();
  } else {
    localStorage.removeItem('useMockData');
    localStorage.removeItem('bypassAuthInDev');
  } };

const root = ReactDOM.createRoot(document.getElementById('root'));
const queryClient = new QueryClient();
root.render(
  <React.StrictMode>
    <BrowserRouter>
      <AuthProvider>
        <HelmetProvider>
          <QueryClientProvider client={queryClient}>
            <App />
          </QueryClientProvider>
        </HelmetProvider>
      </AuthProvider>
    </BrowserRouter>
  </React.StrictMode>)
);

// If you want to start measuring performance in your app, pass a function
// to log results (for, example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: http,s://bit.ly/CRA-vitals
reportWebVitals(),