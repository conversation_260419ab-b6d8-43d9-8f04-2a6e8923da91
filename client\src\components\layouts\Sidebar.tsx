import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { cn } from '../../lib/utils';
import { 
  Home, 
  BarChart3, 
  Settings, 
  Users, 
  Bot,
  MessageSquare,
  Calendar,
  CreditCard
} from 'lucide-react';

interface SidebarProps {
  isCollapsed?: boolean;
  className?: string;
}

interface NavItem {
  title: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  children?: NavItem[];
}

const navigationItems: NavItem[] = [
  {
    title: '概览', href: '/dashboard', icon: Home,
  },
  {
    title: '智能体', href: '/agents', icon: Bot,
  },
  {
    title: '聊天', href: '/chat', icon: MessageSquare,
  },
  {
    title: '预约', href: '/booking', icon: Calendar,
  },
  {
    title: '分析', href: '/analytics', icon: BarChart3,
  },
  {
    title: '用户管理', href: '/staff', icon: Users,
  },
  {
    title: '计费', href: '/billing', icon: CreditCard,
  },
  {
    title: '设置', href: '/settings', icon: Settings,
  },
];

export const Sidebar: React.FC<SidebarProps> = ({ 
  isCollapsed = false, 
  className 
}) => {
  const location = useLocation();

  const isActive = (href: string) => {
    return location.pathname === href || location.pathname.startsWith(href + '/');
  };

  return (
    <div className={cn(
      "flex flex-col h-full bg-white border-r border-gray-200",
      isCollapsed ? "w-16" : "w-64",
      className
    )}>
      {/* Logo */}
      <div className="flex items-center justify-center h-16 border-b border-gray-200">
        {!isCollapsed ? (
          <h1 className="text-xl font-bold text-violet-600">iBuddy</h1>
        ) : (
          <div className="w-8 h-8 bg-violet-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold">iB</span>
          </div>
        )}
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4 space-y-2">
        {navigationItems.map((item) => {
          const Icon = item.icon;
          const active = isActive(item.href);

          return (
            <Link
              key={item.href}
              to={item.href}
              className={cn(
                "flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors",
                active 
                  ? "bg-violet-100 text-violet-700" 
                  : "text-gray-600 hover:bg-gray-100 hover:text-gray-900",
                isCollapsed && "justify-center"
              )}
              title={isCollapsed ? item.title : undefined}
            >
              <Icon className={cn("h-5 w-5", !isCollapsed && "mr-3")} />
              {!isCollapsed && <span>{item.title}</span>}
            </Link>
          );
        })}
      </nav>

      {/* User section */}
      <div className="p-4 border-t border-gray-200">
        <div className={cn(
          "flex items-center",
          isCollapsed ? "justify-center" : "space-x-3"
        )}>
          <div className="w-8 h-8 bg-gray-300 rounded-full"></div>
          {!isCollapsed && (
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900 truncate">用户</p>
              <p className="text-xs text-gray-500 truncate"><EMAIL></p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Sidebar; 