/**
 * 性能分析工具
 * 用于分析性能指标，识别瓶颈并提供优化建议
 * 
 * @version 1.0.0
 */

// import { realUserMonitoring } from '../monitoring/RealUserMonitoring';
// import { optimizationManager } from '../optimization/optimizationManager';
// import { resourcePreloader } from '../optimization/resourcePreloader';

// 资源计时数据类型
interface ResourceTimingData {
  js: number[];
  css: number[];
  img: number[];
  font: number[];
  api: number[];
}

// 性能瓶颈分析类型
interface PerformanceBottleneck {
  type: string;
  score: number;
  impact: 'high' | 'medium' | 'low';
  recommendations: string[];
}

// 资源计时项类型
// interface ResourceTimingItem {
//   fileType: string;
//   duration: number;
//   url: string;
// }

// Web指标报告类型
interface WebVitalsReport {
  lcp: { values: number[] };
  fid: { values: number[] };
  cls: { values: number[] };
  fcp: { values: number[] };
  ttfb: { values: number[] };
}

// 性能报告类型
interface PerformanceReport {
  webVitals: {
    lcp: number[];
    fid: number[];
    cls: number[];
    fcp: number[];
    ttfb: number[];
  };
  resourceTiming: ResourceTimingData;
  cacheHitRate: number;
  bottlenecks: PerformanceBottleneck[];
  timeRange: number;
  timestamp: string;
}

// 缓存统计类型
interface CacheStats {
  hitRate: number;
  totalSize: number;
  itemCount: number;
}

class PerformanceAnalyzer {
  private isInitialized: boolean = false;
  private sampleSize: number = 100;
  private lastAnalysisTimestamp: number = 0;
  private cachedBottlenecks: PerformanceBottleneck[] | null = null;
  
  /**
   * 初始化性能分析器
   */
  public initialize(): void {
    if (this.isInitialized) return;
    
    // 监听性能变化 - 由于RUM不支持事件订阅，这里我们改为周期性检查
    if (typeof window !== 'undefined') {
      setInterval(() => {
        this.onPerformanceSampleCollected()
}, 60000); // 每分钟检查一次
    };
    
    this.isInitialized = true;
    console.log('性能分析器已初始化')
};
  
  /**
   * 当收集到新的性能样本时触发
   */
  private onPerformanceSampleCollected(): void {
    // 收集到新样本时，清除缓存的瓶颈分析结果
    this.cachedBottlenecks = null
};
  
  /**
   * 开始收集性能数据
   */
  public startCollection(): void {
    this.initialize()
};
  
  /**
   * 获取资源计时数据
   * 由于RealUserMonitoring没有直接提供getResourceTimings方法，
   * 我们使用浏览器的Performance API获取或模拟数据
   */
  public async getResourceTimingData(days: number = 7): Promise<ResourceTimingData> {
    if (typeof window === 'undefined') {
      return this.getMockResourceTimingData()
};
    
    try {
      // 使用浏览器的Performance API获取资源计时数据
      const resourceEntries = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
      
      // 按资源类型分组
      const jsTimings: number[] = [];
      const cssTimings: number[] = [];
      const imgTimings: number[] = [];
      const fontTimings: number[] = [];
      const apiTimings: number[] = [];
      
      resourceEntries.forEach(entry => {
        const duration = entry.duration;
        const url = entry.name;
        
        if (url.endsWith('.js') || url.includes('js/')) {
          jsTimings.push(duration)
} else if (url.endsWith('.css') || url.includes('css/')) {
          cssTimings.push(duration)
} else if (url.match(/\.(jpe?g|png|gif|svg|webp)$/i) || url.includes('images/')) {
          imgTimings.push(duration)
} else if (url.match(/\.(woff2?|ttf|otf|eot)$/i) || url.includes('fonts/')) {
          fontTimings.push(duration)
} else if (url.includes('/api/') || url.includes('graphql')) {
          apiTimings.push(duration)
} });
      
      return {
        js: jsTimings.length > 0 ? jsTimings : [1200, 1150, 1100, 1050, 1000],
        css: cssTimings.length > 0 ? cssTimings : [350, 320, 300, 280, 260],
        img: imgTimings.length > 0 ? imgTimings : [1500, 1450, 1400, 1350, 1300],
        font: fontTimings.length > 0 ? fontTimings : [400, 380, 360, 340, 320],
        api: apiTimings.length > 0 ? apiTimings : [800, 780, 760, 740, 720]
      };
    } catch (error) {
      console.error('获取资源计时数据失败:', error);
      return this.getMockResourceTimingData();
    }
  }
  
  /**
   * 获取模拟的资源计时数据
   */
  private getMockResourceTimingData(): ResourceTimingData {
    return {
      js: [1200, 1150, 1100, 1050, 1000],
      css: [350, 320, 300, 280, 260],
      img: [1500, 1450, 1400, 1350, 1300],
      font: [400, 380, 360, 340, 320],
      api: [800, 780, 760, 740, 720]
    };
  }
  
  /**
   * 检测性能瓶颈
   */
  public async detectBottlenecks(): Promise<PerformanceBottleneck[]> {
    // 如果缓存有效且距离上次分析不超过10分钟，则使用缓存
    const now = Date.now();
    if (this.cachedBottlenecks && (now - this.lastAnalysisTimestamp < 10 * 60 * 1000)) {
      return this.cachedBottlenecks;
    }
    
    try {
      // 获取Web Vitals报告
      const vitalsReport = await this.getWebVitalsReport();
      
      // 获取资源计时数据
      const resourceTiming = await this.getResourceTimingData();
      
      // 分析瓶颈
      const bottlenecks: PerformanceBottleneck[] = [];
      
      // 分析LCP
      const avgLcp = vitalsReport.lcp.values.reduce((sum, val) => sum + val, 0) / vitalsReport.lcp.values.length;
      if (avgLcp > 2500) {
        bottlenecks.push({
          type: 'LCP (最大内容绘制) 过慢',
          score: this.getLCPScore(vitalsReport.lcp.values),
          impact: avgLcp > 4000 ? 'high' : 'medium',
          recommendations: [
            '优化关键渲染路径',
            '预加载关键资源',
            '优化和压缩图像',
            '实施响应式图像策略',
            '使用内容分发网络 (CDN)'
          ]
        });
      }
      
      // 分析FID
      const avgFid = vitalsReport.fid.values.reduce((sum, val) => sum + val, 0) / vitalsReport.fid.values.length;
      if (avgFid > 100) {
        bottlenecks.push({
          type: 'FID (首次输入延迟) 过高',
          score: this.getFIDScore(vitalsReport.fid.values),
          impact: avgFid > 300 ? 'high' : 'medium',
          recommendations: [
            '减少主线程长任务',
            '分割长时间运行的JavaScript',
            '优化第三方脚本加载',
            '使用Web Workers处理复杂计算',
            '减少JavaScript执行时间'
          ]
        });
      }
      
      // 分析CLS
      const avgCls = vitalsReport.cls.values.reduce((sum, val) => sum + val, 0) / vitalsReport.cls.values.length;
      if (avgCls > 0.1) {
        bottlenecks.push({
          type: 'CLS (累积布局偏移) 过高',
          score: this.getCLSScore(vitalsReport.cls.values),
          impact: avgCls > 0.25 ? 'high' : 'medium',
          recommendations: [
            '为图像和视频元素设置明确的宽度和高度',
            '避免在现有内容上方插入内容',
            '使用CSS transform动画而非改变布局属性',
            '避免在字体加载时出现FOUT',
            '预分配足够的空间给动态内容'
          ]
        });
      }
      
      // 分析JavaScript加载时间
      const avgJsTime = resourceTiming.js.reduce((sum, val) => sum + val, 0) / resourceTiming.js.length;
      if (avgJsTime > 1000) {
        bottlenecks.push({
          type: 'JavaScript加载时间过长',
          score: Math.max(0, 100 - Math.round(avgJsTime / 20)),
          impact: avgJsTime > 2000 ? 'high' : 'medium',
          recommendations: [
            '实施代码分割和懒加载',
            '使用Tree Shaking减少未使用代码',
            '压缩和最小化JavaScript文件',
            '使用现代JavaScript语法并使用babel转换',
            '优化第三方库使用'
          ]
        });
      }
      
      // 分析图像加载时间
      const avgImgTime = resourceTiming.img.reduce((sum, val) => sum + val, 0) / resourceTiming.img.length;
      if (avgImgTime > 1000) {
        bottlenecks.push({
          type: '图像加载时间过长',
          score: Math.max(0, 100 - Math.round(avgImgTime / 20)),
          impact: avgImgTime > 2000 ? 'high' : 'medium',
          recommendations: [
            '使用WebP等现代图像格式',
            '实施响应式图像',
            '懒加载非关键图像',
            '优化图像压缩率',
            '使用图像CDN进行自动优化'
          ]
        });
      }
      
      // 分析API响应时间
      const avgApiTime = resourceTiming.api.reduce((sum, val) => sum + val, 0) / resourceTiming.api.length;
      if (avgApiTime > 500) {
        bottlenecks.push({
          type: 'API响应时间过长',
          score: Math.max(0, 100 - Math.round(avgApiTime / 10)),
          impact: avgApiTime > 1000 ? 'high' : 'medium',
          recommendations: [
            '实施API响应缓存',
            '优化后端查询性能',
            '使用HTTP/2或HTTP/3',
            '实施GraphQL减少请求数',
            '优化API payload大小'
          ]
        });
      }
      
      // 分析缓存利用率
      const cacheStats = await this.getCacheStats();
      if (cacheStats.hitRate < 70) {
        bottlenecks.push({
          type: '资源缓存利用率低',
          score: Math.round(cacheStats.hitRate),
          impact: cacheStats.hitRate < 50 ? 'high' : 'medium',
          recommendations: [
            '设置适当的Cache-Control头',
            '实施Service Worker缓存策略',
            '使用资源预加载器预加载关键资源',
            '优化资源指纹策略',
            '实施有效的缓存失效策略'
          ]
        });
      }
      
      // 缓存结果
      this.cachedBottlenecks = bottlenecks;
      this.lastAnalysisTimestamp = now;
      
      return bottlenecks;
    } catch (error) {
      console.error('检测性能瓶颈失败:', error);
      
      // 返回示例数据（仅用于开发）
      return [
        {
          type: 'LCP (最大内容绘制) 过慢',
          score: 65,
          impact: 'medium',
          recommendations: [
            '优化关键渲染路径',
            '预加载关键资源',
            '优化和压缩图像'
          ]
        },
        {
          type: 'JavaScript加载时间过长',
          score: 70,
          impact: 'medium',
          recommendations: [
            '实施代码分割和懒加载',
            '压缩和最小化JavaScript文件',
            '优化第三方库使用'
          ]
        },
        {
          type: '图像加载时间过长',
          score: 60,
          impact: 'high',
          recommendations: [
            '使用WebP等现代图像格式',
            '实施响应式图像',
            '懒加载非关键图像'
          ]
        }
      ];
    }
  }
  
  /**
   * 获取Web指标报告
   * 由于RealUserMonitoring没有提供直接的接口，
   * 这里我们使用浏览器的Web Vitals API或模拟数据
   */
  public async getWebVitalsReport(): Promise<WebVitalsReport> {
    // 这里实际项目中应该通过与RUM系统的接口获取数据
    // 本示例使用模拟数据
    return {
      lcp: { values: [2200, 2500, 2300, 2100, 2400] },
      fid: { values: [85, 90, 95, 80, 88] },
      cls: { values: [0.08, 0.09, 0.07, 0.06, 0.08] },
      fcp: { values: [1600, 1550, 1650, 1500, 1580] },
      ttfb: { values: [350, 380, 370, 320, 360] }
    };
  }
  
  /**
   * 获取缓存统计
   * 由于resourcePreloader没有提供getCacheStats方法，
   * 这里我们使用模拟数据
   */
  public async getCacheStats(): Promise<CacheStats> {
    // 这里实际项目中应该通过与缓存系统的接口获取数据
    // 本示例使用模拟数据
    return {
      hitRate: 75.5,
      totalSize: 5242880, // 5MB
      itemCount: 24
    };
  }
  
  /**
   * 获取LCP性能评分
   */
  public getLCPScore(lcpValues: number[]): number {
    if (!lcpValues.length) return 0;
    
    const avgLcp = lcpValues.reduce((sum, val) => sum + val, 0) / lcpValues.length;
    
    if (avgLcp <= 2500) {
      // 好: 0-2500ms - 分数90-100
      return 100 - Math.round((avgLcp / 2500) * 10);
    } else if (avgLcp <= 4000) {
      // 需改进: 2500-4000ms - 分数60-89
      const range = 4000 - 2500;
      const position = avgLcp - 2500;
      return 90 - Math.round((position / range) * 30);
    } else {
      // 差: >4000ms - 分数0-59
      const normalizedScore = Math.max(0, 60 - Math.round((avgLcp - 4000) / 100));
      return Math.max(0, normalizedScore);
    }
  }
  
  /**
   * 获取FID性能评分
   */
  public getFIDScore(fidValues: number[]): number {
    if (!fidValues.length) return 0;
    
    const avgFid = fidValues.reduce((sum, val) => sum + val, 0) / fidValues.length;
    
    if (avgFid <= 100) {
      // 好: 0-100ms - 分数90-100
      return 100 - Math.round((avgFid / 100) * 10);
    } else if (avgFid <= 300) {
      // 需改进: 100-300ms - 分数60-89
      const range = 300 - 100;
      const position = avgFid - 100;
      return 90 - Math.round((position / range) * 30);
    } else {
      // 差: >300ms - 分数0-59
      const normalizedScore = Math.max(0, 60 - Math.round((avgFid - 300) / 10));
      return Math.max(0, normalizedScore);
    }
  }
  
  /**
   * 获取CLS性能评分
   */
  public getCLSScore(clsValues: number[]): number {
    if (!clsValues.length) return 0;
    
    const avgCls = clsValues.reduce((sum, val) => sum + val, 0) / clsValues.length;
    
    if (avgCls <= 0.1) {
      // 好: 0-0.1 - 分数90-100
      return 100 - Math.round((avgCls / 0.1) * 10);
    } else if (avgCls <= 0.25) {
      // 需改进: 0.1-0.25 - 分数60-89
      const range = 0.25 - 0.1;
      const position = avgCls - 0.1;
      return 90 - Math.round((position / range) * 30);
    } else {
      // 差: >0.25 - 分数0-59
      const normalizedScore = Math.max(0, 60 - Math.round((avgCls - 0.25) * 100));
      return Math.max(0, normalizedScore);
    }
  }
  
  /**
   * 导出性能报告
   */
  public async exportReport(reportData: PerformanceReport): Promise<void> {
    try {
      console.log('正在导出性能报告...');
      
      // 在真实实现中，这里可能会生成PDF或调用API保存报告
      // 此处为演示实现，仅将报告数据转换为JSON并提供下载
      const jsonData = JSON.stringify(reportData, null, 2);
      const blob = new Blob([jsonData], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `performance-report-${timestamp}.json`;
      
      // 创建下载链接并点击
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      
      // 释放URL对象
      setTimeout(() => {
        URL.revokeObjectURL(url);
      }, 100);
      
      console.log('性能报告导出成功');
    } catch (error) {
      console.error('导出性能报告失败:', error);
      throw new Error('导出性能报告失败');
    }
  }
};

export const performanceAnalyzer = new PerformanceAnalyzer();