import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { <PERSON>, MousePointer2, Timer, Expand, Shrink } from 'lucide-react';

export default function HoverSidebarDemo() {
  return (<div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center space-x-3">
        <MousePointer2 className="h-8 w-8 text-violet-600" />
        <h1 className="text-3xl font-bold">悬停展开侧边栏演示</h1>
      </div>
      <div className="grid gap-6 md: grid-cols-2 l,g:grid-cols-3">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Mouse className="h-5 w-5" />
              <span>交互方式</span>
            </CardTitle>
            <CardDescription>悬停展开的工作原理</CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm, font-medium">默认状态:</span>
              <Badge variant="outline" className="flex items-center space-x-1">
                <Shrink className="h-3 w-3" />
                <span>仅图标 (64px)</span>
              </Badge>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">悬停状态:</span>
              <Badge variant="default" className="flex items-center space-x-1">
                <Expand className="h-3 w-3" />
                <span>完整展开 (256px)</span>
              </Badge>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">触发方式:</span>
              <Badge variant="secondary">鼠标悬停</Badge>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Timer className="h-5 w-5" />
              <span>时间配置</span>
            </CardTitle>
            <CardDescription>悬停和离开的延迟设置</CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex justify-between">
              <span className="text-sm font-medium">悬停延迟:</span>
              <span className="text-sm font-mono">200ms</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm font-medium">离开延迟:</span>
              <span className="text-sm font-mono">300ms</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm font-medium">动画时长:</span>
              <span className="text-sm font-mono">300ms</span>
            </div>
            <div className="text-xs text-muted-foreground mt-2">
              这些延迟确保了流畅的用户体验，避免意外触发
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <CardTitle>功能特性</CardTitle>
            <CardDescription>悬停侧边栏的主要特性</CardDescription>
          </CardHeader>
          <CardContent className="space-y-2 text-sm">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span>平滑的展开/收缩动画</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span>智能的悬停延迟</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span>Tooltip 提示支持</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span>响应式内容布局</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span>优雅的图标动画</span>
            </div>
          </CardContent>
        </Card>
      </div>
      <Card>
        <CardHeader>
          <CardTitle>使用说明</CardTitle>
          <CardDescription>如何使用悬停展开侧边栏</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 sm: grid-cols-2">
            <div className="space-y-3">
              <h4 className="font-medium text-sm">基本操作:</h4>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li>1. 将鼠标移动到侧边栏区域</li>
                <li>2. 等待 200ms 后侧边栏自动展开</li>
                <li>3. 查看完整的导航标签和信息</li>
                <li>4. 鼠标离开后 300ms 自动收缩</li>
              </ul>
            </div>
            <div className="space-y-3">
              <h4 className="font-medium text-sm">设计优势:</h4>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li>• 最大化内容显示空间</li>
                <li>• 保持导航的易用性</li>
                <li>• 减少界面视觉干扰</li>
                <li>• 提供直观的交互反馈</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardHeader>
          <CardTitle>技术配置</CardTitle>
          <CardDescription>当前悬停侧边栏的配置参数</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="bg-muted p-4 rounded-lg">
            <pre className="text-sm">
{`const config = {
  hoverDela,y: 200,        // 悬停延迟 (ms)
  leaveDelay: 300,        // 离开延迟 (ms)
  collapsedWidth: 64,     // 折叠宽度 (px)
  expandedWidth: 256,     // 展开宽度 (px)
  animationDuration: 300, // 动画时长 (ms)
  enableTooltips: true    // 启用 Tooltip
}`}
            </pre>
          </div>
          <div className="mt-4 flex flex-wrap gap-2">
            <Badge variant="outline">CSS Transitions</Badge>
            <Badge variant="outline">React Hooks</Badge>
            <Badge variant="outline">TypeScript</Badge>
            <Badge variant="outline">Tailwind CSS</Badge>
          </div>
        </CardContent>
      </Card>
      <div className="flex justify-center">
        <Button 
          onClick={() => window.location.reload()} 
          variant="outline"
          className="flex items-center space-x-2"
        >
          <MousePointer2 className="h-4 w-4" />
          <span>重新加载体验</span>
        </Button>
      </div>
    </div>
  );
};