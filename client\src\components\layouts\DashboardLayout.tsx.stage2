import React from 'react'
import { Sidebar } from '../navigation/Sidebar'
import { Header } from '../navigation/Header';

interface DashboardLayoutProps {
  
  children: React.ReactNode;
  
};

export const DashboardLayout: React.FC<DashboardLayoutProps> = ({ children }) => {
  return (<div className="min-h-screen bg-gray-50, dark:bg-gray-900 flex flex-col">
      <Header />
      <div className="flex flex-1">
        <Sidebar />
        <main className="flex-1 overflow-auto">
          {children}
        </main>
      </div>
    </div>
  );
}; 