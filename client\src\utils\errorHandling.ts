import React from 'react';
import { z } from 'zod';
import { toast } from '@/hooks/use-toast';

// 应用错误接口
export interface AppError {
  code: string;
  message: string;
  details?: any;
  timestamp: Date;
}

// 自定义错误类
export class ValidationError extends Error {
  public readonly code = 'VALIDATION_ERROR';
  public readonly details: z.ZodError;
  public readonly timestamp = new Date();

  constructor(zodError: z.ZodError) {
    super('表单验证失败');
    this.details = zodError;
  }
}

export class NetworkError extends Error {
  public readonly code = 'NETWORK_ERROR';
  public readonly timestamp = new Date();

  constructor(message: string = '网络连接失败') {
    super(message);
  }
}

export class BusinessError extends Error {
  public readonly code: string;
  public readonly timestamp = new Date();

  constructor(code: string, message: string) {
    super(message);
    this.code = code;
  }
}

// 错误处理器
export class ErrorHandler {
  // 处理Zod验证错误
  static handleValidationError(error: z.ZodError): string[] {
    return error.errors.map(err => err.message);
  }

  // 格式化字段错误
  static formatFieldError(error: z.ZodError, fieldName: string): string | undefined {
    const fieldError = error.errors.find(err =>
      err.path.join('.') === fieldName
    );
    return fieldError?.message;
  }

  // 处理网络错误
  static handleNetworkError(error: any): AppError {
    if (error.response) {
      // 服务器响应错误
      return {
        code: 'SERVER_ERROR',
        message: error.response.data?.message || '服务器响应错误',
        details: error.response.data,
        timestamp: new Date()
      };
    } else if (error.request) {
      // 网络连接错误
      return {
        code: 'NETWORK_ERROR',
        message: '网络连接失败，请检查网络连接',
        timestamp: new Date()
      };
    } else {
      // 其他错误
      return {
        code: 'UNKNOWN_ERROR',
        message: error.message || '发生未知错误',
        timestamp: new Date()
      };
    }
  }

  // 通用错误处理
  static handleError(error: any): void {
    console.error('Error occurred:', error);

    if (error instanceof ValidationError) {
      const messages = this.handleValidationError(error.details);
      toast({
        title: '表单验证失败',
        description: messages.join('\n')
      });
      return;
    }

    if (error instanceof NetworkError) {
      toast({
        title: '网络错误',
        description: error.message
      });
      return;
    }

    if (error instanceof BusinessError) {
      toast({
        title: '操作失败',
        description: error.message
      });
      return;
    }

    // 处理Axios错误
    const appError = this.handleNetworkError(error);
    toast({
      title: '错误',
      description: appError.message
    });
  }

  // 异步操作错误处理装饰器
  static withErrorHandling = <T extends any[], R>(fn: (...args: T) => Promise<R>) => {
    return async (...args: T): Promise<R | null> => {
      try {
        return await fn(...args);
      } catch (error) {
        this.handleError(error);
        return null;
      }
    };
  };
}

// 表单验证Hook
export function useFormValidation<T extends Record<string, any>>(schema: z.ZodType<T>) {
  const validateData = (data: unknown): {
    success: boolean; 
    data?: T; 
    errors?: string[];
  } => {
    try {
      const validatedData = schema.parse(data);
      return { success: true, data: validatedData };
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errors = ErrorHandler.handleValidationError(error);
        return { success: false, errors };
      }
      return { 
        success: false, 
        errors: ['验证失败：未知错误'] 
      };
    }
  };

  const validateField = (fieldName: string, value: unknown): string | undefined => {
    try {
      // 直接验证整个对象，但只关注特定字段的错误
      schema.parse({ [fieldName]: value });
      return undefined;
    } catch (error) {
      if (error instanceof z.ZodError) {
        return ErrorHandler.formatFieldError(error, fieldName);
      }
      return '验证失败';
    }
  };

  return {
    validateData,
    validateField
  };
}

// 异步操作状态管理Hook
export function useAsyncOperation() {
  const [isLoading, setIsLoading] = React.useState(false);
  const [error, setError] = React.useState<AppError | null>(null);

  const execute = async <T>(operation: () => Promise<T>,
    options?: {
      showSuccessToast?: boolean;
      successMessage?: string;
      onSuccess?: (result: T) => void;
      onError?: (error: AppError) => void;
    }
  ): Promise<T | null> => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await operation();
      
      if (options?.showSuccessToast) {
        toast({
          title: '操作成功',
          description: options.successMessage || '操作已完成'
        });
      }

      options?.onSuccess?.(result);
      return result;
    } catch (err) {
      const appError = ErrorHandler.handleNetworkError(err);
      setError(appError);
      
      options?.onError?.(appError);
      ErrorHandler.handleError(err);
      
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  const reset = () => {
    setError(null);
    setIsLoading(false);
  };

  return {
    isLoading,
    error,
    execute,
    reset
  };
}

// 批量验证工具
export const BatchValidator = {
  // 验证多个字段
  validateFields: <T extends Record<string, any>>(schema: z.ZodType<T>,
    data: Partial<T>,
    fields: (keyof T)[]
  ): Record<keyof T, string | undefined> => {
    const errors: Record<keyof T, string | undefined> = {} as any;
    
    fields.forEach(field => {
      try {
        const fieldValue = data[field];
        // 创建包含单个字段的对象进行验证
        schema.parse({ [field]: fieldValue });
      } catch (error) {
        if (error instanceof z.ZodError) {
          errors[field] = ErrorHandler.formatFieldError(error, String(field));
        }
      }
    });

    return errors;
  },

  // 验证必填字段
  validateRequiredFields: <T>(data: Partial<T>,
    requiredFields: (keyof T)[]
  ): string[] => {
    const missingFields: string[] = [];
    
    requiredFields.forEach(field => {
      if (!data[field]) {
        missingFields.push(String(field));
      }
    });

    return missingFields;
  }
}; 