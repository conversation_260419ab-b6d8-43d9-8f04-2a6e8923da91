/**
 * 资源优化工具集
 * 包含字体、CSS、图片、预加载等优化功能
 */

// 字体优化工具
interface LazyImageOptions {
  threshold?: number;
  rootMargin?: string;
  placeholder?: string;
  errorImage?: string;
}

export const fontOptimizer = {
  /**
   * 预加载关键字体
   */
  preloadFonts: (fonts: string[]) => {
    fonts.forEach(font => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'font';
      link.href = font;
      link.crossOrigin = 'anonymous';
      document.head.appendChild(link);
    });
  },

  /**
   * 字体显示优化
   */
  optimizeFontDisplay: () => {
    const style = document.createElement('style');
    style.textContent = `
      @font-face {
        font-family: 'Inter';
        font-display: swap;
      }
      @font-face {
        font-family: 'JetBrains Mono';
        font-display: swap;
      }
    `;
    document.head.appendChild(style);
  }
};

// CSS优化工具
export const cssOptimizer = {
  /**
   * 移除未使用的CSS（运行时检测）
   */
  purgeUnusedCSS: () => {
    const usedSelectors = new Set<string>();
    
    // 扫描DOM中实际使用的类名
    const scanElement = (element: Element) => {
      if (element.className) {
        const classes = element.className.split(' ');
        classes.forEach(cls => {
          if (cls.trim()) {
            usedSelectors.add(`.${cls.trim()}`);
          }
        });
      }
      
      Array.from(element.children).forEach(scanElement);
    };
    
    scanElement(document.body);
    
    console.log(`Found ${usedSelectors.size} used CSS selectors`);
    return Array.from(usedSelectors);
  },

  /**
   * 内联关键CSS
   */
  inlineCriticalCSS: (criticalCSS: string) => {
    const style = document.createElement('style');
    style.textContent = criticalCSS;
    style.setAttribute('data-critical', 'true');
    document.head.appendChild(style);
  },

  /**
   * 异步加载非关键CSS
   */
  loadNonCriticalCSS: (href: string) => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = 'style';
    link.href = href;
    link.onload = () => {
      link.rel = 'stylesheet';
    };
    document.head.appendChild(link);
  }
};

// 图片优化工具
export const imageOptimizer = {
  /**
   * 创建懒加载观察器
   */
  createLazyLoader: (options: LazyImageOptions = {}) => {
    const {
      threshold = 0.1,
      rootMargin = '50px',
      // placeholder = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAiIHZpZXdCb3g9IjAgMCAxMDAgMTAwIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjZmZmIi8+PC9zdmc+',
      errorImage = '/images/error-placeholder.png'
    } = options;

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target as HTMLImageElement;
          const src = img.dataset.src;
          const srcset = img.dataset.srcset;

          if (src) {
            img.src = src;
            img.removeAttribute('data-src');
          }

          if (srcset) {
            img.srcset = srcset;
            img.removeAttribute('data-srcset');
          }

          img.classList.remove('lazy-loading');
          img.classList.add('lazy-loaded');

          // 错误处理
          img.onerror = () => {
            img.src = errorImage;
            img.classList.add('lazy-error');
          };

          observer.unobserve(img);
        }
      });
    }, {
      threshold,
      rootMargin
    });

    return observer;
  },

  /**
   * 初始化懒加载
   */
  initLazyLoading: (selector = 'img[data-src]', options: LazyImageOptions = {}) => {
    const observer = imageOptimizer.createLazyLoader(options);
    const images = document.querySelectorAll(selector);
    
    images.forEach(img => {
      img.classList.add('lazy-loading');
      observer.observe(img);
    });

    return observer;
  },

  /**
   * 响应式图片工具
   */
  generateResponsiveImageset: (baseSrc: string, sizes: number[]) => {
    const srcset = sizes.map(size => {
      const url = baseSrc.replace(/\.(jpg|jpeg|png|webp)$/, `_${size}w.$1`);
      return `${url} ${size}w`;
    }).join(', ');

    return {
      src: baseSrc,
      srcSet: srcset,
      sizes: '(max-width: 480px) 480px, (max-width: 768px) 768px, (max-width: 1024px) 1024px, 1440px'
    };
  }
};

// 资源预加载工具
export const preloadOptimizer = {
  /**
   * 预加载关键资源
   */
  preloadResources: (resources: Array<{ href: string; as: string; type?: string }>) => {
    resources.forEach(({ href, as, type }) => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.href = href;
      link.as = as;
      if (type) link.type = type;
      document.head.appendChild(link);
    });
  },

  /**
   * 预连接外部域名
   */
  preconnectDomains: (domains: string[]) => {
    domains.forEach(domain => {
      const link = document.createElement('link');
      link.rel = 'preconnect';
      link.href = domain;
      document.head.appendChild(link);
    });
  },

  /**
   * DNS预解析
   */
  dnsPrefetch: (domains: string[]) => {
    domains.forEach(domain => {
      const link = document.createElement('link');
      link.rel = 'dns-prefetch';
      link.href = domain;
      document.head.appendChild(link);
    });
  },

  /**
   * 预获取下一页面资源
   */
  prefetchPages: (urls: string[]) => {
    urls.forEach(url => {
      const link = document.createElement('link');
      link.rel = 'prefetch';
      link.href = url;
      document.head.appendChild(link);
    });
  }
};

// 缓存策略工具
export const cacheOptimizer = {
  /**
   * 设置资源缓存策略
   */
  setCacheStrategy: (selector: string, maxAge: number) => {
    const elements = document.querySelectorAll(selector);
    elements.forEach(element => {
      (element as HTMLElement).dataset.cacheMaxAge = maxAge.toString();
    });
  },

  /**
   * 添加版本号到URL
   */
  addVersionToUrl: (url: string, version?: string) => {
    const versionParam = version || Date.now().toString();
    return `${url}${url.includes('?') ? '&' : '?'}v=${versionParam}`;
  }
};

/**
 * 资源优化管理器
 */
export class ResourceOptimizer {
  private static instance: ResourceOptimizer;
  private initialized = false;

  public static getInstance(): ResourceOptimizer {
    if (!ResourceOptimizer.instance) {
      ResourceOptimizer.instance = new ResourceOptimizer();
    }
    return ResourceOptimizer.instance;
  }

  /**
   * 初始化资源优化
   */
  public init(config: {
    enableLazyImages?: boolean;
    enableFontOptimization?: boolean;
    enableCSSOptimization?: boolean;
    enableResourcePreloading?: boolean;
  } = {}) {
    if (this.initialized) return;

    const {
      enableLazyImages = true,
      enableFontOptimization = true,
      enableCSSOptimization = true,
      enableResourcePreloading = true
    } = config;

    if (enableLazyImages) {
      this.initLazyImages();
    }

    if (enableFontOptimization) {
      this.initFontOptimization();
    }

    if (enableCSSOptimization) {
      this.initCSSOptimization();
    }

    if (enableResourcePreloading) {
      this.initResourcePreloading();
    }

    this.initialized = true;
  }

  private initLazyImages(): void {
    imageOptimizer.initLazyLoading();
  }

  private initFontOptimization(): void {
    fontOptimizer.optimizeFontDisplay();
    fontOptimizer.preloadFonts([
      '/fonts/inter-variable.woff2',
      '/fonts/jetbrains-mono-variable.woff2'
    ]);
  }

  private initCSSOptimization(): void {
    // 在生产环境中运行CSS优化
    if (process.env.NODE_ENV === 'production') {
      setTimeout(() => {
        cssOptimizer.purgeUnusedCSS();
      }, 2000);
    }
  }

  private initResourcePreloading(): void {
    // 预连接常用外部域名
    preloadOptimizer.preconnectDomains([
      'https://fonts.googleapis.com',
      'https://cdn.jsdelivr.net',
      'https://api.example.com'
    ]);
  }
}

export default ResourceOptimizer; 