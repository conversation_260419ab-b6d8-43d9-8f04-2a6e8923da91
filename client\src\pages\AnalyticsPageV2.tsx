import React, { useState, useC<PERSON>back, useMemo, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { PageContainer } from '@/components/layouts/PageContainer'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
// import { DatePickerWithRange } from '@/components/ui/date-picker'
// import { Separator } from '@/components/ui/separator'
import { useAnalyticsData } from '../hooks/useAnalyticsData'
import KpiCard from '../components/dashboard/KpiCard'
import {
  ModernLineChart, 
  ModernPieChart, 
  ModernComposedChart
} from '@/components/charts/ModernCharts'
// import { CHART_LABELS } from '@/constants/chartLabels'
import {
  // BarChart3,
  // TrendingUp,
  // Activity,
  Target,
  Download,
  RefreshCw,
  Calendar,
  // Filter,
  MessageSquare,
  // Calendar as CalendarIcon,
  FileText,
  Zap,
  Clock,
  Users,
  DollarSign
} from 'lucide-react'
// import { useEnhancedAnalytics } from '@/hooks/useEnhancedAnalytics'
import { RealTimeIndicator } from '@/components/common/RealTimeIndicator'
import '@/styles/modern-charts.css'

// Types for better type safety
interface ModuleData {
  name: string;
  value: number;
  change: string;
  trend: 'up' | 'down' | 'stable';
  icon: React.ComponentType<{ className?: string }>;
  color: string
}

interface PerformanceMetric {
  title: string;
  value: string;
  change: number;
  changeType: 'positive' | 'negative' | 'neutral';
  description: string;
  icon: React.ComponentType<{ className?: string }>
}

interface ChartDataPoint {
  date: string;
  chatBot: number;
  booking: number;
  content: number;
  api: number
}

type TimeRange = '7d' | '30d' | '90d'
type ActiveTab = 'overview' | 'modules' | 'prediction' | 'performance'
// type ModuleFilter = 'all' | 'chatBot' | 'booking' | 'content' | 'api'

export default function AnalyticsPageV2() {
  const { t } = useTranslation();
  
  // State management with proper typing
  const [activeTab, setActiveTab] = useState<ActiveTab>('overview');
  const [timeRange, setTimeRange] = useState<TimeRange>('30d');
  // const [moduleFilter, setModuleFilter] = useState<ModuleFilter>('all');
  const { kpis, chartData, loading, error } = useAnalyticsData() as {
    kpis: any[]; 
    chartData: ChartDataPoint[]; 
    loading: boolean; 
    error: any 
  };
  // const [analyticsState] = useEnhancedAnalytics();
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());
  const [realTimeEnabled, setRealTimeEnabled] = useState<boolean>(true);

  // Auto-refresh effect for real-time monitoring
  useEffect(() => {
    if (!realTimeEnabled) return;

    const timer = setInterval(() => {
      setLastUpdated(new Date());
      // Trigger data refresh logic here
    }, 5000); // Refresh every 5 seconds when real-time is enabled

    return () => clearInterval(timer)
}, [realTimeEnabled]);

  // Computed values with memoization for performance
  const days = useMemo(() => {
    return timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : 90
}, [timeRange]);

  const displayedData = useMemo(() => {
    return chartData.slice(-days)
}, [chartData, days]);

  const lastDataPoint = useMemo(() => {
    return displayedData[displayedData.length - 1] || {}
}, [displayedData]);

  // Module data with enhanced information
  const moduleData: ModuleData[] = useMemo(() => [
    { 
      name: t('analytics.aiChatbot'),
      value: 1247 + Math.floor(Math.random() * 200),
      change: "+12%",
      trend: 'up',
      icon: MessageSquare,
      color: "#8B5CF6"
    },
    { 
      name: t('analytics.bookingSystem'),
      value: 856 + Math.floor(Math.random() * 150),
      change: "+8%",
      trend: 'up',
      icon: Calendar,
      color: "#A855F7"
    },
    { 
      name: t('analytics.contentGeneration'),
      value: 634 + Math.floor(Math.random() * 100),
      change: "+15%",
      trend: 'up',
      icon: FileText,
      color: "#C084FC"
    },
    { 
      name: t('analytics.platformApi'),
      value: 423 + Math.floor(Math.random() * 80),
      change: "+5%",
      trend: 'stable',
      icon: Zap,
      color: "#DDD6FE"
    }
  ], [t]);

  // Pie chart data for module distribution
  const pieChartData = useMemo(() => [
    { name: t('analytics.aiChatbot'), value: lastDataPoint.chatBot || 0, fill: "#8B5CF6" },
    { name: t('analytics.bookingSystem'), value: lastDataPoint.booking || 0, fill: "#A855F7" },
    { name: t('analytics.contentGeneration'), value: lastDataPoint.content || 0, fill: "#C084FC" },
    { name: t('analytics.platformApi'), value: lastDataPoint.api || 0, fill: "#DDD6FE" }
  ], [lastDataPoint, t]);

  // Performance metrics with proper typing
  const performanceMetrics: PerformanceMetric[] = useMemo(() => [
    {
      title: t('analytics.responseTime'),
      value: "1.2s",
      change: -0.3,
      changeType: "positive",
      description: t('analytics.averageApiResponseTime'),
      icon: Clock
    },
    {
      title: t('analytics.successRate'),
      value: "99.8%",
      change: 0.2,
      changeType: "positive",
      description: t('analytics.businessOperationSuccessRate'),
      icon: Target
    },
    {
      title: t('analytics.userSatisfaction'),
      value: "4.8/5",
      change: 0.1,
      changeType: "positive",
      description: t('analytics.averageUserRating'),
      icon: Users
    },
    {
      title: t('analytics.conversionRate'),
      value: "18.5%",
      change: 2.3,
      changeType: "positive",
      description: t('analytics.visitorConversionRate'),
      icon: DollarSign
    }
  ], [t]);

  // Chart data for trend analysis
  const trendChartData = useMemo(() => 
    displayedData.map(item => ({
      date: item.date,
      chatBot: item.chatBot || 0,
      booking: item.booking || 0,
      content: item.content || 0,
      api: item.api || 0
    })), [displayedData]);

  const handleRefresh = useCallback(() => {
    setLastUpdated(new Date());
    // Add refresh logic here
  }, []);

  const handleExport = useCallback(() => {
    // Add export logic here
    console.log('Exporting analytics data...');
  }, []);

  if (loading) {
    return (
      <PageContainer>
        <div className="flex items-center justify-center h-64">
          <RefreshCw className="animate-spin h-8 w-8" />
          <span className="ml-2">{t('common.loading')}</span>
        </div>
      </PageContainer>
    )
}

  if (error) {
    return (
      <PageContainer>
        <div className="flex items-center justify-center h-64">
          <p className="text-red-500">{t('common.error')}: {error.message}</p>
        </div>
      </PageContainer>
    )
}

  return (
    <PageContainer>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold">{t('analytics.title')}</h1>
            <p className="text-muted-foreground">{t('analytics.subtitle')}</p>
          </div>
          <div className="flex items-center gap-3">
            <RealTimeIndicator 
              isConnected={realTimeEnabled} 
              lastUpdated={lastUpdated}
              onToggle={(enabled) => setRealTimeEnabled(enabled)}
            />
            <Select value={timeRange} onValueChange={(value: TimeRange) => setTimeRange(value)}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7d">7 {t('common.days')}</SelectItem>
                <SelectItem value="30d">30 {t('common.days')}</SelectItem>
                <SelectItem value="90d">90 {t('common.days')}</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline" size="sm" onClick={handleRefresh}>
              <RefreshCw className="h-4 w-4 mr-2" />
              {t('common.refresh')}
            </Button>
            <Button variant="outline" size="sm" onClick={handleExport}>
              <Download className="h-4 w-4 mr-2" />
              {t('common.export')}
            </Button>
          </div>
        </div>

        {/* KPI Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {kpis.map((kpi, index) => (
            <KpiCard key={index} {...kpi} />
          ))}
        </div>

        {/* Main Content Tabs */}
        <Tabs value={activeTab} onValueChange={(value: string) => setActiveTab(value as ActiveTab)}>
          <TabsList>
            <TabsTrigger value="overview">{t('analytics.overview')}</TabsTrigger>
            <TabsTrigger value="modules">{t('analytics.modules')}</TabsTrigger>
            <TabsTrigger value="performance">{t('analytics.performance')}</TabsTrigger>
            <TabsTrigger value="prediction">{t('analytics.prediction')}</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>{t('analytics.usageTrend')}</CardTitle>
                  <CardDescription>{t('analytics.usageTrendDescription')}</CardDescription>
                </CardHeader>
                <CardContent>
                  <ModernLineChart data={trendChartData} />
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle>{t('analytics.moduleDistribution')}</CardTitle>
                  <CardDescription>{t('analytics.moduleDistributionDescription')}</CardDescription>
                </CardHeader>
                <CardContent>
                  <ModernPieChart data={pieChartData} />
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="modules" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {moduleData.map((module, index) => (
                <Card key={index}>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <module.icon className="h-8 w-8" color={module.color} />
                      <Badge variant={module.trend === 'up' ? 'default' : 'secondary'}>
                        {module.change}
                      </Badge>
                    </div>
                    <div className="mt-4">
                      <h3 className="font-semibold">{module.name}</h3>
                      <p className="text-2xl font-bold">{module.value.toLocaleString()}</p>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="performance" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {performanceMetrics.map((metric, index) => (
                <Card key={index}>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <metric.icon className="h-8 w-8 text-primary" />
                      <Badge variant={metric.changeType === 'positive' ? 'default' : 'destructive'}>
                        {metric.change > 0 ? '+' : ''}{metric.change}%
                      </Badge>
                    </div>
                    <div className="mt-4">
                      <h3 className="font-semibold">{metric.title}</h3>
                      <p className="text-2xl font-bold">{metric.value}</p>
                      <p className="text-sm text-muted-foreground mt-1">{metric.description}</p>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="prediction" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>{t('analytics.predictiveAnalysis')}</CardTitle>
                <CardDescription>{t('analytics.predictiveAnalysisDescription')}</CardDescription>
              </CardHeader>
              <CardContent>
                <ModernComposedChart data={trendChartData} />
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </PageContainer>
  )
}