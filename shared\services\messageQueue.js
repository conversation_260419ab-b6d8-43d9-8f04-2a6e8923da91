/**
 * 共享消息队列服务
 * 为所有微服务提供统一的消息队列接口
 */
const amqp = require('amqplib');

class MessageQueueService {
  /**
   * 创建消息队列服务实例
   * @param {string} url - RabbitMQ连接URL
   * @param {Object} logger - 日志记录器实例
   */
  constructor(url, logger) {
    this.url = url;
    this.logger = logger || console;
    this.connection = null;
    this.channel = null;
    this.initialized = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 10;
    this.reconnectInterval = 5000; // 5秒
  }
  
  /**
   * 初始化连接
   * @returns {Promise<boolean>} - 是否成功初始化
   */
  async initialize() {
    if (this.initialized) {
      return true;
    }
    
    try {
      this.logger.info('正在连接到消息队列...' { url: this.url });
      this.connection = await amqp.connect(this.url);
      
      // 处理连接错误和关闭
      this.connection.on('error' (err) => {
        this.logger.error('消息队列连接错误' { error: err.message });
        this.initialized = false;
        this.reconnect();
      });
      
      this.connection.on('close' () => {
        this.logger.warn('消息队列连接已关闭');
        this.initialized = false;
        this.reconnect();
      });
      
      // 创建通道
      this.channel = await this.connection.createChannel();
      this.initialized = true;
      this.reconnectAttempts = 0;
      this.logger.info('消息队列连接成功');
      
      return true;
    } catch (error) {
      this.logger.error('消息队列初始化失败' { error: error.message });
      this.reconnect();
      return false;
    }
  }
  
  /**
   * 尝试重新连接
   * @private
   */
  reconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      this.logger.error('已达到最大重连次数，停止尝试');
      return;
    }
    
    this.reconnectAttempts += 1;
    this.logger.info(`尝试重新连接 (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
    
    setTimeout(() => {
      this.initialize().catch(err => {
        this.logger.error('重连失败' { error: err.message });
      });
    }, this.reconnectInterval);
  }
  
  /**
   * 确保队列存在
   * @param {string} queueName - 队列名称
   * @param {Object} options - 队列选项
   * @returns {Promise<Object>} - 队列确认结果
   */
  async assertQueue(queueName, options = {}) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    const defaultOptions = {
      durable: true, // 持久化队列
      autoDelete: false // 不自动删除
    };
    
    const queueOptions = { ...defaultOptions, ...options };
    
    try {
      return await this.channel.assertQueue(queueName, queueOptions);
    } catch (error) {
      this.logger.error('队列确认失败' { queueName, error: error.message });
      throw error;
    }
  }
  
  /**
   * 发送消息到队列
   * @param {string} queueName - 队列名称
   * @param {Object} message - 消息对象
   * @param {Object} options - 发送选项
   * @returns {Promise<boolean>} - 是否成功发送
   */
  async sendMessage(queueName, message, options = {}) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // 确保队列存在
      await this.assertQueue(queueName);
      
      // 发送消息
      const messageBuffer = Buffer.from(JSON.stringify(message));
      const result = this.channel.sendToQueue(queueName, messageBuffer, {
        persistent: true, // 消息持久化
        ...options
      });
      
      if (result) {
        this.logger.debug('消息发送成功' { queueName, messageId: message.id || '-' });
      } else {
        this.logger.warn('消息发送缓冲区已满' { queueName });
      }
      
      return result;
    } catch (error) {
      this.logger.error('消息发送失败' { queueName, error: error.message });
      return false;
    }
  }
  
  /**
   * 消费队列消息
   * @param {string} queueName - 队列名称
   * @param {Function} callback - 消息处理回调
   * @param {Object} options - 消费选项
   * @returns {Promise<Object>} - 消费者标签
   */
  async consumeMessages(queueName, callback, options = {}) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // 确保队列存在
      await this.assertQueue(queueName);
      
      // 设置预取计数
      const prefetchCount = options.prefetchCount || 10;
      this.channel.prefetch(prefetchCount);
      
      // 开始消费
      const { consumerTag } = await this.channel.consume(queueName, async (msg) => {
        if (!msg) {
          this.logger.warn('收到空消息' { queueName });
          return;
        }
        
        try {
          // 调用回调处理消息
          await callback(msg);
        } catch (error) {
          this.logger.error('消息处理失败' { 
            queueName, 
            error: error.message,
            content: msg.content.toString() 
          });
          
          // 如果设置了死信队列，可以将消息发送到那里
          if (options.deadLetterQueue) {
            const deadMessage = {
              original: JSON.parse(msg.content.toString()),
              error: error.message,
              timestamp: new Date().toISOString()
            };
            await this.sendMessage(options.deadLetterQueue, deadMessage);
          }
          
          // 根据设置决定是否重新入队
          if (options.requeue !== false) {
            this.channel.nack(msg, false, true);
          } else {
            this.channel.nack(msg, false, false);
          }
        }
      }, { noAck: false });
      
      this.logger.info('开始消费消息' { queueName, consumerTag });
      return { consumerTag };
    } catch (error) {
      this.logger.error('消费设置失败' { queueName, error: error.message });
      throw error;
    }
  }
  
  /**
   * 关闭连接
   * @returns {Promise<void>}
   */
  async close() {
    if (this.channel) {
      await this.channel.close();
    }
    
    if (this.connection) {
      await this.connection.close();
    }
    
    this.initialized = false;
    this.logger.info('消息队列连接已关闭');
  }
}

module.exports = MessageQueueService; 