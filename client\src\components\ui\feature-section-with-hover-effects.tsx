﻿import { cn } from "@/lib/utils";
import {
  IconAdjustmentsBolt,
  IconCloud,
  IconCurrencyDollar,
  IconEaseInOut,
  IconHeart,
  IconHelp,
  IconRouteAltLeft,
  IconTerminal2,
} from "@tabler/icons-react";

export function FeaturesSectionWithHoverEffects() {
  const features = [
    {
      title: "AI-Powered Content Generation",
      description:
        "Advanced AI algorithms to create high-quality, engaging content that resonates with your audience.",
      icon: <IconTerminal2 />,
    },
    {
      title: "Ease of use",
      description:
        "Intuitive interface designed for creators of all skill levels. Start creating amazing content in minutes.",
      icon: <IconEaseInOut />,
    },
    {
      title: "Flexible Pricing",
      description:
        "Affordable plans that scale with your needs. Start free, upgrade when you're ready.",
      icon: <IconCurrencyDollar />,
    },
    {
      title: "Cloud-based Platform",
      description: "Access your content creation tools anywhere, anytime with 99.9% uptime guarantee.",
      icon: <IconCloud />,
    },
    {
      title: "Multi-platform Distribution",
      description: "Seamlessly distribute your content across multiple platforms with one-click publishing.",
      icon: <IconRouteAltLeft />,
    },
    {
      title: "24/7 Customer Support",
      description:
        "Our dedicated support team is always here to help you succeed with your content strategy.",
      icon: <IconHelp />,
    },
    {
      title: "Smart Optimization",
      description:
        "Intelligent content optimization based on performance analytics and audience insights.",
      icon: <IconAdjustmentsBolt />,
    },
    {
      title: "Built with Love",
      description: "Crafted by content creators, for content creators. We understand your needs.",
      icon: <IconHeart />,
    },
  ];
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 relative z-10 py-10 max-w-7xl mx-auto">
      {features.map((feature, index) => (
        <Feature key={feature.title} {...feature} index={index} />
      ))}
    </div>
  );
}

const Feature = ({
  title,
  description,
  icon,
  index,
}: {
  title: string;
  description: string;
  icon: React.ReactNode;
  index: number;
}) => {
  return (
    <div
      data-testid="feature-card"
      data-card-index={index}
      className={cn(
        // 基础样式 - 确保所有卡片都有group类和cursor-pointer
        "flex flex-col py-10 relative group cursor-pointer transition-all duration-300 ease-out",
        // 边框样式 - 修复grid布局边框逻辑
        "dark:border-neutral-800",
        // 右边框：除了每行最后一个卡片（index 3, 7）
        (index + 1) % 4 !== 0 && "lg:border-r",
        // 左边框：每行第一个卡片（index 0, 4）
        index % 4 === 0 && "lg:border-l",
        // 下边框：前4个卡片（第一行）
        index < 4 && "lg:border-b",
        // Hover时提升层级 - 确保高于导航栏(z-30)
        "hover:z-40"
      )}
    >
      {/* 主要Hover背景效果 - 统一所有卡片 */}
      <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 ease-out absolute inset-0 h-full w-full bg-gradient-to-t from-purple-50 dark:from-purple-900/40 to-transparent pointer-events-none z-0" />

      {/* 额外的Hover增强层 - 确保效果可见 */}
      <div className="absolute inset-0 group-hover:bg-purple-50/5 dark:group-hover:bg-purple-900/5 transition-colors duration-300 ease-out pointer-events-none z-1" />

      {/* Icon Section */}
      <div className="mb-4 relative z-10 px-10 text-neutral-600 dark:text-neutral-400 group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors duration-300 ease-out">
        {icon}
      </div>

      {/* Title Section with Left Border */}
      <div className="text-lg font-bold mb-2 relative z-10 px-10">
        <div className="absolute left-0 inset-y-0 h-6 group-hover:h-8 w-1 rounded-tr-full rounded-br-full bg-neutral-300 dark:bg-neutral-700 group-hover:bg-purple-500 transition-all duration-300 ease-out origin-center" />
        <span className="group-hover:translate-x-2 transition-transform duration-300 ease-out inline-block text-neutral-800 dark:text-neutral-100 group-hover:text-purple-700 dark:group-hover:text-purple-300">
          {title}
        </span>
      </div>

      {/* Description */}
      <p className="text-sm text-neutral-600 dark:text-neutral-300 max-w-xs relative z-10 px-10 group-hover:text-purple-700 dark:group-hover:text-purple-300 transition-colors duration-300 ease-out">
        {description}
      </p>
    </div>
  );
};
