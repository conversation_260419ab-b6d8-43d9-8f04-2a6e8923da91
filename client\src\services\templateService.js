import axiosInstance from '../api/axiosInstance';

/**
 * 模板服务
 */
class TemplateService {
  constructor() {
    this.baseURL = '/api/templates';
  }

  /**
   * 获取模板列表
   * @param {Object} filters - 筛选条件
   * @returns {Promise<Array>} 模板列表
   */
  async getTemplates(filters = {}) {
    try {
      const response = await axiosInstance.get(this.baseURL, { params: filters });
      return response.data;
    } catch (error) {
      console.error('获取模板列表失败:', error.response?.data || error);
      throw error.response?.data || error;
    }
  }

  /**
   * 获取模板详情
   * @param {string} templateId - 模板ID
   * @returns {Promise<Object>} 模板详情
   */
  async getTemplate(templateId) {
    try {
      const response = await axiosInstance.get(`${this.baseURL}/${templateId}`);
      return response.data;
    } catch (error) {
      console.error('获取模板详情失败:', error.response?.data || error);
      throw error.response?.data || error;
    }
  }

  /**
   * 创建模板
   * @param {Object} templateData - 模板数据
   * @returns {Promise<Object>} 创建的模板
   */
  async createTemplate(templateData) {
    try {
      const response = await axiosInstance.post(this.baseURL, templateData);
      return response.data;
    } catch (error) {
      console.error('创建模板失败:', error.response?.data || error);
      throw error.response?.data || error;
    }
  }

  /**
   * 更新模板
   * @param {string} templateId - 模板ID
   * @param {Object} templateData - 模板数据
   * @returns {Promise<Object>} 更新后的模板
   */
  async updateTemplate(templateId, templateData) {
    try {
      const response = await axiosInstance.put(`${this.baseURL}/${templateId}`, templateData);
      return response.data;
    } catch (error) {
      console.error('更新模板失败:', error.response?.data || error);
      throw error.response?.data || error;
    }
  }

  /**
   * 删除模板
   * @param {string} templateId - 模板ID
   * @returns {Promise<Object>} 删除结果
   */
  async deleteTemplate(templateId) {
    try {
      const response = await axiosInstance.delete(`${this.baseURL}/${templateId}`);
      return response.data;
    } catch (error) {
      console.error('删除模板失败:', error.response?.data || error);
      throw error.response?.data || error;
    }
  }

  /**
   * 复制模板
   * @param {string} templateId - 模板ID
   * @param {Object} options - 复制选项
   * @returns {Promise<Object>} 复制的模板
   */
  async duplicateTemplate(templateId, options = {}) {
    try {
      const response = await axiosInstance.post(`${this.baseURL}/${templateId}/duplicate`, options);
      return response.data;
    } catch (error) {
      console.error('复制模板失败:', error.response?.data || error);
      throw error.response?.data || error;
    }
  }

  /**
   * 渲染模板
   * @param {string} templateId - 模板ID
   * @param {Object} data - 渲染数据
   * @returns {Promise<Object>} 渲染结果
   */
  async renderTemplate(templateId, data = {}) {
    try {
      const response = await axiosInstance.post(`${this.baseURL}/${templateId}/render`, data);
      return response.data;
    } catch (error) {
      console.error('渲染模板失败:', error.response?.data || error);
      throw error.response?.data || error;
    }
  }

  /**
   * 预览模板
   * @param {Object} templateData - 模板数据
   * @param {Object} previewData - 预览数据
   * @returns {Promise<Object>} 预览结果
   */
  async previewTemplate(templateData, previewData = {}) {
    try {
      const response = await axiosInstance.post(`${this.baseURL}/preview`, {
        template: templateData,
        data: previewData
      });
      return response.data;
    } catch (error) {
      console.error('预览模板失败:', error.response?.data || error);
      throw error.response?.data || error;
    }
  }

  /**
   * 获取模板分类
   * @returns {Promise<Array>} 分类列表
   */
  async getTemplateCategories() {
    try {
      const response = await axiosInstance.get(`${this.baseURL}/categories`);
      return response.data;
    } catch (error) {
      console.error('获取模板分类失败:', error.response?.data || error);
      throw error.response?.data || error;
    }
  }

  /**
   * 按分类获取模板
   * @param {string} categoryId - 分类ID
   * @param {Object} filters - 筛选条件
   * @returns {Promise<Array>} 模板列表
   */
  async getTemplatesByCategory(categoryId, filters = {}) {
    try {
      const response = await axiosInstance.get(`${this.baseURL}/category/${categoryId}`, {
        params: filters
      });
      return response.data;
    } catch (error) {
      console.error('按分类获取模板失败:', error.response?.data || error);
      throw error.response?.data || error;
    }
  }

  /**
   * 验证模板语法
   * @param {Object} templateData - 模板数据
   * @returns {Promise<Object>} 验证结果
   */
  async validateTemplate(templateData) {
    try {
      const response = await axiosInstance.post(`${this.baseURL}/validate`, templateData);
      return response.data;
    } catch (error) {
      console.error('验证模板失败:', error.response?.data || error);
      throw error.response?.data || error;
    }
  }

  /**
   * 获取模板变量
   * @param {string} templateId - 模板ID
   * @returns {Promise<Array>} 变量列表
   */
  async getTemplateVariables(templateId) {
    try {
      const response = await axiosInstance.get(`${this.baseURL}/${templateId}/variables`);
      return response.data;
    } catch (error) {
      console.error('获取模板变量失败:', error.response?.data || error);
      throw error.response?.data || error;
    }
  }

  /**
   * 搜索模板
   * @param {string} query - 搜索关键词
   * @param {Object} filters - 筛选条件
   * @returns {Promise<Array>} 搜索结果
   */
  async searchTemplates(query, filters = {}) {
    try {
      const response = await axiosInstance.get(`${this.baseURL}/search`, {
        params: { q: query, ...filters }
      });
      return response.data;
    } catch (error) {
      console.error('搜索模板失败:', error.response?.data || error);
      throw error.response?.data || error;
    }
  }

  /**
   * 收藏模板
   * @param {string} templateId - 模板ID
   * @returns {Promise<Object>} 操作结果
   */
  async favoriteTemplate(templateId) {
    try {
      const response = await axiosInstance.post(`${this.baseURL}/${templateId}/favorite`);
      return response.data;
    } catch (error) {
      console.error('收藏模板失败:', error.response?.data || error);
      throw error.response?.data || error;
    }
  }

  /**
   * 取消收藏模板
   * @param {string} templateId - 模板ID
   * @returns {Promise<Object>} 操作结果
   */
  async unfavoriteTemplate(templateId) {
    try {
      const response = await axiosInstance.delete(`${this.baseURL}/${templateId}/favorite`);
      return response.data;
    } catch (error) {
      console.error('取消收藏模板失败:', error.response?.data || error);
      throw error.response?.data || error;
    }
  }

  /**
   * 获取收藏的模板
   * @param {Object} filters - 筛选条件
   * @returns {Promise<Array>} 收藏的模板列表
   */
  async getFavoriteTemplates(filters = {}) {
    try {
      const response = await axiosInstance.get(`${this.baseURL}/favorites`, {
        params: filters
      });
      return response.data;
    } catch (error) {
      console.error('获取收藏模板失败:', error.response?.data || error);
      throw error.response?.data || error;
    }
  }

  /**
   * 批量删除模板
   * @param {Array} templateIds - 模板ID列表
   * @returns {Promise<Object>} 删除结果
   */
  async batchDeleteTemplates(templateIds) {
    try {
      const response = await axiosInstance.post(`${this.baseURL}/batch-delete`, {
        templateIds
      });
      return response.data;
    } catch (error) {
      console.error('批量删除模板失败:', error.response?.data || error);
      throw error.response?.data || error;
    }
  }

  /**
   * 导出模板
   * @param {string} templateId - 模板ID
   * @param {string} format - 导出格式
   * @returns {Promise<Object>} 导出结果
   */
  async exportTemplate(templateId, format = 'json') {
    try {
      const response = await axiosInstance.get(`${this.baseURL}/${templateId}/export`, {
        params: { format }
      });
      return response.data;
    } catch (error) {
      console.error('导出模板失败:', error.response?.data || error);
      throw error.response?.data || error;
    }
  }

  /**
   * 导入模板
   * @param {File|Object} templateData - 模板文件或数据
   * @returns {Promise<Object>} 导入结果
   */
  async importTemplate(templateData) {
    try {
      const formData = new FormData();
      if (templateData instanceof File) {
        formData.append('template', templateData);
      } else {
        formData.append('template', JSON.stringify(templateData));
      }
      
      const response = await axiosInstance.post(`${this.baseURL}/import`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      return response.data;
    } catch (error) {
      console.error('导入模板失败:', error.response?.data || error);
      throw error.response?.data || error;
    }
  }

  /**
   * 获取模板使用统计
   * @param {string} templateId - 模板ID
   * @returns {Promise<Object>} 统计数据
   */
  async getTemplateStats(templateId) {
    try {
      const response = await axiosInstance.get(`${this.baseURL}/${templateId}/stats`);
      return response.data;
    } catch (error) {
      console.error('获取模板统计失败:', error.response?.data || error);
      throw error.response?.data || error;
    }
  }
}

// 创建实例
const templateService = new TemplateService();

export default templateService; 