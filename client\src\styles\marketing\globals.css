/* 营销页面全局样式 - 雾灰 + 紫罗兰主题 */
@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* CSS 变量定义 */
:root {
  /* 主色调 - 柔紫 */
  --marketing-primary-50: #faf7ff;
  --marketing-primary-100: #F2EFFF;
  --marketing-primary-200: #e6d9ff;
  --marketing-primary-300: #d1b8ff;
  --marketing-primary-400: #b488ff;
  --marketing-primary-500: #9B5DE5;
  --marketing-primary-600: #8a4cd9;
  --marketing-primary-700: #7a3fc7;
  --marketing-primary-800: #6b35b5;
  --marketing-primary-900: #5c2ca3;

  /* 背景色系 - 雾灰 */
  --marketing-bg-primary: #F3F4F6;
  --marketing-bg-secondary: #ffffff;
  --marketing-bg-accent: #F2EFFF;
  --marketing-bg-muted: #f9fafb;

  /* 文字色系 */
  --marketing-text-primary: #373737;
  --marketing-text-secondary: #6b7280;
  --marketing-text-muted: #9ca3af;
  --marketing-text-accent: #9B5DE5;
  --marketing-text-inverse: #ffffff;

  /* 边框色 */
  --marketing-border-light: #f3f4f6;
  --marketing-border-default: #e5e7eb;
  --marketing-border-medium: #d1d5db;
  --marketing-border-accent: #9B5DE5;

  /* 阴影 */
  --marketing-shadow-card: 0 4px 6px -1px rgba(155, 93, 229, 0.1), 0 2px 4px -2px rgba(155, 93, 229, 0.1);
  --marketing-shadow-hover: 0 10px 15px -3px rgba(155, 93, 229, 0.1), 0 4px 6px -4px rgba(155, 93, 229, 0.1);
  --marketing-shadow-large: 0 20px 25px -5px rgba(155, 93, 229, 0.1), 0 8px 10px -6px rgba(155, 93, 229, 0.1);

  /* 动画持续时间 */
  --marketing-duration-fast: 200ms;
  --marketing-duration-normal: 300ms;
  --marketing-duration-slow: 500ms;
}

/* 基础样式重置 */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  line-height: 1.6;
  color: var(--marketing-text-primary);
  background-color: var(--marketing-bg-primary);
  overflow-x: hidden;
}

/* 选择文本样式 */
::selection {
  background-color: var(--marketing-primary-200);
  color: var(--marketing-primary-800);
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--marketing-bg-muted);
}

::-webkit-scrollbar-thumb {
  background: var(--marketing-primary-300);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--marketing-primary-400);
}

/* 字体优化 */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.25;
  color: var(--marketing-text-primary);
}

h1 {
  font-size: clamp(2rem, 5vw, 4rem);
  font-weight: 700;
}

h2 {
  font-size: clamp(1.5rem, 4vw, 2.5rem);
}

h3 {
  font-size: clamp(1.25rem, 3vw, 1.875rem);
}

/* 链接样式 */
a {
  color: var(--marketing-text-accent);
  text-decoration: none;
  transition: color var(--marketing-duration-fast) ease;
}

a:hover {
  color: var(--marketing-primary-600);
}

/* 按钮基础样式 */
.btn-base {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  font-weight: 500;
  transition: all var(--marketing-duration-normal) ease;
  cursor: pointer;
  border: none;
  outline: none;
}

.btn-base:focus {
  box-shadow: 0 0 0 3px rgba(155, 93, 229, 0.1);
}

/* 渐变文字效果 */
.text-gradient-primary {
  background: linear-gradient(135deg, var(--marketing-primary-500) 0%, var(--marketing-primary-400) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 玻璃效果 */
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 卡片样式 */
.marketing-card {
  background: var(--marketing-bg-secondary);
  border-radius: 12px;
  box-shadow: var(--marketing-shadow-card);
  border: 1px solid var(--marketing-border-default);
  transition: all var(--marketing-duration-normal) ease;
}

.marketing-card:hover {
  box-shadow: var(--marketing-shadow-hover);
  transform: translateY(-2px);
}

/* 动画类 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulseGlow {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(155, 93, 229, 0.7);
  }
  50% {
    box-shadow: 0 0 0 10px rgba(155, 93, 229, 0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* 动画应用类 */
.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-fade-in-down {
  animation: fadeInDown 0.6s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.4s ease-out;
}

.animate-slide-in-left {
  animation: slideInLeft 0.6s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.6s ease-out;
}

.animate-pulse-glow {
  animation: pulseGlow 2s infinite;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* 响应式容器 */
.container-marketing {
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 1rem;
}

@media (min-width: 640px) {
  .container-marketing {
    padding: 0 1.5rem;
  }
}

@media (min-width: 1024px) {
  .container-marketing {
    padding: 0 2rem;
  }
}

/* 渐变背景 */
.bg-gradient-hero {
  background: linear-gradient(135deg, #F3F4F6 0%, #F2EFFF 50%, #faf7ff 100%);
}

.bg-gradient-primary {
  background: linear-gradient(135deg, #9B5DE5 0%, #b488ff 100%);
}

.bg-gradient-secondary {
  background: linear-gradient(135deg, #F2EFFF 0%, #ffffff 100%);
}

/* 特殊效果 */
.backdrop-blur-lg {
  backdrop-filter: blur(16px);
}

.backdrop-blur-md {
  backdrop-filter: blur(12px);
}

/* 性能优化 */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

/* 辅助功能 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus状态增强 */
.focus-ring {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus-ring:focus {
  outline: 2px solid var(--marketing-primary-500);
  outline-offset: 2px;
}

/* 打印样式 */
@media print {
  .print-hidden {
    display: none !important;
  }
  
  body {
    background: white !important;
    color: black !important;
  }
} 