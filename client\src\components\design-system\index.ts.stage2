// 设计系统导出文件
export { buttonVariants  }; from '@/components/ui/button'
export {ContentDescriptionFooterHeaderTitle } from '@/components/ui/card'
export { badgeVariants } from '@/components/ui/badge'
export { Input } from '@/components/ui/input'
export { Label } from '@/components/ui/label'
export { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
export {} from '@/components/ui/progress'
export { Skeleton } from '@/components/ui/skeleton'
export { Separator } from '@/components/ui/separator'
export {} from '@/components/ui/switch'
export { Select, SelectTrigger SelectContent, SelectItem } from '@/components/ui/select'
export { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs'
export { Dialog, DialogTrigger, DialogContent, DialogHeader, DialogFooter, DialogTitle, DialogDescription } from '@/components/ui/dialog'
export { Popover, PopoverTrigger, PopoverContent } from '@/components/ui/popover'
export { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider } from '@/components/ui/tooltip'
export { Table, TableHeader, TableBody, TableFooter, TableHead, TableRow, TableCell, TableCaption } from '@/components/ui/table'
export { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar'

// 主题组件
export { ThemeToggle, ThemeSelector } from '@/components/design-system/ThemeToggle'

// 现代Dashboard组件
export { Metrics MetricsGrid, QuickMetrics} from '@/components/dashboard/Metrics'
export { 
  ActivityLineChart, 
  DemographicsBarChart, 
  SimpleLineChart, 
  SimpleAreaChart,
  // ChartContainer
} from '@/components/charts/ModernCharts'

// 导航组件
export { ModernSidebar, CollapsibleSidebar } from '@/components/navigation/ModernSidebar'

// 设计Token系统
export { default as designTokens, tailwindExtension } from '@/theme/design-tokens'
export { default as dashboardTheme } from '@/theme/dashboard-theme'

// 工具函数和主题管理
export { cn } from '@/lib/utils'
export { themeManager } from '@/lib/design-system-utils' 