const express = require('express');
const router = express.Router();
const notificationController = require('../controllers/notificationController');
const authMiddleware = require('../middleware/authMiddleware');

// 应用认证中间件
router.use(authMiddleware);

// 获取所有通知规则
router.get('/rules', notificationController.getAllRules);

// 获取单个通知规则
router.get('/rules/:id', notificationController.getRuleById);

// 获取规则运行历史记录
router.get('/rules/:id/history', notificationController.getRuleHistory);

// 创建新通知规则
router.post('/rules', notificationController.createRule);

// 更新通知规则
router.put('/rules/:id', notificationController.updateRule);

// 删除通知规则
router.delete('/rules/:id', notificationController.deleteRule);

// 手动触发通知处理
router.post('/process', notificationController.processNotifications);

module.exports = router; 