export interface FlowNode {
  id: string;
  type: FlowNodeType;
  position: { x: number; y: number };
  data: FlowNodeData;
  style?: React.CSSProperties;
}

export interface FlowEdge {
  id: string;
  source: string;
  target: string;
  sourceHandle?: string;
  targetHandle?: string;
  type?: string;
  data?: {
    condition?: string;
    label?: string;
  };
  style?: React.CSSProperties;
}

export enum FlowNodeType {
  TRIGGER = 'trigger',
  CONDITION = 'condition',
  AI_RESPONSE = 'ai_response',
  HUMAN_HANDOFF = 'human_handoff',
  API_CALL = 'api_call',
  DELAY = 'delay',
  WEBHOOK = 'webhook',
  EMAIL = 'email',
  SMS = 'sms',
  VARIABLE_SET = 'variable_set',
  JUMP = 'jump',
  END = 'end'
}

export interface FlowNodeData {
  label: string;
  description?: string;
  config?: Record<string, any>;
  
  // Trigger节点特有属性
  triggerType?: 'message' | 'keyword' | 'intent' | 'time' | 'webhook';
  triggerValue?: string;
  
  // Condition节点特有属性
  conditionType?: 'text_contains' | 'intent_match' | 'variable_equals' | 'time_range' | 'user_property';
  conditionValue?: string;
  conditionOperator?: 'equals' | 'contains' | 'starts_with' | 'ends_with' | 'greater_than' | 'less_than';
  
  // AI Response节点特有属性
  prompt?: string;
  responseTemplate?: string;
  maxTokens?: number;
  temperature?: number;
  
  // Human Handoff节点特有属性
  handoffReason?: string;
  assignTo?: string;
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  
  // API Call节点特有属性
  apiUrl?: string;
  apiMethod?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  apiHeaders?: Record<string, string>;
  apiBody?: string;
  responseMapping?: Record<string, string>;
  
  // Delay节点特有属性
  delayType?: 'seconds' | 'minutes' | 'hours' | 'days';
  delayValue?: number;
  
  // Variable Set节点特有属性
  variableName?: string;
  variableValue?: string;
  variableType?: 'string' | 'number' | 'boolean' | 'array' | 'object';
  
  // Email/SMS节点特有属性
  recipient?: string;
  subject?: string;
  content?: string;
  template?: string;
}

export interface FlowTemplate {
  id: string;
  name: string;
  description: string;
  category: 'customer_service' | 'sales' | 'support' | 'marketing' | 'custom';
  thumbnail?: string;
  nodes: FlowNode[];
  edges: FlowEdge[];
  variables?: FlowVariable[];
  metadata: {
    author: string;
    version: string;
    createdAt: string;
    updatedAt: string;
    tags: string[];
    difficulty: 'beginner' | 'intermediate' | 'advanced';
    estimatedSetupTime: number; // in minutes
  };
}

export interface FlowVariable {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'array' | 'object';
  defaultValue?: any;
  description?: string;
  required?: boolean;
  validation?: {
    pattern?: string;
    min?: number;
    max?: number;
    options?: string[];
  };
}

export interface FlowExecution {
  id: string;
  flowId: string;
  userId: string;
  status: 'running' | 'completed' | 'failed' | 'paused';
  currentNodeId: string;
  variables: Record<string, any>;
  executionPath: string[];
  startedAt: string;
  completedAt?: string;
  error?: string;
  metrics: {
    totalNodes: number;
    executedNodes: number;
    executionTime: number;
    apiCalls: number;
    humanHandoffs: number;
  };
}

export interface FlowBuilderState {
  nodes: FlowNode[];
  edges: FlowEdge[];
  selectedNode?: string;
  selectedEdge?: string;
  isEditing: boolean;
  isDirty: boolean;
  zoom: number;
  viewport: { x: number; y: number };
  variables: FlowVariable[];
  metadata: {
    name: string;
    description: string;
    version: string;
    tags: string[];
  };
}

export interface FlowValidationResult {
  isValid: boolean;
  errors: FlowValidationError[];
  warnings: FlowValidationWarning[];
}

export interface FlowValidationError {
  nodeId?: string;
  edgeId?: string;
  type: 'missing_connection' | 'invalid_config' | 'circular_reference' | 'unreachable_node';
  message: string;
  severity: 'error' | 'warning';
}

export interface FlowValidationWarning {
  nodeId?: string;
  edgeId?: string;
  type: 'performance' | 'best_practice' | 'accessibility';
  message: string;
  severity: 'warning';
  suggestion?: string;
}

export interface NodeTypeDefinition {
  type: FlowNodeType;
  label: string;
  description: string;
  icon: string;
  color: string;
  category: 'trigger' | 'logic' | 'action' | 'integration' | 'utility';
  inputs: number;
  outputs: number;
  configurable: boolean;
  deprecated?: boolean;
  premium?: boolean;
}

// 预定义的节点类型配置
export const NODE_TYPE_DEFINITIONS: Record<FlowNodeType, NodeTypeDefinition> = {
  [FlowNodeType.TRIGGER]: {
    type: FlowNodeType.TRIGGER,
    label: 'Trigger',
    description: 'Start point for the conversation flow',
    icon: 'play-circle',
    color: '#10B981',
    category: 'trigger',
    inputs: 0,
    outputs: 1,
    configurable: true
  },
  [FlowNodeType.CONDITION]: {
    type: FlowNodeType.CONDITION,
    label: 'Condition',
    description: 'Branch the flow based on conditions',
    icon: 'git-branch',
    color: '#F59E0B',
    category: 'logic',
    inputs: 1,
    outputs: 2,
    configurable: true
  },
  [FlowNodeType.AI_RESPONSE]: {
    type: FlowNodeType.AI_RESPONSE,
    label: 'AI Response',
    description: 'Generate AI-powered response',
    icon: 'brain',
    color: '#8B5CF6',
    category: 'action',
    inputs: 1,
    outputs: 1,
    configurable: true
  },
  [FlowNodeType.HUMAN_HANDOFF]: {
    type: FlowNodeType.HUMAN_HANDOFF,
    label: 'Human Handoff',
    description: 'Transfer to human agent',
    icon: 'user',
    color: '#EF4444',
    category: 'action',
    inputs: 1,
    outputs: 0,
    configurable: true
  },
  [FlowNodeType.API_CALL]: {
    type: FlowNodeType.API_CALL,
    label: 'API Call',
    description: 'Make external API request',
    icon: 'globe',
    color: '#3B82F6',
    category: 'integration',
    inputs: 1,
    outputs: 1,
    configurable: true
  },
  [FlowNodeType.DELAY]: {
    type: FlowNodeType.DELAY,
    label: 'Delay',
    description: 'Wait for specified time',
    icon: 'clock',
    color: '#6B7280',
    category: 'utility',
    inputs: 1,
    outputs: 1,
    configurable: true
  },
  [FlowNodeType.WEBHOOK]: {
    type: FlowNodeType.WEBHOOK,
    label: 'Webhook',
    description: 'Send webhook notification',
    icon: 'send',
    color: '#06B6D4',
    category: 'integration',
    inputs: 1,
    outputs: 1,
    configurable: true
  },
  [FlowNodeType.EMAIL]: {
    type: FlowNodeType.EMAIL,
    label: 'Email',
    description: 'Send email notification',
    icon: 'mail',
    color: '#DC2626',
    category: 'action',
    inputs: 1,
    outputs: 1,
    configurable: true
  },
  [FlowNodeType.SMS]: {
    type: FlowNodeType.SMS,
    label: 'SMS',
    description: 'Send SMS message',
    icon: 'message-square',
    color: '#059669',
    category: 'action',
    inputs: 1,
    outputs: 1,
    configurable: true
  },
  [FlowNodeType.VARIABLE_SET]: {
    type: FlowNodeType.VARIABLE_SET,
    label: 'Set Variable',
    description: 'Set or update variable value',
    icon: 'variable',
    color: '#7C3AED',
    category: 'utility',
    inputs: 1,
    outputs: 1,
    configurable: true
  },
  [FlowNodeType.JUMP]: {
    type: FlowNodeType.JUMP,
    label: 'Jump',
    description: 'Jump to another part of flow',
    icon: 'corner-down-right',
    color: '#F97316',
    category: 'utility',
    inputs: 1,
    outputs: 0,
    configurable: true
  },
  [FlowNodeType.END]: {
    type: FlowNodeType.END,
    label: 'End',
    description: 'End the conversation flow',
    icon: 'square',
    color: '#374151',
    category: 'utility',
    inputs: 1,
    outputs: 0,
    configurable: false
  }
};
