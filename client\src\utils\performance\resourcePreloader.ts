/**
 * 资源预加载器
 * 提供智能预加载图像、字体、CSS等资源的功能
 */

// 预加载状态
type PreloadStatus = 'pending' | 'success' | 'error' | 'not_started'

// 已预加载的资源映射
const preloadedResources = new Map<string, PreloadStatus>();

// 调试模式标志
const isDebugMode = process.env.NODE_ENV === 'development'

/**
 * 创建HTML元素
 * @param tag 标签名称
 * @param attributes 属性对象
 * @returns HTML元素
 */
function createElement<T extends HTMLElement>(
  tag: string,
  attributes: Record<string, string> = {}
): T {
  const element = document.createElement(tag) as T;
  
  Object.entries(attributes).forEach(([key, value]) => {
    element.setAttribute(key, value);
  });
  
  return element;
}

/**
 * 预加载图像
 * @param src 图像URL
 * @param options 选项
 * @returns Promise
 */
export function preloadImage(
  src: string,
  options: {
    crossOrigin?: 'anonymous' | 'use-credentials';
    sizes?: string;
    media?: string;
  } = {}
): Promise<HTMLImageElement> {
  return new Promise((resolve, reject) => {
    // 检查是否已预加载
    if (preloadedResources.has(src)) {
      const status = preloadedResources.get(src);
      if (status === 'success') {
        const existingImg = document.querySelector(`img[src="${src}"]`) as HTMLImageElement;
        if (existingImg) {
          resolve(existingImg);
          return;
        }
      }
    }
    
    // 标记为加载中
    preloadedResources.set(src, 'pending');
    
    const img = new Image();
    
    if (options.crossOrigin) {
      img.crossOrigin = options.crossOrigin;
    }
    
    if (options.sizes) {
      img.sizes = options.sizes;
    }
    
    img.onload = () => {
      preloadedResources.set(src, 'success');
      if (isDebugMode) {
        console.log(`[资源预加载] 图像加载成功: ${src}`);
      }
      resolve(img);
    };
    
    img.onerror = (err) => {
      preloadedResources.set(src, 'error');
      if (isDebugMode) {
        console.error(`[资源预加载] 图像加载失败: ${src}`, err);
      }
      reject(new Error(`图像加载失败: ${src}`));
    };
    
    img.src = src;
  });
}

/**
 * 预加载多个图像
 * @param srcs 图像URL数组
 * @returns Promise
 */
export function preloadImages(srcs: string[]): Promise<HTMLImageElement[]> {
  return Promise.all(srcs.map(src => preloadImage(src)));
}

/**
 * 预加载字体
 * @param fontFamily 字体族名称
 * @param url 字体URL
 * @param options 选项
 */
export function preloadFont(
  fontFamily: string,
  url: string,
  options: {
    fontStyle?: string;
    fontWeight?: string | number;
    fontDisplay?: 'auto' | 'block' | 'swap' | 'fallback' | 'optional';
    unicodeRange?: string;
  } = {}
): Promise<void> {
  return new Promise((resolve, reject) => {
    // 生成唯一标识符
    const id = `${fontFamily}_${url}_${options.fontWeight || 'normal'}`;
    
    // 检查是否已预加载
    if (preloadedResources.has(id)) {
      const status = preloadedResources.get(id);
      
      if (status === 'success') {
        resolve();
        return;
      }
    }
    
    // 标记为加载中
    preloadedResources.set(id, 'pending');
    
    // 创建@font-face规则
    const fontFaceRule = `@font-face {
      font-family: '${fontFamily}'
      src: url('${url}') format('${url.endsWith('.woff2') ? 'woff2' : url.endsWith('.woff') ? 'woff' : url.endsWith('.ttf') ? 'truetype' : 'opentype'}');
      font-style: ${options.fontStyle || 'normal'};
      font-weight: ${options.fontWeight || 'normal'};
      font-display: ${options.fontDisplay || 'swap'};
      ${options.unicodeRange ? `unicode-range: ${options.unicodeRange};` : ''}
    };`;
    
    // 创建样式元素
    const style = document.createElement('style');
    style.setAttribute('data-preload-font', id);
    style.textContent = fontFaceRule;
    document.head.appendChild(style);
    
    // 创建用于检测字体加载的元素
    const testElement = document.createElement('span');
    testElement.style.fontFamily = `'${fontFamily}' monospace`;
    testElement.style.visibility = 'hidden';
    testElement.style.position = 'absolute';
    testElement.style.top = '-10000px';
    testElement.style.left = '-10000px';
    testElement.textContent = 'Font Load Test';
    document.body.appendChild(testElement);
    
    // 使用FontFaceObserver API(如果可用)或回退到超时
    if ('FontFace' in window) {
      // 尝试使用字体API
      const fontFace = new FontFace(fontFamily, `url(${url})`, {
        style: options.fontStyle || 'normal',
        weight: options.fontWeight?.toString() || 'normal',
        display: options.fontDisplay || 'swap',
        unicodeRange: options.unicodeRange
      });
      
      fontFace.load().then(() => {
        // 字体已加载
        (document.fonts as any).add(fontFace);
        document.body.removeChild(testElement);
        preloadedResources.set(id, 'success');
        if (isDebugMode) {
          console.log(`[资源预加载] 字体加载成功: ${fontFamily} (${url})`);
        }
        resolve();
      }).catch((err: Error) => {
        // 字体加载失败
        document.body.removeChild(testElement);
        preloadedResources.set(id, 'error');
        if (isDebugMode) {
          console.error(`[资源预加载] 字体加载失败: ${fontFamily}`, err);
        }
        reject(err);
      });
    } else {
      // 回退到超时检测
      setTimeout(() => {
        document.body.removeChild(testElement);
        preloadedResources.set(id, 'success');
        if (isDebugMode) {
          console.log(`[资源预加载] 字体加载超时(假定成功): ${fontFamily}`);
        }
        resolve();
      }, 3000);
    }
  });
}

/**
 * 预加载CSS文件
 * @param href CSS文件URL
 * @returns Promise
 */
export function preloadCSS(href: string): Promise<HTMLLinkElement> {
  return new Promise((resolve, reject) => {
    // 检查是否已预加载
    if (preloadedResources.has(href)) {
      const status = preloadedResources.get(href);
      
      if (status === 'success') {
        const existingLink = document.querySelector(`link[href="${href}"]`) as HTMLLinkElement;
        if (existingLink) {
          resolve(existingLink);
          return;
        }
      }
    }
    
    // 标记为加载中
    preloadedResources.set(href, 'pending');
    
    // 检查是否已存在相同的链接
    const existingLink = document.querySelector(`link[href="${href}"]`);
    if (existingLink) {
      preloadedResources.set(href, 'success');
      if (isDebugMode) {
        console.log(`[资源预加载] CSS已存在: ${href}`);
      }
      resolve(existingLink as HTMLLinkElement);
      return;
    }
    
    const link = createElement<HTMLLinkElement>('link', {
      rel: 'preload',
      href,
      as: 'style',
      type: 'text/css'
    });
    
    link.onload = () => {
      // 加载成功后将rel更改为stylesheet以应用样式
      link.setAttribute('rel', 'stylesheet');
      preloadedResources.set(href, 'success');
      if (isDebugMode) {
        console.log(`[资源预加载] CSS加载成功: ${href}`);
      }
      resolve(link);
    };
    
    link.onerror = (err) => {
      preloadedResources.set(href, 'error');
      if (isDebugMode) {
        console.error(`[资源预加载] CSS加载失败: ${href}`, err);
      }
      reject(new Error(`CSS加载失败: ${href}`));
    };
    
    document.head.appendChild(link);
  });
}

/**
 * 预加载JavaScript文件
 * @param src 脚本URL
 * @param options 选项
 * @returns Promise
 */
export function preloadScript(
  src: string,
  options: {
    async?: boolean;
    defer?: boolean;
    module?: boolean;
    integrity?: string;
    crossOrigin?: string;
  } = {}
): Promise<HTMLScriptElement> {
  return new Promise((resolve, reject) => {
    // 检查是否已预加载
    if (preloadedResources.has(src)) {
      const status = preloadedResources.get(src);
      
      if (status === 'success') {
        const existingScript = document.querySelector(`script[src="${src}"]`) as HTMLScriptElement;
        if (existingScript) {
          resolve(existingScript);
          return;
        }
      }
    }
    
    // 标记为加载中
    preloadedResources.set(src, 'pending');
    
    // 检查是否已存在相同的脚本
    const existingScript = document.querySelector(`script[src="${src}"]`);
    if (existingScript) {
      preloadedResources.set(src, 'success');
      if (isDebugMode) {
        console.log(`[资源预加载] 脚本已存在: ${src}`);
      }
      resolve(existingScript as HTMLScriptElement);
      return;
    }
    
    const script = createElement<HTMLScriptElement>('script', {
      src,
      type: options.module ? 'module' : 'text/javascript'
    });
    
    if (options.async) {
      script.async = true;
    }
    
    if (options.defer) {
      script.defer = true;
    }
    
    if (options.integrity) {
      script.integrity = options.integrity;
    }
    
    if (options.crossOrigin) {
      script.crossOrigin = options.crossOrigin;
    }
    
    script.onload = () => {
      preloadedResources.set(src, 'success');
      if (isDebugMode) {
        console.log(`[资源预加载] 脚本加载成功: ${src}`);
      }
      resolve(script);
    };
    
    script.onerror = (err) => {
      preloadedResources.set(src, 'error');
      if (isDebugMode) {
        console.error(`[资源预加载] 脚本加载失败: ${src}`, err);
      }
      reject(new Error(`脚本加载失败: ${src}`));
    };
    
    document.head.appendChild(script);
  });
}

/**
 * 使用Link预加载提示预加载任何资源
 * @param url 资源URL
 * @param as 资源类型
 * @param options 选项
 */
export function preloadResource(
  url: string,
  as: 'audio' | 'document' | 'embed' | 'fetch' | 'font' | 'image' | 'object' | 'script' | 'style' | 'track' | 'video' | 'worker',
  options: {
    crossOrigin?: string;
    type?: string;
    media?: string;
    integrity?: string;
  } = {}
): void {
  // 检查是否已预加载
  if (preloadedResources.has(url)) {
    return;
  }
  
  // 标记为加载中
  preloadedResources.set(url, 'pending');
  
  // 检查是否已存在相同的预加载链接
  const existingLink = document.querySelector(`link[rel="preload"][href="${url}"]`);
  if (existingLink) {
    if (isDebugMode) {
      console.log(`[资源预加载] 预加载已存在: ${url}`);
    }
    return;
  }
  
  const attributes: Record<string, string> = {
    rel: 'preload',
    href: url,
    as
  };
  
  if (options.crossOrigin) {
    attributes.crossorigin = options.crossOrigin;
  }
  
  if (options.type) {
    attributes.type = options.type;
  }
  
  if (options.media) {
    attributes.media = options.media;
  }
  
  if (options.integrity) {
    attributes.integrity = options.integrity;
  }
  
  const link = createElement<HTMLLinkElement>('link', attributes);
  document.head.appendChild(link);
  
  if (isDebugMode) {
    console.log(`[资源预加载] 添加预加载提示: ${url} (${as})`);
  }
  
  // 由于没有可靠的方式检测预加载完成，我们假设它会成功
  setTimeout(() => {
    preloadedResources.set(url, 'success');
  }, 1000);
}

/**
 * 检查资源是否已预加载
 * @param url 资源URL
 * @returns 预加载状态
 */
export function getPreloadStatus(url: string): PreloadStatus {
  return preloadedResources.get(url) || 'not_started';
}

/**
 * 获取预加载的资源列表
 * @returns URL列表
 */
export function getPreloadedResources(): string[] {
  return Array.from(preloadedResources.keys());
}

/**
 * 预加载关键资源集合
 * @param resources 资源配置
 */
export function preloadCriticalResources(resources: {
  images?: string[];
  fonts?: Array<{
    family: string;
    url: string;
    options?: Parameters<typeof preloadFont>[2];
  }>;
  styles?: string[];
  scripts?: Array<{
    src: string;
    options?: Parameters<typeof preloadScript>[1];
  }>;
}): Promise<void> {
  const promises: Promise<any>[] = [];
  
  // 预加载图像
  if (resources.images && resources.images.length > 0) {
    promises.push(preloadImages(resources.images));
  }
  
  // 预加载字体
  if (resources.fonts && resources.fonts.length > 0) {
    resources.fonts.forEach(font => {
      promises.push(preloadFont(font.family, font.url, font.options));
    });
  }
  
  // 预加载样式
  if (resources.styles && resources.styles.length > 0) {
    resources.styles.forEach(href => {
      promises.push(preloadCSS(href));
    });
  }
  
  // 预加载脚本
  if (resources.scripts && resources.scripts.length > 0) {
    resources.scripts.forEach(script => {
      promises.push(preloadScript(script.src, script.options));
    });
  }
  
  // 等待所有资源加载完成
  return Promise.all(promises).then(() => {
    if (isDebugMode) {
      console.log('[资源预加载] 所有关键资源加载完成');
    }
  }).catch(err => {
    console.error('[资源预加载] 部分资源加载失败:', err);
  });
}

const resourcePreloader = {
  preloadImage,
  preloadImages,
  preloadFont,
  preloadCSS,
  preloadScript,
  preloadResource,
  preloadCriticalResources,
  getPreloadStatus,
  getPreloadedResources
};

export default resourcePreloader;