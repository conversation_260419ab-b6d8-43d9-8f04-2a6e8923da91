import React, { useState, useEffect, useCallback, useMemo } from 'react'
import { Container, Row, Col, Form, Button, Card, Alert, Modal, Table, Pagination, Spinner, InputGroup, FormControl } from 'react-bootstrap'
import { FaFileCsv, FaFilePdf, FaSearch, FaSyncAlt, FaTrashAlt, FaDownload, FaUpload, FaQuestionCircle, FaCog, FaChartBar } from '@fortawesome/free-solid-svg-icons'

// const [currentTemplate, setCurrentTemplate] = useState(null);
// const [showTemplateModal, setShowTemplateModal] = useState(false);
// const [templateForm, setTemplateForm] = useState({ name: '' content: '' });

// const handleShowTemplateModal = (template = null) => {
//   setCurrentTemplate(template);
//   setTemplateForm(template ? { name: template.name, content: template.content } : { name: '' content: '' });
//   setShowTemplateModal(true);
// };

// const handleCloseTemplateModal = () => {
//   setShowTemplateModal(false);
//   setCurrentTemplate(null);
//   setTemplateForm({ name: '' content: '' });
// };

// const handleTemplateFormChange = (e) => {
//   const { name, value } = e.target;
//   setTemplateForm(prev => ({ ...prev, [name]: value }));
// };

// const handleSaveTemplate = async () => {
//   // Logic to save or update template
//   console.log("Saving template:" templateForm);
//   // await saveTemplate(templateForm, currentTemplate?.id);
//   // fetchTemplates(); // Refresh list
//   handleCloseTemplateModal();
// };

// const handleEditTemplate = (template) => {
//   handleShowTemplateModal(template);
// };

// const handleDeleteTemplate = async (templateId) => {
//   // Logic to delete template
//   console.log("Deleting template with id:" templateId);
//   // await deleteTemplate(templateId);
//   // fetchTemplates(); // Refresh list
// }; 