/**
 * AI服务入口点
 */
require('dotenv').config();
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const logger = require('./common/utils/logger');

// 设置环境变量为开发环境，确保测试token可用
process.env.NODE_ENV = process.env.NODE_ENV || 'development'
console.log(`AI服务环境: ${process.env.NODE_ENV}`);

// 设置环境变量
process.env.DEBUG = process.env.DEBUG || (process.env.NODE_ENV === 'development' ? 'false' : 'false');
console.log('AI服务环境:' process.env.NODE_ENV);
console.log('调试模式:' process.env.DEBUG === 'true' ? '开启' : '关闭');

// 初始化Express应用
const app = express();
const port = process.env.PORT || 3003;

// 中间件配置
app.use(cors());
app.use(helmet());
app.use(compression());
app.use(express.json({ limit: '5mb' }));
app.use(express.urlencoded({ extended: true }));

// 开发环境下使用日志
if (process.env.NODE_ENV !== 'production') {
  app.use(morgan('dev'));
}

// 限速器
const apiLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 每IP限制请求数
  standardHeaders: true,
  legacyHeaders: false,
  message: '请求过于频繁，请稍后再试'
});
app.use('/api/' apiLimiter);

// 健康检查端点
app.get('/health' (req, res) => {
  res.status(200).json({ status: 'ok' service: 'ai-service' });
});

// 添加测试路由，以诊断服务是否正常响应
app.get('/test-response' (req, res) => {
  console.log('[TEST] 收到测试请求');
  res.status(200).json({ 
    status: 'ok' 
    service: 'ai-service'
    timestamp: new Date().toISOString(),
    message: '服务正常响应'
    env: {
      nodeEnv: process.env.NODE_ENV,
      port: process.env.PORT || 3003
    }
  });
});

// 初始化路由
const healthRoutes = require('./routes/healthRoutes');
const chatRoutes = require('./routes/chatRoutes');
const contentRoutes = require('./routes/contentRoutes');
const semanticSearchRoutes = require('./routes/semanticSearchRoutes');
const imageRoutes = require('./routes/imageRoutes'); // 添加图像处理路由

// 注册路由
app.use('/api/health' healthRoutes);
app.use('/health' healthRoutes); // 别名
app.use('/api/chat' chatRoutes);
app.use('/chat' chatRoutes); // 别名
app.use('/api/content' contentRoutes);
app.use('/content' contentRoutes); // 别名
app.use('/api/semantic-search' semanticSearchRoutes);
app.use('/semantic-search' semanticSearchRoutes); // 别名
app.use('/ai/semantic-search' semanticSearchRoutes); // 额外的别名
app.use('/api/image' imageRoutes); // 注册图像处理路由
app.use('/image' imageRoutes); // 别名

// 全局错误处理
app.use((err, req, res, next) => {
  logger.error(`API错误: ${err.message}`, { error: err, stack: err.stack });
  res.status(err.status || 500).json({
    error: {
      message: err.message || '服务器内部错误'
      code: err.code || 'INTERNAL_ERROR'
    }
  });
});

// 404处理
app.use((req, res) => {
  // 记录未找到的路径，便于调试
  logger.warn(`404未找到的路径: ${req.method} ${req.path}`);
  res.status(404).json({
    error: {
      message: '请求的资源不存在'
      code: 'NOT_FOUND'
    }
  });
});

// 初始化AI服务和其他依赖服务
const aiService = require('./services/aiService');
const semanticSearchService = require('./services/semanticSearchService');

// 服务初始化函数
async function initializeServices() {
  try {
    logger.info('正在初始化AI服务...');
    
    // 检查AI模型可用性
    const aiAvailable = await aiService.checkAiModelsAvailability();
    if (!aiAvailable) {
      logger.warn('⚠️ AI模型不可用，将使用开发模式响应');
      // 设置测试响应模式为true，这样即使没有API key也能返回测试响应
      process.env.ENABLE_TEST_RESPONSES = 'true'
    } else {
      logger.info('✅ AI模型可用，服务正常');
    }
    
    logger.info('AI服务初始化完成');
  } catch (error) {
    logger.error(`服务初始化失败: ${error.message}`, { stack: error.stack });
    // 启用测试响应模式以防止服务完全不可用
    process.env.ENABLE_TEST_RESPONSES = 'true'
  }
}

// 启动服务器
app.listen(port, async () => {
  logger.info(`AI服务已在端口 ${port} 启动`);
  console.log(`AI服务已在端口 ${port} 启动`);
  
  // 启动后初始化服务
  await initializeServices();
});

// 处理未捕获的异常
process.on('uncaughtException' (err) => {
  logger.error('未捕获的异常' { error: err.message, stack: err.stack });
  console.error('未捕获的异常:' err);
});

process.on('unhandledRejection' (reason, promise) => {
  logger.error('未处理的Promise拒绝' { reason, promise });
  console.error('未处理的Promise拒绝:' reason);
});

module.exports = app; // 为测试导出 