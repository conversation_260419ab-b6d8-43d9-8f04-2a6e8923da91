/**
 * 高级状态管理系统
 * 包括选择器优化、记忆化策略、状态持久化等功能
 */
import { useState, useEffect, useCallback, useRef, useMemo } from 'react';

// 类型定义
interface StateSelector<T, R> {
  (state: T): R
}

// 状态更新配置
interface StateUpdateConfig {
  debounce?: number;
  throttle?: number;
  batch?: boolean;
  persist?: boolean
}

// 持久化配置
interface PersistenceConfig {
  key: string;
  storage?: 'localStorage' | 'sessionStorage';
  serialize?: (value: any) => string;
  deserialize?: (value: string) => any
}

// 防抖函数
function debounce<T extends (...args: any[]) => any>(func: T, wait: number): T {
  let timeout: NodeJS.Timeout;
  return ((...args: any[]) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait)
}) as T
}

// 节流函数
function throttle<T extends (...args: any[]) => any>(func: T, limit: number): T {
  let inThrottle: boolean;
  return ((...args: any[]) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit)
}
  }) as T
}

/**
 * 创建优化的状态选择器
 */
export function createOptimizedSelector<T, R>(
  selector: StateSelector<T, R>,
  deps: any[] = []
): StateSelector<T, R> {
  let lastState: T;
  let lastResult: R;
  let lastDeps: any[];

  return (state: T): R => {
    // 检查依赖是否变化
    const depsChanged = !lastDeps || deps.some((dep, index) => 
      !Object.is(dep, lastDeps[index])
    );

    // 如果状态或依赖变化，重新计算
    if (depsChanged || !Object.is(state, lastState)) {
      lastResult = selector(state);
      lastState = state;
      lastDeps = [...deps]
}

    return lastResult
}
}

/**
 * 批量状态更新器
 */
export class BatchedStateUpdater<T> {
  private updates: Array<(state: T) => T> = [];
  private isScheduled = false;
  private updateFunction: (updater: (state: T) => T) => void;

  constructor(updateFunction: (updater: (state: T) => T) => void) {
    this.updateFunction = updateFunction
}

  add(updater: (state: T) => T) {
    this.updates.push(updater);
    
    if (!this.isScheduled) {
      this.isScheduled = true;
      Promise.resolve().then(() => this.flush())
}
  }

  flush() {
    if (this.updates.length > 0) {
      const allUpdates = [...this.updates];
      this.updates = [];
      this.isScheduled = false;

      this.updateFunction((prevState: T) => {
        return allUpdates.reduce((state, update) => update(state), prevState)
})
}
  }
}

/**
 * 状态持久化管理器
 */
export class StatePersistenceManager {
  private config: PersistenceConfig;
  private storage: Storage;

  constructor(config: PersistenceConfig) {
    this.config = config;
    this.storage = config.storage === 'sessionStorage' 
      ? window.sessionStorage 
      : window.localStorage
}

  save<T>(value: T): void {
    try {
      const serialized = this.config.serialize 
        ? this.config.serialize(value)
        : JSON.stringify(value);
      this.storage.setItem(this.config.key, serialized)
} catch (error) {
      console.warn('Failed to persist state:', error)
}
  }

  load<T>(): T | null {
    try {
      const stored = this.storage.getItem(this.config.key);
      if (stored === null) return null;
      
      return this.config.deserialize 
        ? this.config.deserialize(stored)
        : JSON.parse(stored)
} catch (error) {
      console.warn('Failed to load persisted state:', error);
      return null
}
  }

  clear(): void {
    this.storage.removeItem(this.config.key)
}
}

/**
 * 高级状态管理Hook
 */
export function useAdvancedState<T>(
  initialState: T | (() => T),
  config: StateUpdateConfig & { persistenceConfig?: PersistenceConfig } = {}
) {
  const {
    debounce: debounceMs,
    throttle: throttleMs,
    batch = false,
    persist = false,
    persistenceConfig
  } = config;

  // 持久化管理器
  const persistenceManager = useMemo(() => {
    if (persist && persistenceConfig) {
      return new StatePersistenceManager(persistenceConfig)
}
    return null
}, [persist, persistenceConfig]);

  // 初始化状态（可能从持久化存储加载）
  const getInitialState = useCallback(() => {
    if (persistenceManager) {
      const persisted = persistenceManager.load<T>();
      if (persisted !== null) return persisted
}
    return typeof initialState === 'function' 
      ? (initialState as () => T)() 
      : initialState
}, [initialState, persistenceManager]);

  const [state, setState] = useState<T>(getInitialState);

  // 批量更新器
  const batchUpdater = useMemo(() => {
    return batch ? new BatchedStateUpdater<T>(setState) : null
}, [batch]);

  // 创建优化的更新函数
  const updateState = useMemo(() => {
    let updateFn = (updater: (prevState: T) => T | T) => {
      if (batch && batchUpdater) {
        if (typeof updater === 'function') {
          batchUpdater.add(updater as (state: T) => T)
} else {
          batchUpdater.add(() => updater)
}
      } else {
        setState(updater as any)
}
    };

    if (debounceMs) {
      updateFn = debounce(updateFn, debounceMs)
} else if (throttleMs) {
      updateFn = throttle(updateFn, throttleMs)
}

    return updateFn
}, [debounceMs, throttleMs, batch, batchUpdater]);

  // 持久化状态变化
  useEffect(() => {
    if (persistenceManager) {
      persistenceManager.save(state)
}
  }, [state, persistenceManager]);

  // 清理函数
  const clearPersistedState = useCallback(() => {
    if (persistenceManager) {
      persistenceManager.clear()
}
  }, [persistenceManager]);

  return {
    state,
    setState: updateState,
    clearPersistedState
  }
}

/**
 * 计算属性Hook（类似Vue的computed）
 */
export function useComputed<R>(
  computeFn: () => R,
  deps: any[],
  options: { 
    lazy?: boolean;
    debounce?: number;
    throttle?: number
} = {}
) {
  const { lazy = false, debounce: debounceMs, throttle: throttleMs } = options;
  const [value, setValue] = useState<R>(() => lazy ? undefined as any : computeFn());
  const computeRef = useRef(computeFn);
  const depsRef = useRef(deps);

  // 创建优化的计算函数
  const optimizedCompute = useMemo(() => {
    let computeFunction = () => {
      const newValue = computeRef.current();
      setValue(newValue);
      return newValue
};

    if (debounceMs) {
      const debouncedFn = debounce(computeFunction, debounceMs);
      return () => {
        debouncedFn();
        return value; // 返回当前值，因为debounced函数返回可能是undefined
      }
} else if (throttleMs) {
      const throttledFn = throttle(computeFunction, throttleMs);
      return () => {
        throttledFn();
        return value; // 返回当前值，因为throttled函数返回可能是undefined
      }
}

    return computeFunction
}, [debounceMs, throttleMs, value]);

  // 依赖变化时重新计算
  useEffect(() => {
    // 检查依赖是否真的变化了
    const hasChanged = depsRef.current.some((dep, index) =>
      !Object.is(dep, deps[index])
    );

    if (hasChanged || (!lazy && value === undefined)) {
      optimizedCompute();
      depsRef.current = deps
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, deps);

  // 手动触发计算
  const recompute = useCallback(() => {
    return optimizedCompute()
}, [optimizedCompute]);

  return {
    value,
    recompute,
    isComputed: value !== undefined
  }
}

/**
 * 状态同步Hook（多组件间状态同步）
 */
export function useStateBroadcast<T>(
  channel: string,
  initialValue: T
) {
  const [state, setState] = useState<T>(initialValue);
  const channelRef = useRef<BroadcastChannel | null>(null);

  useEffect(() => {
    // 创建广播频道
    if (typeof window !== 'undefined' && 'BroadcastChannel' in window) {
      channelRef.current = new BroadcastChannel(channel);
      
      // 监听其他窗口/标签页的状态变化
      channelRef.current.onmessage = (event) => {
        setState(event.data)
}
}

    return () => {
      if (channelRef.current) {
        channelRef.current.close()
}
    }
}, [channel]);

  // 更新状态并广播
  const updateState = useCallback((newState: T | ((prev: T) => T)) => {
    const updatedState = typeof newState === 'function'
      ? (newState as (prev: T) => T)(state)
      : newState;
    
    setState(updatedState);
    
    // 广播到其他窗口/标签页
    if (channelRef.current) {
      channelRef.current.postMessage(updatedState)
}
  }, [state]);

  return [state, updateState] as const
}

/**
 * 性能监控Hook
 */
export function useStatePerformanceMonitor<T>(
  state: T,
  name: string,
  options: {
    logThreshold?: number;
    warnThreshold?: number;
    trackMemory?: boolean
} = {}
) {
  const {
    logThreshold = 10,
    warnThreshold = 50,
    trackMemory = false
  } = options;

  const updateCountRef = useRef(0);
  const lastUpdateRef = useRef(Date.now());
  const performanceDataRef = useRef<{
    updates: number;
    averageInterval: number;
    maxInterval: number;
    minInterval: number
}>({
    updates: 0,
    averageInterval: 0,
    maxInterval: 0,
    minInterval: Infinity
  });

  useEffect(() => {
    const now = Date.now();
    const interval = now - lastUpdateRef.current;
    
    updateCountRef.current++;
    lastUpdateRef.current = now;

    const data = performanceDataRef.current;
    data.updates++;
    data.maxInterval = Math.max(data.maxInterval, interval);
    data.minInterval = Math.min(data.minInterval, interval);
    data.averageInterval = (data.averageInterval * (data.updates - 1) + interval) / data.updates;

    // 性能警告
    if (interval > warnThreshold) {
      console.warn(`[State Performance] Slow update detected in ${name}: ${interval}ms`)
} else if (interval < logThreshold && data.updates > 1) {
      console.log(`[State Performance] Fast update in ${name}: ${interval}ms`)
}

    // 内存使用情况
    if (trackMemory && 'performance' in window && 'memory' in (window as any).performance) {
      const memory = (window as any).performance.memory;
      console.log(`[State Memory] ${name} - Used: ${memory.usedJSHeapSize / 1024 / 1024}MB`)
}
  }, [state, name, logThreshold, warnThreshold, trackMemory]);

  return {
    updateCount: updateCountRef.current,
    performanceData: performanceDataRef.current
  }
}