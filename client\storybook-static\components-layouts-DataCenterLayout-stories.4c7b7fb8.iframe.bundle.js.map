{"version": 3, "file": "components-layouts-DataCenterLayout-stories.4c7b7fb8.iframe.bundle.js", "mappings": ";;;AASA;;AAGA;AACA;;AAEA;;;AAIA;;AAEA;;AAEA;;AAGA;;;AAGA", "sources": ["webpack://client/./src/components/layouts/DataCenterLayout.jsx"], "sourcesContent": ["import React from 'react';\nimport { Outlet, useLocation, Link } from 'react-router-dom';\nimport styled from 'styled-components';\nimport { dataMenu } from '../../config/menu.config';\nimport { useAuth } from '../../context/AuthContext';\n\nconst Layout = styled.div`\n  display: flex;\n  height: 100vh;\n`;\nconst Sidebar = styled.nav`\n  width: 240px;\n  background: ${props => props.theme.colors.surface};\n  box-shadow: ${props => props.theme.shadows.dropdown};\n  padding: 1rem;\n`;\nconst MenuItem = styled.div`\n  margin-bottom: 0.5rem;\n  a {\n    color: ${props => props.theme.colors.text};\n    text-decoration: none;\n    &:hover { color: ${props => props.theme.colors.primary}; }\n  }\n`;\nconst Content = styled.main`\n  flex: 1;\n  background: ${props => props.theme.colors.background};\n  padding: 2rem;\n  overflow-y: auto;\n`;\n\nexport default function DataCenterLayout() {\n  const location = useLocation();\n  const { user } = useAuth();\n  const userPlan = user?.plan || 'free';\n  // 在 Dashboard 页（/data/dashboard）路由时隐藏侧边栏\n  const hideSidebar = location.pathname === '/data/dashboard';\n  // simple plan check function\n  const planOrder = ['free','proA','proB','enterprise'];\n  const hasAccess = (minPlan) => planOrder.indexOf(userPlan) >= planOrder.indexOf(minPlan);\n\n  return (\n    <Layout>\n      {!hideSidebar && (\n        <Sidebar>\n          {dataMenu.filter(item => hasAccess(item.minPlan)).map(item => (\n            <MenuItem key={item.key}>\n              <Link to={item.path} className={location.pathname.startsWith(item.path) ? 'active' : ''}>\n                {item.title}\n              </Link>\n            </MenuItem>\n          ))}\n        </Sidebar>\n      )}\n      <Content>\n        <Outlet />\n      </Content>\n    </Layout>\n  );\n} "], "names": [], "sourceRoot": ""}