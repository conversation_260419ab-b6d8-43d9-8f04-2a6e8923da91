import React from 'react'
import { Helmet } from 'react-helmet-async'
import { CustomerReviews } from '@/components/ui/reviews';

const ReviewsDemo: React.FC = () => {;
  return (<>
      <Helmet>
        <title>Customer Reviews Demo - iTeraBiz</title>
        <meta name="description" content="Demo page showcasing the new Customer Reviews section" />
      </Helmet>
      <div className="min-h-screen bg-white">
        {/* Demo header */}
        <div className="bg-gradient-to-r from-purple-600 to-pink-600 text-white py-16">
          <div className="max-w-4xl mx-auto px-4 text-center">
            <h1 className="text-4xl font-bold mb-4">
              Customer Reviews Demo
            </h1>
            <p className="text-xl opacity-90">
              Experience the new optimized Customer Reviews section with modern design and animations
            </p>
          </div>
        </div>
        {/* Reviews section */}
        <CustomerReviews 
          showControls={true}
          autoSlide={true}
          slideInterval={6000}
        />
        {/* Feature highlights */}
        <div className="py-16 px-4 bg-gray-50">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-center mb-12 text-gray-900">
              🎨 Optimization Features
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2, lg:grid-cols-3 gap-8">
              <div className="bg-white rounded-2xl p-6 shadow-lg">
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
                  <span className="text-2xl">✨</span>
                </div>
                <h3 className="text-xl font-semibold mb-2">Modern Design</h3>
                <p className="text-gray-600">Glass morphism effects, gradient accents, and consistent purple theme matching your brand</p>
              </div>
              <div className="bg-white rounded-2xl p-6 shadow-lg">
                <div className="w-12 h-12 bg-pink-100 rounded-lg flex items-center justify-center mb-4">
                  <span className="text-2xl">🎪</span>
                </div>
                <h3 className="text-xl font-semibold mb-2">Rich Animations</h3>
                <p className="text-gray-600">Framer Motion animations with staggered reveals, hover effects, and smooth transitions</p>
              </div>
              <div className="bg-white rounded-2xl p-6 shadow-lg">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                  <span className="text-2xl">📱</span>
                </div>
                <h3 className="text-xl font-semibold mb-2">Responsive Layout</h3>
                <p className="text-gray-600">Perfectly adapted for mobile, tablet, and desktop with intelligent grid layouts</p>
              </div>
              <div className="bg-white rounded-2xl p-6 shadow-lg">
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
                  <span className="text-2xl">⭐</span>
                </div>
                <h3 className="text-xl font-semibold mb-2">Interactive Stars</h3>
                <p className="text-gray-600">Animated star ratings with hover effects, glow animations, and partial rating support</p>
              </div>
              <div className="bg-white rounded-2xl p-6 shadow-lg">
                <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mb-4">
                  <span className="text-2xl">🎯</span>
                </div>
                <h3 className="text-xl font-semibold mb-2">Auto Pagination</h3>
                <p className="text-gray-600">Intelligent auto-slide with manual controls, visibility detection, and smooth page transitions</p>
              </div>
              <div className="bg-white rounded-2xl p-6 shadow-lg">
                <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mb-4">
                  <span className="text-2xl">📊</span>
                </div>
                <h3 className="text-xl font-semibold mb-2">Smart Statistics</h3>
                <p className="text-gray-600">Dynamic calculation of average ratings, review counts, and satisfaction metrics</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default ReviewsDemo;