"use strict";(self.webpackChunkclient=self.webpackChunkclient||[]).push([[452],{"./src/components/layouts/SettingsCenterLayout.stories.jsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{EnterpriseContentSettings:()=>EnterpriseContentSettings,FreeAgentReplySettings:()=>FreeAgentReplySettings,__namedExportsOrder:()=>__namedExportsOrder,default:()=>SettingsCenterLayout_stories});__webpack_require__("./node_modules/react/index.js");var chunk_AYJ5UCUI=__webpack_require__("./node_modules/react-router/dist/development/chunk-AYJ5UCUI.mjs"),styled_components_browser_esm=__webpack_require__("./node_modules/styled-components/dist/styled-components.browser.esm.js"),menu_config=__webpack_require__("./src/config/menu.config.js"),AuthContext=__webpack_require__("./src/context/AuthContext.js"),jsx_runtime=__webpack_require__("./node_modules/react/jsx-runtime.js");const Layout=styled_components_browser_esm.Ay.div`
  display: flex;
  height: 100vh;
`,Sidebar=styled_components_browser_esm.Ay.nav`
  width: 240px;
  background: ${props=>props.theme.colors.surface};
  box-shadow: ${props=>props.theme.shadows.dropdown};
  padding: 1rem;
`,MenuItem=styled_components_browser_esm.Ay.div`
  margin-bottom: 0.5rem;
  a {
    color: ${props=>props.theme.colors.text};
    text-decoration: none;
    &:hover { color: ${props=>props.theme.colors.primary}; }
  }
`,Content=styled_components_browser_esm.Ay.main`
  flex: 1;
  background: ${props=>props.theme.colors.background};
  padding: 2rem;
  overflow-y: auto;
`;function SettingsCenterLayout(){const location=(0,chunk_AYJ5UCUI.zy)(),{user}=(0,AuthContext.As)(),userPlan=(null==user?void 0:user.plan)||"free",planOrder=["free","proA","proB","enterprise"],hideSidebar="/settings"===location.pathname;return console.log("[SettingsCenterLayout] Current location.pathname:",location.pathname),console.log("[SettingsCenterLayout] hideSidebar:",hideSidebar),console.log("[SettingsCenterLayout] User plan for menu:",userPlan),(0,jsx_runtime.jsxs)(Layout,{children:[!hideSidebar&&(0,jsx_runtime.jsx)(Sidebar,{children:menu_config.j.filter((item=>{return minPlan=item.minPlan,planOrder.indexOf(userPlan)>=planOrder.indexOf(minPlan);var minPlan})).map((item=>(0,jsx_runtime.jsx)(MenuItem,{children:(0,jsx_runtime.jsx)(chunk_AYJ5UCUI.N_,{to:item.path,className:location.pathname.startsWith(item.path)?"active":"",children:item.title})},item.key)))}),(0,jsx_runtime.jsx)(Content,{children:(0,jsx_runtime.jsx)(chunk_AYJ5UCUI.sv,{})})]})}SettingsCenterLayout.__docgenInfo={description:"",methods:[],displayName:"SettingsCenterLayout"};const SettingsCenterLayout_stories={title:"Layouts/Settings Center",component:SettingsCenterLayout,tags:["layout-routing-issue"],decorators:[(Story,context)=>(0,jsx_runtime.jsx)(AuthContext.cy.Provider,{value:{user:{plan:context.args.plan}},children:(0,jsx_runtime.jsx)(chunk_AYJ5UCUI.fS,{initialEntries:[context.args.route],children:(0,jsx_runtime.jsx)(Story,{})})})],argTypes:{plan:{control:{type:"select"},options:["free","proA","proB","enterprise"]},route:{control:"text"}}},Template=args=>(0,jsx_runtime.jsx)(SettingsCenterLayout,{}),EnterpriseContentSettings=Template.bind({});EnterpriseContentSettings.args={plan:"enterprise",route:"/settings/content-generator"};const FreeAgentReplySettings=Template.bind({});FreeAgentReplySettings.args={plan:"free",route:"/settings/agent-reply"};const __namedExportsOrder=["EnterpriseContentSettings","FreeAgentReplySettings"];EnterpriseContentSettings.parameters={...EnterpriseContentSettings.parameters,docs:{...EnterpriseContentSettings.parameters?.docs,source:{originalSource:"args => <SettingsCenterLayout />",...EnterpriseContentSettings.parameters?.docs?.source}}},FreeAgentReplySettings.parameters={...FreeAgentReplySettings.parameters,docs:{...FreeAgentReplySettings.parameters?.docs,source:{originalSource:"args => <SettingsCenterLayout />",...FreeAgentReplySettings.parameters?.docs?.source}}}},"./src/config/menu.config.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.d(__webpack_exports__,{j:()=>settingsMenu,k:()=>dataMenu});const dataMenu=[{key:"dashboard",path:"/data/dashboard",title:"Dashboard",icon:null,minPlan:"free"},{key:"agent-reply",path:"/data/agent-reply",title:"Auto Reply",icon:null,minPlan:"proA"},{key:"walkin-booking",path:"/data/walkin-booking",title:"Walk-in Booking",icon:null,minPlan:"proA"},{key:"onsite-booking",path:"/data/onsite-booking",title:"On-site Booking",icon:null,minPlan:"proA"},{key:"content-generator",path:"/data/content-generator",title:"Content Generator",icon:null,minPlan:"proA"},{key:"analytics",path:"/data/analytics",title:"数据分析",icon:null,minPlan:"proA"},{key:"tasks",path:"/data/tasks",title:"任务管理",icon:null,minPlan:"proA"}],settingsMenu=[{key:"agent-reply",path:"/settings/agent-reply",title:"Auto Reply Settings",icon:null,minPlan:"free"},{key:"walkin-booking",path:"/settings/walkin-booking",title:"Walk-in Settings",icon:null,minPlan:"free"},{key:"onsite-booking",path:"/settings/onsite-booking",title:"On-site Settings",icon:null,minPlan:"free"},{key:"content-generator",path:"/settings/content-generator",title:"Content Generator Settings",icon:null,minPlan:"free"},{key:"platform-api",path:"/settings/platform-api",title:"Platform API Management",icon:null,minPlan:"free"}]}}]);
//# sourceMappingURL=components-layouts-SettingsCenterLayout-stories.f185682b.iframe.bundle.js.map