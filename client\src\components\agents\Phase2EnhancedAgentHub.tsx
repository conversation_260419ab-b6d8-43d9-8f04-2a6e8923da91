/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useState, Suspense, lazy } from 'react'
import { useTranslation } from 'react-i18next'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { 
  Workflow, 
  Building, 
  BarChart3, 
  Settings, 
  Sparkles,
  Loader2,
  CheckCircle,
  AlertCircle,
  Clock
} from 'lucide-react'
import { cn } from '@/lib/utils'
import LazyConfigLoader from './details/configforms/LazyConfigLoader'

// 懒加载第二阶段组件
const VisualFlowBuilder = lazy(() => import('./flow-builder/VisualFlowBuilder'))
const CRMIntegrationHub = lazy(() => import('./crm-integration/CRMIntegrationHub'))
const AdvancedAnalyticsEngine = lazy(() => import('./analytics/AdvancedAnalyticsEngine'))

// 加载状态组件
const LoadingFallback: React.FC<{ feature: string }> = ({ feature }) => (
  <Card>
    <CardContent className="pt-6">
      <div className="text-center space-y-4">
        <Loader2 className="h-8 w-8 animate-spin mx-auto text-primary" />
        <div>
          <h3 className="text-lg font-semibold">Loading {feature}</h3>
          <p className="text-sm text-muted-foreground">
            Preparing advanced features...
          </p>
        </div>
        <div className="space-y-2">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-3/4 mx-auto" />
          <Skeleton className="h-4 w-1/2 mx-auto" />
        </div>
      </div>
    </CardContent>
  </Card>
)

interface Phase2EnhancedAgentHubProps {
  agentId?: string;
  agentType?: any;
  config?: any;
  onConfigChange?: (...args: any[]) => void;
  className?: string;
}

const Phase2EnhancedAgentHub: React.FC<Phase2EnhancedAgentHubProps> = ({
  agentId,
  agentType,
  config,
  onConfigChange,
  className
}) => {
  const { t } = useTranslation()
  const [activeTab, setActiveTab] = useState('configuration')
  const [featureStatus, setFeatureStatus] = useState({
    configuration: 'ready',
    flowBuilder: 'ready',
    crmIntegration: 'ready',
    analytics: 'ready',
  });

  // 获取功能状态图标
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'ready':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'loading':
        return <Loader2 className="h-4 w-4 animate-spin text-blue-600" />
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-600" />
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-600" />
      default:
        return <CheckCircle className="h-4 w-4 text-gray-400" />
    }
  }

  // 功能配置
  const features = [
    {
      id: 'configuration',
      name: 'Smart Configuration',
      description: 'AI-powered configuration with intelligent recommendations',
      icon: <Settings className="h-5 w-5" />,
      status: featureStatus.configuration,
      tier: 'core',
    },
    {
      id: 'flowBuilder',
      name: 'Visual Flow Builder',
      description: 'Drag-and-drop conversation flow designer',
      icon: <Workflow className="h-5 w-5" />,
      status: featureStatus.flowBuilder,
      tier: 'premium',
    },
    {
      id: 'crmIntegration',
      name: 'CRM Integration Hub',
      description: 'Connect with Salesforce, HubSpot, and more',
      icon: <Building className="h-5 w-5" />,
      status: featureStatus.crmIntegration,
      tier: 'premium',
    },
    {
      id: 'analytics',
      name: 'Advanced Analytics',
      description: 'Comprehensive insights and performance metrics',
      icon: <BarChart3 className="h-5 w-5" />,
      status: featureStatus.analytics,
      tier: 'enterprise',
    },
  ];

  // 获取层级颜色
  const getTierColor = (tier: string) => {
    switch (tier) {
      case 'core':
        return 'bg-blue-100 text-blue-800'
      case 'premium':
        return 'bg-purple-100 text-purple-800'
      case 'enterprise':
        return 'bg-gold-100 text-gold-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Enhanced AI Agent</h1>
          <p className="text-muted-foreground">
            Advanced configuration, flow building, integrations, and analytics
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="secondary" className="bg-gradient-to-r from-blue-100 to-purple-100">
            <Sparkles className="h-3 w-3 mr-1" />
            Phase 2 Features
          </Badge>
        </div>
      </div>

      {/* 功能状态概览 */}
      <Card>
        <CardHeader>
          <CardTitle>Feature Status</CardTitle>
          <CardDescription>Overview of available features and their current status</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {features.map((feature) => (
              <div
                key={feature.id}
                className={cn(
                  "p-4 rounded-lg border transition-all duration-200 cursor-pointer hover:shadow-md",
                  activeTab === feature.id && "ring-2 ring-primary ring-opacity-20 bg-primary/5"
                )}
                onClick={() => setActiveTab(feature.id)}
              >
                <div className="flex items-center gap-3 mb-2">
                  <div className="p-2 rounded-lg bg-primary/10 text-primary">
                    {feature.icon}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <h3 className="font-medium text-sm">{feature.name}</h3>
                      {getStatusIcon(feature.status)}
                    </div>
                    <Badge className={getTierColor(feature.tier)} variant="secondary">
                      {feature.tier}
                    </Badge>
                  </div>
                </div>
                <p className="text-xs text-muted-foreground">{feature.description}</p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* 主要功能区域 */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="configuration" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            <span className="hidden sm:inline">Configuration</span>
          </TabsTrigger>
          <TabsTrigger value="flowBuilder" className="flex items-center gap-2">
            <Workflow className="h-4 w-4" />
            <span className="hidden sm:inline">Flow Builder</span>
          </TabsTrigger>
          <TabsTrigger value="crmIntegration" className="flex items-center gap-2">
            <Building className="h-4 w-4" />
            <span className="hidden sm:inline">CRM Hub</span>
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            <span className="hidden sm:inline">Analytics</span>
          </TabsTrigger>
        </TabsList>

        {/* 智能配置标签页 */}
        <TabsContent value="configuration" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5 text-primary" />
                Smart Configuration System
              </CardTitle>
              <CardDescription>
                AI-powered configuration with intelligent recommendations and real-time validation
              </CardDescription>
            </CardHeader>
            <CardContent>
              <LazyConfigLoader
                agentType={agentType}
                config={config}
                onConfigChange={(key, value) => onConfigChange?.(key, value)}
              />
            </CardContent>
          </Card>
        </TabsContent>

        {/* 可视化流程构建器标签页 */}
        <TabsContent value="flowBuilder" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Workflow className="h-5 w-5 text-primary" />
                Visual Flow Builder
              </CardTitle>
              <CardDescription>
                Design conversation flows with drag-and-drop interface and real-time validation
              </CardDescription>
            </CardHeader>
            <CardContent className="p-0">
              <Suspense fallback={<LoadingFallback feature="Visual Flow Builder" />}>
                <div className="h-[600px]">
                  <VisualFlowBuilder
                    onSave={(nodes, edges) => {
                      console.log('Saving flow:', { nodes, edges })
                    }}
                    onTest={(nodes, edges) => {
                      console.log('Testing flow:', { nodes, edges })
                    }}
                  />
                </div>
              </Suspense>
            </CardContent>
          </Card>
        </TabsContent>

        {/* CRM集成中心标签页 */}
        <TabsContent value="crmIntegration" className="space-y-4">
          <Suspense fallback={<LoadingFallback feature="CRM Integration Hub" />}>
            <CRMIntegrationHub
              onAddConnection={(providerId) => {
                console.log('Adding connection for:', providerId)
              }}
              onUpdateConnection={(connectionId, updates) => {
                console.log('Updating connection:', connectionId, updates)
              }}
              onDeleteConnection={(connectionId) => {
                console.log('Deleting connection:', connectionId)
              }}
            />
          </Suspense>
        </TabsContent>

        {/* 高级分析引擎标签页 */}
        <TabsContent value="analytics" className="space-y-4">
          <Suspense fallback={<LoadingFallback feature="Advanced Analytics Engine" />}>
            <AdvancedAnalyticsEngine />
          </Suspense>
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default Phase2EnhancedAgentHub
