import React, { Suspense, lazy, useEffect } from 'react'
import {
  Route,
  Routes,
  Navigate,
  Outlet
} from 'react-router-dom'
import { useAuth } from './contexts/AuthContext'
import ErrorBoundary from './components/common/ErrorBoundary'
import { LoadingProvider } from './contexts/LoadingContext'
import { ThemeProvider as CustomThemeProvider } from './contexts/ThemeProvider'
import { ThemeProvider as NextThemesProvider } from 'next-themes'
import { AppLayout } from './components/layouts/AppLayout'
import { ToastContainer } from 'react-toastify'
import 'react-toastify/dist/ReactToastify.css'
import './App.css'
import './styles/globals.css'
import './styles/modern-charts.css'
import './styles/notfound-animations.css'
import { HelmetProvider } from 'react-helmet-async'
import { NextUIProvider } from '@nextui-org/react'
import { initializeEnvironment } from './utils/env-setup'

// Initialize i18n
import './lib/i18n'

// 简单的加载指示器组件，用于Suspense fallback
const LoadingIndicator = ({ fullScreen = false }) => (
  <div className={`flex items-center justify-center ${fullScreen ? 'h-screen' : 'h-full'}`}>
    <div className="animate-spin rounded-full h-8 w-8 border-2 border-gray-300 border-t-purple-600"></div>
  </div>
);

// 懒加载核心页面组件 - 命名导出组件需要特殊处理
const OverviewPage = lazy(() => import('./pages/OverviewPage'));
const DataInsightPage = lazy(() => import('./pages/DataInsightPage'));
const AgentListPage = lazy(() => import('./pages/agents/AgentListPage'));
const AgentDetailPage = lazy(() => import('./pages/agents/AgentDetailPage'));
const StaffManagementPage = lazy(() => import('./pages/StaffManagementPage'));
const SystemSettingsPage = lazy(() => import('./pages/SystemSettingsPage'));
const UserProfilePage = lazy(() => import('./pages/UserProfilePage'));
const ChatPage = lazy(() => import('./pages/ChatPage'));
const AnalyticsPageV2 = lazy(() => import('./pages/AnalyticsPageV2'));
const DashboardHome = lazy(() => import('./pages/DashboardHome'));

// 预约模块和设置模块路由
const BookingRoutes = lazy(() => import('./routes/BookingRoutes'));
const SettingsRoutes = lazy(() => import('./routes/SettingsRoutes'));

// 已有的懒加载组件
const HomePage = lazy(() => import('./pages/home/<USER>'));
const OptimizedLoginPage = lazy(() => import('./pages/auth/OptimizedLoginPage'));
const OptimizedRegisterPage = lazy(() => import('./pages/auth/OptimizedRegisterPage'));
const ForgotPasswordPage = lazy(() => import('./pages/auth/ForgotPasswordPage'));
const NotFoundPage = lazy(() => import('./pages/NotFoundPage'));
const ReviewsDemo = lazy(() => import('./pages/ReviewsDemo'));
const ButtonShowcase = lazy(() => import('./components/ui/button/ButtonShowcase'));
const TestMinimalFooter = lazy(() => import('./components/enhanced-landing/TestMinimalFooter'));
const BillingPage = lazy(() => import('./pages/BillingPage'));
const CreateAgentPage = lazy(() => import('./pages/agents/CreateAgentPage'));
const AIAgentManagementPage = lazy(() => import('./pages/agents/AIAgentManagementPage'));
const PlatformAPIManagementPage = lazy(() => import('./pages/integrations/PlatformAPIManagementPage'));
const TestBackgroundPaths = lazy(() => import('./pages/TestBackgroundPaths'));

const AppDashboardLayout = () => (
  <AppLayout>
    <Outlet />
  </AppLayout>
);

function AppContent() {
  const { isAuthenticated, loading } = useAuth();

  if (loading) {
    return <LoadingIndicator fullScreen />
}
  
  return (
    <Routes>
      {/* Public routes first */}
      <Route path="/home" element={<Suspense fallback={<LoadingIndicator fullScreen />}><HomePage /></Suspense>} />
      <Route path="/test-footer" element={<Suspense fallback={<LoadingIndicator fullScreen />}><TestMinimalFooter /></Suspense>} />
      <Route path="/reviews-demo" element={<Suspense fallback={<LoadingIndicator fullScreen />}><ReviewsDemo /></Suspense>} />
      <Route path="/button-showcase" element={<Suspense fallback={<LoadingIndicator fullScreen />}><ButtonShowcase /></Suspense>} />
      <Route path="/test-background-paths" element={<Suspense fallback={<LoadingIndicator fullScreen />}><TestBackgroundPaths /></Suspense>} />
      {!isAuthenticated && <Route path="/login" element={<Suspense fallback={<LoadingIndicator fullScreen />}><OptimizedLoginPage /></Suspense>} />}
      {!isAuthenticated && <Route path="/register" element={<Suspense fallback={<LoadingIndicator fullScreen />}><OptimizedRegisterPage /></Suspense>} />}
      {!isAuthenticated && <Route path="/forgot-password" element={<Suspense fallback={<LoadingIndicator fullScreen />}><ForgotPasswordPage /></Suspense>} />}

      {/* Authenticated routes & Redirects */}
      {isAuthenticated ? (
        <>
          {/* Main app routes - wrapped in AppLayout */}
          <Route path="/" element={<AppDashboardLayout />}>
            <Route index element={<Navigate to="overview" replace />} />
            <Route path="overview" element={
              <Suspense fallback={<LoadingIndicator />}>
                <OverviewPage />
              </Suspense>
            } />
            <Route path="dashboard" element={<Navigate to="overview" replace />} />
            <Route path="system-monitoring" element={
              <Suspense fallback={<LoadingIndicator />}>
                <DashboardHome />
              </Suspense>
            } />
            <Route path="data-insight" element={
              <Suspense fallback={<LoadingIndicator />}>
                <DataInsightPage />
              </Suspense>
            } />
            <Route path="agents" element={
              <Suspense fallback={<LoadingIndicator />}>
                <AgentListPage />
              </Suspense>
            } />
            <Route path="agents/new" element={
              <Suspense fallback={<LoadingIndicator />}>
                <CreateAgentPage />
              </Suspense>
            } />
            <Route path="agents/:agentId" element={
              <Suspense fallback={<LoadingIndicator />}>
                <AgentDetailPage />
              </Suspense>
            } />
            <Route path="agents/management" element={
              <Suspense fallback={<LoadingIndicator />}>
                <AIAgentManagementPage />
              </Suspense>
            } />
            <Route path="platform-api" element={
              <Suspense fallback={<LoadingIndicator />}>
                <PlatformAPIManagementPage />
              </Suspense>
            } />
            <Route path="billing" element={
              <Suspense fallback={<LoadingIndicator />}>
                <BillingPage />
              </Suspense>
            } />
            <Route path="analytics" element={
              <Suspense fallback={<LoadingIndicator />}>
                <AnalyticsPageV2 />
              </Suspense>
            } />
            <Route path="staff" element={
              <Suspense fallback={<LoadingIndicator />}>
                <StaffManagementPage />
              </Suspense>
            } />
            <Route path="system-settings" element={
              <Suspense fallback={<LoadingIndicator />}>
                <SystemSettingsPage />
              </Suspense>
            } />
            <Route path="profile" element={
              <Suspense fallback={<LoadingIndicator />}>
                <UserProfilePage />
              </Suspense>
            } />
            <Route path="chat" element={
              <Suspense fallback={<LoadingIndicator />}>
                <ChatPage />
              </Suspense>
            } />
            
            {/* 预约模块路由 - 使用懒加载路由组件 */}
            <Route path="booking/*" element={
              <Suspense fallback={<LoadingIndicator />}>
                <BookingRoutes />
              </Suspense>
            } />

            {/* 设置页面路由 - 使用懒加载路由组件 */}
            <Route path="settings/*" element={
              <Suspense fallback={<LoadingIndicator />}>
                <SettingsRoutes />
              </Suspense>
            } />
            
            {/* Catch-all for unknown routes within app - redirect to overview */}
            <Route path="*" element={<Navigate to="/overview" replace />} />
          </Route>
          
          {/* Redirect old dashboard routes to new paths */}
          <Route path="/dashboard/*" element={<Navigate to="/overview" replace />} />
          <Route path="/login" element={<Navigate to="/overview" replace />} />
          <Route path="/register" element={<Navigate to="/overview" replace />} />
        </>
      ) : (
        <>
          <Route path="/" element={<Navigate to="/home" replace />} />
          <Route path="/overview" element={<Navigate to="/login" replace />} />
          <Route path="/dashboard" element={<Navigate to="/login" replace />} />
          <Route path="/data-insight" element={<Navigate to="/login" replace />} />
          <Route path="/agents" element={<Navigate to="/login" replace />} />
          <Route path="/platform-api" element={<Navigate to="/login" replace />} />
          <Route path="/billing" element={<Navigate to="/login" replace />} />
          <Route path="/analytics" element={<Navigate to="/login" replace />} />
          <Route path="/staff" element={<Navigate to="/login" replace />} />
          <Route path="/system-settings" element={<Navigate to="/login" replace />} />
          <Route path="*" element={<Navigate to="/home" replace />} /> 
        </>
      )}
      
      <Route path="*" element={<Suspense fallback={<LoadingIndicator fullScreen />}><NotFoundPage /></Suspense>} />
    </Routes>
  )
}

function App() {
  useEffect(() => {
    // 初始化Stripe配置
    initializeEnvironment();
    
    // 清理Facebook OAuth等可能产生的无用URL fragments
    if (window.location.hash === '#_=_' || window.location.hash.includes('_=_')) {
      console.log('🧹 Cleaning up OAuth callback fragment:', window.location.hash);
      window.history.replaceState(null, '', window.location.pathname + window.location.search);
    }
  }, []);

  return (
    <HelmetProvider>
      <NextThemesProvider attribute="class" defaultTheme="system" enableSystem>
        <CustomThemeProvider defaultTheme="light" storageKey="saas-ui-theme">
          <LoadingProvider>
            <NextUIProvider>
              <ErrorBoundary>
                <div className="App">
                  <AppContent />
                  <ToastContainer 
                    position="top-right" 
                    autoClose={4000}
                    hideProgressBar={false}
                    newestOnTop={true}
                    closeOnClick={true}
                    rtl={false}
                    pauseOnFocusLoss={false}
                    draggable={true}
                    pauseOnHover={false}
                    theme="dark"
                    limit={5}
                    style={{
                      fontFamily: '"Inter" -apple-system, BlinkMacSystemFont, sans-serif'
                    }}
                  />
                </div>
              </ErrorBoundary>
            </NextUIProvider>
          </LoadingProvider>
        </CustomThemeProvider>
      </NextThemesProvider>
    </HelmetProvider>
  )
}

export default App; 