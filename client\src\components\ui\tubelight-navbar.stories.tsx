import type { <PERSON>a, StoryObj } from '@storybook/react'
import { Home, User, Briefcase, FileText, Settings } from 'lucide-react'
import { NavBar } from './tubelight-navbar'

const meta: Meta<typeof NavBar> = {
  title: 'UI/TubelightNavBar',
  component: NavBar,
  parameters: {
    layout: 'fullscreen',
  },
  tags: ['autodocs'],
}

export default meta
type Story = StoryObj<typeof NavBar>

const navItems = [
  { name: 'Home', url: '#hero', icon: Home },
  { name: 'Features', url: '#features', icon: Briefcase },
  { name: 'Pricing', url: '#pricing', icon: FileText },
  { name: 'About', url: '#testimonials', icon: User },
  { name: 'Dashboard', url: '/dashboard', icon: Settings }
]

export const Default: Story = {
  args: {
    items: navItems,
    isDarkMode: false,
  },
}

export const DarkMode: Story = {
  args: {
    items: navItems,
    isDarkMode: true,
  },
  parameters: {
    backgrounds: { default: 'dark' },
  },
}

export const WithCustomClass: Story = {
  args: {
    items: navItems,
    className: 'custom-navbar',
    isDarkMode: false,
  },
} 