/**
 * 懒加载组件工具
 * 提供React组件的智能懒加载功能，支持预加载和显示加载状态
 * 
 * @version 1.0.0
 * @date 2025-03-06
 * 
 * 功能特点：
 * 1. 集中管理所有懒加载组件
 * 2. 多种预加载触发方式：悬停、可见性、距离、手动
 * 3. 与代码分割系统集成，优化资源加载
 * 4. 提供加载状态跟踪和状态查询
 * 5. 支持基于优先级的预加载策略
 * 6. 批量预加载和组件注册功能
 * 
 * 使用场景：
 * - 减少初始加载时间，按需加载组件
 * - 优化用户体验，预加载可能使用的组件
 * - 智能分配加载资源，根据优先级加载
 * - 监控组件加载状态和性能
 */

import React, { lazy, ComponentType, LazyExoticComponent } from 'react';
import { advancedCodeSplitting } from './AdvancedCodeSplitting';

export type PreloadTrigger = 'hover' | 'visible' | 'near' | 'manual';

export interface LazyComponentOptions {
  /** 是否启用预加载 */
  enablePreload?: boolean;
  /** 预加载触发方式 */
  preloadTrigger?: PreloadTrigger;
  /** 预加载距离（仅当preloadTrigger为'near'时生效） */
  preloadDistance?: number;
  /** 自定义加载组件 */
  fallback?: React.ReactNode;
  /** 模块加载资源优先级 */
  priority?: 'high' | 'medium' | 'low';
  /** 组件名称（用于调试和监控） */
  name?: string;
}

export interface LazyLoadedComponent<T extends ComponentType<any>> {
  /** 懒加载的组件 */
  Component: LazyExoticComponent<T>;
  /** 手动预加载组件 */
  preload: () => Promise<void>;
  /** 组件加载状态 */
  isLoaded: boolean;
  /** 检查组件是否正在加载 */
  isLoading: boolean;
}

/**
 * 默认懒加载选项
 */
const defaultOptions: LazyComponentOptions = {
  enablePreload: true,
  preloadTrigger: 'hover',
  preloadDistance: 300,
  priority: 'medium',
  fallback: null
};

/**
 * 获取懒加载组件
 * @param importFn 组件导入函数
 * @param options 懒加载选项
 */
export function createLazyComponent<T extends ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  options?: LazyComponentOptions
): LazyLoadedComponent<T> {
  const mergedOptions = { ...defaultOptions, ...options };
  let isLoaded = false;
  let isLoading = false;
  
  // 创建预加载函数
  const preloadFn = async (): Promise<void> => {
    if (isLoaded || isLoading) return;
    
    isLoading = true;
    try {
      // 执行实际的导入
      await importFn();
      isLoaded = true;
    } catch (error) {
      console.error('组件预加载失败:', error);
    } finally {
      isLoading = false;
    }
  };
  
  // 创建懒加载组件
  const Component = lazy(async () => {
    isLoading = true;
    try {
      const module = await importFn();
      isLoaded = true;
      return module;
    } finally {
      isLoading = false;
    }
  });
  
  // 如果设置了名称，将此组件路径注册到代码分割系统
  if (mergedOptions.name && mergedOptions.enablePreload) {
    // 注册到代码分割系统
    advancedCodeSplitting.registerComponent(
      mergedOptions.name,
      preloadFn,
      mergedOptions.priority === 'high' ? 10 : 
      mergedOptions.priority === 'medium' ? 5 : 1
    );
  }
  
  return {
    Component,
    preload: preloadFn,
    get isLoaded() { return isLoaded; },
    get isLoading() { return isLoading; }
  };
}

/**
 * 懒加载组件包装器
 */
export interface LazyComponentWrapperProps {
  /** 要懒加载的组件导入函数 */
  importFn: () => Promise<{ default: ComponentType<any> }>;
  /** 组件属性 */
  componentProps?: any;
  /** 懒加载选项 */
  options?: LazyComponentOptions;
  /** 子元素（用于触发预加载） */
  children?: React.ReactNode;
}

/**
 * 懒加载组件包装器
 * 用于自动处理组件的懒加载和预加载
 * 
 * 注意：实际应用时，应该在应用中直接使用Suspense和懒加载组件
 */
export function LazyComponentWrapper(props: LazyComponentWrapperProps) {
  const { importFn, componentProps = {}, options = {}, children } = props;
  const mergedOptions = { ...defaultOptions, ...options };
  const lazyComponent = createLazyComponent(importFn, options);
  const Component = lazyComponent.Component;
  const preload = lazyComponent.preload;
  
  // 根据预加载触发方式设置事件处理
  let preloadProps = {};
  if (mergedOptions.enablePreload) {
    switch (mergedOptions.preloadTrigger) {
      case 'hover':
        preloadProps = { onMouseEnter: preload };
        break;
      case 'visible':
        preloadProps = { onFocus: preload, onMouseEnter: preload };
        break;
      case 'manual':
        preloadProps = {};
        break;
      default:
        preloadProps = { onMouseEnter: preload };
    }
  }
  
  // 使用函数返回元素结构，避免JSX语法
  const fallbackElement = mergedOptions.fallback || React.createElement('div', null, '加载中...');
  const componentElement = React.createElement(Component, componentProps);
  const suspenseElement = React.createElement(React.Suspense, { fallback: fallbackElement }, componentElement);
  
  return React.createElement('div', preloadProps, suspenseElement, children);
}

/**
 * 预加载组件集合
 * @param components 组件集合
 */
export async function preloadComponents(
  components: Array<LazyLoadedComponent<any>>
): Promise<void> {
  await Promise.all(components.map(component => component.preload()));
}

/**
 * 批量预加载组件
 * @param importFns 组件导入函数数组
 */
export async function preloadBatch(
  importFns: Array<() => Promise<{ default: ComponentType<any> }>>
): Promise<void> {
  await Promise.all(importFns.map(fn => fn()));
}

/**
 * 懒加载组件管理器
 */
class LazyComponentManager {
  private componentCache: Map<string, LazyLoadedComponent<any>> = new Map();
  
  /**
   * 注册懒加载组件
   * @param key 组件键名
   * @param importFn 组件导入函数
   * @param options 懒加载选项
   */
  register<T extends ComponentType<any>>(
    key: string,
    importFn: () => Promise<{ default: T }>,
    options?: LazyComponentOptions
  ): LazyLoadedComponent<T> {
    if (this.componentCache.has(key)) {
      return this.componentCache.get(key) as LazyLoadedComponent<T>;
    }
    
    const component = createLazyComponent(importFn, {
      ...options,
      name: key
    });
    
    this.componentCache.set(key, component);
    return component;
  }
  
  /**
   * 获取注册的懒加载组件
   * @param key 组件键名
   */
  get<T extends ComponentType<any>>(key: string): LazyLoadedComponent<T> | undefined {
    return this.componentCache.get(key) as LazyLoadedComponent<T> | undefined;
  }
  
  /**
   * 预加载指定的组件
   * @param keys 组件键名数组
   */
  async preloadByKeys(keys: string[]): Promise<void> {
    const components = keys
      .map(key => this.componentCache.get(key))
      .filter(Boolean) as LazyLoadedComponent<any>[];
    
    await preloadComponents(components);
  }
  
  /**
   * 获取所有已注册的组件键
   */
  getAllKeys(): string[] {
    return Array.from(this.componentCache.keys());
  }
  
  /**
   * 获取所有已加载的组件键
   */
  getLoadedKeys(): string[] {
    return Array.from(this.componentCache.entries())
      .filter(([_, component]) => component.isLoaded)
      .map(([key]) => key);
  }
  
  /**
   * 清除组件缓存
   */
  clearCache(): void {
    this.componentCache.clear();
  }
}

// 导出单例实例
export const lazyComponentManager = new LazyComponentManager();

// 默认导出
export default lazyComponentManager;