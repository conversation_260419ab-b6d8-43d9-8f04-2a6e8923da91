import React, { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Calendar, Clock, Users, MapPin, Settings, BarChart3 } from 'lucide-react'
import { useNavigate } from 'react-router-dom'
import { PageTitle } from '@/components/common/PageTitle';

export default function BookingManagementPage() {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('overview');

  const bookingStats = {
    todayBookings: 12,
    pendingApprovals: 3,
    totalThisWeek: 45,
    avgWaitTime: '15 分钟'
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <PageTitle
        title="预约系统管理"
        description="管理所有预约相关设置和数据"
        actions={
          <Button variant="outline" onClick={() => navigate('/booking/settings')}>
            <Settings className="mr-2 h-4 w-4" />
            系统设置
          </Button>
        };
      />

      {/* 统计卡片 */}
      <div className="grid gap-4 md: grid-cols-2 l,g:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">今日预约</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{bookingStats.todayBookings}</div>
            <p className="text-xs text-muted-foreground">比昨天增加 2 个</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">待审核</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{bookingStats.pendingApprovals}</div>
            <p className="text-xs text-muted-foreground">需要处理</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">本周总数</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{bookingStats.totalThisWeek}</div>
            <p className="text-xs text-muted-foreground">比上周增加 12%</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">平均等待时间</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{bookingStats.avgWaitTime}</div>
            <p className="text-xs text-muted-foreground">优于行业平均</p>
          </CardContent>
        </Card>
      </div>
      {/* 快速导航 */}
      <div className="grid gap-4 md: grid-cols-2 lg:grid-cols-3">
        <Card className="cursor-pointer hove,r:shadow-md transition-shadow" 
              onClick={() => navigate('/booking/walkin')}>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              现场排队预约
            </CardTitle>
            <CardDescription>
              管理现场排队和即时预约服务
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => navigate('/booking/onsite')}>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              上门服务预约
            </CardTitle>
            <CardDescription>
              安排和管理上门服务预约
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => navigate('/booking/service-area')}>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              服务区域设置
            </CardTitle>
            <CardDescription>
              配置可提供服务的地理区域
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => navigate('/booking/service-types')}>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              服务类型管理
            </CardTitle>
            <CardDescription>
              配置不同类型的服务项目
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => navigate('/booking/schedule')}>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              时间表管理
            </CardTitle>
            <CardDescription>
              设置营业时间和可预约时段
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => setActiveTab('analytics')}>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              数据分析
            </CardTitle>
            <CardDescription>
              查看预约系统的详细数据分析
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
      {/* 主要内容 */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="overview">概览</TabsTrigger>
          <TabsTrigger value="recent">最近预约</TabsTrigger>
          <TabsTrigger value="analytics">数据分析</TabsTrigger>
        </TabsList>
        <TabsContent value="overview" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>系统概览</CardTitle>
              <CardDescription>预约系统的整体运行状况</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-6 md: grid-cols-2 lg:grid-cols-3">
                <div className="space-y-2">
                  <h4 className="font-medium">现场排队服务</h4>
                  <div className="text-sm text-muted-foreground space-y-1">
                    <div className="flex justify-between">
                      <span>今日排队</span>
                      <span className="font-medium">28人</span>
                    </div>
                    <div className="flex justify-between">
                      <span>平均等待</span>
                      <span className="font-medium">12分钟</span>
                    </div>
                    <div className="flex justify-between">
                      <span>完成率</span>
                      <span className="font-medium text-green-600">96.4%</span>
                    </div>
                  </div>
                </div>
                <div className="space-y-2">
                  <h4 className="font-medium">上门服务</h4>
                  <div className="text-sm text-muted-foreground space-y-1">
                    <div className="flex justify-between">
                      <span>今日预约</span>
                      <span className="font-medium">8单</span>
                    </div>
                    <div className="flex justify-between">
                      <span>进行中</span>
                      <span className="font-medium">3单</span>
                    </div>
                    <div className="flex justify-between">
                      <span>准时率</span>
                      <span className="font-medium text-green-600">92.1%</span>
                    </div>
                  </div>
                </div>
                <div className="space-y-2">
                  <h4 className="font-medium">系统状态</h4>
                  <div className="text-sm text-muted-foreground space-y-1">
                    <div className="flex justify-between">
                      <span>API响应</span>
                      <span className="font-medium text-green-600">正常</span>
                    </div>
                    <div className="flex justify-between">
                      <span>数据库</span>
                      <span className="font-medium text-green-600">正常</span>
                    </div>
                    <div className="flex justify-between">
                      <span>通知服务</span>
                      <span className="font-medium text-orange-600">轻微延迟</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="recent" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>最近预约</CardTitle>
              <CardDescription>最新的预约记录和状态</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="space-y-1">
                    <div className="font-medium">张先生 - 家电维修</div>
                    <div className="text-sm text-muted-foreground">上门服务 • 今日 14:30</div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium text-green-600">进行中</div>
                    <div className="text-xs text-muted-foreground">预计1小时完成</div>
                  </div>
                </div>
                <div className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="space-y-1">
                    <div className="font-medium">李女士 - 技术咨询</div>
                    <div className="text-sm text-muted-foreground">现场服务 • 排队号A003</div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium text-blue-600">等待中</div>
                    <div className="text-xs text-muted-foreground">前面还有2人</div>
                  </div>
                </div>
                <div className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="space-y-1">
                    <div className="font-medium">王先生 - 产品演示</div>
                    <div className="text-sm text-muted-foreground">现场服务 • 今日 1,3:45</div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium text-gray-600">已完成</div>
                    <div className="text-xs text-muted-foreground">满意度: 5星</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="analytics" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>数据分析</CardTitle>
              <CardDescription>
                预订数据分析功能已移至数据洞察页面，请前往查看详细分析报告。
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <p className="text-muted-foreground mb-4">
                  完整的预订数据分析功能现在位于数据洞察页面
                </p>
                <Button onClick={() => window.location.href = '/data-insight'}>
                  前往数据洞察
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};