// 🧭 Navigation routes for iBuddy2 app
export const routes = {
  // Main pages
  home: '/',
  pricing: '/pricing',
  dashboard: '/dashboard',
  
  // Feature-specific pages
  addonPackages: '/addon-packages',
  usageSettings: '/settings/usage',
  billingSettings: '/settings/billing',
  notifications: '/settings/notifications',
  
  // Quick actions from pricing section
  buyAddon: '/addon-packages',
  setupOverage: '/settings/usage#overage',
  manageUsage: '/settings/usage',
  
  // External links
  contactSales: '/contact/sales',
  scheduleDemo: '/contact/demo',
  support: '/support'
};

// 📱 Navigation function factory
export const createNavigationHandler = (router?: any) => {
  return (route: keyof typeof routes) => {
    const path = routes[route];
    
    if (router) {
      // If using Next.js router
      router.push(path);
    } else {
      // Fallback for development/testing
      console.log(`Navigate to: ${path}`);
      
      // In a real app, you might use window.location or history API
      if (typeof window !== 'undefined') {
        window.location.hash = path;
      }
    }
  };
};

// 🎯 Feature mapping for pricing section navigation
export const pricingFeatureMap = {
  'buy-addon': 'addonPackages',
  'setup-overage': 'setupOverage',
  'manage-usage': 'usageSettings',
  'billing-settings': 'billingSettings',
  'contact-sales': 'contactSales',
  'schedule-demo': 'scheduleDemo'
} as const;

// 💼 Plan-specific feature availability
export const planFeatures = {
  starter: {
    allowedPlatforms: 3,
    hasWhatsApp: false,
    hasAdvancedAnalytics: false,
    hasCustomWorkflows: false
  },
  professional: {
    allowedPlatforms: 6,
    hasWhatsApp: true,
    hasAdvancedAnalytics: true,
    hasCustomWorkflows: true
  },
  enterprise: {
    allowedPlatforms: Infinity,
    hasWhatsApp: true,
    hasAdvancedAnalytics: true,
    hasCustomWorkflows: true,
    hasCustomIntegrations: true,
    hasDedicatedSupport: true
  }
};

// 🔗 Deep link utilities
export const createDeepLink = (feature: string, params?: Record<string, string>) => {
  const baseRoute = routes[feature as keyof typeof routes] || '/';
  
  if (!params) return baseRoute;
  
  const searchParams = new URLSearchParams(params);
  return `${baseRoute}?${searchParams.toString()}`;
};

// 📊 Usage-based navigation suggestions
export const getUsageBasedSuggestions = (currentUsage: number, planLimit: number) => {
  const usagePercentage = (currentUsage / planLimit) * 100;
  
  if (usagePercentage >= 90) {
    return {
      priority: 'urgent',
      suggestions: [
        { title: 'Buy Add-on Package', route: 'addonPackages', description: 'Avoid overage charges' },
        { title: 'Enable Auto-billing', route: 'setupOverage', description: 'Seamless overage handling' }
      ]
    };
  } else if (usagePercentage >= 70) {
    return {
      priority: 'warning',
      suggestions: [
        { title: 'Monitor Usage', route: 'usageSettings', description: 'Set up alerts' },
        { title: 'Browse Add-ons', route: 'addonPackages', description: 'Prepare for scale' }
      ]
    };
  } else {
    return {
      priority: 'normal',
      suggestions: [
        { title: 'Usage Analytics', route: 'usageSettings', description: 'Track your growth' },
        { title: 'Upgrade Plan', route: 'pricing', description: 'More features available' }
      ]
    };
  }
};

export default routes;