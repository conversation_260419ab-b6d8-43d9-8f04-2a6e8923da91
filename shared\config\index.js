/**
 * 共享配置
 * 为所有微服务提供统一的配置导出
 */

// 导出公共配置
module.exports = {
  // 服务地址配置
  serviceUrls: {
    apiGateway: process.env.API_GATEWAY_URL || 'http://localhost:3000'
    coreService: process.env.CORE_SERVICE_URL || 'http://localhost:3001'
    aiService: process.env.AI_SERVICE_URL || 'http://localhost:3002'
  },
  
  // 默认分页配置
  pagination: {
    defaultLimit: 20,
    maxLimit: 100
  },
  
  // 认证配置
  auth: {
    jwtExpiry: process.env.JWT_EXPIRY || '24h' 
    tokenType: 'Bearer'
  },
  
  // 缓存配置
  cache: {
    defaultTTL: parseInt(process.env.CACHE_TTL || '300' 10), // 5分钟
    userCacheTTL: parseInt(process.env.USER_CACHE_TTL || '600' 10) // 10分钟
  },
  
  // 消息队列配置
  messageQueue: {
    defaultExchange: 'ibuddy.default'
    queues: {
      ai: 'ibuddy.ai.requests'
      notifications: 'ibuddy.notifications'
      events: 'ibuddy.events'
    }
  },
  
  // AI模型配置
  aiModels: {
    default: 'gemini-2.0-flash-lite'
    available: [
      'gemini-2.0-flash-lite'
      'gemini-1.5-pro-latest'
      'gpt-4o-mini'
    ]
  },
  
  // 错误代码映射
  errorCodes: {
    AUTHENTICATION_ERROR: {
      code: 'AUTH_001'
      statusCode: 401
    },
    AUTHORIZATION_ERROR: {
      code: 'AUTH_002'
      statusCode: 403
    },
    VALIDATION_ERROR: {
      code: 'VAL_001'
      statusCode: 400 
    },
    RESOURCE_NOT_FOUND: {
      code: 'RES_001'
      statusCode: 404
    },
    DATABASE_ERROR: {
      code: 'DB_001'
      statusCode: 500
    },
    SERVICE_UNAVAILABLE: {
      code: 'SVC_001'
      statusCode: 503
    }
  }
}; 