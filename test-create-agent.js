const axios = require('axios');

console.log('🧪 测试创建Agent功能...');

const testAgentData = {
  name: 'Test Agent Quick Create',
  agentType: 'AI_AUTO_REPLY',
  description: 'Test agent created via Quick Create functionality'
};

console.log('📝 准备创建Agent:', testAgentData);

// 测试直接连接Core Service
axios.post('http://localhost:3002/api/agents', testAgentData, {
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})
.then(response => {
  console.log('✅ Core Service创建Agent成功:');
  console.log('状态码:', response.status);
  console.log('响应数据:', response.data);
  console.log('创建的Agent ID:', response.data.id);
  console.log('Agent状态:', response.data.status);
})
.catch(error => {
  console.error('❌ Core Service创建Agent失败:');
  if (error.response) {
    console.error('状态码:', error.response.status);
    console.error('错误数据:', error.response.data);
  } else if (error.request) {
    console.error('请求错误:', error.request);
  } else {
    console.error('配置错误:', error.message);
  }
  console.error('完整错误:', error.message);
}); 