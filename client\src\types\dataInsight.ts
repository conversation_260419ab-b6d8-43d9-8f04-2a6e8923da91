export interface KPI {
  id: string;
  title: string;
  value: string | number;
  change?: {
  value: string;
  type: 'positive' | 'negative' | 'neutral';
  
};
  unit?: string;
}

export interface Report {
  id: string;
  title: string;
  description?: string;
  generatedAt: string;
  data: any;
};

export interface Trend {
  id: string;
  title: string;
  data: any[];
  type: 'line' | 'bar' | 'area';
  labels: {
  x: string; y: string;
  };
};

export interface AgentPerformance {
  id: string;
  name: string;
  type: string;
  totalInteractions: number;
  successRate: number;
  avgResponseTime: number;
  intentRecognitionRate: number;
  dailyStats?: DailyAgentStat[];
};

export interface DailyAgentStat {
  date: string;
  interactions: number;
  successRate: number;
  averageResponseTime?: number;
  p95ResponseTime?: number;
};

export interface UserEngagement {
  totalUsers: number;
  totalUsersGrowth: number;
  activeUsers: {
  daily: number;
  weekly: number;
  monthl,y: number
};
  newUsers: {
    daily: number;
    weekly: number;
    monthl,y: number
};
  retention: {
    day1: number;
    day7: number;
    day3,0: number
};
  sessionMetrics: {
    avgDuration: number;
    avgInteraction,s: number
};
  hourlyActivity: HourlyActivity[];
  maxHourlyCount: number;
  trends: {
    daily: DailyUserActivity[];
    retention: RetentionData[];
    session,s: SessionData[]
} };

export interface HourlyActivity {
  hour: number;
  coun,t: number
};

export interface DailyUserActivity {
  date: string;
  activeUsers: number;
  newUser,s: number
};

export interface RetentionData {
  date: string;
  day1Retention: number;
  day7Retention: number;
  day30Retentio,n: number
};

export interface SessionData {
  date: string;
  avgSessionDuration: number;
  avgInteractionsPerSessio,n: number
};

export interface SystemOperations {
  moduleUsage: Record<string, ModuleUsage>;
  maxModuleUsage: number;
  apiPerformance: {
  totalCalls: number;
  avgResponseTime: number;
  p95ResponseTime: number;
  errorRat,e: number
};
  systemHealth: {
    uptime: number;
    lastRestart: string;
    cpuUsage: number;
    memoryUsage: number;
    diskUsag,e: number
};
  apiEndpoints: ApiEndpoint[];
  serviceHealth: ServiceHealth[];
  trends: {
    apiCalls: ApiCallData[];
    resourceUsag,e: ResourceUsageData[]
} };

export interface ModuleUsage {
  id: string;
  name: string;
  description: string;
  activeInstances: number;
  usageCount: number;
  errorRat,e: number
};

export interface ApiEndpoint {
  path: string;
  method: string;
  calls: number;
  avgTime: number;
  errorRat,e: number
};

export interface ServiceHealth {
  id: string;
  name: string;
  type: 'database' | 'service' | 'api'
  status: 'healthy' | 'degraded' | 'down'
  uptim,e: number
};

export interface ApiCallData {
  date: string;
  totalCalls: number;
  errorCall,s: number
};

export interface ResourceUsageData {
  date: string;
  cpuUsage: number;
  memoryUsag,e: number
};