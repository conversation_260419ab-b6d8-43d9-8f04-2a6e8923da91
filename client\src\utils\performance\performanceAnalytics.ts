/**
 * 性能分析系统
 * 收集和分析Web应用的各种性能指标
 */

// 性能指标类型
export interface PerformanceMetrics {
  /** 首次内容绘制 */
  fcp?: number;
  /** 最大内容绘制 */
  lcp?: number;
  /** 首次输入延迟 */
  fid?: number;
  /** 累积布局偏移 */
  cls?: number;
  /** 首次交互时间 */
  tti?: number;
  /** 总阻塞时间 */
  tbt?: number;
  /** 速度指数 */
  si?: number;
}

// 导航性能指标
export interface NavigationMetrics {
  /** DNS解析时间 */
  dnsLookup: number;
  /** TCP连接时间 */
  tcpConnect: number;
  /** 请求响应时间 */
  request: number;
  /** 响应时间 */
  response: number;
  /** DOM加载时间 */
  domLoad: number;
  /** 页面完全加载时间 */
  pageLoad: number;
}

// 资源性能指标
export interface ResourceMetrics {
  /** 资源URL */
  url: string;
  /** 资源类型 */
  type: string;
  /** 资源大小 */
  size: number;
  /** 加载时间 */
  duration: number;
  /** 传输大小 */
  transferSize: number;
  /** 编码大小 */
  encodedBodySize: number;
  /** 解码大小 */
  decodedBodySize: number;
}

// 用户体验指标
export interface UserExperienceMetrics {
  /** 页面路径 */
  path: string;
  /** 停留时间 */
  timeOnPage: number;
  /** 滚动深度 */
  scrollDepth: number;
  /** 点击次数 */
  clicks: number;
  /** 交互次数 */
  interactions: number;
  /** 错误次数 */
  errors: number;
}

// 性能报告
export interface PerformanceReport {
  /** 时间戳 */
  timestamp: number;
  /** 页面URL */
  url: string;
  /** 用户代理 */
  userAgent: string;
  /** 连接类型 */
  connectionType: string;
  /** 核心Web指标 */
  webVitals: PerformanceMetrics;
  /** 导航指标 */
  navigation: NavigationMetrics;
  /** 资源指标 */
  resources: ResourceMetrics[];
  /** 用户体验指标 */
  userExperience: UserExperienceMetrics;
}

/**
 * 性能分析器类
 */
export class PerformanceAnalyzer {
  private observers: Map<string, PerformanceObserver> = new Map();
  private metrics: Partial<PerformanceMetrics> = {};
  private userExperience: Partial<UserExperienceMetrics> = {};
  private startTime: number = Date.now();
  private isCollecting: boolean = false;
  private isInitialized: boolean = false;
  
  /**
   * 初始化性能分析器
   */
  public initialize(): void {
    if (this.isInitialized) return;
    
    console.log('[性能分析器] 初始化性能分析器');
    this.startCollection();
    this.isInitialized = true;
  }
  
  /**
   * 开始收集性能数据
   */
  public startCollection(): void {
    if (this.isCollecting) return;
    
    this.isCollecting = true;
    this.startTime = Date.now();
    
    // 收集Web Vitals
    this.collectWebVitals();
    
    // 收集导航性能
    this.collectNavigationMetrics();
    
    // 收集资源性能
    this.collectResourceMetrics();
    
    // 收集用户体验数据
    this.collectUserExperienceMetrics();
    
    console.log('[性能分析器] 开始收集性能数据');
  }
  
  /**
   * 停止收集性能数据
   */
  public stopCollection(): void {
    if (!this.isCollecting) return;
    
    this.isCollecting = false;
    
    // 断开所有观察者
    this.observers.forEach(observer => observer.disconnect());
    this.observers.clear();
    
    console.log('[性能分析器] 停止收集性能数据');
  }
  
  /**
   * 收集核心Web指标
   */
  private collectWebVitals(): void {
    // 收集FCP (First Contentful Paint)
    this.observePerformance('paint', (entries) => {
      entries.forEach((entry: PerformanceEntry) => {
        if (entry.name === 'first-contentful-paint') {
          this.metrics.fcp = entry.startTime;
        }
      });
    });
    
    // 收集LCP (Largest Contentful Paint)
    this.observePerformance('largest-contentful-paint', (entries) => {
      entries.forEach((entry: any) => {
        this.metrics.lcp = entry.startTime;
      });
    });
    
    // 收集FID (First Input Delay)
    this.observePerformance('first-input', (entries) => {
      entries.forEach((entry: any) => {
        this.metrics.fid = entry.processingStart - entry.startTime;
      });
    });
    
    // 收集CLS (Cumulative Layout Shift)
    this.observePerformance('layout-shift', (entries) => {
      let clsValue = 0;
      entries.forEach((entry: any) => {
        if (!entry.hadRecentInput) {
          clsValue += entry.value;
        }
      });
      this.metrics.cls = clsValue;
    });
  }
  
  /**
   * 收集导航性能指标
   */
  private collectNavigationMetrics(): NavigationMetrics | null {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    
    if (!navigation) return null;
    
    return {
      dnsLookup: navigation.domainLookupEnd - navigation.domainLookupStart,
      tcpConnect: navigation.connectEnd - navigation.connectStart,
      request: navigation.responseStart - navigation.requestStart,
      response: navigation.responseEnd - navigation.responseStart,
      domLoad: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
      pageLoad: navigation.loadEventEnd - navigation.loadEventStart
    };
  }
  
  /**
   * 收集资源性能指标
   */
  private collectResourceMetrics(): ResourceMetrics[] {
    const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
    
    return resources.map(resource => ({
      url: resource.name,
      type: this.getResourceType(resource.name),
      size: resource.transferSize || 0,
      duration: resource.duration,
      transferSize: resource.transferSize || 0,
      encodedBodySize: resource.encodedBodySize || 0,
      decodedBodySize: resource.decodedBodySize || 0
    }));
  }
  
  /**
   * 收集用户体验指标
   */
  private collectUserExperienceMetrics(): void {
    let scrollDepth = 0;
    let clicks = 0;
    let interactions = 0;
    let errors = 0;
    
    const handleScroll = () => {
      const scrolled = (window.scrollY + window.innerHeight) / document.body.scrollHeight;
      scrollDepth = Math.max(scrollDepth, Math.round(scrolled * 100));
    };
    
    const handleClick = () => {
      clicks++;
    };
    
    const handleInteraction = () => {
      interactions++;
    };
    
    const handleError = () => {
      errors++;
    };
    
    // 添加事件监听器
    window.addEventListener('scroll', handleScroll, { passive: true });
    window.addEventListener('click', handleClick);
    window.addEventListener('keydown', handleInteraction);
    window.addEventListener('touchstart', handleInteraction);
    window.addEventListener('error', handleError);
    
    // 清理函数
    const cleanup = () => {
      window.removeEventListener('scroll', handleScroll);
      window.removeEventListener('click', handleClick);
      window.removeEventListener('keydown', handleInteraction);
      window.removeEventListener('touchstart', handleInteraction);
      window.removeEventListener('error', handleError);
    };
    
    // 页面卸载时更新指标
    window.addEventListener('beforeunload', () => {
      this.userExperience = {
        path: window.location.pathname,
        timeOnPage: Date.now() - this.startTime,
        scrollDepth,
        clicks,
        interactions,
        errors
      };
      cleanup();
    });
  }
  
  /**
   * 观察性能条目
   */
  private observePerformance(type: string, callback: (entries: PerformanceEntry[]) => void): void {
    try {
      const observer = new PerformanceObserver((list) => {
        callback(list.getEntries());
      });
      
      observer.observe({ type, buffered: true });
      this.observers.set(type, observer);
    } catch (error) {
      console.warn(`[性能分析器] 无法观察性能类型: ${type}`, error);
    }
  }
  
  /**
   * 获取资源类型
   */
  private getResourceType(url: string): string {
    const extension = url.split('.').pop()?.toLowerCase();
    
    if (['js', 'mjs'].includes(extension || '')) return 'script';
    if (['css'].includes(extension || '')) return 'stylesheet';
    if (['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'].includes(extension || '')) return 'image';
    if (['woff', 'woff2', 'ttf', 'otf', 'eot'].includes(extension || '')) return 'font';
    if (['html', 'htm'].includes(extension || '')) return 'document';
    
    return 'other';
  }
  
  /**
   * 生成性能报告
   */
  public generateReport(): PerformanceReport {
    const navigation = this.collectNavigationMetrics();
    const resources = this.collectResourceMetrics();
    
    return {
      timestamp: Date.now(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      connectionType: this.getConnectionType(),
      webVitals: this.metrics as PerformanceMetrics,
      navigation: navigation || {} as NavigationMetrics,
      resources,
      userExperience: this.userExperience as UserExperienceMetrics
    };
  }
  
  /**
   * 获取连接类型
   */
  private getConnectionType(): string {
    const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection;
    return connection?.effectiveType || 'unknown';
  }
  
  /**
   * 发送性能报告
   */
  public async sendReport(endpoint: string = '/api/analytics/performance'): Promise<void> {
    try {
      const report = this.generateReport();
      
      await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(report)
      });
      
      console.log('[性能分析器] 性能报告已发送');
    } catch (error) {
      console.error('[性能分析器] 发送性能报告失败:', error);
    }
  }
  
  /**
   * 获取当前指标
   */
  public getMetrics(): Partial<PerformanceMetrics> {
    return { ...this.metrics };
  }
  
  /**
   * 获取用户体验指标
   */
  public getUserExperience(): Partial<UserExperienceMetrics> {
    return { ...this.userExperience };
  }
  
  /**
   * 重置所有指标
   */
  public reset(): void {
    this.metrics = {};
    this.userExperience = {};
    this.startTime = Date.now();
  }
  
  /**
   * 销毁分析器
   */
  public destroy(): void {
    this.stopCollection();
    this.reset();
    this.isInitialized = false;
  }
}

// 导出单例
export const performanceAnalyzer = new PerformanceAnalyzer();
export default performanceAnalyzer;