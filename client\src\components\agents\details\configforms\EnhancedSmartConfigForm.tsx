import React, { useState, useEffect, useMemo, useCallback } from 'react'
import { useTranslation } from 'react-i18next'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Slider } from '@/components/ui/slider'
import { 
  Lightbulb, 
  Settings, 
  Zap, 
  Brain, 
  MessageSquare, 
  CheckCircle,
  ArrowRight,
  HelpCircle,
  Sparkles,
  Eye,
  Loader2
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { debounce } from 'lodash'

interface EnhancedSmartConfigFormProps {
  config: Record<string, any>;
  onConfigChange: (key: string, value: any) => void;
  className?: string;
}

// 业务场景模板
const BUSINESS_SCENARIOS = [
  {
    id: 'customer_service', icon: MessageSquare, color: 'bg-blue-500', defaults: {
      systemPrompt: 'You are a professional customer service representative. Please maintain a polite, professional, and concise tone.',
      responseMode: 'direct', temperature: 0.3,
      maxTokens: 200,
      humanEscalationThreshold: 0.8,
      responseStyle: 'professional'
    }
  },
  {
    id: 'sales_support', icon: Zap, color: 'bg-green-500', defaults: {
      systemPrompt: 'You are a friendly sales consultant focused on understanding customer needs and providing suitable product recommendations.',
      responseMode: 'direct', temperature: 0.5,
      maxTokens: 300,
      humanEscalationThreshold: 0.6,
      responseStyle: 'friendly'
    }
  },
  {
    id: 'technical_support', icon: Settings, color: 'bg-purple-500', defaults: {
      systemPrompt: 'You are a technical support expert providing accurate technical solutions and detailed operational guidance.',
      responseMode: 'collaborative', temperature: 0.2,
      maxTokens: 400,
      humanEscalationThreshold: 0.9,
      responseStyle: 'professional'
    }
  }
];

// 设置步骤定义
const SETUP_STEPS = [
  { id: 'scenario', icon: Lightbulb },
  { id: 'basic', icon: Settings },
  { id: 'behavior', icon: Brain },
  { id: 'advanced', icon: Sparkles }
];

const EnhancedSmartConfigForm: React.FC<EnhancedSmartConfigFormProps> = ({
  config,
  onConfigChange,
  className
}) => {
  const { t } = useTranslation();
  const [currentStep, setCurrentStep] = useState(0);
  const [selectedScenario, setSelectedScenario] = useState<string | null>(null);
  const [isSmartMode, setIsSmartMode] = useState(true);
  const [setupProgress, setSetupProgress] = useState(0);
  const [previewResponse, setPreviewResponse] = useState('');
  const [isPreviewLoading, setIsPreviewLoading] = useState(false);

  // 计算设置完成度
  useEffect(() => {
    const requiredFields = ['systemPrompt', 'responseStyle', 'temperature'];
    const completedFields = requiredFields.filter(field => config[field] !== undefined && config[field] !== '');
    setSetupProgress((completedFields.length / requiredFields.length) * 100);
  }, [config]);

  // 应用场景模板
  const applyScenarioTemplate = useCallback((scenarioId: string) => {
    const scenario = BUSINESS_SCENARIOS.find(s => s.id === scenarioId);
    if (scenario) {
      Object.entries(scenario.defaults).forEach(([key, value]) => {
        onConfigChange(key, value);
      });
      setSelectedScenario(scenarioId);
      setCurrentStep(1);
    }
  }, [onConfigChange]);

  // 智能建议系统
  const getSmartSuggestions = useMemo(() => {
    const suggestions = [];
    
    if (!config.systemPrompt) {
      suggestions.push({
        type: 'warning' as const, message: t('agents.smartConfig.suggestions.systemPromptMissing')
      });
    }
    
    if (config.temperature > 0.8) {
      suggestions.push({
        type: 'info' as const, message: t('agents.smartConfig.suggestions.highCreativity')
      });
    }
    
    return suggestions;
  }, [config, t]);

  // 实时预览功能（模拟）
  const debouncedPreview = useMemo(
    () => debounce(async (message: string) => {
      if (!message.trim()) return;
      
      setIsPreviewLoading(true);
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 基于配置生成模拟回复
        const style = config.responseStyle || 'professional';
        const creativity = config.temperature || 0.5;
        
        let response = '';
        if (style === 'professional') {
          response = `Thank you for your inquiry. I understand you're asking about "${message}". Let me provide you with accurate information to assist you.`;
        } else if (style === 'friendly') {
          response = `Hi there! Thanks for reaching out about "${message}". I'd be happy to help you with that! 😊`;
        } else {
          response = `Hey! Got your message about "${message}". Let me help you out with that.`;
        }
        
        if (creativity > 0.7) {
          response += ' Is there anything specific you\'d like to know more about?';
        }
        
        setPreviewResponse(response);
      } finally {
        setIsPreviewLoading(false);
      }
    }, 500),
    [config]
  );

  const handleNext = useCallback(() => {
    if (currentStep < SETUP_STEPS.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  }, [currentStep]);

  const handlePrevious = useCallback(() => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  }, [currentStep]);

  return (
    <div className={cn("space-y-6", className)}>
      {/* 智能模式切换 */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <Brain className="h-5 w-5 text-primary" />
            {t('agents.smartConfig.title')}
          </h3>
          <p className="text-sm text-muted-foreground">
            {isSmartMode ? t('agents.smartConfig.subtitle') : t('agents.smartConfig.expertMode')}
          </p>
        </div>
        <Button
          variant="outline"
          onClick={() => setIsSmartMode(!isSmartMode)}
          className="flex items-center gap-2"
        >
          {isSmartMode ? <Settings className="h-4 w-4" /> : <Sparkles className="h-4 w-4" />}
          {isSmartMode ? t('agents.smartConfig.switchToExpert') : t('agents.smartConfig.switchToSmart')}
        </Button>
      </div>

      {/* 进度指示器 */}
      {isSmartMode && (
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium">{t('agents.smartConfig.setupProgress')}</span>
              <span className="text-sm text-muted-foreground">{Math.round(setupProgress)}%</span>
            </div>
            <Progress value={setupProgress} className="h-2" />
          </CardContent>
        </Card>
      )}

      {isSmartMode ? (
        // 智能引导模式
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 主配置区域 */}
          <div className="lg:col-span-2">
            <Tabs value={SETUP_STEPS[currentStep]?.id} className="w-full">
              <TabsList className="grid w-full grid-cols-4">
                {SETUP_STEPS.map((step, index) => {
                  const Icon = step.icon;
                  return (
                    <TabsTrigger 
                      key={step.id} 
                      value={step.id}
                      disabled={index > currentStep}
                      className="flex items-center gap-2"
                    >
                      {index < currentStep && <CheckCircle className="h-4 w-4" />}
                      <Icon className="h-4 w-4" />
                      <span className="hidden sm:inline">{t(`agents.smartConfig.steps.${step.id}`)}</span>
                    </TabsTrigger>
                  )
                })}
              </TabsList>

              {/* 步骤1：场景选择 */}
              <TabsContent value="scenario" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Lightbulb className="h-5 w-5 text-yellow-500" />
                      {t('agents.smartConfig.scenarios.title')}
                    </CardTitle>
                    <CardDescription>
                      {t('agents.smartConfig.scenarios.subtitle')}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 gap-4">
                      {BUSINESS_SCENARIOS.map((scenario) => {
                        const Icon = scenario.icon;
                        return (
                          <Card 
                            key={scenario.id}
                            className={cn(
                              "cursor-pointer transition-all hover:shadow-md",
                              selectedScenario === scenario.id ? 'ring-2 ring-primary' : ''
                            )}
                            onClick={() => applyScenarioTemplate(scenario.id)}
                          >
                            <CardContent className="p-4">
                              <div className="flex items-center gap-3">
                                <div className={cn("p-2 rounded-lg text-white", scenario.color)}>
                                  <Icon className="h-4 w-4" />
                                </div>
                                <div>
                                  <h4 className="font-medium">{t(`agents.smartConfig.scenarios.${scenario.id}`)}</h4>
                                  <p className="text-sm text-muted-foreground">
                                    {t(`agents.smartConfig.scenarios.${scenario.id}Desc`)}
                                  </p>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        )
                      })}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* 步骤2：基础设置 */}
              <TabsContent value="basic" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>{t('agents.smartConfig.basic.title')}</CardTitle>
                    <CardDescription>{t('agents.smartConfig.basic.subtitle')}</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="agentName">{t('agents.smartConfig.basic.agentName')}</Label>
                      <Input
                        id="agentName"
                        value={config.agentName || ''}
                        onChange={(e) => onConfigChange('agentName', e.target.value)}
                        placeholder={t('agents.smartConfig.basic.agentNamePlaceholder')}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="systemPrompt">{t('agents.smartConfig.basic.roleDefinition')}</Label>
                      <Textarea
                        id="systemPrompt"
                        value={config.systemPrompt || ''}
                        onChange={(e) => onConfigChange('systemPrompt', e.target.value)}
                        placeholder={t('agents.smartConfig.basic.roleDefinitionPlaceholder')}
                        rows={3}
                      />
                      <p className="text-xs text-muted-foreground">
                        {t('agents.smartConfig.basic.roleDefinitionHelp')}
                      </p>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="responseStyle">{t('agents.smartConfig.basic.responseStyle')}</Label>
                      <Select
                        value={config.responseStyle || 'professional'}
                        onValueChange={(value) => onConfigChange('responseStyle', value)}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="professional">{t('agents.smartConfig.basic.responseStyleProfessional')}</SelectItem>
                          <SelectItem value="friendly">{t('agents.smartConfig.basic.responseStyleFriendly')}</SelectItem>
                          <SelectItem value="casual">{t('agents.smartConfig.basic.responseStyleCasual')}</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="flex justify-between">
                      <Button variant="outline" onClick={handlePrevious} disabled={currentStep === 0}>
                        {t('common.previous')}
                      </Button>
                      <Button onClick={handleNext} className="flex items-center gap-2">
                        {t('common.next')} <ArrowRight className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* 步骤3：行为调优 */}
              <TabsContent value="behavior" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>{t('agents.smartConfig.behavior.title')}</CardTitle>
                    <CardDescription>{t('agents.smartConfig.behavior.subtitle')}</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {/* 创意度控制 */}
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <Label htmlFor="temperature">{t('agents.smartConfig.behavior.creativity')}</Label>
                        <span className="text-sm font-medium">
                          {(config.temperature || 0.5).toFixed(1)}
                        </span>
                      </div>
                      <Slider
                        value={[config.temperature || 0.5]}
                        onValueChange={(value) => onConfigChange('temperature', value[0])}
                        max={1}
                        min={0}
                        step={0.1}
                        className="w-full"
                      />
                      <div className="flex justify-between text-xs text-muted-foreground">
                        <span>{t('agents.smartConfig.behavior.creativityPrecise')}</span>
                        <span>{t('agents.smartConfig.behavior.creativityCreative')}</span>
                      </div>
                      <p className="text-xs text-muted-foreground">
                        {t('agents.smartConfig.behavior.creativityHelp')}
                      </p>
                    </div>

                    {/* 回复长度 */}
                    <div className="space-y-2">
                      <Label htmlFor="responseLength">{t('agents.smartConfig.behavior.responseLength')}</Label>
                      <Select
                        value={config.maxTokens <= 150 ? 'short' : config.maxTokens <= 300 ? 'medium' : 'long'}
                        onValueChange={(value) => {
                          const tokenMap = { short: 150, medium: 300, long: 500 };
                          onConfigChange('maxTokens', tokenMap[value as keyof typeof tokenMap]);
                        }}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="short">{t('agents.smartConfig.behavior.responseLengthShort')}</SelectItem>
                          <SelectItem value="medium">{t('agents.smartConfig.behavior.responseLengthMedium')}</SelectItem>
                          <SelectItem value="long">{t('agents.smartConfig.behavior.responseLengthLong')}</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* 转人工门槛 */}
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <Label htmlFor="escalationThreshold">{t('agents.smartConfig.behavior.escalationThreshold')}</Label>
                        <span className="text-sm font-medium">
                          {(config.humanEscalationThreshold || 0.7).toFixed(1)}
                        </span>
                      </div>
                      <Slider
                        value={[config.humanEscalationThreshold || 0.7]}
                        onValueChange={(value) => onConfigChange('humanEscalationThreshold', value[0])}
                        max={1}
                        min={0}
                        step={0.1}
                        className="w-full"
                      />
                      <div className="flex justify-between text-xs text-muted-foreground">
                        <span>{t('agents.smartConfig.behavior.escalationNever')}</span>
                        <span>{t('agents.smartConfig.behavior.escalationAlways')}</span>
                      </div>
                      <p className="text-xs text-muted-foreground">
                        {t('agents.smartConfig.behavior.escalationHelp')}
                      </p>
                    </div>

                    <div className="flex justify-between">
                      <Button variant="outline" onClick={handlePrevious}>
                        {t('common.previous')}
                      </Button>
                      <Button onClick={handleNext} className="flex items-center gap-2">
                        {t('common.next')} <ArrowRight className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* 步骤4：高级选项 */}
              <TabsContent value="advanced" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Advanced Options</CardTitle>
                    <CardDescription>Optional advanced configuration for power users</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="text-center py-8">
                      <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
                      <h3 className="text-lg font-semibold mb-2">Configuration Complete!</h3>
                      <p className="text-muted-foreground mb-4">
                        Your AI assistant is ready to be deployed with the current settings.
                      </p>
                      <div className="flex justify-center gap-3">
                        <Button variant="outline" onClick={handlePrevious}>
                          {t('common.previous')}
                        </Button>
                        <Button className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4" />
                          {t('common.save')}
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>

          {/* 实时预览面板 */}
          <div className="lg:col-span-1">
            <Card className="sticky top-4">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Eye className="h-4 w-4" />
                  {t('agents.smartConfig.preview.title')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Input
                  placeholder={t('agents.smartConfig.preview.testMessage')}
                  onChange={(e) => debouncedPreview(e.target.value)}
                />
                <div className="min-h-[100px] p-4 bg-muted rounded-md">
                  {isPreviewLoading ? (
                    <div className="flex items-center gap-2">
                      <Loader2 className="h-4 w-4 animate-spin" />
                      {t('agents.smartConfig.preview.generating')}
                    </div>
                  ) : (
                    <p className="text-sm">{previewResponse || t('agents.smartConfig.preview.placeholder')}</p>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* 智能建议面板 */}
            {getSmartSuggestions.length > 0 && (
              <Card className="mt-4">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <HelpCircle className="h-5 w-5 text-blue-500" />
                    {t('agents.smartConfig.suggestions.title')}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {getSmartSuggestions.map((suggestion, index) => (
                      <div key={index} className="flex items-start gap-2">
                        <Badge variant={suggestion.type === 'warning' ? 'destructive' : 'default'}>
                          {t(`agents.smartConfig.suggestions.${suggestion.type}`)}
                        </Badge>
                        <p className="text-sm">{suggestion.message}</p>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      ) : (
        // 专家模式 - 显示所有配置选项
        <Card>
          <CardHeader>
            <CardTitle>{t('agents.smartConfig.expertMode')}</CardTitle>
            <CardDescription>Complete configuration options for advanced users</CardDescription>
          </CardHeader>
          <CardContent>
            {/* 这里可以集成原有的完整配置表单 */}
            <p className="text-muted-foreground">Expert mode configuration will be integrated here.</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default EnhancedSmartConfigForm;
