"use client"

import { useEffect, useMemo, useState } from "react"
import {
  Cloud,
  fetchSimpleIcons,
  renderSimpleIcon,
} from "react-icon-cloud"

const cloudProps = {
  containerProps: {
    style: {
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      width: "100%",
      paddingTop: 40,
    },
  },
  options: {
    reverse: true,
    depth: 1,
    wheelZoom: false,
    imageScale: 2,
    activeCursor: "default",
    tooltip: "native",
    initial: [0.1, -0.1],
    clickToFront: 500,
    tooltipDelay: 0,
    outlineColour: "#0000",
    maxSpeed: 0.04,
    minSpeed: 0.02,
  },
}

export function SimpleIconCloudTest() {
  const [data, setData] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  const iconSlugs = ["react", "javascript", "typescript", "nodejs", "github"]

  useEffect(() => {
    console.log('SimpleIconCloudTest: 开始获取图标数据')
    setLoading(true)
    setError(null)
    
    fetchSimpleIcons({ slugs: iconSlugs })
      .then((result) => {
        console.log('SimpleIconCloudTest: 图标数据获取成功:', result)
        setData(result)
        setLoading(false)
      })
      .catch((err) => {
        console.error('SimpleIconCloudTest: 图标数据获取失败:', err)
        setError(err.message)
        setLoading(false)
      })
  }, [])

  const renderedIcons = useMemo(() => {
    if (!data) return null

    console.log('SimpleIconCloudTest: 渲染图标数量:', Object.keys(data.simpleIcons).length)
    
    return Object.values(data.simpleIcons).map((icon) =>
      renderSimpleIcon({
        icon,
        bgHex: "#f3f2ef",
        fallbackHex: "#6e6e73",
        minContrastRatio: 1.2,
        size: 42,
        aProps: {
          href: undefined,
          target: undefined,
          rel: undefined,
          onClick: (e) => e.preventDefault(),
        },
      })
    )
  }, [data])

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96 border border-gray-300 rounded">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
          <span className="text-sm text-gray-600">Loading icons...</span>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-96 border border-red-300 rounded bg-red-50">
        <div className="text-center text-red-600">
          <p className="font-semibold">Failed to load icons</p>
          <p className="text-xs mt-1">{error}</p>
        </div>
      </div>
    )
  }

  if (!data || !renderedIcons) {
    return (
      <div className="flex items-center justify-center h-96 border border-gray-300 rounded">
        <div className="text-center text-gray-500">
          <p>No icons to display</p>
        </div>
      </div>
    )
  }

  console.log('SimpleIconCloudTest: 准备渲染Cloud组件')

  try {
    return (
      <div className="h-96 border border-green-300 rounded bg-green-50">
        <Cloud {...cloudProps}>
          <>{renderedIcons}</>
        </Cloud>
      </div>
    )
  } catch (err) {
    console.error('SimpleIconCloudTest: Cloud组件渲染错误:', err)
    return (
      <div className="flex items-center justify-center h-96 border border-red-300 rounded bg-red-50">
        <div className="text-center text-red-600">
          <p className="font-semibold">Cloud component error</p>
          <p className="text-xs mt-1">{err.message}</p>
        </div>
      </div>
    )
  }
}
