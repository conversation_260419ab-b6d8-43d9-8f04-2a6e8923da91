import { HeroSection } from "@/components/ui/hero-section-dark"

function HeroSectionDemo() {
  const handleGetStarted = () => {
    // 这里可以添加导航逻辑或其他操作
    console.log('Get Started clicked!')
    // 例如：navigate('/register')
  }

  return (
    <HeroSection
      title="AI-Powered Content Creation Platform"
      subtitle={{
        regular: "Transform your ideas into ",
        gradient: "beautiful digital experiences",
      }}
      description="Transform your content creation process with our advanced AI platform. Generate, optimize, and distribute engaging content across multiple channels in minutes, not hours."
      ctaText="Get Started Free"
      onCtaClick={handleGetStarted}
      bottomImage={{
        light: "https://images.unsplash.com/photo-1551434678-e076c223a692?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2340&q=80",
        dark: "https://images.unsplash.com/photo-1518709268805-4e9042af2176?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2340&q=80",
      }}
      gridOptions={{
        angle: 65,
        opacity: 0.4,
        cellSize: 50,
        lightLineColor: "#4a4a4a",
        darkLineColor: "#2a2a2a",
      }}
    />
  )
}

export { HeroSectionDemo } 