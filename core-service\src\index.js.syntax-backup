/**
 * Core Service for iTeraBiz Platform
 * Handles user management, subscriptions, analytics, and business logic
 */
require('dotenv').config();
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const winston = require('winston');
const path = require('path');
const fs = require('fs');

// Import Supabase client
const { createClient } = require('@supabase/supabase-js');

// Import routes
const authRoutes = require('./routes/auth');
const userRoutes = require('./routes/users');
const subscriptionRoutes = require('./routes/subscriptions');
const analyticsRoutes = require('./routes/analytics');
const agentRoutes = require('./routes/agentRoutes');
const billingRoutes = require('./routes/billing');
const dataInsightsRoutes = require('./routes/dataInsightsRoutes');

// Import middleware
const { authMiddleware } = require('./middleware/auth');
const errorHandler = require('./middleware/errorHandler');

// Setup winston logger
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info'
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    }),
    new winston.transports.File({ 
      filename: 'logs/error.log' 
      level: 'error'
      maxsize: 5242880, // 5MB
      maxFiles: 5
    }),
    new winston.transports.File({ 
      filename: 'logs/combined.log'
      maxsize: 5242880, // 5MB
      maxFiles: 5
    })
  ]
});

// Create Express app
const app = express();
const PORT = process.env.PORT || 3002;

// Create logs directory if it doesn't exist
const logsDir = path.join(__dirname, '../logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Create uploads directory if it doesn't exist
const uploadsDir = path.join(__dirname, '../uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// Initialize Supabase client
let supabase = null;
const initializeSupabase = () => {
  try {
    const supabaseUrl = process.env.SUPABASE_URL || process.env.REACT_APP_SUPABASE_URL;
    const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_ANON_KEY || process.env.REACT_APP_SUPABASE_ANON_KEY;
    
    if (!supabaseUrl || !supabaseKey) {
      logger.warn('⚠️ Supabase configuration missing. Service will continue with limited functionality.');
      return false;
    }
    
    supabase = createClient(supabaseUrl, supabaseKey);
    logger.info('✅ Supabase client initialized successfully');
    return true;
  } catch (error) {
    logger.warn('⚠️ Supabase initialization failed:' error.message);
    logger.warn('⚠️ Service will continue with limited functionality');
    return false;
  }
};

// Initialize Supabase first
const dbConnected = initializeSupabase();

// Make supabase available globally for routes
app.locals.supabase = supabase;

// Basic middleware
app.use(helmet({
  crossOriginResourcePolicy: { policy: "cross-origin" }
}));

app.use(cors({
  origin: process.env.CORS_ORIGIN || 'http://localhost:3000'
  credentials: true,
  methods: ['GET' 'POST' 'PUT' 'DELETE' 'PATCH' 'OPTIONS'],
  allowedHeaders: ['Content-Type' 'Authorization' 'X-Requested-With']
}));

app.use(morgan('combined' {
  stream: {
    write: (message) => logger.info(message.trim())
  }
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Static files
app.use('/uploads' express.static(path.join(__dirname, '../uploads')));

// Health check endpoint
app.get('/health' (req, res) => {
  res.json({
    status: 'healthy'
    timestamp: new Date().toISOString(),
    service: 'core-service'
    version: '1.0.0'
    environment: process.env.NODE_ENV || 'development'
    database: supabase ? 'connected' : 'disconnected'
  });
});

// API Routes
app.use('/api/auth' authRoutes);
app.use('/api/users' authMiddleware, userRoutes);
app.use('/api/subscriptions' authMiddleware, subscriptionRoutes);
app.use('/api/analytics' authMiddleware, analyticsRoutes);
// 在开发环境中暂时移除认证要求
if (process.env.NODE_ENV === 'development') {
  app.use('/api/agents' agentRoutes);
  // 添加直接的/agents路由作为API Gateway路径重写的备用方案
  app.use('/agents' agentRoutes);
} else {
app.use('/api/agents' authMiddleware, agentRoutes);
}
app.use('/api/billing' authMiddleware, billingRoutes);
app.use('/data-insights' authMiddleware, dataInsightsRoutes);

// 添加不需要认证的测试路由
app.get('/test/agents' (req, res) => {
  res.json({
    success: true,
    message: 'Agents test endpoint working'
    timestamp: new Date().toISOString()
  });
});

// 添加不需要认证的agents路由用于测试
app.get('/api/test-agents' (req, res) => {
  res.json({
    success: true,
    message: 'success'
    data: [
      {
        id: '1'
        name: 'Test Agent 1'
        agentType: 'AI_AUTO_REPLY'
        status: 'ACTIVE'
        description: 'Test agent for development'
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: '2'
        name: 'Test Agent 2'
        agentType: 'CONTENT_GENERATOR'
        status: 'INACTIVE'
        description: 'Another test agent'
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ]
  });
});

// 添加路径调试端点
app.get('/debug/path' (req, res) => {
  res.json({
    success: true,
    message: 'Path debug endpoint'
    originalUrl: req.originalUrl,
    path: req.path,
    baseUrl: req.baseUrl,
    url: req.url,
    method: req.method,
    headers: req.headers,
    timestamp: new Date().toISOString()
  });
});

// 添加API路径下的调试端点
app.get('/api/debug/path' (req, res) => {
  res.json({
    success: true,
    message: 'API Path debug endpoint'
    originalUrl: req.originalUrl,
    path: req.path,
    baseUrl: req.baseUrl,
    url: req.url,
    method: req.method,
    headers: req.headers,
    timestamp: new Date().toISOString()
  });
});

// Public routes (no auth required)
app.get('/api/status' (req, res) => {
  res.json({
    message: 'Core Service is running'
    timestamp: new Date().toISOString(),
    database: supabase ? 'connected' : 'disconnected'
  });
});

// 404 handler
app.use('*' (req, res) => {
  res.status(404).json({
    success: false,
    message: `Route ${req.originalUrl} not found`
  });
});

// Error handling middleware
app.use(errorHandler);

// Graceful shutdown
process.on('SIGTERM' async () => {
  logger.info('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT' async () => {
  logger.info('SIGINT received, shutting down gracefully');
  process.exit(0);
});

// Start server
const startServer = async () => {
  try {
    app.listen(PORT, () => {
      logger.info(`🚀 Core Service started on port ${PORT}`);
      logger.info(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
      logger.info(`🔗 CORS Origin: ${process.env.CORS_ORIGIN || 'http://localhost:3000'}`);
      logger.info(`🗄️ Database: ${dbConnected ? 'Supabase Connected' : 'Disconnected (continuing without DB)'}`);
    });
  } catch (error) {
    logger.error('Failed to start server:' error);
    process.exit(1);
  }
};

startServer();

module.exports = app; 