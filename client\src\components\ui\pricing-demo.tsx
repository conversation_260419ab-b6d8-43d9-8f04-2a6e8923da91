"use client";

import { Pricing } from "@/components/ui/pricing";

const iTeraBizPlans = [
  {
    name: "STARTER",
    price: "29",
    yearlyPrice: "23",
    period: "per month",
    features: [
      "10,000 AI-generated words/month",
      "5 Content templates",
      "Basic analytics",
      "Email support",
      "1 User account",
      "Standard content quality",
    ],
    description: "Perfect for individuals and small content creators",
    buttonText: "Start Free Trial",
    href: "/register",
    isPopular: false,
  },
  {
    name: "PROFESSIONAL", 
    price: "79",
    yearlyPrice: "63",
    period: "per month",
    features: [
      "50,000 AI-generated words/month",
      "25+ Content templates",
      "Advanced analytics & insights",
      "Priority support",
      "5 User accounts",
      "API access",
      "Custom branding",
      "Multi-platform publishing",
    ],
    description: "Ideal for growing teams and content agencies",
    buttonText: "Get Started",
    href: "/register",
    isPopular: true,
  },
  {
    name: "ENTERPRISE",
    price: "199",
    yearlyPrice: "159",
    period: "per month",
    features: [
      "Unlimited AI-generated content",
      "Custom templates & workflows",
      "Dedicated account manager",
      "White-label solution",
      "Unlimited users",
      "Advanced API & integrations",
      "SLA guarantee",
      "Custom AI model training",
      "Priority feature requests",
    ],
    description: "For large organizations with enterprise needs",
    buttonText: "Contact Sales",
    href: "/contact/sales",
    isPopular: false,
  },
];

function PricingDemo() {
  return (
    <div className="min-h-screen bg-white dark:bg-neutral-900">
      <Pricing 
        plans={iTeraBizPlans}
        title="Choose Your Perfect Plan"
        description="Scale your content creation with our AI-powered platform
All plans include access to our advanced AI tools, analytics, and dedicated support."
      />
    </div>
  );
}

export { PricingDemo }; 