﻿/* eslint-disable unicode-bom */
import React from "react";
import { <PERSON>, <PERSON>ontent, CardH<PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { TrendingUp, TrendingDown, Minus, Target, Info } from "lucide-react";
import { cn } from "@/lib/utils";
import { AnalyticsMetric } from "@/types/analytics";

interface MetricCardProps {
  metric: AnalyticsMetric;
  compact?: boolean;
  showTarget?: boolean;
  className?: string;
}

const MetricCard: React.FC<MetricCardProps> = ({
  metric,
  compact = false,
  showTarget = true,
  className
}) => {
  const formatValue = (
    value: number,
    format: string,
    unit?: string
  ) => {
    switch (format) {
      case "percentage":
        return `${value.toFixed(1)}%`;
      case "currency":
        return `$${value.toLocaleString()}`;
      case "duration":
        if (unit === "s") {
          return `${value.toFixed(1)}s`;
        } else if (unit === "m") {
          return `${value.toFixed(1)}m`;
        } else if (unit === "h") {
          return `${value.toFixed(1)}h`;
        }
        return `${value.toFixed(1)}${unit || ""}`;
      case "bytes":
        const sizes = ["B", "KB", "MB", "GB", "TB"];
        const i = Math.floor(Math.log(value) / Math.log(1024));
        return `${(value / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
      case "number":
      default:
        if (value >= 1000000) {
          return `${(value / 1000000).toFixed(1)}M`;
        } else if (value >= 1000) {
          return `${(value / 1000).toFixed(1)}K`;
        }
        return value.toLocaleString();
    }
  };

  const getTrendInfo = () => {
    switch (metric.trend) {
      case "up":
        return {
          icon: <TrendingUp className="h-4 w-4 text-green-600" />,
          color: "text-green-600",
          bgColor: "bg-green-50",
          borderColor: "border-green-200",
        };
      case "down":
        return {
          icon: <TrendingDown className="h-4 w-4 text-red-600" />,
          color: "text-red-600",
          bgColor: "bg-red-50",
          borderColor: "border-red-200",
        };
      case "stable":
      default:
        return {
          icon: <Minus className="h-4 w-4" />,
          color: "text-gray-600",
          bgColor: "bg-gray-50",
          borderColor: "border-gray-200",
        };
    }
  };

  const getTargetProgress = () => {
    if (!metric.target) return null;
    
    const progress = (metric.value / metric.target) * 100;
    return Math.min(progress, 100);
  };

  const getChangeDisplay = () => {
    if (metric.change === undefined || metric.changePercentage === undefined) {
      return null;
    }

    const isPositive = metric.change > 0;
    const changeColor = isPositive ? "text-green-600" : "text-red-600";
    const changeBg = isPositive ? "bg-green-50" : "bg-red-50";
    
    return (
      <Badge variant="secondary" className={cn(changeBg, changeColor)}>
        {isPositive ? "+" : ""}{metric.changePercentage.toFixed(1)}%
      </Badge>
    );
  };

  const trendInfo = getTrendInfo();
  const targetProgress = getTargetProgress();
  const changeDisplay = getChangeDisplay();

  return (
    <Card className={cn("transition-all duration-200 hover:shadow-md", className)}>
      <CardHeader className={cn("pb-2", compact && "pb-1")}>
        <div className="flex items-center justify-between">
          <CardTitle className={cn("text-sm font-medium text-muted-foreground", compact && "text-xs")}>
            {metric.name}
          </CardTitle>
          <div className="flex items-center gap-1">
            {changeDisplay}
            <div className={cn("p-1 rounded", trendInfo.bgColor)}>
              <span className={trendInfo.color}>
                {trendInfo.icon}
              </span>
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent className={cn("pt-0", compact && "pb-3")}>
        <div className="space-y-2">
          <div className="flex items-baseline gap-2">
            <span className={cn("text-2xl font-bold", compact && "text-xl")}>
              {formatValue(metric.value, metric.format, metric.unit)}
            </span>
            {metric.previousValue !== undefined && (
              <span className="text-sm text-muted-foreground">
                from {formatValue(metric.previousValue, metric.format, metric.unit)}
              </span>
            )}
          </div>

          {!compact && metric.description && (
            <p className="text-xs text-muted-foreground">
              {metric.description}
            </p>
          )}

          {showTarget && targetProgress !== null && !compact && (
            <div className="space-y-1">
              <div className="flex items-center justify-between text-xs">
                <span className="flex items-center gap-1 text-muted-foreground">
                  <Target className="h-3 w-3" />
                  Target: {formatValue(metric.target!, metric.format, metric.unit)}
                </span>
                <span className="font-medium">
                  {targetProgress.toFixed(0)}%
                </span>
              </div>
              <Progress 
                value={targetProgress} 
                className="h-1"
              />
            </div>
          )}

          {metric.benchmark && !compact && (
            <div className="flex items-center gap-2 text-xs">
              <Info className="h-3 w-3 text-muted-foreground" />
              <span className="text-muted-foreground">
                Benchmark: {formatValue(metric.benchmark, metric.format, metric.unit)}
              </span>
              <Badge 
                variant="outline" 
                className={cn(
                  "text-xs",
                  metric.value > metric.benchmark ? "text-green-600" : "text-red-600"
                )}
              >
                {metric.value > metric.benchmark ? "Above" : "Below"}
              </Badge>
            </div>
          )}

          {metric.change !== undefined && !compact && (
            <div className="text-xs text-muted-foreground">
              {metric.change > 0 ? "+" : ""}{formatValue(Math.abs(metric.change), metric.format, metric.unit)} 
              {" "}vs previous period
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default MetricCard;
