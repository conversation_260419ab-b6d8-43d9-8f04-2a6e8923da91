// 预定义场景
const SCENARIOS: NotificationScenario[] = [
  {
    id: 'booking_confirmed', name: '预约确认', description: '客户预约成功后立即发送', icon: Bell,
    defaultChannels: ['same_as_booking', 'email']
  },
  {
    id: 'booking_reminder', name: '预约提醒', description: '服务开始前发送提醒', icon: Bell,
    defaultChannels: ['same_as_booking', 'sms']
  },
  {
    id: 'service_ready', name: '即将轮到客户时通知', description: '即将轮到客户时通知', icon: Bell,
    defaultChannels: ['same_as_booking', 'push']
  }
];

const defaultSettings: SmartNotificationSettings = {
// ... existing code ...
} 