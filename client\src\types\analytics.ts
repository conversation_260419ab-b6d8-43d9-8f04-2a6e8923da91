export interface AnalyticsMetric {
  id: string;
  name: string;
  description: string;
  value: number;
  previousValue?: number;
  change?: number;
  changePercentage?: number;
  trend: 'up' | 'down' | 'stable'
  format: 'number' | 'percentage' | 'currency' | 'duration' | 'bytes'
  unit?: string;
  target?: number;
  benchmark?: number
}

export interface ConversationFunnelStep {
  id: string;
  name: string;
  description: string;
  count: number;
  percentage: number;
  dropoffRate?: number;
  averageTime?: number;
  conversionRate?: number
}

export interface ConversationFunnel {
  id: string;
  name: string;
  description: string;
  steps: ConversationFunnelStep[];
  totalEntries: number;
  totalCompletions: number;
  overallConversionRate: number;
  averageCompletionTime: number;
  period: {
    start: string;
    end: string
} }

export interface SentimentAnalysis {
  overall: {
    positive: number;
    neutral: number;
    negative: number;
    score: number; // -1 to 1
  };
  trends: {
    date: string;
    positive: number;
    neutral: number;
    negative: number;
    score: number
}[];
  topics: {
    topic: string;
    sentiment: number;
    mentions: number;
    keywords: string[]
}[];
  alerts: {
    type: 'negative_spike' | 'positive_trend' | 'topic_concern'
    message: string;
    severity: 'low' | 'medium' | 'high'
    timestamp: string
}[]
}

export interface UserJourney {
  id: string;
  userId: string;
  sessionId: string;
  startTime: string;
  endTime?: string;
  duration?: number;
  touchpoints: UserTouchpoint[];
  outcome: 'completed' | 'abandoned' | 'escalated' | 'ongoing'
  satisfaction?: number;
  tags: string[]
}

export interface UserTouchpoint {
  id: string;
  timestamp: string;
  type: 'message' | 'action' | 'event' | 'handoff'
  channel: string;
  content?: string;
  metadata?: Record<string, any>;
  duration?: number;
  success: boolean
}

export interface PerformanceMetrics {
  responseTime: {
    average: number;
    p50: number;
    p95: number;
    p99: number
};
  throughput: {
    messagesPerSecond: number;
    conversationsPerHour: number;
    peakLoad: number
};
  availability: {
    uptime: number;
    downtime: number;
    incidents: number
};
  aiMetrics: {
    accuracy: number;
    confidence: number;
    escalationRate: number;
    resolutionRate: number
} }

export interface CustomReport {
  id: string;
  name: string;
  description: string;
  type: 'dashboard' | 'export' | 'scheduled'
  config: {
    metrics: string[];
    filters: ReportFilter[];
    groupBy: string[];
    timeRange: TimeRange;
    visualization: 'table' | 'chart' | 'graph' | 'heatmap'
  };
  schedule?: {
    frequency: 'daily' | 'weekly' | 'monthly'
    time: string;
    recipients: string[];
    format: 'pdf' | 'excel' | 'csv'
  };
  createdBy: string;
  createdAt: string;
  lastRun?: string
}

export interface ReportFilter {
  field: string;
  operator: 'equals' | 'not_equals' | 'contains' | 'greater_than' | 'less_than' | 'between' | 'in'
  value: any;
  logicalOperator?: 'AND' | 'OR'
}

export interface TimeRange {
  type: 'relative' | 'absolute'
  relative?: {
    value: number;
    unit: 'minutes' | 'hours' | 'days' | 'weeks' | 'months'
  };
  absolute?: {
    start: string;
    end: string
} }

export interface AnalyticsDashboard {
  id: string;
  name: string;
  description: string;
  layout: DashboardWidget[];
  filters: ReportFilter[];
  timeRange: TimeRange;
  refreshInterval?: number;
  isPublic: boolean;
  createdBy: string;
  createdAt: string;
  updatedAt: string
}

export interface DashboardWidget {
  id: string;
  type: 'metric' | 'chart' | 'table' | 'funnel' | 'heatmap' | 'text'
  title: string;
  position: {
    x: number;
    y: number;
    width: number;
    height: number
};
  config: {
    metricId?: string;
    chartType?: 'line' | 'bar' | 'pie' | 'area' | 'scatter'
    dataSource: string;
    query: string;
    filters: ReportFilter[];
    groupBy?: string[];
    aggregation?: 'sum' | 'avg' | 'count' | 'min' | 'max'
  };
  style?: {
    backgroundColor?: string;
    textColor?: string;
    borderColor?: string
} }

export interface PredictiveAnalytics {
  forecasts: {
    metric: string;
    predictions: {
      date: string;
      value: number;
      confidence: number;
      upperBound: number;
      lowerBound: number
}[];
    accuracy: number;
    model: string
}[];
  anomalies: {
    timestamp: string;
    metric: string;
    value: number;
    expectedValue: number;
    severity: 'low' | 'medium' | 'high'
    description: string
}[];
  recommendations: {
    type: 'optimization' | 'alert' | 'action'
    title: string;
    description: string;
    impact: 'low' | 'medium' | 'high'
    effort: 'low' | 'medium' | 'high'
    category: string
}[]
}

export interface AnalyticsQuery {
  id: string;
  name: string;
  description: string;
  sql: string;
  parameters: {
    name: string;
    type: 'string' | 'number' | 'date' | 'boolean'
    defaultValue?: any;
    required: boolean
}[];
  resultSchema: {
    columns: {
      name: string;
      type: string;
      description?: string
}[]
};
  tags: string[];
  createdBy: string;
  createdAt: string
}

export interface AnalyticsExport {
  id: string;
  name: string;
  type: 'csv' | 'excel' | 'pdf' | 'json'
  query: string;
  filters: ReportFilter[];
  status: 'pending' | 'processing' | 'completed' | 'failed'
  downloadUrl?: string;
  fileSize?: number;
  recordCount?: number;
  createdAt: string;
  completedAt?: string;
  expiresAt?: string;
  error?: string
}

// 预定义的分析模板
export const ANALYTICS_TEMPLATES = {
  conversationFunnel: {
    name: 'Conversation Funnel Analysis',
    description: 'Track user progression through conversation stages',
    metrics: ['total_conversations', 'completed_conversations', 'abandoned_conversations'],
    defaultFilters: [],
    visualization: 'funnel'
  },
  sentimentTrends: {
    name: 'Sentiment Trends',
    description: 'Monitor customer sentiment over time',
    metrics: ['sentiment_score', 'positive_rate', 'negative_rate'],
    defaultFilters: [],
    visualization: 'line'
  },
  performanceOverview: {
    name: 'Performance Overview',
    description: 'Key performance indicators dashboard',
    metrics: ['response_time', 'resolution_rate', 'satisfaction_score'],
    defaultFilters: [],
    visualization: 'dashboard'
  },
  userJourneyMap: {
    name: 'User Journey Mapping',
    description: 'Visualize complete user interaction paths',
    metrics: ['journey_length', 'touchpoint_count', 'completion_rate'],
    defaultFilters: [],
    visualization: 'journey'
  }
};
