/*! For license information please see 721.3d971910.iframe.bundle.js.LICENSE.txt */
"use strict";(self.webpackChunkclient=self.webpackChunkclient||[]).push([[721],{"./node_modules/@radix-ui/primitive/dist/index.mjs":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{function composeEventHandlers(originalEventHandler,ourEventHandler,{checkForDefaultPrevented=!0}={}){return function handleEvent(event){if(originalEventHandler?.(event),!1===checkForDefaultPrevented||!event.defaultPrevented)return ourEventHandler?.(event)}}__webpack_require__.d(__webpack_exports__,{m:()=>composeEventHandlers})},"./node_modules/@radix-ui/react-compose-refs/dist/index.mjs":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.d(__webpack_exports__,{s:()=>useComposedRefs});var react__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("./node_modules/react/index.js");function setRef(ref,value){if("function"==typeof ref)return ref(value);null!=ref&&(ref.current=value)}function useComposedRefs(...refs){return react__WEBPACK_IMPORTED_MODULE_0__.useCallback(function composeRefs(...refs){return node=>{let hasCleanup=!1;const cleanups=refs.map((ref=>{const cleanup=setRef(ref,node);return hasCleanup||"function"!=typeof cleanup||(hasCleanup=!0),cleanup}));if(hasCleanup)return()=>{for(let i=0;i<cleanups.length;i++){const cleanup=cleanups[i];"function"==typeof cleanup?cleanup():setRef(refs[i],null)}}}}(...refs),refs)}},"./node_modules/@radix-ui/react-context/dist/index.mjs":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.d(__webpack_exports__,{A:()=>createContextScope,q:()=>createContext2});var react__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("./node_modules/react/index.js"),react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__("./node_modules/react/jsx-runtime.js");function createContext2(rootComponentName,defaultContext){const Context=react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext),Provider=props=>{const{children,...context}=props,value=react__WEBPACK_IMPORTED_MODULE_0__.useMemo((()=>context),Object.values(context));return(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Context.Provider,{value,children})};return Provider.displayName=rootComponentName+"Provider",[Provider,function useContext2(consumerName){const context=react__WEBPACK_IMPORTED_MODULE_0__.useContext(Context);if(context)return context;if(void 0!==defaultContext)return defaultContext;throw new Error(`\`${consumerName}\` must be used within \`${rootComponentName}\``)}]}function createContextScope(scopeName,createContextScopeDeps=[]){let defaultContexts=[];const createScope=()=>{const scopeContexts=defaultContexts.map((defaultContext=>react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext)));return function useScope(scope){const contexts=scope?.[scopeName]||scopeContexts;return react__WEBPACK_IMPORTED_MODULE_0__.useMemo((()=>({[`__scope${scopeName}`]:{...scope,[scopeName]:contexts}})),[scope,contexts])}};return createScope.scopeName=scopeName,[function createContext3(rootComponentName,defaultContext){const BaseContext=react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext),index=defaultContexts.length;defaultContexts=[...defaultContexts,defaultContext];const Provider=props=>{const{scope,children,...context}=props,Context=scope?.[scopeName]?.[index]||BaseContext,value=react__WEBPACK_IMPORTED_MODULE_0__.useMemo((()=>context),Object.values(context));return(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Context.Provider,{value,children})};return Provider.displayName=rootComponentName+"Provider",[Provider,function useContext2(consumerName,scope){const Context=scope?.[scopeName]?.[index]||BaseContext,context=react__WEBPACK_IMPORTED_MODULE_0__.useContext(Context);if(context)return context;if(void 0!==defaultContext)return defaultContext;throw new Error(`\`${consumerName}\` must be used within \`${rootComponentName}\``)}]},composeContextScopes(createScope,...createContextScopeDeps)]}function composeContextScopes(...scopes){const baseScope=scopes[0];if(1===scopes.length)return baseScope;const createScope=()=>{const scopeHooks=scopes.map((createScope2=>({useScope:createScope2(),scopeName:createScope2.scopeName})));return function useComposedScopes(overrideScopes){const nextScopes=scopeHooks.reduce(((nextScopes2,{useScope,scopeName})=>({...nextScopes2,...useScope(overrideScopes)[`__scope${scopeName}`]})),{});return react__WEBPACK_IMPORTED_MODULE_0__.useMemo((()=>({[`__scope${baseScope.scopeName}`]:nextScopes})),[nextScopes])}};return createScope.scopeName=baseScope.scopeName,createScope}},"./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.d(__webpack_exports__,{qW:()=>DismissableLayer});var react=__webpack_require__("./node_modules/react/index.js"),dist=__webpack_require__("./node_modules/@radix-ui/primitive/dist/index.mjs"),react_primitive_dist=__webpack_require__("./node_modules/@radix-ui/react-primitive/dist/index.mjs"),react_compose_refs_dist=__webpack_require__("./node_modules/@radix-ui/react-compose-refs/dist/index.mjs"),react_use_callback_ref_dist=__webpack_require__("./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs");var originalBodyPointerEvents,jsx_runtime=__webpack_require__("./node_modules/react/jsx-runtime.js"),DismissableLayerContext=react.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),DismissableLayer=react.forwardRef(((props,forwardedRef)=>{const{disableOutsidePointerEvents=!1,onEscapeKeyDown,onPointerDownOutside,onFocusOutside,onInteractOutside,onDismiss,...layerProps}=props,context=react.useContext(DismissableLayerContext),[node,setNode]=react.useState(null),ownerDocument=node?.ownerDocument??globalThis?.document,[,force]=react.useState({}),composedRefs=(0,react_compose_refs_dist.s)(forwardedRef,(node2=>setNode(node2))),layers=Array.from(context.layers),[highestLayerWithOutsidePointerEventsDisabled]=[...context.layersWithOutsidePointerEventsDisabled].slice(-1),highestLayerWithOutsidePointerEventsDisabledIndex=layers.indexOf(highestLayerWithOutsidePointerEventsDisabled),index=node?layers.indexOf(node):-1,isBodyPointerEventsDisabled=context.layersWithOutsidePointerEventsDisabled.size>0,isPointerEventsEnabled=index>=highestLayerWithOutsidePointerEventsDisabledIndex,pointerDownOutside=function usePointerDownOutside(onPointerDownOutside,ownerDocument=globalThis?.document){const handlePointerDownOutside=(0,react_use_callback_ref_dist.c)(onPointerDownOutside),isPointerInsideReactTreeRef=react.useRef(!1),handleClickRef=react.useRef((()=>{}));return react.useEffect((()=>{const handlePointerDown=event=>{if(event.target&&!isPointerInsideReactTreeRef.current){let handleAndDispatchPointerDownOutsideEvent2=function(){handleAndDispatchCustomEvent("dismissableLayer.pointerDownOutside",handlePointerDownOutside,eventDetail,{discrete:!0})};const eventDetail={originalEvent:event};"touch"===event.pointerType?(ownerDocument.removeEventListener("click",handleClickRef.current),handleClickRef.current=handleAndDispatchPointerDownOutsideEvent2,ownerDocument.addEventListener("click",handleClickRef.current,{once:!0})):handleAndDispatchPointerDownOutsideEvent2()}else ownerDocument.removeEventListener("click",handleClickRef.current);isPointerInsideReactTreeRef.current=!1},timerId=window.setTimeout((()=>{ownerDocument.addEventListener("pointerdown",handlePointerDown)}),0);return()=>{window.clearTimeout(timerId),ownerDocument.removeEventListener("pointerdown",handlePointerDown),ownerDocument.removeEventListener("click",handleClickRef.current)}}),[ownerDocument,handlePointerDownOutside]),{onPointerDownCapture:()=>isPointerInsideReactTreeRef.current=!0}}((event=>{const target=event.target,isPointerDownOnBranch=[...context.branches].some((branch=>branch.contains(target)));isPointerEventsEnabled&&!isPointerDownOnBranch&&(onPointerDownOutside?.(event),onInteractOutside?.(event),event.defaultPrevented||onDismiss?.())}),ownerDocument),focusOutside=function useFocusOutside(onFocusOutside,ownerDocument=globalThis?.document){const handleFocusOutside=(0,react_use_callback_ref_dist.c)(onFocusOutside),isFocusInsideReactTreeRef=react.useRef(!1);return react.useEffect((()=>{const handleFocus=event=>{if(event.target&&!isFocusInsideReactTreeRef.current){handleAndDispatchCustomEvent("dismissableLayer.focusOutside",handleFocusOutside,{originalEvent:event},{discrete:!1})}};return ownerDocument.addEventListener("focusin",handleFocus),()=>ownerDocument.removeEventListener("focusin",handleFocus)}),[ownerDocument,handleFocusOutside]),{onFocusCapture:()=>isFocusInsideReactTreeRef.current=!0,onBlurCapture:()=>isFocusInsideReactTreeRef.current=!1}}((event=>{const target=event.target;[...context.branches].some((branch=>branch.contains(target)))||(onFocusOutside?.(event),onInteractOutside?.(event),event.defaultPrevented||onDismiss?.())}),ownerDocument);return function useEscapeKeydown(onEscapeKeyDownProp,ownerDocument=globalThis?.document){const onEscapeKeyDown=(0,react_use_callback_ref_dist.c)(onEscapeKeyDownProp);react.useEffect((()=>{const handleKeyDown=event=>{"Escape"===event.key&&onEscapeKeyDown(event)};return ownerDocument.addEventListener("keydown",handleKeyDown,{capture:!0}),()=>ownerDocument.removeEventListener("keydown",handleKeyDown,{capture:!0})}),[onEscapeKeyDown,ownerDocument])}((event=>{index===context.layers.size-1&&(onEscapeKeyDown?.(event),!event.defaultPrevented&&onDismiss&&(event.preventDefault(),onDismiss()))}),ownerDocument),react.useEffect((()=>{if(node)return disableOutsidePointerEvents&&(0===context.layersWithOutsidePointerEventsDisabled.size&&(originalBodyPointerEvents=ownerDocument.body.style.pointerEvents,ownerDocument.body.style.pointerEvents="none"),context.layersWithOutsidePointerEventsDisabled.add(node)),context.layers.add(node),dispatchUpdate(),()=>{disableOutsidePointerEvents&&1===context.layersWithOutsidePointerEventsDisabled.size&&(ownerDocument.body.style.pointerEvents=originalBodyPointerEvents)}}),[node,ownerDocument,disableOutsidePointerEvents,context]),react.useEffect((()=>()=>{node&&(context.layers.delete(node),context.layersWithOutsidePointerEventsDisabled.delete(node),dispatchUpdate())}),[node,context]),react.useEffect((()=>{const handleUpdate=()=>force({});return document.addEventListener("dismissableLayer.update",handleUpdate),()=>document.removeEventListener("dismissableLayer.update",handleUpdate)}),[]),(0,jsx_runtime.jsx)(react_primitive_dist.sG.div,{...layerProps,ref:composedRefs,style:{pointerEvents:isBodyPointerEventsDisabled?isPointerEventsEnabled?"auto":"none":void 0,...props.style},onFocusCapture:(0,dist.m)(props.onFocusCapture,focusOutside.onFocusCapture),onBlurCapture:(0,dist.m)(props.onBlurCapture,focusOutside.onBlurCapture),onPointerDownCapture:(0,dist.m)(props.onPointerDownCapture,pointerDownOutside.onPointerDownCapture)})}));DismissableLayer.displayName="DismissableLayer";var DismissableLayerBranch=react.forwardRef(((props,forwardedRef)=>{const context=react.useContext(DismissableLayerContext),ref=react.useRef(null),composedRefs=(0,react_compose_refs_dist.s)(forwardedRef,ref);return react.useEffect((()=>{const node=ref.current;if(node)return context.branches.add(node),()=>{context.branches.delete(node)}}),[context.branches]),(0,jsx_runtime.jsx)(react_primitive_dist.sG.div,{...props,ref:composedRefs})}));function dispatchUpdate(){const event=new CustomEvent("dismissableLayer.update");document.dispatchEvent(event)}function handleAndDispatchCustomEvent(name,handler,detail,{discrete}){const target=detail.originalEvent.target,event=new CustomEvent(name,{bubbles:!1,cancelable:!0,detail});handler&&target.addEventListener(name,handler,{once:!0}),discrete?(0,react_primitive_dist.hO)(target,event):target.dispatchEvent(event)}DismissableLayerBranch.displayName="DismissableLayerBranch"},"./node_modules/@radix-ui/react-focus-guards/dist/index.mjs":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.d(__webpack_exports__,{Oh:()=>useFocusGuards});var react__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("./node_modules/react/index.js"),count=0;function useFocusGuards(){react__WEBPACK_IMPORTED_MODULE_0__.useEffect((()=>{const edgeGuards=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",edgeGuards[0]??createFocusGuard()),document.body.insertAdjacentElement("beforeend",edgeGuards[1]??createFocusGuard()),count++,()=>{1===count&&document.querySelectorAll("[data-radix-focus-guard]").forEach((node=>node.remove())),count--}}),[])}function createFocusGuard(){const element=document.createElement("span");return element.setAttribute("data-radix-focus-guard",""),element.tabIndex=0,element.style.outline="none",element.style.opacity="0",element.style.position="fixed",element.style.pointerEvents="none",element}},"./node_modules/@radix-ui/react-focus-scope/dist/index.mjs":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.d(__webpack_exports__,{n:()=>FocusScope});var react__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("./node_modules/react/index.js"),_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__=__webpack_require__("./node_modules/@radix-ui/react-compose-refs/dist/index.mjs"),_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__=__webpack_require__("./node_modules/@radix-ui/react-primitive/dist/index.mjs"),_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_2__=__webpack_require__("./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs"),react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__("./node_modules/react/jsx-runtime.js"),EVENT_OPTIONS={bubbles:!1,cancelable:!0},FocusScope=react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(((props,forwardedRef)=>{const{loop=!1,trapped=!1,onMountAutoFocus:onMountAutoFocusProp,onUnmountAutoFocus:onUnmountAutoFocusProp,...scopeProps}=props,[container,setContainer]=react__WEBPACK_IMPORTED_MODULE_0__.useState(null),onMountAutoFocus=(0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_2__.c)(onMountAutoFocusProp),onUnmountAutoFocus=(0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_2__.c)(onUnmountAutoFocusProp),lastFocusedElementRef=react__WEBPACK_IMPORTED_MODULE_0__.useRef(null),composedRefs=(0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.s)(forwardedRef,(node=>setContainer(node))),focusScope=react__WEBPACK_IMPORTED_MODULE_0__.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;react__WEBPACK_IMPORTED_MODULE_0__.useEffect((()=>{if(trapped){let handleFocusIn2=function(event){if(focusScope.paused||!container)return;const target=event.target;container.contains(target)?lastFocusedElementRef.current=target:focus(lastFocusedElementRef.current,{select:!0})},handleFocusOut2=function(event){if(focusScope.paused||!container)return;const relatedTarget=event.relatedTarget;null!==relatedTarget&&(container.contains(relatedTarget)||focus(lastFocusedElementRef.current,{select:!0}))},handleMutations2=function(mutations){if(document.activeElement===document.body)for(const mutation of mutations)mutation.removedNodes.length>0&&focus(container)};document.addEventListener("focusin",handleFocusIn2),document.addEventListener("focusout",handleFocusOut2);const mutationObserver=new MutationObserver(handleMutations2);return container&&mutationObserver.observe(container,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",handleFocusIn2),document.removeEventListener("focusout",handleFocusOut2),mutationObserver.disconnect()}}}),[trapped,container,focusScope.paused]),react__WEBPACK_IMPORTED_MODULE_0__.useEffect((()=>{if(container){focusScopesStack.add(focusScope);const previouslyFocusedElement=document.activeElement;if(!container.contains(previouslyFocusedElement)){const mountEvent=new CustomEvent("focusScope.autoFocusOnMount",EVENT_OPTIONS);container.addEventListener("focusScope.autoFocusOnMount",onMountAutoFocus),container.dispatchEvent(mountEvent),mountEvent.defaultPrevented||(!function focusFirst(candidates,{select=!1}={}){const previouslyFocusedElement=document.activeElement;for(const candidate of candidates)if(focus(candidate,{select}),document.activeElement!==previouslyFocusedElement)return}(function removeLinks(items){return items.filter((item=>"A"!==item.tagName))}(getTabbableCandidates(container)),{select:!0}),document.activeElement===previouslyFocusedElement&&focus(container))}return()=>{container.removeEventListener("focusScope.autoFocusOnMount",onMountAutoFocus),setTimeout((()=>{const unmountEvent=new CustomEvent("focusScope.autoFocusOnUnmount",EVENT_OPTIONS);container.addEventListener("focusScope.autoFocusOnUnmount",onUnmountAutoFocus),container.dispatchEvent(unmountEvent),unmountEvent.defaultPrevented||focus(previouslyFocusedElement??document.body,{select:!0}),container.removeEventListener("focusScope.autoFocusOnUnmount",onUnmountAutoFocus),focusScopesStack.remove(focusScope)}),0)}}}),[container,onMountAutoFocus,onUnmountAutoFocus,focusScope]);const handleKeyDown=react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event=>{if(!loop&&!trapped)return;if(focusScope.paused)return;const isTabKey="Tab"===event.key&&!event.altKey&&!event.ctrlKey&&!event.metaKey,focusedElement=document.activeElement;if(isTabKey&&focusedElement){const container2=event.currentTarget,[first,last]=function getTabbableEdges(container){const candidates=getTabbableCandidates(container),first=findVisible(candidates,container),last=findVisible(candidates.reverse(),container);return[first,last]}(container2);first&&last?event.shiftKey||focusedElement!==last?event.shiftKey&&focusedElement===first&&(event.preventDefault(),loop&&focus(last,{select:!0})):(event.preventDefault(),loop&&focus(first,{select:!0})):focusedElement===container2&&event.preventDefault()}}),[loop,trapped,focusScope.paused]);return(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.sG.div,{tabIndex:-1,...scopeProps,ref:composedRefs,onKeyDown:handleKeyDown})}));function getTabbableCandidates(container){const nodes=[],walker=document.createTreeWalker(container,NodeFilter.SHOW_ELEMENT,{acceptNode:node=>{const isHiddenInput="INPUT"===node.tagName&&"hidden"===node.type;return node.disabled||node.hidden||isHiddenInput?NodeFilter.FILTER_SKIP:node.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;walker.nextNode();)nodes.push(walker.currentNode);return nodes}function findVisible(elements,container){for(const element of elements)if(!isHidden(element,{upTo:container}))return element}function isHidden(node,{upTo}){if("hidden"===getComputedStyle(node).visibility)return!0;for(;node;){if(void 0!==upTo&&node===upTo)return!1;if("none"===getComputedStyle(node).display)return!0;node=node.parentElement}return!1}function focus(element,{select=!1}={}){if(element&&element.focus){const previouslyFocusedElement=document.activeElement;element.focus({preventScroll:!0}),element!==previouslyFocusedElement&&function isSelectableInput(element){return element instanceof HTMLInputElement&&"select"in element}(element)&&select&&element.select()}}FocusScope.displayName="FocusScope";var focusScopesStack=function createFocusScopesStack(){let stack=[];return{add(focusScope){const activeFocusScope=stack[0];focusScope!==activeFocusScope&&activeFocusScope?.pause(),stack=arrayRemove(stack,focusScope),stack.unshift(focusScope)},remove(focusScope){stack=arrayRemove(stack,focusScope),stack[0]?.resume()}}}();function arrayRemove(array,item){const updatedArray=[...array],index=updatedArray.indexOf(item);return-1!==index&&updatedArray.splice(index,1),updatedArray}},"./node_modules/@radix-ui/react-id/dist/index.mjs":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{var react__WEBPACK_IMPORTED_MODULE_0___namespace_cache;__webpack_require__.d(__webpack_exports__,{B:()=>useId});var react__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("./node_modules/react/index.js"),_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__("./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs"),useReactId=(react__WEBPACK_IMPORTED_MODULE_0___namespace_cache||(react__WEBPACK_IMPORTED_MODULE_0___namespace_cache=__webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__,2)))[" useId ".trim().toString()]||(()=>{}),count=0;function useId(deterministicId){const[id,setId]=react__WEBPACK_IMPORTED_MODULE_0__.useState(useReactId());return(0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__.N)((()=>{deterministicId||setId((reactId=>reactId??String(count++)))}),[deterministicId]),deterministicId||(id?`radix-${id}`:"")}},"./node_modules/@radix-ui/react-portal/dist/index.mjs":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.d(__webpack_exports__,{Z:()=>Portal});var react__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("./node_modules/react/index.js"),react_dom__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__("./node_modules/react-dom/index.js"),_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__=__webpack_require__("./node_modules/@radix-ui/react-primitive/dist/index.mjs"),_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_3__=__webpack_require__("./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs"),react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__=__webpack_require__("./node_modules/react/jsx-runtime.js"),Portal=react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(((props,forwardedRef)=>{const{container:containerProp,...portalProps}=props,[mounted,setMounted]=react__WEBPACK_IMPORTED_MODULE_0__.useState(!1);(0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_3__.N)((()=>setMounted(!0)),[]);const container=containerProp||mounted&&globalThis?.document?.body;return container?react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.sG.div,{...portalProps,ref:forwardedRef}),container):null}));Portal.displayName="Portal"},"./node_modules/@radix-ui/react-primitive/dist/index.mjs":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.d(__webpack_exports__,{hO:()=>dispatchDiscreteCustomEvent,sG:()=>Primitive});var react__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("./node_modules/react/index.js"),react_dom__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__("./node_modules/react-dom/index.js"),_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__=__webpack_require__("./node_modules/@radix-ui/react-slot/dist/index.mjs"),react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__=__webpack_require__("./node_modules/react/jsx-runtime.js"),Primitive=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce(((primitive,node)=>{const Slot=(0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.TL)(`Primitive.${node}`),Node=react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(((props,forwardedRef)=>{const{asChild,...primitiveProps}=props,Comp=asChild?Slot:node;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Comp,{...primitiveProps,ref:forwardedRef})}));return Node.displayName=`Primitive.${node}`,{...primitive,[node]:Node}}),{});function dispatchDiscreteCustomEvent(target,event){target&&react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync((()=>target.dispatchEvent(event)))}},"./node_modules/@radix-ui/react-slot/dist/index.mjs":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.d(__webpack_exports__,{TL:()=>createSlot});var react__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("./node_modules/react/index.js"),_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__=__webpack_require__("./node_modules/@radix-ui/react-compose-refs/dist/index.mjs"),react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__("./node_modules/react/jsx-runtime.js");function createSlot(ownerName){const SlotClone=createSlotClone(ownerName),Slot2=react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(((props,forwardedRef)=>{const{children,...slotProps}=props,childrenArray=react__WEBPACK_IMPORTED_MODULE_0__.Children.toArray(children),slottable=childrenArray.find(isSlottable);if(slottable){const newElement=slottable.props.children,newChildren=childrenArray.map((child=>child===slottable?react__WEBPACK_IMPORTED_MODULE_0__.Children.count(newElement)>1?react__WEBPACK_IMPORTED_MODULE_0__.Children.only(null):react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(newElement)?newElement.props.children:null:child));return(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SlotClone,{...slotProps,ref:forwardedRef,children:react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(newElement)?react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(newElement,void 0,newChildren):null})}return(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SlotClone,{...slotProps,ref:forwardedRef,children})}));return Slot2.displayName=`${ownerName}.Slot`,Slot2}function createSlotClone(ownerName){const SlotClone=react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(((props,forwardedRef)=>{const{children,...slotProps}=props,childrenRef=react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(children)?function getElementRef(element){let getter=Object.getOwnPropertyDescriptor(element.props,"ref")?.get,mayWarn=getter&&"isReactWarning"in getter&&getter.isReactWarning;if(mayWarn)return element.ref;if(getter=Object.getOwnPropertyDescriptor(element,"ref")?.get,mayWarn=getter&&"isReactWarning"in getter&&getter.isReactWarning,mayWarn)return element.props.ref;return element.props.ref||element.ref}(children):void 0,ref=(0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.s)(childrenRef,forwardedRef);if(react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(children)){const props2=function mergeProps(slotProps,childProps){const overrideProps={...childProps};for(const propName in childProps){const slotPropValue=slotProps[propName],childPropValue=childProps[propName];/^on[A-Z]/.test(propName)?slotPropValue&&childPropValue?overrideProps[propName]=(...args)=>{const result=childPropValue(...args);return slotPropValue(...args),result}:slotPropValue&&(overrideProps[propName]=slotPropValue):"style"===propName?overrideProps[propName]={...slotPropValue,...childPropValue}:"className"===propName&&(overrideProps[propName]=[slotPropValue,childPropValue].filter(Boolean).join(" "))}return{...slotProps,...overrideProps}}(slotProps,children.props);return children.type!==react__WEBPACK_IMPORTED_MODULE_0__.Fragment&&(props2.ref=ref),react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(children,props2)}return react__WEBPACK_IMPORTED_MODULE_0__.Children.count(children)>1?react__WEBPACK_IMPORTED_MODULE_0__.Children.only(null):null}));return SlotClone.displayName=`${ownerName}.SlotClone`,SlotClone}var SLOTTABLE_IDENTIFIER=Symbol("radix.slottable");function isSlottable(child){return react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(child)&&"function"==typeof child.type&&"__radixId"in child.type&&child.type.__radixId===SLOTTABLE_IDENTIFIER}},"./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.d(__webpack_exports__,{c:()=>useCallbackRef});var react__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("./node_modules/react/index.js");function useCallbackRef(callback){const callbackRef=react__WEBPACK_IMPORTED_MODULE_0__.useRef(callback);return react__WEBPACK_IMPORTED_MODULE_0__.useEffect((()=>{callbackRef.current=callback})),react__WEBPACK_IMPORTED_MODULE_0__.useMemo((()=>(...args)=>callbackRef.current?.(...args)),[])}},"./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{var react__WEBPACK_IMPORTED_MODULE_0___namespace_cache;__webpack_require__.d(__webpack_exports__,{i:()=>useControllableState});var react__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("./node_modules/react/index.js"),_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__("./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs"),useInsertionEffect=(react__WEBPACK_IMPORTED_MODULE_0___namespace_cache||(react__WEBPACK_IMPORTED_MODULE_0___namespace_cache=__webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__,2)))[" useInsertionEffect ".trim().toString()]||_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__.N;function useControllableState({prop,defaultProp,onChange=()=>{},caller}){const[uncontrolledProp,setUncontrolledProp,onChangeRef]=function useUncontrolledState({defaultProp,onChange}){const[value,setValue]=react__WEBPACK_IMPORTED_MODULE_0__.useState(defaultProp),prevValueRef=react__WEBPACK_IMPORTED_MODULE_0__.useRef(value),onChangeRef=react__WEBPACK_IMPORTED_MODULE_0__.useRef(onChange);return useInsertionEffect((()=>{onChangeRef.current=onChange}),[onChange]),react__WEBPACK_IMPORTED_MODULE_0__.useEffect((()=>{prevValueRef.current!==value&&(onChangeRef.current?.(value),prevValueRef.current=value)}),[value,prevValueRef]),[value,setValue,onChangeRef]}({defaultProp,onChange}),isControlled=void 0!==prop,value=isControlled?prop:uncontrolledProp;{const isControlledRef=react__WEBPACK_IMPORTED_MODULE_0__.useRef(void 0!==prop);react__WEBPACK_IMPORTED_MODULE_0__.useEffect((()=>{const wasControlled=isControlledRef.current;if(wasControlled!==isControlled){const from=wasControlled?"controlled":"uncontrolled",to=isControlled?"controlled":"uncontrolled";console.warn(`${caller} is changing from ${from} to ${to}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}isControlledRef.current=isControlled}),[isControlled,caller])}const setValue=react__WEBPACK_IMPORTED_MODULE_0__.useCallback((nextValue=>{if(isControlled){const value2=function isFunction(value){return"function"==typeof value}(nextValue)?nextValue(prop):nextValue;value2!==prop&&onChangeRef.current?.(value2)}else setUncontrolledProp(nextValue)}),[isControlled,prop,setUncontrolledProp,onChangeRef]);return[value,setValue]}Symbol("RADIX:SYNC_STATE")},"./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.d(__webpack_exports__,{N:()=>useLayoutEffect2});var react__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("./node_modules/react/index.js"),useLayoutEffect2=globalThis?.document?react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect:()=>{}},"./node_modules/aria-hidden/dist/es2015/index.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.d(__webpack_exports__,{Eq:()=>hideOthers});var getDefaultParent=function(originalTarget){return"undefined"==typeof document?null:(Array.isArray(originalTarget)?originalTarget[0]:originalTarget).ownerDocument.body},counterMap=new WeakMap,uncontrolledNodes=new WeakMap,markerMap={},lockCount=0,unwrapHost=function(node){return node&&(node.host||unwrapHost(node.parentNode))},applyAttributeToOthers=function(originalTarget,parentNode,markerName,controlAttribute){var targets=function(parent,targets){return targets.map((function(target){if(parent.contains(target))return target;var correctedTarget=unwrapHost(target);return correctedTarget&&parent.contains(correctedTarget)?correctedTarget:(console.error("aria-hidden",target,"in not contained inside",parent,". Doing nothing"),null)})).filter((function(x){return Boolean(x)}))}(parentNode,Array.isArray(originalTarget)?originalTarget:[originalTarget]);markerMap[markerName]||(markerMap[markerName]=new WeakMap);var markerCounter=markerMap[markerName],hiddenNodes=[],elementsToKeep=new Set,elementsToStop=new Set(targets),keep=function(el){el&&!elementsToKeep.has(el)&&(elementsToKeep.add(el),keep(el.parentNode))};targets.forEach(keep);var deep=function(parent){parent&&!elementsToStop.has(parent)&&Array.prototype.forEach.call(parent.children,(function(node){if(elementsToKeep.has(node))deep(node);else try{var attr=node.getAttribute(controlAttribute),alreadyHidden=null!==attr&&"false"!==attr,counterValue=(counterMap.get(node)||0)+1,markerValue=(markerCounter.get(node)||0)+1;counterMap.set(node,counterValue),markerCounter.set(node,markerValue),hiddenNodes.push(node),1===counterValue&&alreadyHidden&&uncontrolledNodes.set(node,!0),1===markerValue&&node.setAttribute(markerName,"true"),alreadyHidden||node.setAttribute(controlAttribute,"true")}catch(e){console.error("aria-hidden: cannot operate on ",node,e)}}))};return deep(parentNode),elementsToKeep.clear(),lockCount++,function(){hiddenNodes.forEach((function(node){var counterValue=counterMap.get(node)-1,markerValue=markerCounter.get(node)-1;counterMap.set(node,counterValue),markerCounter.set(node,markerValue),counterValue||(uncontrolledNodes.has(node)||node.removeAttribute(controlAttribute),uncontrolledNodes.delete(node)),markerValue||node.removeAttribute(markerName)})),--lockCount||(counterMap=new WeakMap,counterMap=new WeakMap,uncontrolledNodes=new WeakMap,markerMap={})}},hideOthers=function(originalTarget,parentNode,markerName){void 0===markerName&&(markerName="data-aria-hidden");var targets=Array.from(Array.isArray(originalTarget)?originalTarget:[originalTarget]),activeParentNode=parentNode||getDefaultParent(originalTarget);return activeParentNode?(targets.push.apply(targets,Array.from(activeParentNode.querySelectorAll("[aria-live]"))),applyAttributeToOthers(targets,activeParentNode,markerName,"aria-hidden")):function(){return null}}},"./node_modules/lucide-react/dist/esm/createLucideIcon.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.d(__webpack_exports__,{A:()=>createLucideIcon});var react=__webpack_require__("./node_modules/react/index.js");const toPascalCase=string=>{const camelCase=(string=>string.replace(/^([A-Z])|[\s-_]+(\w)/g,((match,p1,p2)=>p2?p2.toUpperCase():p1.toLowerCase())))(string);return camelCase.charAt(0).toUpperCase()+camelCase.slice(1)},mergeClasses=(...classes)=>classes.filter(((className,index,array)=>Boolean(className)&&""!==className.trim()&&array.indexOf(className)===index)).join(" ").trim(),hasA11yProp=props=>{for(const prop in props)if(prop.startsWith("aria-")||"role"===prop||"title"===prop)return!0};var defaultAttributes={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};const Icon=(0,react.forwardRef)((({color="currentColor",size=24,strokeWidth=2,absoluteStrokeWidth,className="",children,iconNode,...rest},ref)=>(0,react.createElement)("svg",{ref,...defaultAttributes,width:size,height:size,stroke:color,strokeWidth:absoluteStrokeWidth?24*Number(strokeWidth)/Number(size):strokeWidth,className:mergeClasses("lucide",className),...!children&&!hasA11yProp(rest)&&{"aria-hidden":"true"},...rest},[...iconNode.map((([tag,attrs])=>(0,react.createElement)(tag,attrs))),...Array.isArray(children)?children:[children]]))),createLucideIcon=(iconName,iconNode)=>{const Component=(0,react.forwardRef)((({className,...props},ref)=>{return(0,react.createElement)(Icon,{ref,iconNode,className:mergeClasses(`lucide-${string=toPascalCase(iconName),string.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()}`,`lucide-${iconName}`,className),...props});var string}));return Component.displayName=toPascalCase(iconName),Component}},"./node_modules/react-remove-scroll/dist/es2015/Combination.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.d(__webpack_exports__,{A:()=>Combination});var __assign=function(){return __assign=Object.assign||function __assign(t){for(var s,i=1,n=arguments.length;i<n;i++)for(var p in s=arguments[i])Object.prototype.hasOwnProperty.call(s,p)&&(t[p]=s[p]);return t},__assign.apply(this,arguments)};function __rest(s,e){var t={};for(var p in s)Object.prototype.hasOwnProperty.call(s,p)&&e.indexOf(p)<0&&(t[p]=s[p]);if(null!=s&&"function"==typeof Object.getOwnPropertySymbols){var i=0;for(p=Object.getOwnPropertySymbols(s);i<p.length;i++)e.indexOf(p[i])<0&&Object.prototype.propertyIsEnumerable.call(s,p[i])&&(t[p[i]]=s[p[i]])}return t}Object.create;function __spreadArray(to,from,pack){if(pack||2===arguments.length)for(var ar,i=0,l=from.length;i<l;i++)!ar&&i in from||(ar||(ar=Array.prototype.slice.call(from,0,i)),ar[i]=from[i]);return to.concat(ar||Array.prototype.slice.call(from))}Object.create;"function"==typeof SuppressedError&&SuppressedError;var react=__webpack_require__("./node_modules/react/index.js");function assignRef(ref,value){return"function"==typeof ref?ref(value):ref&&(ref.current=value),ref}var useIsomorphicLayoutEffect="undefined"!=typeof window?react.useLayoutEffect:react.useEffect,currentValues=new WeakMap;function useMergeRefs(refs,defaultValue){var callbackRef=function useCallbackRef(initialValue,callback){var ref=(0,react.useState)((function(){return{value:initialValue,callback,facade:{get current(){return ref.value},set current(value){var last=ref.value;last!==value&&(ref.value=value,ref.callback(value,last))}}}}))[0];return ref.callback=callback,ref.facade}(defaultValue||null,(function(newValue){return refs.forEach((function(ref){return assignRef(ref,newValue)}))}));return useIsomorphicLayoutEffect((function(){var oldValue=currentValues.get(callbackRef);if(oldValue){var prevRefs_1=new Set(oldValue),nextRefs_1=new Set(refs),current_1=callbackRef.current;prevRefs_1.forEach((function(ref){nextRefs_1.has(ref)||assignRef(ref,null)})),nextRefs_1.forEach((function(ref){prevRefs_1.has(ref)||assignRef(ref,current_1)}))}currentValues.set(callbackRef,refs)}),[refs]),callbackRef}function ItoI(a){return a}function innerCreateMedium(defaults,middleware){void 0===middleware&&(middleware=ItoI);var buffer=[],assigned=!1;return{read:function(){if(assigned)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return buffer.length?buffer[buffer.length-1]:defaults},useMedium:function(data){var item=middleware(data,assigned);return buffer.push(item),function(){buffer=buffer.filter((function(x){return x!==item}))}},assignSyncMedium:function(cb){for(assigned=!0;buffer.length;){var cbs=buffer;buffer=[],cbs.forEach(cb)}buffer={push:function(x){return cb(x)},filter:function(){return buffer}}},assignMedium:function(cb){assigned=!0;var pendingQueue=[];if(buffer.length){var cbs=buffer;buffer=[],cbs.forEach(cb),pendingQueue=buffer}var executeQueue=function(){var cbs=pendingQueue;pendingQueue=[],cbs.forEach(cb)},cycle=function(){return Promise.resolve().then(executeQueue)};cycle(),buffer={push:function(x){pendingQueue.push(x),cycle()},filter:function(filter){return pendingQueue=pendingQueue.filter(filter),buffer}}}}}var effectCar=function createSidecarMedium(options){void 0===options&&(options={});var medium=innerCreateMedium(null);return medium.options=__assign({async:!0,ssr:!1},options),medium}(),nothing=function(){},RemoveScroll=react.forwardRef((function(props,parentRef){var ref=react.useRef(null),_a=react.useState({onScrollCapture:nothing,onWheelCapture:nothing,onTouchMoveCapture:nothing}),callbacks=_a[0],setCallbacks=_a[1],forwardProps=props.forwardProps,children=props.children,className=props.className,removeScrollBar=props.removeScrollBar,enabled=props.enabled,shards=props.shards,sideCar=props.sideCar,noIsolation=props.noIsolation,inert=props.inert,allowPinchZoom=props.allowPinchZoom,_b=props.as,Container=void 0===_b?"div":_b,gapMode=props.gapMode,rest=__rest(props,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),SideCar=sideCar,containerRef=useMergeRefs([ref,parentRef]),containerProps=__assign(__assign({},rest),callbacks);return react.createElement(react.Fragment,null,enabled&&react.createElement(SideCar,{sideCar:effectCar,removeScrollBar,shards,noIsolation,inert,setCallbacks,allowPinchZoom:!!allowPinchZoom,lockRef:ref,gapMode}),forwardProps?react.cloneElement(react.Children.only(children),__assign(__assign({},containerProps),{ref:containerRef})):react.createElement(Container,__assign({},containerProps,{className,ref:containerRef}),children))}));RemoveScroll.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},RemoveScroll.classNames={fullWidth:"width-before-scroll-bar",zeroRight:"right-scroll-bar-position"};var currentNonce,SideCar=function(_a){var sideCar=_a.sideCar,rest=__rest(_a,["sideCar"]);if(!sideCar)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var Target=sideCar.read();if(!Target)throw new Error("Sidecar medium not found");return react.createElement(Target,__assign({},rest))};SideCar.isSideCarExport=!0;function makeStyleTag(){if(!document)return null;var tag=document.createElement("style");tag.type="text/css";var nonce=currentNonce||__webpack_require__.nc;return nonce&&tag.setAttribute("nonce",nonce),tag}var stylesheetSingleton=function(){var counter=0,stylesheet=null;return{add:function(style){0==counter&&(stylesheet=makeStyleTag())&&(!function injectStyles(tag,css){tag.styleSheet?tag.styleSheet.cssText=css:tag.appendChild(document.createTextNode(css))}(stylesheet,style),function insertStyleTag(tag){(document.head||document.getElementsByTagName("head")[0]).appendChild(tag)}(stylesheet)),counter++},remove:function(){! --counter&&stylesheet&&(stylesheet.parentNode&&stylesheet.parentNode.removeChild(stylesheet),stylesheet=null)}}},styleSingleton=function(){var sheet,useStyle=(sheet=stylesheetSingleton(),function(styles,isDynamic){react.useEffect((function(){return sheet.add(styles),function(){sheet.remove()}}),[styles&&isDynamic])});return function(_a){var styles=_a.styles,dynamic=_a.dynamic;return useStyle(styles,dynamic),null}},zeroGap={left:0,top:0,right:0,gap:0},parse=function(x){return parseInt(x||"",10)||0},getGapWidth=function(gapMode){if(void 0===gapMode&&(gapMode="margin"),"undefined"==typeof window)return zeroGap;var offsets=function(gapMode){var cs=window.getComputedStyle(document.body),left=cs["padding"===gapMode?"paddingLeft":"marginLeft"],top=cs["padding"===gapMode?"paddingTop":"marginTop"],right=cs["padding"===gapMode?"paddingRight":"marginRight"];return[parse(left),parse(top),parse(right)]}(gapMode),documentWidth=document.documentElement.clientWidth,windowWidth=window.innerWidth;return{left:offsets[0],top:offsets[1],right:offsets[2],gap:Math.max(0,windowWidth-documentWidth+offsets[2]-offsets[0])}},Style=styleSingleton(),lockAttribute="data-scroll-locked",getStyles=function(_a,allowRelative,gapMode,important){var left=_a.left,top=_a.top,right=_a.right,gap=_a.gap;return void 0===gapMode&&(gapMode="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(important,";\n   padding-right: ").concat(gap,"px ").concat(important,";\n  }\n  body[").concat(lockAttribute,"] {\n    overflow: hidden ").concat(important,";\n    overscroll-behavior: contain;\n    ").concat([allowRelative&&"position: relative ".concat(important,";"),"margin"===gapMode&&"\n    padding-left: ".concat(left,"px;\n    padding-top: ").concat(top,"px;\n    padding-right: ").concat(right,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(gap,"px ").concat(important,";\n    "),"padding"===gapMode&&"padding-right: ".concat(gap,"px ").concat(important,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat("right-scroll-bar-position"," {\n    right: ").concat(gap,"px ").concat(important,";\n  }\n  \n  .").concat("width-before-scroll-bar"," {\n    margin-right: ").concat(gap,"px ").concat(important,";\n  }\n  \n  .").concat("right-scroll-bar-position"," .").concat("right-scroll-bar-position"," {\n    right: 0 ").concat(important,";\n  }\n  \n  .").concat("width-before-scroll-bar"," .").concat("width-before-scroll-bar"," {\n    margin-right: 0 ").concat(important,";\n  }\n  \n  body[").concat(lockAttribute,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(gap,"px;\n  }\n")},getCurrentUseCounter=function(){var counter=parseInt(document.body.getAttribute(lockAttribute)||"0",10);return isFinite(counter)?counter:0},RemoveScrollBar=function(_a){var noRelative=_a.noRelative,noImportant=_a.noImportant,_b=_a.gapMode,gapMode=void 0===_b?"margin":_b;react.useEffect((function(){return document.body.setAttribute(lockAttribute,(getCurrentUseCounter()+1).toString()),function(){var newCounter=getCurrentUseCounter()-1;newCounter<=0?document.body.removeAttribute(lockAttribute):document.body.setAttribute(lockAttribute,newCounter.toString())}}),[]);var gap=react.useMemo((function(){return getGapWidth(gapMode)}),[gapMode]);return react.createElement(Style,{styles:getStyles(gap,!noRelative,gapMode,noImportant?"":"!important")})},passiveSupported=!1;if("undefined"!=typeof window)try{var options=Object.defineProperty({},"passive",{get:function(){return passiveSupported=!0,!0}});window.addEventListener("test",options,options),window.removeEventListener("test",options,options)}catch(err){passiveSupported=!1}var nonPassive=!!passiveSupported&&{passive:!1},elementCanBeScrolled=function(node,overflow){if(!(node instanceof Element))return!1;var styles=window.getComputedStyle(node);return"hidden"!==styles[overflow]&&!(styles.overflowY===styles.overflowX&&!function(node){return"TEXTAREA"===node.tagName}(node)&&"visible"===styles[overflow])},locationCouldBeScrolled=function(axis,node){var ownerDocument=node.ownerDocument,current=node;do{if("undefined"!=typeof ShadowRoot&&current instanceof ShadowRoot&&(current=current.host),elementCouldBeScrolled(axis,current)){var _a=getScrollVariables(axis,current);if(_a[1]>_a[2])return!0}current=current.parentNode}while(current&&current!==ownerDocument.body);return!1},elementCouldBeScrolled=function(axis,node){return"v"===axis?function(node){return elementCanBeScrolled(node,"overflowY")}(node):function(node){return elementCanBeScrolled(node,"overflowX")}(node)},getScrollVariables=function(axis,node){return"v"===axis?[(_a=node).scrollTop,_a.scrollHeight,_a.clientHeight]:function(_a){return[_a.scrollLeft,_a.scrollWidth,_a.clientWidth]}(node);var _a},getTouchXY=function(event){return"changedTouches"in event?[event.changedTouches[0].clientX,event.changedTouches[0].clientY]:[0,0]},getDeltaXY=function(event){return[event.deltaX,event.deltaY]},extractRef=function(ref){return ref&&"current"in ref?ref.current:ref},generateStyle=function(id){return"\n  .block-interactivity-".concat(id," {pointer-events: none;}\n  .allow-interactivity-").concat(id," {pointer-events: all;}\n")},idCounter=0,lockStack=[];function getOutermostShadowParent(node){for(var shadowParent=null;null!==node;)node instanceof ShadowRoot&&(shadowParent=node.host,node=node.host),node=node.parentNode;return shadowParent}const sidecar=function exportSidecar(medium,exported){return medium.useMedium(exported),SideCar}(effectCar,(function RemoveScrollSideCar(props){var shouldPreventQueue=react.useRef([]),touchStartRef=react.useRef([0,0]),activeAxis=react.useRef(),id=react.useState(idCounter++)[0],Style=react.useState(styleSingleton)[0],lastProps=react.useRef(props);react.useEffect((function(){lastProps.current=props}),[props]),react.useEffect((function(){if(props.inert){document.body.classList.add("block-interactivity-".concat(id));var allow_1=__spreadArray([props.lockRef.current],(props.shards||[]).map(extractRef),!0).filter(Boolean);return allow_1.forEach((function(el){return el.classList.add("allow-interactivity-".concat(id))})),function(){document.body.classList.remove("block-interactivity-".concat(id)),allow_1.forEach((function(el){return el.classList.remove("allow-interactivity-".concat(id))}))}}}),[props.inert,props.lockRef.current,props.shards]);var shouldCancelEvent=react.useCallback((function(event,parent){if("touches"in event&&2===event.touches.length||"wheel"===event.type&&event.ctrlKey)return!lastProps.current.allowPinchZoom;var currentAxis,touch=getTouchXY(event),touchStart=touchStartRef.current,deltaX="deltaX"in event?event.deltaX:touchStart[0]-touch[0],deltaY="deltaY"in event?event.deltaY:touchStart[1]-touch[1],target=event.target,moveDirection=Math.abs(deltaX)>Math.abs(deltaY)?"h":"v";if("touches"in event&&"h"===moveDirection&&"range"===target.type)return!1;var canBeScrolledInMainDirection=locationCouldBeScrolled(moveDirection,target);if(!canBeScrolledInMainDirection)return!0;if(canBeScrolledInMainDirection?currentAxis=moveDirection:(currentAxis="v"===moveDirection?"h":"v",canBeScrolledInMainDirection=locationCouldBeScrolled(moveDirection,target)),!canBeScrolledInMainDirection)return!1;if(!activeAxis.current&&"changedTouches"in event&&(deltaX||deltaY)&&(activeAxis.current=currentAxis),!currentAxis)return!0;var cancelingAxis=activeAxis.current||currentAxis;return function(axis,endTarget,event,sourceDelta,noOverscroll){var directionFactor=function(axis,direction){return"h"===axis&&"rtl"===direction?-1:1}(axis,window.getComputedStyle(endTarget).direction),delta=directionFactor*sourceDelta,target=event.target,targetInLock=endTarget.contains(target),shouldCancelScroll=!1,isDeltaPositive=delta>0,availableScroll=0,availableScrollTop=0;do{var _a=getScrollVariables(axis,target),position=_a[0],elementScroll=_a[1]-_a[2]-directionFactor*position;(position||elementScroll)&&elementCouldBeScrolled(axis,target)&&(availableScroll+=elementScroll,availableScrollTop+=position),target=target instanceof ShadowRoot?target.host:target.parentNode}while(!targetInLock&&target!==document.body||targetInLock&&(endTarget.contains(target)||endTarget===target));return(isDeltaPositive&&(noOverscroll&&Math.abs(availableScroll)<1||!noOverscroll&&delta>availableScroll)||!isDeltaPositive&&(noOverscroll&&Math.abs(availableScrollTop)<1||!noOverscroll&&-delta>availableScrollTop))&&(shouldCancelScroll=!0),shouldCancelScroll}(cancelingAxis,parent,event,"h"===cancelingAxis?deltaX:deltaY,!0)}),[]),shouldPrevent=react.useCallback((function(_event){var event=_event;if(lockStack.length&&lockStack[lockStack.length-1]===Style){var delta="deltaY"in event?getDeltaXY(event):getTouchXY(event),sourceEvent=shouldPreventQueue.current.filter((function(e){return e.name===event.type&&(e.target===event.target||event.target===e.shadowParent)&&(x=e.delta,y=delta,x[0]===y[0]&&x[1]===y[1]);var x,y}))[0];if(sourceEvent&&sourceEvent.should)event.cancelable&&event.preventDefault();else if(!sourceEvent){var shardNodes=(lastProps.current.shards||[]).map(extractRef).filter(Boolean).filter((function(node){return node.contains(event.target)}));(shardNodes.length>0?shouldCancelEvent(event,shardNodes[0]):!lastProps.current.noIsolation)&&event.cancelable&&event.preventDefault()}}}),[]),shouldCancel=react.useCallback((function(name,delta,target,should){var event={name,delta,target,should,shadowParent:getOutermostShadowParent(target)};shouldPreventQueue.current.push(event),setTimeout((function(){shouldPreventQueue.current=shouldPreventQueue.current.filter((function(e){return e!==event}))}),1)}),[]),scrollTouchStart=react.useCallback((function(event){touchStartRef.current=getTouchXY(event),activeAxis.current=void 0}),[]),scrollWheel=react.useCallback((function(event){shouldCancel(event.type,getDeltaXY(event),event.target,shouldCancelEvent(event,props.lockRef.current))}),[]),scrollTouchMove=react.useCallback((function(event){shouldCancel(event.type,getTouchXY(event),event.target,shouldCancelEvent(event,props.lockRef.current))}),[]);react.useEffect((function(){return lockStack.push(Style),props.setCallbacks({onScrollCapture:scrollWheel,onWheelCapture:scrollWheel,onTouchMoveCapture:scrollTouchMove}),document.addEventListener("wheel",shouldPrevent,nonPassive),document.addEventListener("touchmove",shouldPrevent,nonPassive),document.addEventListener("touchstart",scrollTouchStart,nonPassive),function(){lockStack=lockStack.filter((function(inst){return inst!==Style})),document.removeEventListener("wheel",shouldPrevent,nonPassive),document.removeEventListener("touchmove",shouldPrevent,nonPassive),document.removeEventListener("touchstart",scrollTouchStart,nonPassive)}}),[]);var removeScrollBar=props.removeScrollBar,inert=props.inert;return react.createElement(react.Fragment,null,inert?react.createElement(Style,{styles:generateStyle(id)}):null,removeScrollBar?react.createElement(RemoveScrollBar,{gapMode:props.gapMode}):null)}));var ReactRemoveScroll=react.forwardRef((function(props,ref){return react.createElement(RemoveScroll,__assign({},props,{ref,sideCar:sidecar}))}));ReactRemoveScroll.classNames=RemoveScroll.classNames;const Combination=ReactRemoveScroll}}]);