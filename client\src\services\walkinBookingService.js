import axiosInstance from '../api/axiosInstance';

/**
 * 获取当日现场预约列表
 * @returns {Promise<Array>} - 当日预约列表
 */
export const getTodayWalkinBookings = async () => {
  try {
    const response = await axiosInstance.get('/api/bookings/today');
    return response.data;
  } catch (error) {
    console.error('获取今日现场预约失败:', error.response?.data || error);
    throw error.response?.data || error;
  }
};

/**
 * 获取特定日期的现场预约列表
 * @param {string} date - 日期字符串，格式 YYYY-MM-DD
 * @returns {Promise<Array>} - 指定日期的预约列表
 */
export const getWalkinBookingsByDate = async (date) => {
  try {
    const response = await axiosInstance.get(`/api/bookings/date/${date}`);
    return response.data;
  } catch (error) {
    console.error('获取指定日期现场预约失败:', error.response?.data || error);
    throw error.response?.data || error;
  }
};

/**
 * 创建新的现场预约
 * @param {Object} bookingData - 预约数据
 * @returns {Promise<Object>} - 创建的预约对象
 */
export const createWalkinBooking = async (bookingData) => {
  try {
    const response = await axiosInstance.post('/api/bookings/walkin', bookingData);
    return response.data;
  } catch (error) {
    console.error('创建现场预约失败:', error.response?.data || error);
    throw error.response?.data || error;
  }
};

/**
 * 更新现场预约状态
 * @param {string} bookingId - 预约ID
 * @param {string} status - 新状态
 * @returns {Promise<Object>} - 更新后的预约对象
 */
export const updateWalkinBookingStatus = async (bookingId, status) => {
  try {
    const response = await axiosInstance.patch(`/api/bookings/walkin/${bookingId}/status`, { status });
    return response.data;
  } catch (error) {
    console.error('更新现场预约状态失败:', error.response?.data || error);
    throw error.response?.data || error;
  }
};

/**
 * 取消现场预约
 * @param {string} bookingId - 预约ID
 * @param {string} reason - 取消原因
 * @returns {Promise<Object>} - 取消后的预约对象
 */
export const cancelWalkinBooking = async (bookingId, reason) => {
  try {
    const response = await axiosInstance.post(`/api/bookings/walkin/${bookingId}/cancel`, { reason });
    return response.data;
  } catch (error) {
    console.error('取消现场预约失败:', error.response?.data || error);
    throw error.response?.data || error;
  }
};

/**
 * 客户签到
 * @param {string} bookingId - 预约ID
 * @returns {Promise<Object>} - 签到后的预约对象
 */
export const checkInWalkinBooking = async (bookingId) => {
  try {
    const response = await axiosInstance.post(`/api/bookings/walkin/${bookingId}/checkin`);
    return response.data;
  } catch (error) {
    console.error('客户签到失败:', error.response?.data || error);
    throw error.response?.data || error;
  }
};

/**
 * 获取服务类型列表
 * @returns {Promise<Array>} - 服务类型列表
 */
export const getServiceTypes = async () => {
  try {
    const response = await axiosInstance.get('/api/services/types');
    return response.data;
  } catch (error) {
    console.error('获取服务类型失败:', error.response?.data || error);
    throw error.response?.data || error;
  }
};

/**
 * 获取可用时间段
 * @param {string} date - 日期字符串，格式 YYYY-MM-DD
 * @param {string} serviceId - 服务ID
 * @returns {Promise<Array>} - 可用时间段列表
 */
export const getAvailableTimeSlots = async (date, serviceId) => {
  try {
    const response = await axiosInstance.get('/api/bookings/available-slots', {
      params: { date, serviceId }
    });
    return response.data;
  } catch (error) {
    console.error('获取可用时间段失败:', error.response?.data || error);
    throw error.response?.data || error;
  }
};

/**
 * 获取当前队列状态
 * @returns {Promise<Object>} - 队列状态信息
 */
export const getQueueStatus = async () => {
  try {
    const response = await axiosInstance.get('/api/bookings/queue-status');
    return response.data;
  } catch (error) {
    console.error('获取队列状态失败:', error.response?.data || error);
    throw error.response?.data || error;
  }
};

/**
 * 获取预计等待时间
 * @param {string} serviceId - 服务ID
 * @returns {Promise<Object>} - 等待时间信息
 */
export const getEstimatedWaitTime = async (serviceId) => {
  try {
    const response = await axiosInstance.get('/api/bookings/wait-time', {
      params: { serviceId }
    });
    return response.data;
  } catch (error) {
    console.error('获取预计等待时间失败:', error.response?.data || error);
    throw error.response?.data || error;
  }
};

const walkinBookingService = {
  getTodayWalkinBookings,
  getWalkinBookingsByDate,
  createWalkinBooking,
  updateWalkinBookingStatus,
  cancelWalkinBooking,
  checkInWalkinBooking,
  getServiceTypes,
  getAvailableTimeSlots,
  getQueueStatus,
  getEstimatedWaitTime
};

export default walkinBookingService;