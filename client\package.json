{"name": "client", "version": "0.1.0", "private": true, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@fullcalendar/daygrid": "^6.1.17", "@fullcalendar/interaction": "^6.1.17", "@fullcalendar/list": "^6.1.17", "@fullcalendar/moment": "^6.1.17", "@fullcalendar/scrollgrid": "^6.1.17", "@fullcalendar/timegrid": "^6.1.17", "@hookform/resolvers": "^5.0.1", "@nextui-org/react": "^2.4.8", "@number-flow/react": "^0.5.10", "@radix-ui/react-accordion": "^1.2.10", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-collapsible": "^1.1.10", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.12", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toast": "^1.2.13", "@radix-ui/react-toggle": "^1.1.8", "@radix-ui/react-tooltip": "^1.2.6", "@sentry/react": "^9.30.0", "@sentry/tracing": "^7.120.3", "@stripe/react-stripe-js": "^2.9.0", "@stripe/stripe-js": "^2.4.0", "@supabase/supabase-js": "^2.49.4", "@tabler/icons-react": "^3.33.0", "@tanstack/react-query": "^5.76.0", "@tanstack/react-table": "^8.21.3", "@testing-library/dom": "^10.4.0", "aos": "^2.3.4", "axios": "^1.9.0", "axios-cache-interceptor": "^1.8.0", "canvas-confetti": "^1.9.3", "chart.js": "^4.4.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "embla-carousel-react": "^8.6.0", "express-rate-limit": "^6.7.0", "framer-motion": "^12.18.1", "i18next": "^25.2.1", "i18next-browser-languagedetector": "^8.1.0", "leaflet": "^1.9.4", "leaflet-routing-machine": "^3.2.12", "lucide-react": "^0.509.0", "moment": "^2.30.1", "next-themes": "^0.4.6", "react": "^18.3.1", "react-awesome-reveal": "^4.3.1", "react-chartjs-2": "^5.3.0", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-helmet": "^6.1.0", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.56.4", "react-i18next": "^15.5.2", "react-icon-cloud": "^4.1.7", "react-icons": "^5.5.0", "react-intersection-observer": "^9.16.0", "react-leaflet": "^4.2.1", "react-particles": "^2.12.2", "react-router-dom": "^6.30.1", "react-scripts": "5.0.1", "react-spring": "^9.7.4", "react-toastify": "^11.0.5", "reactflow": "^11.11.4", "recharts": "^2.15.3", "redaxios": "^0.5.1", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "tsparticles-basic": "^2.12.0", "tsparticles-engine": "^2.12.0", "validate-color": "^2.2.4", "web-vitals": "^2.1.4", "zod": "^3.25.23"}, "scripts": {"start": "craco start", "build": "craco build", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "storybook:test": "test-storybook", "eject": "react-scripts eject", "lint": "eslint \"src/**/*.{js,jsx,ts,tsx}\"", "lint:fix": "eslint \"src/**/*.{js,jsx,ts,tsx}\" --fix", "fix:all": "node scripts/run-all-fixers.js", "fix:eslint": "node scripts/fix-eslint-issues.js", "fix:unused-imports": "node scripts/fix-unused-imports.js", "fix:hook-deps": "node scripts/fix-hook-deps.js", "fix:clean-imports": "node scripts/clean-unused-imports.js", "fix:add-ignores": "node scripts/add-eslint-ignores.js", "fix:advanced-unused": "node scripts/advanced-fix-unused.js", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "test-storybook": "test-storybook", "ci-test-storybook": "concurrently -k -s first -n 'SB,TEST' 'http-server storybook-static --port 6006 --silent' 'wait-on tcp:6006 && test-storybook --url http://localhost:6006 --junit'"}, "eslintConfig": {"extends": ["react-app", "react-app/jest", "plugin:storybook/recommended"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "description": "This project was bootstrapped with [Create React App](https://github.com/facebook/create-react-app).", "keywords": [], "author": "", "license": "ISC", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "devDependencies": {"@craco/craco": "^7.1.0", "@playwright/test": "^1.52.0", "@storybook/addon-a11y": "^8.6.12", "@storybook/addon-essentials": "^8.6.12", "@storybook/addon-interactions": "^8.6.12", "@storybook/addon-onboarding": "^8.6.12", "@storybook/blocks": "^8.6.12", "@storybook/cli": "^8.6.12", "@storybook/preset-create-react-app": "^8.6.12", "@storybook/react": "^8.6.12", "@storybook/react-webpack5": "^8.6.12", "@storybook/test": "^8.6.12", "@storybook/test-runner": "0.22.0", "@storybook/testing-library": "^0.2.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/aria-query": "^5.0.4", "@types/canvas-confetti": "^1.9.0", "@types/cross-spawn": "^6.0.6", "@types/jest": "^29.5.14", "@types/mdx-js__react": "^1.5.8", "@types/react-dom": "^19.1.5", "@types/react-icon-base": "^2.1.6", "@types/react-toastify": "^4.0.2", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.14", "concurrently": "^8.2.2", "eslint-plugin-storybook": "^0.12.0", "glob": "^11.0.2", "http-proxy-middleware": "^3.0.5", "http-server": "^14.1.1", "identity-obj-proxy": "^3.0.0", "json-server": "^1.0.0-beta.3", "msw": "^1.3.5", "playwright": "^1.38.0", "postcss": "^8.4.23", "postcss-import": "^16.1.0", "prop-types": "^15.8.1", "sass": "^1.89.1", "start-server-and-test": "^2.0.11", "storybook": "^8.6.12", "tailwindcss": "^3.3.0", "ts-jest": "^29.3.4", "typescript": "^5.4.3", "wait-on": "^7.0.1", "webpack": "^5.99.8"}, "msw": {"workerDirectory": "public"}, "overrides": {"react": "18.3.1", "react-dom": "18.3.1", "@types/react": "18.3.1", "@types/react-dom": "18.3.1"}, "resolutions": {"react": "18.3.1", "react-dom": "18.3.1", "@types/react": "18.3.1", "@types/react-dom": "18.3.1"}, "peerDependenciesMeta": {"react": {"optional": false}, "react-dom": {"optional": false}}}