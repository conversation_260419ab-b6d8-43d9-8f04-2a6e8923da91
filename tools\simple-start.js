/**
 * 简化的服务启动脚本 - 用于测试
 */
const { spawn } = require('child_process');
const path = require('path');

// 颜色配置
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

console.log(`${colors.bright}${colors.cyan}`);
console.log('╔══════════════════════════════════════════╗');
console.log('║       iTeraBiz 简化启动脚本 (测试)         ║');
console.log('╚══════════════════════════════════════════╝');
console.log(`${colors.reset}\n`);

// 启动 API Gateway（最稳定的服务）
console.log(`${colors.blue}[启动] API Gateway...${colors.reset}`);
const apiGateway = spawn('npm', ['run', 'dev'], {
  cwd: path.join(__dirname, '..', 'api-gateway'),
  stdio: 'inherit',
  shell: true
});

apiGateway.on('error', (err) => {
  console.error(`${colors.red}[错误] API Gateway 启动失败:${colors.reset}`, err);
});

// 5秒后启动客户端
setTimeout(() => {
  console.log(`${colors.green}[启动] React 客户端...${colors.reset}`);
  const client = spawn('npm', ['start'], {
    cwd: path.join(__dirname, '..', 'client'),
    stdio: 'inherit',
    shell: true
  });

  client.on('error', (err) => {
    console.error(`${colors.red}[错误] 客户端启动失败:${colors.reset}`, err);
  });
}, 5000);

// 优雅关闭
process.on('SIGINT', () => {
  console.log(`\n${colors.yellow}[信息] 正在关闭服务...${colors.reset}`);
  process.exit(0);
});

console.log(`${colors.cyan}[信息] 启动脚本运行中... 按 Ctrl+C 停止${colors.reset}`);
console.log(`${colors.cyan}[信息] API Gateway: http://localhost:3001${colors.reset}`);
console.log(`${colors.cyan}[信息] 客户端: http://localhost:3000${colors.reset}`); 