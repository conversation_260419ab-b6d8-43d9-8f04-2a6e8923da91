import React, { useState, useCallback, useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { 
  Eye, 
  Loader2, 
  MessageSquare, 
  Bot, 
  User, 
  RefreshCw,
  Sparkles,
  Clock
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { debounce } from 'lodash'
import type { SmartAiConfig } from '@/hooks/useSmartAiConfig'

interface LivePreviewPanelProps {
  config: SmartAiConfig;
  className?: string
}

interface PreviewMessage {
  id: string;
  type: 'user' | 'ai'
  content: string;
  timestamp: Date;
  confidence?: number
}

interface PreviewMetrics {
  responseTime: number;
  confidence: number;
  tokenCount: number;
  escalationRisk: 'low' | 'medium' | 'high'
}

const LivePreviewPanel: React.FC<LivePreviewPanelProps> = ({ config, className }) => {
  const { t } = useTranslation();
  const [testMessage, setTestMessage] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [messages, setMessages] = useState<PreviewMessage[]>([]);
  const [currentMetrics, setCurrentMetrics] = useState<PreviewMetrics | null>(null);

  // 预设测试消息
  const testMessageTemplates = [
    'Hello, I need help with my order',
    'What are your business hours?',
    'I want to return a product',
    'Can you help me with technical support?',
    'I have a complaint about your service'
  ];

  // 模拟AI响应生成
  const generateAIResponse = useCallback(async (userMessage: string): Promise<{
    response: string;
    metrics: PreviewMetrics
}> => {
    // 模拟API延迟
    const delay = Math.random() * 1000 + 500;
    await new Promise(resolve => setTimeout(resolve, delay));

    const style = config.responseStyle || 'professional'
    const creativity = config.temperature || 0.5;
    const maxTokens = config.maxTokens || 200;
    
    // 基于配置生成不同风格的回复
    let response = ''
    let confidence = 0.8;
    
    if (userMessage.toLowerCase().includes('order')) {
      if (style === 'professional') {
        response = 'Thank you for contacting us regarding your order. I\'d be happy to assist you with any order-related inquiries. Could you please provide your order number so I can look up the details for you?'
      } else if (style === 'friendly') {
        response = 'Hi there! I\'d love to help you with your order! 😊 Could you share your order number with me so I can check what\'s going on?'
      } else {
        response = 'Hey! What\'s up with your order? Drop me the order number and I\'ll sort it out for you.'
      }
      confidence = 0.9
    } else if (userMessage.toLowerCase().includes('hours')) {
      if (style === 'professional') {
        response = 'Our business hours are Monday through Friday, 9:00 AM to 6:00 PM EST. We\'re closed on weekends and major holidays. Is there anything specific I can help you with today?'
      } else if (style === 'friendly') {
        response = 'We\'re open Monday to Friday, 9 AM to 6 PM EST! We take weekends off to recharge. 😄 What can I help you with while we\'re here?'
      } else {
        response = 'We\'re open 9-6, Mon-Fri. Weekends we\'re out! What do you need?'
      }
      confidence = 0.95
    } else if (userMessage.toLowerCase().includes('return')) {
      if (style === 'professional') {
        response = 'I understand you\'d like to return a product. Our return policy allows returns within 30 days of purchase. To process your return, I\'ll need your order number and the reason for the return.'
      } else if (style === 'friendly') {
        response = 'No problem! Returns are totally doable within 30 days. 📦 Just need your order number and a quick reason why you\'re returning it, and we\'ll get you sorted!'
      } else {
        response = 'Sure thing! 30 days to return stuff. Need your order number and why you\'re returning it.'
      }
      confidence = 0.85
    } else {
      // 通用回复
      if (style === 'professional') {
        response = 'Thank you for your message. I\'m here to assist you with any questions or concerns you may have. Could you please provide more details about how I can help you today?'
      } else if (style === 'friendly') {
        response = 'Thanks for reaching out! I\'m here to help with whatever you need. 😊 Could you tell me a bit more about what you\'re looking for?'
      } else {
        response = 'Hey! What can I help you with? Give me some more details and I\'ll figure it out.'
      }
      confidence = 0.7
    }

    // 根据创意度调整回复
    if (creativity > 0.7) {
      response += ' Is there anything else I can help you with while we\'re chatting?'
    }

    // 限制token长度
    const words = response.split(' ');
    const estimatedTokens = Math.ceil(words.length * 1.3);
    if (estimatedTokens > maxTokens) {
      const maxWords = Math.floor(maxTokens / 1.3);
      response = words.slice(0, maxWords).join(' ') + '...'
    }

    // 计算升级风险
    let escalationRisk: 'low' | 'medium' | 'high' = 'low';
    if (userMessage.toLowerCase().includes('complaint') || userMessage.toLowerCase().includes('problem')) {
      escalationRisk = 'high';
      confidence = Math.max(0.3, confidence - 0.3);
    } else if (userMessage.toLowerCase().includes('help') || userMessage.toLowerCase().includes('support')) {
      escalationRisk = 'medium';
      confidence = Math.max(0.5, confidence - 0.1);
    }

    // 检查是否需要转人工
    const escalationThreshold = config.humanEscalationThreshold || 0.7;
    if (confidence < escalationThreshold) {
      response = config.humanEscalationMessage || 'I\'d like to connect you with a human agent who can better assist you with this matter. Please hold on while I transfer you.';
      escalationRisk = 'high';
    }

    return {
      response,
      metrics: {
        responseTime: delay,
        confidence,
        tokenCount: Math.ceil(response.split(' ').length * 1.3),
        escalationRisk
      }
    }
  }, [config]);

  // 防抖的消息发送
  const debouncedSendMessage = useMemo(
    () => debounce(async (message: string) => {
      if (!message.trim()) return;

      const userMessage: PreviewMessage = {
        id: Date.now().toString(), type: 'user', content: message, timestamp: new Date()
      };

      setMessages(prev => [...prev, userMessage]);
      setIsGenerating(true);

      try {
        const { response, metrics } = await generateAIResponse(message);
        
        const aiMessage: PreviewMessage = {
          id: (Date.now() + 1).toString(), type: 'ai', content: response, timestamp: new Date(), confidence: metrics.confidence
        };

        setMessages(prev => [...prev, aiMessage]);
        setCurrentMetrics(metrics);
      } catch (error) {
        console.error('Error generating AI response:', error);
      } finally {
        setIsGenerating(false);
      }
    }, 300),
    [generateAIResponse]
  );

  const handleSendMessage = useCallback(() => {
    if (testMessage.trim()) {
      debouncedSendMessage(testMessage);
      setTestMessage('');
    }
  }, [testMessage, debouncedSendMessage]);

  const handleTemplateClick = useCallback((template: string) => {
    setTestMessage(template);
    debouncedSendMessage(template);
  }, [debouncedSendMessage]);

  const clearConversation = useCallback(() => {
    setMessages([]);
    setCurrentMetrics(null);
  }, []);

  const getEscalationRiskColor = (risk: 'low' | 'medium' | 'high') => {
    switch (risk) {
      case 'low': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'high': return 'bg-red-100 text-red-800';
    }
  };

  return (
    <div className={cn("space-y-4", className)}>
      {/* 预览面板 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Eye className="h-4 w-4" />
              {t('agents.smartConfig.preview.title')}
            </CardTitle>
            <Button variant="outline" size="sm" onClick={clearConversation}>
              <RefreshCw className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* 对话区域 */}
          <ScrollArea className="h-64 w-full border rounded-md p-4">
            {messages.length === 0 ? (
              <div className="flex items-center justify-center h-full text-muted-foreground">
                <div className="text-center">
                  <MessageSquare className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">{t('agents.smartConfig.preview.placeholder')}</p>
                </div>
              </div>
            ) : (
              <div className="space-y-3">
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={cn(
                      "flex gap-3",
                      message.type === 'user' ? 'justify-end' : 'justify-start'
                    )}
                  >
                    <div
                      className={cn(
                        "max-w-[80%] rounded-lg px-3 py-2 text-sm",
                        message.type === 'user'
                          ? 'bg-primary text-primary-foreground'
                          : 'bg-muted'
                      )}
                    >
                      <div className="flex items-start gap-2">
                        {message.type === 'ai' && <Bot className="h-4 w-4 mt-0.5 flex-shrink-0" />}
                        {message.type === 'user' && <User className="h-4 w-4 mt-0.5 flex-shrink-0" />}
                        <div>
                          <p>{message.content}</p>
                          {message.confidence && (
                            <div className="flex items-center gap-1 mt-1">
                              <Badge variant="secondary" className="text-xs">
                                {Math.round(message.confidence * 100)}% confidence
                              </Badge>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
                {isGenerating && (
                  <div className="flex justify-start">
                    <div className="bg-muted rounded-lg px-3 py-2 text-sm">
                      <div className="flex items-center gap-2">
                        <Bot className="h-4 w-4" />
                        <Loader2 className="h-4 w-4 animate-spin" />
                        <span>{t('agents.smartConfig.preview.generating')}</span>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}
          </ScrollArea>

          {/* 输入区域 */}
          <div className="flex gap-2">
            <Input
              value={testMessage}
              onChange={(e) => setTestMessage(e.target.value)}
              placeholder={t('agents.smartConfig.preview.testMessage')}
              onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
            />
            <Button onClick={handleSendMessage} disabled={!testMessage.trim() || isGenerating}>
              <MessageSquare className="h-4 w-4" />
            </Button>
          </div>

          {/* 快速测试模板 */}
          <div className="space-y-2">
            <p className="text-xs text-muted-foreground">Quick test messages:</p>
            <div className="flex flex-wrap gap-2">
              {testMessageTemplates.map((template, index) => (
                <Button
                  key={index}
                  variant="outline"
                  size="sm"
                  onClick={() => handleTemplateClick(template)}
                  className="text-xs"
                >
                  {template}
                </Button>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 性能指标 */}
      {currentMetrics && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Sparkles className="h-4 w-4" />
              Response Metrics
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-1">
                <p className="text-xs text-muted-foreground">Response Time</p>
                <div className="flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  <span className="text-sm font-medium">{Math.round(currentMetrics.responseTime)}ms</span>
                </div>
              </div>
              <div className="space-y-1">
                <p className="text-xs text-muted-foreground">Confidence</p>
                <span className="text-sm font-medium">{Math.round(currentMetrics.confidence * 100)}%</span>
              </div>
              <div className="space-y-1">
                <p className="text-xs text-muted-foreground">Token Count</p>
                <span className="text-sm font-medium">{currentMetrics.tokenCount}</span>
              </div>
              <div className="space-y-1">
                <p className="text-xs text-muted-foreground">Escalation Risk</p>
                <Badge className={getEscalationRiskColor(currentMetrics.escalationRisk)}>
                  {currentMetrics.escalationRisk}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default LivePreviewPanel;
