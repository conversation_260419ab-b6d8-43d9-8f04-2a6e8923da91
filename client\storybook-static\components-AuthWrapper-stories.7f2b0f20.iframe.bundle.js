/*! For license information please see components-AuthWrapper-stories.7f2b0f20.iframe.bundle.js.LICENSE.txt */
(self.webpackChunkclient=self.webpackChunkclient||[]).push([[619],{"./node_modules/classnames/index.js":(module,exports)=>{var __WEBPACK_AMD_DEFINE_RESULT__;!function(){"use strict";var hasOwn={}.hasOwnProperty;function classNames(){for(var classes="",i=0;i<arguments.length;i++){var arg=arguments[i];arg&&(classes=appendClass(classes,parseValue(arg)))}return classes}function parseValue(arg){if("string"==typeof arg||"number"==typeof arg)return arg;if("object"!=typeof arg)return"";if(Array.isArray(arg))return classNames.apply(null,arg);if(arg.toString!==Object.prototype.toString&&!arg.toString.toString().includes("[native code]"))return arg.toString();var classes="";for(var key in arg)hasOwn.call(arg,key)&&arg[key]&&(classes=appendClass(classes,key));return classes}function appendClass(value,newClass){return newClass?value?value+" "+newClass:value+newClass:value}module.exports?(classNames.default=classNames,module.exports=classNames):void 0===(__WEBPACK_AMD_DEFINE_RESULT__=function(){return classNames}.apply(exports,[]))||(module.exports=__WEBPACK_AMD_DEFINE_RESULT__)}()},"./node_modules/react-bootstrap/esm/Spinner.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{"use strict";__webpack_require__.d(__webpack_exports__,{A:()=>__WEBPACK_DEFAULT_EXPORT__});var classnames__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("./node_modules/classnames/index.js"),classnames__WEBPACK_IMPORTED_MODULE_0___default=__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_0__),react__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__("./node_modules/react/index.js"),_ThemeProvider__WEBPACK_IMPORTED_MODULE_3__=__webpack_require__("./node_modules/react-bootstrap/esm/ThemeProvider.js"),react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__=__webpack_require__("./node_modules/react/jsx-runtime.js");const Spinner=react__WEBPACK_IMPORTED_MODULE_1__.forwardRef((({bsPrefix,variant,animation="border",size,as:Component="div",className,...props},ref)=>{const bsSpinnerPrefix=`${bsPrefix=(0,_ThemeProvider__WEBPACK_IMPORTED_MODULE_3__.oU)(bsPrefix,"spinner")}-${animation}`;return(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Component,{ref,...props,className:classnames__WEBPACK_IMPORTED_MODULE_0___default()(className,bsSpinnerPrefix,size&&`${bsSpinnerPrefix}-${size}`,variant&&`text-${variant}`)})}));Spinner.displayName="Spinner";const __WEBPACK_DEFAULT_EXPORT__=Spinner},"./node_modules/react-bootstrap/esm/ThemeProvider.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{"use strict";__webpack_require__.d(__webpack_exports__,{Jm:()=>useBootstrapMinBreakpoint,Wz:()=>useIsRTL,gy:()=>useBootstrapBreakpoints,oU:()=>useBootstrapPrefix});var react__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("./node_modules/react/index.js");__webpack_require__("./node_modules/react/jsx-runtime.js");const DEFAULT_BREAKPOINTS=["xxl","xl","lg","md","sm","xs"],ThemeContext=react__WEBPACK_IMPORTED_MODULE_0__.createContext({prefixes:{},breakpoints:DEFAULT_BREAKPOINTS,minBreakpoint:"xs"}),{Consumer,Provider}=ThemeContext;function useBootstrapPrefix(prefix,defaultPrefix){const{prefixes}=(0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(ThemeContext);return prefix||prefixes[defaultPrefix]||defaultPrefix}function useBootstrapBreakpoints(){const{breakpoints}=(0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(ThemeContext);return breakpoints}function useBootstrapMinBreakpoint(){const{minBreakpoint}=(0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(ThemeContext);return minBreakpoint}function useIsRTL(){const{dir}=(0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(ThemeContext);return"rtl"===dir}},"./src/components/AuthWrapper.stories.jsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{"use strict";__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{Authenticated:()=>Authenticated,Default:()=>Default,Loading:()=>Loading,Unauthenticated:()=>Unauthenticated,__namedExportsOrder:()=>__namedExportsOrder,default:()=>AuthWrapper_stories});__webpack_require__("./node_modules/react/index.js");var chunk_AYJ5UCUI=__webpack_require__("./node_modules/react-router/dist/development/chunk-AYJ5UCUI.mjs"),AuthContext=__webpack_require__("./src/context/AuthContext.js"),Spinner=__webpack_require__("./node_modules/react-bootstrap/esm/Spinner.js"),jsx_runtime=__webpack_require__("./node_modules/react/jsx-runtime.js");const AuthWrapper=_ref=>{let{children}=_ref;const{isAuthenticated,loading}=(0,AuthContext.As)();console.log(`AuthWrapper: loading=${loading}, isAuthenticated=${isAuthenticated}`);if(window.navigator.userAgent.includes("Storybook")||!0)return console.log("AuthWrapper: 检测到 Storybook 环境，跳过认证检查"),children;return(()=>{try{const bypassAuthInDev="true"===localStorage.getItem("bypassAuthInDev")&&!1,usingMockData="true"===localStorage.getItem("useMockData")||"localhost"===window.location.hostname&&"3001"===window.location.port,hasAuthBypassParam=window.location.search.includes("skipAuth=true");return bypassAuthInDev||usingMockData&&hasAuthBypassParam}catch(error){return console.error("检查认证绕过设置时出错:",error),!1}})()?(console.log("AuthWrapper: 根据设置绕过认证检查"),children):loading?(0,jsx_runtime.jsx)("div",{className:"d-flex justify-content-center align-items-center",style:{height:"100vh"},children:(0,jsx_runtime.jsx)(Spinner.A,{animation:"border",role:"status",children:(0,jsx_runtime.jsx)("span",{className:"visually-hidden",children:"Loading..."})})}):isAuthenticated?children:(0,jsx_runtime.jsx)(chunk_AYJ5UCUI.C5,{to:"/login",replace:!0})},components_AuthWrapper=AuthWrapper;AuthWrapper.__docgenInfo={description:"AuthWrapper - 用于包装需要身份验证的组件\r\n与 ProtectedRoute 不同，这个组件设计为直接包装子组件，而不是用于路由定义",methods:[],displayName:"AuthWrapper"};const AuthWrapper_stories={title:"Components/AuthWrapper",component:components_AuthWrapper,parameters:{docs:{description:{component:"认证包装器组件，用于保护需要认证才能访问的内容。根据用户认证状态自动处理导航和加载状态。"}}},decorators:[Story=>{const{isAuthenticated,loading}=(0,AuthContext.As)();return(0,jsx_runtime.jsxs)("div",{children:[(0,jsx_runtime.jsxs)("div",{style:{background:"#f0f0f0",padding:"8px",marginBottom:"10px",fontSize:"14px"},children:[(0,jsx_runtime.jsx)("strong",{children:"当前认证状态："}),loading?"加载中...":isAuthenticated?"已认证":"未认证"]}),(0,jsx_runtime.jsx)(Story,{})]})}]},ProtectedContent=()=>(0,jsx_runtime.jsxs)("div",{style:{padding:"20px",border:"1px solid #ccc",borderRadius:"4px"},children:[(0,jsx_runtime.jsx)("h3",{children:"受保护的内容"}),(0,jsx_runtime.jsx)("p",{children:"这是只有认证用户才能看到的内容。"})]}),Default=()=>(0,jsx_runtime.jsx)(components_AuthWrapper,{children:(0,jsx_runtime.jsx)(ProtectedContent,{})}),Loading=()=>(0,jsx_runtime.jsx)(components_AuthWrapper,{children:(0,jsx_runtime.jsx)(ProtectedContent,{})});Loading.parameters={docs:{description:{story:'组件在加载状态下显示加载动画 (通过工具栏设置 Authentication 为 "加载中")'}}};const Unauthenticated=()=>(0,jsx_runtime.jsx)(components_AuthWrapper,{children:(0,jsx_runtime.jsx)(ProtectedContent,{})});Unauthenticated.parameters={docs:{description:{story:'未认证状态下会自动重定向到登录页面 (通过工具栏设置 Authentication 为 "未登录")'}}};const Authenticated=()=>(0,jsx_runtime.jsx)(components_AuthWrapper,{children:(0,jsx_runtime.jsx)(ProtectedContent,{})});Authenticated.parameters={docs:{description:{story:'已认证状态下正常显示子组件内容 (通过工具栏设置 Authentication 为 "已登录")'}}};const __namedExportsOrder=["Default","Loading","Unauthenticated","Authenticated"];Default.parameters={...Default.parameters,docs:{...Default.parameters?.docs,source:{originalSource:"() => <AuthWrapper>\r\n    <ProtectedContent />\r\n  </AuthWrapper>",...Default.parameters?.docs?.source}}},Loading.parameters={...Loading.parameters,docs:{...Loading.parameters?.docs,source:{originalSource:"() => <AuthWrapper>\r\n    <ProtectedContent />\r\n  </AuthWrapper>",...Loading.parameters?.docs?.source}}},Unauthenticated.parameters={...Unauthenticated.parameters,docs:{...Unauthenticated.parameters?.docs,source:{originalSource:"() => <AuthWrapper>\r\n    <ProtectedContent />\r\n  </AuthWrapper>",...Unauthenticated.parameters?.docs?.source}}},Authenticated.parameters={...Authenticated.parameters,docs:{...Authenticated.parameters?.docs,source:{originalSource:"() => <AuthWrapper>\r\n    <ProtectedContent />\r\n  </AuthWrapper>",...Authenticated.parameters?.docs?.source}}}}}]);