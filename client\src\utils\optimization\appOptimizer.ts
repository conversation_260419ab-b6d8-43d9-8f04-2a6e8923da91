/**
 * 应用级别优化初始化系统
 * 整合所有优化功能，提供统一的初始化入口
 */
import { apiCacheManager } from '../api/apiCacheManager';
import { resourceOptimizationManager } from './resourceOptimizer';
import { ComponentType, lazy } from 'react';
import { resourcePreloader } from './resourcePreloader';

/**
 * App优化配置接口
 */
export interface AppOptimizationConfig {
  // 资源预加载配置
  resourcePreloading: {
    enabled: boolean;
    routes?: string[];
    components?: string[];
    assets?: string[];
    // 关键资源预加载
    criticalResources?: Array<{ href: string; as: string; type?: string; }>;
  };
  
  // 字体优化配置
  fontOptimization: {
    enabled: boolean;
    preloadFonts?: string[];
    fontDisplay?: 'auto' | 'block' | 'swap' | 'fallback' | 'optional';
    criticalFonts?: string[];
  };
  
  // 代码拆分配置
  codeSplitting: {
    enabled: boolean;
    routeLevel?: boolean;
    componentLevel?: boolean;
    vendorChunks?: boolean;
    chunkSizeThreshold?: number;
  };
  
  // 性能监控配置
  performance: {
    enabled: boolean;
    enableWebVitals?: boolean;
    enableMemoryTracking?: boolean;
    enableNetworkTracking?: boolean;
    enableUserTiming?: boolean;
  };
  
  // 缓存策略配置
  caching: {
    enabled: boolean;
    routes?: boolean;
    api?: boolean;
    assets?: boolean;
    ttl?: number;
  };
}

/**
 * 性能指标接口
 */
export interface PerformanceMetrics {
  // 页面加载时间
  pageLoadTime?: number;
  // 首次内容绘制
  firstContentfulPaint?: number;
  // 最大内容绘制
  largestContentfulPaint?: number;
  // 累积布局偏移
  cumulativeLayoutShift?: number;
  // 首次输入延迟
  firstInputDelay?: number;
  // 内存使用
  memoryUsage?: {
    used: number;
    total: number;
    percent: number;
  };
  // 网络性能
  networkPerformance?: {
    count: number;
    average: number;
    min: number;
    max: number;
  };
}

// 性能指标收集器
class PerformanceCollector {
  private metrics: Map<string, number[]> = new Map();
  private startTimes: Map<string, number> = new Map();

  startMeasure(name: string) {
    this.startTimes.set(name, performance.now());
  }

  endMeasure(name: string) {
    const startTime = this.startTimes.get(name);
    if (startTime) {
      const duration = performance.now() - startTime;
      
      if (!this.metrics.has(name)) {
        this.metrics.set(name, []);
      }
      
      this.metrics.get(name)!.push(duration);
      this.startTimes.delete(name);
      
      return duration;
    }
    return 0;
  }

  getMetrics(name: string) {
    const values = this.metrics.get(name);
    if (!values || values.length === 0) {
      return { count: 0, average: 0, min: 0, max: 0 };
    }
    
    const count = values.length;
    const sum = values.reduce((a, b) => a + b, 0);
    const average = sum / count;
    const min = Math.min(...values);
    const max = Math.max(...values);
    
    return { count, average, min, max };
  }

  getAllMetrics() {
    const result: Record<string, any> = {};
    
    for (const [name] of this.metrics) {
      result[name] = this.getMetrics(name);
    }
    
    return result;
  }

  clear() {
    this.metrics.clear();
    this.startTimes.clear();
  }
}

// 路由预取管理器
class RoutePrefetchManager {
  private prefetchedRoutes = new Set<string>();
  private prefetchQueue: string[] = [];
  private isProcessing = false;

  async prefetchRoute(route: string, delay = 0) {
    if (this.prefetchedRoutes.has(route)) {
      return;
    }

    this.prefetchQueue.push(route);
    
    if (!this.isProcessing) {
      this.processQueue(delay);
    }
  }

  private async processQueue(delay: number) {
    this.isProcessing = true;
    
    while (this.prefetchQueue.length > 0) {
      const route = this.prefetchQueue.shift()!;
      
      try {
        await this.doPreFetch(route);
        this.prefetchedRoutes.add(route);
        
        if (delay > 0) {
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      } catch (error) {
        console.warn(`[Route Prefetch] Failed to prefetch ${route}:`, error);
      }
    }
    
    this.isProcessing = false;
  }

  private async doPreFetch(route: string) {
    // 创建预取链接
    const link = document.createElement('link');
    link.rel = 'prefetch';
    link.href = route;
    
    return new Promise((resolve, reject) => {
      link.onload = resolve;
      link.onerror = reject;
      document.head.appendChild(link);
      
      // 超时处理
      setTimeout(() => {
        document.head.removeChild(link);
        reject(new Error('Prefetch timeout'));
      }, 5000);
    });
  }

  getPrefetchedRoutes() {
    return Array.from(this.prefetchedRoutes);
  }
}

// 应用优化管理器
export class AppOptimizer {
  private config: AppOptimizationConfig;
  private performanceCollector: PerformanceCollector;
  private routePrefetcher: RoutePrefetchManager;
  private initialized = false;
  private performanceMetrics: PerformanceMetrics = {};
  private metricsHistory: PerformanceMetrics[] = [];
  private observer?: PerformanceObserver;

  constructor(config: AppOptimizationConfig) {
    this.config = config;
    this.performanceCollector = new PerformanceCollector();
    this.routePrefetcher = new RoutePrefetchManager();
    
    if (config.performance.enabled) {
      this.initPerformanceMonitoring();
    }
    
    if (config.resourcePreloading.enabled) {
      this.initResourcePreloading();
    }
    
    if (config.fontOptimization.enabled) {
      this.initFontOptimization();
    }
  }

  /**
   * 初始化所有优化
   */
  async initialize() {
    if (this.initialized) {
      console.warn('[App Optimizer] Already initialized');
      return;
    }

    console.log('[App Optimizer] Starting initialization...');
    this.performanceCollector.startMeasure('total-init');

    try {
      // 1. 初始化资源优化
      if (this.config.resources) {
        await this.initializeResourceOptimization();
      }

      // 2. 初始化API缓存
      if (this.config.caching.enabled) {
        await this.initializeApiCache();
      }

      // 3. 初始化性能监控
      if (this.config.performance.enabled) {
        this.initializePerformanceMonitoring();
      }

      // 4. 初始化路由预取
      if (this.config.resourcePreloading.enabled) {
        await this.initializeRoutePrefetching();
      }

      const totalTime = this.performanceCollector.endMeasure('total-init');
      console.log(`[App Optimizer] Initialization completed in ${totalTime.toFixed(2)}ms`);
      
      this.initialized = true;
    } catch (error) {
      console.error('[App Optimizer] Initialization failed:', error);
      throw error;
    }
  }

  /**
   * 初始化资源优化
   */
  private async initializeResourceOptimization() {
    this.performanceCollector.startMeasure('resource-init');
    
    const {
      criticalFonts = [],
      externalDomains = [],
      lazyImages = true,
      criticalResources = []
    } = this.config.resources;

    resourceOptimizationManager.initialize({
      criticalFonts,
      externalDomains,
      criticalResources,
      lazyImageOptions: lazyImages ? {} : undefined
    });

    this.performanceCollector.endMeasure('resource-init');
  }

  /**
   * 初始化API缓存
   */
  private async initializeApiCache() {
    this.performanceCollector.startMeasure('api-cache-init');
    
    const { warmupUrls = [] } = this.config.caching;
    
    if (warmupUrls.length > 0) {
      await apiCacheManager.warmupCache(warmupUrls);
    }

    // 定期清理过期缓存
    setInterval(() => {
      apiCacheManager.clearExpired();
    }, 5 * 60 * 1000); // 每5分钟清理一次

    this.performanceCollector.endMeasure('api-cache-init');
  }

  /**
   * 初始化性能监控
   */
  private initializePerformanceMonitoring() {
    const { trackCore = true, trackMemory = false, logLevel = 'basic' } = this.config.performance;

    if (trackCore) {
      this.setupCoreVitalsMonitoring();
    }

    if (trackMemory) {
      this.setupMemoryMonitoring();
    }

    if (logLevel !== 'none') {
      this.setupPerformanceLogging(logLevel);
    }
  }

  /**
   * 初始化路由预取
   */
  private async initializeRoutePrefetching() {
    const { routes = [], delay = 1000 } = this.config.resourcePreloading;
    
    if (routes.length > 0) {
      // 延迟执行路由预取，避免影响首屏加载
      setTimeout(() => {
        routes.forEach(route => {
          this.routePrefetcher.prefetchRoute(route, delay);
        });
      }, 2000);
    }
  }

  /**
   * 设置核心性能指标监控
   */
  private setupCoreVitalsMonitoring() {
    // First Contentful Paint
    new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.name === 'first-contentful-paint') {
          console.log(`[Core Vitals] FCP: ${entry.startTime.toFixed(2)}ms`);
        }
      }
    }).observe({ entryTypes: ['paint'] });

    // Largest Contentful Paint
    new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const lastEntry = entries[entries.length - 1];
      console.log(`[Core Vitals] LCP: ${lastEntry.startTime.toFixed(2)}ms`);
    }).observe({ entryTypes: ['largest-contentful-paint'] });

    // Cumulative Layout Shift
    new PerformanceObserver((list) => {
      let cumulativeScore = 0;
      for (const entry of list.getEntries()) {
        if (!(entry as any).hadRecentInput) {
          cumulativeScore += (entry as any).value;
        }
      }
      console.log(`[Core Vitals] CLS: ${cumulativeScore.toFixed(4)}`);
    }).observe({ entryTypes: ['layout-shift'] });
  }

  /**
   * 设置内存监控
   */
  private setupMemoryMonitoring() {
    if ('memory' in (window as any).performance) {
      setInterval(() => {
        const memory = (window as any).performance.memory;
        const used = (memory.usedJSHeapSize / 1024 / 1024).toFixed(2);
        const total = (memory.totalJSHeapSize / 1024 / 1024).toFixed(2);
        const limit = (memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2);
        
        console.log(`[Memory] Used: ${used}MB / Total: ${total}MB / Limit: ${limit}MB`);
      }, 30000); // 每30秒记录一次
    }
  }

  /**
   * 设置性能日志
   */
  private setupPerformanceLogging(level: 'basic' | 'detailed') {
    if (level === 'detailed') {
      // 详细的资源加载时间
      new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.duration > 100) { // 只记录超过100ms的资源
            console.log(`[Resource] ${entry.name}: ${entry.duration.toFixed(2)}ms`);
          }
        }
      }).observe({ entryTypes: ['resource'] });

      // 详细的导航时间
      window.addEventListener('load', () => {
        const nav = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        console.log('[Navigation Timing]', {
          dns: nav.domainLookupEnd - nav.domainLookupStart,
          connect: nav.connectEnd - nav.connectStart,
          request: nav.responseStart - nav.requestStart,
          response: nav.responseEnd - nav.responseStart,
          dom: nav.domContentLoadedEventEnd - nav.responseEnd,
          load: nav.loadEventEnd - nav.loadEventStart
        });
      });
    }
  }

  /**
   * 获取优化统计
   */
  getOptimizationStats() {
    return {
      initialized: this.initialized,
      performance: this.performanceCollector.getAllMetrics(),
      resources: resourceOptimizationManager.getStats(),
      apiCache: apiCacheManager.getStats(),
      prefetchedRoutes: this.routePrefetcher.getPrefetchedRoutes(),
      config: this.config
    };
  }

  /**
   * 清理资源
   */
  cleanup() {
    this.performanceCollector.clear();
    apiCacheManager.clearAll();
    this.initialized = false;
  }

  /**
   * 初始化性能监控
   */
  private initPerformanceMonitoring(): void {
    // Web Vitals 监控
    if (this.config.performance.enableWebVitals) {
      this.observeWebVitals();
    }
    
    // 网络性能监控
    if (this.config.performance.enableNetworkTracking) {
      this.observeNetworkPerformance();
    }
    
    // 内存监控
    if (this.config.performance.enableMemoryTracking) {
      this.observeMemoryUsage();
    }
    
    // 用户自定义时间监控
    if (this.config.performance.enableUserTiming) {
      this.observeUserTiming();
    }
  }
  
  /**
   * 观察Web Vitals指标
   */
  private observeWebVitals(): void {
    if (!('performance' in window) || !('PerformanceObserver' in window)) {
      return;
    }
    
    try {
      // 观察页面加载性能
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          switch (entry.entryType) {
            case 'navigation':
              const navEntry = entry as PerformanceNavigationTiming;
              this.performanceMetrics.pageLoadTime = navEntry.loadEventEnd - navEntry.loadEventStart;
              break;
            case 'paint':
              const paintEntry = entry as PerformancePaintTiming;
              if (paintEntry.name === 'first-contentful-paint') {
                this.performanceMetrics.firstContentfulPaint = paintEntry.startTime;
              }
              break;
            case 'largest-contentful-paint':
              this.performanceMetrics.largestContentfulPaint = entry.startTime;
              break;
            case 'layout-shift':
              if (!entry.hadRecentInput) {
                this.performanceMetrics.cumulativeLayoutShift = 
                  (this.performanceMetrics.cumulativeLayoutShift || 0) + (entry as any).value;
              }
              break;
            case 'first-input':
              this.performanceMetrics.firstInputDelay = (entry as any).processingStart - entry.startTime;
              break;
          }
        }
        
        this.saveMetricsToHistory();
      });
      
      observer.observe({ entryTypes: ['navigation', 'paint', 'largest-contentful-paint', 'layout-shift', 'first-input'] });
      this.observer = observer;
    } catch (error) {
      console.warn('Performance monitoring initialization failed:', error);
    }
  }
  
  /**
   * 观察网络性能
   */
  private observeNetworkPerformance(): void {
    const getNetworkMetrics = () => {
      const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
      const requests = resources.filter(r => r.initiatorType === 'fetch' || r.initiatorType === 'xmlhttprequest');
      
      if (requests.length === 0) {
        return { count: 0, average: 0, min: 0, max: 0 };
      }
      
      const durations = requests.map(r => r.duration);
      return {
        count: requests.length,
        average: durations.reduce((a, b) => a + b, 0) / durations.length,
        min: Math.min(...durations),
        max: Math.max(...durations)
      };
    };
    
    this.performanceMetrics.networkPerformance = getNetworkMetrics();
  }
  
  /**
   * 观察内存使用
   */
  private observeMemoryUsage(): void {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      this.performanceMetrics.memoryUsage = {
        used: memory.usedJSHeapSize,
        total: memory.totalJSHeapSize,
        percent: (memory.usedJSHeapSize / memory.totalJSHeapSize) * 100
      };
    }
  }
  
  /**
   * 观察用户自定义时间
   */
  private observeUserTiming(): void {
    const marks = performance.getEntriesByType('mark');
    const measures = performance.getEntriesByType('measure');
    
    // 可以根据需要处理自定义的性能标记和测量
    console.log('Custom performance marks:', marks);
    console.log('Custom performance measures:', measures);
  }
  
  /**
   * 初始化资源预加载
   */
  private async initResourcePreloading(): Promise<void> {
    const { routes = [], components = [], assets = [], criticalResources = [] } = this.config.resourcePreloading;
    
    // 预加载关键资源
    for (const resource of criticalResources) {
      await resourcePreloader.preloadResource(resource.href, resource.as as any, resource.type);
    }
    
    // 预加载路由
    for (const route of routes) {
      await this.prefetchRoute(route);
    }
    
    // 预加载组件
    for (const component of components) {
      await this.prefetchComponent(component);
    }
    
    // 预加载资产
    for (const asset of assets) {
      await resourcePreloader.preloadResource(asset, 'fetch');
    }
  }
  
  /**
   * 预取路由
   */
  async prefetchRoute(route: string, delay = 0) {
    if (delay > 0) {
      await new Promise(resolve => setTimeout(resolve, delay));
    }
    
    try {
      // 这里可以根据路由配置预加载相应的组件
      console.log(`Prefetching route: ${route}`);
      // 实际实现会依赖于路由配置
    } catch (error) {
      console.warn(`Failed to prefetch route ${route}:`, error);
    }
  }
  
  /**
   * 预取组件
   */
  async prefetchComponent(componentPath: string) {
    try {
      console.log(`Prefetching component: ${componentPath}`);
      // 这里可以使用import()动态导入组件
      // await import(componentPath);
    } catch (error) {
      console.warn(`Failed to prefetch component ${componentPath}:`, error);
    }
  }
  
  /**
   * 初始化字体优化
   */
  private initFontOptimization(): void {
    const { fontDisplay = 'swap', criticalFonts = [] } = this.config.fontOptimization;
    
    // 预加载关键字体
    for (const font of criticalFonts) {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.href = font;
      link.as = 'font';
      link.type = 'font/woff2';
      link.crossOrigin = 'anonymous';
      document.head.appendChild(link);
    }
    
    // 设置字体显示策略
    const style = document.createElement('style');
    style.textContent = `
      @font-face {
        font-display: ${fontDisplay};
      }
    `;
    document.head.appendChild(style);
  }
  
  /**
   * 创建懒加载组件
   */
  createLazyComponent<T extends ComponentType<any>>(
    importFn: () => Promise<{ default: T }>,
    fallback?: React.ComponentType
  ): ComponentType {
    return lazy(importFn);
  }
  
  /**
   * 获取性能指标
   */
  getMetrics(): PerformanceMetrics {
    return { ...this.performanceMetrics };
  }
  
  /**
   * 获取指标历史
   */
  getMetricsHistory(): PerformanceMetrics[] {
    return [...this.metricsHistory];
  }
  
  /**
   * 保存指标到历史记录
   */
  private saveMetricsToHistory(): void {
    this.metricsHistory.push({ ...this.performanceMetrics });
    
    // 限制历史记录数量
    if (this.metricsHistory.length > 100) {
      this.metricsHistory.shift();
    }
  }
  
  /**
   * 优化建议
   */
  getOptimizationSuggestions(): string[] {
    const suggestions: string[] = [];
    
    if (this.performanceMetrics.largestContentfulPaint && this.performanceMetrics.largestContentfulPaint > 2500) {
      suggestions.push('考虑优化最大内容绘制时间，目标是2.5秒以内');
    }
    
    if (this.performanceMetrics.cumulativeLayoutShift && this.performanceMetrics.cumulativeLayoutShift > 0.1) {
      suggestions.push('存在布局偏移问题，建议为图片和广告设置固定尺寸');
    }
    
    if (this.performanceMetrics.firstInputDelay && this.performanceMetrics.firstInputDelay > 100) {
      suggestions.push('首次输入延迟较高，考虑减少JavaScript执行时间');
    }
    
    if (this.performanceMetrics.memoryUsage && this.performanceMetrics.memoryUsage.percent > 80) {
      suggestions.push('内存使用率较高，考虑优化内存泄漏或减少数据缓存');
    }
    
    return suggestions;
  }
  
  /**
   * 销毁优化器
   */
  destroy(): void {
    if (this.observer) {
      this.observer.disconnect();
    }
  }
}

// 默认配置
export const defaultOptimizationConfig: AppOptimizationConfig = {
  apiCache: {
    enable: true,
    warmupUrls: [
      '/dashboard/metrics',
      '/analytics/overview',
      '/agents'
    ]
  },
  resources: {
    criticalFonts: [
      '/fonts/inter.woff2',
      '/fonts/jetbrains-mono.woff2'
    ],
    externalDomains: [
      'http://fonts.googleapis.com',
      'https://api.openai.com',
      'https://api.stripe.com'
    ],
    lazyImages: true,
    criticalResources: [
      { href: '/css/critical.css', as: 'style' },
      { href: '/js/vendor.js', as: 'script' }
    ]
  },
  performance: {
    enable: true,
    trackCore: true,
    trackMemory: false,
    logLevel: 'basic'
  },
  prefetch: {
    enable: true,
    routes: [
      '/dashboard/analytics',
      '/dashboard/data-insight',
      '/settings'
    ],
    delay: 1000
  }
};

// 全局优化器实例
let globalOptimizer: AppOptimizer | null = null;

/**
 * 初始化应用优化
 */
export async function initializeAppOptimization(config: Partial<AppOptimizationConfig> = {}) {
  const finalConfig = { ...defaultOptimizationConfig, ...config };
  
  if (globalOptimizer) {
    console.warn('[App Optimizer] Already initialized');
    return globalOptimizer;
  }
  
  globalOptimizer = new AppOptimizer(finalConfig);
  await globalOptimizer.initialize();
  
  return globalOptimizer;
}

/**
 * 获取全局优化器实例
 */
export function getAppOptimizer(): AppOptimizer | null {
  return globalOptimizer;
}