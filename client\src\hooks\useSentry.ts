import { useCallback, useEffect } from 'react';
import { captureError, captureMessage, addBreadcrumb, setUser, measurePerformance } from '../lib/sentry';
import type { SeverityLevel } from '@sentry/react';

interface UseSentryReturn {
  captureError: (error: Error, context?: Record<string, any>) => void;
  captureMessage: (message: string, level?: SeverityLevel) => void;
  addBreadcrumb: (message: string, category: string, level?: SeverityLevel) => void;
  setUser: (user: { id: string; email?: string; username?: string }) => void;
  measurePerformance: (name: string, fn: () => void) => void;
  logUserAction: (action: string, details?: Record<string, any>) => void;
}

export const useSentry = (): UseSentryReturn => {
  // 记录用户操作的便捷方法
  const logUserAction = useCallback((action: string, details?: Record<string, any>) => {
    addBreadcrumb(
      `User action: ${action}`,
      'user',
      'info'
    );
    
    if (details) {
      console.log(`[User Action] ${action}:`, details);
    }
  }, []);

  // 自动记录页面访问
  useEffect(() => {
    const currentPath = window.location.pathname;
    addBreadcrumb(
      `Page visited: ${currentPath}`,
      'navigation',
      'info'
    );
  }, []);

  return {
    captureError,
    captureMessage,
    addBreadcrumb,
    setUser,
    measurePerformance,
    logUserAction,
  };
};

// 错误边界Hook
export const useErrorHandler = () => {
  return useCallback((error: Error, errorInfo?: any) => {
    console.error('Error caught by error handler:', error);
    captureError(error, {
      errorInfo,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
    });
  }, []);
};

// 性能监控Hook
export const usePerformanceMonitor = () => {
  const trackApiCall = useCallback((apiName: string, duration: number, success: boolean) => {
    addBreadcrumb(
      `API call: ${apiName} (${duration}ms) - ${success ? 'success' : 'failed'}`,
      'api',
      success ? 'info' : 'error'
    );
  }, []);

  const trackComponentRender = useCallback((componentName: string, renderTime: number) => {
    if (renderTime > 100) { // 只记录超过100ms的渲染
      addBreadcrumb(
        `Slow render: ${componentName} took ${renderTime}ms`,
        'performance',
        'warning'
      );
    }
  }, []);

  return {
    trackApiCall,
    trackComponentRender,
  };
}; 