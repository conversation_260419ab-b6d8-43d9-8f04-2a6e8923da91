import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Button, Card, CardContent, CardHeader, CardTitle } from '@/components/design-system';

import { ArrowRightIcon } from '@/components/ui/icons';

export default function UpgradePage() {
  document.title = 'Upgrade Subscription'
  const navigate = useNavigate();
  
  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4">
      <Card className="w-full max-w-md text-center">
        <CardHeader>
          <div className="mx-auto w-16 h-16 bg-muted rounded-full flex items-center justify-center mb-4">
            <className="w-8 h-8 text-muted-foreground" />
          </div>
          <CardTitle className="text-2xl">权限不足</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-muted-foreground">
            您当前的订阅级别无法访问此功能。请升级以解锁更多功能。
          </p>
          <Button)
            onClick={() => navigate('/plans')}
            className="w-full"
          >
            查看订阅计划
            <ArrowRightIcon className="w-4 h-4 ml-2" />
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};