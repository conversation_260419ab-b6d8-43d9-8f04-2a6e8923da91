import React, { useState } from "react";
import { Agent, AgentRule } from "@/types/agent";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { getAgentRules, createAgentRule, updateAgentRule, deleteAgentRule } from "@/services/agentService";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Skeleton } from "@/components/ui/skeleton";
import { useToast } from "@/hooks/use-toast";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialog<PERSON>ooter,
  <PERSON><PERSON><PERSON><PERSON>ogHeader,
  AlertDialog<PERSON>itle
} from "@/components/ui/alert-dialog";
import { PlusCircle, Edit2, Trash2 } from "lucide-react";
import { useForm, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Toggle } from "@/components/ui/toggle";

interface AgentRulesTabProps {
  agent: Agent;
}

const ruleFormSchema = z.object({
  pattern: z.string().min(1, "Pattern cannot be empty"),
  response: z.string().min(1, "Response cannot be empty"),
  priority: z.number().int().min(0, "Priority cannot be negative").max(100, "Priority cannot exceed 100"),
  isRegex: z.boolean(),
  isActive: z.boolean()
});

type RuleFormData = z.infer<typeof ruleFormSchema>;

export function AgentRulesTab({ agent }: AgentRulesTabProps) {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingRule, setEditingRule] = useState<AgentRule | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [ruleToDelete, setRuleToDelete] = useState<AgentRule | null>(null);

  const { data: rules, isLoading, error } = useQuery<AgentRule[], Error>({
    queryKey: ["agentRules", agent.id],
    queryFn: () => getAgentRules(agent.id)
  });

  const {
    handleSubmit,
    control,
    reset,
    formState: { errors, isSubmitting }
  } = useForm<RuleFormData>({
    resolver: zodResolver(ruleFormSchema),
    defaultValues: {
      pattern: "",
      response: "",
      priority: 0,
      isRegex: false,
      isActive: true
    }
  });

  const createMutation = useMutation<AgentRule, Error, RuleFormData>({
    mutationFn: (newRuleData) => createAgentRule(agent.id, newRuleData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["agentRules", agent.id] });
      toast({ title: "Rule created successfully" });
      setIsFormOpen(false);
      reset();
    },
    onError: (err) => toast({ title: "Creation failed", description: err.message, variant: "destructive" })
  });

  const updateMutation = useMutation<AgentRule, Error, { ruleId: string; data: Partial<RuleFormData> }>({
    mutationFn: ({ ruleId, data }) => updateAgentRule(agent.id, ruleId, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["agentRules", agent.id] });
      toast({ title: "Rule updated successfully" });
      setIsFormOpen(false);
      setEditingRule(null);
      reset();
    },
    onError: (err) => toast({ title: "Update failed", description: err.message, variant: "destructive" })
  });

  const deleteMutation = useMutation<void, Error, string>({
    mutationFn: (ruleId) => deleteAgentRule(agent.id, ruleId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["agentRules", agent.id] });
      toast({ title: "Rule deleted successfully" });
      setShowDeleteConfirm(false);
      setRuleToDelete(null);
    },
    onError: (err) => toast({ title: "Deletion failed", description: err.message, variant: "destructive" })
  });

  const handleFormSubmit = (data: RuleFormData) => {
    const payload = { ...data };

    if (editingRule) {
      updateMutation.mutate({ ruleId: editingRule.id, data: payload });
    } else {
      createMutation.mutate(payload);
    }
  };

  const openEditForm = (rule: AgentRule) => {
    setEditingRule(rule);
    reset({
      pattern: rule.pattern,
      response: rule.response,
      priority: rule.priority,
      isRegex: rule.isRegex,
      isActive: rule.isActive
    });
    setIsFormOpen(true);
  };

  const openNewForm = () => {
    setEditingRule(null);
    reset({ pattern: "", response: "", priority: 0, isRegex: false, isActive: true });
    setIsFormOpen(true);
  };

  const handleDeleteRule = (rule: AgentRule) => {
    setRuleToDelete(rule);
    setShowDeleteConfirm(true);
  };

  const confirmDelete = () => {
    if (ruleToDelete) {
      deleteMutation.mutate(ruleToDelete.id);
    }
  };

  if (isLoading) return (
    <Card>
      <CardHeader><Skeleton className="h-8 w-1/3" /></CardHeader>
      <CardContent className="space-y-2">
        {[1, 2, 3].map(i => <Skeleton key={i} className="h-16 w-full" />)}
      </CardContent>
    </Card>
  );
  
  if (error) return <p className="text-red-500">Failed to load rules: {error.message}</p>;

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 p-4 bg-gray-50 dark:bg-gray-900 rounded-lg border">
        <div className="flex-1">
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white">Rule Management</h3>
          <p className="text-sm text-muted-foreground mt-1">Configure auto-reply rules for {agent.name}.</p>
        </div>
        <div className="flex-shrink-0">
          <Button 
            onClick={openNewForm}
            className="violet-gradient-button min-w-[140px]"
          >
            <PlusCircle className="mr-2 h-4 w-4" />
            Add New Rule
          </Button>
        </div>
      </div>
      
      {rules && rules.length > 0 ? (
        <Card>
          <CardContent className="pt-6 space-y-4">
            {rules.sort((a, b) => a.priority - b.priority).map((rule) => (
              <Card key={rule.id} className={`p-4 ${!rule.isActive ? "bg-muted/50" : ""}`}>
                <div className="flex justify-between items-start">
                  <div>
                    <p className="font-semibold text-lg break-all">
                      {rule.pattern} {rule.isRegex && <span className="text-xs text-muted-foreground">(regex)</span>}
                    </p>
                    <p className="text-sm text-muted-foreground break-all">Response: {rule.response}</p>
                    <p className="text-xs text-muted-foreground">
                      Priority: {rule.priority} | Status: {rule.isActive ? "Enabled" : "Disabled"}
                    </p>
                  </div>
                  <div className="flex items-center space-x-1 shrink-0 ml-2">
                    <Button variant="ghost" size="icon" onClick={() => openEditForm(rule)}>
                      <Edit2 className="h-4 w-4" />
                    </Button>
                    <Button 
                      variant="ghost" 
                      size="icon" 
                      onClick={() => handleDeleteRule(rule)} 
                      className="text-red-500 hover:text-red-600"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </Card>
            ))}
          </CardContent>
        </Card>
      ) : (
        <Card className="p-6 text-center border-2 border-dashed border-gray-300">
          <div className="space-y-3">
            <p className="text-muted-foreground">No rules configured yet.</p>
            <Button onClick={openNewForm} variant="outline">
              <PlusCircle className="mr-2 h-4 w-4" />
              Add First Rule
            </Button>
          </div>
        </Card>
      )}

      <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>{editingRule ? "Edit Rule" : "Create New Rule"}</DialogTitle>
            <DialogDescription>
              Configure pattern matching and response for automatic replies.
            </DialogDescription>
          </DialogHeader>
          
          <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="pattern">Pattern</Label>
              <Controller
                name="pattern"
                control={control}
                render={({ field }) => (
                  <Input
                    id="pattern"
                    placeholder="Enter pattern to match"
                    {...field}
                  />
                )}
              />
              {errors.pattern && <p className="text-sm text-red-500">{errors.pattern.message}</p>}
            </div>

            <div className="space-y-2">
              <Label htmlFor="response">Response</Label>
              <Controller
                name="response"
                control={control}
                render={({ field }) => (
                  <Textarea
                    id="response"
                    placeholder="Enter response message"
                    {...field}
                  />
                )}
              />
              {errors.response && <p className="text-sm text-red-500">{errors.response.message}</p>}
            </div>

            <div className="space-y-2">
              <Label htmlFor="priority">Priority</Label>
              <Controller
                name="priority"
                control={control}
                render={({ field }) => (
                  <Input
                    id="priority"
                    type="number"
                    min="0"
                    max="100"
                    {...field}
                    onChange={(e) => field.onChange(parseInt(e.target.value, 10))}
                  />
                )}
              />
              {errors.priority && <p className="text-sm text-red-500">{errors.priority.message}</p>}
            </div>

            <div className="flex items-center space-x-4">
              <Controller
                name="isRegex"
                control={control}
                render={({ field }) => (
                  <Toggle pressed={field.value} onPressedChange={field.onChange}>
                    Use Regex
                  </Toggle>
                )}
              />
              <Controller
                name="isActive"
                control={control}
                render={({ field }) => (
                  <Toggle pressed={field.value} onPressedChange={field.onChange}>
                    Active
                  </Toggle>
                )}
              />
            </div>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsFormOpen(false)}>
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {editingRule ? "Update" : "Create"} Rule
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      <AlertDialog open={showDeleteConfirm} onOpenChange={setShowDeleteConfirm}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the rule. This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDelete}>Delete</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
