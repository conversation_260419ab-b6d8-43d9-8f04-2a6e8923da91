/*! For license information please see 686.ec7df239.iframe.bundle.js.LICENSE.txt */
"use strict";(self.webpackChunkclient=self.webpackChunkclient||[]).push([[686],{"./node_modules/@radix-ui/react-select/dist/index.mjs":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.d(__webpack_exports__,{UC:()=>Content2,YJ:()=>Group,In:()=>Icon,q7:()=>Item,VF:()=>ItemIndicator,p4:()=>ItemText,JU:()=>Label,ZL:()=>Portal,bL:()=>dist_Root2,wn:()=>ScrollDownButton,PP:()=>ScrollUpButton,wv:()=>Separator,l9:()=>Trigger,WT:()=>Value,LM:()=>Viewport});var react=__webpack_require__("./node_modules/react/index.js"),react_dom=__webpack_require__("./node_modules/react-dom/index.js");function clamp(value,[min,max]){return Math.min(max,Math.max(min,value))}var dist=__webpack_require__("./node_modules/@radix-ui/primitive/dist/index.mjs"),react_context_dist=__webpack_require__("./node_modules/@radix-ui/react-context/dist/index.mjs"),react_compose_refs_dist=__webpack_require__("./node_modules/@radix-ui/react-compose-refs/dist/index.mjs"),react_slot_dist=__webpack_require__("./node_modules/@radix-ui/react-slot/dist/index.mjs"),jsx_runtime=__webpack_require__("./node_modules/react/jsx-runtime.js");var __instanciated=new WeakMap;Map;function at(array,index){if("at"in Array.prototype)return Array.prototype.at.call(array,index);const actualIndex=function toSafeIndex(array,index){const length=array.length,relativeIndex=toSafeInteger(index),actualIndex=relativeIndex>=0?relativeIndex:length+relativeIndex;return actualIndex<0||actualIndex>=length?-1:actualIndex}(array,index);return-1===actualIndex?void 0:array[actualIndex]}function toSafeInteger(number){return number!=number||0===number?0:Math.trunc(number)}var DirectionContext=react.createContext(void 0);var react_dismissable_layer_dist=__webpack_require__("./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs"),react_focus_guards_dist=__webpack_require__("./node_modules/@radix-ui/react-focus-guards/dist/index.mjs"),react_focus_scope_dist=__webpack_require__("./node_modules/@radix-ui/react-focus-scope/dist/index.mjs"),react_id_dist=__webpack_require__("./node_modules/@radix-ui/react-id/dist/index.mjs");const sides=["top","right","bottom","left"],floating_ui_utils_min=Math.min,floating_ui_utils_max=Math.max,round=Math.round,floor=Math.floor,createCoords=v=>({x:v,y:v}),oppositeSideMap={left:"right",right:"left",bottom:"top",top:"bottom"},oppositeAlignmentMap={start:"end",end:"start"};function floating_ui_utils_clamp(start,value,end){return floating_ui_utils_max(start,floating_ui_utils_min(value,end))}function floating_ui_utils_evaluate(value,param){return"function"==typeof value?value(param):value}function floating_ui_utils_getSide(placement){return placement.split("-")[0]}function floating_ui_utils_getAlignment(placement){return placement.split("-")[1]}function getOppositeAxis(axis){return"x"===axis?"y":"x"}function getAxisLength(axis){return"y"===axis?"height":"width"}function floating_ui_utils_getSideAxis(placement){return["top","bottom"].includes(floating_ui_utils_getSide(placement))?"y":"x"}function getAlignmentAxis(placement){return getOppositeAxis(floating_ui_utils_getSideAxis(placement))}function floating_ui_utils_getOppositeAlignmentPlacement(placement){return placement.replace(/start|end/g,(alignment=>oppositeAlignmentMap[alignment]))}function getOppositePlacement(placement){return placement.replace(/left|right|bottom|top/g,(side=>oppositeSideMap[side]))}function floating_ui_utils_getPaddingObject(padding){return"number"!=typeof padding?function expandPaddingObject(padding){return{top:0,right:0,bottom:0,left:0,...padding}}(padding):{top:padding,right:padding,bottom:padding,left:padding}}function floating_ui_utils_rectToClientRect(rect){const{x,y,width,height}=rect;return{width,height,top:y,left:x,right:x+width,bottom:y+height,x,y}}function computeCoordsFromPlacement(_ref,placement,rtl){let{reference,floating}=_ref;const sideAxis=floating_ui_utils_getSideAxis(placement),alignmentAxis=getAlignmentAxis(placement),alignLength=getAxisLength(alignmentAxis),side=floating_ui_utils_getSide(placement),isVertical="y"===sideAxis,commonX=reference.x+reference.width/2-floating.width/2,commonY=reference.y+reference.height/2-floating.height/2,commonAlign=reference[alignLength]/2-floating[alignLength]/2;let coords;switch(side){case"top":coords={x:commonX,y:reference.y-floating.height};break;case"bottom":coords={x:commonX,y:reference.y+reference.height};break;case"right":coords={x:reference.x+reference.width,y:commonY};break;case"left":coords={x:reference.x-floating.width,y:commonY};break;default:coords={x:reference.x,y:reference.y}}switch(floating_ui_utils_getAlignment(placement)){case"start":coords[alignmentAxis]-=commonAlign*(rtl&&isVertical?-1:1);break;case"end":coords[alignmentAxis]+=commonAlign*(rtl&&isVertical?-1:1)}return coords}async function detectOverflow(state,options){var _await$platform$isEle;void 0===options&&(options={});const{x,y,platform,rects,elements,strategy}=state,{boundary="clippingAncestors",rootBoundary="viewport",elementContext="floating",altBoundary=!1,padding=0}=floating_ui_utils_evaluate(options,state),paddingObject=floating_ui_utils_getPaddingObject(padding),element=elements[altBoundary?"floating"===elementContext?"reference":"floating":elementContext],clippingClientRect=floating_ui_utils_rectToClientRect(await platform.getClippingRect({element:null==(_await$platform$isEle=await(null==platform.isElement?void 0:platform.isElement(element)))||_await$platform$isEle?element:element.contextElement||await(null==platform.getDocumentElement?void 0:platform.getDocumentElement(elements.floating)),boundary,rootBoundary,strategy})),rect="floating"===elementContext?{x,y,width:rects.floating.width,height:rects.floating.height}:rects.reference,offsetParent=await(null==platform.getOffsetParent?void 0:platform.getOffsetParent(elements.floating)),offsetScale=await(null==platform.isElement?void 0:platform.isElement(offsetParent))&&await(null==platform.getScale?void 0:platform.getScale(offsetParent))||{x:1,y:1},elementClientRect=floating_ui_utils_rectToClientRect(platform.convertOffsetParentRelativeRectToViewportRelativeRect?await platform.convertOffsetParentRelativeRectToViewportRelativeRect({elements,rect,offsetParent,strategy}):rect);return{top:(clippingClientRect.top-elementClientRect.top+paddingObject.top)/offsetScale.y,bottom:(elementClientRect.bottom-clippingClientRect.bottom+paddingObject.bottom)/offsetScale.y,left:(clippingClientRect.left-elementClientRect.left+paddingObject.left)/offsetScale.x,right:(elementClientRect.right-clippingClientRect.right+paddingObject.right)/offsetScale.x}}function getSideOffsets(overflow,rect){return{top:overflow.top-rect.height,right:overflow.right-rect.width,bottom:overflow.bottom-rect.height,left:overflow.left-rect.width}}function isAnySideFullyClipped(overflow){return sides.some((side=>overflow[side]>=0))}function hasWindow(){return"undefined"!=typeof window}function getNodeName(node){return isNode(node)?(node.nodeName||"").toLowerCase():"#document"}function getWindow(node){var _node$ownerDocument;return(null==node||null==(_node$ownerDocument=node.ownerDocument)?void 0:_node$ownerDocument.defaultView)||window}function getDocumentElement(node){var _ref;return null==(_ref=(isNode(node)?node.ownerDocument:node.document)||window.document)?void 0:_ref.documentElement}function isNode(value){return!!hasWindow()&&(value instanceof Node||value instanceof getWindow(value).Node)}function isElement(value){return!!hasWindow()&&(value instanceof Element||value instanceof getWindow(value).Element)}function isHTMLElement(value){return!!hasWindow()&&(value instanceof HTMLElement||value instanceof getWindow(value).HTMLElement)}function isShadowRoot(value){return!(!hasWindow()||"undefined"==typeof ShadowRoot)&&(value instanceof ShadowRoot||value instanceof getWindow(value).ShadowRoot)}function isOverflowElement(element){const{overflow,overflowX,overflowY,display}=getComputedStyle(element);return/auto|scroll|overlay|hidden|clip/.test(overflow+overflowY+overflowX)&&!["inline","contents"].includes(display)}function isTableElement(element){return["table","td","th"].includes(getNodeName(element))}function isTopLayer(element){return[":popover-open",":modal"].some((selector=>{try{return element.matches(selector)}catch(e){return!1}}))}function isContainingBlock(elementOrCss){const webkit=isWebKit(),css=isElement(elementOrCss)?getComputedStyle(elementOrCss):elementOrCss;return["transform","translate","scale","rotate","perspective"].some((value=>!!css[value]&&"none"!==css[value]))||!!css.containerType&&"normal"!==css.containerType||!webkit&&!!css.backdropFilter&&"none"!==css.backdropFilter||!webkit&&!!css.filter&&"none"!==css.filter||["transform","translate","scale","rotate","perspective","filter"].some((value=>(css.willChange||"").includes(value)))||["paint","layout","strict","content"].some((value=>(css.contain||"").includes(value)))}function isWebKit(){return!("undefined"==typeof CSS||!CSS.supports)&&CSS.supports("-webkit-backdrop-filter","none")}function isLastTraversableNode(node){return["html","body","#document"].includes(getNodeName(node))}function getComputedStyle(element){return getWindow(element).getComputedStyle(element)}function getNodeScroll(element){return isElement(element)?{scrollLeft:element.scrollLeft,scrollTop:element.scrollTop}:{scrollLeft:element.scrollX,scrollTop:element.scrollY}}function getParentNode(node){if("html"===getNodeName(node))return node;const result=node.assignedSlot||node.parentNode||isShadowRoot(node)&&node.host||getDocumentElement(node);return isShadowRoot(result)?result.host:result}function getNearestOverflowAncestor(node){const parentNode=getParentNode(node);return isLastTraversableNode(parentNode)?node.ownerDocument?node.ownerDocument.body:node.body:isHTMLElement(parentNode)&&isOverflowElement(parentNode)?parentNode:getNearestOverflowAncestor(parentNode)}function getOverflowAncestors(node,list,traverseIframes){var _node$ownerDocument2;void 0===list&&(list=[]),void 0===traverseIframes&&(traverseIframes=!0);const scrollableAncestor=getNearestOverflowAncestor(node),isBody=scrollableAncestor===(null==(_node$ownerDocument2=node.ownerDocument)?void 0:_node$ownerDocument2.body),win=getWindow(scrollableAncestor);if(isBody){const frameElement=getFrameElement(win);return list.concat(win,win.visualViewport||[],isOverflowElement(scrollableAncestor)?scrollableAncestor:[],frameElement&&traverseIframes?getOverflowAncestors(frameElement):[])}return list.concat(scrollableAncestor,getOverflowAncestors(scrollableAncestor,[],traverseIframes))}function getFrameElement(win){return win.parent&&Object.getPrototypeOf(win.parent)?win.frameElement:null}function getCssDimensions(element){const css=getComputedStyle(element);let width=parseFloat(css.width)||0,height=parseFloat(css.height)||0;const hasOffset=isHTMLElement(element),offsetWidth=hasOffset?element.offsetWidth:width,offsetHeight=hasOffset?element.offsetHeight:height,shouldFallback=round(width)!==offsetWidth||round(height)!==offsetHeight;return shouldFallback&&(width=offsetWidth,height=offsetHeight),{width,height,$:shouldFallback}}function unwrapElement(element){return isElement(element)?element:element.contextElement}function getScale(element){const domElement=unwrapElement(element);if(!isHTMLElement(domElement))return createCoords(1);const rect=domElement.getBoundingClientRect(),{width,height,$}=getCssDimensions(domElement);let x=($?round(rect.width):rect.width)/width,y=($?round(rect.height):rect.height)/height;return x&&Number.isFinite(x)||(x=1),y&&Number.isFinite(y)||(y=1),{x,y}}const noOffsets=createCoords(0);function getVisualOffsets(element){const win=getWindow(element);return isWebKit()&&win.visualViewport?{x:win.visualViewport.offsetLeft,y:win.visualViewport.offsetTop}:noOffsets}function getBoundingClientRect(element,includeScale,isFixedStrategy,offsetParent){void 0===includeScale&&(includeScale=!1),void 0===isFixedStrategy&&(isFixedStrategy=!1);const clientRect=element.getBoundingClientRect(),domElement=unwrapElement(element);let scale=createCoords(1);includeScale&&(offsetParent?isElement(offsetParent)&&(scale=getScale(offsetParent)):scale=getScale(element));const visualOffsets=function shouldAddVisualOffsets(element,isFixed,floatingOffsetParent){return void 0===isFixed&&(isFixed=!1),!(!floatingOffsetParent||isFixed&&floatingOffsetParent!==getWindow(element))&&isFixed}(domElement,isFixedStrategy,offsetParent)?getVisualOffsets(domElement):createCoords(0);let x=(clientRect.left+visualOffsets.x)/scale.x,y=(clientRect.top+visualOffsets.y)/scale.y,width=clientRect.width/scale.x,height=clientRect.height/scale.y;if(domElement){const win=getWindow(domElement),offsetWin=offsetParent&&isElement(offsetParent)?getWindow(offsetParent):offsetParent;let currentWin=win,currentIFrame=getFrameElement(currentWin);for(;currentIFrame&&offsetParent&&offsetWin!==currentWin;){const iframeScale=getScale(currentIFrame),iframeRect=currentIFrame.getBoundingClientRect(),css=getComputedStyle(currentIFrame),left=iframeRect.left+(currentIFrame.clientLeft+parseFloat(css.paddingLeft))*iframeScale.x,top=iframeRect.top+(currentIFrame.clientTop+parseFloat(css.paddingTop))*iframeScale.y;x*=iframeScale.x,y*=iframeScale.y,width*=iframeScale.x,height*=iframeScale.y,x+=left,y+=top,currentWin=getWindow(currentIFrame),currentIFrame=getFrameElement(currentWin)}}return floating_ui_utils_rectToClientRect({width,height,x,y})}function getWindowScrollBarX(element,rect){const leftScroll=getNodeScroll(element).scrollLeft;return rect?rect.left+leftScroll:getBoundingClientRect(getDocumentElement(element)).left+leftScroll}function getHTMLOffset(documentElement,scroll,ignoreScrollbarX){void 0===ignoreScrollbarX&&(ignoreScrollbarX=!1);const htmlRect=documentElement.getBoundingClientRect();return{x:htmlRect.left+scroll.scrollLeft-(ignoreScrollbarX?0:getWindowScrollBarX(documentElement,htmlRect)),y:htmlRect.top+scroll.scrollTop}}function getClientRectFromClippingAncestor(element,clippingAncestor,strategy){let rect;if("viewport"===clippingAncestor)rect=function getViewportRect(element,strategy){const win=getWindow(element),html=getDocumentElement(element),visualViewport=win.visualViewport;let width=html.clientWidth,height=html.clientHeight,x=0,y=0;if(visualViewport){width=visualViewport.width,height=visualViewport.height;const visualViewportBased=isWebKit();(!visualViewportBased||visualViewportBased&&"fixed"===strategy)&&(x=visualViewport.offsetLeft,y=visualViewport.offsetTop)}return{width,height,x,y}}(element,strategy);else if("document"===clippingAncestor)rect=function getDocumentRect(element){const html=getDocumentElement(element),scroll=getNodeScroll(element),body=element.ownerDocument.body,width=floating_ui_utils_max(html.scrollWidth,html.clientWidth,body.scrollWidth,body.clientWidth),height=floating_ui_utils_max(html.scrollHeight,html.clientHeight,body.scrollHeight,body.clientHeight);let x=-scroll.scrollLeft+getWindowScrollBarX(element);const y=-scroll.scrollTop;return"rtl"===getComputedStyle(body).direction&&(x+=floating_ui_utils_max(html.clientWidth,body.clientWidth)-width),{width,height,x,y}}(getDocumentElement(element));else if(isElement(clippingAncestor))rect=function getInnerBoundingClientRect(element,strategy){const clientRect=getBoundingClientRect(element,!0,"fixed"===strategy),top=clientRect.top+element.clientTop,left=clientRect.left+element.clientLeft,scale=isHTMLElement(element)?getScale(element):createCoords(1);return{width:element.clientWidth*scale.x,height:element.clientHeight*scale.y,x:left*scale.x,y:top*scale.y}}(clippingAncestor,strategy);else{const visualOffsets=getVisualOffsets(element);rect={x:clippingAncestor.x-visualOffsets.x,y:clippingAncestor.y-visualOffsets.y,width:clippingAncestor.width,height:clippingAncestor.height}}return floating_ui_utils_rectToClientRect(rect)}function hasFixedPositionAncestor(element,stopNode){const parentNode=getParentNode(element);return!(parentNode===stopNode||!isElement(parentNode)||isLastTraversableNode(parentNode))&&("fixed"===getComputedStyle(parentNode).position||hasFixedPositionAncestor(parentNode,stopNode))}function getRectRelativeToOffsetParent(element,offsetParent,strategy){const isOffsetParentAnElement=isHTMLElement(offsetParent),documentElement=getDocumentElement(offsetParent),isFixed="fixed"===strategy,rect=getBoundingClientRect(element,!0,isFixed,offsetParent);let scroll={scrollLeft:0,scrollTop:0};const offsets=createCoords(0);if(isOffsetParentAnElement||!isOffsetParentAnElement&&!isFixed)if(("body"!==getNodeName(offsetParent)||isOverflowElement(documentElement))&&(scroll=getNodeScroll(offsetParent)),isOffsetParentAnElement){const offsetRect=getBoundingClientRect(offsetParent,!0,isFixed,offsetParent);offsets.x=offsetRect.x+offsetParent.clientLeft,offsets.y=offsetRect.y+offsetParent.clientTop}else documentElement&&(offsets.x=getWindowScrollBarX(documentElement));const htmlOffset=!documentElement||isOffsetParentAnElement||isFixed?createCoords(0):getHTMLOffset(documentElement,scroll);return{x:rect.left+scroll.scrollLeft-offsets.x-htmlOffset.x,y:rect.top+scroll.scrollTop-offsets.y-htmlOffset.y,width:rect.width,height:rect.height}}function isStaticPositioned(element){return"static"===getComputedStyle(element).position}function getTrueOffsetParent(element,polyfill){if(!isHTMLElement(element)||"fixed"===getComputedStyle(element).position)return null;if(polyfill)return polyfill(element);let rawOffsetParent=element.offsetParent;return getDocumentElement(element)===rawOffsetParent&&(rawOffsetParent=rawOffsetParent.ownerDocument.body),rawOffsetParent}function getOffsetParent(element,polyfill){const win=getWindow(element);if(isTopLayer(element))return win;if(!isHTMLElement(element)){let svgOffsetParent=getParentNode(element);for(;svgOffsetParent&&!isLastTraversableNode(svgOffsetParent);){if(isElement(svgOffsetParent)&&!isStaticPositioned(svgOffsetParent))return svgOffsetParent;svgOffsetParent=getParentNode(svgOffsetParent)}return win}let offsetParent=getTrueOffsetParent(element,polyfill);for(;offsetParent&&isTableElement(offsetParent)&&isStaticPositioned(offsetParent);)offsetParent=getTrueOffsetParent(offsetParent,polyfill);return offsetParent&&isLastTraversableNode(offsetParent)&&isStaticPositioned(offsetParent)&&!isContainingBlock(offsetParent)?win:offsetParent||function getContainingBlock(element){let currentNode=getParentNode(element);for(;isHTMLElement(currentNode)&&!isLastTraversableNode(currentNode);){if(isContainingBlock(currentNode))return currentNode;if(isTopLayer(currentNode))return null;currentNode=getParentNode(currentNode)}return null}(element)||win}const platform={convertOffsetParentRelativeRectToViewportRelativeRect:function convertOffsetParentRelativeRectToViewportRelativeRect(_ref){let{elements,rect,offsetParent,strategy}=_ref;const isFixed="fixed"===strategy,documentElement=getDocumentElement(offsetParent),topLayer=!!elements&&isTopLayer(elements.floating);if(offsetParent===documentElement||topLayer&&isFixed)return rect;let scroll={scrollLeft:0,scrollTop:0},scale=createCoords(1);const offsets=createCoords(0),isOffsetParentAnElement=isHTMLElement(offsetParent);if((isOffsetParentAnElement||!isOffsetParentAnElement&&!isFixed)&&(("body"!==getNodeName(offsetParent)||isOverflowElement(documentElement))&&(scroll=getNodeScroll(offsetParent)),isHTMLElement(offsetParent))){const offsetRect=getBoundingClientRect(offsetParent);scale=getScale(offsetParent),offsets.x=offsetRect.x+offsetParent.clientLeft,offsets.y=offsetRect.y+offsetParent.clientTop}const htmlOffset=!documentElement||isOffsetParentAnElement||isFixed?createCoords(0):getHTMLOffset(documentElement,scroll,!0);return{width:rect.width*scale.x,height:rect.height*scale.y,x:rect.x*scale.x-scroll.scrollLeft*scale.x+offsets.x+htmlOffset.x,y:rect.y*scale.y-scroll.scrollTop*scale.y+offsets.y+htmlOffset.y}},getDocumentElement,getClippingRect:function getClippingRect(_ref){let{element,boundary,rootBoundary,strategy}=_ref;const clippingAncestors=[..."clippingAncestors"===boundary?isTopLayer(element)?[]:function getClippingElementAncestors(element,cache){const cachedResult=cache.get(element);if(cachedResult)return cachedResult;let result=getOverflowAncestors(element,[],!1).filter((el=>isElement(el)&&"body"!==getNodeName(el))),currentContainingBlockComputedStyle=null;const elementIsFixed="fixed"===getComputedStyle(element).position;let currentNode=elementIsFixed?getParentNode(element):element;for(;isElement(currentNode)&&!isLastTraversableNode(currentNode);){const computedStyle=getComputedStyle(currentNode),currentNodeIsContaining=isContainingBlock(currentNode);currentNodeIsContaining||"fixed"!==computedStyle.position||(currentContainingBlockComputedStyle=null),(elementIsFixed?!currentNodeIsContaining&&!currentContainingBlockComputedStyle:!currentNodeIsContaining&&"static"===computedStyle.position&&currentContainingBlockComputedStyle&&["absolute","fixed"].includes(currentContainingBlockComputedStyle.position)||isOverflowElement(currentNode)&&!currentNodeIsContaining&&hasFixedPositionAncestor(element,currentNode))?result=result.filter((ancestor=>ancestor!==currentNode)):currentContainingBlockComputedStyle=computedStyle,currentNode=getParentNode(currentNode)}return cache.set(element,result),result}(element,this._c):[].concat(boundary),rootBoundary],firstClippingAncestor=clippingAncestors[0],clippingRect=clippingAncestors.reduce(((accRect,clippingAncestor)=>{const rect=getClientRectFromClippingAncestor(element,clippingAncestor,strategy);return accRect.top=floating_ui_utils_max(rect.top,accRect.top),accRect.right=floating_ui_utils_min(rect.right,accRect.right),accRect.bottom=floating_ui_utils_min(rect.bottom,accRect.bottom),accRect.left=floating_ui_utils_max(rect.left,accRect.left),accRect}),getClientRectFromClippingAncestor(element,firstClippingAncestor,strategy));return{width:clippingRect.right-clippingRect.left,height:clippingRect.bottom-clippingRect.top,x:clippingRect.left,y:clippingRect.top}},getOffsetParent,getElementRects:async function(data){const getOffsetParentFn=this.getOffsetParent||getOffsetParent,getDimensionsFn=this.getDimensions,floatingDimensions=await getDimensionsFn(data.floating);return{reference:getRectRelativeToOffsetParent(data.reference,await getOffsetParentFn(data.floating),data.strategy),floating:{x:0,y:0,width:floatingDimensions.width,height:floatingDimensions.height}}},getClientRects:function getClientRects(element){return Array.from(element.getClientRects())},getDimensions:function getDimensions(element){const{width,height}=getCssDimensions(element);return{width,height}},getScale,isElement,isRTL:function isRTL(element){return"rtl"===getComputedStyle(element).direction}};function rectsAreEqual(a,b){return a.x===b.x&&a.y===b.y&&a.width===b.width&&a.height===b.height}function autoUpdate(reference,floating,update,options){void 0===options&&(options={});const{ancestorScroll=!0,ancestorResize=!0,elementResize="function"==typeof ResizeObserver,layoutShift="function"==typeof IntersectionObserver,animationFrame=!1}=options,referenceEl=unwrapElement(reference),ancestors=ancestorScroll||ancestorResize?[...referenceEl?getOverflowAncestors(referenceEl):[],...getOverflowAncestors(floating)]:[];ancestors.forEach((ancestor=>{ancestorScroll&&ancestor.addEventListener("scroll",update,{passive:!0}),ancestorResize&&ancestor.addEventListener("resize",update)}));const cleanupIo=referenceEl&&layoutShift?function observeMove(element,onMove){let timeoutId,io=null;const root=getDocumentElement(element);function cleanup(){var _io;clearTimeout(timeoutId),null==(_io=io)||_io.disconnect(),io=null}return function refresh(skip,threshold){void 0===skip&&(skip=!1),void 0===threshold&&(threshold=1),cleanup();const elementRectForRootMargin=element.getBoundingClientRect(),{left,top,width,height}=elementRectForRootMargin;if(skip||onMove(),!width||!height)return;const options={rootMargin:-floor(top)+"px "+-floor(root.clientWidth-(left+width))+"px "+-floor(root.clientHeight-(top+height))+"px "+-floor(left)+"px",threshold:floating_ui_utils_max(0,floating_ui_utils_min(1,threshold))||1};let isFirstUpdate=!0;function handleObserve(entries){const ratio=entries[0].intersectionRatio;if(ratio!==threshold){if(!isFirstUpdate)return refresh();ratio?refresh(!1,ratio):timeoutId=setTimeout((()=>{refresh(!1,1e-7)}),1e3)}1!==ratio||rectsAreEqual(elementRectForRootMargin,element.getBoundingClientRect())||refresh(),isFirstUpdate=!1}try{io=new IntersectionObserver(handleObserve,{...options,root:root.ownerDocument})}catch(e){io=new IntersectionObserver(handleObserve,options)}io.observe(element)}(!0),cleanup}(referenceEl,update):null;let frameId,reobserveFrame=-1,resizeObserver=null;elementResize&&(resizeObserver=new ResizeObserver((_ref=>{let[firstEntry]=_ref;firstEntry&&firstEntry.target===referenceEl&&resizeObserver&&(resizeObserver.unobserve(floating),cancelAnimationFrame(reobserveFrame),reobserveFrame=requestAnimationFrame((()=>{var _resizeObserver;null==(_resizeObserver=resizeObserver)||_resizeObserver.observe(floating)}))),update()})),referenceEl&&!animationFrame&&resizeObserver.observe(referenceEl),resizeObserver.observe(floating));let prevRefRect=animationFrame?getBoundingClientRect(reference):null;return animationFrame&&function frameLoop(){const nextRefRect=getBoundingClientRect(reference);prevRefRect&&!rectsAreEqual(prevRefRect,nextRefRect)&&update();prevRefRect=nextRefRect,frameId=requestAnimationFrame(frameLoop)}(),update(),()=>{var _resizeObserver2;ancestors.forEach((ancestor=>{ancestorScroll&&ancestor.removeEventListener("scroll",update),ancestorResize&&ancestor.removeEventListener("resize",update)})),null==cleanupIo||cleanupIo(),null==(_resizeObserver2=resizeObserver)||_resizeObserver2.disconnect(),resizeObserver=null,animationFrame&&cancelAnimationFrame(frameId)}}const floating_ui_dom_offset=function(options){return void 0===options&&(options=0),{name:"offset",options,async fn(state){var _middlewareData$offse,_middlewareData$arrow;const{x,y,placement,middlewareData}=state,diffCoords=await async function convertValueToCoords(state,options){const{placement,platform,elements}=state,rtl=await(null==platform.isRTL?void 0:platform.isRTL(elements.floating)),side=floating_ui_utils_getSide(placement),alignment=floating_ui_utils_getAlignment(placement),isVertical="y"===floating_ui_utils_getSideAxis(placement),mainAxisMulti=["left","top"].includes(side)?-1:1,crossAxisMulti=rtl&&isVertical?-1:1,rawValue=floating_ui_utils_evaluate(options,state);let{mainAxis,crossAxis,alignmentAxis}="number"==typeof rawValue?{mainAxis:rawValue,crossAxis:0,alignmentAxis:null}:{mainAxis:rawValue.mainAxis||0,crossAxis:rawValue.crossAxis||0,alignmentAxis:rawValue.alignmentAxis};return alignment&&"number"==typeof alignmentAxis&&(crossAxis="end"===alignment?-1*alignmentAxis:alignmentAxis),isVertical?{x:crossAxis*crossAxisMulti,y:mainAxis*mainAxisMulti}:{x:mainAxis*mainAxisMulti,y:crossAxis*crossAxisMulti}}(state,options);return placement===(null==(_middlewareData$offse=middlewareData.offset)?void 0:_middlewareData$offse.placement)&&null!=(_middlewareData$arrow=middlewareData.arrow)&&_middlewareData$arrow.alignmentOffset?{}:{x:x+diffCoords.x,y:y+diffCoords.y,data:{...diffCoords,placement}}}}},floating_ui_dom_shift=function(options){return void 0===options&&(options={}),{name:"shift",options,async fn(state){const{x,y,placement}=state,{mainAxis:checkMainAxis=!0,crossAxis:checkCrossAxis=!1,limiter={fn:_ref=>{let{x,y}=_ref;return{x,y}}},...detectOverflowOptions}=floating_ui_utils_evaluate(options,state),coords={x,y},overflow=await detectOverflow(state,detectOverflowOptions),crossAxis=floating_ui_utils_getSideAxis(floating_ui_utils_getSide(placement)),mainAxis=getOppositeAxis(crossAxis);let mainAxisCoord=coords[mainAxis],crossAxisCoord=coords[crossAxis];if(checkMainAxis){const maxSide="y"===mainAxis?"bottom":"right";mainAxisCoord=floating_ui_utils_clamp(mainAxisCoord+overflow["y"===mainAxis?"top":"left"],mainAxisCoord,mainAxisCoord-overflow[maxSide])}if(checkCrossAxis){const maxSide="y"===crossAxis?"bottom":"right";crossAxisCoord=floating_ui_utils_clamp(crossAxisCoord+overflow["y"===crossAxis?"top":"left"],crossAxisCoord,crossAxisCoord-overflow[maxSide])}const limitedCoords=limiter.fn({...state,[mainAxis]:mainAxisCoord,[crossAxis]:crossAxisCoord});return{...limitedCoords,data:{x:limitedCoords.x-x,y:limitedCoords.y-y,enabled:{[mainAxis]:checkMainAxis,[crossAxis]:checkCrossAxis}}}}}},floating_ui_dom_flip=function(options){return void 0===options&&(options={}),{name:"flip",options,async fn(state){var _middlewareData$arrow,_middlewareData$flip;const{placement,middlewareData,rects,initialPlacement,platform,elements}=state,{mainAxis:checkMainAxis=!0,crossAxis:checkCrossAxis=!0,fallbackPlacements:specifiedFallbackPlacements,fallbackStrategy="bestFit",fallbackAxisSideDirection="none",flipAlignment=!0,...detectOverflowOptions}=floating_ui_utils_evaluate(options,state);if(null!=(_middlewareData$arrow=middlewareData.arrow)&&_middlewareData$arrow.alignmentOffset)return{};const side=floating_ui_utils_getSide(placement),initialSideAxis=floating_ui_utils_getSideAxis(initialPlacement),isBasePlacement=floating_ui_utils_getSide(initialPlacement)===initialPlacement,rtl=await(null==platform.isRTL?void 0:platform.isRTL(elements.floating)),fallbackPlacements=specifiedFallbackPlacements||(isBasePlacement||!flipAlignment?[getOppositePlacement(initialPlacement)]:function getExpandedPlacements(placement){const oppositePlacement=getOppositePlacement(placement);return[floating_ui_utils_getOppositeAlignmentPlacement(placement),oppositePlacement,floating_ui_utils_getOppositeAlignmentPlacement(oppositePlacement)]}(initialPlacement)),hasFallbackAxisSideDirection="none"!==fallbackAxisSideDirection;!specifiedFallbackPlacements&&hasFallbackAxisSideDirection&&fallbackPlacements.push(...function getOppositeAxisPlacements(placement,flipAlignment,direction,rtl){const alignment=floating_ui_utils_getAlignment(placement);let list=function getSideList(side,isStart,rtl){const lr=["left","right"],rl=["right","left"],tb=["top","bottom"],bt=["bottom","top"];switch(side){case"top":case"bottom":return rtl?isStart?rl:lr:isStart?lr:rl;case"left":case"right":return isStart?tb:bt;default:return[]}}(floating_ui_utils_getSide(placement),"start"===direction,rtl);return alignment&&(list=list.map((side=>side+"-"+alignment)),flipAlignment&&(list=list.concat(list.map(floating_ui_utils_getOppositeAlignmentPlacement)))),list}(initialPlacement,flipAlignment,fallbackAxisSideDirection,rtl));const placements=[initialPlacement,...fallbackPlacements],overflow=await detectOverflow(state,detectOverflowOptions),overflows=[];let overflowsData=(null==(_middlewareData$flip=middlewareData.flip)?void 0:_middlewareData$flip.overflows)||[];if(checkMainAxis&&overflows.push(overflow[side]),checkCrossAxis){const sides=function floating_ui_utils_getAlignmentSides(placement,rects,rtl){void 0===rtl&&(rtl=!1);const alignment=floating_ui_utils_getAlignment(placement),alignmentAxis=getAlignmentAxis(placement),length=getAxisLength(alignmentAxis);let mainAlignmentSide="x"===alignmentAxis?alignment===(rtl?"end":"start")?"right":"left":"start"===alignment?"bottom":"top";return rects.reference[length]>rects.floating[length]&&(mainAlignmentSide=getOppositePlacement(mainAlignmentSide)),[mainAlignmentSide,getOppositePlacement(mainAlignmentSide)]}(placement,rects,rtl);overflows.push(overflow[sides[0]],overflow[sides[1]])}if(overflowsData=[...overflowsData,{placement,overflows}],!overflows.every((side=>side<=0))){var _middlewareData$flip2,_overflowsData$filter;const nextIndex=((null==(_middlewareData$flip2=middlewareData.flip)?void 0:_middlewareData$flip2.index)||0)+1,nextPlacement=placements[nextIndex];if(nextPlacement){var _overflowsData$;const ignoreCrossAxisOverflow="alignment"===checkCrossAxis&&initialSideAxis!==floating_ui_utils_getSideAxis(nextPlacement),hasInitialMainAxisOverflow=(null==(_overflowsData$=overflowsData[0])?void 0:_overflowsData$.overflows[0])>0;if(!ignoreCrossAxisOverflow||hasInitialMainAxisOverflow)return{data:{index:nextIndex,overflows:overflowsData},reset:{placement:nextPlacement}}}let resetPlacement=null==(_overflowsData$filter=overflowsData.filter((d=>d.overflows[0]<=0)).sort(((a,b)=>a.overflows[1]-b.overflows[1]))[0])?void 0:_overflowsData$filter.placement;if(!resetPlacement)switch(fallbackStrategy){case"bestFit":{var _overflowsData$filter2;const placement=null==(_overflowsData$filter2=overflowsData.filter((d=>{if(hasFallbackAxisSideDirection){const currentSideAxis=floating_ui_utils_getSideAxis(d.placement);return currentSideAxis===initialSideAxis||"y"===currentSideAxis}return!0})).map((d=>[d.placement,d.overflows.filter((overflow=>overflow>0)).reduce(((acc,overflow)=>acc+overflow),0)])).sort(((a,b)=>a[1]-b[1]))[0])?void 0:_overflowsData$filter2[0];placement&&(resetPlacement=placement);break}case"initialPlacement":resetPlacement=initialPlacement}if(placement!==resetPlacement)return{reset:{placement:resetPlacement}}}return{}}}},floating_ui_dom_size=function(options){return void 0===options&&(options={}),{name:"size",options,async fn(state){var _state$middlewareData,_state$middlewareData2;const{placement,rects,platform,elements}=state,{apply=()=>{},...detectOverflowOptions}=floating_ui_utils_evaluate(options,state),overflow=await detectOverflow(state,detectOverflowOptions),side=floating_ui_utils_getSide(placement),alignment=floating_ui_utils_getAlignment(placement),isYAxis="y"===floating_ui_utils_getSideAxis(placement),{width,height}=rects.floating;let heightSide,widthSide;"top"===side||"bottom"===side?(heightSide=side,widthSide=alignment===(await(null==platform.isRTL?void 0:platform.isRTL(elements.floating))?"start":"end")?"left":"right"):(widthSide=side,heightSide="end"===alignment?"top":"bottom");const maximumClippingHeight=height-overflow.top-overflow.bottom,maximumClippingWidth=width-overflow.left-overflow.right,overflowAvailableHeight=floating_ui_utils_min(height-overflow[heightSide],maximumClippingHeight),overflowAvailableWidth=floating_ui_utils_min(width-overflow[widthSide],maximumClippingWidth),noShift=!state.middlewareData.shift;let availableHeight=overflowAvailableHeight,availableWidth=overflowAvailableWidth;if(null!=(_state$middlewareData=state.middlewareData.shift)&&_state$middlewareData.enabled.x&&(availableWidth=maximumClippingWidth),null!=(_state$middlewareData2=state.middlewareData.shift)&&_state$middlewareData2.enabled.y&&(availableHeight=maximumClippingHeight),noShift&&!alignment){const xMin=floating_ui_utils_max(overflow.left,0),xMax=floating_ui_utils_max(overflow.right,0),yMin=floating_ui_utils_max(overflow.top,0),yMax=floating_ui_utils_max(overflow.bottom,0);isYAxis?availableWidth=width-2*(0!==xMin||0!==xMax?xMin+xMax:floating_ui_utils_max(overflow.left,overflow.right)):availableHeight=height-2*(0!==yMin||0!==yMax?yMin+yMax:floating_ui_utils_max(overflow.top,overflow.bottom))}await apply({...state,availableWidth,availableHeight});const nextDimensions=await platform.getDimensions(elements.floating);return width!==nextDimensions.width||height!==nextDimensions.height?{reset:{rects:!0}}:{}}}},floating_ui_dom_hide=function(options){return void 0===options&&(options={}),{name:"hide",options,async fn(state){const{rects}=state,{strategy="referenceHidden",...detectOverflowOptions}=floating_ui_utils_evaluate(options,state);switch(strategy){case"referenceHidden":{const offsets=getSideOffsets(await detectOverflow(state,{...detectOverflowOptions,elementContext:"reference"}),rects.reference);return{data:{referenceHiddenOffsets:offsets,referenceHidden:isAnySideFullyClipped(offsets)}}}case"escaped":{const offsets=getSideOffsets(await detectOverflow(state,{...detectOverflowOptions,altBoundary:!0}),rects.floating);return{data:{escapedOffsets:offsets,escaped:isAnySideFullyClipped(offsets)}}}default:return{}}}}},floating_ui_dom_arrow=options=>({name:"arrow",options,async fn(state){const{x,y,placement,rects,platform,elements,middlewareData}=state,{element,padding=0}=floating_ui_utils_evaluate(options,state)||{};if(null==element)return{};const paddingObject=floating_ui_utils_getPaddingObject(padding),coords={x,y},axis=getAlignmentAxis(placement),length=getAxisLength(axis),arrowDimensions=await platform.getDimensions(element),isYAxis="y"===axis,minProp=isYAxis?"top":"left",maxProp=isYAxis?"bottom":"right",clientProp=isYAxis?"clientHeight":"clientWidth",endDiff=rects.reference[length]+rects.reference[axis]-coords[axis]-rects.floating[length],startDiff=coords[axis]-rects.reference[axis],arrowOffsetParent=await(null==platform.getOffsetParent?void 0:platform.getOffsetParent(element));let clientSize=arrowOffsetParent?arrowOffsetParent[clientProp]:0;clientSize&&await(null==platform.isElement?void 0:platform.isElement(arrowOffsetParent))||(clientSize=elements.floating[clientProp]||rects.floating[length]);const centerToReference=endDiff/2-startDiff/2,largestPossiblePadding=clientSize/2-arrowDimensions[length]/2-1,minPadding=floating_ui_utils_min(paddingObject[minProp],largestPossiblePadding),maxPadding=floating_ui_utils_min(paddingObject[maxProp],largestPossiblePadding),min$1=minPadding,max=clientSize-arrowDimensions[length]-maxPadding,center=clientSize/2-arrowDimensions[length]/2+centerToReference,offset=floating_ui_utils_clamp(min$1,center,max),shouldAddOffset=!middlewareData.arrow&&null!=floating_ui_utils_getAlignment(placement)&&center!==offset&&rects.reference[length]/2-(center<min$1?minPadding:maxPadding)-arrowDimensions[length]/2<0,alignmentOffset=shouldAddOffset?center<min$1?center-min$1:center-max:0;return{[axis]:coords[axis]+alignmentOffset,data:{[axis]:offset,centerOffset:center-offset-alignmentOffset,...shouldAddOffset&&{alignmentOffset}},reset:shouldAddOffset}}}),floating_ui_dom_limitShift=function(options){return void 0===options&&(options={}),{options,fn(state){const{x,y,placement,rects,middlewareData}=state,{offset=0,mainAxis:checkMainAxis=!0,crossAxis:checkCrossAxis=!0}=floating_ui_utils_evaluate(options,state),coords={x,y},crossAxis=floating_ui_utils_getSideAxis(placement),mainAxis=getOppositeAxis(crossAxis);let mainAxisCoord=coords[mainAxis],crossAxisCoord=coords[crossAxis];const rawOffset=floating_ui_utils_evaluate(offset,state),computedOffset="number"==typeof rawOffset?{mainAxis:rawOffset,crossAxis:0}:{mainAxis:0,crossAxis:0,...rawOffset};if(checkMainAxis){const len="y"===mainAxis?"height":"width",limitMin=rects.reference[mainAxis]-rects.floating[len]+computedOffset.mainAxis,limitMax=rects.reference[mainAxis]+rects.reference[len]-computedOffset.mainAxis;mainAxisCoord<limitMin?mainAxisCoord=limitMin:mainAxisCoord>limitMax&&(mainAxisCoord=limitMax)}if(checkCrossAxis){var _middlewareData$offse,_middlewareData$offse2;const len="y"===mainAxis?"width":"height",isOriginSide=["top","left"].includes(floating_ui_utils_getSide(placement)),limitMin=rects.reference[crossAxis]-rects.floating[len]+(isOriginSide&&(null==(_middlewareData$offse=middlewareData.offset)?void 0:_middlewareData$offse[crossAxis])||0)+(isOriginSide?0:computedOffset.crossAxis),limitMax=rects.reference[crossAxis]+rects.reference[len]+(isOriginSide?0:(null==(_middlewareData$offse2=middlewareData.offset)?void 0:_middlewareData$offse2[crossAxis])||0)-(isOriginSide?computedOffset.crossAxis:0);crossAxisCoord<limitMin?crossAxisCoord=limitMin:crossAxisCoord>limitMax&&(crossAxisCoord=limitMax)}return{[mainAxis]:mainAxisCoord,[crossAxis]:crossAxisCoord}}}},floating_ui_dom_computePosition=(reference,floating,options)=>{const cache=new Map,mergedOptions={platform,...options},platformWithCache={...mergedOptions.platform,_c:cache};return(async(reference,floating,config)=>{const{placement="bottom",strategy="absolute",middleware=[],platform}=config,validMiddleware=middleware.filter(Boolean),rtl=await(null==platform.isRTL?void 0:platform.isRTL(floating));let rects=await platform.getElementRects({reference,floating,strategy}),{x,y}=computeCoordsFromPlacement(rects,placement,rtl),statefulPlacement=placement,middlewareData={},resetCount=0;for(let i=0;i<validMiddleware.length;i++){const{name,fn}=validMiddleware[i],{x:nextX,y:nextY,data,reset}=await fn({x,y,initialPlacement:placement,placement:statefulPlacement,strategy,middlewareData,rects,platform,elements:{reference,floating}});x=null!=nextX?nextX:x,y=null!=nextY?nextY:y,middlewareData={...middlewareData,[name]:{...middlewareData[name],...data}},reset&&resetCount<=50&&(resetCount++,"object"==typeof reset&&(reset.placement&&(statefulPlacement=reset.placement),reset.rects&&(rects=!0===reset.rects?await platform.getElementRects({reference,floating,strategy}):reset.rects),({x,y}=computeCoordsFromPlacement(rects,statefulPlacement,rtl))),i=-1)}return{x,y,placement:statefulPlacement,strategy,middlewareData}})(reference,floating,{...mergedOptions,platform:platformWithCache})};var index="undefined"!=typeof document?react.useLayoutEffect:react.useEffect;function deepEqual(a,b){if(a===b)return!0;if(typeof a!=typeof b)return!1;if("function"==typeof a&&a.toString()===b.toString())return!0;let length,i,keys;if(a&&b&&"object"==typeof a){if(Array.isArray(a)){if(length=a.length,length!==b.length)return!1;for(i=length;0!=i--;)if(!deepEqual(a[i],b[i]))return!1;return!0}if(keys=Object.keys(a),length=keys.length,length!==Object.keys(b).length)return!1;for(i=length;0!=i--;)if(!{}.hasOwnProperty.call(b,keys[i]))return!1;for(i=length;0!=i--;){const key=keys[i];if(("_owner"!==key||!a.$$typeof)&&!deepEqual(a[key],b[key]))return!1}return!0}return a!=a&&b!=b}function getDPR(element){if("undefined"==typeof window)return 1;return(element.ownerDocument.defaultView||window).devicePixelRatio||1}function roundByDPR(element,value){const dpr=getDPR(element);return Math.round(value*dpr)/dpr}function useLatestRef(value){const ref=react.useRef(value);return index((()=>{ref.current=value})),ref}const arrow$1=options=>({name:"arrow",options,fn(state){const{element,padding}="function"==typeof options?options(state):options;return element&&function isRef(value){return{}.hasOwnProperty.call(value,"current")}(element)?null!=element.current?floating_ui_dom_arrow({element:element.current,padding}).fn(state):{}:element?floating_ui_dom_arrow({element,padding}).fn(state):{}}}),floating_ui_react_dom_shift=(options,deps)=>({...floating_ui_dom_shift(options),options:[options,deps]}),floating_ui_react_dom_limitShift=(options,deps)=>({...floating_ui_dom_limitShift(options),options:[options,deps]}),floating_ui_react_dom_flip=(options,deps)=>({...floating_ui_dom_flip(options),options:[options,deps]}),floating_ui_react_dom_size=(options,deps)=>({...floating_ui_dom_size(options),options:[options,deps]}),floating_ui_react_dom_hide=(options,deps)=>({...floating_ui_dom_hide(options),options:[options,deps]}),floating_ui_react_dom_arrow=(options,deps)=>({...arrow$1(options),options:[options,deps]});var react_primitive_dist=__webpack_require__("./node_modules/@radix-ui/react-primitive/dist/index.mjs"),Arrow=react.forwardRef(((props,forwardedRef)=>{const{children,width=10,height=5,...arrowProps}=props;return(0,jsx_runtime.jsx)(react_primitive_dist.sG.svg,{...arrowProps,ref:forwardedRef,width,height,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:props.asChild?children:(0,jsx_runtime.jsx)("polygon",{points:"0,0 30,0 15,10"})})}));Arrow.displayName="Arrow";var Root=Arrow,react_use_callback_ref_dist=__webpack_require__("./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs"),react_use_layout_effect_dist=__webpack_require__("./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs");var[createPopperContext,createPopperScope]=(0,react_context_dist.A)("Popper"),[PopperProvider,usePopperContext]=createPopperContext("Popper"),Popper=props=>{const{__scopePopper,children}=props,[anchor,setAnchor]=react.useState(null);return(0,jsx_runtime.jsx)(PopperProvider,{scope:__scopePopper,anchor,onAnchorChange:setAnchor,children})};Popper.displayName="Popper";var PopperAnchor=react.forwardRef(((props,forwardedRef)=>{const{__scopePopper,virtualRef,...anchorProps}=props,context=usePopperContext("PopperAnchor",__scopePopper),ref=react.useRef(null),composedRefs=(0,react_compose_refs_dist.s)(forwardedRef,ref);return react.useEffect((()=>{context.onAnchorChange(virtualRef?.current||ref.current)})),virtualRef?null:(0,jsx_runtime.jsx)(react_primitive_dist.sG.div,{...anchorProps,ref:composedRefs})}));PopperAnchor.displayName="PopperAnchor";var[PopperContentProvider,useContentContext]=createPopperContext("PopperContent"),PopperContent=react.forwardRef(((props,forwardedRef)=>{const{__scopePopper,side="bottom",sideOffset=0,align="center",alignOffset=0,arrowPadding=0,avoidCollisions=!0,collisionBoundary=[],collisionPadding:collisionPaddingProp=0,sticky="partial",hideWhenDetached=!1,updatePositionStrategy="optimized",onPlaced,...contentProps}=props,context=usePopperContext("PopperContent",__scopePopper),[content,setContent]=react.useState(null),composedRefs=(0,react_compose_refs_dist.s)(forwardedRef,(node=>setContent(node))),[arrow,setArrow]=react.useState(null),arrowSize=function useSize(element){const[size,setSize]=react.useState(void 0);return(0,react_use_layout_effect_dist.N)((()=>{if(element){setSize({width:element.offsetWidth,height:element.offsetHeight});const resizeObserver=new ResizeObserver((entries=>{if(!Array.isArray(entries))return;if(!entries.length)return;const entry=entries[0];let width,height;if("borderBoxSize"in entry){const borderSizeEntry=entry.borderBoxSize,borderSize=Array.isArray(borderSizeEntry)?borderSizeEntry[0]:borderSizeEntry;width=borderSize.inlineSize,height=borderSize.blockSize}else width=element.offsetWidth,height=element.offsetHeight;setSize({width,height})}));return resizeObserver.observe(element,{box:"border-box"}),()=>resizeObserver.unobserve(element)}setSize(void 0)}),[element]),size}(arrow),arrowWidth=arrowSize?.width??0,arrowHeight=arrowSize?.height??0,desiredPlacement=side+("center"!==align?"-"+align:""),collisionPadding="number"==typeof collisionPaddingProp?collisionPaddingProp:{top:0,right:0,bottom:0,left:0,...collisionPaddingProp},boundary=Array.isArray(collisionBoundary)?collisionBoundary:[collisionBoundary],hasExplicitBoundaries=boundary.length>0,detectOverflowOptions={padding:collisionPadding,boundary:boundary.filter(isNotNull),altBoundary:hasExplicitBoundaries},{refs,floatingStyles,placement,isPositioned,middlewareData}=function useFloating(options){void 0===options&&(options={});const{placement="bottom",strategy="absolute",middleware=[],platform,elements:{reference:externalReference,floating:externalFloating}={},transform=!0,whileElementsMounted,open}=options,[data,setData]=react.useState({x:0,y:0,strategy,placement,middlewareData:{},isPositioned:!1}),[latestMiddleware,setLatestMiddleware]=react.useState(middleware);deepEqual(latestMiddleware,middleware)||setLatestMiddleware(middleware);const[_reference,_setReference]=react.useState(null),[_floating,_setFloating]=react.useState(null),setReference=react.useCallback((node=>{node!==referenceRef.current&&(referenceRef.current=node,_setReference(node))}),[]),setFloating=react.useCallback((node=>{node!==floatingRef.current&&(floatingRef.current=node,_setFloating(node))}),[]),referenceEl=externalReference||_reference,floatingEl=externalFloating||_floating,referenceRef=react.useRef(null),floatingRef=react.useRef(null),dataRef=react.useRef(data),hasWhileElementsMounted=null!=whileElementsMounted,whileElementsMountedRef=useLatestRef(whileElementsMounted),platformRef=useLatestRef(platform),openRef=useLatestRef(open),update=react.useCallback((()=>{if(!referenceRef.current||!floatingRef.current)return;const config={placement,strategy,middleware:latestMiddleware};platformRef.current&&(config.platform=platformRef.current),floating_ui_dom_computePosition(referenceRef.current,floatingRef.current,config).then((data=>{const fullData={...data,isPositioned:!1!==openRef.current};isMountedRef.current&&!deepEqual(dataRef.current,fullData)&&(dataRef.current=fullData,react_dom.flushSync((()=>{setData(fullData)})))}))}),[latestMiddleware,placement,strategy,platformRef,openRef]);index((()=>{!1===open&&dataRef.current.isPositioned&&(dataRef.current.isPositioned=!1,setData((data=>({...data,isPositioned:!1}))))}),[open]);const isMountedRef=react.useRef(!1);index((()=>(isMountedRef.current=!0,()=>{isMountedRef.current=!1})),[]),index((()=>{if(referenceEl&&(referenceRef.current=referenceEl),floatingEl&&(floatingRef.current=floatingEl),referenceEl&&floatingEl){if(whileElementsMountedRef.current)return whileElementsMountedRef.current(referenceEl,floatingEl,update);update()}}),[referenceEl,floatingEl,update,whileElementsMountedRef,hasWhileElementsMounted]);const refs=react.useMemo((()=>({reference:referenceRef,floating:floatingRef,setReference,setFloating})),[setReference,setFloating]),elements=react.useMemo((()=>({reference:referenceEl,floating:floatingEl})),[referenceEl,floatingEl]),floatingStyles=react.useMemo((()=>{const initialStyles={position:strategy,left:0,top:0};if(!elements.floating)return initialStyles;const x=roundByDPR(elements.floating,data.x),y=roundByDPR(elements.floating,data.y);return transform?{...initialStyles,transform:"translate("+x+"px, "+y+"px)",...getDPR(elements.floating)>=1.5&&{willChange:"transform"}}:{position:strategy,left:x,top:y}}),[strategy,transform,elements.floating,data.x,data.y]);return react.useMemo((()=>({...data,update,refs,elements,floatingStyles})),[data,update,refs,elements,floatingStyles])}({strategy:"fixed",placement:desiredPlacement,whileElementsMounted:(...args)=>autoUpdate(...args,{animationFrame:"always"===updatePositionStrategy}),elements:{reference:context.anchor},middleware:[(options={mainAxis:sideOffset+arrowHeight,alignmentAxis:alignOffset},{...floating_ui_dom_offset(options),options:[options,deps]}),avoidCollisions&&floating_ui_react_dom_shift({mainAxis:!0,crossAxis:!1,limiter:"partial"===sticky?floating_ui_react_dom_limitShift():void 0,...detectOverflowOptions}),avoidCollisions&&floating_ui_react_dom_flip({...detectOverflowOptions}),floating_ui_react_dom_size({...detectOverflowOptions,apply:({elements,rects,availableWidth,availableHeight})=>{const{width:anchorWidth,height:anchorHeight}=rects.reference,contentStyle=elements.floating.style;contentStyle.setProperty("--radix-popper-available-width",`${availableWidth}px`),contentStyle.setProperty("--radix-popper-available-height",`${availableHeight}px`),contentStyle.setProperty("--radix-popper-anchor-width",`${anchorWidth}px`),contentStyle.setProperty("--radix-popper-anchor-height",`${anchorHeight}px`)}}),arrow&&floating_ui_react_dom_arrow({element:arrow,padding:arrowPadding}),transformOrigin({arrowWidth,arrowHeight}),hideWhenDetached&&floating_ui_react_dom_hide({strategy:"referenceHidden",...detectOverflowOptions})]});var options,deps;const[placedSide,placedAlign]=getSideAndAlignFromPlacement(placement),handlePlaced=(0,react_use_callback_ref_dist.c)(onPlaced);(0,react_use_layout_effect_dist.N)((()=>{isPositioned&&handlePlaced?.()}),[isPositioned,handlePlaced]);const arrowX=middlewareData.arrow?.x,arrowY=middlewareData.arrow?.y,cannotCenterArrow=0!==middlewareData.arrow?.centerOffset,[contentZIndex,setContentZIndex]=react.useState();return(0,react_use_layout_effect_dist.N)((()=>{content&&setContentZIndex(window.getComputedStyle(content).zIndex)}),[content]),(0,jsx_runtime.jsx)("div",{ref:refs.setFloating,"data-radix-popper-content-wrapper":"",style:{...floatingStyles,transform:isPositioned?floatingStyles.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:contentZIndex,"--radix-popper-transform-origin":[middlewareData.transformOrigin?.x,middlewareData.transformOrigin?.y].join(" "),...middlewareData.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:props.dir,children:(0,jsx_runtime.jsx)(PopperContentProvider,{scope:__scopePopper,placedSide,onArrowChange:setArrow,arrowX,arrowY,shouldHideArrow:cannotCenterArrow,children:(0,jsx_runtime.jsx)(react_primitive_dist.sG.div,{"data-side":placedSide,"data-align":placedAlign,...contentProps,ref:composedRefs,style:{...contentProps.style,animation:isPositioned?void 0:"none"}})})})}));PopperContent.displayName="PopperContent";var OPPOSITE_SIDE={top:"bottom",right:"left",bottom:"top",left:"right"},PopperArrow=react.forwardRef((function PopperArrow2(props,forwardedRef){const{__scopePopper,...arrowProps}=props,contentContext=useContentContext("PopperArrow",__scopePopper),baseSide=OPPOSITE_SIDE[contentContext.placedSide];return(0,jsx_runtime.jsx)("span",{ref:contentContext.onArrowChange,style:{position:"absolute",left:contentContext.arrowX,top:contentContext.arrowY,[baseSide]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[contentContext.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[contentContext.placedSide],visibility:contentContext.shouldHideArrow?"hidden":void 0},children:(0,jsx_runtime.jsx)(Root,{...arrowProps,ref:forwardedRef,style:{...arrowProps.style,display:"block"}})})}));function isNotNull(value){return null!==value}PopperArrow.displayName="PopperArrow";var transformOrigin=options=>({name:"transformOrigin",options,fn(data){const{placement,rects,middlewareData}=data,isArrowHidden=0!==middlewareData.arrow?.centerOffset,arrowWidth=isArrowHidden?0:options.arrowWidth,arrowHeight=isArrowHidden?0:options.arrowHeight,[placedSide,placedAlign]=getSideAndAlignFromPlacement(placement),noArrowAlign={start:"0%",center:"50%",end:"100%"}[placedAlign],arrowXCenter=(middlewareData.arrow?.x??0)+arrowWidth/2,arrowYCenter=(middlewareData.arrow?.y??0)+arrowHeight/2;let x="",y="";return"bottom"===placedSide?(x=isArrowHidden?noArrowAlign:`${arrowXCenter}px`,y=-arrowHeight+"px"):"top"===placedSide?(x=isArrowHidden?noArrowAlign:`${arrowXCenter}px`,y=`${rects.floating.height+arrowHeight}px`):"right"===placedSide?(x=-arrowHeight+"px",y=isArrowHidden?noArrowAlign:`${arrowYCenter}px`):"left"===placedSide&&(x=`${rects.floating.width+arrowHeight}px`,y=isArrowHidden?noArrowAlign:`${arrowYCenter}px`),{data:{x,y}}}});function getSideAndAlignFromPlacement(placement){const[side,align="center"]=placement.split("-");return[side,align]}var Root2=Popper,Anchor=PopperAnchor,Content=PopperContent,dist_Arrow=PopperArrow,react_portal_dist=__webpack_require__("./node_modules/@radix-ui/react-portal/dist/index.mjs"),react_use_controllable_state_dist=__webpack_require__("./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs");var VISUALLY_HIDDEN_STYLES=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),VisuallyHidden=react.forwardRef(((props,forwardedRef)=>(0,jsx_runtime.jsx)(react_primitive_dist.sG.span,{...props,ref:forwardedRef,style:{...VISUALLY_HIDDEN_STYLES,...props.style}})));VisuallyHidden.displayName="VisuallyHidden";var es2015=__webpack_require__("./node_modules/aria-hidden/dist/es2015/index.js"),Combination=__webpack_require__("./node_modules/react-remove-scroll/dist/es2015/Combination.js"),OPEN_KEYS=[" ","Enter","ArrowUp","ArrowDown"],SELECTION_KEYS=[" ","Enter"],[Collection,useCollection,createCollectionScope]=function createCollection(name){const PROVIDER_NAME=name+"CollectionProvider",[createCollectionContext,createCollectionScope]=(0,react_context_dist.A)(PROVIDER_NAME),[CollectionProviderImpl,useCollectionContext]=createCollectionContext(PROVIDER_NAME,{collectionRef:{current:null},itemMap:new Map}),CollectionProvider=props=>{const{scope,children}=props,ref=react.useRef(null),itemMap=react.useRef(new Map).current;return(0,jsx_runtime.jsx)(CollectionProviderImpl,{scope,itemMap,collectionRef:ref,children})};CollectionProvider.displayName=PROVIDER_NAME;const COLLECTION_SLOT_NAME=name+"CollectionSlot",CollectionSlotImpl=(0,react_slot_dist.TL)(COLLECTION_SLOT_NAME),CollectionSlot=react.forwardRef(((props,forwardedRef)=>{const{scope,children}=props,context=useCollectionContext(COLLECTION_SLOT_NAME,scope),composedRefs=(0,react_compose_refs_dist.s)(forwardedRef,context.collectionRef);return(0,jsx_runtime.jsx)(CollectionSlotImpl,{ref:composedRefs,children})}));CollectionSlot.displayName=COLLECTION_SLOT_NAME;const ITEM_SLOT_NAME=name+"CollectionItemSlot",ITEM_DATA_ATTR="data-radix-collection-item",CollectionItemSlotImpl=(0,react_slot_dist.TL)(ITEM_SLOT_NAME),CollectionItemSlot=react.forwardRef(((props,forwardedRef)=>{const{scope,children,...itemData}=props,ref=react.useRef(null),composedRefs=(0,react_compose_refs_dist.s)(forwardedRef,ref),context=useCollectionContext(ITEM_SLOT_NAME,scope);return react.useEffect((()=>(context.itemMap.set(ref,{ref,...itemData}),()=>{context.itemMap.delete(ref)}))),(0,jsx_runtime.jsx)(CollectionItemSlotImpl,{[ITEM_DATA_ATTR]:"",ref:composedRefs,children})}));return CollectionItemSlot.displayName=ITEM_SLOT_NAME,[{Provider:CollectionProvider,Slot:CollectionSlot,ItemSlot:CollectionItemSlot},function useCollection(scope){const context=useCollectionContext(name+"CollectionConsumer",scope);return react.useCallback((()=>{const collectionNode=context.collectionRef.current;if(!collectionNode)return[];const orderedNodes=Array.from(collectionNode.querySelectorAll("[data-radix-collection-item]"));return Array.from(context.itemMap.values()).sort(((a,b)=>orderedNodes.indexOf(a.ref.current)-orderedNodes.indexOf(b.ref.current)))}),[context.collectionRef,context.itemMap])},createCollectionScope]}("Select"),[createSelectContext,createSelectScope]=(0,react_context_dist.A)("Select",[createCollectionScope,createPopperScope]),usePopperScope=createPopperScope(),[SelectProvider,useSelectContext]=createSelectContext("Select"),[SelectNativeOptionsProvider,useSelectNativeOptionsContext]=createSelectContext("Select"),Select=props=>{const{__scopeSelect,children,open:openProp,defaultOpen,onOpenChange,value:valueProp,defaultValue,onValueChange,dir,name,autoComplete,disabled,required,form}=props,popperScope=usePopperScope(__scopeSelect),[trigger,setTrigger]=react.useState(null),[valueNode,setValueNode]=react.useState(null),[valueNodeHasChildren,setValueNodeHasChildren]=react.useState(!1),direction=function useDirection(localDir){const globalDir=react.useContext(DirectionContext);return localDir||globalDir||"ltr"}(dir),[open,setOpen]=(0,react_use_controllable_state_dist.i)({prop:openProp,defaultProp:defaultOpen??!1,onChange:onOpenChange,caller:"Select"}),[value,setValue]=(0,react_use_controllable_state_dist.i)({prop:valueProp,defaultProp:defaultValue,onChange:onValueChange,caller:"Select"}),triggerPointerDownPosRef=react.useRef(null),isFormControl=!trigger||(form||!!trigger.closest("form")),[nativeOptionsSet,setNativeOptionsSet]=react.useState(new Set),nativeSelectKey=Array.from(nativeOptionsSet).map((option=>option.props.value)).join(";");return(0,jsx_runtime.jsx)(Root2,{...popperScope,children:(0,jsx_runtime.jsxs)(SelectProvider,{required,scope:__scopeSelect,trigger,onTriggerChange:setTrigger,valueNode,onValueNodeChange:setValueNode,valueNodeHasChildren,onValueNodeHasChildrenChange:setValueNodeHasChildren,contentId:(0,react_id_dist.B)(),value,onValueChange:setValue,open,onOpenChange:setOpen,dir:direction,triggerPointerDownPosRef,disabled,children:[(0,jsx_runtime.jsx)(Collection.Provider,{scope:__scopeSelect,children:(0,jsx_runtime.jsx)(SelectNativeOptionsProvider,{scope:props.__scopeSelect,onNativeOptionAdd:react.useCallback((option=>{setNativeOptionsSet((prev=>new Set(prev).add(option)))}),[]),onNativeOptionRemove:react.useCallback((option=>{setNativeOptionsSet((prev=>{const optionsSet=new Set(prev);return optionsSet.delete(option),optionsSet}))}),[]),children})}),isFormControl?(0,jsx_runtime.jsxs)(SelectBubbleInput,{"aria-hidden":!0,required,tabIndex:-1,name,autoComplete,value,onChange:event=>setValue(event.target.value),disabled,form,children:[void 0===value?(0,jsx_runtime.jsx)("option",{value:""}):null,Array.from(nativeOptionsSet)]},nativeSelectKey):null]})})};Select.displayName="Select";var SelectTrigger=react.forwardRef(((props,forwardedRef)=>{const{__scopeSelect,disabled=!1,...triggerProps}=props,popperScope=usePopperScope(__scopeSelect),context=useSelectContext("SelectTrigger",__scopeSelect),isDisabled=context.disabled||disabled,composedRefs=(0,react_compose_refs_dist.s)(forwardedRef,context.onTriggerChange),getItems=useCollection(__scopeSelect),pointerTypeRef=react.useRef("touch"),[searchRef,handleTypeaheadSearch,resetTypeahead]=useTypeaheadSearch((search=>{const enabledItems=getItems().filter((item=>!item.disabled)),currentItem=enabledItems.find((item=>item.value===context.value)),nextItem=findNextItem(enabledItems,search,currentItem);void 0!==nextItem&&context.onValueChange(nextItem.value)})),handleOpen=pointerEvent=>{isDisabled||(context.onOpenChange(!0),resetTypeahead()),pointerEvent&&(context.triggerPointerDownPosRef.current={x:Math.round(pointerEvent.pageX),y:Math.round(pointerEvent.pageY)})};return(0,jsx_runtime.jsx)(Anchor,{asChild:!0,...popperScope,children:(0,jsx_runtime.jsx)(react_primitive_dist.sG.button,{type:"button",role:"combobox","aria-controls":context.contentId,"aria-expanded":context.open,"aria-required":context.required,"aria-autocomplete":"none",dir:context.dir,"data-state":context.open?"open":"closed",disabled:isDisabled,"data-disabled":isDisabled?"":void 0,"data-placeholder":shouldShowPlaceholder(context.value)?"":void 0,...triggerProps,ref:composedRefs,onClick:(0,dist.m)(triggerProps.onClick,(event=>{event.currentTarget.focus(),"mouse"!==pointerTypeRef.current&&handleOpen(event)})),onPointerDown:(0,dist.m)(triggerProps.onPointerDown,(event=>{pointerTypeRef.current=event.pointerType;const target=event.target;target.hasPointerCapture(event.pointerId)&&target.releasePointerCapture(event.pointerId),0===event.button&&!1===event.ctrlKey&&"mouse"===event.pointerType&&(handleOpen(event),event.preventDefault())})),onKeyDown:(0,dist.m)(triggerProps.onKeyDown,(event=>{const isTypingAhead=""!==searchRef.current;event.ctrlKey||event.altKey||event.metaKey||1!==event.key.length||handleTypeaheadSearch(event.key),isTypingAhead&&" "===event.key||OPEN_KEYS.includes(event.key)&&(handleOpen(),event.preventDefault())}))})})}));SelectTrigger.displayName="SelectTrigger";var SelectValue=react.forwardRef(((props,forwardedRef)=>{const{__scopeSelect,className,style,children,placeholder="",...valueProps}=props,context=useSelectContext("SelectValue",__scopeSelect),{onValueNodeHasChildrenChange}=context,hasChildren=void 0!==children,composedRefs=(0,react_compose_refs_dist.s)(forwardedRef,context.onValueNodeChange);return(0,react_use_layout_effect_dist.N)((()=>{onValueNodeHasChildrenChange(hasChildren)}),[onValueNodeHasChildrenChange,hasChildren]),(0,jsx_runtime.jsx)(react_primitive_dist.sG.span,{...valueProps,ref:composedRefs,style:{pointerEvents:"none"},children:shouldShowPlaceholder(context.value)?(0,jsx_runtime.jsx)(jsx_runtime.Fragment,{children:placeholder}):children})}));SelectValue.displayName="SelectValue";var SelectIcon=react.forwardRef(((props,forwardedRef)=>{const{__scopeSelect,children,...iconProps}=props;return(0,jsx_runtime.jsx)(react_primitive_dist.sG.span,{"aria-hidden":!0,...iconProps,ref:forwardedRef,children:children||"▼"})}));SelectIcon.displayName="SelectIcon";var SelectPortal=props=>(0,jsx_runtime.jsx)(react_portal_dist.Z,{asChild:!0,...props});SelectPortal.displayName="SelectPortal";var SelectContent=react.forwardRef(((props,forwardedRef)=>{const context=useSelectContext("SelectContent",props.__scopeSelect),[fragment,setFragment]=react.useState();if((0,react_use_layout_effect_dist.N)((()=>{setFragment(new DocumentFragment)}),[]),!context.open){const frag=fragment;return frag?react_dom.createPortal((0,jsx_runtime.jsx)(SelectContentProvider,{scope:props.__scopeSelect,children:(0,jsx_runtime.jsx)(Collection.Slot,{scope:props.__scopeSelect,children:(0,jsx_runtime.jsx)("div",{children:props.children})})}),frag):null}return(0,jsx_runtime.jsx)(SelectContentImpl,{...props,ref:forwardedRef})}));SelectContent.displayName="SelectContent";var CONTENT_MARGIN=10,[SelectContentProvider,useSelectContentContext]=createSelectContext("SelectContent"),Slot=(0,react_slot_dist.TL)("SelectContent.RemoveScroll"),SelectContentImpl=react.forwardRef(((props,forwardedRef)=>{const{__scopeSelect,position="item-aligned",onCloseAutoFocus,onEscapeKeyDown,onPointerDownOutside,side,sideOffset,align,alignOffset,arrowPadding,collisionBoundary,collisionPadding,sticky,hideWhenDetached,avoidCollisions,...contentProps}=props,context=useSelectContext("SelectContent",__scopeSelect),[content,setContent]=react.useState(null),[viewport,setViewport]=react.useState(null),composedRefs=(0,react_compose_refs_dist.s)(forwardedRef,(node=>setContent(node))),[selectedItem,setSelectedItem]=react.useState(null),[selectedItemText,setSelectedItemText]=react.useState(null),getItems=useCollection(__scopeSelect),[isPositioned,setIsPositioned]=react.useState(!1),firstValidItemFoundRef=react.useRef(!1);react.useEffect((()=>{if(content)return(0,es2015.Eq)(content)}),[content]),(0,react_focus_guards_dist.Oh)();const focusFirst=react.useCallback((candidates=>{const[firstItem,...restItems]=getItems().map((item=>item.ref.current)),[lastItem]=restItems.slice(-1),PREVIOUSLY_FOCUSED_ELEMENT=document.activeElement;for(const candidate of candidates){if(candidate===PREVIOUSLY_FOCUSED_ELEMENT)return;if(candidate?.scrollIntoView({block:"nearest"}),candidate===firstItem&&viewport&&(viewport.scrollTop=0),candidate===lastItem&&viewport&&(viewport.scrollTop=viewport.scrollHeight),candidate?.focus(),document.activeElement!==PREVIOUSLY_FOCUSED_ELEMENT)return}}),[getItems,viewport]),focusSelectedItem=react.useCallback((()=>focusFirst([selectedItem,content])),[focusFirst,selectedItem,content]);react.useEffect((()=>{isPositioned&&focusSelectedItem()}),[isPositioned,focusSelectedItem]);const{onOpenChange,triggerPointerDownPosRef}=context;react.useEffect((()=>{if(content){let pointerMoveDelta={x:0,y:0};const handlePointerMove=event=>{pointerMoveDelta={x:Math.abs(Math.round(event.pageX)-(triggerPointerDownPosRef.current?.x??0)),y:Math.abs(Math.round(event.pageY)-(triggerPointerDownPosRef.current?.y??0))}},handlePointerUp=event=>{pointerMoveDelta.x<=10&&pointerMoveDelta.y<=10?event.preventDefault():content.contains(event.target)||onOpenChange(!1),document.removeEventListener("pointermove",handlePointerMove),triggerPointerDownPosRef.current=null};return null!==triggerPointerDownPosRef.current&&(document.addEventListener("pointermove",handlePointerMove),document.addEventListener("pointerup",handlePointerUp,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",handlePointerMove),document.removeEventListener("pointerup",handlePointerUp,{capture:!0})}}}),[content,onOpenChange,triggerPointerDownPosRef]),react.useEffect((()=>{const close=()=>onOpenChange(!1);return window.addEventListener("blur",close),window.addEventListener("resize",close),()=>{window.removeEventListener("blur",close),window.removeEventListener("resize",close)}}),[onOpenChange]);const[searchRef,handleTypeaheadSearch]=useTypeaheadSearch((search=>{const enabledItems=getItems().filter((item=>!item.disabled)),currentItem=enabledItems.find((item=>item.ref.current===document.activeElement)),nextItem=findNextItem(enabledItems,search,currentItem);nextItem&&setTimeout((()=>nextItem.ref.current.focus()))})),itemRefCallback=react.useCallback(((node,value,disabled)=>{const isFirstValidItem=!firstValidItemFoundRef.current&&!disabled;(void 0!==context.value&&context.value===value||isFirstValidItem)&&(setSelectedItem(node),isFirstValidItem&&(firstValidItemFoundRef.current=!0))}),[context.value]),handleItemLeave=react.useCallback((()=>content?.focus()),[content]),itemTextRefCallback=react.useCallback(((node,value,disabled)=>{const isFirstValidItem=!firstValidItemFoundRef.current&&!disabled;(void 0!==context.value&&context.value===value||isFirstValidItem)&&setSelectedItemText(node)}),[context.value]),SelectPosition="popper"===position?SelectPopperPosition:SelectItemAlignedPosition,popperContentProps=SelectPosition===SelectPopperPosition?{side,sideOffset,align,alignOffset,arrowPadding,collisionBoundary,collisionPadding,sticky,hideWhenDetached,avoidCollisions}:{};return(0,jsx_runtime.jsx)(SelectContentProvider,{scope:__scopeSelect,content,viewport,onViewportChange:setViewport,itemRefCallback,selectedItem,onItemLeave:handleItemLeave,itemTextRefCallback,focusSelectedItem,selectedItemText,position,isPositioned,searchRef,children:(0,jsx_runtime.jsx)(Combination.A,{as:Slot,allowPinchZoom:!0,children:(0,jsx_runtime.jsx)(react_focus_scope_dist.n,{asChild:!0,trapped:context.open,onMountAutoFocus:event=>{event.preventDefault()},onUnmountAutoFocus:(0,dist.m)(onCloseAutoFocus,(event=>{context.trigger?.focus({preventScroll:!0}),event.preventDefault()})),children:(0,jsx_runtime.jsx)(react_dismissable_layer_dist.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown,onPointerDownOutside,onFocusOutside:event=>event.preventDefault(),onDismiss:()=>context.onOpenChange(!1),children:(0,jsx_runtime.jsx)(SelectPosition,{role:"listbox",id:context.contentId,"data-state":context.open?"open":"closed",dir:context.dir,onContextMenu:event=>event.preventDefault(),...contentProps,...popperContentProps,onPlaced:()=>setIsPositioned(!0),ref:composedRefs,style:{display:"flex",flexDirection:"column",outline:"none",...contentProps.style},onKeyDown:(0,dist.m)(contentProps.onKeyDown,(event=>{const isModifierKey=event.ctrlKey||event.altKey||event.metaKey;if("Tab"===event.key&&event.preventDefault(),isModifierKey||1!==event.key.length||handleTypeaheadSearch(event.key),["ArrowUp","ArrowDown","Home","End"].includes(event.key)){let candidateNodes=getItems().filter((item=>!item.disabled)).map((item=>item.ref.current));if(["ArrowUp","End"].includes(event.key)&&(candidateNodes=candidateNodes.slice().reverse()),["ArrowUp","ArrowDown"].includes(event.key)){const currentElement=event.target,currentIndex=candidateNodes.indexOf(currentElement);candidateNodes=candidateNodes.slice(currentIndex+1)}setTimeout((()=>focusFirst(candidateNodes))),event.preventDefault()}}))})})})})})}));SelectContentImpl.displayName="SelectContentImpl";var SelectItemAlignedPosition=react.forwardRef(((props,forwardedRef)=>{const{__scopeSelect,onPlaced,...popperProps}=props,context=useSelectContext("SelectContent",__scopeSelect),contentContext=useSelectContentContext("SelectContent",__scopeSelect),[contentWrapper,setContentWrapper]=react.useState(null),[content,setContent]=react.useState(null),composedRefs=(0,react_compose_refs_dist.s)(forwardedRef,(node=>setContent(node))),getItems=useCollection(__scopeSelect),shouldExpandOnScrollRef=react.useRef(!1),shouldRepositionRef=react.useRef(!0),{viewport,selectedItem,selectedItemText,focusSelectedItem}=contentContext,position=react.useCallback((()=>{if(context.trigger&&context.valueNode&&contentWrapper&&content&&viewport&&selectedItem&&selectedItemText){const triggerRect=context.trigger.getBoundingClientRect(),contentRect=content.getBoundingClientRect(),valueNodeRect=context.valueNode.getBoundingClientRect(),itemTextRect=selectedItemText.getBoundingClientRect();if("rtl"!==context.dir){const itemTextOffset=itemTextRect.left-contentRect.left,left=valueNodeRect.left-itemTextOffset,leftDelta=triggerRect.left-left,minContentWidth=triggerRect.width+leftDelta,contentWidth=Math.max(minContentWidth,contentRect.width),rightEdge=window.innerWidth-CONTENT_MARGIN,clampedLeft=clamp(left,[CONTENT_MARGIN,Math.max(CONTENT_MARGIN,rightEdge-contentWidth)]);contentWrapper.style.minWidth=minContentWidth+"px",contentWrapper.style.left=clampedLeft+"px"}else{const itemTextOffset=contentRect.right-itemTextRect.right,right=window.innerWidth-valueNodeRect.right-itemTextOffset,rightDelta=window.innerWidth-triggerRect.right-right,minContentWidth=triggerRect.width+rightDelta,contentWidth=Math.max(minContentWidth,contentRect.width),leftEdge=window.innerWidth-CONTENT_MARGIN,clampedRight=clamp(right,[CONTENT_MARGIN,Math.max(CONTENT_MARGIN,leftEdge-contentWidth)]);contentWrapper.style.minWidth=minContentWidth+"px",contentWrapper.style.right=clampedRight+"px"}const items=getItems(),availableHeight=window.innerHeight-2*CONTENT_MARGIN,itemsHeight=viewport.scrollHeight,contentStyles=window.getComputedStyle(content),contentBorderTopWidth=parseInt(contentStyles.borderTopWidth,10),contentPaddingTop=parseInt(contentStyles.paddingTop,10),contentBorderBottomWidth=parseInt(contentStyles.borderBottomWidth,10),fullContentHeight=contentBorderTopWidth+contentPaddingTop+itemsHeight+parseInt(contentStyles.paddingBottom,10)+contentBorderBottomWidth,minContentHeight=Math.min(5*selectedItem.offsetHeight,fullContentHeight),viewportStyles=window.getComputedStyle(viewport),viewportPaddingTop=parseInt(viewportStyles.paddingTop,10),viewportPaddingBottom=parseInt(viewportStyles.paddingBottom,10),topEdgeToTriggerMiddle=triggerRect.top+triggerRect.height/2-CONTENT_MARGIN,triggerMiddleToBottomEdge=availableHeight-topEdgeToTriggerMiddle,selectedItemHalfHeight=selectedItem.offsetHeight/2,contentTopToItemMiddle=contentBorderTopWidth+contentPaddingTop+(selectedItem.offsetTop+selectedItemHalfHeight),itemMiddleToContentBottom=fullContentHeight-contentTopToItemMiddle;if(contentTopToItemMiddle<=topEdgeToTriggerMiddle){const isLastItem=items.length>0&&selectedItem===items[items.length-1].ref.current;contentWrapper.style.bottom="0px";const viewportOffsetBottom=content.clientHeight-viewport.offsetTop-viewport.offsetHeight,height=contentTopToItemMiddle+Math.max(triggerMiddleToBottomEdge,selectedItemHalfHeight+(isLastItem?viewportPaddingBottom:0)+viewportOffsetBottom+contentBorderBottomWidth);contentWrapper.style.height=height+"px"}else{const isFirstItem=items.length>0&&selectedItem===items[0].ref.current;contentWrapper.style.top="0px";const height=Math.max(topEdgeToTriggerMiddle,contentBorderTopWidth+viewport.offsetTop+(isFirstItem?viewportPaddingTop:0)+selectedItemHalfHeight)+itemMiddleToContentBottom;contentWrapper.style.height=height+"px",viewport.scrollTop=contentTopToItemMiddle-topEdgeToTriggerMiddle+viewport.offsetTop}contentWrapper.style.margin=`${CONTENT_MARGIN}px 0`,contentWrapper.style.minHeight=minContentHeight+"px",contentWrapper.style.maxHeight=availableHeight+"px",onPlaced?.(),requestAnimationFrame((()=>shouldExpandOnScrollRef.current=!0))}}),[getItems,context.trigger,context.valueNode,contentWrapper,content,viewport,selectedItem,selectedItemText,context.dir,onPlaced]);(0,react_use_layout_effect_dist.N)((()=>position()),[position]);const[contentZIndex,setContentZIndex]=react.useState();(0,react_use_layout_effect_dist.N)((()=>{content&&setContentZIndex(window.getComputedStyle(content).zIndex)}),[content]);const handleScrollButtonChange=react.useCallback((node=>{node&&!0===shouldRepositionRef.current&&(position(),focusSelectedItem?.(),shouldRepositionRef.current=!1)}),[position,focusSelectedItem]);return(0,jsx_runtime.jsx)(SelectViewportProvider,{scope:__scopeSelect,contentWrapper,shouldExpandOnScrollRef,onScrollButtonChange:handleScrollButtonChange,children:(0,jsx_runtime.jsx)("div",{ref:setContentWrapper,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:contentZIndex},children:(0,jsx_runtime.jsx)(react_primitive_dist.sG.div,{...popperProps,ref:composedRefs,style:{boxSizing:"border-box",maxHeight:"100%",...popperProps.style}})})})}));SelectItemAlignedPosition.displayName="SelectItemAlignedPosition";var SelectPopperPosition=react.forwardRef(((props,forwardedRef)=>{const{__scopeSelect,align="start",collisionPadding=CONTENT_MARGIN,...popperProps}=props,popperScope=usePopperScope(__scopeSelect);return(0,jsx_runtime.jsx)(Content,{...popperScope,...popperProps,ref:forwardedRef,align,collisionPadding,style:{boxSizing:"border-box",...popperProps.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})}));SelectPopperPosition.displayName="SelectPopperPosition";var[SelectViewportProvider,useSelectViewportContext]=createSelectContext("SelectContent",{}),SelectViewport=react.forwardRef(((props,forwardedRef)=>{const{__scopeSelect,nonce,...viewportProps}=props,contentContext=useSelectContentContext("SelectViewport",__scopeSelect),viewportContext=useSelectViewportContext("SelectViewport",__scopeSelect),composedRefs=(0,react_compose_refs_dist.s)(forwardedRef,contentContext.onViewportChange),prevScrollTopRef=react.useRef(0);return(0,jsx_runtime.jsxs)(jsx_runtime.Fragment,{children:[(0,jsx_runtime.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce}),(0,jsx_runtime.jsx)(Collection.Slot,{scope:__scopeSelect,children:(0,jsx_runtime.jsx)(react_primitive_dist.sG.div,{"data-radix-select-viewport":"",role:"presentation",...viewportProps,ref:composedRefs,style:{position:"relative",flex:1,overflow:"hidden auto",...viewportProps.style},onScroll:(0,dist.m)(viewportProps.onScroll,(event=>{const viewport=event.currentTarget,{contentWrapper,shouldExpandOnScrollRef}=viewportContext;if(shouldExpandOnScrollRef?.current&&contentWrapper){const scrolledBy=Math.abs(prevScrollTopRef.current-viewport.scrollTop);if(scrolledBy>0){const availableHeight=window.innerHeight-2*CONTENT_MARGIN,cssMinHeight=parseFloat(contentWrapper.style.minHeight),cssHeight=parseFloat(contentWrapper.style.height),prevHeight=Math.max(cssMinHeight,cssHeight);if(prevHeight<availableHeight){const nextHeight=prevHeight+scrolledBy,clampedNextHeight=Math.min(availableHeight,nextHeight),heightDiff=nextHeight-clampedNextHeight;contentWrapper.style.height=clampedNextHeight+"px","0px"===contentWrapper.style.bottom&&(viewport.scrollTop=heightDiff>0?heightDiff:0,contentWrapper.style.justifyContent="flex-end")}}}prevScrollTopRef.current=viewport.scrollTop}))})})]})}));SelectViewport.displayName="SelectViewport";var[SelectGroupContextProvider,useSelectGroupContext]=createSelectContext("SelectGroup"),SelectGroup=react.forwardRef(((props,forwardedRef)=>{const{__scopeSelect,...groupProps}=props,groupId=(0,react_id_dist.B)();return(0,jsx_runtime.jsx)(SelectGroupContextProvider,{scope:__scopeSelect,id:groupId,children:(0,jsx_runtime.jsx)(react_primitive_dist.sG.div,{role:"group","aria-labelledby":groupId,...groupProps,ref:forwardedRef})})}));SelectGroup.displayName="SelectGroup";var SelectLabel=react.forwardRef(((props,forwardedRef)=>{const{__scopeSelect,...labelProps}=props,groupContext=useSelectGroupContext("SelectLabel",__scopeSelect);return(0,jsx_runtime.jsx)(react_primitive_dist.sG.div,{id:groupContext.id,...labelProps,ref:forwardedRef})}));SelectLabel.displayName="SelectLabel";var[SelectItemContextProvider,useSelectItemContext]=createSelectContext("SelectItem"),SelectItem=react.forwardRef(((props,forwardedRef)=>{const{__scopeSelect,value,disabled=!1,textValue:textValueProp,...itemProps}=props,context=useSelectContext("SelectItem",__scopeSelect),contentContext=useSelectContentContext("SelectItem",__scopeSelect),isSelected=context.value===value,[textValue,setTextValue]=react.useState(textValueProp??""),[isFocused,setIsFocused]=react.useState(!1),composedRefs=(0,react_compose_refs_dist.s)(forwardedRef,(node=>contentContext.itemRefCallback?.(node,value,disabled))),textId=(0,react_id_dist.B)(),pointerTypeRef=react.useRef("touch"),handleSelect=()=>{disabled||(context.onValueChange(value),context.onOpenChange(!1))};if(""===value)throw new Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,jsx_runtime.jsx)(SelectItemContextProvider,{scope:__scopeSelect,value,disabled,textId,isSelected,onItemTextChange:react.useCallback((node=>{setTextValue((prevTextValue=>prevTextValue||(node?.textContent??"").trim()))}),[]),children:(0,jsx_runtime.jsx)(Collection.ItemSlot,{scope:__scopeSelect,value,disabled,textValue,children:(0,jsx_runtime.jsx)(react_primitive_dist.sG.div,{role:"option","aria-labelledby":textId,"data-highlighted":isFocused?"":void 0,"aria-selected":isSelected&&isFocused,"data-state":isSelected?"checked":"unchecked","aria-disabled":disabled||void 0,"data-disabled":disabled?"":void 0,tabIndex:disabled?void 0:-1,...itemProps,ref:composedRefs,onFocus:(0,dist.m)(itemProps.onFocus,(()=>setIsFocused(!0))),onBlur:(0,dist.m)(itemProps.onBlur,(()=>setIsFocused(!1))),onClick:(0,dist.m)(itemProps.onClick,(()=>{"mouse"!==pointerTypeRef.current&&handleSelect()})),onPointerUp:(0,dist.m)(itemProps.onPointerUp,(()=>{"mouse"===pointerTypeRef.current&&handleSelect()})),onPointerDown:(0,dist.m)(itemProps.onPointerDown,(event=>{pointerTypeRef.current=event.pointerType})),onPointerMove:(0,dist.m)(itemProps.onPointerMove,(event=>{pointerTypeRef.current=event.pointerType,disabled?contentContext.onItemLeave?.():"mouse"===pointerTypeRef.current&&event.currentTarget.focus({preventScroll:!0})})),onPointerLeave:(0,dist.m)(itemProps.onPointerLeave,(event=>{event.currentTarget===document.activeElement&&contentContext.onItemLeave?.()})),onKeyDown:(0,dist.m)(itemProps.onKeyDown,(event=>{""!==contentContext.searchRef?.current&&" "===event.key||(SELECTION_KEYS.includes(event.key)&&handleSelect()," "===event.key&&event.preventDefault())}))})})})}));SelectItem.displayName="SelectItem";var SelectItemText=react.forwardRef(((props,forwardedRef)=>{const{__scopeSelect,className,style,...itemTextProps}=props,context=useSelectContext("SelectItemText",__scopeSelect),contentContext=useSelectContentContext("SelectItemText",__scopeSelect),itemContext=useSelectItemContext("SelectItemText",__scopeSelect),nativeOptionsContext=useSelectNativeOptionsContext("SelectItemText",__scopeSelect),[itemTextNode,setItemTextNode]=react.useState(null),composedRefs=(0,react_compose_refs_dist.s)(forwardedRef,(node=>setItemTextNode(node)),itemContext.onItemTextChange,(node=>contentContext.itemTextRefCallback?.(node,itemContext.value,itemContext.disabled))),textContent=itemTextNode?.textContent,nativeOption=react.useMemo((()=>(0,jsx_runtime.jsx)("option",{value:itemContext.value,disabled:itemContext.disabled,children:textContent},itemContext.value)),[itemContext.disabled,itemContext.value,textContent]),{onNativeOptionAdd,onNativeOptionRemove}=nativeOptionsContext;return(0,react_use_layout_effect_dist.N)((()=>(onNativeOptionAdd(nativeOption),()=>onNativeOptionRemove(nativeOption))),[onNativeOptionAdd,onNativeOptionRemove,nativeOption]),(0,jsx_runtime.jsxs)(jsx_runtime.Fragment,{children:[(0,jsx_runtime.jsx)(react_primitive_dist.sG.span,{id:itemContext.textId,...itemTextProps,ref:composedRefs}),itemContext.isSelected&&context.valueNode&&!context.valueNodeHasChildren?react_dom.createPortal(itemTextProps.children,context.valueNode):null]})}));SelectItemText.displayName="SelectItemText";var SelectItemIndicator=react.forwardRef(((props,forwardedRef)=>{const{__scopeSelect,...itemIndicatorProps}=props;return useSelectItemContext("SelectItemIndicator",__scopeSelect).isSelected?(0,jsx_runtime.jsx)(react_primitive_dist.sG.span,{"aria-hidden":!0,...itemIndicatorProps,ref:forwardedRef}):null}));SelectItemIndicator.displayName="SelectItemIndicator";var SelectScrollUpButton=react.forwardRef(((props,forwardedRef)=>{const contentContext=useSelectContentContext("SelectScrollUpButton",props.__scopeSelect),viewportContext=useSelectViewportContext("SelectScrollUpButton",props.__scopeSelect),[canScrollUp,setCanScrollUp]=react.useState(!1),composedRefs=(0,react_compose_refs_dist.s)(forwardedRef,viewportContext.onScrollButtonChange);return(0,react_use_layout_effect_dist.N)((()=>{if(contentContext.viewport&&contentContext.isPositioned){let handleScroll2=function(){const canScrollUp2=viewport.scrollTop>0;setCanScrollUp(canScrollUp2)};const viewport=contentContext.viewport;return handleScroll2(),viewport.addEventListener("scroll",handleScroll2),()=>viewport.removeEventListener("scroll",handleScroll2)}}),[contentContext.viewport,contentContext.isPositioned]),canScrollUp?(0,jsx_runtime.jsx)(SelectScrollButtonImpl,{...props,ref:composedRefs,onAutoScroll:()=>{const{viewport,selectedItem}=contentContext;viewport&&selectedItem&&(viewport.scrollTop=viewport.scrollTop-selectedItem.offsetHeight)}}):null}));SelectScrollUpButton.displayName="SelectScrollUpButton";var SelectScrollDownButton=react.forwardRef(((props,forwardedRef)=>{const contentContext=useSelectContentContext("SelectScrollDownButton",props.__scopeSelect),viewportContext=useSelectViewportContext("SelectScrollDownButton",props.__scopeSelect),[canScrollDown,setCanScrollDown]=react.useState(!1),composedRefs=(0,react_compose_refs_dist.s)(forwardedRef,viewportContext.onScrollButtonChange);return(0,react_use_layout_effect_dist.N)((()=>{if(contentContext.viewport&&contentContext.isPositioned){let handleScroll2=function(){const maxScroll=viewport.scrollHeight-viewport.clientHeight,canScrollDown2=Math.ceil(viewport.scrollTop)<maxScroll;setCanScrollDown(canScrollDown2)};const viewport=contentContext.viewport;return handleScroll2(),viewport.addEventListener("scroll",handleScroll2),()=>viewport.removeEventListener("scroll",handleScroll2)}}),[contentContext.viewport,contentContext.isPositioned]),canScrollDown?(0,jsx_runtime.jsx)(SelectScrollButtonImpl,{...props,ref:composedRefs,onAutoScroll:()=>{const{viewport,selectedItem}=contentContext;viewport&&selectedItem&&(viewport.scrollTop=viewport.scrollTop+selectedItem.offsetHeight)}}):null}));SelectScrollDownButton.displayName="SelectScrollDownButton";var SelectScrollButtonImpl=react.forwardRef(((props,forwardedRef)=>{const{__scopeSelect,onAutoScroll,...scrollIndicatorProps}=props,contentContext=useSelectContentContext("SelectScrollButton",__scopeSelect),autoScrollTimerRef=react.useRef(null),getItems=useCollection(__scopeSelect),clearAutoScrollTimer=react.useCallback((()=>{null!==autoScrollTimerRef.current&&(window.clearInterval(autoScrollTimerRef.current),autoScrollTimerRef.current=null)}),[]);return react.useEffect((()=>()=>clearAutoScrollTimer()),[clearAutoScrollTimer]),(0,react_use_layout_effect_dist.N)((()=>{const activeItem=getItems().find((item=>item.ref.current===document.activeElement));activeItem?.ref.current?.scrollIntoView({block:"nearest"})}),[getItems]),(0,jsx_runtime.jsx)(react_primitive_dist.sG.div,{"aria-hidden":!0,...scrollIndicatorProps,ref:forwardedRef,style:{flexShrink:0,...scrollIndicatorProps.style},onPointerDown:(0,dist.m)(scrollIndicatorProps.onPointerDown,(()=>{null===autoScrollTimerRef.current&&(autoScrollTimerRef.current=window.setInterval(onAutoScroll,50))})),onPointerMove:(0,dist.m)(scrollIndicatorProps.onPointerMove,(()=>{contentContext.onItemLeave?.(),null===autoScrollTimerRef.current&&(autoScrollTimerRef.current=window.setInterval(onAutoScroll,50))})),onPointerLeave:(0,dist.m)(scrollIndicatorProps.onPointerLeave,(()=>{clearAutoScrollTimer()}))})})),SelectSeparator=react.forwardRef(((props,forwardedRef)=>{const{__scopeSelect,...separatorProps}=props;return(0,jsx_runtime.jsx)(react_primitive_dist.sG.div,{"aria-hidden":!0,...separatorProps,ref:forwardedRef})}));SelectSeparator.displayName="SelectSeparator";var SelectArrow=react.forwardRef(((props,forwardedRef)=>{const{__scopeSelect,...arrowProps}=props,popperScope=usePopperScope(__scopeSelect),context=useSelectContext("SelectArrow",__scopeSelect),contentContext=useSelectContentContext("SelectArrow",__scopeSelect);return context.open&&"popper"===contentContext.position?(0,jsx_runtime.jsx)(dist_Arrow,{...popperScope,...arrowProps,ref:forwardedRef}):null}));SelectArrow.displayName="SelectArrow";var SelectBubbleInput=react.forwardRef((({__scopeSelect,value,...props},forwardedRef)=>{const ref=react.useRef(null),composedRefs=(0,react_compose_refs_dist.s)(forwardedRef,ref),prevValue=function usePrevious(value){const ref=react.useRef({value,previous:value});return react.useMemo((()=>(ref.current.value!==value&&(ref.current.previous=ref.current.value,ref.current.value=value),ref.current.previous)),[value])}(value);return react.useEffect((()=>{const select=ref.current;if(!select)return;const selectProto=window.HTMLSelectElement.prototype,setValue=Object.getOwnPropertyDescriptor(selectProto,"value").set;if(prevValue!==value&&setValue){const event=new Event("change",{bubbles:!0});setValue.call(select,value),select.dispatchEvent(event)}}),[prevValue,value]),(0,jsx_runtime.jsx)(react_primitive_dist.sG.select,{...props,style:{...VISUALLY_HIDDEN_STYLES,...props.style},ref:composedRefs,defaultValue:value})}));function shouldShowPlaceholder(value){return""===value||void 0===value}function useTypeaheadSearch(onSearchChange){const handleSearchChange=(0,react_use_callback_ref_dist.c)(onSearchChange),searchRef=react.useRef(""),timerRef=react.useRef(0),handleTypeaheadSearch=react.useCallback((key=>{const search=searchRef.current+key;handleSearchChange(search),function updateSearch(value){searchRef.current=value,window.clearTimeout(timerRef.current),""!==value&&(timerRef.current=window.setTimeout((()=>updateSearch("")),1e3))}(search)}),[handleSearchChange]),resetTypeahead=react.useCallback((()=>{searchRef.current="",window.clearTimeout(timerRef.current)}),[]);return react.useEffect((()=>()=>window.clearTimeout(timerRef.current)),[]),[searchRef,handleTypeaheadSearch,resetTypeahead]}function findNextItem(items,search,currentItem){const normalizedSearch=search.length>1&&Array.from(search).every((char=>char===search[0]))?search[0]:search,currentItemIndex=currentItem?items.indexOf(currentItem):-1;let wrappedItems=function wrapArray(array,startIndex){return array.map(((_,index)=>array[(startIndex+index)%array.length]))}(items,Math.max(currentItemIndex,0));1===normalizedSearch.length&&(wrappedItems=wrappedItems.filter((v=>v!==currentItem)));const nextItem=wrappedItems.find((item=>item.textValue.toLowerCase().startsWith(normalizedSearch.toLowerCase())));return nextItem!==currentItem?nextItem:void 0}SelectBubbleInput.displayName="SelectBubbleInput";var dist_Root2=Select,Trigger=SelectTrigger,Value=SelectValue,Icon=SelectIcon,Portal=SelectPortal,Content2=SelectContent,Viewport=SelectViewport,Group=SelectGroup,Label=SelectLabel,Item=SelectItem,ItemText=SelectItemText,ItemIndicator=SelectItemIndicator,ScrollUpButton=SelectScrollUpButton,ScrollDownButton=SelectScrollDownButton,Separator=SelectSeparator},"./node_modules/lucide-react/dist/esm/icons/check.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.d(__webpack_exports__,{A:()=>Check});const Check=(0,__webpack_require__("./node_modules/lucide-react/dist/esm/createLucideIcon.js").A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},"./node_modules/lucide-react/dist/esm/icons/chevron-down.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.d(__webpack_exports__,{A:()=>ChevronDown});const ChevronDown=(0,__webpack_require__("./node_modules/lucide-react/dist/esm/createLucideIcon.js").A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},"./node_modules/lucide-react/dist/esm/icons/chevron-up.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.d(__webpack_exports__,{A:()=>ChevronUp});const ChevronUp=(0,__webpack_require__("./node_modules/lucide-react/dist/esm/createLucideIcon.js").A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])}}]);