import api from './api'
import coreServiceApi from './coreServiceApi'
import { Agent, AgentSettings, AgentData, AgentLog, AgentRule, AgentType, AgentStatus } from '@/types/agent'

// Define the payload for creating an agent
export interface CreateAgentPayload {
  name: string;
  agentType: AgentType;
  description?: string;
  status?: AgentStatus; // Optional, backend might set a default like 'DRAFT'
}

// Function to create a new agent
export const createAgent = async (payload: CreateAgentPayload): Promise<Agent> => {
  console.log('Creating agent directly through Core Service (port 3002):', payload);
  const { data } = await coreServiceApi.post<Agent>('/agents', payload);
  console.log('Agent created successfully:', data);
  return data;
};

// Agents List
export const getAgents = async (): Promise<Agent[]> => {
  console.log('Fetching agents directly from Core Service (port 3002)');
  const response = await coreServiceApi.get<{ message: string; data: any[] }>('/agents');
  
  console.log('getAgents: Raw agents data from API:', response.data.data);
  
  // Transform the data to match our Agent interface - convert snake_case to camelCase
  const transformedData = response.data.data.map(agent => ({
    ...agent,
    // 确保type字段正确映射
    type: agent.type || agent.agentType || agent.agent_type,
    // 转换时间字段
    createdAt: agent.created_at || agent.createdAt,
    updatedAt: agent.updated_at || agent.updatedAt,
    // 确保其他必要字段
    status: agent.status || 'draft',
    isActive: agent.is_active !== undefined ? agent.is_active : agent.isActive !== undefined ? agent.isActive : true,
    platforms: agent.platforms || [],
    configuration: agent.configuration || agent.config || {},
    performance: agent.performance || {
      totalInteractions: 0,
      successfulResponses: 0,
      failedResponses: 0,
      averageResponseTime: 0,
      userSatisfactionScore: 0,
      uptime: 100,
      lastUpdated: new Date()
    },
    createdBy: agent.created_by || agent.createdBy || 'system',
    lastModifiedBy: agent.last_modified_by || agent.lastModifiedBy || 'system'
  }));
  
  console.log('getAgents: Transformed agents data, sample:', transformedData.slice(0, 2).map(agent => ({
    id: agent.id,
    name: agent.name,
    type: agent.type,
    status: agent.status
  })));
  
  return transformedData;
};

export const updateAgentStatus = async (agentId: string, status: 'ACTIVE' | 'INACTIVE'): Promise<Agent> => {
  console.log(`Updating agent status directly through Core Service (port 3002): ${agentId} -> ${status}`);
  const { data } = await coreServiceApi.patch(`/agents/${agentId}/status`, { status });
  return data;
};

// Agent Details
export const getAgentById = async (agentId: string): Promise<Agent> => {
  console.log(`Fetching agent by ID directly from Core Service (port 3002): ${agentId}`);
  const response = await coreServiceApi.get<{ message: string; data: any }>(`/agents/${agentId}`);
  
  // Transform snake_case to camelCase
  const agent = response.data.data;
  console.log('getAgentById: Raw agent data from API:', agent);
  
  const transformedAgent = {
    ...agent,
    // 确保type字段正确映射
    type: agent.type || agent.agentType || agent.agent_type,
    // 转换时间字段
    createdAt: agent.created_at || agent.createdAt,
    updatedAt: agent.updated_at || agent.updatedAt,
    // 确保其他必要字段
    status: agent.status || 'draft',
    isActive: agent.is_active !== undefined ? agent.is_active : agent.isActive !== undefined ? agent.isActive : true,
    platforms: agent.platforms || [],
    configuration: agent.configuration || agent.config || {},
    performance: agent.performance || {
      totalInteractions: 0,
      successfulResponses: 0,
      failedResponses: 0,
      averageResponseTime: 0,
      userSatisfactionScore: 0,
      uptime: 100,
      lastUpdated: new Date()
    },
    createdBy: agent.created_by || agent.createdBy || 'system',
    lastModifiedBy: agent.last_modified_by || agent.lastModifiedBy || 'system'
  };
  
  console.log('getAgentById: Transformed agent data:', {
    id: transformedAgent.id,
    name: transformedAgent.name,
    type: transformedAgent.type,
    status: transformedAgent.status,
    originalType: agent.type,
    originalAgentType: agent.agentType,
    originalAgent_type: agent.agent_type
  });
  
  return transformedAgent;
};

// Agent Settings
export const getAgentSettings = async (agentId: string): Promise<AgentSettings> => {
  // 从 API 响应中提取实际的 settings 对象
  console.log(`Making GET request directly to core service (port 3002) for /agents/${agentId}/settings`);
  
  try {
    // 使用直接连接到核心服务的API客户端，注意路径是/agents而不是/core/agents
    const response = await coreServiceApi.get<{ message: string; data: AgentSettings }>(`/agents/${agentId}/settings`);
    console.log('Settings fetch success using direct core service connection:', response.data);
    return response.data.data;
  } catch (error: any) {
    console.error('Error fetching agent settings:', error);
    throw error;
  }
};

export const updateAgentSettings = async (agentId: string, settings: Partial<AgentSettings>): Promise<AgentSettings> => {
  console.log('updateAgentSettings called with:', { agentId, settings });
  
  try {
    // 简化: 基本的负载处理
    const payload = {
      name: settings.name,
      description: settings.description,
      active: settings.active,
      config: settings.config || {}
    };
    
    // 记录数据大小，以便检查是否超过限制
    const payloadString = JSON.stringify(payload);
    const payloadSize = payloadString.length;
    console.log(`Making PUT request directly to core service (port 3002) for /agents/${agentId}/settings with payload size: ${payloadSize} bytes`);
    console.log(`Config data size: ${JSON.stringify(payload.config).length} bytes`);
    
    // 记录数据字段和结构，帮助排查可能的问题
    console.log('Payload structure:', {
      top_level_fields: Object.keys(payload),
      config_fields: Object.keys(payload.config || {}),
      name_length: payload.name?.length || 0,
      description_length: payload.description?.length || 0,
      config_json: JSON.stringify(payload.config, null, 2).substring(0, 500) + '...' // 只显示前500个字符防止日志过大
    });
    
    // 检查负载大小是否超过API限制（1MB）
    const MAX_PAYLOAD_SIZE = 1024 * 1024; // 1MB
    if (payloadSize > MAX_PAYLOAD_SIZE) {
      console.error(`Payload size (${payloadSize} bytes) exceeds maximum allowed size (${MAX_PAYLOAD_SIZE} bytes)`);
      
      // 尝试简化大型配置对象
      const simplifiedConfig = { ...payload.config };
      
      // 特别检查知识库文件信息，这是最可能大量占用空间的
      if (simplifiedConfig.externalKBFilesInfo && Array.isArray(simplifiedConfig.externalKBFilesInfo)) {
        console.log(`External KB files info has ${simplifiedConfig.externalKBFilesInfo.length} files`);
        // 保留文件信息但如果过多则限制数量
        if (simplifiedConfig.externalKBFilesInfo.length > 20) {
          console.log('Too many knowledge base files, limiting to 20');
          simplifiedConfig.externalKBFilesInfo = simplifiedConfig.externalKBFilesInfo.slice(0, 20);
        }
      }
      
      // 更新为简化后的配置
      payload.config = simplifiedConfig;
      
      // 再次检查大小
      const simplifiedPayloadSize = JSON.stringify(payload).length;
      console.log(`Simplified payload size: ${simplifiedPayloadSize} bytes`);
      
      if (simplifiedPayloadSize > MAX_PAYLOAD_SIZE) {
        throw new Error(`设置数据过大，无法保存。请减少上传的知识库文件数量或简化设置内容。当前大小: ${Math.round(payloadSize/1024)}KB，最大允许: ${Math.round(MAX_PAYLOAD_SIZE/1024)}KB。`);
      }
    }
    
    // 使用直接连接到核心服务的API客户端，注意路径是/agents而不是/core/agents
    const response = await coreServiceApi.put(`/agents/${agentId}/settings`, payload);
    
    if (response.data) {
      console.log('Settings update success using direct core service connection:', response.data);
      // 记录返回数据结构
      console.log('Response structure:', {
        success: true,
        data_type: typeof response.data,
        status: response.status,
        fields: Object.keys(response.data)
      });
      return response.data;
    } else {
      console.error('No data in response:', response);
      throw new Error('No data received from server');
    }
  } catch (error: any) {
    console.error('Error updating agent settings:', error);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
    throw error;
  }
};

// Agent Data (Analytics)
export const getAgentData = async (agentId: string, params?: { timeRange?: string; metric?: string; }): Promise<AgentData> => {
  const { data } = await api.get(`/agents/${agentId}/data`, { params });
  return data;
};

// Agent Logs
export const getAgentLogs = async (agentId: string): Promise<AgentLog[]> => {
  const { data } = await api.get(`/agents/${agentId}/logs`);
  return data;
};

// Agent Rules
export const getAgentRules = async (agentId: string): Promise<AgentRule[]> => {
  const { data } = await api.get(`/agents/${agentId}/rules`);
  return data;
};

export const createAgentRule = async (agentId: string, rule: Omit<AgentRule, 'id' | 'agentId' | 'createdAt' | 'updatedAt'>): Promise<AgentRule> => {
  const { data } = await api.post(`/agents/${agentId}/rules`, rule);
  return data;
};

export const updateAgentRule = async (agentId: string, ruleId: string, rule: Partial<AgentRule>): Promise<AgentRule> => {
  const { data } = await api.put(`/agents/${agentId}/rules/${ruleId}`, rule);
  return data;
};

export const deleteAgentRule = async (agentId: string, ruleId: string): Promise<void> => {
  await api.delete(`/agents/${agentId}/rules/${ruleId}`);
};

export const testAgent = async (agentId: string, input: { inputText: string }): Promise<any> => {
  console.log(`Testing agent ${agentId} with input:`, input);
  
  try {
    // 使用Core Service API测试代理
    const response = await coreServiceApi.post(`/agents/${agentId}/test`, input);
    console.log('Agent test response:', response.data);
    return response.data;
  } catch (error: any) {
    console.error('Error testing agent:', error);
    
    // 提供详细的错误信息
    if (error.response) {
      console.error('Test error status:', error.response.status);
      console.error('Test error data:', error.response.data);
      
      // 解析并抛出更有意义的错误
      const errorMessage = error.response.data?.message || 
                          error.response.data?.error || 
                          `测试失败 (状态: ${error.response.status})`;
      throw new Error(errorMessage);
    }
    
    throw error;
  }
};

// Function to delete an agent by ID
export const deleteAgent = async (agentId: string): Promise<{ message: string; agentId: string }> => {
  // The backend currently returns { message: string, agentId: string }, adjust if backend changes
  const { data } = await api.delete<{ message: string; agentId: string }>(`/core/agents/${agentId}`);
  return data;
};

/**
 * 代理服务 - 处理与代理相关的API调用
 */
const agentService = {
  /**
   * 获取所有代理
   */
  async getAgents() {
    try {
      const response = await api.get('/core/agents');
      return response.data.data || [];
    } catch (error) {
      console.error('获取代理列表失败:', error);
      throw error;
    }
  },

  /**
   * 获取单个代理
   * @param agentId 代理ID
   */
  async getAgent(agentId: string) {
    try {
      const response = await api.get(`/core/agents/${agentId}`);
      return response.data.data;
    } catch (error) {
      console.error(`获取代理(ID:${agentId})失败:`, error);
      throw error;
    }
  },

  /**
   * 创建代理
   * @param agentData 代理数据
   */
  async createAgent(agentData: any) {
    try {
      const response = await api.post('/core/agents', agentData);
      return response.data;
    } catch (error) {
      console.error('创建代理失败:', error);
      throw error;
    }
  },

  /**
   * 更新代理
   * @param agentId 代理ID
   * @param agentData 更新数据
   */
  async updateAgent(agentId: string, agentData: any) {
    try {
      const response = await api.put(`/core/agents/${agentId}`, agentData);
      return response.data;
    } catch (error) {
      console.error(`更新代理(ID:${agentId})失败:`, error);
      throw error;
    }
  },

  /**
   * 删除代理
   * @param agentId 代理ID
   */
  async deleteAgent(agentId: string) {
    try {
      const response = await api.delete(`/core/agents/${agentId}`);
      return response.data;
    } catch (error) {
      console.error(`删除代理(ID:${agentId})失败:`, error);
      throw error;
    }
  },

  /**
   * 获取代理设置
   * @param agentId 代理ID
   */
  async getAgentSettings(agentId: string) {
    try {
      const response = await api.get(`/core/agents/${agentId}/settings`);
      return response.data.data || {};
    } catch (error) {
      console.error(`获取代理设置(ID:${agentId})失败:`, error);
      throw error;
    }
  },

  /**
   * 更新代理设置 - 简化版实现
   * @param params 包含代理ID和设置数据
   */
  async updateAgentSettings({ agentId, settings }: { agentId: string, settings: any }) {
    console.log('updateAgentSettings called with:', { agentId, settings });
    try {
      // 准备发送的数据
      const payload = {
        name: settings.name,
        description: settings.description,
        active: settings.active,
        config: settings.config || {}
      };
      
      console.log(`Making PUT request directly to core service (port 3002) for /agents/${agentId}/settings with payload:`, payload);
      console.log(`Config data, size: ${JSON.stringify(payload.config).length} bytes`);
      
      // 使用直接连接到核心服务的API客户端，注意路径是/agents而不是/core/agents
      const response = await coreServiceApi.put(`/agents/${agentId}/settings`, payload);
      
      console.log(`Settings update success for agent, ${agentId}:`, response.data);
      return response.data;
    } catch (error: any) {
      console.error(`更新代理设置失败(ID:${agentId}):`, error);
      
      // 添加更详细的错误日志
      if (error.response) {
        console.error('Error, response:', {
          status: error.response.status,
          data: error.response.data
        });
      } else if (error.request) {
        console.error('No response, received:', {
          request: error.request,
          message: error.message
        });
      }
      
      throw error;
    }
  },

  /**
   * 获取代理规则
   * @param agentId 代理ID
   */
  async getAgentRules(agentId: string) {
    try {
      const response = await api.get(`/core/agents/${agentId}/rules`);
      return response.data.data || [];
    } catch (error) {
      console.error(`获取代理规则(ID:${agentId})失败:`, error);
      throw error;
    }
  },

  /**
   * 创建代理规则
   * @param agentId 代理ID
   * @param ruleData 规则数据
   */
  async createAgentRule(agentId: string, ruleData: any) {
    try {
      const response = await api.post(`/core/agents/${agentId}/rules`, ruleData);
      return response.data;
    } catch (error) {
      console.error(`创建代理规则(ID:${agentId})失败:`, error);
      throw error;
    }
  },

  /**
   * 更新代理规则
   * @param agentId 代理ID
   * @param ruleId 规则ID
   * @param ruleData 规则数据
   */
  async updateAgentRule(agentId: string, ruleId: string, ruleData: any) {
    try {
      const response = await api.put(`/core/agents/${agentId}/rules/${ruleId}`, ruleData);
      return response.data;
    } catch (error) {
      console.error(`更新代理规则(ID:${agentId}, 规则ID:${ruleId})失败:`, error);
      throw error;
    }
  },

  /**
   * 删除代理规则
   * @param agentId 代理ID
   * @param ruleId 规则ID
   */
  async deleteAgentRule(agentId: string, ruleId: string) {
    try {
      const response = await api.delete(`/core/agents/${agentId}/rules/${ruleId}`);
      return response.data;
    } catch (error) {
      console.error(`删除代理规则(ID:${agentId}, 规则ID:${ruleId})失败:`, error);
      throw error;
    }
  },

  /**
   * 获取代理日志
   * @param agentId 代理ID
   * @param page 页码
   * @param limit 每页限制
   */
  async getAgentLogs(agentId: string, page = 1, limit = 10) {
    try {
      const response = await api.get(`/core/agents/${agentId}/logs`, {
        params: { page, limit }
      });
      return response.data.data || { logs: [], total: 0, page, limit, totalPages: 0 };
    } catch (error) {
      console.error(`获取代理日志(ID:${agentId})失败:`, error);
      throw error;
    }
  }
};

export default agentService;