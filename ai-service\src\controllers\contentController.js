/**
 * 内容生成控制器
 * 处理AI内容生成相关的请求
 */
const logger = require('../common/utils/logger');
const aiService = require('../services/aiService');

/**
 * 生成文本内容
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function generateContent(req, res) {
  try {
    const { prompt, template, length, style, userId } = req.body;
    
    // 输入验证
    if (!prompt) {
      return res.status(400).json({
        success: false,
        error: '提示语不能为空'
      });
    }
    
    // 记录请求
    logger.info(`内容生成请求：用户=${userId}`, {
      promptLength: prompt.length,
      template: template || 'none',
      style: style || 'none'
    });
    
    // 准备生成参数
    const params = {
      text: prompt,
      userId,
      context: {
        template,
        contentType: 'generated',
        style,
        targetLength: length
      }
    };
    
    // 调用AI服务生成内容
    const result = await aiService.generateContent(params);
    
    // 返回成功响应
    return res.status(200).json({
      success: true,
      data: {
        content: result.text,
        model: result.model,
        processingTime: result.processingTime
      }
    });
  } catch (error) {
    // 记录错误
    logger.error(`内容生成错误: ${error.message}`, { error });
    
    // 返回错误响应
    return res.status(500).json({
      success: false,
      error: '生成内容时出错'
      details: error.message
    });
  }
}

/**
 * 总结文本内容
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function summarizeContent(req, res) {
  try {
    const { text, length, userId } = req.body;
    
    // 输入验证
    if (!text) {
      return res.status(400).json({
        success: false,
        error: '文本内容不能为空'
      });
    }
    
    // 记录请求
    logger.info(`文本总结请求：用户=${userId}`, {
      textLength: text.length,
      targetLength: length || 'default'
    });
    
    // 准备总结参数
    const params = {
      text: `请总结以下文本:\n\n${text}`,
      userId,
      context: {
        contentType: 'summary'
        targetLength: length
      }
    };
    
    // 调用AI服务总结内容
    const result = await aiService.generateContent(params);
    
    // 返回成功响应
    return res.status(200).json({
      success: true,
      data: {
        summary: result.text,
        model: result.model,
        processingTime: result.processingTime
      }
    });
  } catch (error) {
    // 记录错误
    logger.error(`文本总结错误: ${error.message}`, { error });
    
    // 返回错误响应
    return res.status(500).json({
      success: false,
      error: '总结文本时出错'
      details: error.message
    });
  }
}

/**
 * 翻译文本内容
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function translateContent(req, res) {
  try {
    const { text, targetLanguage, userId } = req.body;
    
    // 输入验证
    if (!text) {
      return res.status(400).json({
        success: false,
        error: '文本内容不能为空'
      });
    }
    
    if (!targetLanguage) {
      return res.status(400).json({
        success: false,
        error: '目标语言不能为空'
      });
    }
    
    // 记录请求
    logger.info(`文本翻译请求：用户=${userId}`, {
      textLength: text.length,
      targetLanguage
    });
    
    // 准备翻译参数
    const params = {
      text: `请将以下文本翻译成${targetLanguage}:\n\n${text}`,
      userId,
      context: {
        contentType: 'translation'
        targetLanguage
      }
    };
    
    // 调用AI服务翻译内容
    const result = await aiService.generateContent(params);
    
    // 返回成功响应
    return res.status(200).json({
      success: true,
      data: {
        translation: result.text,
        sourceLanguage: 'auto-detect'
        targetLanguage,
        model: result.model,
        processingTime: result.processingTime
      }
    });
  } catch (error) {
    // 记录错误
    logger.error(`文本翻译错误: ${error.message}`, { error });
    
    // 返回错误响应
    return res.status(500).json({
      success: false,
      error: '翻译文本时出错'
      details: error.message
    });
  }
}

/**
 * 分析文本内容
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function analyzeContent(req, res) {
  try {
    const { text, analysisType, userId } = req.body;
    
    // 输入验证
    if (!text) {
      return res.status(400).json({
        success: false,
        error: '文本内容不能为空'
      });
    }
    
    // 记录请求
    logger.info(`文本分析请求：用户=${userId}`, {
      textLength: text.length,
      analysisType: analysisType || 'general'
    });
    
    // 准备分析参数
    let prompt = '请分析以下文本：\n\n' + text;
    if (analysisType) {
      prompt = `请进行${analysisType}分析：\n\n${text}`;
    }
    
    const params = {
      text: prompt,
      userId,
      context: {
        contentType: 'analysis'
        analysisType: analysisType || 'general'
      }
    };
    
    // 调用AI服务分析内容
    const result = await aiService.generateContent(params);
    
    // 返回成功响应
    return res.status(200).json({
      success: true,
      data: {
        analysis: result.text,
        analysisType: analysisType || 'general'
        model: result.model,
        processingTime: result.processingTime
      }
    });
  } catch (error) {
    // 记录错误
    logger.error(`文本分析错误: ${error.message}`, { error });
    
    // 返回错误响应
    return res.status(500).json({
      success: false,
      error: '分析文本时出错'
      details: error.message
    });
  }
}

module.exports = {
  generateContent,
  summarizeContent,
  translateContent,
  analyzeContent
}; 