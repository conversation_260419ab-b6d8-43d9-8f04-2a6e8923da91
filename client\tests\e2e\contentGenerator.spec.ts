import { test, expect } from '@playwright/test';

test('内容生成器完整流程', async ({ page }) => {
  // 访问内容生成器页面
  await page.goto('/modules/content-generator');
  
  // 检查页面标题和主要元素
  await expect(page.locator('h1, h2')).toContainText(/Content Generator|内容生成器/);
  
  // 测试生成内容流程
  await test.step('生成内容', async () => {
    // 输入提示
    await page.locator('textarea[name="prompt"]').fill('写一篇关于人工智能的短文');
    
    // 点击生成按钮
    await page.locator('button:has-text("Generate")').click();
    
    // 等待结果加载
    await page.waitForSelector('.loading', { state: 'detached' });
    
    // 验证生成结果显示
    await expect(page.locator('.result-content')).toBeVisible();
    await expect(page.locator('.result-content')).not.toBeEmpty();
  });
  
  // 测试历史记录功能
  await test.step('查看历史记录', async () => {
    // 切换到历史标签
    await page.locator('button:has-text("History")').click();
    
    // 验证历史记录包含我们刚才生成的内容
    await expect(page.locator('.history-item').first()).toBeVisible();
    await expect(page.locator('.history-item').first()).toContainText('人工智能');
  });
  
  // 测试模板功能
  await test.step('模板管理', async () => {
    // 切换到模板标签
    await page.locator('button:has-text("Templates")').click();
    
    // 验证模板列表加载
    await expect(page.locator('.template-list')).toBeVisible();
    
    // 点击添加新模板
    await page.locator('button:has-text("Add Template")').click();
    
    // 填写模板表单
    await page.locator('input[name="name"]').fill('测试模板');
    await page.locator('textarea[name="description"]').fill('这是一个测试模板');
    await page.locator('textarea[name="prompt"]').fill('生成一篇关于{{主题}}的内容');
    
    // 保存模板
    await page.locator('button:has-text("Save")').click();
    
    // 验证新模板出现在列表中
    await expect(page.locator('.template-item:has-text("测试模板")')).toBeVisible();
    
    // 测试使用模板
    await page.locator('.template-item:has-text("测试模板") button:has-text("Use")').click();
    
    // 验证模板内容被加载到生成器中
    await expect(page.locator('textarea[name="prompt"]')).toHaveValue('生成一篇关于{{主题}}的内容');
  });
}); 