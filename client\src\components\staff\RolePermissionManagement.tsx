import React, { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle
} from "@/components/ui/alert-dialog"
import {
  Shield, 
  Plus, 
  Edit, 
  Trash2, 
  Users, 
  MoreHorizontal,
  Search,
  Key,
  Settings,
  // CheckCircle,
  // XCircle,
  UserCheck,
  // Lock
} from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

// 权限类型定义
interface Permission {
  id: string;
  name: string;
  description: string;
  category: string;
  resource: string;
  action: string;
}

// 角色类型定义
interface Role {
  id: string;
  name: string;
  description: string;
  permissions: string[];
  userCount: number;
  isSystem: boolean;
  createdAt: string;
  level: 'admin' | 'manager' | 'user' | 'viewer'
  
};

// 用户角色分配
interface UserRole {
  userId: string;
  userName: string;
  email: string;
  currentRole: string;
  department: string
};

export function RolePermissionManagement() {
  const { t } = useTranslation();
  const { toast } = useToast();
  
  // 权限分类 - 使用动态翻译
  const PERMISSION_CATEGORIES = [
    t('permissions.userManagement'),
    t('permissions.contentManagement'), 
    t('permissions.analytics'),
    t('permissions.systemSettings'),
    t('permissions.financial'),
    t('permissions.apiAccess')
  ];

  // 模拟权限数据 - 使用动态翻译
  const MOCK_PERMISSIONS: Permission[] = [
    // User Management
    { id: 'user.create', name: t('permissions.createUsers'), description: 'Create new user accounts', category: t('permissions.userManagement'), resource: 'users', action: 'create' },
    { id: 'user.read', name: t('permissions.viewUsers'), description: 'View user profiles and lists', category: t('permissions.userManagement'), resource: 'users', action: 'read' },
    { id: 'user.update', name: t('permissions.editUsers'), description: 'Edit user information and settings', category: t('permissions.userManagement'), resource: 'users', action: 'update' },
    { id: 'user.delete', name: t('permissions.deleteUsers'), description: 'Delete user accounts', category: t('permissions.userManagement'), resource: 'users', action: 'delete' },
    
    // Content Management
    { id: 'content.create', name: t('permissions.createContent'), description: 'Create new content and posts', category: t('permissions.contentManagement'), resource: 'content', action: 'create' },
    { id: 'content.read', name: t('permissions.viewContent'), description: 'View all content and posts', category: t('permissions.contentManagement'), resource: 'content', action: 'read' },
    { id: 'content.update', name: t('permissions.editContent'), description: 'Edit existing content', category: t('permissions.contentManagement'), resource: 'content', action: 'update' },
    { id: 'content.delete', name: t('permissions.deleteContent'), description: 'Delete content and posts', category: t('permissions.contentManagement'), resource: 'content', action: 'delete' },
    { id: 'content.publish', name: t('permissions.publishContent'), description: 'Publish and unpublish content', category: t('permissions.contentManagement'), resource: 'content', action: 'publish' },
    
    // Analytics
    { id: 'analytics.read', name: t('permissions.viewAnalytics'), description: 'Access analytics dashboards', category: t('permissions.analytics'), resource: 'analytics', action: 'read' },
    { id: 'analytics.export', name: t('permissions.exportAnalytics'), description: 'Export analytics data and reports', category: t('permissions.analytics'), resource: 'analytics', action: 'export' },
    
    // System Settings
    { id: 'settings.read', name: t('permissions.viewSettings'), description: 'View system configuration', category: t('permissions.systemSettings'), resource: 'settings', action: 'read' },
    { id: 'settings.update', name: t('permissions.manageSettings'), description: 'Modify system settings', category: t('permissions.systemSettings'), resource: 'settings', action: 'update' },
    { id: 'settings.security', name: t('permissions.securitySettings'), description: 'Manage security and authentication', category: t('permissions.systemSettings'), resource: 'security', action: 'manage' },
    
    // Financial
    { id: 'billing.read', name: t('permissions.viewBilling'), description: 'View billing information and invoices', category: t('permissions.financial'), resource: 'billing', action: 'read' },
    { id: 'billing.manage', name: t('permissions.manageBilling'), description: 'Manage billing and payment methods', category: t('permissions.financial'), resource: 'billing', action: 'manage' },
    
    // API Access
    { id: 'api.read', name: t('permissions.apiReadAccess'), description: 'Read data via API', category: t('permissions.apiAccess'), resource: 'api', action: 'read' },
    { id: 'api.write', name: t('permissions.apiWriteAccess'), description: 'Write data via API', category: t('permissions.apiAccess'), resource: 'api', action: 'write' },
    { id: 'api.admin', name: t('permissions.apiAdminAccess'), description: 'Full API administrative access', category: t('permissions.apiAccess'), resource: 'api', action: 'admin' }
  ];

  // 模拟角色数据
  const MOCK_ROLES: Role[] = [
    {
      id: 'admin', name: t('roles.admin'), description: t('permissions.fullSystemAccess'), permissions: MOCK_PERMISSIONS.map(p => p.id),
      userCount: 2,
      isSystem: true,
      createdAt: '2023-01-01', level: 'admin'
    },
    {
      id: 'manager', name: t('roles.manager'), description: t('permissions.departmentManagement'), permissions: [
        'user.read', 'user.update', 'content.create', 'content.read', 'content.update', 'content.publish',
        'analytics.read', 'analytics.export', 'settings.read'
      ],
      userCount: 8,
      isSystem: true,
      createdAt: '2023-01-01', level: 'manager'
    },
    {
      id: 'editor', name: t('roles.editor'), description: t('permissions.contentCreation'), permissions: [
        'content.create', 'content.read', 'content.update', 'content.publish', 'user.read', 'analytics.read'
      ],
      userCount: 15,
      isSystem: false,
      createdAt: '2023-02-15', level: 'user'
    },
    {
      id: 'viewer', name: t('roles.viewer'), description: t('permissions.readOnlyAccess'), permissions: ['user.read', 'content.read', 'analytics.read'],
      userCount: 25,
      isSystem: true,
      createdAt: '2023-01-01', level: 'viewer'
    }
  ];

  // 模拟用户角色数据
  const MOCK_USER_ROLES: UserRole[] = [
    { userId: '1', userName: 'Zhang San', email: '<EMAIL>', currentRole: 'admin', department: 'IT' },
    { userId: '2', userName: 'Li Si', email: '<EMAIL>', currentRole: 'manager', department: 'Sales' },
    { userId: '3', userName: 'Wang Wu', email: '<EMAIL>', currentRole: 'editor', department: 'Marketing' },
    { userId: '4', userName: 'Zhao Liu', email: '<EMAIL>', currentRole: 'viewer', department: 'Finance' }
  ];

  const [activeTab, setActiveTab] = useState('roles');
  
  // 角色管理状态
  const [roles, setRoles] = useState<Role[]>(MOCK_ROLES);
  const [searchTerm, setSearchTerm] = useState('');
  const [isRoleDialogOpen, setIsRoleDialogOpen] = useState(false);
  const [isDeleteRoleDialogOpen, setIsDeleteRoleDialogOpen] = useState(false);
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);
  const [roleFormData, setRoleFormData] = useState<Partial<Role>>({ permissions: [] });
  
  // 权限管理状态
  const [permissions] = useState<Permission[]>(MOCK_PERMISSIONS);
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([]);
  
  // 用户角色分配状态
  // const [userRoles, setUserRoles] = useState<UserRole[]>(MOCK_USER_ROLES); // Commented out unused variable

  // 过滤角色
  const filteredRoles = roles.filter(role => 
    role.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    role.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // 根据分类分组权限
  const groupedPermissions = PERMISSION_CATEGORIES.reduce((acc, category) => {
    acc[category] = permissions.filter(p => p.category === category);
    return acc;
  }, {} as Record<string, Permission[]>);

  // 重置角色表单
  const resetRoleForm = () => {
    setRoleFormData({ permissions: [] });
    setSelectedPermissions([]);
    setSelectedRole(null);
  };

  // 添加/编辑角色
  const handleSaveRole = () => {
    if (!roleFormData.name || !roleFormData.description) {
      toast({
        title: t('common.error'), description: t('roles.fillRoleNameDescription'), variant: "destructive"
      });
      return;
    }

    if (selectedRole) {
      // 编辑现有角色
      setRoles(roles.map(role => 
        role.id === selectedRole.id 
          ? { ...role, ...roleFormData, permissions: selectedPermissions }
          : role
      ));
      toast({
        title: t('common.success'), description: t('roles.roleUpdatedSuccess')
      });
    } else {
      // 添加新角色
      const newRole: Role = {
        id: Date.now().toString(), name: roleFormData.name!, description: roleFormData.description!, permissions: selectedPermissions,
        userCount: 0,
        isSystem: false,
        createdAt: new Date().toISOString().split('T')[0], level: roleFormData.level || 'user'
      };
      
      setRoles([...roles, newRole]);
      toast({
        title: t('common.success'), description: t('roles.roleCreatedSuccess')
      });
    }
    
    setIsRoleDialogOpen(false);
    resetRoleForm();
  };

  // 删除角色
  const handleDeleteRole = () => {
    if (!selectedRole) return;
    
    if (selectedRole.isSystem) {
      toast({
        title: t('common.error'), description: t('roles.cannotDeleteSystemRole'), variant: "destructive"
      });
      return;
    }
    
    setRoles(roles.filter(role => role.id !== selectedRole.id));
    setIsDeleteRoleDialogOpen(false);
    resetRoleForm();
    
    toast({
      title: t('common.success'), description: t('roles.roleDeletedSuccess')
    });
  };

  // 打开编辑角色对话框
  const openEditRoleDialog = (role: Role) => {
    setSelectedRole(role);
    setRoleFormData({ ...role, permissions: [...role.permissions] });
    setSelectedPermissions(role.permissions);
    setIsRoleDialogOpen(true);
  };

  // 打开删除角色对话框
  const openDeleteRoleDialog = (role: Role) => {
    setSelectedRole(role);
    setIsDeleteRoleDialogOpen(true);
  };

  // 切换权限选择
  const togglePermission = (permissionId: string) => {
    setSelectedPermissions(prev => 
      prev.includes(permissionId) ? prev.filter(id => id !== permissionId)
         : [...prev, permissionId]
    );
  };

  // 切换分类权限
  const toggleCategoryPermissions = (category: string, checked: boolean) => {
    const categoryPermissions = (groupedPermissions[category] as Permission[]).map((p: Permission) => p.id); 
    if (checked) {
      setSelectedPermissions(prev => [...new Set([...prev, ...categoryPermissions])])
    } else {
      setSelectedPermissions(prev => prev.filter(id => !categoryPermissions.includes(id)))
    }
  };

  // 检查分类是否完全选中
  const isCategoryFullySelected = (category: string) => {
    const categoryPermissions = (groupedPermissions[category] as Permission[]).map((p: Permission) => p.id); 
    return categoryPermissions.every((id: string) => selectedPermissions.includes(id))
  };

  // 获取角色级别颜色
  const getRoleLevelColor = (level: string) => {
    switch (level) {
      case 'admin': return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
      case 'manager': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400'
      case 'user': return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
      case 'viewer': return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400'
    }
  };

  // 根据角色ID获取正确的名称翻译
  const getRoleNameById = (roleId: string) => {
    switch (roleId) {
      case 'admin': return t('roles.admin');
      case 'manager': return t('roles.manager');
      case 'editor': return t('roles.editor');
      case 'viewer': return t('roles.viewer');
      default: return roles.find(r => r.id === roleId)?.name || roleId; // 对于自定义角色，使用原始名称
    }
  };

  // 根据角色ID获取正确的描述翻译
  const getRoleDescriptionById = (roleId: string) => {
    switch (roleId) {
      case 'admin': return t('permissions.fullSystemAccess');
      case 'manager': return t('permissions.departmentManagement');
      case 'editor': return t('permissions.contentCreation');
      case 'viewer': return t('permissions.readOnlyAccess');
      default: return roles.find(r => r.id === roleId)?.description || ''; // 对于自定义角色，使用原始描述
    }
  };

  return (<div className="space-y-6">
      {/* 头部 */}
      <div className="flex flex-col sm: flex-row justify-between items-start s m:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">{t('roles.management')}</h2>
          <p className="text-gray-600 dark:text-gray-300">{t('roles.description')}</p>
        </div>
      </div>
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="roles" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            {t('roles.roles')}
          </TabsTrigger>
          <TabsTrigger value="permissions" className="flex items-center gap-2">
            <Key className="h-4 w-4" />
            {t('roles.permissions')}
          </TabsTrigger>
          <TabsTrigger value="assignments" className="flex items-center gap-2">
            <UserCheck className="h-4 w-4" />
            {t('roles.userAssignments')}
          </TabsTrigger>
        </TabsList>
        {/* 角色管理标签页 */}
        <TabsContent value="roles" className="space-y-6">
          <div className="flex flex-col sm: flex-row justify-between items-start s m:items-center gap-4">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search roles..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Dialog open={isRoleDialogOpen} onOpenChange={setIsRoleDialogOpen}>
              <DialogTrigger asChild>
                <Button onClick={() => resetRoleForm()}>
                  <Plus className="mr-2 h-4 w-4" />
                  {t('roles.addRole')}
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle>
                    {selectedRole ? t('roles.updateRole') : t('roles.createNewRole')}
                  </DialogTitle>
                  <DialogDescription>
                    {selectedRole ? t('roles.updateRoleInfo') : t('roles.createNewRole')}
                  </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-1 gap-2">
                    <Label htmlFor="name">{t('roles.roleName')}</Label>
                    <Input
                      id="name"
                      value={roleFormData.name}
                      onChange={(e) => setRoleFormData({ ...roleFormData, name: e.target.value })}
                      placeholder={t('roles.roleName')}
                    />
                  </div>
                  <div className="grid grid-cols-1 gap-2">
                    <Label htmlFor="description">{t('roles.roleDescription')}</Label>
                    <Textarea
                      id="description"
                      value={roleFormData.description}
                      onChange={(e) => setRoleFormData({ ...roleFormData, description: e.target.value })}
                      placeholder={t('roles.briefRoleDescription')}
                      rows={3}
                    />
                  </div>
                  {/* 权限设置 */}
                  <div className="mt-4">
                    <h3 className="text-lg font-medium mb-3">{t('roles.permissions')}</h3>
                    <div className="space-y-4">
                      {Object.entries(groupedPermissions).map(([category, perms]) => (
                        <div key={category} className="border rounded-lg p-4">
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="font-medium">{category}</h4>
                            <div className="flex items-center space-x-2">
                              <Switch
                                checked={isCategoryFullySelected(category)}
                                onCheckedChange={(checked) => toggleCategoryPermissions(category, checked)}
                              />
                              <span className="text-sm">
                                {isCategoryFullySelected(category) ? 
                                  t('common.all') : 
                                  `${(perms as Permission[]).filter(p => roleFormData.permissions?.includes(p.id) || false).length} / ${(perms as Permission[]).length}`
                                };
                              </span>
                            </div>
                          </div>
                          <div className="grid grid-cols-1 gap-2 mt-2">
                            {(perms as Permission[]).map((permission) => (
                              <div key={permission.id} className="flex items-center space-x-2">
                                <Switch 
                                  checked={roleFormData.permissions?.includes(permission.id) || false}
                                  onCheckedChange={() => togglePermission(permission.id)}
                                />
                                <div>
                                  <div className="font-medium text-sm">{permission.name}</div>
                                  <div className="text-xs text-muted-foreground">{permission.description}</div>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsRoleDialogOpen(false)}>
                    {t('common.cancel')}
                  </Button>
                  <Button onClick={handleSaveRole}>
                    {selectedRole ? t('common.save') : t('roles.createRole')}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
          {/* 角色统计卡片 */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{t('roles.totalRoles')}</CardTitle>
                <Shield className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{roles.length}</div>
                <p className="text-xs text-muted-foreground">
                  {roles.filter(r => r.isSystem).length} {t('roles.systemRoles')}
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{t('roles.totalUsers')}</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {roles.reduce((sum, role) => sum + role.userCount, 0)}
                </div>
                <p className="text-xs text-muted-foreground">
                  {t('roles.withAssignedRoles')}
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{t('roles.totalPermissions')}</CardTitle>
                <Key className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{permissions.length}</div>
                <p className="text-xs text-muted-foreground">
                  {t('roles.acrossCategories', { count: PERMISSION_CATEGORIES.length })}
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{t('roles.customRoles')}</CardTitle>
                <Settings className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {roles.filter(r => !r.isSystem).length}
                </div>
                <p className="text-xs text-muted-foreground">
                  {t('roles.createdByUsers')}
                </p>
              </CardContent>
            </Card>
          </div>
          {/* 角色列表 */}
          <Card>
            <CardHeader>
              <CardTitle>{t('roles.systemRoles')}</CardTitle>
              <CardDescription>
                {t('roles.manageRolesPermissions')}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{t('roles.role')}</TableHead>
                    <TableHead>{t('roles.level')}</TableHead>
                    <TableHead>{t('roles.users')}</TableHead>
                    <TableHead>{t('roles.permissions')}</TableHead>
                    <TableHead>{t('roles.type')}</TableHead>
                    <TableHead className="text-right">{t('common.actions')}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredRoles.length > 0 ? (
                    filteredRoles.map((role) => (
                      <TableRow key={role.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{getRoleNameById(role.id)}</div>
                            <div className="text-sm text-muted-foreground line-clamp-1">
                              {getRoleDescriptionById(role.id)}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge 
                            variant="secondary"
                            className={getRoleLevelColor(role.level)}
                          >
                            {t(`roles.${role.level}`)}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">
                            {role.userCount} {t('common.users')}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <span className="text-sm">
                            {role.permissions.length} {t('roles.permissions')}
                          </span>
                        </TableCell>
                        <TableCell>
                          {role.isSystem ? (<Badge variant="secondary" className="bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400">
                              {t('common.system')}
                            </Badge>
                          ) : (
                            <Badge variant="outline">
                              {t('common.custom')}
                            </Badge>
                          )}
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon">
                                <MoreHorizontal className="h-4 w-4" />
                                <span className="sr-only">{t('common.actions')}</span>
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>{t('roles.roleActions')}</DropdownMenuLabel>
                              <DropdownMenuItem onClick={() => openEditRoleDialog(role)}>
                                <Edit className="mr-2 h-4 w-4" />
                                {t('roles.editRole')}
                              </DropdownMenuItem>
                              {!role.isSystem && (
                                <>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem 
                                    onClick={() => openDeleteRoleDialog(role)}
                                    className="text-red-600 hover:text-red-700"
                                  >
                                    <Trash2 className="mr-2 h-4 w-4" />
                                    {t('roles.deleteRole')}
                                  </DropdownMenuItem>
                                </>
                              )}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center h-24">
                        <div className="flex flex-col items-center justify-center">
                          <Shield className="h-8 w-8 text-muted-foreground mb-2" />
                          <p className="text-muted-foreground">{t('roles.noRolesFound')}</p>
                          <p className="text-sm text-muted-foreground">
                            {searchTerm ? t('roles.adjustSearchCriteria') : t('roles.createFirstCustomRole')}
                          </p>
                        </div>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
        {/* 权限列表标签页 */}
        <TabsContent value="permissions" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>{t('permissions.systemPermissions')}</CardTitle>
              <CardDescription>
                {t('roles.overviewAllPermissions')}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {Object.entries(groupedPermissions).map(([category, categoryPermissions]) => (
                  <div key={category} className="border rounded-lg p-4">
                    <h3 className="font-semibold mb-4">{category}</h3>
                    <div className="grid gap-3">
                      {(categoryPermissions as Permission[]).map((permission: Permission) => (<div key={permission.id} className="flex items-start space-x-3 p-3 border rounded-md">
                          <Key className="h-4 w-4 mt-1 text-muted-foreground" />
                          <div className="flex-1">
                            <div className="font-medium">{permission.name}</div>
                            <div className="text-sm text-muted-foreground">{permission.description}</div>
                            <div className="flex items-center space-x-2 mt-1">
                              <Badge variant="outline" className="text-xs">, {permission.resource}:{permission.action}
                              </Badge>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        {/* 用户角色分配标签页 */}
        <TabsContent value="assignments" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>{t('roles.userRoleAssignments')}</CardTitle>
              <CardDescription>
                {t('roles.manageUserRoleAssignments')}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{t('common.user')}</TableHead>
                    <TableHead>{t('roles.currentRole')}</TableHead>
                    <TableHead>{t('departments.department')}</TableHead>
                    <TableHead className="text-right">{t('common.actions')}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {MOCK_USER_ROLES.map((userRole) => {
                    const role = roles.find(r => r.id === userRole.currentRole);
                    return (
                      <TableRow key={userRole.userId}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{userRole.userName}</div>
                            <div className="text-sm text-muted-foreground">{userRole.email}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          {role ? (
                            <Badge 
                              variant="secondary"
                              className={getRoleLevelColor(role.level)}
                            >
                              {getRoleNameById(role.id)}
                            </Badge>
                          ) : (
                            <span className="text-muted-foreground">{t('common.unknown')}</span>
                          )}
                        </TableCell>
                        <TableCell>{userRole.department}</TableCell>
                        <TableCell className="text-right">
                          <Button variant="ghost" size="sm">
                            <Edit className="h-4 w-4 mr-2" />
                            {t('roles.changeRole')}
                          </Button>
                        </TableCell>
                      </TableRow>
                    )
                  })}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
      {/* 删除角色确认对话框 */}
      <AlertDialog open={isDeleteRoleDialogOpen} onOpenChange={setIsDeleteRoleDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t('roles.deleteRole')}</AlertDialogTitle>
            <AlertDialogDescription>
              {t('roles.confirmDeleteRole')}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t('common.cancel')}</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteRole} className="bg-red-600 hover:bg-red-700">
              {t('roles.deleteRole')}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
};
