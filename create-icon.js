const { createCanvas } = require('canvas');
const fs = require('fs');
const path = require('path');
const Jimp = require('jimp');

// Function to create a new website app icon
async function createWebsiteAppIcon() {
  // Create a canvas for the icon
  const size = 512;
  const canvas = createCanvas(size, size);
  const ctx = canvas.getContext('2d');

  // Set background to a gradient (violet theme)
  const gradient = ctx.createLinearGradient(0, 0, size, size);
  gradient.addColorStop(0, '#7c3aed'); // Violet color
  gradient.addColorStop(1, '#4c1d95'); // Darker violet
  ctx.fillStyle = gradient;
  ctx.fillRect(0, 0, size, size);

  // Draw a simple 'V' in the center
  ctx.fillStyle = 'white';
  ctx.font = 'bold 300px Arial';
  ctx.textAlign = 'center';
  ctx.textBaseline = 'middle';
  ctx.fillText('V', size / 2, size / 2);

  // Save the file
  const buffer = canvas.toBuffer('image/png');
  fs.writeFileSync(path.join(__dirname, 'images', 'icon.png'), buffer);
  console.log('New app icon created successfully');
}

// Function to modify ibuddy1.png with "Violet SaaS" text using Node Canvas
async function modifyIBuddyImageWithCanvas() {
  try {
    // Read the original image into a buffer
    const imagePath = path.join(__dirname, 'images', 'ibuddy1.png');
    const imageBuffer = fs.readFileSync(imagePath);
    
    // Convert buffer to Image for canvas
    const img = new (require('canvas')).Image();
    img.src = imageBuffer;
    
    // Create canvas with the same dimensions as the image
    const canvas = createCanvas(img.width, img.height);
    const ctx = canvas.getContext('2d');
    
    // Draw the original image on the canvas
    ctx.drawImage(img, 0, 0);
    
    // Add text to the image
    ctx.fillStyle = '#7c3aed'; // Violet color
    ctx.font = 'bold 32px Arial';
    ctx.fillText('Violet SaaS', 20, 40);
    
    // Save the modified image
    const outputBuffer = canvas.toBuffer('image/png');
    fs.writeFileSync(imagePath, outputBuffer);
    console.log('Modified ibuddy1.png successfully with Canvas');
  } catch (error) {
    console.error('Error modifying image with Canvas:', error);
  }
}

// Run both functions
async function main() {
  console.log('Starting image processing...');
  await createWebsiteAppIcon();
  await modifyIBuddyImageWithCanvas();
  console.log('All tasks completed');
}

main().catch(console.error); 