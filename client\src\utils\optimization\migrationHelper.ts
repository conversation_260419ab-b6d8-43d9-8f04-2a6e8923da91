/**
 * 迁移辅助工具
 * 帮助将旧的API调用迁移到新的优化系统
 */

// 需要更新的API调用模式
export const API_MIGRATION_PATTERNS = {
  // 旧的直接axios调用
  OLD_AXIOS: [
    'axios.get(',
    'axios.post(',
    'axios.put(',
    'axios.delete('
  ],
  
  // 旧的API客户端调用
  OLD_API_CLIENT: [
    'apiClient.get(',
    'apiClient.post(',
    'apiClient.put(',
    'apiClient.delete('
  ],
  
  // 需要替换为useApi Hook的模式
  SHOULD_USE_HOOK: [
    'useState.*loading',
    'useState.*error',
    'useEffect.*fetch',
    'useEffect.*api'
  ]
};

// 迁移建议
export const MIGRATION_SUGGESTIONS = {
  // API调用迁移
  API_CALLS: {
    description: '将直接的API调用迁移到新的缓存系统',
    examples: [
      {
        old: "axios.get('/api/users')",
        new: "apiCacheManager.get('/api/users')"
      },
      {
        old: "apiClient.get('/users')",
        new: "使用新的apiClient（已自动集成缓存）"
      }
    ]
  },
  
  // Hook迁移
  HOOKS: {
    description: '使用新的useApi Hook替代手动状态管理',
    examples: [
      {
        old: `
const [data, setData] = useState(null);
const [loading, setLoading] = useState(false);
const [error, setError] = useState(null);

useEffect(() => {
  setLoading(true);
  fetch('/api/data')
    .then(res => res.json())
    .then(setData)
    .catch(setError)
    .finally(() => setLoading(false));
}, []);`,
        new: `
const { data, loading, error } = useApi('/api/data');`
      }
    ]
  },
  
  // 状态管理迁移
  STATE_MANAGEMENT: {
    description: '使用高级状态管理工具',
    examples: [
      {
        old: "useState + useEffect + localStorage",
        new: "useAdvancedState with persistence config"
      },
      {
        old: "Manual debouncing with setTimeout",
        new: "useAdvancedState with debounce option"
      }
    ]
  }
};

/**
 * 检查文件是否需要迁移
 */
export function shouldMigrateFile(fileContent: string): {
  needsMigration: boolean;
  issues: Array<{
    type: 'api_call' | 'hook_pattern' | 'state_management';
    line: number;
    pattern: string;
    suggestion: string;
  }>;
} {
  const lines = fileContent.split('\n');
  const issues: Array<{
    type: 'api_call' | 'hook_pattern' | 'state_management';
    line: number;
    pattern: string;
    suggestion: string;
  }> = [];

  lines.forEach((line, index) => {
    const lineNumber = index + 1;
    
    // 检查旧的API调用模式
    API_MIGRATION_PATTERNS.OLD_AXIOS.forEach(pattern => {
      if (line.includes(pattern)) {
        issues.push({
          type: 'api_call',
          line: lineNumber,
          pattern: pattern,
          suggestion: 'Consider using apiCacheManager or new apiClient for automatic caching'
        });
      }
    });
    
    // 检查应该使用Hook的模式
    API_MIGRATION_PATTERNS.SHOULD_USE_HOOK.forEach(pattern => {
      const regex = new RegExp(pattern);
      if (regex.test(line)) {
        issues.push({
          type: 'hook_pattern',
          line: lineNumber,
          pattern: pattern,
          suggestion: 'Consider using useApi Hook for better performance and caching'
        });
      }
    });
  });

  return {
    needsMigration: issues.length > 0,
    issues
  };
}

/**
 * 生成迁移报告
 */
export function generateMigrationReport(files: Array<{ path: string; content: string }>): {
  summary: {
    totalFiles: number;
    filesToMigrate: number;
    totalIssues: number;
  };
  fileReports: Array<{
    path: string;
    needsMigration: boolean;
    issues: Array<{
      type: string;
      line: number;
      pattern: string;
      suggestion: string;
    }>;
  }>;
  recommendations: string[];
} {
  const fileReports = files.map(file => ({
    path: file.path,
    ...shouldMigrateFile(file.content)
  }));

  const filesToMigrate = fileReports.filter(report => report.needsMigration);
  const totalIssues = fileReports.reduce((sum, report) => sum + report.issues.length, 0);

  // 生成建议
  const recommendations = [
    '1. 优先迁移高频使用的API调用到新的缓存系统',
    '2. 将重复的useState + useEffect模式替换为useApi Hook',
    '3. 考虑使用useAdvancedState来处理复杂的状态管理需求',
    '4. 对于实时数据，确保使用正确的缓存策略（noCache）',
    '5. 测试迁移后的性能改进'
  ];

  return {
    summary: {
      totalFiles: files.length,
      filesToMigrate: filesToMigrate.length,
      totalIssues
    },
    fileReports,
    recommendations
  };
}

/**
 * 自动化迁移助手
 */
export const migrationHelper = {
  /**
   * 替换简单的API调用
   */
  replaceApiCalls: (content: string): string => {
    let updatedContent = content;
    
    // 替换axios直接调用
    updatedContent = updatedContent.replace(
      /axios\.(get|post|put|delete)\(/g,
      'apiCacheManager.$1('
    );
    
    return updatedContent;
  },

  /**
   * 建议Hook迁移
   */
  suggestHookMigration: (content: string): Array<{
    type: 'warning' | 'suggestion';
    message: string;
    location?: { line: number; column: number };
  }> => {
    const suggestions = [];
    
    // 检查常见的手动状态管理模式
    if (content.includes('useState') && content.includes('useEffect')) {
      suggestions.push({
        type: 'suggestion' as const,
        message: 'Consider using useApi Hook for better performance and caching'
      });
    }
    
    // 检查多个API调用
    const apiCallMatches = content.match(/axios\.(get|post|put|delete)/g);
    if (apiCallMatches && apiCallMatches.length > 3) {
      suggestions.push({
        type: 'warning' as const,
        message: 'Consider implementing request deduplication or using useMultipleApi for batch requests'
      });
    }
    
    // 检查复杂的状态管理
    const stateMatches = content.match(/useState/g);
    if (stateMatches && stateMatches.length > 5) {
      suggestions.push({
        type: 'suggestion' as const,
        message: 'Consider using useAdvancedState with batching or persistence options'
      });
    }
    
    // 检查性能敏感的操作
    if (content.includes('useEffect') && content.includes('setInterval')) {
      suggestions.push({
        type: 'warning' as const,
        message: 'Consider using performance monitoring hooks to track impact'
      });
    }
    
    return suggestions;
  }
};

export default migrationHelper;