import React from 'react'
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  MapPin, 
  Clock, 
  User, 
  MessageSquare,
  ArrowRight,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Eye
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface UserJourneyMapProps {
  agentId?: string;
  timeRange?: string;
  className?: string
}

const UserJourneyMap: React.FC<UserJourneyMapProps> = ({
  agentId, timeRange, className
}) => {
  // 模拟用户旅程数据
  const journeyData = [
    {
      id: 'journey-1',
      userId: 'user-123',
      sessionId: 'session-456',
      startTime: '2024-01-20T10:00:00Z',
      endTime: '2024-01-20T10:15:00Z',
      duration: 900, // 15 minutes
      outcome: 'completed' as const,
      satisfaction: 4.5,
      touchpoints: [
        { id: '1', timestamp: '2024-01-20T10:00:00Z', type: 'message' as const, channel: 'web', content: 'Hello, I need help with my order', success: true },
        { id: '2', timestamp: '2024-01-20T10:01:00Z', type: 'action' as const, channel: 'web', content: 'Order lookup initiated', success: true },
        { id: '3', timestamp: '2024-01-20T10:02:00Z', type: 'message' as const, channel: 'web', content: 'Found your order #12345', success: true },
        { id: '4', timestamp: '2024-01-20T10:05:00Z', type: 'action' as const, channel: 'web', content: 'Refund processed', success: true },
        { id: '5', timestamp: '2024-01-20T10:15:00Z', type: 'message' as const, channel: 'web', content: 'Issue resolved successfully', success: true },
      ],
      tags: ['order-issue', 'refund', 'resolved'],
    },
    {
      id: 'journey-2',
      userId: 'user-789',
      sessionId: 'session-101',
      startTime: '2024-01-20T11:30:00Z',
      endTime: '2024-01-20T11:45:00Z',
      duration: 900,
      outcome: 'escalated' as const,
      satisfaction: 2.0,
      touchpoints: [
        { id: '1', timestamp: '2024-01-20T11:30:00Z', type: 'message' as const, channel: 'web', content: 'I have a complex billing question', success: true },
        { id: '2', timestamp: '2024-01-20T11:32:00Z', type: 'message' as const, channel: 'web', content: 'Let me help you with that', success: true },
        { id: '3', timestamp: '2024-01-20T11:35:00Z', type: 'action' as const, channel: 'web', content: 'AI unable to resolve', success: false },
        { id: '4', timestamp: '2024-01-20T11:40:00Z', type: 'handoff' as const, channel: 'web', content: 'Transferred to human agent', success: true },
        { id: '5', timestamp: '2024-01-20T11:45:00Z', type: 'message' as const, channel: 'web', content: 'Human agent taking over', success: true },
      ],
      tags: ['billing', 'complex', 'escalated'],
    },
    {
      id: 'journey-3',
      userId: 'user-456',
      sessionId: 'session-789',
      startTime: '2024-01-20T14:00:00Z',
      endTime: null,
      duration: null,
      outcome: 'abandoned' as const,
      satisfaction: null,
      touchpoints: [
        { id: '1', timestamp: '2024-01-20T14:00:00Z', type: 'message' as const, channel: 'web', content: 'Hi there', success: true },
        { id: '2', timestamp: '2024-01-20T14:01:00Z', type: 'message' as const, channel: 'web', content: 'How can I help you today?', success: true },
        { id: '3', timestamp: '2024-01-20T14:05:00Z', type: 'event' as const, channel: 'web', content: 'User inactive for 5 minutes', success: false },
      ],
      tags: ['abandoned', 'no-response'],
    },
  ];

  const getOutcomeIcon = (outcome: string) => {
    switch (outcome) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'escalated':
        return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
      case 'abandoned':
        return <XCircle className="h-4 w-4 text-red-600" />;
      default:
        return <Clock className="h-4 w-4 text-blue-600" />;
    }
  };

  const getOutcomeColor = (outcome: string) => {
    switch (outcome) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'escalated':
        return 'bg-yellow-100 text-yellow-800';
      case 'abandoned':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-blue-100 text-blue-800';
    }
  };

  const getTouchpointIcon = (type: string) => {
    switch (type) {
      case 'message':
        return <MessageSquare className="h-3 w-3" />;
      case 'action':
        return <MapPin className="h-3 w-3" />;
      case 'handoff':
        return <User className="h-3 w-3" />;
      case 'event':
        return <Clock className="h-3 w-3" />;
      default:
        return <MapPin className="h-3 w-3" />;
    }
  };

  const formatDuration = (seconds: number | null) => {
    if (!seconds) return 'Ongoing';
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  return (
    <div className={cn("space-y-6", className)}>
      {/* 旅程概览 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2 mb-2">
              <User className="h-4 w-4 text-blue-600" />
              <span className="text-sm font-medium">Total Journeys</span>
            </div>
            <div className="text-2xl font-bold">{journeyData.length}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2 mb-2">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium">Completed</span>
            </div>
            <div className="text-2xl font-bold text-green-600">
              {journeyData.filter(j => j.outcome === 'completed').length}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2 mb-2">
              <AlertTriangle className="h-4 w-4 text-yellow-600" />
              <span className="text-sm font-medium">Escalated</span>
            </div>
            <div className="text-2xl font-bold text-yellow-600">
              {journeyData.filter(j => j.outcome === 'escalated').length}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2 mb-2">
              <XCircle className="h-4 w-4 text-red-600" />
              <span className="text-sm font-medium">Abandoned</span>
            </div>
            <div className="text-2xl font-bold text-red-600">
              {journeyData.filter(j => j.outcome === 'abandoned').length}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 用户旅程列表 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MapPin className="h-5 w-5 text-primary" />
            User Journey Map
          </CardTitle>
          <CardDescription>
            Detailed view of individual user interaction paths
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {journeyData.map((journey) => (
              <div key={journey.id} className="border rounded-lg p-4">
                {/* 旅程头部 */}
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 rounded-full bg-primary/10 text-primary flex items-center justify-center text-sm font-medium">
                      {journey.userId.slice(-3)}
                    </div>
                    <div>
                      <h4 className="font-medium">User Journey #{journey.id.slice(-3)}</h4>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <span>Started: {formatTime(journey.startTime)}</span>
                        <span>•</span>
                        <span>Duration: {formatDuration(journey.duration)}</span>
                        {journey.satisfaction && (
                          <>
                            <span>•</span>
                            <span>Rating: {journey.satisfaction}/5</span>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge className={getOutcomeColor(journey.outcome)} variant="secondary">
                      {getOutcomeIcon(journey.outcome)}
                      <span className="ml-1 capitalize">{journey.outcome}</span>
                    </Badge>
                    <Button variant="ghost" size="sm">
                      <Eye className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                {/* 触点时间线 */}
                <div className="space-y-3">
                  {journey.touchpoints.map((touchpoint, index) => (
                    <div key={touchpoint.id} className="flex items-start gap-3">
                      <div className="flex flex-col items-center">
                        <div className={cn(
                          "w-6 h-6 rounded-full flex items-center justify-center text-white text-xs",
                          touchpoint.success ? "bg-green-500" : "bg-red-500"
                        )}>
                          {getTouchpointIcon(touchpoint.type)}
                        </div>
                        {index < journey.touchpoints.length - 1 && (
                          <div className="w-px h-6 bg-border mt-1" />
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <span className="text-sm font-medium capitalize">{touchpoint.type}</span>
                          <span className="text-xs text-muted-foreground">
                            {formatTime(touchpoint.timestamp)}
                          </span>
                          <Badge variant="outline" className="text-xs">
                            {touchpoint.channel}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground">{touchpoint.content}</p>
                      </div>
                      {index < journey.touchpoints.length - 1 && (
                        <ArrowRight className="h-4 w-4 text-muted-foreground mt-1" />
                      )}
                    </div>
                  ))}
                </div>

                {/* 标签 */}
                <div className="flex items-center gap-2 mt-4 pt-3 border-t">
                  <span className="text-xs text-muted-foreground">Tags:</span>
                  {journey.tags.map((tag) => (
                    <Badge key={tag} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* 旅程洞察 */}
      <Card>
        <CardHeader>
          <CardTitle>Journey Insights</CardTitle>
          <CardDescription>Key patterns and optimization opportunities</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="p-4 bg-green-50 rounded-lg border border-green-200">
              <div className="flex items-start gap-3">
                <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                <div>
                  <h4 className="font-medium text-green-900">High Success Rate</h4>
                  <p className="text-sm text-green-700 mt-1">
                    33% of journeys are completed successfully with high satisfaction scores. 
                    Order-related queries show particularly strong resolution rates.
                  </p>
                </div>
              </div>
            </div>
            
            <div className="p-4 bg-yellow-50 rounded-lg border border-yellow-200">
              <div className="flex items-start gap-3">
                <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
                <div>
                  <h4 className="font-medium text-yellow-900">Escalation Pattern</h4>
                  <p className="text-sm text-yellow-700 mt-1">
                    Complex billing questions frequently require human intervention. 
                    Consider adding specialized billing AI training or quick escalation paths.
                  </p>
                </div>
              </div>
            </div>
            
            <div className="p-4 bg-red-50 rounded-lg border border-red-200">
              <div className="flex items-start gap-3">
                <XCircle className="h-5 w-5 text-red-600 mt-0.5" />
                <div>
                  <h4 className="font-medium text-red-900">Abandonment Risk</h4>
                  <p className="text-sm text-red-700 mt-1">
                    Users who don't respond within 5 minutes tend to abandon the conversation. 
                    Implement proactive engagement strategies to reduce abandonment.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
};

export default UserJourneyMap;
