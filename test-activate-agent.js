const axios = require('axios');

console.log('🧪 测试Agent激活功能...');

// 使用一个已存在的agent ID (从前面的查询中获取)
const agentId = 'e41595bf-4f41-4b14-8efd-3543abb17c58'; // Ong agent
const newStatus = 'ACTIVE';

console.log(`📝 准备激活Agent: ${agentId} -> ${newStatus}`);

// 测试直接连接Core Service
axios.patch(`http://localhost:3002/api/agents/${agentId}/status`, { status: newStatus }, {
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})
.then(response => {
  console.log('✅ Core Service状态更新成功:');
  console.log('状态码:', response.status);
  console.log('响应数据:', response.data);
})
.catch(error => {
  console.error('❌ Core Service状态更新失败:');
  if (error.response) {
    console.error('状态码:', error.response.status);
    console.error('响应数据:', error.response.data);
  } else if (error.request) {
    console.error('请求错误:', error.request);
  } else {
    console.error('配置错误:', error.message);
  }
  console.error('完整错误:', error.message);
}); 