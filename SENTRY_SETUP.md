# 🔍 Sentry 错误监控设置指南

## 概述

Sentry.io 已成功集成到 ibuddy2 项目中，为前端和后端提供全面的错误监控和性能追踪。

## 🚀 安装完成

### 前端 (React)
- ✅ `@sentry/react` - React 错误监控
- ✅ `@sentry/tracing` - 性能监控

### 后端 (Node.js)
- ✅ `@sentry/node` - Node.js 错误监控
- ✅ `@sentry/tracing` - 性能监控

## 📋 配置步骤

### 1. 获取 Sentry DSN

1. 访问 [Sentry.io](https://sentry.io) 并创建账户
2. 创建新项目：
   - **前端项目**：选择 "React" 平台
   - **后端项目**：选择 "Node.js" 平台
3. 复制项目的 DSN（Data Source Name）

### 2. 环境变量配置

在项目根目录创建 `.env` 文件：

```env
# Sentry Configuration
REACT_APP_SENTRY_DSN=your_frontend_sentry_dsn_here
SENTRY_DSN=your_backend_sentry_dsn_here
REACT_APP_VERSION=1.0.0
APP_VERSION=1.0.0

# API Configuration
REACT_APP_API_BASE_URL=http://localhost:3001
REACT_APP_API_TIMEOUT=10000

# Feature Flags
REACT_APP_ENABLE_ANALYTICS=true
REACT_APP_ENABLE_PERFORMANCE_MONITORING=true

# Environment
NODE_ENV=development
```

## 🎯 功能特性

### 前端功能
- ✅ 自动错误捕获
- ✅ React 错误边界
- ✅ 性能监控
- ✅ 用户操作追踪
- ✅ API 调用监控
- ✅ 面包屑记录
- ✅ 用户上下文设置

### 后端功能
- ✅ Express 中间件集成
- ✅ 自动错误捕获
- ✅ 性能监控
- ✅ API 请求追踪
- ✅ 未捕获异常处理
- ✅ 错误过滤和采样

## 📝 使用方法

### 前端使用

#### 基本使用
```typescript
import { useSentry } from './hooks/useSentry';

function MyComponent() {
  const { captureError, logUserAction } = useSentry();

  const handleButtonClick = () => {
    try {
      logUserAction('button_click', { button: 'submit' });
      // 你的代码逻辑
    } catch (error) {
      captureError(error as Error, { component: 'MyComponent' });
    }
  };

  return <button onClick={handleButtonClick}>提交</button>;
}
```

#### 错误边界
```typescript
import ErrorBoundary from './components/common/ErrorBoundary';

function App() {
  return (
    <ErrorBoundary fallback={<div>出错了！</div>}>
      <MyComponent />
    </ErrorBoundary>
  );
}
```

#### 性能监控
```typescript
import { usePerformanceMonitor } from './hooks/useSentry';

function ApiComponent() {
  const { trackApiCall } = usePerformanceMonitor();

  const fetchData = async () => {
    const start = Date.now();
    try {
      const response = await fetch('/api/data');
      const duration = Date.now() - start;
      trackApiCall('fetch_data', duration, response.ok);
      return response.json();
    } catch (error) {
      const duration = Date.now() - start;
      trackApiCall('fetch_data', duration, false);
      throw error;
    }
  };
}
```

### 后端使用

#### 手动错误捕获
```javascript
const { captureError, addBreadcrumb } = require('./config/sentry');

app.get('/api/example', async (req, res) => {
  try {
    addBreadcrumb('API call started', 'api', 'info');
    
    // 你的业务逻辑
    const result = await someAsyncOperation();
    
    res.json(result);
  } catch (error) {
    captureError(error, {
      endpoint: '/api/example',
      userId: req.user?.id,
      requestId: req.id
    });
    
    res.status(500).json({ error: 'Internal server error' });
  }
});
```

## 📊 监控面板

### 示例组件
项目中包含一个示例组件 `SentryExample.tsx`，演示所有功能：

```typescript
import SentryExample from './components/examples/SentryExample';

// 在你的路由中使用
<Route path="/sentry-demo" component={SentryExample} />
```

### 功能演示
- 模拟 API 调用（成功/失败）
- 手动触发错误
- 用户登录模拟
- 发送自定义消息
- 错误边界测试

## 🔧 配置选项

### 前端配置 (`client/src/lib/sentry.ts`)
- 环境检测
- 错误过滤
- 性能采样
- 用户上下文
- 面包屑记录

### 后端配置 (`ai-service/src/config/sentry.js`)
- Express 中间件
- 错误过滤
- 性能监控
- 请求追踪
- 事务管理

## 🛠️ 最佳实践

### 1. 不要记录敏感信息
```typescript
// ❌ 错误做法
captureError(error, { password: user.password });

// ✅ 正确做法
captureError(error, { userId: user.id, email: user.email });
```

### 2. 设置用户上下文
```typescript
// 用户登录后
setUser({
  id: user.id,
  email: user.email,
  username: user.username
});
```

### 3. 合理使用采样率
```javascript
// 生产环境建议设置较低的采样率
tracesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0
```

### 4. 添加有意义的标签
```typescript
addBreadcrumb('User clicked checkout button', 'user', 'info');
```

## 🚨 故障排除

### 常见问题

1. **Sentry 没有接收到错误**
   - 检查 DSN 是否正确配置
   - 确认环境变量正确加载
   - 检查网络连接

2. **开发环境看不到错误**
   - 检查浏览器控制台
   - 确认 `debug: true` 设置
   - 查看 Sentry 控制台

3. **性能监控不工作**
   - 确认 `tracesSampleRate` 设置
   - 检查 BrowserTracing 集成
   - 验证 API 调用追踪

### 调试命令

```bash
# 前端
cd client
npm run build  # 检查构建错误

# 后端
cd ai-service
npm start      # 检查服务启动
```

## 📈 监控指标

### 前端监控
- JavaScript 错误
- 网络请求失败
- 组件渲染性能
- 用户交互追踪

### 后端监控
- API 响应时间
- 数据库查询性能
- 服务器错误
- 资源使用情况

## 🔗 相关文件

### 前端文件
- `client/src/lib/sentry.ts` - Sentry 配置
- `client/src/hooks/useSentry.ts` - React Hooks
- `client/src/components/common/ErrorBoundary.tsx` - 错误边界
- `client/src/components/examples/SentryExample.tsx` - 示例组件

### 后端文件
- `ai-service/src/config/sentry.js` - Sentry 配置
- `ai-service/src/index.js` - 主入口文件（已集成）

## 📚 更多资源

- [Sentry React 文档](https://docs.sentry.io/platforms/javascript/guides/react/)
- [Sentry Node.js 文档](https://docs.sentry.io/platforms/node/)
- [性能监控指南](https://docs.sentry.io/product/performance/)
- [错误处理最佳实践](https://docs.sentry.io/product/error-monitoring/)

---

🎉 **Sentry 已成功集成到 ibuddy2 项目中！** 现在你可以实时监控应用程序的错误和性能，提升用户体验和系统稳定性。 