/**
 * 设计系统统一入口
 * 导出所有设计tokens、组件和工具
 */

// 设计tokens
export * from './ultimate-design-tokens';
export * from './enhanced-motion-system';

// 从tokens目录导出所有token定义
export * from './tokens';

// 类型定义
export type {
  DesignTokens,
  ThemeMode,
  ColorScale,
  SemanticColors,
  BackgroundColors,
  TextColors,
  BorderColors,
  SpacingScale,
  ComponentSpacing,
  BorderRadiusScale,
  ShadowScale,
  FontFamily,
  FontSize,
  FontWeight,
  LineHeight,
  AnimationDuration,
  AnimationEasing,
  SpringConfig,
  MicroInteraction,
  Breakpoints,
  ThemeConfig,
  CSSVariables,
  DesignSystemContext
} from '../types/design-system';

// 导出设计token实例
export { ultimateDesignTokens } from './ultimate-design-tokens';

// 工具函数 - 创建CSS变量
export const createCSSVariables = (tokens: any): Record<string, string> => {
  const variables: Record<string, string> = {};
  
  // 颜色变量
  if (tokens.colors?.primary) {
    Object.entries(tokens.colors.primary).forEach(([key, value]) => {
      variables[`--color-primary-${key}`] = value as string;
    });
  }
  
  if (tokens.colors?.background) {
    Object.entries(tokens.colors.background).forEach(([key, value]) => {
      variables[`--color-background-${key}`] = value as string;
    });
  }
  
  if (tokens.colors?.text) {
    Object.entries(tokens.colors.text).forEach(([key, value]) => {
      variables[`--color-text-${key}`] = value as string;
    });
  }
  
  if (tokens.colors?.border) {
    Object.entries(tokens.colors.border).forEach(([key, value]) => {
      variables[`--color-border-${key}`] = value as string;
    });
  }
  
  // 间距变量
  if (tokens.spacing) {
    Object.entries(tokens.spacing).forEach(([key, value]) => {
      if (typeof value === 'string') {
        variables[`--spacing-${key}`] = value;
      }
    });
  }
  
  // 圆角变量
  if (tokens.borderRadius) {
    Object.entries(tokens.borderRadius).forEach(([key, value]) => {
      variables[`--border-radius-${key}`] = value as string;
    });
  }
  
  // 阴影变量
  if (tokens.shadows) {
    Object.entries(tokens.shadows).forEach(([key, value]) => {
      variables[`--shadow-${key}`] = value as string;
    });
  }
  
  // 动画变量
  if (tokens.animation?.duration) {
    Object.entries(tokens.animation.duration).forEach(([key, value]) => {
      variables[`--animation-duration-${key}`] = value as string;
    });
  }
  
  if (tokens.animation?.easing) {
    Object.entries(tokens.animation.easing).forEach(([key, value]) => {
      variables[`--animation-easing-${key}`] = value as string;
    });
  }
  
  // 字体变量
  if (tokens.typography?.fontFamily) {
    Object.entries(tokens.typography.fontFamily).forEach(([key, value]) => {
      if (Array.isArray(value)) {
        variables[`--font-family-${key}`] = value.join(', ');
      }
    });
  }
  
  // 断点变量
  if (tokens.breakpoints) {
    Object.entries(tokens.breakpoints).forEach(([key, value]) => {
      variables[`--breakpoint-${key}`] = value as string;
    });
  }
  
  return variables;
};

// 应用CSS变量到文档
export const applyCSSVariables = (variables: Record<string, string>, target?: HTMLElement) => {
  const element = target || document.documentElement;
  
  Object.entries(variables).forEach(([property, value]) => {
    element.style.setProperty(property, value);
  });
};

// 获取CSS变量值
export const getCSSVariable = (property: string, element?: HTMLElement): string => {
  const target = element || document.documentElement;
  return getComputedStyle(target).getPropertyValue(property).trim();
};

// 主题切换工具
export const createThemeUtils = (lightTokens: any, darkTokens: any) => {
  return {
    applyTheme: (mode: 'light' | 'dark') => {
      const tokens = mode === 'light' ? lightTokens : darkTokens;
      const variables = createCSSVariables(tokens);
      applyCSSVariables(variables);
      
      // 添加主题类名
      document.documentElement.classList.remove('light', 'dark');
      document.documentElement.classList.add(mode);
    },
    
    toggleTheme: () => {
      const currentTheme = document.documentElement.classList.contains('dark') ? 'dark' : 'light';
      const newTheme = currentTheme === 'light' ? 'dark' : 'light';
      return newTheme;
    },
    
    getCurrentTheme: (): 'light' | 'dark' => {
      return document.documentElement.classList.contains('dark') ? 'dark' : 'light';
    }
  };
}; 