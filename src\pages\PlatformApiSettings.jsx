import React, { useState, useEffect, useContext } from 'react';
import { Container, Row, Col, Form, Button, Card, Alert, Spinner, InputGroup, ListGroup, Badge } from 'react-bootstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faKey, faSave, faCopy, faEye, faEyeSlash, faTrash, faPlus, faQuestionCircle, faExternalLinkAlt, faCheckCircle, faTimesCircle } from '@fortawesome/free-solid-svg-icons';
import { FaFacebook, FaInstagram, FaWhatsapp, FaTelegram, FaDiscord } from 'react-icons/fa';

const PlatformApiSettings = () => {
  const [loading, setLoading] = useState(false);
  const [alert, setAlert] = useState({ show: false, message: '', variant: '' });
  const [showPasswords, setShowPasswords] = useState({});
  const [platforms, setPlatforms] = useState({
    facebook: { enabled: false, appId: '', appSecret: '', accessToken: '' },
    instagram: { enabled: false, accessToken: '', businessAccountId: '' },
    whatsapp: { enabled: false, phoneNumberId: '', accessToken: '', webhookToken: '' },
    telegram: { enabled: false, botToken: '', webhookUrl: '' },
    discord: { enabled: false, botToken: '', guildId: '' }
  });

  const handlePlatformChange = (platform, field, value) => {
    setPlatforms(prev => ({
      ...prev,
      [platform]: {
        ...prev[platform],
        [field]: value
      }
    }));
  };

  const togglePasswordVisibility = (platform, field) => {
    setShowPasswords(prev => ({
      ...prev,
      [`${platform}_${field}`]: !prev[`${platform}_${field}`]
    }));
  };

  const handleSave = (platform) => {
    console.log('Saving platform settings:', platform, platforms[platform]);
    setAlert({
      show: true,
      message: `${platform} settings saved successfully`,
      variant: 'success'
    });
  };

  const handleConnectWhatsApp = () => {
    console.log("WhatsApp Connect Clicked");
    // Placeholder for actual WhatsApp connection logic
  };

  const renderPlatformCard = (platform, config) => {
    const platformIcons = {
      facebook: FaFacebook,
      instagram: FaInstagram,
      whatsapp: FaWhatsapp,
      telegram: FaTelegram,
      discord: FaDiscord
    };

    const PlatformIcon = platformIcons[platform];

    return (
      <Card key={platform} className="mb-4">
        <Card.Header className="d-flex justify-content-between align-items-center">
          <div className="d-flex align-items-center">
            <PlatformIcon className="me-2" size={24} />
            <h5 className="mb-0 text-capitalize">{platform}</h5>
          </div>
          <Form.Check
            type="switch"
            checked={config.enabled}
            onChange={(e) => handlePlatformChange(platform, 'enabled', e.target.checked)}
          />
        </Card.Header>
        {config.enabled && (
          <Card.Body>
            {platform === 'facebook' && (
              <>
                <Form.Group className="mb-3">
                  <Form.Label>App ID</Form.Label>
                  <Form.Control
                    type="text"
                    value={config.appId || ''}
                    onChange={(e) => handlePlatformChange(platform, 'appId', e.target.value)}
                    placeholder="Enter Facebook App ID"
                  />
                </Form.Group>
                <Form.Group className="mb-3">
                  <Form.Label>App Secret</Form.Label>
                  <InputGroup>
                    <Form.Control
                      type={showPasswords[`${platform}_appSecret`] ? "text" : "password"}
                      value={config.appSecret || ''}
                      onChange={(e) => handlePlatformChange(platform, 'appSecret', e.target.value)}
                      placeholder="Enter Facebook App Secret"
                    />
                    <Button
                      variant="outline-secondary"
                      onClick={() => togglePasswordVisibility(platform, 'appSecret')}
                    >
                      <FontAwesomeIcon icon={showPasswords[`${platform}_appSecret`] ? faEyeSlash : faEye} />
                    </Button>
                  </InputGroup>
                </Form.Group>
              </>
            )}

            <Button
              variant="success"
              onClick={() => handleSave(platform)}
              disabled={loading}
            >
              {loading ? (
                <Spinner animation="border" size="sm" className="me-2" />
              ) : (
                <FontAwesomeIcon icon={faSave} className="me-2" />
              )}
              Save Settings
            </Button>
          </Card.Body>
        )}
      </Card>
    );
  };

  return (
    <Container fluid className="py-4">
      <Row>
        <Col>
          <h2 className="mb-4">
            <FontAwesomeIcon icon={faKey} className="me-2" />
            Platform API Settings
          </h2>
          
          {alert.show && (
            <Alert
              variant={alert.variant}
              onClose={() => setAlert({ show: false, message: '', variant: '' })}
              dismissible
            >
              {alert.message}
            </Alert>
          )}

          <Row>
            <Col lg={8}>
              {Object.entries(platforms).map(([platform, config]) =>
                renderPlatformCard(platform, config)
              )}
            </Col>
          </Row>
        </Col>
      </Row>
    </Container>
  );
};

export default PlatformApiSettings; 