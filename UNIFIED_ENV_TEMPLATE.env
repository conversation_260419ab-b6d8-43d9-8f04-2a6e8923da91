# =================================
# ibuddy2 统一环境配置
# =================================

# 应用基础配置
NODE_ENV=development
APP_VERSION=1.0.0

# =================================
# Sentry 错误监控配置
# =================================
# 获取方式: 访问 https://sentry.io，创建项目获取 DSN
REACT_APP_SENTRY_DSN=https://<EMAIL>/project_id
SENTRY_DSN=https://<EMAIL>/project_id

# =================================
# 服务端口配置 (避免端口冲突)
# =================================
API_GATEWAY_PORT=3001
CORE_SERVICE_PORT=3002
AI_SERVICE_PORT=3003
CLIENT_PORT=3000
SERVER_PORT=3004

# =================================
# AI 服务配置 (修复 AI 初始化错误)
# =================================
# OpenAI 配置
OPENAI_API_KEY=sk-your-openai-api-key-here
OPENAI_MODEL=gpt-4o-mini
OPENAI_MAX_TOKENS=2048

# Google AI 配置
GEMINI_API_KEY=your-gemini-api-key-here
GOOGLE_APPLICATION_CREDENTIALS=path/to/your/google-credentials.json
GOOGLE_PROJECT_ID=your-google-project-id

# OpenRouter 配置
OPENROUTER_API_KEY=your-openrouter-api-key-here

# AI 模型配置
DEFAULT_AI_MODEL=gpt-4o-mini
ENABLE_MODEL_FALLBACK=true
ENABLE_TEST_RESPONSES=true

# =================================
# 数据库配置
# =================================
# Supabase 配置
SUPABASE_URL=your-supabase-url
SUPABASE_SERVICE_KEY=your-supabase-service-key
SUPABASE_ANON_KEY=your-supabase-anon-key

# PostgreSQL 配置 (备选)
DATABASE_URL=postgresql://username:password@localhost:5432/ibuddy2

# =================================
# 缓存和队列配置
# =================================
# Redis 配置
REDIS_URL=redis://localhost:6379
REDIS_TTL=3600
ENABLE_REDIS_CACHE=false

# 消息队列配置
ENABLE_MESSAGE_QUEUE=false
RABBITMQ_URL=amqp://localhost:5672

# =================================
# 安全配置
# =================================
JWT_SECRET=your-super-secure-jwt-secret-key-here
ENCRYPTION_KEY=your-32-character-encryption-key-here
CORS_ORIGIN=http://localhost:3000

# =================================
# 第三方集成配置
# =================================
# Meta/Facebook 配置
FACEBOOK_APP_ID=your_facebook_app_id
FACEBOOK_APP_SECRET=your_facebook_app_secret
WHATSAPP_ACCESS_TOKEN=your_whatsapp_access_token
WHATSAPP_PHONE_NUMBER_ID=your_whatsapp_phone_number_id

# Instagram 配置
INSTAGRAM_APP_ID=your_instagram_app_id
INSTAGRAM_APP_SECRET=your_instagram_app_secret

# TikTok 配置
TIKTOK_CLIENT_KEY=your_tiktok_client_key
TIKTOK_CLIENT_SECRET=your_tiktok_client_secret

# Webhook 配置
WEBHOOK_VERIFY_TOKEN=your_webhook_verify_token
WEBHOOK_SECRET=your_webhook_secret

# =================================
# 性能和监控配置
# =================================
# 日志配置
LOG_LEVEL=info
ENABLE_PERFORMANCE_MONITORING=true
ENABLE_ANALYTICS=true

# API 限制配置
RATE_LIMIT_MAX=100
RATE_LIMIT_WINDOW_MS=60000
API_REQUEST_TIMEOUT=15000

# 文件上传配置
MAX_FILE_SIZE=10MB
ALLOWED_FILE_TYPES=pdf,doc,docx,txt,png,jpg,jpeg,mp3,wav

# =================================
# 开发和调试配置
# =================================
DEBUG=false
ENABLE_DEBUG_LOGS=false
MOCK_API_RESPONSES=false 