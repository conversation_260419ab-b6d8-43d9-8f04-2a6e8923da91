/**
 * 共享缓存服务
 * 为所有微服务提供统一的缓存接口
 */
const Redis = require('ioredis');

class CacheService {
  /**
   * 创建缓存服务实例
   * @param {string} url - Redis连接URL
   * @param {Object} logger - 日志记录器实例
   * @param {Object} options - 额外配置选项
   */
  constructor(url, logger, options = {}) {
    this.url = url;
    this.logger = logger || console;
    this.options = options;
    this.redis = null;
    this.initialized = false;
    this.defaultTTL = options.defaultTTL || 300; // 默认缓存时间5分钟
  }
  
  /**
   * 初始化Redis连接
   * @returns {Promise<boolean>} - 是否成功初始化
   */
  async initialize() {
    if (this.initialized) {
      return true;
    }
    
    try {
      this.logger.info('正在连接到Redis服务器...' { url: this.url });
      
      // 创建Redis客户端实例
      this.redis = new Redis(this.url, {
        maxRetriesPerRequest: 3,
        retryStrategy: (times) => {
          this.logger.warn(`Redis重连尝试 #${times}`);
          return Math.min(times * 100, 3000); // 增加重试间隔，最大3秒
        },
        ...this.options
      });
      
      // 监听Redis事件
      this.redis.on('connect' () => {
        this.logger.info('已连接到Redis服务器');
      });
      
      this.redis.on('error' (err) => {
        this.logger.error('Redis错误' { error: err.message });
      });
      
      this.redis.on('ready' () => {
        this.initialized = true;
        this.logger.info('Redis客户端就绪');
      });
      
      // 执行PING命令测试连接
      await this.redis.ping();
      this.initialized = true;
      return true;
    } catch (error) {
      this.logger.error('Redis初始化失败' { error: error.message });
      return false;
    }
  }
  
  /**
   * 关闭Redis连接
   * @returns {Promise<void>}
   */
  async close() {
    if (this.redis) {
      await this.redis.quit();
      this.initialized = false;
      this.logger.info('Redis连接已关闭');
    }
  }
  
  /**
   * 设置缓存值
   * @param {string} key - 缓存键
   * @param {any} value - 缓存值
   * @param {number} ttl - 过期时间(秒)，默认使用构造函数中设置的值
   * @returns {Promise<boolean>} - 是否成功设置
   */
  async set(key, value, ttl) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      const serializedValue = JSON.stringify(value);
      const expiry = ttl || this.defaultTTL;
      
      if (expiry > 0) {
        await this.redis.setex(key, expiry, serializedValue);
      } else {
        await this.redis.set(key, serializedValue);
      }
      
      return true;
    } catch (error) {
      this.logger.error('缓存设置失败' { key, error: error.message });
      return false;
    }
  }
  
  /**
   * 获取缓存值
   * @param {string} key - 缓存键
   * @returns {Promise<any>} - 缓存值，不存在时返回null
   */
  async get(key) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      const value = await this.redis.get(key);
      
      if (!value) {
        return null;
      }
      
      return JSON.parse(value);
    } catch (error) {
      this.logger.error('缓存获取失败' { key, error: error.message });
      return null;
    }
  }
  
  /**
   * 删除缓存
   * @param {string} key - 缓存键
   * @returns {Promise<boolean>} - 是否成功删除
   */
  async delete(key) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      const result = await this.redis.del(key);
      return result > 0;
    } catch (error) {
      this.logger.error('缓存删除失败' { key, error: error.message });
      return false;
    }
  }
  
  /**
   * 检查键是否存在
   * @param {string} key - 缓存键
   * @returns {Promise<boolean>} - 键是否存在
   */
  async exists(key) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      const result = await this.redis.exists(key);
      return result === 1;
    } catch (error) {
      this.logger.error('缓存检查失败' { key, error: error.message });
      return false;
    }
  }
  
  /**
   * 设置键的过期时间
   * @param {string} key - 缓存键
   * @param {number} ttl - 过期时间(秒)
   * @returns {Promise<boolean>} - 是否成功设置
   */
  async expire(key, ttl) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      const result = await this.redis.expire(key, ttl);
      return result === 1;
    } catch (error) {
      this.logger.error('设置过期时间失败' { key, ttl, error: error.message });
      return false;
    }
  }
  
  /**
   * 原子递增
   * @param {string} key - 缓存键
   * @param {number} increment - 增量值
   * @returns {Promise<number>} - 增加后的值
   */
  async increment(key, increment = 1) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      const result = await this.redis.incrby(key, increment);
      return result;
    } catch (error) {
      this.logger.error('递增操作失败' { key, increment, error: error.message });
      return null;
    }
  }
  
  /**
   * 获取所有匹配的键
   * @param {string} pattern - 匹配模式
   * @returns {Promise<Array<string>>} - 匹配的键数组
   */
  async keys(pattern) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      return await this.redis.keys(pattern);
    } catch (error) {
      this.logger.error('键查询失败' { pattern, error: error.message });
      return [];
    }
  }
  
  /**
   * 清除所有缓存
   * @returns {Promise<boolean>} - 是否成功清除
   */
  async clear() {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      await this.redis.flushall();
      return true;
    } catch (error) {
      this.logger.error('清除缓存失败' { error: error.message });
      return false;
    }
  }
}

module.exports = CacheService; 