/**
 * 模块服务
 * 使用缓存API客户端处理模块相关请求
 */
import { cachedRequest, ApiClientType, cacheManager } from '../utils/api/cachedApiClient';
import { supabase } from '@/lib/supabase';

// import api from './api' // No longer needed
// import { Module } from '@/types/module' // This line should be removed or commented out if present

// const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:3001' // No longer needed

// Helper function to handle API responses - No longer needed with supabase client, it handles errors

// 默认重试选项
/*
const defaultRetryOptions = {
  maxRetries: 3,
  initialRetryDelay: 1000,
  maxRetryDelay: 10000
};
*/

// 模块API重试选项 - 更宽松的设置
/*
const moduleRetryOptions = {
  maxRetries: 5,
  initialRetryDelay: 2000,
  maxRetryDelay: 30000
};
*/

// Redefine Module interface to match Supabase table structure
export interface Module {
  id: string; // Changed from moduleId to id (uuid);
  name: string; // Was moduleName;
  description: string; // Was description, now optional and can be null;
  category: 'core' | 'addon' | 'integration' // Was isEnabled;
  is_enabled: boolean; // Was isEnabled;
  version?: string | null;
  // lastUpdated is managed by updated_at in DB
  high_level_config: any; // Was highLevelConfig;
  // hasDetailedSettings can be inferred or managed differently if needed
  detailed_settings_schema: any; // New field;
  detailed_settings_values: any; // New field;
  created_at: string; // TIMESTAMPTZ;
  updated_at: string; // TIMESTAMPTZ;
  created_by: string; // UUID;
  icon_url?: string | null;
  sort_order?: number | null;
  // type?: string; // This was in your table, add if needed for frontend
  
};

// Type for updates, ensuring we don't try to update id, created_at, updated_at directly via these functions
// For high_level_config and detailed_settings_values, specific functions are better.
export type ModuleUpdatableFields = Omit<Module, 'id' | 'created_at' | 'updated_at' | 'created_by' | 'high_level_config' | 'detailed_settings_schema' | 'detailed_settings_values'>;

// 模块安装状态
export interface ModuleInstallStatus {
  success: boolean;
  message: string;
  moduleId: string;
};

/**
 * 获取所有模块
 * 使用长期缓存策略(1天)，因为模块信息不经常变化
 */
export const getAllModules = () => {
  return cachedRequest<Module[]>({
    url: '/modules',
    method: 'get'
  },
  ApiClientType.LONG_TERM
  // moduleRetryOptions
  );
};

/**
 * 获取已安装模块
 * 使用默认缓存策略(15分钟)
 */
export const getInstalledModules = () => {
  return cachedRequest<Module[]>({
    url: '/modules/installed',
    method: 'get'
  },
  ApiClientType.DEFAULT
  // moduleRetryOptions
  );
};

/**
 * 获取模块详情
 * 使用长期缓存策略(1天)
 */
export const getModuleById = (moduleId: string) => {
  return cachedRequest<Module>({
    url: `/modules/${moduleId}`,
    method: 'get'
  },
  ApiClientType.LONG_TERM
  // moduleRetryOptions
  );
};

/**
 * 安装模块
 * 不使用缓存，并在操作成功后清除相关缓存
 */
export const installModule = async (moduleId: string) => {
  const result = await cachedRequest<ModuleInstallStatus>({
    url: `/modules/${moduleId}/install`,
    method: 'post'
  },
  ApiClientType.NO_CACHE
  );
  
  // 清除模块相关缓存
  cacheManager.clearCache('/modules');
  cacheManager.clearCache('/modules/installed');
  cacheManager.clearCache(`/modules/${moduleId}`);
  
  return result;
};

/**
 * 卸载模块
 * 不使用缓存，并在操作成功后清除相关缓存
 */
export const uninstallModule = async (moduleId: string) => {
  const result = await cachedRequest<ModuleInstallStatus>({
    url: `/modules/${moduleId}/uninstall`,
    method: 'post'
  },
  ApiClientType.NO_CACHE
  );
  
  // 清除模块相关缓存
  cacheManager.clearCache('/modules');
  cacheManager.clearCache('/modules/installed');
  cacheManager.clearCache(`/modules/${moduleId}`);
  
  return result;
};

/**
 * 更新模块
 * 不使用缓存，并在操作成功后清除相关缓存
 */
export const updateModule = async (moduleId: string) => {
  const result = await cachedRequest<ModuleInstallStatus>({
    url: `/modules/${moduleId}/update`,
    method: 'post'
  },
  ApiClientType.NO_CACHE
  );
  
  // 清除模块相关缓存
  cacheManager.clearCache('/modules');
  cacheManager.clearCache('/modules/installed');
  cacheManager.clearCache(`/modules/${moduleId}`);
  
  return result;
};

export const getModules = async (): Promise<Module[]> => {
  console.log('Fetching modules from Supabase...');
  const { data, error } = await supabase
    .from('modules')
    .select('*')
    .order('sort_order', { ascending: true }) // Optional: order by sort_order
    .order('name', { ascending: true }); // Optional: then by name

  if (error) {
    console.error('Error fetching modules:', error);
    throw error;
  }
  return data || [];
};

export const updateModuleIsEnabled = async (moduleId: string, isEnabled: boolean): Promise<Module> => {
  console.log(`Updating module ${moduleId} is_enabled status to ${isEnabled} via Supabase...`);
  const { data, error } = await supabase
    .from('modules')
    .update({ is_enabled: isEnabled }) // updated_at will be handled by trigger
    .eq('id', moduleId)
    .select() // To get the updated row back
    .single(); // Expecting a single row to be updated and returned

  if (error) {
    console.error(`Error updating module ${moduleId} status:`, error);
    throw error;
  }
  if (!data) {
    throw new Error('No data returned after update, module not found or RLS issue?');
  }

  // 清除缓存，确保后续请求能获取最新数据
  cacheManager.clearCache('/modules');
  cacheManager.clearCache('/modules/installed');
  cacheManager.clearCache(`/modules/${moduleId}`);

  return data;
};

// This function is for general module field updates EXCLUDING complex JSON fields or status
export const updateModuleGeneralInfo = async (moduleId: string, updates: Partial<ModuleUpdatableFields>): Promise<Module> => {
  console.log(`Updating module ${moduleId} general info via Supabase, with:`, updates);
  
  // Ensure non-updatable fields are not passed
  const { id, created_at, updated_at, created_by, high_level_config, detailed_settings_schema, detailed_settings_values, is_enabled, ...safeUpdates } = updates as any;

  if (Object.keys(safeUpdates).length === 0) {
    console.warn('No updatable fields provided for updateModuleGeneralInfo');
    // Optionally fetch and return the current module data if no updates are to be made
    const currentModule = await getModuleById(moduleId);
    if (!currentModule) throw new Error('Module not found when trying to update with no fields.');
    return currentModule;
  }

  const { data, error } = await supabase
    .from('modules')
    .update(safeUpdates)
    .eq('id', moduleId)
    .select()
    .single();

  if (error) {
    console.error(`Error updating module ${moduleId} general info:`, error);
    throw error;
  }
  if (!data) {
    throw new Error('No data returned after general info update, module not found or RLS issue?');
  }
  return data;
};

export const updateModuleDetailedSettings = async (moduleId: string, settings: Record<string, any>): Promise<Module> => {
  console.log(`Updating detailed_settings_values for module ${moduleId} via Supabase:`, settings);
  const { data, error } = await supabase
    .from('modules')
    .update({ detailed_settings_values: settings })
    .eq('id', moduleId)
    .select()
    .single();

  if (error) {
    console.error(`Error updating detailed settings for module ${moduleId}:`, error);
    throw error;
  }
  if (!data) {
    throw new Error('No data returned after detailed settings update, module not found or RLS issue?');
  }
  
  // 清除缓存
  cacheManager.clearCache('/modules');
  cacheManager.clearCache('/modules/installed');
  cacheManager.clearCache(`/modules/${moduleId}`);
  
  return data;
};

export const updateModuleHighLevelConfig = async (moduleId: string, config: Record<string, any>): Promise<Module> => {
  console.log(`Updating high_level_config for module ${moduleId} via Supabase:`, config);
  const { data, error } = await supabase
    .from('modules')
    .update({ high_level_config: config })
    .eq('id', moduleId)
    .select()
    .single();

  if (error) {
    console.error(`Error updating high level config for module ${moduleId}:`, error);
    throw error;
  }
  if (!data) {
    throw new Error('No data returned after high level config update, module not found or RLS issue?');
  }
  
  // 清除缓存
  cacheManager.clearCache('/modules');
  cacheManager.clearCache('/modules/installed');
  cacheManager.clearCache(`/modules/${moduleId}`);
  
  return data;
};

const moduleService = {
  getAllModules,
  getInstalledModules,
  getModuleById,
  installModule,
  uninstallModule,
  updateModule,
  getModules,
  updateModuleIsEnabled,
  updateModuleGeneralInfo,
  updateModuleDetailedSettings,
  updateModuleHighLevelConfig
};

export default moduleService; 