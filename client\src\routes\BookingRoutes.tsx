import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';

/**
 * 预约模块路由配置
 * 将所有预约相关页面集中管理，实现代码分割
 */
const BookingRoutes = () => {
  return (
    <Routes>
      <Route index element={<div>Booking Dashboard</div>} />
      <Route path="schedule" element={<div>Schedule Page</div>} />
      <Route path="*" element={<Navigate to="/booking" replace />} />
    </Routes>
  )
};

export default BookingRoutes; 