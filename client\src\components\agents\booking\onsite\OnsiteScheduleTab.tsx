import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Calendar, Clock, Users, UserCheck, Plus, Edit, MapPin, Phone, Mail } from "lucide-react";

/* eslint-disable @typescript-eslint/no-unused-vars */

interface Agent {
  id: string;
  name: string;
}

interface Technician {
  id: string;
  name: string;
  phone: string;
  email: string;
  skills: string[];
  workingHours: {
    [key: string]: { start: string; end: string; available: boolean };
  };
  currentLocation?: { lat: number; lng: number; address: string };
  isActive: boolean;
  maxDailyServices: number;
}

interface Appointment {
  id: string;
  customerName: string;
  customerPhone: string;
  serviceType: string;
  address: string;
  scheduledTime: Date;
  duration: number;
  technicianId: string;
  status: "scheduled" | "in-progress" | "completed" | "cancelled";
  priority: "normal" | "high" | "urgent";
  notes?: string;
  estimatedCost: number;
}

interface OnsiteScheduleTabProps {
  agent: Agent;
}

export default function OnsiteScheduleTab({ agent }: OnsiteScheduleTabProps) {
  const [technicians, setTechnicians] = useState<Technician[]>([
    {
      id: "1",
      name: "Master Zhang",
      phone: "138****1234",
      email: "<EMAIL>",
      skills: ["Appliance Repair", "Circuit Maintenance"],
      workingHours: {
        monday: { start: "09:00", end: "18:00", available: true },
        tuesday: { start: "09:00", end: "18:00", available: true },
        wednesday: { start: "09:00", end: "18:00", available: true },
        thursday: { start: "09:00", end: "18:00", available: true },
        friday: { start: "09:00", end: "18:00", available: true },
        saturday: { start: "09:00", end: "17:00", available: true },
        sunday: { start: "10:00", end: "16:00", available: false },
      },
      currentLocation: { lat: 39.9042, lng: 116.4074, address: "Chaoyang District, Beijing" },
      isActive: true,
      maxDailyServices: 6,
    },
    {
      id: "2",
      name: "Master Li",
      phone: "139****5678",
      email: "<EMAIL>",
      skills: ["Network Installation", "Technical Support"],
      workingHours: {
        monday: { start: "08:00", end: "17:00", available: true },
        tuesday: { start: "08:00", end: "17:00", available: true },
        wednesday: { start: "08:00", end: "17:00", available: true },
        thursday: { start: "08:00", end: "17:00", available: true },
        friday: { start: "08:00", end: "17:00", available: true },
        saturday: { start: "09:00", end: "15:00", available: true },
        sunday: { start: "10:00", end: "14:00", available: false },
      },
      isActive: true,
      maxDailyServices: 8,
    },
  ]);

  const [appointments, setAppointments] = useState<Appointment[]>([
    {
      id: "1",
      customerName: "Mr. Wang",
      customerPhone: "131****9999",
      serviceType: "Appliance Repair",
      address: "No.1 Jianguo Road, Chaoyang District, Beijing",
      scheduledTime: new Date(2024, 0, 15, 10, 0),
      duration: 90,
      technicianId: "1",
      status: "scheduled",
      priority: "normal",
      notes: "Air conditioner not cooling, needs inspection",
      estimatedCost: 200,
    },
    {
      id: "2",
      customerName: "Ms. Chen",
      customerPhone: "132****8888",
      serviceType: "Network Installation",
      address: "No.2 Zhongguancun Street, Haidian District, Beijing",
      scheduledTime: new Date(2024, 0, 15, 14, 30),
      duration: 120,
      technicianId: "2",
      status: "in-progress",
      priority: "high",
      estimatedCost: 300,
    },
  ]);

  const [editingTechnician, setEditingTechnician] = useState<Technician | null>(null);

  const weekdays = [
    { key: "monday", label: "Monday" },
    { key: "tuesday", label: "Tuesday" },
    { key: "wednesday", label: "Wednesday" },
    { key: "thursday", label: "Thursday" },
    { key: "friday", label: "Friday" },
    { key: "saturday", label: "Saturday" },
    { key: "sunday", label: "Sunday" },
  ];

  const getStatusBadge = (status: Appointment["status"]) => {
    const statusMap = {
      scheduled: { label: "Scheduled", variant: "outline" as const, color: "text-blue-600" },
      "in-progress": { label: "In Progress", variant: "default" as const, color: "text-green-600" },
      completed: { label: "Completed", variant: "secondary" as const, color: "text-gray-600" },
      cancelled: { label: "Cancelled", variant: "destructive" as const, color: "text-red-600" },
    };

    const config = statusMap[status];
    return (
      <Badge variant={config.variant} className={config.color}>
        {config.label}
      </Badge>
    );
  };

  const getPriorityBadge = (priority: Appointment["priority"]) => {
    const priorityMap = {
      "normal": { label: "Normal", className: "bg-gray-100 text-gray-800" },
      "high": { label: "High", className: "bg-yellow-100 text-yellow-800" },
      "urgent": { label: "Urgent", className: "bg-red-100 text-red-800" }
    };

    const config = priorityMap[priority];
    return (
      <Badge variant="outline" className={config.className}>
        {config.label}
      </Badge>
    );
  };

  const getTechnicianName = (technicianId: string) => {
    const technician = technicians.find(t => t.id === technicianId);
    return technician ? technician.name : "Unassigned";
  };

  const getTodayAppointments = () => {
    const today = new Date();
    return appointments.filter(apt => 
      apt.scheduledTime.toDateString() === today.toDateString()
    );
  };

  const getActiveTechnicians = () => {
    return technicians.filter(t => t.isActive);
  };

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const handleStatusChange = (appointmentId: string, newStatus: Appointment["status"]) => {
    setAppointments(prev => prev.map(apt => 
      apt.id === appointmentId ? { ...apt, status: newStatus } : apt
    ));
  };

  const handleSaveTechnician = () => {
    if (!editingTechnician) return;

    setTechnicians(prev => prev.map(tech => 
      tech.id === editingTechnician.id ? editingTechnician : tech
    ));
    setEditingTechnician(null);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Schedule Management</h3>
          <p className="text-sm text-muted-foreground">
            Manage {agent.name}'s service schedule and technician assignments
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline">
            <Plus className="mr-2 h-4 w-4" />
            Add Appointment
          </Button>
          <Button variant="outline">
            <Users className="mr-2 h-4 w-4" />
            Manage Technicians
          </Button>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Today's Appointments</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{getTodayAppointments().length}</div>
            <p className="text-xs text-muted-foreground">appointments</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Technicians</CardTitle>
            <UserCheck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{getActiveTechnicians().length}</div>
            <p className="text-xs text-muted-foreground">technicians online</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">In Progress</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {appointments.filter(apt => apt.status === "in-progress").length}
            </div>
            <p className="text-xs text-muted-foreground">active services</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Today's Revenue</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {getTodayAppointments().reduce((sum, apt) => sum + apt.estimatedCost, 0)}
            </div>
            <p className="text-xs text-muted-foreground">estimated</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="appointments" className="space-y-4">
        <TabsList>
          <TabsTrigger value="appointments">Appointments</TabsTrigger>
          <TabsTrigger value="technicians">Technicians</TabsTrigger>
          <TabsTrigger value="schedule">Schedule</TabsTrigger>
        </TabsList>

        <TabsContent value="appointments" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Today's Appointments</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {getTodayAppointments().map((appointment) => (
                  <div
                    key={appointment.id}
                    className="flex items-center justify-between p-4 border rounded-lg"
                  >
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <h4 className="font-medium">{appointment.customerName}</h4>
                        {getStatusBadge(appointment.status)}
                        {getPriorityBadge(appointment.priority)}
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {appointment.serviceType}  {appointment.scheduledTime.toLocaleTimeString()}
                      </p>
                      <div className="flex items-center gap-1 text-sm text-muted-foreground">
                        <MapPin className="h-3 w-3" />
                        {appointment.address}
                      </div>
                      <p className="text-sm">
                        Technician: {getTechnicianName(appointment.technicianId)}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">{appointment.estimatedCost}</p>
                      <p className="text-sm text-muted-foreground">{appointment.duration}min</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="technicians" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Technician Management</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {technicians.map((technician) => (
                  <div
                    key={technician.id}
                    className="flex items-center justify-between p-4 border rounded-lg"
                  >
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <h4 className="font-medium">{technician.name}</h4>
                        <Badge variant={technician.isActive ? "default" : "secondary"}>
                          {technician.isActive ? "Active" : "Inactive"}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-4 text-sm text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <Phone className="h-3 w-3" />
                          {technician.phone}
                        </div>
                        <div className="flex items-center gap-1">
                          <Mail className="h-3 w-3" />
                          {technician.email}
                        </div>
                      </div>
                      <p className="text-sm">Skills: {technician.skills.join(", ")}</p>
                      {technician.currentLocation && (
                        <div className="flex items-center gap-1 text-sm text-muted-foreground">
                          <MapPin className="h-3 w-3" />
                          {technician.currentLocation.address}
                        </div>
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setEditingTechnician(technician)}
                      >
                        <Edit className="h-4 w-4" />
                        Edit
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="schedule" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Weekly Schedule</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                Schedule calendar view will be implemented here
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {editingTechnician && (
        <Dialog open={!!editingTechnician} onOpenChange={() => setEditingTechnician(null)}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Edit Technician</DialogTitle>
              <DialogDescription>
                Update technician information and working hours
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Name</Label>
                  <Input
                    id="name"
                    value={editingTechnician.name}
                    onChange={(e) =>
                      setEditingTechnician({
                        ...editingTechnician,
                        name: e.target.value
                      })
                    }
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="phone">Phone</Label>
                  <Input
                    id="phone"
                    value={editingTechnician.phone}
                    onChange={(e) =>
                      setEditingTechnician({
                        ...editingTechnician,
                        phone: e.target.value
                      })
                    }
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={editingTechnician.email}
                  onChange={(e) =>
                    setEditingTechnician({
                      ...editingTechnician,
                      email: e.target.value
                    })
                  }
                />
              </div>
              <div className="space-y-2">
                <Label>Working Hours</Label>
                <div className="space-y-2">
                  {weekdays.map((day) => (
                    <div key={day.key} className="flex items-center gap-4">
                      <div className="w-20 text-sm">{day.label}</div>
                      <Input
                        placeholder="Start"
                        className="w-24"
                        value={editingTechnician.workingHours[day.key]?.start || ""}
                        onChange={(e) =>
                          setEditingTechnician({
                            ...editingTechnician,
                            workingHours: {
                              ...editingTechnician.workingHours,
                              [day.key]: {
                                ...editingTechnician.workingHours[day.key],
                                start: e.target.value
                              }
                            }
                          })
                        }
                      />
                      <span>-</span>
                      <Input
                        placeholder="End"
                        className="w-24"
                        value={editingTechnician.workingHours[day.key]?.end || ""}
                        onChange={(e) =>
                          setEditingTechnician({
                            ...editingTechnician,
                            workingHours: {
                              ...editingTechnician.workingHours,
                              [day.key]: {
                                ...editingTechnician.workingHours[day.key],
                                end: e.target.value
                              }
                            }
                          })
                        }
                      />
                    </div>
                  ))}
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setEditingTechnician(null)}>
                Cancel
              </Button>
              <Button onClick={handleSaveTechnician}>Save Changes</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}
