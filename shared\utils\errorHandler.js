/**
 * 共享错误处理工具
 * 为所有微服务提供统一的错误处理功能
 */

/**
 * 自定义API错误类
 */
class ApiError extends Error {
  constructor(message, statusCode = 500, code = null, details = null) {
    super(message);
    this.name = 'ApiError';
    this.statusCode = statusCode;
    this.code = code;
    this.details = details;
    this.isOperational = true;
    
    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * 数据库错误类
 */
class DatabaseError extends ApiError {
  constructor(message, originalError = null) {
    super(message, 500, 'DATABASE_ERROR');
    this.originalError = originalError;
  }
}

/**
 * 认证错误类
 */
class AuthenticationError extends ApiError {
  constructor(message = '认证失败') {
    super(message, 401, 'AUTHENTICATION_ERROR');
  }
}

/**
 * 授权错误类
 */
class AuthorizationError extends ApiError {
  constructor(message = '权限不足') {
    super(message, 403, 'AUTHORIZATION_ERROR');
  }
}

/**
 * 验证错误类
 */
class ValidationError extends ApiError {
  constructor(message, details = null) {
    super(message, 400, 'VALIDATION_ERROR', details);
  }
}

/**
 * 资源未找到错误类
 */
class NotFoundError extends ApiError {
  constructor(message = '资源未找到') {
    super(message, 404, 'NOT_FOUND_ERROR');
  }
}

/**
 * 冲突错误类
 */
class ConflictError extends ApiError {
  constructor(message = '资源冲突') {
    super(message, 409, 'CONFLICT_ERROR');
  }
}

/**
 * 速率限制错误类
 */
class RateLimitError extends ApiError {
  constructor(message = '请求过于频繁') {
    super(message, 429, 'RATE_LIMIT_ERROR');
  }
}

/**
 * 格式化错误响应
 * @param {Error} error - 错误对象
 * @param {boolean} includeStack - 是否包含堆栈信息
 * @returns {Object} - 格式化的错误响应
 */
const formatErrorResponse = (error, includeStack = false) => {
  const response = {
    success: false,
    error: {
      message: error.message || '内部服务器错误',
      code: error.code || 'INTERNAL_ERROR',
      statusCode: error.statusCode || 500
    }
  };
  
  if (error.details) {
    response.error.details = error.details;
  }
  
  if (includeStack && error.stack) {
    response.error.stack = error.stack;
  }
  
  return response;
};

/**
 * 错误处理中间件
 * @param {Error} err - 错误对象
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @param {Function} next - 下一个中间件
 */
const errorHandler = (err, req, res, next) => {
  console.error('Error occurred:', {
    message: err.message,
    stack: err.stack,
    url: req.url,
    method: req.method,
    ip: req.ip
  });
  
  // 如果是自定义API错误
  if (err instanceof ApiError) {
    const response = formatErrorResponse(err, process.env.NODE_ENV === 'development');
    return res.status(err.statusCode).json(response);
  }
  
  // 处理Supabase错误
  if (err.code && err.code.startsWith('23')) {
    const response = formatErrorResponse(
      new DatabaseError('数据库操作失败', err),
      process.env.NODE_ENV === 'development'
    );
    return res.status(500).json(response);
  }
  
  // 处理JWT错误
  if (err.name === 'JsonWebTokenError') {
    const response = formatErrorResponse(
      new AuthenticationError('无效的token'),
      process.env.NODE_ENV === 'development'
    );
    return res.status(401).json(response);
  }
  
  if (err.name === 'TokenExpiredError') {
    const response = formatErrorResponse(
      new AuthenticationError('token已过期'),
      process.env.NODE_ENV === 'development'
    );
    return res.status(401).json(response);
  }
  
  // 处理验证错误
  if (err.name === 'ValidationError') {
    const response = formatErrorResponse(
      new ValidationError('数据验证失败', err.details),
      process.env.NODE_ENV === 'development'
    );
    return res.status(400).json(response);
  }
  
  // 默认内部服务器错误
  const response = formatErrorResponse(
    new ApiError('内部服务器错误'),
    process.env.NODE_ENV === 'development'
  );
  
  res.status(500).json(response);
};

/**
 * 异步错误包装器
 * @param {Function} fn - 异步函数
 * @returns {Function} - 包装后的函数
 */
const asyncHandler = (fn) => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * 404错误处理器
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @param {Function} next - 下一个中间件
 */
const notFoundHandler = (req, res, next) => {
  const error = new NotFoundError(`路由 ${req.originalUrl} 未找到`);
  next(error);
};

module.exports = {
  ApiError,
  DatabaseError,
  AuthenticationError,
  AuthorizationError,
  ValidationError,
  NotFoundError,
  ConflictError,
  RateLimitError,
  formatErrorResponse,
  errorHandler,
  asyncHandler,
  notFoundHandler
}; 