import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { 
  Mail, 
  Instagram, 
  Facebook, 
  MessageSquare, 
  Music,
  ShoppingBag,
  Package,
  Phone,
  Settings,
  Activity,
  CheckCircle,
  AlertCircle,
  XCircle,
  ExternalLink,
  RefreshCw
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const PlatformAPIManagementPage: React.FC = () => {
  const navigate = useNavigate();
  
  const [platforms] = useState([
    {
      id: 'gmail',
      name: 'Gmail',
      description: 'Integrate with Gmail to manage communications and automate email workflows',
      category: 'productivity',
      icon: <Mail className="h-5 w-5" />,
      status: 'connected',
      isEnabled: true,
      connectedAccounts: 3,
      lastSync: '2024-01-16 10:30',
      features: ['Email Management', 'Auto-reply', 'Contact Sync', 'Analytics']
    },
    {
      id: 'instagram',
      name: 'Instagram',
      description: 'Connect Instagram Business accounts for content management and analytics',
      category: 'social',
      icon: <Instagram className="h-5 w-5" />,
      status: 'connected',
      isEnabled: true,
      connectedAccounts: 2,
      lastSync: '2024-01-16 09:45',
      features: ['Content Publishing', 'Story Management', 'Analytics', 'Comments']
    },
    {
      id: 'facebook',
      name: 'Facebook',
      description: 'Manage Facebook pages and advertising campaigns',
      category: 'social',
      icon: <Facebook className="h-5 w-5" />,
      status: 'connected',
      isEnabled: true,
      connectedAccounts: 4,
      lastSync: '2024-01-16 11:15',
      features: ['Page Management', 'Post Scheduling', 'Ad Management', 'Insights']
    },
    {
      id: 'messenger',
      name: 'Messenger',
      description: 'Automate customer conversations through Facebook Messenger',
      category: 'communication',
      icon: <MessageSquare className="h-5 w-5" />,
      status: 'connected',
      isEnabled: true,
      connectedAccounts: 2,
      lastSync: '2024-01-16 08:20',
      features: ['Auto-reply', 'Chatbots', 'Customer Support', 'Broadcast']
    },
    {
      id: 'tiktok',
      name: 'TikTok',
      description: 'Connect TikTok Business accounts for content and analytics',
      category: 'social',
      icon: <Music className="h-5 w-5" />,
      status: 'pending',
      isEnabled: false,
      connectedAccounts: 0,
      lastSync: 'Never',
      features: ['Video Analytics', 'Content Management', 'User Insights', 'Trends']
    },
    {
      id: 'shopee',
      name: 'Shopee',
      description: 'Integrate with Shopee for e-commerce operations and order management',
      category: 'ecommerce',
      icon: <ShoppingBag className="h-5 w-5" />,
      status: 'connected',
      isEnabled: true,
      connectedAccounts: 1,
      lastSync: '2024-01-16 07:30',
      features: ['Order Management', 'Product Sync', 'Inventory', 'Analytics']
    },
    {
      id: 'lazada',
      name: 'Lazada',
      description: 'Connect Lazada seller accounts for comprehensive e-commerce management',
      category: 'ecommerce',
      icon: <Package className="h-5 w-5" />,
      status: 'error',
      isEnabled: false,
      connectedAccounts: 0,
      lastSync: '2024-01-15 14:20',
      features: ['Product Management', 'Order Processing', 'Logistics', 'Reports']
    },
    {
      id: 'whatsapp',
      name: 'WhatsApp Business',
      description: 'Automate WhatsApp Business communications and customer support',
      category: 'communication',
      icon: <Phone className="h-5 w-5" />,
      status: 'connected',
      isEnabled: true,
      connectedAccounts: 2,
      lastSync: '2024-01-16 12:00',
      features: ['Message Templates', 'Auto-reply', 'Broadcast', 'Analytics']
    }
  ]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'connected': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'pending': return <AlertCircle className="h-4 w-4 text-yellow-600" />;
      case 'error': return <XCircle className="h-4 w-4 text-red-600" />;
      default: return <XCircle className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'error': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const connectedPlatforms = platforms.filter(p => p.status === 'connected').length;
  const totalAccounts = platforms.reduce((sum, p) => sum + p.connectedAccounts, 0);

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Platform API Management</h1>
          <p className="text-muted-foreground">
            Manage integrations with Gmail, Instagram, Facebook, Messenger, TikTok, Shopee, Lazada, and WhatsApp Business
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={() => navigate('/integrations/api-keys')}>
            <Settings className="mr-2 h-4 w-4" />
            API Keys
          </Button>
          <Button onClick={() => navigate('/integrations/marketplace')}>
            <ExternalLink className="mr-2 h-4 w-4" />
            Marketplace
          </Button>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Connected Platforms</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{connectedPlatforms}/{platforms.length}</div>
            <p className="text-xs text-muted-foreground">
              {((connectedPlatforms / platforms.length) * 100).toFixed(0)}% integration rate
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Accounts</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalAccounts}</div>
            <p className="text-xs text-muted-foreground">
              Across all platforms
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Categories</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">4</div>
            <p className="text-xs text-muted-foreground">
              Social, E-commerce, Communication, Productivity
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Last Sync</CardTitle>
            <RefreshCw className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">12:00</div>
            <p className="text-xs text-muted-foreground">
              WhatsApp Business
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Platform List */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {platforms.map((platform) => (
          <Card key={platform.id} className="hover:shadow-md transition-shadow">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  {platform.icon}
                  <div>
                    <CardTitle className="text-lg">{platform.name}</CardTitle>
                    <CardDescription className="text-sm">{platform.description}</CardDescription>
                  </div>
                </div>
                <Switch 
                  checked={platform.isEnabled} 
                  disabled={platform.status !== 'connected'}
                />
              </div>
              
              <div className="flex items-center space-x-2">
                <Badge className={getStatusColor(platform.status)} variant="secondary">
                  <div className="flex items-center space-x-1">
                    {getStatusIcon(platform.status)}
                    <span className="capitalize">{platform.status}</span>
                  </div>
                </Badge>
              </div>
            </CardHeader>
            
            <CardContent>
              <div className="space-y-4">
                {/* Connection Info */}
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="text-muted-foreground">Connected Accounts</p>
                    <p className="font-semibold">{platform.connectedAccounts}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Last Sync</p>
                    <p className="font-semibold">{platform.lastSync}</p>
                  </div>
                </div>
                
                {/* Features */}
                <div>
                  <p className="text-sm font-medium mb-2">Features</p>
                  <div className="flex flex-wrap gap-1">
                    {platform.features.slice(0, 3).map((feature, index) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {feature}
                      </Badge>
                    ))}
                    {platform.features.length > 3 && (
                      <Badge variant="secondary" className="text-xs">
                        +{platform.features.length - 3} more
                      </Badge>
                    )}
                  </div>
                </div>
                
                {/* Actions */}
                <div className="flex space-x-2 pt-2">
                  {platform.status === 'connected' ? (
                    <>
                      <Button 
                        size="sm" 
                        variant="outline" 
                        className="flex-1"
                      >
                        <RefreshCw className="mr-1 h-3 w-3" />
                        Refresh
                      </Button>
                      <Button 
                        size="sm" 
                        onClick={() => navigate(`/integrations/platforms/${platform.id}`)}
                        className="flex-1"
                      >
                        <Settings className="mr-1 h-3 w-3" />
                        Configure
                      </Button>
                    </>
                  ) : (
                    <Button 
                      size="sm" 
                      className="w-full"
                    >
                      <ExternalLink className="mr-1 h-3 w-3" />
                      Connect
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default PlatformAPIManagementPage;
