import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { 
  Bot, 
  Users, 
  FileText, 
  MessageSquare, 
  Calendar, 
  MapPin,
  Settings,
  Activity,
  TrendingUp,
  Clock
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

interface AIAgent {
  id: string;
  name: string;
  description: string;
  type: 'lead-generation' | 'content-generator' | 'customer-service' | 'walkin-booking' | 'onsite-booking';
  status: 'active' | 'inactive' | 'training';
  icon: React.ReactNode;
  metrics: {
    interactions: number;
    successRate: number;
    avgResponseTime: number;
    satisfaction: number;
  };
  features: string[];
  lastUpdated: string;
}

const AIAgentManagementPage: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  
  const [agents, setAgents] = useState<AIAgent[]>([
    {
      id: 'lead-gen-001',
      name: 'Lead Generation Agent',
      description: 'Automatically identifies and qualifies potential customers from various channels',
      type: 'lead-generation',
      status: 'active',
      icon: <Users className="h-6 w-6" />,
      metrics: {
        interactions: 2847,
        successRate: 89.2,
        avgResponseTime: 1.8,
        satisfaction: 4.6
      },
      features: ['Lead Scoring', 'Multi-channel Integration', 'Auto Follow-up', 'CRM Sync'],
      lastUpdated: '2024-01-15'
    },
    {
      id: 'content-gen-001',
      name: 'Content Generator Agent',
      description: 'Creates engaging content for marketing campaigns and social media',
      type: 'content-generator',
      status: 'active',
      icon: <FileText className="h-6 w-6" />,
      metrics: {
        interactions: 1923,
        successRate: 94.7,
        avgResponseTime: 3.2,
        satisfaction: 4.8
      },
      features: ['Multi-format Content', 'Brand Voice Consistency', 'SEO Optimization', 'A/B Testing'],
      lastUpdated: '2024-01-14'
    },
    {
      id: 'customer-service-001',
      name: 'AI Customer Service Agent',
      description: 'Provides 24/7 customer support with intelligent query resolution',
      type: 'customer-service',
      status: 'active',
      icon: <MessageSquare className="h-6 w-6" />,
      metrics: {
        interactions: 5642,
        successRate: 91.8,
        avgResponseTime: 0.9,
        satisfaction: 4.4
      },
      features: ['Natural Language Processing', 'Sentiment Analysis', 'Escalation Management', 'Multi-language Support'],
      lastUpdated: '2024-01-16'
    },
    {
      id: 'walkin-booking-001',
      name: 'Walk-in Booking Agent',
      description: 'Manages walk-in appointments and queue optimization',
      type: 'walkin-booking',
      status: 'active',
      icon: <Calendar className="h-6 w-6" />,
      metrics: {
        interactions: 1456,
        successRate: 96.3,
        avgResponseTime: 1.2,
        satisfaction: 4.7
      },
      features: ['Real-time Queue Management', 'Wait Time Prediction', 'SMS Notifications', 'Resource Optimization'],
      lastUpdated: '2024-01-13'
    },
    {
      id: 'onsite-booking-001',
      name: 'Onsite Booking Management Agent',
      description: 'Coordinates on-site service appointments and logistics',
      type: 'onsite-booking',
      status: 'training',
      icon: <MapPin className="h-6 w-6" />,
      metrics: {
        interactions: 892,
        successRate: 87.4,
        avgResponseTime: 2.1,
        satisfaction: 4.3
      },
      features: ['Route Optimization', 'Technician Scheduling', 'Customer Communication', 'Service Tracking'],
      lastUpdated: '2024-01-12'
    }
  ]);

  const toggleAgentStatus = (agentId: string) => {
    setAgents(prev => prev.map(agent => 
      agent.id === agentId 
        ? { ...agent, status: agent.status === 'active' ? 'inactive' : 'active' }
        : agent
    ));
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-gray-100 text-gray-800';
      case 'training': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'lead-generation': return <Users className="h-4 w-4" />;
      case 'content-generator': return <FileText className="h-4 w-4" />;
      case 'customer-service': return <MessageSquare className="h-4 w-4" />;
      case 'walkin-booking': return <Calendar className="h-4 w-4" />;
      case 'onsite-booking': return <MapPin className="h-4 w-4" />;
      default: return <Bot className="h-4 w-4" />;
    }
  };

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">AI Agent Management</h1>
          <p className="text-muted-foreground">
            Manage and monitor your AI agents for lead generation, content creation, customer service, and booking management
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={() => navigate('/agents/analytics')}>
            <Activity className="mr-2 h-4 w-4" />
            Analytics
          </Button>
          <Button onClick={() => navigate('/agents/create')}>
            <Bot className="mr-2 h-4 w-4" />
            Create Agent
          </Button>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Agents</CardTitle>
            <Bot className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{agents.filter(a => a.status === 'active').length}</div>
            <p className="text-xs text-muted-foreground">
              +1 from last month
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Interactions</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {agents.reduce((sum, agent) => sum + agent.metrics.interactions, 0).toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              +12% from last month
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Success Rate</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {(agents.reduce((sum, agent) => sum + agent.metrics.successRate, 0) / agents.length).toFixed(1)}%
            </div>
            <p className="text-xs text-muted-foreground">
              +2.1% from last month
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Response Time</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {(agents.reduce((sum, agent) => sum + agent.metrics.avgResponseTime, 0) / agents.length).toFixed(1)}s
            </div>
            <p className="text-xs text-muted-foreground">
              -0.3s from last month
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Agent List */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {agents.map((agent) => (
          <Card key={agent.id} className="hover:shadow-md transition-shadow">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  {agent.icon}
                  <div>
                    <CardTitle className="text-lg">{agent.name}</CardTitle>
                    <CardDescription className="text-sm">{agent.description}</CardDescription>
                  </div>
                </div>
                <Switch 
                  checked={agent.status === 'active'} 
                  onCheckedChange={() => toggleAgentStatus(agent.id)}
                />
              </div>
              <div className="flex items-center space-x-2">
                <Badge className={getStatusColor(agent.status)}>
                  {agent.status}
                </Badge>
                <Badge variant="outline" className="flex items-center space-x-1">
                  {getTypeIcon(agent.type)}
                  <span className="capitalize">{agent.type.replace('-', ' ')}</span>
                </Badge>
              </div>
            </CardHeader>
            
            <CardContent>
              <div className="space-y-4">
                {/* Metrics */}
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="text-muted-foreground">Interactions</p>
                    <p className="font-semibold">{agent.metrics.interactions.toLocaleString()}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Success Rate</p>
                    <p className="font-semibold">{agent.metrics.successRate}%</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Response Time</p>
                    <p className="font-semibold">{agent.metrics.avgResponseTime}s</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Satisfaction</p>
                    <p className="font-semibold">{agent.metrics.satisfaction}/5</p>
                  </div>
                </div>
                
                {/* Features */}
                <div>
                  <p className="text-sm font-medium mb-2">Key Features</p>
                  <div className="flex flex-wrap gap-1">
                    {agent.features.slice(0, 3).map((feature, index) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {feature}
                      </Badge>
                    ))}
                    {agent.features.length > 3 && (
                      <Badge variant="secondary" className="text-xs">
                        +{agent.features.length - 3} more
                      </Badge>
                    )}
                  </div>
                </div>
                
                {/* Actions */}
                <div className="flex space-x-2 pt-2">
                  <Button 
                    size="sm" 
                    variant="outline" 
                    onClick={() => navigate(`/agents/${agent.id}`)}
                    className="flex-1"
                  >
                    <Settings className="mr-1 h-3 w-3" />
                    Configure
                  </Button>
                  <Button 
                    size="sm" 
                    onClick={() => navigate(`/agents/${agent.id}/analytics`)}
                    className="flex-1"
                  >
                    <Activity className="mr-1 h-3 w-3" />
                    Analytics
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default AIAgentManagementPage;
