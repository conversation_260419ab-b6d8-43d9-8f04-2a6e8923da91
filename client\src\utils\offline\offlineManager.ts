import { storageCache } from '../api/localStorageCache'

/**
 * 请求类型
 */
export enum RequestMethod {
  GET = 'GET',
  POST = 'POST',
  PUT = 'PUT',
  DELETE = 'DELETE',
  PATCH = 'PATCH'
};

/**
 * 离线请求项接口
 */
export interface OfflineRequest {
  /** 唯一ID */
  id: string;
  /** 请求URL */
  url: string;
  /** 请求方法 */
  method: RequestMethod;
  /** 请求体 */
  body?: any;
  /** 请求头 */
  headers?: Record<string, string>;
  /** 创建时间戳 */
  createdAt: number;
  /** 重试次数 */
  retries: number;
  /** 优先级 (越高越优先) */
  priority: number;
};

/**
 * 离线管理器配置
 */
export interface OfflineManagerConfig {
  /** 存储键前缀 */
  storageKeyPrefix?: string;
  /** 最大队列长度 */
  maxQueueSize?: number;
  /** 最大重试次数 */
  maxRetries?: number;
  /** 是否自动同步 */
  autoSync?: boolean;
  /** 同步间隔(毫秒) */
  syncInterval?: number;
  /** 是否启用调试日志 */
  debug?: boolean
};

/**
 * 默认配置
 */
const DEFAULT_CONFIG: OfflineManagerConfig = {
  storageKeyPrefix: 'offline_',
  maxQueueSize: 100,
  maxRetries: 3,
  autoSync: true,
  syncInterval: 30000, // 30秒
  debug: false
};

/**
 * 离线管理器
 * 管理离线请求队列，并在网络恢复时同步
 */
export class OfflineManager {
  private config: Required<OfflineManagerConfig>;
  private queue: OfflineRequest[] = [];
  private isOnline: boolean = navigator.onLine;
  private syncTimer: number | null = null;
  private isSyncing: boolean = false;
  
  /**
   * 事件处理程序
   */
  private listeners: {
    sync: Array<(successCount: number, failedCount: number) => void>;
    online: Array<() => void>;
    offline: Array<() => void>;
    queueUpdate: Array<(queue: OfflineRequest[]) => void>
} = {
    sync: [],
    online: [],
    offline: [],
    queueUpdate: []
  };
  
  /**
   * 创建离线管理器实例
   * @param config 配置选项
   */
  constructor(config: OfflineManagerConfig = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config } as Required<OfflineManagerConfig>;
    
    // 加载队列
    this.loadQueue();
    
    // 监听网络状态变化
    window.addEventListener('online', this.handleOnline);
    window.addEventListener('offline', this.handleOffline);
    
    // 启动自动同步
    if (this.config.autoSync && this.isOnline) {
      this.startAutoSync()
};
    
    this.log('离线管理器初始化完成')
};
  
  /**
   * 调试日志
   * @param message 日志消息
   * @param data 日志数据
   */
  private log(message: string, data?: any): void {
    if (this.config.debug) {
      if (data) {
        console.log(`[OfflineManager] ${message}`, data)
} else {
        console.log(`[OfflineManager] ${message}`)
} }
};
  
  /**
   * 处理上线事件
   */
  private handleOnline = (): void => {
    this.isOnline = true;
    this.log('网络已恢复');
    
    this.listeners.online.forEach(listener => listener());
    
    if (this.config.autoSync) {
      this.startAutoSync();
      this.sync(); // 立即尝试同步
    } };
  
  /**
   * 处理离线事件
   */
  private handleOffline = (): void => {
    this.isOnline = false;
    this.log('网络已断开');
    
    this.stopAutoSync();
    this.listeners.offline.forEach(listener => listener())
};
  
  /**
   * 启动自动同步
   */
  private startAutoSync(): void {
    if (this.syncTimer !== null) {
      this.stopAutoSync()
};
    
    this.syncTimer = window.setInterval(() => {
      if (this.isOnline && this.queue.length > 0) {
        this.sync()
} }, this.config.syncInterval);
    
    this.log(`启动自动同步，间隔: ${this.config.syncInterval}ms`)
};
  
  /**
   * 停止自动同步
   */
  private stopAutoSync(): void {
    if (this.syncTimer !== null) {
      window.clearInterval(this.syncTimer);
      this.syncTimer = null;
      this.log('停止自动同步')
} };
  
  /**
   * 从持久化存储加载队列
   */
  private loadQueue(): void {
    try {
      const storedQueue = storageCache.get<OfflineRequest[]>(
        `${this.config.storageKeyPrefix}queue`
      );
      
      if (storedQueue) {
        this.queue = storedQueue;
        this.log(`从存储加载队列，${this.queue.length}个请求`)
} } catch (err) {
      console.error('[OfflineManager], 加载队列失败:', err);
} };
  
  /**
   * 保存队列到持久化存储
   */
  private saveQueue(): void {
    try {
      storageCache.set(
        `${this.config.storageKeyPrefix}queue`,
        this.queue,
        86400 * 7 // 保存7天
      );
      this.log(`保存队列，${this.queue.length}个请求`)
} catch (err) {
      console.error('[OfflineManager], 保存队列失败:', err);
} };
  
  /**
   * 通知队列更新
   */
  private notifyQueueUpdate(): void {
    this.listeners.queueUpdate.forEach(listener => listener([...this.queue]))
};
  
  /**
   * 添加请求到队列
   * @param request 请求数据
   * @returns 是否成功添加
   */
  public enqueue(request: Omit<OfflineRequest, 'id' | 'createdAt' | 'retries'>): boolean {
    // 检查队列是否已满
    if (this.queue.length >= this.config.maxQueueSize) {
      this.log('队列已满，移除最旧的请求');
      this.queue.shift(); // 移除最旧的请求
    }
    
    const offlineRequest: OfflineRequest = {
      ...request,
      id: this.generateRequestId(),
      createdAt: Date.now(),
      retries: 0,
      priority: request.priority || 0
    };
    
    // 按优先级插入队列
    const insertIndex = this.queue.findIndex(
      existing => existing.priority < offlineRequest.priority
    );
    
    if (insertIndex === -1) {
      this.queue.push(offlineRequest);
    } else {
      this.queue.splice(insertIndex, 0, offlineRequest);
    }
    
    this.saveQueue();
    this.notifyQueueUpdate();
    
    this.log(`请求已加入队列: ${request.method} ${request.url}`, offlineRequest);
    
    // 如果在线且自动同步，立即尝试同步
    if (this.isOnline && this.config.autoSync && !this.isSyncing) {
      this.sync();
    }
    
    return true;
};
  
  /**
   * 从队列中移除请求
   * @param requestId 请求ID
   * @returns 是否成功移除
   */
  public dequeue(requestId: string): boolean {
    const index = this.queue.findIndex(req => req.id === requestId);
    
    if (index !== -1) {
      const removed = this.queue.splice(index, 1)[0];
      this.saveQueue();
      this.notifyQueueUpdate();
      
      this.log(`请求已从队列移除: ${removed.method} ${removed.url}`);
      return true;
    }
    
    return false;
};
  
  /**
   * 清空请求队列
   */
  public clearQueue(): void {
    const queueLength = this.queue.length;
    this.queue = [];
    this.saveQueue();
    this.notifyQueueUpdate();
    
    this.log(`队列已清空，移除了${queueLength}个请求`);
  }
  
  /**
   * 获取队列副本
   * @returns 队列副本
   */
  public getQueue(): OfflineRequest[] {
    return [...this.queue];
  }
  
  /**
   * 获取队列长度
   * @returns 队列长度
   */
  public getQueueLength(): number {
    return this.queue.length;
  }
  
  /**
   * 执行单个请求
   * @param request 请求对象
   * @returns 请求结果Promise
   */
  private async executeRequest(request: OfflineRequest): Promise<Response> {
    this.log(`执行请求: ${request.method} ${request.url}`);
    
    const requestOptions: RequestInit = {
      method: request.method,
      headers: request.headers || {
        'Content-Type': 'application/json'
      },
      body: request.body ? JSON.stringify(request.body) : undefined
    };
    
    return fetch(request.url, requestOptions);
  }
  
  /**
   * 同步队列中的请求
   * @returns 同步结果Promise
   */
  public async sync(): Promise<{ successCount: number; failedCount: number }> {
    if (!this.isOnline || this.isSyncing || this.queue.length === 0) {
      return { successCount: 0, failedCount: 0 };
    }
    
    this.isSyncing = true;
    this.log('开始同步队列');
    
    let successCount = 0;
    let failedCount = 0;
    const requestsToRemove: string[] = [];
    const requestsToRetry: OfflineRequest[] = [];
    
    // 按优先级排序
    const sortedQueue = [...this.queue].sort((a, b) => b.priority - a.priority);
    
    for (const request of sortedQueue) {
      try {
        const response = await this.executeRequest(request);
        
        if (response.ok) {
          // 请求成功，标记为移除
          requestsToRemove.push(request.id);
          successCount++;
          this.log(`请求成功: ${request.method} ${request.url}`);
        } else {
          // 请求失败但有HTTP响应
          if (request.retries < this.config.maxRetries) {
            // 标记为重试
            request.retries++;
            requestsToRetry.push(request);
            this.log(`请求失败，将重试(${request.retries}/${this.config.maxRetries}): ${request.method} ${request.url}`);
          } else {
            // 超过最大重试次数，标记为移除
            requestsToRemove.push(request.id);
            failedCount++;
            this.log(`请求失败，达到最大重试次数: ${request.method} ${request.url}`);
          }
        }
      } catch (error) {
        // 网络错误或其他异常
        if (request.retries < this.config.maxRetries) {
          // 标记为重试
          request.retries++;
          requestsToRetry.push(request);
          this.log(`请求出错，将重试(${request.retries}/${this.config.maxRetries}): ${request.method} ${request.url}`, error);
        } else {
          // 超过最大重试次数，标记为移除
          requestsToRemove.push(request.id);
          failedCount++;
          this.log(`请求出错，达到最大重试次数: ${request.method} ${request.url}`, error);
        }
        
        // 如果是网络错误，可能又离线了
        if (!navigator.onLine) {
          this.isOnline = false;
          this.log('同步过程中网络断开，中止同步');
          break;
        }
      }
    }
    
    // 更新队列
    this.queue = this.queue.filter(req => !requestsToRemove.includes(req.id));
    
    // 更新重试次数
    requestsToRetry.forEach(retryReq => {
      const index = this.queue.findIndex(req => req.id === retryReq.id);
      if (index !== -1) {
        this.queue[index].retries = retryReq.retries;
      }
    });
    
    // 保存队列
    this.saveQueue();
    
    // 通知队列更新
    this.notifyQueueUpdate();
    
    // 触发同步完成事件
    this.listeners.sync.forEach(listener => listener(successCount, failedCount));
    
    this.isSyncing = false;
    this.log(`同步完成: ${successCount}成功, ${failedCount}失败, ${this.queue.length}剩余`);
    
    return { successCount, failedCount };
  }
  
  /**
   * 添加事件监听器
   * @param event 事件类型
   * @param listener 监听函数
   */
  public addEventListener<T extends keyof typeof this.listeners>(
    event: T,
    listener: typeof this.listeners[T][0]
  ): void {
    this.listeners[event].push(listener as any);
  }
  
  /**
   * 移除事件监听器
   * @param event 事件类型
   * @param listener 监听函数
   */
  public removeEventListener<T extends keyof typeof this.listeners>(
    event: T,
    listener: typeof this.listeners[T][0]
  ): void {
    const index = this.listeners[event].indexOf(listener as any);
    if (index !== -1) {
      this.listeners[event].splice(index, 1);
    }
  }
  
  /**
   * 检查是否在线
   * @returns 是否在线
   */
  public isNetworkOnline(): boolean {
    return this.isOnline;
  }
  
  /**
   * 获取统计信息
   */
  public getStats(): {
    queueLength: number;
    totalRequests: number;
    networkStatus: string;
    autoSyncEnabled: boolean;
  } {
    return {
      queueLength: this.queue.length,
      totalRequests: this.queue.reduce((sum, req) => sum + req.retries + 1, 0),
      networkStatus: this.isOnline ? 'online' : 'offline',
      autoSyncEnabled: this.config.autoSync
    };
  }
  
  /**
   * 生成请求ID
   */
  private generateRequestId(): string {
    return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  
  /**
   * 销毁实例，清理资源
   */
  public destroy(): void {
    window.removeEventListener('online', this.handleOnline);
    window.removeEventListener('offline', this.handleOffline);
    
    this.stopAutoSync();
    
    // 清空监听器
    Object.keys(this.listeners).forEach(key => {
      this.listeners[key as keyof typeof this.listeners] = [];
    });
    
    this.log('离线管理器已销毁');
  }
}

// 导出单例实例
export const offlineManager = new OfflineManager({
  debug: process.env.NODE_ENV === 'development'
});

export default offlineManager;