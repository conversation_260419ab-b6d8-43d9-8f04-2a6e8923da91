// import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Bot, Plus, Activity, MessageSquare } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

function AgentListPage() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  // Mock agents data
  const agents = [
    {
      id: '1', name: 'Customer Service Bot', description: 'Handles customer inquiries and support requests', status: 'active', type: 'chatbot', conversations: 1247,
      successRate: 94.2
    },
    {
      id: '2', name: 'Sales Assistant', description: 'Assists with product recommendations and sales', status: 'active', type: 'sales', conversations: 856,
      successRate: 91.8
    },
    {
      id: '3', name: 'Technical Support', description: 'Provides technical assistance and troubleshooting', status: 'inactive', type: 'support', conversations: 423,
      successRate: 88.5
    }
  ];

  return (
    <div className="space-y-6 p-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">AI Agents</h1>
          <p className="text-muted-foreground">
            {t('agentListPage.description')}
          </p>
        </div>
        <div className="flex space-x-2">
          <Button onClick={() => navigate('/agents/create')}>
            <Plus className="mr-2 h-4 w-4" />
            {t('agentListPage.createAgent')}
          </Button>
          <Button variant="outline" onClick={() => navigate('/agents/create-smart')}>
            <Bot className="mr-2 h-4 w-4" />
            {t('smartWizard')}
          </Button>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Agents</CardTitle>
            <Bot className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{agents.length}</div>
            <p className="text-xs text-muted-foreground">
              {agents.filter(a => a.status === 'active').length} active
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Conversations</CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {agents.reduce((sum, agent) => sum + agent.conversations, 0).toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              Across all agents
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Success Rate</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {(agents.reduce((sum, agent) => sum + agent.successRate, 0) / agents.length).toFixed(1)}%
            </div>
            <p className="text-xs text-muted-foreground">
              Performance metric
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6">
        <Card>
          <CardHeader>
            <CardTitle>{t('agentListPage.agentList.title')}</CardTitle>
            <CardDescription>
              {t('agentListPage.agentList.description')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {agents.map((agent) => (
                <div key={agent.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center space-x-4">
                    <Bot className="h-10 w-10 text-muted-foreground" />
                    <div>
                      <h3 className="font-semibold">{agent.name}</h3>
                      <p className="text-sm text-muted-foreground">{agent.description}</p>
                      <div className="flex items-center space-x-4 mt-2">
                        <span className="text-xs text-muted-foreground">
                          {agent.conversations} {t('agentListPage.agentCard.conversations')}
                        </span>
                        <span className="text-xs text-muted-foreground">
                          {agent.successRate}% {t('agentListPage.agentCard.successRate')}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant={agent.status === 'active' ? 'default' : 'secondary'}>
                      {t(`agentListPage.agentCard.status.${agent.status}`)}
                    </Badge>
                    <Button variant="outline" size="sm" onClick={() => navigate(`/agents/${agent.id}`)}>
                      {t('agentListPage.agentCard.viewDetails')}
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

export default AgentListPage;