import React from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import BookingManagementPage from '../pages/modules/booking/BookingManagementPage'
import WalkinBookingPage from '../pages/modules/booking/WalkinBookingPage'
import OnsiteBookingPage from '../pages/modules/booking/OnsiteBookingPage'
import ServiceAreaPage from '../pages/modules/booking/ServiceAreaPage'
import ServiceTypePage from '../pages/modules/booking/ServiceTypePage'
import SchedulePage from '../pages/modules/booking/SchedulePage';;

/**
 * 预约模块路由配置
 * 将所有预约相关页面集中管理，实现代码分割
 */
const BookingRoutes = () => {;
  return (
    <Routes>
      <Route index element={<BookingManagementPage />} />
      <Route path="walkin" element={<WalkinBookingPage />} />
      <Route path="onsite" element={<OnsiteBookingPage />} />
      <Route path="service-area" element={<ServiceAreaPage />} />
      <Route path="service-types" element={<ServiceTypePage />} />
      <Route path="schedule" element={<SchedulePage />} />
      <Route path="*" element={<Navigate to="walkin" replace />} />
    </Routes>
  );
};

export default BookingRoutes;