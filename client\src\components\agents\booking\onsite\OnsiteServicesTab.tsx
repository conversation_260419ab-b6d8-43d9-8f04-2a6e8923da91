import React, { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Switch } from "@/components/ui/switch";
import { Plus, Edit, Trash2, Clock, DollarSign, Settings } from "lucide-react";

interface Agent {
  id: string;
  name: string;
}

interface ServiceArea {
  id: string;
  name: string;
  description: string;
  coordinates: { lat: number; lng: number }[];
  isActive: boolean;
  travelTime: number;
  serviceFee: number;
  minBookingNotice: number;
}

interface OnsiteService {
  id: string;
  name: string;
  description: string;
  category: "maintenance" | "consultation" | "installation" | "repair";
  duration: number;
  basePrice: number;
  isActive: boolean;
  requiresSpecialist: boolean;
  equipmentNeeded: string[];
  availableInAreas: string[];
}

interface OnsiteServicesTabProps {
  agent: Agent;
}

export default function OnsiteServicesTab({ agent }: OnsiteServicesTabProps) {
  const [serviceAreas, setServiceAreas] = useState<ServiceArea[]>([
    {
      id: "1",
      name: "Downtown Area",
      description: "Includes CBD, commercial districts and main residential areas",
      coordinates: [],
      isActive: true,
      travelTime: 20,
      serviceFee: 50,
      minBookingNotice: 2,
    },
    {
      id: "2",
      name: "Eastern Suburbs",
      description: "Eastern new development area",
      coordinates: [],
      isActive: true,
      travelTime: 35,
      serviceFee: 80,
      minBookingNotice: 4,
    },
  ]);

  const [onsiteServices, setOnsiteServices] = useState<OnsiteService[]>([
    {
      id: "1",
      name: "Appliance Repair",
      description: "On-site repair services for various household appliances",
      category: "repair",
      duration: 90,
      basePrice: 150,
      isActive: true,
      requiresSpecialist: true,
      equipmentNeeded: ["Tool Kit", "Parts Package", "Testing Equipment"],
      availableInAreas: ["1", "2"],
    },
    {
      id: "2",
      name: "Technical Consultation",
      description: "Professional technical staff on-site consultation and guidance",
      category: "consultation",
      duration: 60,
      basePrice: 200,
      isActive: true,
      requiresSpecialist: false,
      equipmentNeeded: ["Laptop", "Demo Equipment"],
      availableInAreas: ["1"],
    },
  ]);

  const [editingArea, setEditingArea] = useState<ServiceArea | null>(null);
  const [editingService, setEditingService] = useState<OnsiteService | null>(null);
  const [isCreatingArea, setIsCreatingArea] = useState(false);
  const [isCreatingService, setIsCreatingService] = useState(false);

  const categoryLabels = {
    "maintenance": "Maintenance",
    "consultation": "Technical Consultation",
    "installation": "Installation",
    "repair": "Repair Service"
  };

  const handleCreateArea = () => {
    const newArea: ServiceArea = {
      id: Date.now().toString(),
      name: "",
      description: "",
      coordinates: [],
      isActive: true,
      travelTime: 30,
      serviceFee: 50,
      minBookingNotice: 2,
    };
    setEditingArea(newArea);
    setIsCreatingArea(true);
  };

  const handleSaveArea = () => {
    if (!editingArea || !editingArea.name.trim()) return;

    if (isCreatingArea) {
      setServiceAreas([...serviceAreas, editingArea]);
    } else {
      setServiceAreas(serviceAreas.map(area => 
        area.id === editingArea.id ? editingArea : area
      ));
    }
    
    setEditingArea(null);
    setIsCreatingArea(false);
  };

  const handleDeleteArea = (id: string) => {
    setServiceAreas(serviceAreas.filter(area => area.id !== id));
  };

  const handleCreateService = () => {
    const newService: OnsiteService = {
      id: Date.now().toString(),
      name: "",
      description: "",
      category: "consultation",
      duration: 60,
      basePrice: 100,
      isActive: true,
      requiresSpecialist: false,
      equipmentNeeded: [],
      availableInAreas: [],
    };
    setEditingService(newService);
    setIsCreatingService(true);
  };

  const handleSaveService = () => {
    if (!editingService || !editingService.name.trim()) return;

    if (isCreatingService) {
      setOnsiteServices([...onsiteServices, editingService]);
    } else {
      setOnsiteServices(onsiteServices.map(service => 
        service.id === editingService.id ? editingService : service
      ));
    }
    
    setEditingService(null);
    setIsCreatingService(false);
  };

  const handleDeleteService = (id: string) => {
    setOnsiteServices(onsiteServices.filter(service => service.id !== id));
  };

  const getCategoryBadge = (category: OnsiteService["category"]) => {
    const categoryMap: Record<string, { label: string; className: string }> = {
      "maintenance": { label: "Maintenance", className: "bg-blue-100 text-blue-800" },
      "consultation": { label: "Technical Consultation", className: "bg-green-100 text-green-800" },
      "installation": { label: "Installation", className: "bg-purple-100 text-purple-800" },
      "repair": { label: "Repair Service", className: "bg-orange-100 text-orange-800" }
    };

    const config = categoryMap[category];
    return (
      <Badge variant="outline" className={config.className}>
        {config.label}
      </Badge>
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">On-site Service Management</h3>
          <p className="text-sm text-muted-foreground">
            Configure {agent.name}'s on-site service areas and service types
          </p>
        </div>
      </div>

      <Tabs defaultValue="areas" className="space-y-4">
        <TabsList>
          <TabsTrigger value="areas">Service Areas</TabsTrigger>
          <TabsTrigger value="services">Service Types</TabsTrigger>
          <TabsTrigger value="settings">Service Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="areas" className="space-y-4">
          <div className="flex justify-between items-center">
            <h4 className="text-lg font-medium">Service Area Configuration</h4>
            <Button onClick={handleCreateArea} disabled={editingArea !== null}>
              <Plus className="mr-2 h-4 w-4" />
              Add Service Area
            </Button>
          </div>
          <div className="grid gap-4">
            {serviceAreas.map((area) => (
              <Card key={area.id}>
                <CardContent className="pt-6">
                  {editingArea?.id === area.id ? (
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="areaName">Area Name</Label>
                          <Input
                            id="areaName"
                            value={editingArea.name}
                            onChange={(e) => setEditingArea({
                              ...editingArea, name: e.target.value
                            })}
                            placeholder="e.g., Downtown Area"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="areaDescription">Description</Label>
                          <Input
                            id="areaDescription"
                            value={editingArea.description}
                            onChange={(e) => setEditingArea({
                              ...editingArea, description: e.target.value
                            })}
                            placeholder="Describe the coverage of this area"
                          />
                        </div>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="areaTravelTime">Travel Time (min)</Label>
                          <Input
                            id="areaTravelTime"
                            type="number"
                            value={editingArea.travelTime}
                            onChange={(e) => setEditingArea({
                              ...editingArea, travelTime: parseInt(e.target.value, 10)
                            })}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="areaServiceFee">Service Fee</Label>
                          <Input
                            id="areaServiceFee"
                            type="number"
                            value={editingArea.serviceFee}
                            onChange={(e) => setEditingArea({
                              ...editingArea, serviceFee: parseFloat(e.target.value)
                            })}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="areaMinNotice">Min Notice (hours)</Label>
                          <Input
                            id="areaMinNotice"
                            type="number"
                            value={editingArea.minBookingNotice}
                            onChange={(e) => setEditingArea({
                              ...editingArea, minBookingNotice: parseInt(e.target.value, 10)
                            })}
                          />
                        </div>
                      </div>
                      <div className="flex items-center space-x-2 pt-2">
                        <Switch
                          id="areaActive"
                          checked={editingArea.isActive}
                          onCheckedChange={(checked) => setEditingArea({
                            ...editingArea, isActive: checked
                          })}
                        />
                        <Label htmlFor="areaActive">Active</Label>
                      </div>
                      <div className="flex justify-end gap-2">
                        <Button
                          variant="outline"
                          onClick={() => {
                            setEditingArea(null);
                            setIsCreatingArea(false);
                          }}
                        >
                          Cancel
                        </Button>
                        <Button onClick={handleSaveArea}>
                          Save Area
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="flex items-center justify-between">
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          <h4 className="font-medium">{area.name}</h4>
                          <Badge variant={area.isActive ? "default" : "secondary"}>
                            {area.isActive ? "Active" : "Inactive"}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground">{area.description}</p>
                        <div className="flex items-center gap-4 text-sm text-muted-foreground">
                          <div className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            {area.travelTime}min travel
                          </div>
                          <div className="flex items-center gap-1">
                            <DollarSign className="h-3 w-3" />
                            {area.serviceFee} service fee
                          </div>
                          <span>{area.minBookingNotice}h notice required</span>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setEditingArea(area)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDeleteArea(area.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="services" className="space-y-4">
          <div className="flex justify-between items-center">
            <h4 className="text-lg font-medium">Service Type Configuration</h4>
            <Button onClick={handleCreateService} disabled={editingService !== null}>
              <Plus className="mr-2 h-4 w-4" />
              Add Service Type
            </Button>
          </div>
          <div className="grid gap-4">
            {onsiteServices.map((service) => (
              <Card key={service.id}>
                <CardContent className="pt-6">
                  {editingService?.id === service.id ? (
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="serviceName">Service Name</Label>
                          <Input
                            id="serviceName"
                            value={editingService.name}
                            onChange={(e) => setEditingService({
                              ...editingService, name: e.target.value
                            })}
                            placeholder="e.g., Appliance Repair"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="serviceCategory">Category</Label>
                          <Select
                            value={editingService.category}
                            onValueChange={(value) => setEditingService({
                              ...editingService, category: value as OnsiteService["category"]
                            })}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select category" />
                            </SelectTrigger>
                            <SelectContent>
                              {Object.entries(categoryLabels).map(([value, label]) => (
                                <SelectItem key={value} value={value}>
                                  {label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="serviceDescription">Description</Label>
                        <Textarea
                          id="serviceDescription"
                          value={editingService.description}
                          onChange={(e) => setEditingService({
                            ...editingService, description: e.target.value
                          })}
                          placeholder="Describe the service"
                        />
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="serviceDuration">Duration (minutes)</Label>
                          <Input
                            id="serviceDuration"
                            type="number"
                            value={editingService.duration}
                            onChange={(e) => setEditingService({
                              ...editingService, duration: parseInt(e.target.value, 10)
                            })}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="serviceBasePrice">Base Price</Label>
                          <Input
                            id="serviceBasePrice"
                            type="number"
                            value={editingService.basePrice}
                            onChange={(e) => setEditingService({
                              ...editingService, basePrice: parseFloat(e.target.value)
                            })}
                          />
                        </div>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="serviceEquipmentNeeded">Equipment Needed</Label>
                        <Input
                          id="serviceEquipmentNeeded"
                          value={editingService.equipmentNeeded.join(', ')}
                          onChange={(e) => setEditingService({
                            ...editingService, equipmentNeeded: e.target.value.split(',').map(s => s.trim())
                          })}
                          placeholder="e.g., Tool Kit, Laptop"
                        />
                      </div>
                      <div className="flex items-center space-x-2">
                        <Switch
                          id="serviceActive"
                          checked={editingService.isActive}
                          onCheckedChange={(checked) => setEditingService({
                            ...editingService, isActive: checked
                          })}
                        />
                        <Label htmlFor="serviceActive">Active</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Switch
                          id="serviceSpecialist"
                          checked={editingService.requiresSpecialist}
                          onCheckedChange={(checked) => setEditingService({
                            ...editingService, requiresSpecialist: checked
                          })}
                        />
                        <Label htmlFor="serviceSpecialist">Requires Specialist</Label>
                      </div>
                      <div className="flex justify-end gap-2">
                        <Button
                          variant="outline"
                          onClick={() => {
                            setEditingService(null);
                            setIsCreatingService(false);
                          }}
                        >
                          Cancel
                        </Button>
                        <Button onClick={handleSaveService}>
                          Save Service
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="flex items-center justify-between">
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          <h4 className="font-medium">{service.name}</h4>
                          {getCategoryBadge(service.category)}
                          <Badge variant={service.isActive ? "default" : "secondary"}>
                            {service.isActive ? "Active" : "Inactive"}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground">{service.description}</p>
                        <div className="flex items-center gap-4 text-sm text-muted-foreground">
                          <div className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            {service.duration}min
                          </div>
                          <div className="flex items-center gap-1">
                            <DollarSign className="h-3 w-3" />
                            {service.basePrice}
                          </div>
                          {service.requiresSpecialist && (
                            <Badge variant="outline" className="text-xs">
                              Specialist Required
                            </Badge>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setEditingService(service)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDeleteService(service.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Global Service Settings</CardTitle>
              <CardDescription>
                Configure general settings for all on-site services
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Default Service Window</Label>
                  <Input placeholder="e.g., 9:00 AM - 6:00 PM" />
                </div>
                <div className="space-y-2">
                  <Label>Maximum Daily Services</Label>
                  <Input type="number" placeholder="e.g., 8" />
                </div>
              </div>
              <div className="space-y-2">
                <Label>Service Policies</Label>
                <Textarea placeholder="Enter service policies and terms..." />
              </div>
              <Button>
                <Settings className="mr-2 h-4 w-4" />
                Save Settings
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
