-- 创建通知规则表，用于管理自动化预约提醒
CREATE TABLE notification_rules (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL, -- e.g., "24-hour WhatsApp Reminder", "1-hour Email Reminder"
    service_id BIGINT REFERENCES services(id) ON DELETE SET NULL, -- Optional: for service-specific rules
    team_member_id BIGINT REFERENCES teams(id) ON DELETE SET NULL, -- Optional: for team-specific rules (if needed)
    booking_type TEXT, -- Optional: 'walkin', 'on-site', or NULL for all
    
    reminder_value INTEGER NOT NULL, -- e.g., 24, 1, 30
    reminder_unit TEXT NOT NULL, -- 'hours', 'days', 'minutes'
    
    notification_channel TEXT NOT NULL, -- 'email', 'whatsapp', 'messenger', 'instagram', 'sms'
    message_template_id UUID, -- Optional: Foreign key to a message_templates table
    message_content TEXT,    -- Or store simple message content directly here if not using templates
    
    is_active BOOLEAN DEFAULT TRUE,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

COMMENT ON TABLE notification_rules IS 'Defines rules for sending automated appointment reminders.';
COMMENT ON COLUMN notification_rules.service_id IS 'Apply this rule only to specific services (NULL for all).';
COMMENT ON COLUMN notification_rules.booking_type IS 'Apply this rule only to specific booking types (NULL for all).';
COMMENT ON COLUMN notification_rules.reminder_unit IS 'Unit for reminder_value (e.g., hours, days, minutes).';
COMMENT ON COLUMN notification_rules.notification_channel IS 'Channel to send the notification through.';
COMMENT ON COLUMN notification_rules.message_template_id IS 'FK to a table containing message templates for different channels/languages.'; 