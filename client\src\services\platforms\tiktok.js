import api from '../../api/axiosInstance';

const tiktok = {
  // 授权
  authorize: async (authData) => {
    try {
      const response = await api.post('/api/platforms/tiktok/authorize', authData);
      return response.data;
    } catch (error) {
      console.error('TikTok authorization failed:', error);
      throw error;
    }
  },

  // 解绑
  unbind: async (accountId) => {
    try {
      const response = await api.post('/api/platforms/tiktok/unbind', { accountId });
      return response.data;
    } catch (error) {
      console.error('TikTok unbind failed:', error);
      throw error;
    }
  },

  // 获取账号信息
  getAccountInfo: async (accountId) => {
    try {
      const response = await api.get(`/api/platforms/tiktok/account/${accountId}`);
      return response.data;
    } catch (error) {
      console.error('Failed to get TikTok account info:', error);
      throw error;
    }
  },

  // 获取授权URL
  getAuthUrl: async () => {
    try {
      const response = await api.get('/api/platforms/tiktok/auth-url');
      return response.data;
    } catch (error) {
      console.error('Failed to get TikTok auth URL:', error);
      throw error;
    }
  },

  // 获取用户视频列表
  getUserVideos: async (accountId, options = {}) => {
    try {
      const response = await api.get(`/api/platforms/tiktok/videos/${accountId}`, {
        params: options
      });
      return response.data;
    } catch (error) {
      console.error('Failed to get TikTok videos:', error);
      throw error;
    }
  },

  // 发布内容（如果支持）
  postContent: async (accountId, contentData) => {
    try {
      const response = await api.post(`/api/platforms/tiktok/post/${accountId}`, contentData);
      return response.data;
    } catch (error) {
      console.error('Failed to post TikTok content:', error);
      throw error;
    }
  },

  // 获取分析数据
  getAnalytics: async (accountId, dateRange) => {
    try {
      const response = await api.get(`/api/platforms/tiktok/analytics/${accountId}`, {
        params: dateRange
      });
      return response.data;
    } catch (error) {
      console.error('Failed to get TikTok analytics:', error);
      throw error;
    }
  },

  // 刷新令牌
  refreshToken: async (accountId) => {
    try {
      const response = await api.post(`/api/platforms/tiktok/refresh-token/${accountId}`);
      return response.data;
    } catch (error) {
      console.error('Failed to refresh TikTok token:', error);
      throw error;
    }
  },

  // 获取连接状态
  getConnectionStatus: async () => {
    try {
      const response = await api.get('/api/platforms/tiktok/status');
      return response.data;
    } catch (error) {
      console.error('Failed to get TikTok connection status:', error);
      throw error;
    }
  },

  // 测试连接
  testConnection: async (accountId) => {
    try {
      const response = await api.post(`/api/platforms/tiktok/test/${accountId}`);
      return response.data;
    } catch (error) {
      console.error('TikTok connection test failed:', error);
      throw error;
    }
  }
};

export default tiktok;
