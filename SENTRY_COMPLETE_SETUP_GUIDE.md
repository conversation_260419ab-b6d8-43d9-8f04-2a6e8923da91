# 📋 完整详细的 Sentry.io 设置教程

## 🎯 第一步：创建 Sentry.io 账户和项目

### 1.1 注册 Sentry 账户
1. 访问 https://sentry.io/signup/
2. 选择注册方式：
   - **Google 账户** (推荐，快速)
   - **GitHub 账户**
   - **邮箱注册**
3. 验证邮箱地址
4. 选择 **免费计划** (每月 5,000 错误事件，足够开发使用)

### 1.2 创建前端项目
1. 登录后点击 **"Create Project"**
2. 选择平台：**"React"**
3. 项目设置：
   - **项目名称**: `ibuddy2-frontend`
   - **团队**: 使用默认团队
4. 点击 **"Create Project"**
5. **重要**: 复制显示的 DSN，格式类似：
   ```
   https://<EMAIL>/1234567
   ```

### 1.3 创建后端项目
1. 在 Sentry 控制台，点击左上角项目名称
2. 选择 **"Create Project"**
3. 选择平台：**"Node.js"**
4. 项目设置：
   - **项目名称**: `ibuddy2-backend`
   - **团队**: 使用默认团队
5. 点击 **"Create Project"**
6. **重要**: 复制显示的 DSN

## 🔑 第二步：获取和管理 DSN 密钥

### 2.1 如何找到已创建项目的 DSN
如果您忘记了 DSN，可以这样找到：

1. 登录 Sentry.io
2. 选择项目 (ibuddy2-frontend 或 ibuddy2-backend)
3. 点击左侧菜单 **"Settings"**
4. 点击 **"Projects"**
5. 选择您的项目
6. 点击左侧 **"Client Keys (DSN)"**
7. 复制 **DSN** 地址

### 2.2 DSN 格式说明
```
https://[PUBLIC_KEY]@[ORGANIZATION_ID].ingest.sentry.io/[PROJECT_ID]
```

示例：
- 前端 DSN: `https://<EMAIL>/123456`
- 后端 DSN: `https://<EMAIL>/789012`

## 📁 第三步：配置环境变量文件 (关键步骤)

### 3.1 您的项目文件结构
基于分析，您需要配置以下文件：

```
ibuddy2/
├── .env                    # 🔑 主配置文件 (最重要)
├── client/.env             # 前端配置
├── api-gateway/.env        # API 网关配置
├── core-service/.env       # 核心服务配置
├── ai-service/.env         # AI 服务配置 (已创建)
└── server/.env             # 主服务器配置
```

### 3.2 配置主配置文件 (/.env)

**这是最重要的文件！** 在项目根目录创建或编辑 `.env` 文件：

```bash
# =================================
# ibuddy2 主配置文件
# =================================

# 应用基础配置
NODE_ENV=development
APP_VERSION=1.0.0

# =================================
# 🔑 Sentry 错误监控配置 (重要!)
# =================================
# 前端 Sentry DSN (替换为您的实际 DSN)
REACT_APP_SENTRY_DSN=https://<EMAIL>/project_id

# 后端 Sentry DSN (替换为您的实际 DSN)
SENTRY_DSN=https://<EMAIL>/project_id

# =================================
# 服务端口配置
# =================================
CLIENT_PORT=3000
API_GATEWAY_PORT=3001
CORE_SERVICE_PORT=3002
AI_SERVICE_PORT=3003
SERVER_PORT=3004

# =================================
# AI 服务配置 (至少配置一个)
# =================================
# OpenAI 配置 (推荐)
OPENAI_API_KEY=sk-your-openai-api-key-here
OPENAI_MODEL=gpt-4o-mini

# Google AI 配置 (可选)
GEMINI_API_KEY=your-gemini-api-key-here

# OpenRouter 配置 (可选)
OPENROUTER_API_KEY=your-openrouter-api-key-here

# =================================
# 数据库配置
# =================================
SUPABASE_URL=your-supabase-url
SUPABASE_SERVICE_KEY=your-supabase-service-key
SUPABASE_ANON_KEY=your-supabase-anon-key

# =================================
# 安全配置
# =================================
JWT_SECRET=your-super-secure-jwt-secret-key-here
CORS_ORIGIN=http://localhost:3000

# =================================
# 性能和监控配置
# =================================
LOG_LEVEL=info
ENABLE_PERFORMANCE_MONITORING=true
ENABLE_ANALYTICS=true
```

### 3.3 配置前端文件 (/client/.env)

编辑 `client/.env` 文件：

```bash
# 前端特定配置
REACT_APP_PORT=3000
REACT_APP_API_BASE_URL=http://localhost:3001

# 🔑 前端 Sentry DSN
REACT_APP_SENTRY_DSN=https://<EMAIL>/project_id

# 前端特性开关
REACT_APP_ENABLE_ANALYTICS=true
REACT_APP_ENABLE_PERFORMANCE_MONITORING=true
REACT_APP_VERSION=1.0.0

# 开发模式配置
GENERATE_SOURCEMAP=true
REACT_APP_DEBUG=false
```

### 3.4 配置后端服务文件

对于每个后端服务 (`api-gateway/.env`, `core-service/.env`, `server/.env`)，确保包含：

```bash
# 服务基础配置
NODE_ENV=development
PORT=300X  # 每个服务使用不同端口

# 🔑 后端 Sentry DSN (所有后端服务使用相同的)
SENTRY_DSN=https://<EMAIL>/project_id

# 其他服务特定配置...
```

## 🛠️ 第四步：应用配置 (自动化)

### 4.1 使用自动配置脚本

我已经为您准备了自动配置脚本，运行以下命令：

```bash
# 创建主配置文件 (如果不存在)
copy "UNIFIED_ENV_TEMPLATE.env" ".env"

# 应用配置到所有服务
node scripts/complete-error-fix-and-build.js
```

### 4.2 手动配置步骤

如果您更喜欢手动配置：

1. **创建主配置文件**：
   ```bash
   copy "UNIFIED_ENV_TEMPLATE.env" ".env"
   ```

2. **编辑主配置文件**：
   - 打开 `.env` 文件
   - 替换 `your_frontend_dsn` 为您的前端 DSN
   - 替换 `your_backend_dsn` 为您的后端 DSN
   - 添加至少一个 AI API 密钥

3. **同步到子服务**：
   ```bash
   # 复制配置到各个服务
   copy ".env" "client\.env"
   copy ".env" "api-gateway\.env"
   copy ".env" "core-service\.env"
   copy ".env" "server\.env"
   ```

## 🚀 第五步：验证配置

### 5.1 检查配置文件

确保以下文件都存在并包含正确的 Sentry DSN：

```bash
# 检查文件是否存在
dir .env
dir client\.env
dir api-gateway\.env
dir core-service\.env
dir ai-service\.env
dir server\.env
```

### 5.2 测试 Sentry 连接

```bash
# 启动应用测试
npm run dev

# 或使用启动脚本
node tools/start-all-services.js
```

### 5.3 验证 Sentry 监控

1. **访问应用**: http://localhost:3000
2. **制造一些操作**：
   - 点击不同的页面
   - 提交表单
   - 尝试一些功能
3. **查看 Sentry 控制台**: https://sentry.io
4. **确认数据收集**：
   - 在 "Issues" 中查看错误报告
   - 在 "Performance" 中查看性能数据
   - 在 "Releases" 中查看版本信息

## 📊 第六步：配置完成检查清单

### ✅ 必须完成的配置

- [ ] **创建 Sentry 账户**
- [ ] **创建前端项目** (React)
- [ ] **创建后端项目** (Node.js)
- [ ] **获取两个 DSN 密钥**
- [ ] **配置主 .env 文件** (根目录)
- [ ] **配置前端 .env 文件** (client/)
- [ ] **配置所有后端 .env 文件**
- [ ] **添加至少一个 AI API 密钥**
- [ ] **测试应用启动**
- [ ] **验证 Sentry 数据收集**

### 🔧 可选优化配置

- [ ] 配置 Sentry 告警规则
- [ ] 设置性能监控阈值
- [ ] 配置错误过滤规则
- [ ] 设置团队通知
- [ ] 配置集成 (Slack, Email 等)

## 🆘 常见问题解决

### Q1: 找不到 DSN 怎么办？
**A**: 登录 Sentry → 选择项目 → Settings → Projects → 您的项目 → Client Keys (DSN)

### Q2: 前端无法连接 Sentry？
**A**: 检查 `client/.env` 中的 `REACT_APP_SENTRY_DSN` 是否正确配置

### Q3: 后端服务报错？
**A**: 检查各个后端服务的 `.env` 文件中的 `SENTRY_DSN` 配置

### Q4: AI 服务初始化失败？
**A**: 确保在 `.env` 文件中配置了至少一个 AI API 密钥：
- `OPENAI_API_KEY` (推荐)
- `GEMINI_API_KEY`
- `OPENROUTER_API_KEY`

### Q5: 端口冲突错误？
**A**: 运行端口清理脚本：
```bash
node scripts/complete-error-fix-and-build.js
```

## 📞 技术支持

如果遇到问题：

1. **检查 Sentry 控制台** 的 "Issues" 部分
2. **查看应用日志** 文件 (`logs/` 目录)
3. **运行诊断脚本**:
   ```bash
   node tools/check-services-status.js
   ```
4. **查看详细错误信息** 在各服务的日志文件中

---

**🎉 配置完成后，您将拥有企业级的应用监控能力！**

所有错误、性能问题和用户行为都将在 Sentry 控制台中实时显示，帮助您快速定位和解决问题。 