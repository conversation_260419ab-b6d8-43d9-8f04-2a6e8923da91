import { Meta } from '@storybook/blocks';

<Meta title="文档/可访问性指南" />

# 可访问性指南

我们致力于构建符合[WCAG 2.1 AA标准](https://www.w3.org/TR/WCAG21/)的组件库，确保所有用户，包括那些使用辅助技术的用户，都能够有效地使用我们的产品。

## 关键原则

### 可感知性 (Perceivable)

- 提供文本替代方案：为所有非文本内容提供替代文本（如图像的alt属性）
- 提供媒体替代方案：为视频提供字幕，为音频提供文本记录
- 确保内容可以以不同方式呈现，而不会丢失信息
- 使内容易于查看和听取，包括将前景与背景分开

### 可操作性 (Operable)

- 确保所有功能可通过键盘访问
- 给用户足够的时间阅读和使用内容
- 不设计已知会导致癫痫发作的内容
- 提供导航和定位内容的方法

### 可理解性 (Understandable)

- 使文本内容可读且易于理解
- 使网页以可预测的方式出现和操作
- 帮助用户避免和纠正错误

### 健壮性 (Robust)

- 最大限度地提高与当前和未来用户代理（包括辅助技术）的兼容性

## 组件可访问性实践

### 按钮 (Button)

- 使用正确的ARIA角色（通常是`button`）
- 禁用状态必须同时在视觉上和功能上表示
- 确保有足够的点击区域（至少44x44像素）
- 提供可访问的名称（通过文本内容或aria-label）

```jsx
// 良好实践
<Button aria-label="关闭对话框">✕</Button>

// 避免
<Button>✕</Button> // 没有可访问的名称
```

### 对话框 (Dialog)

- 使用正确的ARIA属性，如`role="dialog"`和`aria-modal="true"`
- 确保对话框打开时焦点移入对话框，关闭时返回到触发元素
- 提供关闭对话框的明确方式（如ESC键和关闭按钮）
- 对话框应陷阱焦点，防止用户从键盘访问对话框后面的内容

### 表单控件

- 所有输入字段必须有关联的标签
- 错误消息应通过ARIA属性（如`aria-invalid`和`aria-describedby`）关联到相关字段
- 必填字段应通过`aria-required="true"`标记
- 组合控件（如日期选择器）应使用适当的ARIA模式

```jsx
// 良好实践
<div>
  <label htmlFor="name">姓名</label>
  <input 
    id="name" 
    aria-required="true" 
    aria-invalid={hasError} 
    aria-describedby="name-error" 
  />
  {hasError && <div id="name-error">请输入有效的姓名</div>}
</div>
```

### 卡片和容器

- 确保内容的颜色对比度符合WCAG AA要求（至少4.5:1，大文本至少3:1）
- 使用语义HTML元素构建内容（如`<article>`, `<section>`, `<h1>-<h6>`）
- 交互元素应有清晰的焦点状态

### 图标和视觉元素

- 纯装饰性图标应使用`aria-hidden="true"`
- 具有功能的图标应提供可访问的名称
- 避免仅依靠颜色传达信息

```jsx
// 良好实践
<button>
  <span aria-hidden="true">🔍</span>
  <span className="sr-only">搜索</span>
</button>

// 避免
<button>🔍</button> // 不包含可访问的名称
```

## 键盘导航

所有交互元素都应支持以下键盘操作：

- **Tab**: 在可聚焦元素之间导航
- **Shift+Tab**: 反向导航
- **Enter/Space**: 激活当前聚焦的元素
- **Escape**: 关闭弹出窗口、对话框或取消操作
- **箭头键**: 在相关元素组（如菜单项、选项卡等）之间导航

## 测试与验证

我们使用以下方法测试组件的可访问性：

1. **自动化测试**: 使用Storybook的a11y插件基于axe-core进行测试
2. **键盘测试**: 确保所有功能可通过键盘访问
3. **屏幕阅读器测试**: 使用NVDA、JAWS或VoiceOver等屏幕阅读器测试
4. **色彩对比度检查**: 确保所有文本和交互元素具有足够的对比度

## 资源

- [WCAG 2.1指南](https://www.w3.org/TR/WCAG21/)
- [MDN无障碍文档](https://developer.mozilla.org/zh-CN/docs/Web/Accessibility)
- [Axe-core规则](https://github.com/dequelabs/axe-core/blob/master/doc/rule-descriptions.md)
- [WAI-ARIA实践](https://www.w3.org/TR/wai-aria-practices-1.1/) 