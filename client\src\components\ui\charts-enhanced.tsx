import React from 'react';
import { Chart as ChartJS, CategoryScale, LinearScale, PointElement, LineElement, BarElement, ArcElement, Title, Tooltip, Legend, ChartData, ChartOptions, Filler } from 'chart.js';
import { Line, Bar, Doughnut } from 'react-chartjs-2';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './card';
import { NoChartsState } from './empty-state';
import { SkeletonChart } from './skeleton-enhanced';
import { cn } from '@/lib/utils';
import { dashboardTheme } from '@/theme/dashboard-theme';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

interface ChartWrapperProps {
  title?: string;
  description?: string;
  children: React.ReactNode;
  className?: string;
  isLoading?: boolean;
  error?: string;
  actions?: React.ReactNode;
}

// Enhanced chart wrapper with loading and error states
export function ChartWrapper({ 
  title, 
  description, 
  children, 
  className, 
  isLoading, 
  error,
  actions
}: ChartWrapperProps) {
  if (isLoading) {
    return <SkeletonChart className={className} />;
  }

  if (error) {
    return (
      <Card className={className}>
        <CardContent>
          <NoChartsState 
            action={{
              label: "Retry",
              onClick: () => window.location.reload()
            }}
          />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn("shadow-lg border-0 bg-white dark:bg-slate-900", className)}>
      {(title || description || actions) && (
        <CardHeader className="flex flex-row items-center justify-between">
          <div className="space-y-1">
            {title && <CardTitle className="text-lg font-semibold">{title}</CardTitle>}
            {description && <CardDescription>{description}</CardDescription>}
          </div>
          {actions && <div className="flex gap-2">{actions}</div>}
        </CardHeader>
      )}
      <CardContent className="p-6 pt-0">
        <div className="h-[300px] w-full">
          {children}
        </div>
      </CardContent>
    </Card>
  );
}

// Enhanced chart options with theme
const getBaseOptions = (isDark = false): Partial<ChartOptions<any>> => ({
  responsive: true,
  maintainAspectRatio: false,
  interaction: {
    intersect: false,
    mode: 'index'
  },
  plugins: {
    legend: {
      position: 'top' as const,
      labels: {
        padding: 20,
        usePointStyle: true,
        color: isDark ? '#F1F5F9' : '#1E293B',
        font: {
          family: 'Inter, system-ui, sans-serif',
          size: 12
        }
      }
    },
    tooltip: {
      backgroundColor: dashboardTheme.charts.tooltip.backgroundColor,
      titleColor: dashboardTheme.charts.tooltip.titleColor,
      borderColor: dashboardTheme.charts.tooltip.borderColor,
      borderWidth: 1,
      cornerRadius: 8,
      padding: 12,
      titleFont: {
        family: 'Inter, system-ui, sans-serif',
        size: 13,
        weight: '600'
      },
      bodyFont: {
        family: 'Inter, system-ui, sans-serif',
        size: 12
      }
    }
  },
  elements: {
    point: {
      radius: 4,
      hoverRadius: 6,
      borderWidth: 2
    },
    line: {
      borderWidth: 2,
      tension: 0.4
    },
    bar: {
      borderRadius: 4,
      borderSkipped: false
    }
  }
});

// Line Chart Component
interface LineChartProps {
  data: ChartData<'line'>;
  title?: string;
  description?: string;
  className?: string;
  isLoading?: boolean;
  error?: string;
  height?: number;
  showArea?: boolean;
}

export function LineChart({ 
  data, 
  title, 
  description, 
  className, 
  isLoading, 
  error,
  height = 300,
  showArea = false 
}: LineChartProps) {
  const options: ChartOptions<'line'> = {
    ...getBaseOptions(),
    scales: {
      x: {
        grid: {
          color: dashboardTheme.charts.grid.color
        },
        ticks: {
          color: '#64748B',
          font: {
            family: 'Inter, system-ui, sans-serif',
            size: 11
          }
        }
      },
      y: {
        beginAtZero: true,
        grid: {
          color: dashboardTheme.charts.grid.color
        },
        ticks: {
          color: '#64748B',
          font: {
            family: 'Inter, system-ui, sans-serif',
            size: 11
          }
        }
      }
    }
  };

  // Add area fill if requested
  const enhancedData = showArea ? {
    ...data,
    datasets: data.datasets.map(dataset => ({
      ...dataset,
      fill: true,
      backgroundColor: dataset.backgroundColor || 'rgba(59, 130, 246, 0.1)'
    }))
  } : data;

  return (
    <ChartWrapper 
      title={title} 
      description={description} 
      className={className}
      isLoading={isLoading}
      error={error}
    >
      <Line data={enhancedData} options={options} />
    </ChartWrapper>
  );
}

// Bar Chart Component
interface BarChartProps {
  data: ChartData<'bar'>;
  title?: string;
  description?: string;
  className?: string;
  isLoading?: boolean;
  error?: string;
  horizontal?: boolean;
}

export function BarChart({ 
  data, 
  title, 
  description, 
  className, 
  isLoading, 
  error,
  horizontal = false 
}: BarChartProps) {
  const options: ChartOptions<'bar'> = {
    ...getBaseOptions(),
    indexAxis: horizontal ? 'y' : 'x',
    scales: {
      x: {
        beginAtZero: true,
        grid: {
          color: dashboardTheme.charts.grid.color
        },
        ticks: {
          color: '#64748B',
          font: {
            family: 'Inter, system-ui, sans-serif',
            size: 11
          }
        }
      },
      y: {
        beginAtZero: true,
        grid: {
          color: dashboardTheme.charts.grid.color
        },
        ticks: {
          color: '#64748B',
          font: {
            family: 'Inter, system-ui, sans-serif',
            size: 11
          }
        }
      }
    }
  };

  return (
    <ChartWrapper 
      title={title} 
      description={description} 
      className={className}
      isLoading={isLoading}
      error={error}
    >
      <Bar data={data} options={options} />
    </ChartWrapper>
  );
}

// Doughnut Chart Component
interface DoughnutChartProps {
  data: ChartData<'doughnut'>;
  title?: string;
  description?: string;
  className?: string;
  isLoading?: boolean;
  error?: string;
  centerText?: string;
}

export function DoughnutChart({ 
  data, 
  title, 
  description, 
  className, 
  isLoading, 
  error,
  centerText
}: DoughnutChartProps) {
  const options: ChartOptions<'doughnut'> = {
    ...getBaseOptions(),
    cutout: '60%',
    plugins: {
      ...getBaseOptions().plugins,
      legend: {
        ...getBaseOptions().plugins?.legend,
        position: 'bottom' as const
      }
    }
  };

  return (
    <ChartWrapper 
      title={title} 
      description={description} 
      className={className}
      isLoading={isLoading}
      error={error}
    >
      <div className="relative">
        <Doughnut data={data} options={options} />
        {centerText && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center">
              <div className="text-2xl font-bold text-slate-900 dark:text-slate-100">
                {centerText}
              </div>
            </div>
          </div>
        )}
      </div>
    </ChartWrapper>
  );
}

// Utility functions for chart data
export const generateChartColors = (count: number) => {
  const colors = dashboardTheme.charts.colors.secondary;
  return Array.from({ length: count }, (_, i) => colors[i % colors.length]);
};

export const createGradient = (ctx: CanvasRenderingContext2D, color1: string, color2: string) => {
  const gradient = ctx.createLinearGradient(0, 0, 0, 300);
  gradient.addColorStop(0, color1);
  gradient.addColorStop(1, color2);
  return gradient;
}; 