/**
 * 共享验证工具
 * 为所有微服务提供统一的数据验证功能
 */

/**
 * 验证邮箱格式
 * @param {string} email - 邮箱地址
 * @returns {boolean} - 是否有效
 */
const isValidEmail = (email) => {
  if (!email || typeof email !== 'string') {
    return false;
  }
  
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * 验证密码强度
 * @param {string} password - 密码
 * @param {Object} options - 验证选项
 * @returns {Object} - 验证结果
 */
const validatePassword = (password, options = {}) => {
  const {
    minLength = 8,
    requireUppercase = true,
    requireLowercase = true,
    requireNumbers = true,
    requireSpecialChars = false
  } = options;
  
  const result = {
    isValid: true,
    errors: []
  };
  
  if (!password || typeof password !== 'string') {
    result.isValid = false;
    result.errors.push('密码不能为空');
    return result;
  }
  
  if (password.length < minLength) {
    result.isValid = false;
    result.errors.push(`密码长度至少为${minLength}位`);
  }
  
  if (requireUppercase && !/[A-Z]/.test(password)) {
    result.isValid = false;
    result.errors.push('密码必须包含大写字母');
  }
  
  if (requireLowercase && !/[a-z]/.test(password)) {
    result.isValid = false;
    result.errors.push('密码必须包含小写字母');
  }
  
  if (requireNumbers && !/\d/.test(password)) {
    result.isValid = false;
    result.errors.push('密码必须包含数字');
  }
  
  if (requireSpecialChars && !/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    result.isValid = false;
    result.errors.push('密码必须包含特殊字符');
  }
  
  return result;
};

/**
 * 验证用户名
 * @param {string} username - 用户名
 * @param {Object} options - 验证选项
 * @returns {Object} - 验证结果
 */
const validateUsername = (username, options = {}) => {
  const {
    minLength = 3,
    maxLength = 30,
    allowSpecialChars = false
  } = options;
  
  const result = {
    isValid: true,
    errors: []
  };
  
  if (!username || typeof username !== 'string') {
    result.isValid = false;
    result.errors.push('用户名不能为空');
    return result;
  }
  
  if (username.length < minLength) {
    result.isValid = false;
    result.errors.push(`用户名长度至少为${minLength}位`);
  }
  
  if (username.length > maxLength) {
    result.isValid = false;
    result.errors.push(`用户名长度不能超过${maxLength}位`);
  }
  
  const regex = allowSpecialChars 
    ? /^[a-zA-Z0-9_-]+$/ 
    : /^[a-zA-Z0-9]+$/;
  
  if (!regex.test(username)) {
    result.isValid = false;
    result.errors.push(allowSpecialChars 
      ? '用户名只能包含字母、数字、下划线和连字符' 
      : '用户名只能包含字母和数字');
  }
  
  return result;
};

/**
 * 验证手机号码
 * @param {string} phone - 手机号码
 * @returns {boolean} - 是否有效
 */
const isValidPhone = (phone) => {
  if (!phone || typeof phone !== 'string') {
    return false;
  }
  
  // 支持中国大陆手机号格式
  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(phone);
};

/**
 * 验证URL格式
 * @param {string} url - URL地址
 * @returns {boolean} - 是否有效
 */
const isValidUrl = (url) => {
  if (!url || typeof url !== 'string') {
    return false;
  }
  
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

/**
 * 验证UUID格式
 * @param {string} uuid - UUID字符串
 * @returns {boolean} - 是否有效
 */
const isValidUUID = (uuid) => {
  if (!uuid || typeof uuid !== 'string') {
    return false;
  }
  
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid);
};

/**
 * 清理和验证字符串
 * @param {string} str - 输入字符串
 * @param {Object} options - 验证选项
 * @returns {Object} - 验证结果
 */
const sanitizeAndValidateString = (str, options = {}) => {
  const {
    maxLength = 255,
    minLength = 0,
    trim = true,
    allowEmpty = false
  } = options;
  
  const result = {
    isValid: true,
    value: str,
    errors: []
  };
  
  if (str === null || str === undefined) {
    if (!allowEmpty) {
      result.isValid = false;
      result.errors.push('字符串不能为空');
    }
    return result;
  }
  
  if (typeof str !== 'string') {
    result.value = String(str);
  }
  
  if (trim) {
    result.value = result.value.trim();
  }
  
  if (!allowEmpty && result.value.length === 0) {
    result.isValid = false;
    result.errors.push('字符串不能为空');
    return result;
  }
  
  if (result.value.length < minLength) {
    result.isValid = false;
    result.errors.push(`字符串长度至少为${minLength}位`);
  }
  
  if (result.value.length > maxLength) {
    result.isValid = false;
    result.errors.push(`字符串长度不能超过${maxLength}位`);
  }
  
  return result;
};

/**
 * 验证数字范围
 * @param {number} num - 数字
 * @param {Object} options - 验证选项
 * @returns {Object} - 验证结果
 */
const validateNumber = (num, options = {}) => {
  const {
    min = Number.MIN_SAFE_INTEGER,
    max = Number.MAX_SAFE_INTEGER,
    integer = false
  } = options;
  
  const result = {
    isValid: true,
    errors: []
  };
  
  if (num === null || num === undefined || isNaN(num)) {
    result.isValid = false;
    result.errors.push('必须是有效数字');
    return result;
  }
  
  const numValue = Number(num);
  
  if (integer && !Number.isInteger(numValue)) {
    result.isValid = false;
    result.errors.push('必须是整数');
  }
  
  if (numValue < min) {
    result.isValid = false;
    result.errors.push(`数值不能小于${min}`);
  }
  
  if (numValue > max) {
    result.isValid = false;
    result.errors.push(`数值不能大于${max}`);
  }
  
  return result;
};

/**
 * 验证日期格式
 * @param {string|Date} date - 日期
 * @returns {Object} - 验证结果
 */
const validateDate = (date) => {
  const result = {
    isValid: true,
    errors: []
  };
  
  if (!date) {
    result.isValid = false;
    result.errors.push('日期不能为空');
    return result;
  }
  
  const dateObj = new Date(date);
  
  if (isNaN(dateObj.getTime())) {
    result.isValid = false;
    result.errors.push('无效的日期格式');
  }
  
  return result;
};

/**
 * 批量验证对象字段
 * @param {Object} obj - 要验证的对象
 * @param {Object} rules - 验证规则
 * @returns {Object} - 验证结果
 */
const validateObject = (obj, rules) => {
  const result = {
    isValid: true,
    errors: {},
    hasErrors: false
  };
  
  if (!obj || typeof obj !== 'object') {
    result.isValid = false;
    result.hasErrors = true;
    result.errors.general = ['对象不能为空'];
    return result;
  }
  
  for (const [field, rule] of Object.entries(rules)) {
    const value = obj[field];
    const fieldErrors = [];
    
    // 检查必填字段
    if (rule.required && (value === null || value === undefined || value === '')) {
      fieldErrors.push(`${field}是必填字段`);
    }
    
    // 如果字段有值，进行其他验证
    if (value !== null && value !== undefined && value !== '') {
      // 类型验证
      if (rule.type) {
        switch (rule.type) {
          case 'email':
            if (!isValidEmail(value)) {
              fieldErrors.push(`${field}邮箱格式无效`);
            }
            break;
          case 'phone':
            if (!isValidPhone(value)) {
              fieldErrors.push(`${field}手机号格式无效`);
            }
            break;
          case 'url':
            if (!isValidUrl(value)) {
              fieldErrors.push(`${field}URL格式无效`);
            }
            break;
          case 'uuid':
            if (!isValidUUID(value)) {
              fieldErrors.push(`${field}UUID格式无效`);
            }
            break;
        }
      }
      
      // 长度验证
      if (rule.minLength && value.length < rule.minLength) {
        fieldErrors.push(`${field}长度至少为${rule.minLength}位`);
      }
      
      if (rule.maxLength && value.length > rule.maxLength) {
        fieldErrors.push(`${field}长度不能超过${rule.maxLength}位`);
      }
      
      // 数值范围验证
      if (rule.min !== undefined && Number(value) < rule.min) {
        fieldErrors.push(`${field}不能小于${rule.min}`);
      }
      
      if (rule.max !== undefined && Number(value) > rule.max) {
        fieldErrors.push(`${field}不能大于${rule.max}`);
      }
      
      // 自定义验证函数
      if (rule.validator && typeof rule.validator === 'function') {
        const customResult = rule.validator(value);
        if (!customResult.isValid) {
          fieldErrors.push(...customResult.errors);
        }
      }
    }
    
    if (fieldErrors.length > 0) {
      result.errors[field] = fieldErrors;
      result.isValid = false;
      result.hasErrors = true;
    }
  }
  
  return result;
};

module.exports = {
  isValidEmail,
  validatePassword,
  validateUsername,
  isValidPhone,
  isValidUrl,
  isValidUUID,
  sanitizeAndValidateString,
  validateNumber,
  validateDate,
  validateObject
}; 