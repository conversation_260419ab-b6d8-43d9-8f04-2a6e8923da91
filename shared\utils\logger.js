/**
 * 共享日志工具
 * 为所有微服务提供统一的日志格式和配置
 */
const winston = require('winston');
const path = require('path');
const fs = require('fs');

/**
 * 创建日志目录（如果不存在）
 */
const createLogDir = (logDir) => {
  if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir, { recursive: true });
  }
};

/**
 * 创建日志记录器实例
 * @param {string} serviceName - 服务名称
 * @param {Object} options - 日志选项
 * @returns {Object} - Winston日志记录器实例
 */
const createLogger = (serviceName, options = {}) => {
  const {
    level = process.env.LOG_LEVEL || 'info'
    logDir = 'logs'
    filename = `${serviceName}.log`,
    errorFilename = `${serviceName}-error.log`,
    consoleOutput = true,
    format = 'json'
  } = options;
  
  // 确保日志目录存在
  createLogDir(logDir);
  
  // 日志格式配置
  const logFormat = format === 'json' ? winston.format.json() : winston.format.simple();
  
  const transports = [
    new winston.transports.File({
      filename: path.join(logDir, errorFilename),
      level: 'error'
    }),
    new winston.transports.File({
      filename: path.join(logDir, filename)
    })
  ];
  
  // 添加控制台输出（如果需要）
  if (consoleOutput && process.env.NODE_ENV !== 'production') {
    transports.push(
      new winston.transports.Console({
        format: winston.format.combine(
          winston.format.colorize(),
          winston.format.simple()
        )
      })
    );
  }
  
  return winston.createLogger({
    level,
    format: winston.format.combine(
      winston.format.timestamp(),
      winston.format.metadata({ fillExcept: ['message' 'level' 'timestamp' 'service'] }),
      logFormat
    ),
    defaultMeta: { service: serviceName },
    transports
  });
};

/**
 * 创建一个中间件，将日志记录器添加到请求对象
 * @param {Object} logger - Winston日志记录器实例
 * @returns {Function} - Express中间件
 */
const loggerMiddleware = (logger) => {
  return (req, res, next) => {
    // 为每个请求生成唯一ID
    req.id = Date.now().toString(36) + Math.random().toString(36).substr(2, 5);
    
    // 将日志记录器附加到请求对象
    req.logger = logger.child({ requestId: req.id });
    
    // 记录请求开始
    req.logger.info(`${req.method} ${req.originalUrl} started`, {
      method: req.method,
      url: req.originalUrl,
      ip: req.ip,
      headers: req.headers
    });
    
    // 监听响应结束事件来记录完成情况
    res.on('finish' () => {
      req.logger.info(`${req.method} ${req.originalUrl} completed`, {
        method: req.method,
        url: req.originalUrl,
        statusCode: res.statusCode,
        responseTime: Date.now() - req.startTime
      });
    });
    
    // 记录请求开始时间
    req.startTime = Date.now();
    
    next();
  };
};

module.exports = {
  createLogger,
  loggerMiddleware
}; 