const express = require('express');
const router = express.Router();
// 原始引用 - 已安装 sequelize，但如果启动仍有问题，可取消注释此备份并注释掉下面的引用
// const leadGenerationController = require('../controllers/leadGenerationController');

// 临时使用空控制器，避免启动错误
const leadGenerationController = {
  getLeads: (req, res) => {
    res.json({ message: "Lead Generation API - getLeads placeholder" });
  },
  createLead: (req, res) => {
    res.json({ message: "Lead Generation API - createLead placeholder" received: req.body });
  },
  updateLead: (req, res) => {
    res.json({ message: "Lead Generation API - updateLead placeholder" id: req.params.id });
  },
  deleteLead: (req, res) => {
    res.json({ message: "Lead Generation API - deleteLead placeholder" id: req.params.id });
  },
  getLeadById: (req, res) => {
    res.json({ message: "Lead Generation API - getLeadById placeholder" id: req.params.leadId });
  },
  recordActivity: (req, res) => {
    res.json({ message: "Lead Generation API - recordActivity placeholder" leadId: req.params.leadId, activity: req.body });
  },
  getLeadStats: (req, res) => {
    res.json({ message: "Lead Generation API - getLeadStats placeholder" agentId: req.params.agentId });
  },
  bulkUpdateLeads: (req, res) => {
    res.json({ message: "Lead Generation API - bulkUpdateLeads placeholder" updates: req.body });
  }
};

/**
 * @route   POST /api/lead-generation/agents/:agentId/leads
 * @desc    Create a new lead for a specific agent
 * @access  Public (for form submissions)
 */
router.post('/agents/:agentId/leads', leadGenerationController.createLead);

/**
 * @route   GET /api/lead-generation/agents/:agentId/leads
 * @desc    Get leads for a specific agent
 * @access  Private
 */
router.get('/agents/:agentId/leads', leadGenerationController.getLeads);

/**
 * @route   GET /api/lead-generation/leads/:leadId
 * @desc    Get a specific lead by ID
 * @access  Private
 */
router.get('/leads/:leadId', leadGenerationController.getLeadById);

/**
 * @route   POST /api/lead-generation/leads/:leadId/activities
 * @desc    Record activity for a specific lead
 * @access  Public (for tracking)
 */
router.post('/leads/:leadId/activities', leadGenerationController.recordActivity);

/**
 * @route   GET /api/lead-generation/agents/:agentId/stats
 * @desc    Get lead statistics for a specific agent
 * @access  Private
 */
router.get('/agents/:agentId/stats', leadGenerationController.getLeadStats);

/**
 * @route   PUT /api/lead-generation/leads/bulk
 * @desc    Bulk update leads
 * @access  Private
 */
router.put('/leads/bulk', leadGenerationController.bulkUpdateLeads);

/**
 * @route   DELETE /api/lead-generation/leads/:leadId
 * @desc    Delete a specific lead
 * @access  Private
 */
router.delete('/leads/:leadId', leadGenerationController.deleteLead);

module.exports = router; 