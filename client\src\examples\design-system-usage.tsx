/**
 * 设计系统使用示例
 * 展示如何使用新的设计系统架构
 */

import React from 'react';
// import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
// import { Button } from '@/components/ui/button';
// import { Badge } from '@/components/ui/badge';
// import { Input } from '@/components/ui/input';
// import { Label } from '@/components/ui/label';
// import { Switch } from '@/components/ui/switch';
// import { Textarea } from '@/components/ui/textarea';
// import {
//   DesignSystemProvider,
//   useDesignSystem
// } from '@/design-system/DesignSystemProvider';
// import type {
//   ButtonProps,
//   CardProps,
//   ComponentSize,
//   ComponentVariant
// } from '../types';

// import {
//   designTokens,
//   createTheme,
//   generateCSSVariables,
//   createThemeState
// } from '../theme';

import { 
  ultimateDesignTokens,
  createCSSVariables,
  applyCSSVariables 
} from '../design-system';

// 示例：自定义按钮组件
const ExampleButton: React.FC<ButtonProps> = ({
  children,
  size = 'md',
  variant = 'primary',
  colorScheme = 'primary',
  className = '',
  ...props
}) => {
  const sizeClasses = {
    xs: 'px-2 py-1 text-xs',
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg',
    xl: 'px-8 py-4 text-xl'
  };

  const variantClasses = {
    primary: 'bg-primary-500 text-white hover:bg-primary-600',
    secondary: 'bg-background-secondary text-text-primary border border-border-default hover:bg-background-accent',
    success: 'bg-green-500 text-white hover:bg-green-600',
    warning: 'bg-yellow-500 text-white hover:bg-yellow-600',
    error: 'bg-red-500 text-white hover:bg-red-600',
    info: 'bg-blue-500 text-white hover:bg-blue-600',
    ghost: 'bg-transparent text-text-primary hover:bg-background-accent',
    outline: 'bg-transparent text-text-primary border border-border-default hover:bg-background-accent',
    solid: 'bg-background-primary text-text-primary'
  };

  const classes = `
    inline-flex items-center justify-center
    font-medium rounded-md
    transition-all duration-normal
    focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2
    disabled:opacity-50 disabled:cursor-not-allowed
    ${sizeClasses[size]}
    ${variantClasses[variant]}
    ${className}
  `.trim().replace(/\s+/g, ' ');

  return (
    <button className={classes} {...props}>
      {children}
    </button>
  );
};

// 示例：自定义卡片组件
const ExampleCard: React.FC<CardProps> = ({
  children,
  variant = 'elevated',
  size = 'md',
  className = '',
  ...props
}) => {
  const variantClasses = {
    elevated: 'bg-background-primary shadow-card',
    outline: 'bg-background-primary border border-border-default',
    filled: 'bg-background-secondary',
    ghost: 'bg-transparent'
  };

  const sizeClasses = {
    xs: 'p-2',
    sm: 'p-3',
    md: 'p-4',
    lg: 'p-6',
    xl: 'p-8'
  };

  const classes = `
    rounded-md
    transition-all duration-normal
    ${variantClasses[variant]}
    ${sizeClasses[size]}
    ${className}
  `.trim().replace(/\s+/g, ' ');

  return (
    <div className={classes} {...props}>
      {children}
    </div>
  );
};

// 示例：主题切换组件
const ThemeToggle: React.FC = () => {
  const [currentTheme, setCurrentTheme] = React.useState<'light' | 'dark'>('light');

  const handleToggle = () => {
    const newTheme = currentTheme === 'light' ? 'dark' : 'light';
    setCurrentTheme(newTheme);
    
    // 应用主题变量
    const tokens = newTheme === 'light' ? designTokens : createTheme({
      colors: {
        ...designTokens.colors,
        background: {
          primary: '#0f172a',
          secondary: '#1e293b',
          accent: '#334155',
          muted: '#475569'
        },
        text: {
          primary: '#f8fafc',
          secondary: '#e2e8f0',
          muted: '#cbd5e1',
          accent: '#a78bfa',
          inverse: '#0f172a'
        }
      }
    });

    const variables = generateCSSVariables(tokens);
    applyCSSVariables(variables);
  };

  return (
    <ExampleButton onClick={handleToggle} variant="outline">
      切换到 {currentTheme === 'light' ? '暗色' : '亮色'} 主题
    </ExampleButton>
  );
};

// 示例页面组件
const DesignSystemExample: React.FC = () => {
  React.useEffect(() => {
    // 初始化CSS变量
    const variables = createCSSVariables(ultimateDesignTokens);
    applyCSSVariables(variables);
  }, []);

  return (
    <div className="min-h-screen bg-background-primary text-text-primary p-8">
      <div className="max-w-4xl mx-auto space-y-8">
        {/* 标题 */}
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold text-text-primary">
            设计系统示例
          </h1>
          <p className="text-lg text-text-secondary">
            展示新的设计系统架构和组件
          </p>
          <ThemeToggle />
        </div>

        {/* 按钮示例 */}
        <ExampleCard>
          <h2 className="text-2xl font-semibold mb-4">按钮组件</h2>
          <div className="space-y-4">
            <div className="flex flex-wrap gap-4">
              <ExampleButton size="sm" variant="primary">小按钮</ExampleButton>
              <ExampleButton size="md" variant="secondary">中按钮</ExampleButton>
              <ExampleButton size="lg" variant="success">大按钮</ExampleButton>
            </div>
            <div className="flex flex-wrap gap-4">
              <ExampleButton variant="outline">边框按钮</ExampleButton>
              <ExampleButton variant="ghost">幽灵按钮</ExampleButton>
              <ExampleButton variant="error">错误按钮</ExampleButton>
            </div>
          </div>
        </ExampleCard>

        {/* 卡片示例 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <ExampleCard variant="elevated" size="md">
            <h3 className="text-lg font-semibold mb-2">阴影卡片</h3>
            <p className="text-text-secondary">
              这是一个带有阴影效果的卡片组件。
            </p>
          </ExampleCard>

          <ExampleCard variant="outline" size="md">
            <h3 className="text-lg font-semibold mb-2">边框卡片</h3>
            <p className="text-text-secondary">
              这是一个带有边框的卡片组件。
            </p>
          </ExampleCard>

          <ExampleCard variant="filled" size="md">
            <h3 className="text-lg font-semibold mb-2">填充卡片</h3>
            <p className="text-text-secondary">
              这是一个填充背景色的卡片组件。
            </p>
          </ExampleCard>
        </div>

        {/* 颜色系统展示 */}
        <ExampleCard>
          <h2 className="text-2xl font-semibold mb-4">颜色系统</h2>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            {Object.entries(designTokens.colors.primary).map(([key, value]) => (
              <div key={key} className="text-center">
                <div 
                  className="w-16 h-16 rounded-md mx-auto mb-2"
                  style={{ backgroundColor: value }}
                />
                <div className="text-sm">
                  <div className="font-medium">primary-{key}</div>
                  <div className="text-text-muted">{value}</div>
                </div>
              </div>
            ))}
          </div>
        </ExampleCard>

        {/* 间距系统展示 */}
        <ExampleCard>
          <h2 className="text-2xl font-semibold mb-4">间距系统</h2>
          <div className="space-y-4">
            {Object.entries(designTokens.spacing).map(([key, value]) => (
              <div key={key} className="flex items-center space-x-4">
                <div className="w-20 text-sm font-medium">{key}</div>
                <div className="w-24 text-sm text-text-muted">{value}</div>
                <div 
                  className="bg-primary-200 h-4"
                  style={{ width: value }}
                />
              </div>
            ))}
          </div>
        </ExampleCard>
      </div>
    </div>
  );
};

export default DesignSystemExample; 