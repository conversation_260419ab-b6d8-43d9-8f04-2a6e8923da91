/*! For license information please see 283.30b87179.iframe.bundle.js.LICENSE.txt */
(self.webpackChunkclient=self.webpackChunkclient||[]).push([[283],{"./node_modules/@react-leaflet/core/lib/context.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{"use strict";__webpack_require__.d(__webpack_exports__,{U$:()=>useLeafletContext,W4:()=>extendContext,fB:()=>createLeafletContext,hL:()=>LeafletProvider});var react__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("./node_modules/react/index.js");const CONTEXT_VERSION=1;function createLeafletContext(map){return Object.freeze({__version:CONTEXT_VERSION,map})}function extendContext(source,extra){return Object.freeze({...source,...extra})}const LeafletContext=(0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(null),LeafletProvider=LeafletContext.Provider;function useLeafletContext(){const context=(0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(LeafletContext);if(null==context)throw new Error("No context provided: useLeafletContext() can only be used in a descendant of <MapContainer>");return context}},"./node_modules/@react-leaflet/core/lib/element.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{"use strict";__webpack_require__.d(__webpack_exports__,{K:()=>createElementHook,Q:()=>createElementObject});var react__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("./node_modules/react/index.js");function createElementObject(instance,context,container){return Object.freeze({instance,context,container})}function createElementHook(createElement,updateElement){return null==updateElement?function useImmutableLeafletElement(props,context){const elementRef=(0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();return elementRef.current||(elementRef.current=createElement(props,context)),elementRef}:function useMutableLeafletElement(props,context){const elementRef=(0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();elementRef.current||(elementRef.current=createElement(props,context));const propsRef=(0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(props),{instance}=elementRef.current;return(0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)((function updateElementProps(){propsRef.current!==props&&(updateElement(instance,props,propsRef.current),propsRef.current=props)}),[instance,props,context]),elementRef}}},"./node_modules/@react-leaflet/core/lib/generic.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{"use strict";__webpack_require__.d(__webpack_exports__,{Nq:()=>createLayerComponent,wk:()=>createOverlayComponent,X3:()=>createTileLayerComponent});var react=__webpack_require__("./node_modules/react/index.js"),react_dom=__webpack_require__("./node_modules/react-dom/index.js"),lib_context=__webpack_require__("./node_modules/@react-leaflet/core/lib/context.js");var lib_element=__webpack_require__("./node_modules/@react-leaflet/core/lib/element.js");function useAttribution(map,attribution){const attributionRef=(0,react.useRef)(attribution);(0,react.useEffect)((function updateAttribution(){attribution!==attributionRef.current&&null!=map.attributionControl&&(null!=attributionRef.current&&map.attributionControl.removeAttribution(attributionRef.current),null!=attribution&&map.attributionControl.addAttribution(attribution)),attributionRef.current=attribution}),[map,attribution])}function useEventHandlers(element,eventHandlers){const eventHandlersRef=(0,react.useRef)();(0,react.useEffect)((function addEventHandlers(){return null!=eventHandlers&&element.instance.on(eventHandlers),eventHandlersRef.current=eventHandlers,function removeEventHandlers(){null!=eventHandlersRef.current&&element.instance.off(eventHandlersRef.current),eventHandlersRef.current=null}}),[element,eventHandlers])}var pane=__webpack_require__("./node_modules/@react-leaflet/core/lib/pane.js");function createLayerHook(useElement){return function useLayer(props){const context=(0,lib_context.U$)(),elementRef=useElement((0,pane.P)(props,context),context);return useAttribution(context.map,props.attribution),useEventHandlers(elementRef.current,props.eventHandlers),function useLayerLifecycle(element,context){(0,react.useEffect)((function addLayer(){return(context.layerContainer??context.map).addLayer(element.instance),function removeLayer(){context.layerContainer?.removeLayer(element.instance),context.map.removeLayer(element.instance)}}),[context,element])}(elementRef.current,context),elementRef}}function createLayerComponent(createElement,updateElement){return function component_createContainerComponent(useElement){function ContainerComponent(props,forwardedRef){const{instance,context}=useElement(props).current;return(0,react.useImperativeHandle)(forwardedRef,(()=>instance)),null==props.children?null:react.createElement(lib_context.hL,{value:context},props.children)}return(0,react.forwardRef)(ContainerComponent)}(createLayerHook((0,lib_element.K)(createElement,updateElement)))}function createOverlayComponent(createElement,useLifecycle){const useOverlay=function createDivOverlayHook(useElement,useLifecycle){return function useDivOverlay(props,setOpen){const context=(0,lib_context.U$)(),elementRef=useElement((0,pane.P)(props,context),context);return useAttribution(context.map,props.attribution),useEventHandlers(elementRef.current,props.eventHandlers),useLifecycle(elementRef.current,context,props,setOpen),elementRef}}((0,lib_element.K)(createElement),useLifecycle);return function createDivOverlayComponent(useElement){function OverlayComponent(props,forwardedRef){const[isOpen,setOpen]=(0,react.useState)(!1),{instance}=useElement(props,setOpen).current;(0,react.useImperativeHandle)(forwardedRef,(()=>instance)),(0,react.useEffect)((function updateOverlay(){isOpen&&instance.update()}),[instance,isOpen,props.children]);const contentNode=instance._contentNode;return contentNode?(0,react_dom.createPortal)(props.children,contentNode):null}return(0,react.forwardRef)(OverlayComponent)}(useOverlay)}function createTileLayerComponent(createElement,updateElement){return function component_createLeafComponent(useElement){function LeafComponent(props,forwardedRef){const{instance}=useElement(props).current;return(0,react.useImperativeHandle)(forwardedRef,(()=>instance)),null}return(0,react.forwardRef)(LeafComponent)}(createLayerHook((0,lib_element.K)(createElement,updateElement)))}},"./node_modules/@react-leaflet/core/lib/pane.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{"use strict";function withPane(props,context){const pane=props.pane??context.pane;return pane?{...props,pane}:props}__webpack_require__.d(__webpack_exports__,{P:()=>withPane})},"./node_modules/leaflet/dist/images/marker-icon-2x.png":module=>{"use strict";module.exports="data:image/png;base64,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"},"./node_modules/leaflet/dist/images/marker-icon.png":module=>{"use strict";module.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABkAAAApCAYAAADAk4LOAAAFgUlEQVR4Aa1XA5BjWRTN2oW17d3YaZtr2962HUzbDNpjszW24mRt28p47v7zq/bXZtrp/lWnXr337j3nPCe85NcypgSFdugCpW5YoDAMRaIMqRi6aKq5E3YqDQO3qAwjVWrD8Ncq/RBpykd8oZUb/kaJutow8r1aP9II0WmLKLIsJyv1w/kqw9Ch2MYdB++12Onxee/QMwvf4/Dk/Lfp/i4nxTXtOoQ4pW5Aj7wpici1A9erdAN2OH64x8OSP9j3Ft3b7aWkTg/Fm91siTra0f9on5sQr9INejH6CUUUpavjFNq1B+Oadhxmnfa8RfEmN8VNAsQhPqF55xHkMzz3jSmChWU6f7/XZKNH+9+hBLOHYozuKQPxyMPUKkrX/K0uWnfFaJGS1QPRtZsOPtr3NsW0uyh6NNCOkU3Yz+bXbT3I8G3xE5EXLXtCXbbqwCO9zPQYPRTZ5vIDXD7U+w7rFDEoUUf7ibHIR4y6bLVPXrz8JVZEql13trxwue/uDivd3fkWRbS6/IA2bID4uk0UpF1N8qLlbBlXs4Ee7HLTfV1j54APvODnSfOWBqtKVvjgLKzF5YdEk5ewRkGlK0i33Eofffc7HT56jD7/6U+qH3Cx7SBLNntH5YIPvODnyfIXZYRVDPqgHtLs5ABHD3YzLuespb7t79FY34DjMwrVrcTuwlT55YMPvOBnRrJ4VXTdNnYug5ucHLBjEpt30701A3Ts+HEa73u6dT3FNWwflY86eMHPk+Yu+i6pzUpRrW7SNDg5JHR4KapmM5Wv2E8Tfcb1HoqqHMHU+uWDD7zg54mz5/2BSnizi9T1Dg4QQXLToGNCkb6tb1NU+QAlGr1++eADrzhn/u8Q2YZhQVlZ5+CAOtqfbhmaUCS1ezNFVm2imDbPmPng5wmz+gwh+oHDce0eUtQ6OGDIyR0uUhUsoO3vfDmmgOezH0mZN59x7MBi++WDL1g/eEiU3avlidO671bkLfwbw5XV2P8Pzo0ydy4t2/0eu33xYSOMOD8hTf4CrBtGMSoXfPLchX+J0ruSePw3LZeK0juPJbYzrhkH0io7B3k164hiGvawhOKMLkrQLyVpZg8rHFW7E2uHOL888IBPlNZ1FPzstSJM694fWr6RwpvcJK60+0HCILTBzZLFNdtAzJaohze60T8qBzyh5ZuOg5e7uwQppofEmf2++DYvmySqGBuKaicF1blQjhuHdvCIMvp8whTTfZzI7RldpwtSzL+F1+wkdZ2TBOW2gIF88PBTzD/gpeREAMEbxnJcaJHNHrpzji0gQCS6hdkEeYt9DF/2qPcEC8RM28Hwmr3sdNyht00byAut2k3gufWNtgtOEOFGUwcXWNDbdNbpgBGxEvKkOQsxivJx33iow0Vw5S6SVTrpVq11ysA2Rp7gTfPfktc6zhtXBBC+adRLshf6sG2RfHPZ5EAc4sVZ83yCN00Fk/4kggu40ZTvIEm5g24qtU4KjBrx/BTTH8ifVASAG7gKrnWxJDcU7x8X6Ecczhm3o6YicvsLXWfh3Ch1W0k8x0nXF+0fFxgt4phz8QvypiwCCFKMqXCnqXExjq10beH+UUA7+nG6mdG/Pu0f3LgFcGrl2s0kNNjpmoJ9o4B29CMO8dMT4Q5ox8uitF6fqsrJOr8qnwNbRzv6hSnG5wP+64C7h9lp30hKNtKdWjtdkbuPA19nJ7Tz3zR/ibgARbhb4AlhavcBebmTHcFl2fvYEnW0ox9xMxKBS8btJ+KiEbq9zA4RthQXDhPa0T9TEe69gWupwc6uBUphquXgf+/FrIjweHQS4/pduMe5ERUMHUd9xv8ZR98CxkS4F2n3EUrUZ10EYNw7BWm9x1GiPssi3GgiGRDKWRYZfXlON+dfNbM+GgIwYdwAAAAASUVORK5CYII="},"./node_modules/leaflet/dist/images/marker-shadow.png":module=>{"use strict";module.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACkAAAApCAQAAAACach9AAACMUlEQVR4Ae3ShY7jQBAE0Aoz/f9/HTMzhg1zrdKUrJbdx+Kd2nD8VNudfsL/Th///dyQN2TH6f3y/BGpC379rV+S+qqetBOxImNQXL8JCAr2V4iMQXHGNJxeCfZXhSRBcQMfvkOWUdtfzlLgAENmZDcmo2TVmt8OSM2eXxBp3DjHSMFutqS7SbmemzBiR+xpKCNUIRkdkkYxhAkyGoBvyQFEJEefwSmmvBfJuJ6aKqKWnAkvGZOaZXTUgFqYULWNSHUckZuR1HIIimUExutRxwzOLROIG4vKmCKQt364mIlhSyzAf1m9lHZHJZrlAOMMztRRiKimp/rpdJDc9Awry5xTZCte7FHtuS8wJgeYGrex28xNTd086Dik7vUMscQOa8y4DoGtCCSkAKlNwpgNtphjrC6MIHUkR6YWxxs6Sc5xqn222mmCRFzIt8lEdKx+ikCtg91qS2WpwVfBelJCiQJwvzixfI9cxZQWgiSJelKnwBElKYtDOb2MFbhmUigbReQBV0Cg4+qMXSxXSyGUn4UbF8l+7qdSGnTC0XLCmahIgUHLhLOhpVCtw4CzYXvLQWQbJNmxoCsOKAxSgBJno75avolkRw8iIAFcsdc02e9iyCd8tHwmeSSoKTowIgvscSGZUOA7PuCN5b2BX9mQM7S0wYhMNU74zgsPBj3HU7wguAfnxxjFQGBE6pwN+GjME9zHY7zGp8wVxMShYX9NXvEWD3HbwJf4giO4CFIQxXScH1/TM+04kkBiAAAAAElFTkSuQmCC"},"./node_modules/leaflet/dist/leaflet-src.js":function(__unused_webpack_module,exports){!function(exports){"use strict";var version="1.9.4";function extend(dest){var i,j,len,src;for(j=1,len=arguments.length;j<len;j++)for(i in src=arguments[j])dest[i]=src[i];return dest}var create$2=Object.create||function(){function F(){}return function(proto){return F.prototype=proto,new F}}();function bind(fn,obj){var slice=Array.prototype.slice;if(fn.bind)return fn.bind.apply(fn,slice.call(arguments,1));var args=slice.call(arguments,2);return function(){return fn.apply(obj,args.length?args.concat(slice.call(arguments)):arguments)}}var lastId=0;function stamp(obj){return"_leaflet_id"in obj||(obj._leaflet_id=++lastId),obj._leaflet_id}function throttle(fn,time,context){var lock,args,wrapperFn,later;return later=function(){lock=!1,args&&(wrapperFn.apply(context,args),args=!1)},wrapperFn=function(){lock?args=arguments:(fn.apply(context,arguments),setTimeout(later,time),lock=!0)},wrapperFn}function wrapNum(x,range,includeMax){var max=range[1],min=range[0],d=max-min;return x===max&&includeMax?x:((x-min)%d+d)%d+min}function falseFn(){return!1}function formatNum(num,precision){if(!1===precision)return num;var pow=Math.pow(10,void 0===precision?6:precision);return Math.round(num*pow)/pow}function trim(str){return str.trim?str.trim():str.replace(/^\s+|\s+$/g,"")}function splitWords(str){return trim(str).split(/\s+/)}function setOptions(obj,options){for(var i in Object.prototype.hasOwnProperty.call(obj,"options")||(obj.options=obj.options?create$2(obj.options):{}),options)obj.options[i]=options[i];return obj.options}function getParamString(obj,existingUrl,uppercase){var params=[];for(var i in obj)params.push(encodeURIComponent(uppercase?i.toUpperCase():i)+"="+encodeURIComponent(obj[i]));return(existingUrl&&-1!==existingUrl.indexOf("?")?"&":"?")+params.join("&")}var templateRe=/\{ *([\w_ -]+) *\}/g;function template(str,data){return str.replace(templateRe,(function(str,key){var value=data[key];if(void 0===value)throw new Error("No value provided for variable "+str);return"function"==typeof value&&(value=value(data)),value}))}var isArray=Array.isArray||function(obj){return"[object Array]"===Object.prototype.toString.call(obj)};function indexOf(array,el){for(var i=0;i<array.length;i++)if(array[i]===el)return i;return-1}var emptyImageUrl="data:image/gif;base64,R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs=";function getPrefixed(name){return window["webkit"+name]||window["moz"+name]||window["ms"+name]}var lastTime=0;function timeoutDefer(fn){var time=+new Date,timeToCall=Math.max(0,16-(time-lastTime));return lastTime=time+timeToCall,window.setTimeout(fn,timeToCall)}var requestFn=window.requestAnimationFrame||getPrefixed("RequestAnimationFrame")||timeoutDefer,cancelFn=window.cancelAnimationFrame||getPrefixed("CancelAnimationFrame")||getPrefixed("CancelRequestAnimationFrame")||function(id){window.clearTimeout(id)};function requestAnimFrame(fn,context,immediate){if(!immediate||requestFn!==timeoutDefer)return requestFn.call(window,bind(fn,context));fn.call(context)}function cancelAnimFrame(id){id&&cancelFn.call(window,id)}var Util={__proto__:null,extend,create:create$2,bind,get lastId(){return lastId},stamp,throttle,wrapNum,falseFn,formatNum,trim,splitWords,setOptions,getParamString,template,isArray,indexOf,emptyImageUrl,requestFn,cancelFn,requestAnimFrame,cancelAnimFrame};function Class(){}function checkDeprecatedMixinEvents(includes){if("undefined"!=typeof L&&L&&L.Mixin){includes=isArray(includes)?includes:[includes];for(var i=0;i<includes.length;i++)includes[i]===L.Mixin.Events&&console.warn("Deprecated include of L.Mixin.Events: this property will be removed in future releases, please inherit from L.Evented instead.",(new Error).stack)}}Class.extend=function(props){var NewClass=function(){setOptions(this),this.initialize&&this.initialize.apply(this,arguments),this.callInitHooks()},parentProto=NewClass.__super__=this.prototype,proto=create$2(parentProto);for(var i in proto.constructor=NewClass,NewClass.prototype=proto,this)Object.prototype.hasOwnProperty.call(this,i)&&"prototype"!==i&&"__super__"!==i&&(NewClass[i]=this[i]);return props.statics&&extend(NewClass,props.statics),props.includes&&(checkDeprecatedMixinEvents(props.includes),extend.apply(null,[proto].concat(props.includes))),extend(proto,props),delete proto.statics,delete proto.includes,proto.options&&(proto.options=parentProto.options?create$2(parentProto.options):{},extend(proto.options,props.options)),proto._initHooks=[],proto.callInitHooks=function(){if(!this._initHooksCalled){parentProto.callInitHooks&&parentProto.callInitHooks.call(this),this._initHooksCalled=!0;for(var i=0,len=proto._initHooks.length;i<len;i++)proto._initHooks[i].call(this)}},NewClass},Class.include=function(props){var parentOptions=this.prototype.options;return extend(this.prototype,props),props.options&&(this.prototype.options=parentOptions,this.mergeOptions(props.options)),this},Class.mergeOptions=function(options){return extend(this.prototype.options,options),this},Class.addInitHook=function(fn){var args=Array.prototype.slice.call(arguments,1),init="function"==typeof fn?fn:function(){this[fn].apply(this,args)};return this.prototype._initHooks=this.prototype._initHooks||[],this.prototype._initHooks.push(init),this};var Events={on:function(types,fn,context){if("object"==typeof types)for(var type in types)this._on(type,types[type],fn);else for(var i=0,len=(types=splitWords(types)).length;i<len;i++)this._on(types[i],fn,context);return this},off:function(types,fn,context){if(arguments.length)if("object"==typeof types)for(var type in types)this._off(type,types[type],fn);else{types=splitWords(types);for(var removeAll=1===arguments.length,i=0,len=types.length;i<len;i++)removeAll?this._off(types[i]):this._off(types[i],fn,context)}else delete this._events;return this},_on:function(type,fn,context,_once){if("function"==typeof fn){if(!1===this._listens(type,fn,context)){context===this&&(context=void 0);var newListener={fn,ctx:context};_once&&(newListener.once=!0),this._events=this._events||{},this._events[type]=this._events[type]||[],this._events[type].push(newListener)}}else console.warn("wrong listener type: "+typeof fn)},_off:function(type,fn,context){var listeners,i,len;if(this._events&&(listeners=this._events[type]))if(1!==arguments.length)if("function"==typeof fn){var index=this._listens(type,fn,context);if(!1!==index){var listener=listeners[index];this._firingCount&&(listener.fn=falseFn,this._events[type]=listeners=listeners.slice()),listeners.splice(index,1)}}else console.warn("wrong listener type: "+typeof fn);else{if(this._firingCount)for(i=0,len=listeners.length;i<len;i++)listeners[i].fn=falseFn;delete this._events[type]}},fire:function(type,data,propagate){if(!this.listens(type,propagate))return this;var event=extend({},data,{type,target:this,sourceTarget:data&&data.sourceTarget||this});if(this._events){var listeners=this._events[type];if(listeners){this._firingCount=this._firingCount+1||1;for(var i=0,len=listeners.length;i<len;i++){var l=listeners[i],fn=l.fn;l.once&&this.off(type,fn,l.ctx),fn.call(l.ctx||this,event)}this._firingCount--}}return propagate&&this._propagateEvent(event),this},listens:function(type,fn,context,propagate){"string"!=typeof type&&console.warn('"string" type argument expected');var _fn=fn;"function"!=typeof fn&&(propagate=!!fn,_fn=void 0,context=void 0);var listeners=this._events&&this._events[type];if(listeners&&listeners.length&&!1!==this._listens(type,_fn,context))return!0;if(propagate)for(var id in this._eventParents)if(this._eventParents[id].listens(type,fn,context,propagate))return!0;return!1},_listens:function(type,fn,context){if(!this._events)return!1;var listeners=this._events[type]||[];if(!fn)return!!listeners.length;context===this&&(context=void 0);for(var i=0,len=listeners.length;i<len;i++)if(listeners[i].fn===fn&&listeners[i].ctx===context)return i;return!1},once:function(types,fn,context){if("object"==typeof types)for(var type in types)this._on(type,types[type],fn,!0);else for(var i=0,len=(types=splitWords(types)).length;i<len;i++)this._on(types[i],fn,context,!0);return this},addEventParent:function(obj){return this._eventParents=this._eventParents||{},this._eventParents[stamp(obj)]=obj,this},removeEventParent:function(obj){return this._eventParents&&delete this._eventParents[stamp(obj)],this},_propagateEvent:function(e){for(var id in this._eventParents)this._eventParents[id].fire(e.type,extend({layer:e.target,propagatedFrom:e.target},e),!0)}};Events.addEventListener=Events.on,Events.removeEventListener=Events.clearAllEventListeners=Events.off,Events.addOneTimeEventListener=Events.once,Events.fireEvent=Events.fire,Events.hasEventListeners=Events.listens;var Evented=Class.extend(Events);function Point(x,y,round){this.x=round?Math.round(x):x,this.y=round?Math.round(y):y}var trunc=Math.trunc||function(v){return v>0?Math.floor(v):Math.ceil(v)};function toPoint(x,y,round){return x instanceof Point?x:isArray(x)?new Point(x[0],x[1]):null==x?x:"object"==typeof x&&"x"in x&&"y"in x?new Point(x.x,x.y):new Point(x,y,round)}function Bounds(a,b){if(a)for(var points=b?[a,b]:a,i=0,len=points.length;i<len;i++)this.extend(points[i])}function toBounds(a,b){return!a||a instanceof Bounds?a:new Bounds(a,b)}function LatLngBounds(corner1,corner2){if(corner1)for(var latlngs=corner2?[corner1,corner2]:corner1,i=0,len=latlngs.length;i<len;i++)this.extend(latlngs[i])}function toLatLngBounds(a,b){return a instanceof LatLngBounds?a:new LatLngBounds(a,b)}function LatLng(lat,lng,alt){if(isNaN(lat)||isNaN(lng))throw new Error("Invalid LatLng object: ("+lat+", "+lng+")");this.lat=+lat,this.lng=+lng,void 0!==alt&&(this.alt=+alt)}function toLatLng(a,b,c){return a instanceof LatLng?a:isArray(a)&&"object"!=typeof a[0]?3===a.length?new LatLng(a[0],a[1],a[2]):2===a.length?new LatLng(a[0],a[1]):null:null==a?a:"object"==typeof a&&"lat"in a?new LatLng(a.lat,"lng"in a?a.lng:a.lon,a.alt):void 0===b?null:new LatLng(a,b,c)}Point.prototype={clone:function(){return new Point(this.x,this.y)},add:function(point){return this.clone()._add(toPoint(point))},_add:function(point){return this.x+=point.x,this.y+=point.y,this},subtract:function(point){return this.clone()._subtract(toPoint(point))},_subtract:function(point){return this.x-=point.x,this.y-=point.y,this},divideBy:function(num){return this.clone()._divideBy(num)},_divideBy:function(num){return this.x/=num,this.y/=num,this},multiplyBy:function(num){return this.clone()._multiplyBy(num)},_multiplyBy:function(num){return this.x*=num,this.y*=num,this},scaleBy:function(point){return new Point(this.x*point.x,this.y*point.y)},unscaleBy:function(point){return new Point(this.x/point.x,this.y/point.y)},round:function(){return this.clone()._round()},_round:function(){return this.x=Math.round(this.x),this.y=Math.round(this.y),this},floor:function(){return this.clone()._floor()},_floor:function(){return this.x=Math.floor(this.x),this.y=Math.floor(this.y),this},ceil:function(){return this.clone()._ceil()},_ceil:function(){return this.x=Math.ceil(this.x),this.y=Math.ceil(this.y),this},trunc:function(){return this.clone()._trunc()},_trunc:function(){return this.x=trunc(this.x),this.y=trunc(this.y),this},distanceTo:function(point){var x=(point=toPoint(point)).x-this.x,y=point.y-this.y;return Math.sqrt(x*x+y*y)},equals:function(point){return(point=toPoint(point)).x===this.x&&point.y===this.y},contains:function(point){return point=toPoint(point),Math.abs(point.x)<=Math.abs(this.x)&&Math.abs(point.y)<=Math.abs(this.y)},toString:function(){return"Point("+formatNum(this.x)+", "+formatNum(this.y)+")"}},Bounds.prototype={extend:function(obj){var min2,max2;if(!obj)return this;if(obj instanceof Point||"number"==typeof obj[0]||"x"in obj)min2=max2=toPoint(obj);else if(min2=(obj=toBounds(obj)).min,max2=obj.max,!min2||!max2)return this;return this.min||this.max?(this.min.x=Math.min(min2.x,this.min.x),this.max.x=Math.max(max2.x,this.max.x),this.min.y=Math.min(min2.y,this.min.y),this.max.y=Math.max(max2.y,this.max.y)):(this.min=min2.clone(),this.max=max2.clone()),this},getCenter:function(round){return toPoint((this.min.x+this.max.x)/2,(this.min.y+this.max.y)/2,round)},getBottomLeft:function(){return toPoint(this.min.x,this.max.y)},getTopRight:function(){return toPoint(this.max.x,this.min.y)},getTopLeft:function(){return this.min},getBottomRight:function(){return this.max},getSize:function(){return this.max.subtract(this.min)},contains:function(obj){var min,max;return(obj="number"==typeof obj[0]||obj instanceof Point?toPoint(obj):toBounds(obj))instanceof Bounds?(min=obj.min,max=obj.max):min=max=obj,min.x>=this.min.x&&max.x<=this.max.x&&min.y>=this.min.y&&max.y<=this.max.y},intersects:function(bounds){bounds=toBounds(bounds);var min=this.min,max=this.max,min2=bounds.min,max2=bounds.max,xIntersects=max2.x>=min.x&&min2.x<=max.x,yIntersects=max2.y>=min.y&&min2.y<=max.y;return xIntersects&&yIntersects},overlaps:function(bounds){bounds=toBounds(bounds);var min=this.min,max=this.max,min2=bounds.min,max2=bounds.max,xOverlaps=max2.x>min.x&&min2.x<max.x,yOverlaps=max2.y>min.y&&min2.y<max.y;return xOverlaps&&yOverlaps},isValid:function(){return!(!this.min||!this.max)},pad:function(bufferRatio){var min=this.min,max=this.max,heightBuffer=Math.abs(min.x-max.x)*bufferRatio,widthBuffer=Math.abs(min.y-max.y)*bufferRatio;return toBounds(toPoint(min.x-heightBuffer,min.y-widthBuffer),toPoint(max.x+heightBuffer,max.y+widthBuffer))},equals:function(bounds){return!!bounds&&(bounds=toBounds(bounds),this.min.equals(bounds.getTopLeft())&&this.max.equals(bounds.getBottomRight()))}},LatLngBounds.prototype={extend:function(obj){var sw2,ne2,sw=this._southWest,ne=this._northEast;if(obj instanceof LatLng)sw2=obj,ne2=obj;else{if(!(obj instanceof LatLngBounds))return obj?this.extend(toLatLng(obj)||toLatLngBounds(obj)):this;if(sw2=obj._southWest,ne2=obj._northEast,!sw2||!ne2)return this}return sw||ne?(sw.lat=Math.min(sw2.lat,sw.lat),sw.lng=Math.min(sw2.lng,sw.lng),ne.lat=Math.max(ne2.lat,ne.lat),ne.lng=Math.max(ne2.lng,ne.lng)):(this._southWest=new LatLng(sw2.lat,sw2.lng),this._northEast=new LatLng(ne2.lat,ne2.lng)),this},pad:function(bufferRatio){var sw=this._southWest,ne=this._northEast,heightBuffer=Math.abs(sw.lat-ne.lat)*bufferRatio,widthBuffer=Math.abs(sw.lng-ne.lng)*bufferRatio;return new LatLngBounds(new LatLng(sw.lat-heightBuffer,sw.lng-widthBuffer),new LatLng(ne.lat+heightBuffer,ne.lng+widthBuffer))},getCenter:function(){return new LatLng((this._southWest.lat+this._northEast.lat)/2,(this._southWest.lng+this._northEast.lng)/2)},getSouthWest:function(){return this._southWest},getNorthEast:function(){return this._northEast},getNorthWest:function(){return new LatLng(this.getNorth(),this.getWest())},getSouthEast:function(){return new LatLng(this.getSouth(),this.getEast())},getWest:function(){return this._southWest.lng},getSouth:function(){return this._southWest.lat},getEast:function(){return this._northEast.lng},getNorth:function(){return this._northEast.lat},contains:function(obj){obj="number"==typeof obj[0]||obj instanceof LatLng||"lat"in obj?toLatLng(obj):toLatLngBounds(obj);var sw2,ne2,sw=this._southWest,ne=this._northEast;return obj instanceof LatLngBounds?(sw2=obj.getSouthWest(),ne2=obj.getNorthEast()):sw2=ne2=obj,sw2.lat>=sw.lat&&ne2.lat<=ne.lat&&sw2.lng>=sw.lng&&ne2.lng<=ne.lng},intersects:function(bounds){bounds=toLatLngBounds(bounds);var sw=this._southWest,ne=this._northEast,sw2=bounds.getSouthWest(),ne2=bounds.getNorthEast(),latIntersects=ne2.lat>=sw.lat&&sw2.lat<=ne.lat,lngIntersects=ne2.lng>=sw.lng&&sw2.lng<=ne.lng;return latIntersects&&lngIntersects},overlaps:function(bounds){bounds=toLatLngBounds(bounds);var sw=this._southWest,ne=this._northEast,sw2=bounds.getSouthWest(),ne2=bounds.getNorthEast(),latOverlaps=ne2.lat>sw.lat&&sw2.lat<ne.lat,lngOverlaps=ne2.lng>sw.lng&&sw2.lng<ne.lng;return latOverlaps&&lngOverlaps},toBBoxString:function(){return[this.getWest(),this.getSouth(),this.getEast(),this.getNorth()].join(",")},equals:function(bounds,maxMargin){return!!bounds&&(bounds=toLatLngBounds(bounds),this._southWest.equals(bounds.getSouthWest(),maxMargin)&&this._northEast.equals(bounds.getNorthEast(),maxMargin))},isValid:function(){return!(!this._southWest||!this._northEast)}},LatLng.prototype={equals:function(obj,maxMargin){return!!obj&&(obj=toLatLng(obj),Math.max(Math.abs(this.lat-obj.lat),Math.abs(this.lng-obj.lng))<=(void 0===maxMargin?1e-9:maxMargin))},toString:function(precision){return"LatLng("+formatNum(this.lat,precision)+", "+formatNum(this.lng,precision)+")"},distanceTo:function(other){return Earth.distance(this,toLatLng(other))},wrap:function(){return Earth.wrapLatLng(this)},toBounds:function(sizeInMeters){var latAccuracy=180*sizeInMeters/40075017,lngAccuracy=latAccuracy/Math.cos(Math.PI/180*this.lat);return toLatLngBounds([this.lat-latAccuracy,this.lng-lngAccuracy],[this.lat+latAccuracy,this.lng+lngAccuracy])},clone:function(){return new LatLng(this.lat,this.lng,this.alt)}};var d,CRS={latLngToPoint:function(latlng,zoom){var projectedPoint=this.projection.project(latlng),scale=this.scale(zoom);return this.transformation._transform(projectedPoint,scale)},pointToLatLng:function(point,zoom){var scale=this.scale(zoom),untransformedPoint=this.transformation.untransform(point,scale);return this.projection.unproject(untransformedPoint)},project:function(latlng){return this.projection.project(latlng)},unproject:function(point){return this.projection.unproject(point)},scale:function(zoom){return 256*Math.pow(2,zoom)},zoom:function(scale){return Math.log(scale/256)/Math.LN2},getProjectedBounds:function(zoom){if(this.infinite)return null;var b=this.projection.bounds,s=this.scale(zoom);return new Bounds(this.transformation.transform(b.min,s),this.transformation.transform(b.max,s))},infinite:!1,wrapLatLng:function(latlng){var lng=this.wrapLng?wrapNum(latlng.lng,this.wrapLng,!0):latlng.lng;return new LatLng(this.wrapLat?wrapNum(latlng.lat,this.wrapLat,!0):latlng.lat,lng,latlng.alt)},wrapLatLngBounds:function(bounds){var center=bounds.getCenter(),newCenter=this.wrapLatLng(center),latShift=center.lat-newCenter.lat,lngShift=center.lng-newCenter.lng;if(0===latShift&&0===lngShift)return bounds;var sw=bounds.getSouthWest(),ne=bounds.getNorthEast();return new LatLngBounds(new LatLng(sw.lat-latShift,sw.lng-lngShift),new LatLng(ne.lat-latShift,ne.lng-lngShift))}},Earth=extend({},CRS,{wrapLng:[-180,180],R:6371e3,distance:function(latlng1,latlng2){var rad=Math.PI/180,lat1=latlng1.lat*rad,lat2=latlng2.lat*rad,sinDLat=Math.sin((latlng2.lat-latlng1.lat)*rad/2),sinDLon=Math.sin((latlng2.lng-latlng1.lng)*rad/2),a=sinDLat*sinDLat+Math.cos(lat1)*Math.cos(lat2)*sinDLon*sinDLon,c=2*Math.atan2(Math.sqrt(a),Math.sqrt(1-a));return this.R*c}}),earthRadius=6378137,SphericalMercator={R:earthRadius,MAX_LATITUDE:85.0511287798,project:function(latlng){var d=Math.PI/180,max=this.MAX_LATITUDE,lat=Math.max(Math.min(max,latlng.lat),-max),sin=Math.sin(lat*d);return new Point(this.R*latlng.lng*d,this.R*Math.log((1+sin)/(1-sin))/2)},unproject:function(point){var d=180/Math.PI;return new LatLng((2*Math.atan(Math.exp(point.y/this.R))-Math.PI/2)*d,point.x*d/this.R)},bounds:(d=earthRadius*Math.PI,new Bounds([-d,-d],[d,d]))};function Transformation(a,b,c,d){if(isArray(a))return this._a=a[0],this._b=a[1],this._c=a[2],void(this._d=a[3]);this._a=a,this._b=b,this._c=c,this._d=d}function toTransformation(a,b,c,d){return new Transformation(a,b,c,d)}Transformation.prototype={transform:function(point,scale){return this._transform(point.clone(),scale)},_transform:function(point,scale){return scale=scale||1,point.x=scale*(this._a*point.x+this._b),point.y=scale*(this._c*point.y+this._d),point},untransform:function(point,scale){return scale=scale||1,new Point((point.x/scale-this._b)/this._a,(point.y/scale-this._d)/this._c)}};var EPSG3857=extend({},Earth,{code:"EPSG:3857",projection:SphericalMercator,transformation:function(){var scale=.5/(Math.PI*SphericalMercator.R);return toTransformation(scale,.5,-scale,.5)}()}),EPSG900913=extend({},EPSG3857,{code:"EPSG:900913"});function svgCreate(name){return document.createElementNS("http://www.w3.org/2000/svg",name)}function pointsToPath(rings,closed){var i,j,len,len2,points,p,str="";for(i=0,len=rings.length;i<len;i++){for(j=0,len2=(points=rings[i]).length;j<len2;j++)str+=(j?"L":"M")+(p=points[j]).x+" "+p.y;str+=closed?Browser.svg?"z":"x":""}return str||"M0 0"}var div,style=document.documentElement.style,ie="ActiveXObject"in window,ielt9=ie&&!document.addEventListener,edge="msLaunchUri"in navigator&&!("documentMode"in document),webkit=userAgentContains("webkit"),android=userAgentContains("android"),android23=userAgentContains("android 2")||userAgentContains("android 3"),webkitVer=parseInt(/WebKit\/([0-9]+)|$/.exec(navigator.userAgent)[1],10),androidStock=android&&userAgentContains("Google")&&webkitVer<537&&!("AudioNode"in window),opera=!!window.opera,chrome=!edge&&userAgentContains("chrome"),gecko=userAgentContains("gecko")&&!webkit&&!opera&&!ie,safari=!chrome&&userAgentContains("safari"),phantom=userAgentContains("phantom"),opera12="OTransition"in style,win=0===navigator.platform.indexOf("Win"),ie3d=ie&&"transition"in style,webkit3d="WebKitCSSMatrix"in window&&"m11"in new window.WebKitCSSMatrix&&!android23,gecko3d="MozPerspective"in style,any3d=!window.L_DISABLE_3D&&(ie3d||webkit3d||gecko3d)&&!opera12&&!phantom,mobile="undefined"!=typeof orientation||userAgentContains("mobile"),mobileWebkit=mobile&&webkit,mobileWebkit3d=mobile&&webkit3d,msPointer=!window.PointerEvent&&window.MSPointerEvent,pointer=!(!window.PointerEvent&&!msPointer),touchNative="ontouchstart"in window||!!window.TouchEvent,touch=!window.L_NO_TOUCH&&(touchNative||pointer),mobileOpera=mobile&&opera,mobileGecko=mobile&&gecko,retina=(window.devicePixelRatio||window.screen.deviceXDPI/window.screen.logicalXDPI)>1,passiveEvents=function(){var supportsPassiveOption=!1;try{var opts=Object.defineProperty({},"passive",{get:function(){supportsPassiveOption=!0}});window.addEventListener("testPassiveEventSupport",falseFn,opts),window.removeEventListener("testPassiveEventSupport",falseFn,opts)}catch(e){}return supportsPassiveOption}(),canvas$1=!!document.createElement("canvas").getContext,svg$1=!(!document.createElementNS||!svgCreate("svg").createSVGRect),inlineSvg=!!svg$1&&((div=document.createElement("div")).innerHTML="<svg/>","http://www.w3.org/2000/svg"===(div.firstChild&&div.firstChild.namespaceURI)),vml=!svg$1&&function(){try{var div=document.createElement("div");div.innerHTML='<v:shape adj="1"/>';var shape=div.firstChild;return shape.style.behavior="url(#default#VML)",shape&&"object"==typeof shape.adj}catch(e){return!1}}(),mac=0===navigator.platform.indexOf("Mac"),linux=0===navigator.platform.indexOf("Linux");function userAgentContains(str){return navigator.userAgent.toLowerCase().indexOf(str)>=0}var Browser={ie,ielt9,edge,webkit,android,android23,androidStock,opera,chrome,gecko,safari,phantom,opera12,win,ie3d,webkit3d,gecko3d,any3d,mobile,mobileWebkit,mobileWebkit3d,msPointer,pointer,touch,touchNative,mobileOpera,mobileGecko,retina,passiveEvents,canvas:canvas$1,svg:svg$1,vml,inlineSvg,mac,linux},POINTER_DOWN=Browser.msPointer?"MSPointerDown":"pointerdown",POINTER_MOVE=Browser.msPointer?"MSPointerMove":"pointermove",POINTER_UP=Browser.msPointer?"MSPointerUp":"pointerup",POINTER_CANCEL=Browser.msPointer?"MSPointerCancel":"pointercancel",pEvent={touchstart:POINTER_DOWN,touchmove:POINTER_MOVE,touchend:POINTER_UP,touchcancel:POINTER_CANCEL},handle={touchstart:_onPointerStart,touchmove:_handlePointer,touchend:_handlePointer,touchcancel:_handlePointer},_pointers={},_pointerDocListener=!1;function addPointerListener(obj,type,handler){return"touchstart"===type&&_addPointerDocListener(),handle[type]?(handler=handle[type].bind(this,handler),obj.addEventListener(pEvent[type],handler,!1),handler):(console.warn("wrong event specified:",type),falseFn)}function removePointerListener(obj,type,handler){pEvent[type]?obj.removeEventListener(pEvent[type],handler,!1):console.warn("wrong event specified:",type)}function _globalPointerDown(e){_pointers[e.pointerId]=e}function _globalPointerMove(e){_pointers[e.pointerId]&&(_pointers[e.pointerId]=e)}function _globalPointerUp(e){delete _pointers[e.pointerId]}function _addPointerDocListener(){_pointerDocListener||(document.addEventListener(POINTER_DOWN,_globalPointerDown,!0),document.addEventListener(POINTER_MOVE,_globalPointerMove,!0),document.addEventListener(POINTER_UP,_globalPointerUp,!0),document.addEventListener(POINTER_CANCEL,_globalPointerUp,!0),_pointerDocListener=!0)}function _handlePointer(handler,e){if(e.pointerType!==(e.MSPOINTER_TYPE_MOUSE||"mouse")){for(var i in e.touches=[],_pointers)e.touches.push(_pointers[i]);e.changedTouches=[e],handler(e)}}function _onPointerStart(handler,e){e.MSPOINTER_TYPE_TOUCH&&e.pointerType===e.MSPOINTER_TYPE_TOUCH&&preventDefault(e),_handlePointer(handler,e)}function makeDblclick(event){var prop,i,newEvent={};for(i in event)prop=event[i],newEvent[i]=prop&&prop.bind?prop.bind(event):prop;return event=newEvent,newEvent.type="dblclick",newEvent.detail=2,newEvent.isTrusted=!1,newEvent._simulated=!0,newEvent}var delay=200;function addDoubleTapListener(obj,handler){obj.addEventListener("dblclick",handler);var detail,last=0;function simDblclick(e){if(1===e.detail){if("mouse"!==e.pointerType&&(!e.sourceCapabilities||e.sourceCapabilities.firesTouchEvents)){var path=getPropagationPath(e);if(!path.some((function(el){return el instanceof HTMLLabelElement&&el.attributes.for}))||path.some((function(el){return el instanceof HTMLInputElement||el instanceof HTMLSelectElement}))){var now=Date.now();now-last<=delay?2==++detail&&handler(makeDblclick(e)):detail=1,last=now}}}else detail=e.detail}return obj.addEventListener("click",simDblclick),{dblclick:handler,simDblclick}}function removeDoubleTapListener(obj,handlers){obj.removeEventListener("dblclick",handlers.dblclick),obj.removeEventListener("click",handlers.simDblclick)}var disableTextSelection,enableTextSelection,_userSelect,_outlineElement,_outlineStyle,TRANSFORM=testProp(["transform","webkitTransform","OTransform","MozTransform","msTransform"]),TRANSITION=testProp(["webkitTransition","transition","OTransition","MozTransition","msTransition"]),TRANSITION_END="webkitTransition"===TRANSITION||"OTransition"===TRANSITION?TRANSITION+"End":"transitionend";function get(id){return"string"==typeof id?document.getElementById(id):id}function getStyle(el,style){var value=el.style[style]||el.currentStyle&&el.currentStyle[style];if((!value||"auto"===value)&&document.defaultView){var css=document.defaultView.getComputedStyle(el,null);value=css?css[style]:null}return"auto"===value?null:value}function create$1(tagName,className,container){var el=document.createElement(tagName);return el.className=className||"",container&&container.appendChild(el),el}function remove(el){var parent=el.parentNode;parent&&parent.removeChild(el)}function empty(el){for(;el.firstChild;)el.removeChild(el.firstChild)}function toFront(el){var parent=el.parentNode;parent&&parent.lastChild!==el&&parent.appendChild(el)}function toBack(el){var parent=el.parentNode;parent&&parent.firstChild!==el&&parent.insertBefore(el,parent.firstChild)}function hasClass(el,name){if(void 0!==el.classList)return el.classList.contains(name);var className=getClass(el);return className.length>0&&new RegExp("(^|\\s)"+name+"(\\s|$)").test(className)}function addClass(el,name){if(void 0!==el.classList)for(var classes=splitWords(name),i=0,len=classes.length;i<len;i++)el.classList.add(classes[i]);else if(!hasClass(el,name)){var className=getClass(el);setClass(el,(className?className+" ":"")+name)}}function removeClass(el,name){void 0!==el.classList?el.classList.remove(name):setClass(el,trim((" "+getClass(el)+" ").replace(" "+name+" "," ")))}function setClass(el,name){void 0===el.className.baseVal?el.className=name:el.className.baseVal=name}function getClass(el){return el.correspondingElement&&(el=el.correspondingElement),void 0===el.className.baseVal?el.className:el.className.baseVal}function setOpacity(el,value){"opacity"in el.style?el.style.opacity=value:"filter"in el.style&&_setOpacityIE(el,value)}function _setOpacityIE(el,value){var filter=!1,filterName="DXImageTransform.Microsoft.Alpha";try{filter=el.filters.item(filterName)}catch(e){if(1===value)return}value=Math.round(100*value),filter?(filter.Enabled=100!==value,filter.Opacity=value):el.style.filter+=" progid:"+filterName+"(opacity="+value+")"}function testProp(props){for(var style=document.documentElement.style,i=0;i<props.length;i++)if(props[i]in style)return props[i];return!1}function setTransform(el,offset,scale){var pos=offset||new Point(0,0);el.style[TRANSFORM]=(Browser.ie3d?"translate("+pos.x+"px,"+pos.y+"px)":"translate3d("+pos.x+"px,"+pos.y+"px,0)")+(scale?" scale("+scale+")":"")}function setPosition(el,point){el._leaflet_pos=point,Browser.any3d?setTransform(el,point):(el.style.left=point.x+"px",el.style.top=point.y+"px")}function getPosition(el){return el._leaflet_pos||new Point(0,0)}if("onselectstart"in document)disableTextSelection=function(){on(window,"selectstart",preventDefault)},enableTextSelection=function(){off(window,"selectstart",preventDefault)};else{var userSelectProperty=testProp(["userSelect","WebkitUserSelect","OUserSelect","MozUserSelect","msUserSelect"]);disableTextSelection=function(){if(userSelectProperty){var style=document.documentElement.style;_userSelect=style[userSelectProperty],style[userSelectProperty]="none"}},enableTextSelection=function(){userSelectProperty&&(document.documentElement.style[userSelectProperty]=_userSelect,_userSelect=void 0)}}function disableImageDrag(){on(window,"dragstart",preventDefault)}function enableImageDrag(){off(window,"dragstart",preventDefault)}function preventOutline(element){for(;-1===element.tabIndex;)element=element.parentNode;element.style&&(restoreOutline(),_outlineElement=element,_outlineStyle=element.style.outlineStyle,element.style.outlineStyle="none",on(window,"keydown",restoreOutline))}function restoreOutline(){_outlineElement&&(_outlineElement.style.outlineStyle=_outlineStyle,_outlineElement=void 0,_outlineStyle=void 0,off(window,"keydown",restoreOutline))}function getSizedParentNode(element){do{element=element.parentNode}while(!(element.offsetWidth&&element.offsetHeight||element===document.body));return element}function getScale(element){var rect=element.getBoundingClientRect();return{x:rect.width/element.offsetWidth||1,y:rect.height/element.offsetHeight||1,boundingClientRect:rect}}var DomUtil={__proto__:null,TRANSFORM,TRANSITION,TRANSITION_END,get,getStyle,create:create$1,remove,empty,toFront,toBack,hasClass,addClass,removeClass,setClass,getClass,setOpacity,testProp,setTransform,setPosition,getPosition,get disableTextSelection(){return disableTextSelection},get enableTextSelection(){return enableTextSelection},disableImageDrag,enableImageDrag,preventOutline,restoreOutline,getSizedParentNode,getScale};function on(obj,types,fn,context){if(types&&"object"==typeof types)for(var type in types)addOne(obj,type,types[type],fn);else for(var i=0,len=(types=splitWords(types)).length;i<len;i++)addOne(obj,types[i],fn,context);return this}var eventsKey="_leaflet_events";function off(obj,types,fn,context){if(1===arguments.length)batchRemove(obj),delete obj[eventsKey];else if(types&&"object"==typeof types)for(var type in types)removeOne(obj,type,types[type],fn);else if(types=splitWords(types),2===arguments.length)batchRemove(obj,(function(type){return-1!==indexOf(types,type)}));else for(var i=0,len=types.length;i<len;i++)removeOne(obj,types[i],fn,context);return this}function batchRemove(obj,filterFn){for(var id in obj[eventsKey]){var type=id.split(/\d/)[0];filterFn&&!filterFn(type)||removeOne(obj,type,null,null,id)}}var mouseSubst={mouseenter:"mouseover",mouseleave:"mouseout",wheel:!("onwheel"in window)&&"mousewheel"};function addOne(obj,type,fn,context){var id=type+stamp(fn)+(context?"_"+stamp(context):"");if(obj[eventsKey]&&obj[eventsKey][id])return this;var handler=function(e){return fn.call(context||obj,e||window.event)},originalHandler=handler;!Browser.touchNative&&Browser.pointer&&0===type.indexOf("touch")?handler=addPointerListener(obj,type,handler):Browser.touch&&"dblclick"===type?handler=addDoubleTapListener(obj,handler):"addEventListener"in obj?"touchstart"===type||"touchmove"===type||"wheel"===type||"mousewheel"===type?obj.addEventListener(mouseSubst[type]||type,handler,!!Browser.passiveEvents&&{passive:!1}):"mouseenter"===type||"mouseleave"===type?(handler=function(e){e=e||window.event,isExternalTarget(obj,e)&&originalHandler(e)},obj.addEventListener(mouseSubst[type],handler,!1)):obj.addEventListener(type,originalHandler,!1):obj.attachEvent("on"+type,handler),obj[eventsKey]=obj[eventsKey]||{},obj[eventsKey][id]=handler}function removeOne(obj,type,fn,context,id){id=id||type+stamp(fn)+(context?"_"+stamp(context):"");var handler=obj[eventsKey]&&obj[eventsKey][id];if(!handler)return this;!Browser.touchNative&&Browser.pointer&&0===type.indexOf("touch")?removePointerListener(obj,type,handler):Browser.touch&&"dblclick"===type?removeDoubleTapListener(obj,handler):"removeEventListener"in obj?obj.removeEventListener(mouseSubst[type]||type,handler,!1):obj.detachEvent("on"+type,handler),obj[eventsKey][id]=null}function stopPropagation(e){return e.stopPropagation?e.stopPropagation():e.originalEvent?e.originalEvent._stopped=!0:e.cancelBubble=!0,this}function disableScrollPropagation(el){return addOne(el,"wheel",stopPropagation),this}function disableClickPropagation(el){return on(el,"mousedown touchstart dblclick contextmenu",stopPropagation),el._leaflet_disable_click=!0,this}function preventDefault(e){return e.preventDefault?e.preventDefault():e.returnValue=!1,this}function stop(e){return preventDefault(e),stopPropagation(e),this}function getPropagationPath(ev){if(ev.composedPath)return ev.composedPath();for(var path=[],el=ev.target;el;)path.push(el),el=el.parentNode;return path}function getMousePosition(e,container){if(!container)return new Point(e.clientX,e.clientY);var scale=getScale(container),offset=scale.boundingClientRect;return new Point((e.clientX-offset.left)/scale.x-container.clientLeft,(e.clientY-offset.top)/scale.y-container.clientTop)}var wheelPxFactor=Browser.linux&&Browser.chrome?window.devicePixelRatio:Browser.mac?3*window.devicePixelRatio:window.devicePixelRatio>0?2*window.devicePixelRatio:1;function getWheelDelta(e){return Browser.edge?e.wheelDeltaY/2:e.deltaY&&0===e.deltaMode?-e.deltaY/wheelPxFactor:e.deltaY&&1===e.deltaMode?20*-e.deltaY:e.deltaY&&2===e.deltaMode?60*-e.deltaY:e.deltaX||e.deltaZ?0:e.wheelDelta?(e.wheelDeltaY||e.wheelDelta)/2:e.detail&&Math.abs(e.detail)<32765?20*-e.detail:e.detail?e.detail/-32765*60:0}function isExternalTarget(el,e){var related=e.relatedTarget;if(!related)return!0;try{for(;related&&related!==el;)related=related.parentNode}catch(err){return!1}return related!==el}var DomEvent={__proto__:null,on,off,stopPropagation,disableScrollPropagation,disableClickPropagation,preventDefault,stop,getPropagationPath,getMousePosition,getWheelDelta,isExternalTarget,addListener:on,removeListener:off},PosAnimation=Evented.extend({run:function(el,newPos,duration,easeLinearity){this.stop(),this._el=el,this._inProgress=!0,this._duration=duration||.25,this._easeOutPower=1/Math.max(easeLinearity||.5,.2),this._startPos=getPosition(el),this._offset=newPos.subtract(this._startPos),this._startTime=+new Date,this.fire("start"),this._animate()},stop:function(){this._inProgress&&(this._step(!0),this._complete())},_animate:function(){this._animId=requestAnimFrame(this._animate,this),this._step()},_step:function(round){var elapsed=+new Date-this._startTime,duration=1e3*this._duration;elapsed<duration?this._runFrame(this._easeOut(elapsed/duration),round):(this._runFrame(1),this._complete())},_runFrame:function(progress,round){var pos=this._startPos.add(this._offset.multiplyBy(progress));round&&pos._round(),setPosition(this._el,pos),this.fire("step")},_complete:function(){cancelAnimFrame(this._animId),this._inProgress=!1,this.fire("end")},_easeOut:function(t){return 1-Math.pow(1-t,this._easeOutPower)}}),Map=Evented.extend({options:{crs:EPSG3857,center:void 0,zoom:void 0,minZoom:void 0,maxZoom:void 0,layers:[],maxBounds:void 0,renderer:void 0,zoomAnimation:!0,zoomAnimationThreshold:4,fadeAnimation:!0,markerZoomAnimation:!0,transform3DLimit:8388608,zoomSnap:1,zoomDelta:1,trackResize:!0},initialize:function(id,options){options=setOptions(this,options),this._handlers=[],this._layers={},this._zoomBoundLayers={},this._sizeChanged=!0,this._initContainer(id),this._initLayout(),this._onResize=bind(this._onResize,this),this._initEvents(),options.maxBounds&&this.setMaxBounds(options.maxBounds),void 0!==options.zoom&&(this._zoom=this._limitZoom(options.zoom)),options.center&&void 0!==options.zoom&&this.setView(toLatLng(options.center),options.zoom,{reset:!0}),this.callInitHooks(),this._zoomAnimated=TRANSITION&&Browser.any3d&&!Browser.mobileOpera&&this.options.zoomAnimation,this._zoomAnimated&&(this._createAnimProxy(),on(this._proxy,TRANSITION_END,this._catchTransitionEnd,this)),this._addLayers(this.options.layers)},setView:function(center,zoom,options){return zoom=void 0===zoom?this._zoom:this._limitZoom(zoom),center=this._limitCenter(toLatLng(center),zoom,this.options.maxBounds),options=options||{},this._stop(),this._loaded&&!options.reset&&!0!==options&&(void 0!==options.animate&&(options.zoom=extend({animate:options.animate},options.zoom),options.pan=extend({animate:options.animate,duration:options.duration},options.pan)),this._zoom!==zoom?this._tryAnimatedZoom&&this._tryAnimatedZoom(center,zoom,options.zoom):this._tryAnimatedPan(center,options.pan))?(clearTimeout(this._sizeTimer),this):(this._resetView(center,zoom,options.pan&&options.pan.noMoveStart),this)},setZoom:function(zoom,options){return this._loaded?this.setView(this.getCenter(),zoom,{zoom:options}):(this._zoom=zoom,this)},zoomIn:function(delta,options){return delta=delta||(Browser.any3d?this.options.zoomDelta:1),this.setZoom(this._zoom+delta,options)},zoomOut:function(delta,options){return delta=delta||(Browser.any3d?this.options.zoomDelta:1),this.setZoom(this._zoom-delta,options)},setZoomAround:function(latlng,zoom,options){var scale=this.getZoomScale(zoom),viewHalf=this.getSize().divideBy(2),centerOffset=(latlng instanceof Point?latlng:this.latLngToContainerPoint(latlng)).subtract(viewHalf).multiplyBy(1-1/scale),newCenter=this.containerPointToLatLng(viewHalf.add(centerOffset));return this.setView(newCenter,zoom,{zoom:options})},_getBoundsCenterZoom:function(bounds,options){options=options||{},bounds=bounds.getBounds?bounds.getBounds():toLatLngBounds(bounds);var paddingTL=toPoint(options.paddingTopLeft||options.padding||[0,0]),paddingBR=toPoint(options.paddingBottomRight||options.padding||[0,0]),zoom=this.getBoundsZoom(bounds,!1,paddingTL.add(paddingBR));if((zoom="number"==typeof options.maxZoom?Math.min(options.maxZoom,zoom):zoom)===1/0)return{center:bounds.getCenter(),zoom};var paddingOffset=paddingBR.subtract(paddingTL).divideBy(2),swPoint=this.project(bounds.getSouthWest(),zoom),nePoint=this.project(bounds.getNorthEast(),zoom);return{center:this.unproject(swPoint.add(nePoint).divideBy(2).add(paddingOffset),zoom),zoom}},fitBounds:function(bounds,options){if(!(bounds=toLatLngBounds(bounds)).isValid())throw new Error("Bounds are not valid.");var target=this._getBoundsCenterZoom(bounds,options);return this.setView(target.center,target.zoom,options)},fitWorld:function(options){return this.fitBounds([[-90,-180],[90,180]],options)},panTo:function(center,options){return this.setView(center,this._zoom,{pan:options})},panBy:function(offset,options){if(options=options||{},!(offset=toPoint(offset).round()).x&&!offset.y)return this.fire("moveend");if(!0!==options.animate&&!this.getSize().contains(offset))return this._resetView(this.unproject(this.project(this.getCenter()).add(offset)),this.getZoom()),this;if(this._panAnim||(this._panAnim=new PosAnimation,this._panAnim.on({step:this._onPanTransitionStep,end:this._onPanTransitionEnd},this)),options.noMoveStart||this.fire("movestart"),!1!==options.animate){addClass(this._mapPane,"leaflet-pan-anim");var newPos=this._getMapPanePos().subtract(offset).round();this._panAnim.run(this._mapPane,newPos,options.duration||.25,options.easeLinearity)}else this._rawPanBy(offset),this.fire("move").fire("moveend");return this},flyTo:function(targetCenter,targetZoom,options){if(!1===(options=options||{}).animate||!Browser.any3d)return this.setView(targetCenter,targetZoom,options);this._stop();var from=this.project(this.getCenter()),to=this.project(targetCenter),size=this.getSize(),startZoom=this._zoom;targetCenter=toLatLng(targetCenter),targetZoom=void 0===targetZoom?startZoom:targetZoom;var w0=Math.max(size.x,size.y),w1=w0*this.getZoomScale(startZoom,targetZoom),u1=to.distanceTo(from)||1,rho=1.42,rho2=rho*rho;function r(i){var b=(w1*w1-w0*w0+(i?-1:1)*rho2*rho2*u1*u1)/(2*(i?w1:w0)*rho2*u1),sq=Math.sqrt(b*b+1)-b;return sq<1e-9?-18:Math.log(sq)}function sinh(n){return(Math.exp(n)-Math.exp(-n))/2}function cosh(n){return(Math.exp(n)+Math.exp(-n))/2}function tanh(n){return sinh(n)/cosh(n)}var r0=r(0);function w(s){return w0*(cosh(r0)/cosh(r0+rho*s))}function u(s){return w0*(cosh(r0)*tanh(r0+rho*s)-sinh(r0))/rho2}function easeOut(t){return 1-Math.pow(1-t,1.5)}var start=Date.now(),S=(r(1)-r0)/rho,duration=options.duration?1e3*options.duration:1e3*S*.8;function frame(){var t=(Date.now()-start)/duration,s=easeOut(t)*S;t<=1?(this._flyToFrame=requestAnimFrame(frame,this),this._move(this.unproject(from.add(to.subtract(from).multiplyBy(u(s)/u1)),startZoom),this.getScaleZoom(w0/w(s),startZoom),{flyTo:!0})):this._move(targetCenter,targetZoom)._moveEnd(!0)}return this._moveStart(!0,options.noMoveStart),frame.call(this),this},flyToBounds:function(bounds,options){var target=this._getBoundsCenterZoom(bounds,options);return this.flyTo(target.center,target.zoom,options)},setMaxBounds:function(bounds){return bounds=toLatLngBounds(bounds),this.listens("moveend",this._panInsideMaxBounds)&&this.off("moveend",this._panInsideMaxBounds),bounds.isValid()?(this.options.maxBounds=bounds,this._loaded&&this._panInsideMaxBounds(),this.on("moveend",this._panInsideMaxBounds)):(this.options.maxBounds=null,this)},setMinZoom:function(zoom){var oldZoom=this.options.minZoom;return this.options.minZoom=zoom,this._loaded&&oldZoom!==zoom&&(this.fire("zoomlevelschange"),this.getZoom()<this.options.minZoom)?this.setZoom(zoom):this},setMaxZoom:function(zoom){var oldZoom=this.options.maxZoom;return this.options.maxZoom=zoom,this._loaded&&oldZoom!==zoom&&(this.fire("zoomlevelschange"),this.getZoom()>this.options.maxZoom)?this.setZoom(zoom):this},panInsideBounds:function(bounds,options){this._enforcingBounds=!0;var center=this.getCenter(),newCenter=this._limitCenter(center,this._zoom,toLatLngBounds(bounds));return center.equals(newCenter)||this.panTo(newCenter,options),this._enforcingBounds=!1,this},panInside:function(latlng,options){var paddingTL=toPoint((options=options||{}).paddingTopLeft||options.padding||[0,0]),paddingBR=toPoint(options.paddingBottomRight||options.padding||[0,0]),pixelCenter=this.project(this.getCenter()),pixelPoint=this.project(latlng),pixelBounds=this.getPixelBounds(),paddedBounds=toBounds([pixelBounds.min.add(paddingTL),pixelBounds.max.subtract(paddingBR)]),paddedSize=paddedBounds.getSize();if(!paddedBounds.contains(pixelPoint)){this._enforcingBounds=!0;var centerOffset=pixelPoint.subtract(paddedBounds.getCenter()),offset=paddedBounds.extend(pixelPoint).getSize().subtract(paddedSize);pixelCenter.x+=centerOffset.x<0?-offset.x:offset.x,pixelCenter.y+=centerOffset.y<0?-offset.y:offset.y,this.panTo(this.unproject(pixelCenter),options),this._enforcingBounds=!1}return this},invalidateSize:function(options){if(!this._loaded)return this;options=extend({animate:!1,pan:!0},!0===options?{animate:!0}:options);var oldSize=this.getSize();this._sizeChanged=!0,this._lastCenter=null;var newSize=this.getSize(),oldCenter=oldSize.divideBy(2).round(),newCenter=newSize.divideBy(2).round(),offset=oldCenter.subtract(newCenter);return offset.x||offset.y?(options.animate&&options.pan?this.panBy(offset):(options.pan&&this._rawPanBy(offset),this.fire("move"),options.debounceMoveend?(clearTimeout(this._sizeTimer),this._sizeTimer=setTimeout(bind(this.fire,this,"moveend"),200)):this.fire("moveend")),this.fire("resize",{oldSize,newSize})):this},stop:function(){return this.setZoom(this._limitZoom(this._zoom)),this.options.zoomSnap||this.fire("viewreset"),this._stop()},locate:function(options){if(options=this._locateOptions=extend({timeout:1e4,watch:!1},options),!("geolocation"in navigator))return this._handleGeolocationError({code:0,message:"Geolocation not supported."}),this;var onResponse=bind(this._handleGeolocationResponse,this),onError=bind(this._handleGeolocationError,this);return options.watch?this._locationWatchId=navigator.geolocation.watchPosition(onResponse,onError,options):navigator.geolocation.getCurrentPosition(onResponse,onError,options),this},stopLocate:function(){return navigator.geolocation&&navigator.geolocation.clearWatch&&navigator.geolocation.clearWatch(this._locationWatchId),this._locateOptions&&(this._locateOptions.setView=!1),this},_handleGeolocationError:function(error){if(this._container._leaflet_id){var c=error.code,message=error.message||(1===c?"permission denied":2===c?"position unavailable":"timeout");this._locateOptions.setView&&!this._loaded&&this.fitWorld(),this.fire("locationerror",{code:c,message:"Geolocation error: "+message+"."})}},_handleGeolocationResponse:function(pos){if(this._container._leaflet_id){var latlng=new LatLng(pos.coords.latitude,pos.coords.longitude),bounds=latlng.toBounds(2*pos.coords.accuracy),options=this._locateOptions;if(options.setView){var zoom=this.getBoundsZoom(bounds);this.setView(latlng,options.maxZoom?Math.min(zoom,options.maxZoom):zoom)}var data={latlng,bounds,timestamp:pos.timestamp};for(var i in pos.coords)"number"==typeof pos.coords[i]&&(data[i]=pos.coords[i]);this.fire("locationfound",data)}},addHandler:function(name,HandlerClass){if(!HandlerClass)return this;var handler=this[name]=new HandlerClass(this);return this._handlers.push(handler),this.options[name]&&handler.enable(),this},remove:function(){if(this._initEvents(!0),this.options.maxBounds&&this.off("moveend",this._panInsideMaxBounds),this._containerId!==this._container._leaflet_id)throw new Error("Map container is being reused by another instance");try{delete this._container._leaflet_id,delete this._containerId}catch(e){this._container._leaflet_id=void 0,this._containerId=void 0}var i;for(i in void 0!==this._locationWatchId&&this.stopLocate(),this._stop(),remove(this._mapPane),this._clearControlPos&&this._clearControlPos(),this._resizeRequest&&(cancelAnimFrame(this._resizeRequest),this._resizeRequest=null),this._clearHandlers(),this._loaded&&this.fire("unload"),this._layers)this._layers[i].remove();for(i in this._panes)remove(this._panes[i]);return this._layers=[],this._panes=[],delete this._mapPane,delete this._renderer,this},createPane:function(name,container){var pane=create$1("div","leaflet-pane"+(name?" leaflet-"+name.replace("Pane","")+"-pane":""),container||this._mapPane);return name&&(this._panes[name]=pane),pane},getCenter:function(){return this._checkIfLoaded(),this._lastCenter&&!this._moved()?this._lastCenter.clone():this.layerPointToLatLng(this._getCenterLayerPoint())},getZoom:function(){return this._zoom},getBounds:function(){var bounds=this.getPixelBounds();return new LatLngBounds(this.unproject(bounds.getBottomLeft()),this.unproject(bounds.getTopRight()))},getMinZoom:function(){return void 0===this.options.minZoom?this._layersMinZoom||0:this.options.minZoom},getMaxZoom:function(){return void 0===this.options.maxZoom?void 0===this._layersMaxZoom?1/0:this._layersMaxZoom:this.options.maxZoom},getBoundsZoom:function(bounds,inside,padding){bounds=toLatLngBounds(bounds),padding=toPoint(padding||[0,0]);var zoom=this.getZoom()||0,min=this.getMinZoom(),max=this.getMaxZoom(),nw=bounds.getNorthWest(),se=bounds.getSouthEast(),size=this.getSize().subtract(padding),boundsSize=toBounds(this.project(se,zoom),this.project(nw,zoom)).getSize(),snap=Browser.any3d?this.options.zoomSnap:1,scalex=size.x/boundsSize.x,scaley=size.y/boundsSize.y,scale=inside?Math.max(scalex,scaley):Math.min(scalex,scaley);return zoom=this.getScaleZoom(scale,zoom),snap&&(zoom=Math.round(zoom/(snap/100))*(snap/100),zoom=inside?Math.ceil(zoom/snap)*snap:Math.floor(zoom/snap)*snap),Math.max(min,Math.min(max,zoom))},getSize:function(){return this._size&&!this._sizeChanged||(this._size=new Point(this._container.clientWidth||0,this._container.clientHeight||0),this._sizeChanged=!1),this._size.clone()},getPixelBounds:function(center,zoom){var topLeftPoint=this._getTopLeftPoint(center,zoom);return new Bounds(topLeftPoint,topLeftPoint.add(this.getSize()))},getPixelOrigin:function(){return this._checkIfLoaded(),this._pixelOrigin},getPixelWorldBounds:function(zoom){return this.options.crs.getProjectedBounds(void 0===zoom?this.getZoom():zoom)},getPane:function(pane){return"string"==typeof pane?this._panes[pane]:pane},getPanes:function(){return this._panes},getContainer:function(){return this._container},getZoomScale:function(toZoom,fromZoom){var crs=this.options.crs;return fromZoom=void 0===fromZoom?this._zoom:fromZoom,crs.scale(toZoom)/crs.scale(fromZoom)},getScaleZoom:function(scale,fromZoom){var crs=this.options.crs;fromZoom=void 0===fromZoom?this._zoom:fromZoom;var zoom=crs.zoom(scale*crs.scale(fromZoom));return isNaN(zoom)?1/0:zoom},project:function(latlng,zoom){return zoom=void 0===zoom?this._zoom:zoom,this.options.crs.latLngToPoint(toLatLng(latlng),zoom)},unproject:function(point,zoom){return zoom=void 0===zoom?this._zoom:zoom,this.options.crs.pointToLatLng(toPoint(point),zoom)},layerPointToLatLng:function(point){var projectedPoint=toPoint(point).add(this.getPixelOrigin());return this.unproject(projectedPoint)},latLngToLayerPoint:function(latlng){return this.project(toLatLng(latlng))._round()._subtract(this.getPixelOrigin())},wrapLatLng:function(latlng){return this.options.crs.wrapLatLng(toLatLng(latlng))},wrapLatLngBounds:function(latlng){return this.options.crs.wrapLatLngBounds(toLatLngBounds(latlng))},distance:function(latlng1,latlng2){return this.options.crs.distance(toLatLng(latlng1),toLatLng(latlng2))},containerPointToLayerPoint:function(point){return toPoint(point).subtract(this._getMapPanePos())},layerPointToContainerPoint:function(point){return toPoint(point).add(this._getMapPanePos())},containerPointToLatLng:function(point){var layerPoint=this.containerPointToLayerPoint(toPoint(point));return this.layerPointToLatLng(layerPoint)},latLngToContainerPoint:function(latlng){return this.layerPointToContainerPoint(this.latLngToLayerPoint(toLatLng(latlng)))},mouseEventToContainerPoint:function(e){return getMousePosition(e,this._container)},mouseEventToLayerPoint:function(e){return this.containerPointToLayerPoint(this.mouseEventToContainerPoint(e))},mouseEventToLatLng:function(e){return this.layerPointToLatLng(this.mouseEventToLayerPoint(e))},_initContainer:function(id){var container=this._container=get(id);if(!container)throw new Error("Map container not found.");if(container._leaflet_id)throw new Error("Map container is already initialized.");on(container,"scroll",this._onScroll,this),this._containerId=stamp(container)},_initLayout:function(){var container=this._container;this._fadeAnimated=this.options.fadeAnimation&&Browser.any3d,addClass(container,"leaflet-container"+(Browser.touch?" leaflet-touch":"")+(Browser.retina?" leaflet-retina":"")+(Browser.ielt9?" leaflet-oldie":"")+(Browser.safari?" leaflet-safari":"")+(this._fadeAnimated?" leaflet-fade-anim":""));var position=getStyle(container,"position");"absolute"!==position&&"relative"!==position&&"fixed"!==position&&"sticky"!==position&&(container.style.position="relative"),this._initPanes(),this._initControlPos&&this._initControlPos()},_initPanes:function(){var panes=this._panes={};this._paneRenderers={},this._mapPane=this.createPane("mapPane",this._container),setPosition(this._mapPane,new Point(0,0)),this.createPane("tilePane"),this.createPane("overlayPane"),this.createPane("shadowPane"),this.createPane("markerPane"),this.createPane("tooltipPane"),this.createPane("popupPane"),this.options.markerZoomAnimation||(addClass(panes.markerPane,"leaflet-zoom-hide"),addClass(panes.shadowPane,"leaflet-zoom-hide"))},_resetView:function(center,zoom,noMoveStart){setPosition(this._mapPane,new Point(0,0));var loading=!this._loaded;this._loaded=!0,zoom=this._limitZoom(zoom),this.fire("viewprereset");var zoomChanged=this._zoom!==zoom;this._moveStart(zoomChanged,noMoveStart)._move(center,zoom)._moveEnd(zoomChanged),this.fire("viewreset"),loading&&this.fire("load")},_moveStart:function(zoomChanged,noMoveStart){return zoomChanged&&this.fire("zoomstart"),noMoveStart||this.fire("movestart"),this},_move:function(center,zoom,data,supressEvent){void 0===zoom&&(zoom=this._zoom);var zoomChanged=this._zoom!==zoom;return this._zoom=zoom,this._lastCenter=center,this._pixelOrigin=this._getNewPixelOrigin(center),supressEvent?data&&data.pinch&&this.fire("zoom",data):((zoomChanged||data&&data.pinch)&&this.fire("zoom",data),this.fire("move",data)),this},_moveEnd:function(zoomChanged){return zoomChanged&&this.fire("zoomend"),this.fire("moveend")},_stop:function(){return cancelAnimFrame(this._flyToFrame),this._panAnim&&this._panAnim.stop(),this},_rawPanBy:function(offset){setPosition(this._mapPane,this._getMapPanePos().subtract(offset))},_getZoomSpan:function(){return this.getMaxZoom()-this.getMinZoom()},_panInsideMaxBounds:function(){this._enforcingBounds||this.panInsideBounds(this.options.maxBounds)},_checkIfLoaded:function(){if(!this._loaded)throw new Error("Set map center and zoom first.")},_initEvents:function(remove){this._targets={},this._targets[stamp(this._container)]=this;var onOff=remove?off:on;onOff(this._container,"click dblclick mousedown mouseup mouseover mouseout mousemove contextmenu keypress keydown keyup",this._handleDOMEvent,this),this.options.trackResize&&onOff(window,"resize",this._onResize,this),Browser.any3d&&this.options.transform3DLimit&&(remove?this.off:this.on).call(this,"moveend",this._onMoveEnd)},_onResize:function(){cancelAnimFrame(this._resizeRequest),this._resizeRequest=requestAnimFrame((function(){this.invalidateSize({debounceMoveend:!0})}),this)},_onScroll:function(){this._container.scrollTop=0,this._container.scrollLeft=0},_onMoveEnd:function(){var pos=this._getMapPanePos();Math.max(Math.abs(pos.x),Math.abs(pos.y))>=this.options.transform3DLimit&&this._resetView(this.getCenter(),this.getZoom())},_findEventTargets:function(e,type){for(var target,targets=[],isHover="mouseout"===type||"mouseover"===type,src=e.target||e.srcElement,dragging=!1;src;){if((target=this._targets[stamp(src)])&&("click"===type||"preclick"===type)&&this._draggableMoved(target)){dragging=!0;break}if(target&&target.listens(type,!0)){if(isHover&&!isExternalTarget(src,e))break;if(targets.push(target),isHover)break}if(src===this._container)break;src=src.parentNode}return targets.length||dragging||isHover||!this.listens(type,!0)||(targets=[this]),targets},_isClickDisabled:function(el){for(;el&&el!==this._container;){if(el._leaflet_disable_click)return!0;el=el.parentNode}},_handleDOMEvent:function(e){var el=e.target||e.srcElement;if(!(!this._loaded||el._leaflet_disable_events||"click"===e.type&&this._isClickDisabled(el))){var type=e.type;"mousedown"===type&&preventOutline(el),this._fireDOMEvent(e,type)}},_mouseEvents:["click","dblclick","mouseover","mouseout","contextmenu"],_fireDOMEvent:function(e,type,canvasTargets){if("click"===e.type){var synth=extend({},e);synth.type="preclick",this._fireDOMEvent(synth,synth.type,canvasTargets)}var targets=this._findEventTargets(e,type);if(canvasTargets){for(var filtered=[],i=0;i<canvasTargets.length;i++)canvasTargets[i].listens(type,!0)&&filtered.push(canvasTargets[i]);targets=filtered.concat(targets)}if(targets.length){"contextmenu"===type&&preventDefault(e);var target=targets[0],data={originalEvent:e};if("keypress"!==e.type&&"keydown"!==e.type&&"keyup"!==e.type){var isMarker=target.getLatLng&&(!target._radius||target._radius<=10);data.containerPoint=isMarker?this.latLngToContainerPoint(target.getLatLng()):this.mouseEventToContainerPoint(e),data.layerPoint=this.containerPointToLayerPoint(data.containerPoint),data.latlng=isMarker?target.getLatLng():this.layerPointToLatLng(data.layerPoint)}for(i=0;i<targets.length;i++)if(targets[i].fire(type,data,!0),data.originalEvent._stopped||!1===targets[i].options.bubblingMouseEvents&&-1!==indexOf(this._mouseEvents,type))return}},_draggableMoved:function(obj){return(obj=obj.dragging&&obj.dragging.enabled()?obj:this).dragging&&obj.dragging.moved()||this.boxZoom&&this.boxZoom.moved()},_clearHandlers:function(){for(var i=0,len=this._handlers.length;i<len;i++)this._handlers[i].disable()},whenReady:function(callback,context){return this._loaded?callback.call(context||this,{target:this}):this.on("load",callback,context),this},_getMapPanePos:function(){return getPosition(this._mapPane)||new Point(0,0)},_moved:function(){var pos=this._getMapPanePos();return pos&&!pos.equals([0,0])},_getTopLeftPoint:function(center,zoom){return(center&&void 0!==zoom?this._getNewPixelOrigin(center,zoom):this.getPixelOrigin()).subtract(this._getMapPanePos())},_getNewPixelOrigin:function(center,zoom){var viewHalf=this.getSize()._divideBy(2);return this.project(center,zoom)._subtract(viewHalf)._add(this._getMapPanePos())._round()},_latLngToNewLayerPoint:function(latlng,zoom,center){var topLeft=this._getNewPixelOrigin(center,zoom);return this.project(latlng,zoom)._subtract(topLeft)},_latLngBoundsToNewLayerBounds:function(latLngBounds,zoom,center){var topLeft=this._getNewPixelOrigin(center,zoom);return toBounds([this.project(latLngBounds.getSouthWest(),zoom)._subtract(topLeft),this.project(latLngBounds.getNorthWest(),zoom)._subtract(topLeft),this.project(latLngBounds.getSouthEast(),zoom)._subtract(topLeft),this.project(latLngBounds.getNorthEast(),zoom)._subtract(topLeft)])},_getCenterLayerPoint:function(){return this.containerPointToLayerPoint(this.getSize()._divideBy(2))},_getCenterOffset:function(latlng){return this.latLngToLayerPoint(latlng).subtract(this._getCenterLayerPoint())},_limitCenter:function(center,zoom,bounds){if(!bounds)return center;var centerPoint=this.project(center,zoom),viewHalf=this.getSize().divideBy(2),viewBounds=new Bounds(centerPoint.subtract(viewHalf),centerPoint.add(viewHalf)),offset=this._getBoundsOffset(viewBounds,bounds,zoom);return Math.abs(offset.x)<=1&&Math.abs(offset.y)<=1?center:this.unproject(centerPoint.add(offset),zoom)},_limitOffset:function(offset,bounds){if(!bounds)return offset;var viewBounds=this.getPixelBounds(),newBounds=new Bounds(viewBounds.min.add(offset),viewBounds.max.add(offset));return offset.add(this._getBoundsOffset(newBounds,bounds))},_getBoundsOffset:function(pxBounds,maxBounds,zoom){var projectedMaxBounds=toBounds(this.project(maxBounds.getNorthEast(),zoom),this.project(maxBounds.getSouthWest(),zoom)),minOffset=projectedMaxBounds.min.subtract(pxBounds.min),maxOffset=projectedMaxBounds.max.subtract(pxBounds.max);return new Point(this._rebound(minOffset.x,-maxOffset.x),this._rebound(minOffset.y,-maxOffset.y))},_rebound:function(left,right){return left+right>0?Math.round(left-right)/2:Math.max(0,Math.ceil(left))-Math.max(0,Math.floor(right))},_limitZoom:function(zoom){var min=this.getMinZoom(),max=this.getMaxZoom(),snap=Browser.any3d?this.options.zoomSnap:1;return snap&&(zoom=Math.round(zoom/snap)*snap),Math.max(min,Math.min(max,zoom))},_onPanTransitionStep:function(){this.fire("move")},_onPanTransitionEnd:function(){removeClass(this._mapPane,"leaflet-pan-anim"),this.fire("moveend")},_tryAnimatedPan:function(center,options){var offset=this._getCenterOffset(center)._trunc();return!(!0!==(options&&options.animate)&&!this.getSize().contains(offset)||(this.panBy(offset,options),0))},_createAnimProxy:function(){var proxy=this._proxy=create$1("div","leaflet-proxy leaflet-zoom-animated");this._panes.mapPane.appendChild(proxy),this.on("zoomanim",(function(e){var prop=TRANSFORM,transform=this._proxy.style[prop];setTransform(this._proxy,this.project(e.center,e.zoom),this.getZoomScale(e.zoom,1)),transform===this._proxy.style[prop]&&this._animatingZoom&&this._onZoomTransitionEnd()}),this),this.on("load moveend",this._animMoveEnd,this),this._on("unload",this._destroyAnimProxy,this)},_destroyAnimProxy:function(){remove(this._proxy),this.off("load moveend",this._animMoveEnd,this),delete this._proxy},_animMoveEnd:function(){var c=this.getCenter(),z=this.getZoom();setTransform(this._proxy,this.project(c,z),this.getZoomScale(z,1))},_catchTransitionEnd:function(e){this._animatingZoom&&e.propertyName.indexOf("transform")>=0&&this._onZoomTransitionEnd()},_nothingToAnimate:function(){return!this._container.getElementsByClassName("leaflet-zoom-animated").length},_tryAnimatedZoom:function(center,zoom,options){if(this._animatingZoom)return!0;if(options=options||{},!this._zoomAnimated||!1===options.animate||this._nothingToAnimate()||Math.abs(zoom-this._zoom)>this.options.zoomAnimationThreshold)return!1;var scale=this.getZoomScale(zoom),offset=this._getCenterOffset(center)._divideBy(1-1/scale);return!(!0!==options.animate&&!this.getSize().contains(offset)||(requestAnimFrame((function(){this._moveStart(!0,options.noMoveStart||!1)._animateZoom(center,zoom,!0)}),this),0))},_animateZoom:function(center,zoom,startAnim,noUpdate){this._mapPane&&(startAnim&&(this._animatingZoom=!0,this._animateToCenter=center,this._animateToZoom=zoom,addClass(this._mapPane,"leaflet-zoom-anim")),this.fire("zoomanim",{center,zoom,noUpdate}),this._tempFireZoomEvent||(this._tempFireZoomEvent=this._zoom!==this._animateToZoom),this._move(this._animateToCenter,this._animateToZoom,void 0,!0),setTimeout(bind(this._onZoomTransitionEnd,this),250))},_onZoomTransitionEnd:function(){this._animatingZoom&&(this._mapPane&&removeClass(this._mapPane,"leaflet-zoom-anim"),this._animatingZoom=!1,this._move(this._animateToCenter,this._animateToZoom,void 0,!0),this._tempFireZoomEvent&&this.fire("zoom"),delete this._tempFireZoomEvent,this.fire("move"),this._moveEnd(!0))}});function createMap(id,options){return new Map(id,options)}var Control=Class.extend({options:{position:"topright"},initialize:function(options){setOptions(this,options)},getPosition:function(){return this.options.position},setPosition:function(position){var map=this._map;return map&&map.removeControl(this),this.options.position=position,map&&map.addControl(this),this},getContainer:function(){return this._container},addTo:function(map){this.remove(),this._map=map;var container=this._container=this.onAdd(map),pos=this.getPosition(),corner=map._controlCorners[pos];return addClass(container,"leaflet-control"),-1!==pos.indexOf("bottom")?corner.insertBefore(container,corner.firstChild):corner.appendChild(container),this._map.on("unload",this.remove,this),this},remove:function(){return this._map?(remove(this._container),this.onRemove&&this.onRemove(this._map),this._map.off("unload",this.remove,this),this._map=null,this):this},_refocusOnMap:function(e){this._map&&e&&e.screenX>0&&e.screenY>0&&this._map.getContainer().focus()}}),control=function(options){return new Control(options)};Map.include({addControl:function(control){return control.addTo(this),this},removeControl:function(control){return control.remove(),this},_initControlPos:function(){var corners=this._controlCorners={},l="leaflet-",container=this._controlContainer=create$1("div",l+"control-container",this._container);function createCorner(vSide,hSide){var className=l+vSide+" "+l+hSide;corners[vSide+hSide]=create$1("div",className,container)}createCorner("top","left"),createCorner("top","right"),createCorner("bottom","left"),createCorner("bottom","right")},_clearControlPos:function(){for(var i in this._controlCorners)remove(this._controlCorners[i]);remove(this._controlContainer),delete this._controlCorners,delete this._controlContainer}});var Layers=Control.extend({options:{collapsed:!0,position:"topright",autoZIndex:!0,hideSingleBase:!1,sortLayers:!1,sortFunction:function(layerA,layerB,nameA,nameB){return nameA<nameB?-1:nameB<nameA?1:0}},initialize:function(baseLayers,overlays,options){for(var i in setOptions(this,options),this._layerControlInputs=[],this._layers=[],this._lastZIndex=0,this._handlingClick=!1,this._preventClick=!1,baseLayers)this._addLayer(baseLayers[i],i);for(i in overlays)this._addLayer(overlays[i],i,!0)},onAdd:function(map){this._initLayout(),this._update(),this._map=map,map.on("zoomend",this._checkDisabledLayers,this);for(var i=0;i<this._layers.length;i++)this._layers[i].layer.on("add remove",this._onLayerChange,this);return this._container},addTo:function(map){return Control.prototype.addTo.call(this,map),this._expandIfNotCollapsed()},onRemove:function(){this._map.off("zoomend",this._checkDisabledLayers,this);for(var i=0;i<this._layers.length;i++)this._layers[i].layer.off("add remove",this._onLayerChange,this)},addBaseLayer:function(layer,name){return this._addLayer(layer,name),this._map?this._update():this},addOverlay:function(layer,name){return this._addLayer(layer,name,!0),this._map?this._update():this},removeLayer:function(layer){layer.off("add remove",this._onLayerChange,this);var obj=this._getLayer(stamp(layer));return obj&&this._layers.splice(this._layers.indexOf(obj),1),this._map?this._update():this},expand:function(){addClass(this._container,"leaflet-control-layers-expanded"),this._section.style.height=null;var acceptableHeight=this._map.getSize().y-(this._container.offsetTop+50);return acceptableHeight<this._section.clientHeight?(addClass(this._section,"leaflet-control-layers-scrollbar"),this._section.style.height=acceptableHeight+"px"):removeClass(this._section,"leaflet-control-layers-scrollbar"),this._checkDisabledLayers(),this},collapse:function(){return removeClass(this._container,"leaflet-control-layers-expanded"),this},_initLayout:function(){var className="leaflet-control-layers",container=this._container=create$1("div",className),collapsed=this.options.collapsed;container.setAttribute("aria-haspopup",!0),disableClickPropagation(container),disableScrollPropagation(container);var section=this._section=create$1("section",className+"-list");collapsed&&(this._map.on("click",this.collapse,this),on(container,{mouseenter:this._expandSafely,mouseleave:this.collapse},this));var link=this._layersLink=create$1("a",className+"-toggle",container);link.href="#",link.title="Layers",link.setAttribute("role","button"),on(link,{keydown:function(e){13===e.keyCode&&this._expandSafely()},click:function(e){preventDefault(e),this._expandSafely()}},this),collapsed||this.expand(),this._baseLayersList=create$1("div",className+"-base",section),this._separator=create$1("div",className+"-separator",section),this._overlaysList=create$1("div",className+"-overlays",section),container.appendChild(section)},_getLayer:function(id){for(var i=0;i<this._layers.length;i++)if(this._layers[i]&&stamp(this._layers[i].layer)===id)return this._layers[i]},_addLayer:function(layer,name,overlay){this._map&&layer.on("add remove",this._onLayerChange,this),this._layers.push({layer,name,overlay}),this.options.sortLayers&&this._layers.sort(bind((function(a,b){return this.options.sortFunction(a.layer,b.layer,a.name,b.name)}),this)),this.options.autoZIndex&&layer.setZIndex&&(this._lastZIndex++,layer.setZIndex(this._lastZIndex)),this._expandIfNotCollapsed()},_update:function(){if(!this._container)return this;empty(this._baseLayersList),empty(this._overlaysList),this._layerControlInputs=[];var baseLayersPresent,overlaysPresent,i,obj,baseLayersCount=0;for(i=0;i<this._layers.length;i++)obj=this._layers[i],this._addItem(obj),overlaysPresent=overlaysPresent||obj.overlay,baseLayersPresent=baseLayersPresent||!obj.overlay,baseLayersCount+=obj.overlay?0:1;return this.options.hideSingleBase&&(baseLayersPresent=baseLayersPresent&&baseLayersCount>1,this._baseLayersList.style.display=baseLayersPresent?"":"none"),this._separator.style.display=overlaysPresent&&baseLayersPresent?"":"none",this},_onLayerChange:function(e){this._handlingClick||this._update();var obj=this._getLayer(stamp(e.target)),type=obj.overlay?"add"===e.type?"overlayadd":"overlayremove":"add"===e.type?"baselayerchange":null;type&&this._map.fire(type,obj)},_createRadioElement:function(name,checked){var radioHtml='<input type="radio" class="leaflet-control-layers-selector" name="'+name+'"'+(checked?' checked="checked"':"")+"/>",radioFragment=document.createElement("div");return radioFragment.innerHTML=radioHtml,radioFragment.firstChild},_addItem:function(obj){var input,label=document.createElement("label"),checked=this._map.hasLayer(obj.layer);obj.overlay?((input=document.createElement("input")).type="checkbox",input.className="leaflet-control-layers-selector",input.defaultChecked=checked):input=this._createRadioElement("leaflet-base-layers_"+stamp(this),checked),this._layerControlInputs.push(input),input.layerId=stamp(obj.layer),on(input,"click",this._onInputClick,this);var name=document.createElement("span");name.innerHTML=" "+obj.name;var holder=document.createElement("span");return label.appendChild(holder),holder.appendChild(input),holder.appendChild(name),(obj.overlay?this._overlaysList:this._baseLayersList).appendChild(label),this._checkDisabledLayers(),label},_onInputClick:function(){if(!this._preventClick){var input,layer,inputs=this._layerControlInputs,addedLayers=[],removedLayers=[];this._handlingClick=!0;for(var i=inputs.length-1;i>=0;i--)input=inputs[i],layer=this._getLayer(input.layerId).layer,input.checked?addedLayers.push(layer):input.checked||removedLayers.push(layer);for(i=0;i<removedLayers.length;i++)this._map.hasLayer(removedLayers[i])&&this._map.removeLayer(removedLayers[i]);for(i=0;i<addedLayers.length;i++)this._map.hasLayer(addedLayers[i])||this._map.addLayer(addedLayers[i]);this._handlingClick=!1,this._refocusOnMap()}},_checkDisabledLayers:function(){for(var input,layer,inputs=this._layerControlInputs,zoom=this._map.getZoom(),i=inputs.length-1;i>=0;i--)input=inputs[i],layer=this._getLayer(input.layerId).layer,input.disabled=void 0!==layer.options.minZoom&&zoom<layer.options.minZoom||void 0!==layer.options.maxZoom&&zoom>layer.options.maxZoom},_expandIfNotCollapsed:function(){return this._map&&!this.options.collapsed&&this.expand(),this},_expandSafely:function(){var section=this._section;this._preventClick=!0,on(section,"click",preventDefault),this.expand();var that=this;setTimeout((function(){off(section,"click",preventDefault),that._preventClick=!1}))}}),layers=function(baseLayers,overlays,options){return new Layers(baseLayers,overlays,options)},Zoom=Control.extend({options:{position:"topleft",zoomInText:'<span aria-hidden="true">+</span>',zoomInTitle:"Zoom in",zoomOutText:'<span aria-hidden="true">&#x2212;</span>',zoomOutTitle:"Zoom out"},onAdd:function(map){var zoomName="leaflet-control-zoom",container=create$1("div",zoomName+" leaflet-bar"),options=this.options;return this._zoomInButton=this._createButton(options.zoomInText,options.zoomInTitle,zoomName+"-in",container,this._zoomIn),this._zoomOutButton=this._createButton(options.zoomOutText,options.zoomOutTitle,zoomName+"-out",container,this._zoomOut),this._updateDisabled(),map.on("zoomend zoomlevelschange",this._updateDisabled,this),container},onRemove:function(map){map.off("zoomend zoomlevelschange",this._updateDisabled,this)},disable:function(){return this._disabled=!0,this._updateDisabled(),this},enable:function(){return this._disabled=!1,this._updateDisabled(),this},_zoomIn:function(e){!this._disabled&&this._map._zoom<this._map.getMaxZoom()&&this._map.zoomIn(this._map.options.zoomDelta*(e.shiftKey?3:1))},_zoomOut:function(e){!this._disabled&&this._map._zoom>this._map.getMinZoom()&&this._map.zoomOut(this._map.options.zoomDelta*(e.shiftKey?3:1))},_createButton:function(html,title,className,container,fn){var link=create$1("a",className,container);return link.innerHTML=html,link.href="#",link.title=title,link.setAttribute("role","button"),link.setAttribute("aria-label",title),disableClickPropagation(link),on(link,"click",stop),on(link,"click",fn,this),on(link,"click",this._refocusOnMap,this),link},_updateDisabled:function(){var map=this._map,className="leaflet-disabled";removeClass(this._zoomInButton,className),removeClass(this._zoomOutButton,className),this._zoomInButton.setAttribute("aria-disabled","false"),this._zoomOutButton.setAttribute("aria-disabled","false"),(this._disabled||map._zoom===map.getMinZoom())&&(addClass(this._zoomOutButton,className),this._zoomOutButton.setAttribute("aria-disabled","true")),(this._disabled||map._zoom===map.getMaxZoom())&&(addClass(this._zoomInButton,className),this._zoomInButton.setAttribute("aria-disabled","true"))}});Map.mergeOptions({zoomControl:!0}),Map.addInitHook((function(){this.options.zoomControl&&(this.zoomControl=new Zoom,this.addControl(this.zoomControl))}));var zoom=function(options){return new Zoom(options)},Scale=Control.extend({options:{position:"bottomleft",maxWidth:100,metric:!0,imperial:!0},onAdd:function(map){var className="leaflet-control-scale",container=create$1("div",className),options=this.options;return this._addScales(options,className+"-line",container),map.on(options.updateWhenIdle?"moveend":"move",this._update,this),map.whenReady(this._update,this),container},onRemove:function(map){map.off(this.options.updateWhenIdle?"moveend":"move",this._update,this)},_addScales:function(options,className,container){options.metric&&(this._mScale=create$1("div",className,container)),options.imperial&&(this._iScale=create$1("div",className,container))},_update:function(){var map=this._map,y=map.getSize().y/2,maxMeters=map.distance(map.containerPointToLatLng([0,y]),map.containerPointToLatLng([this.options.maxWidth,y]));this._updateScales(maxMeters)},_updateScales:function(maxMeters){this.options.metric&&maxMeters&&this._updateMetric(maxMeters),this.options.imperial&&maxMeters&&this._updateImperial(maxMeters)},_updateMetric:function(maxMeters){var meters=this._getRoundNum(maxMeters),label=meters<1e3?meters+" m":meters/1e3+" km";this._updateScale(this._mScale,label,meters/maxMeters)},_updateImperial:function(maxMeters){var maxMiles,miles,feet,maxFeet=3.2808399*maxMeters;maxFeet>5280?(maxMiles=maxFeet/5280,miles=this._getRoundNum(maxMiles),this._updateScale(this._iScale,miles+" mi",miles/maxMiles)):(feet=this._getRoundNum(maxFeet),this._updateScale(this._iScale,feet+" ft",feet/maxFeet))},_updateScale:function(scale,text,ratio){scale.style.width=Math.round(this.options.maxWidth*ratio)+"px",scale.innerHTML=text},_getRoundNum:function(num){var pow10=Math.pow(10,(Math.floor(num)+"").length-1),d=num/pow10;return pow10*(d=d>=10?10:d>=5?5:d>=3?3:d>=2?2:1)}}),scale=function(options){return new Scale(options)},ukrainianFlag='<svg aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="12" height="8" viewBox="0 0 12 8" class="leaflet-attribution-flag"><path fill="#4C7BE1" d="M0 0h12v4H0z"/><path fill="#FFD500" d="M0 4h12v3H0z"/><path fill="#E0BC00" d="M0 7h12v1H0z"/></svg>',Attribution=Control.extend({options:{position:"bottomright",prefix:'<a href="https://leafletjs.com" title="A JavaScript library for interactive maps">'+(Browser.inlineSvg?ukrainianFlag+" ":"")+"Leaflet</a>"},initialize:function(options){setOptions(this,options),this._attributions={}},onAdd:function(map){for(var i in map.attributionControl=this,this._container=create$1("div","leaflet-control-attribution"),disableClickPropagation(this._container),map._layers)map._layers[i].getAttribution&&this.addAttribution(map._layers[i].getAttribution());return this._update(),map.on("layeradd",this._addAttribution,this),this._container},onRemove:function(map){map.off("layeradd",this._addAttribution,this)},_addAttribution:function(ev){ev.layer.getAttribution&&(this.addAttribution(ev.layer.getAttribution()),ev.layer.once("remove",(function(){this.removeAttribution(ev.layer.getAttribution())}),this))},setPrefix:function(prefix){return this.options.prefix=prefix,this._update(),this},addAttribution:function(text){return text?(this._attributions[text]||(this._attributions[text]=0),this._attributions[text]++,this._update(),this):this},removeAttribution:function(text){return text?(this._attributions[text]&&(this._attributions[text]--,this._update()),this):this},_update:function(){if(this._map){var attribs=[];for(var i in this._attributions)this._attributions[i]&&attribs.push(i);var prefixAndAttribs=[];this.options.prefix&&prefixAndAttribs.push(this.options.prefix),attribs.length&&prefixAndAttribs.push(attribs.join(", ")),this._container.innerHTML=prefixAndAttribs.join(' <span aria-hidden="true">|</span> ')}}});Map.mergeOptions({attributionControl:!0}),Map.addInitHook((function(){this.options.attributionControl&&(new Attribution).addTo(this)}));var attribution=function(options){return new Attribution(options)};Control.Layers=Layers,Control.Zoom=Zoom,Control.Scale=Scale,Control.Attribution=Attribution,control.layers=layers,control.zoom=zoom,control.scale=scale,control.attribution=attribution;var Handler=Class.extend({initialize:function(map){this._map=map},enable:function(){return this._enabled||(this._enabled=!0,this.addHooks()),this},disable:function(){return this._enabled?(this._enabled=!1,this.removeHooks(),this):this},enabled:function(){return!!this._enabled}});Handler.addTo=function(map,name){return map.addHandler(name,this),this};var Mixin={Events},START=Browser.touch?"touchstart mousedown":"mousedown",Draggable=Evented.extend({options:{clickTolerance:3},initialize:function(element,dragStartTarget,preventOutline,options){setOptions(this,options),this._element=element,this._dragStartTarget=dragStartTarget||element,this._preventOutline=preventOutline},enable:function(){this._enabled||(on(this._dragStartTarget,START,this._onDown,this),this._enabled=!0)},disable:function(){this._enabled&&(Draggable._dragging===this&&this.finishDrag(!0),off(this._dragStartTarget,START,this._onDown,this),this._enabled=!1,this._moved=!1)},_onDown:function(e){if(this._enabled&&(this._moved=!1,!hasClass(this._element,"leaflet-zoom-anim")))if(e.touches&&1!==e.touches.length)Draggable._dragging===this&&this.finishDrag();else if(!(Draggable._dragging||e.shiftKey||1!==e.which&&1!==e.button&&!e.touches||(Draggable._dragging=this,this._preventOutline&&preventOutline(this._element),disableImageDrag(),disableTextSelection(),this._moving))){this.fire("down");var first=e.touches?e.touches[0]:e,sizedParent=getSizedParentNode(this._element);this._startPoint=new Point(first.clientX,first.clientY),this._startPos=getPosition(this._element),this._parentScale=getScale(sizedParent);var mouseevent="mousedown"===e.type;on(document,mouseevent?"mousemove":"touchmove",this._onMove,this),on(document,mouseevent?"mouseup":"touchend touchcancel",this._onUp,this)}},_onMove:function(e){if(this._enabled)if(e.touches&&e.touches.length>1)this._moved=!0;else{var first=e.touches&&1===e.touches.length?e.touches[0]:e,offset=new Point(first.clientX,first.clientY)._subtract(this._startPoint);(offset.x||offset.y)&&(Math.abs(offset.x)+Math.abs(offset.y)<this.options.clickTolerance||(offset.x/=this._parentScale.x,offset.y/=this._parentScale.y,preventDefault(e),this._moved||(this.fire("dragstart"),this._moved=!0,addClass(document.body,"leaflet-dragging"),this._lastTarget=e.target||e.srcElement,window.SVGElementInstance&&this._lastTarget instanceof window.SVGElementInstance&&(this._lastTarget=this._lastTarget.correspondingUseElement),addClass(this._lastTarget,"leaflet-drag-target")),this._newPos=this._startPos.add(offset),this._moving=!0,this._lastEvent=e,this._updatePosition()))}},_updatePosition:function(){var e={originalEvent:this._lastEvent};this.fire("predrag",e),setPosition(this._element,this._newPos),this.fire("drag",e)},_onUp:function(){this._enabled&&this.finishDrag()},finishDrag:function(noInertia){removeClass(document.body,"leaflet-dragging"),this._lastTarget&&(removeClass(this._lastTarget,"leaflet-drag-target"),this._lastTarget=null),off(document,"mousemove touchmove",this._onMove,this),off(document,"mouseup touchend touchcancel",this._onUp,this),enableImageDrag(),enableTextSelection();var fireDragend=this._moved&&this._moving;this._moving=!1,Draggable._dragging=!1,fireDragend&&this.fire("dragend",{noInertia,distance:this._newPos.distanceTo(this._startPos)})}});function clipPolygon(points,bounds,round){var clippedPoints,i,j,k,a,b,len,edge,p,edges=[1,4,2,8];for(i=0,len=points.length;i<len;i++)points[i]._code=_getBitCode(points[i],bounds);for(k=0;k<4;k++){for(edge=edges[k],clippedPoints=[],i=0,j=(len=points.length)-1;i<len;j=i++)a=points[i],b=points[j],a._code&edge?b._code&edge||((p=_getEdgeIntersection(b,a,edge,bounds,round))._code=_getBitCode(p,bounds),clippedPoints.push(p)):(b._code&edge&&((p=_getEdgeIntersection(b,a,edge,bounds,round))._code=_getBitCode(p,bounds),clippedPoints.push(p)),clippedPoints.push(a));points=clippedPoints}return points}function polygonCenter(latlngs,crs){var i,j,p1,p2,f,area,x,y,center;if(!latlngs||0===latlngs.length)throw new Error("latlngs not passed");isFlat(latlngs)||(console.warn("latlngs are not flat! Only the first ring will be used"),latlngs=latlngs[0]);var centroidLatLng=toLatLng([0,0]),bounds=toLatLngBounds(latlngs);bounds.getNorthWest().distanceTo(bounds.getSouthWest())*bounds.getNorthEast().distanceTo(bounds.getNorthWest())<1700&&(centroidLatLng=centroid(latlngs));var len=latlngs.length,points=[];for(i=0;i<len;i++){var latlng=toLatLng(latlngs[i]);points.push(crs.project(toLatLng([latlng.lat-centroidLatLng.lat,latlng.lng-centroidLatLng.lng])))}for(area=x=y=0,i=0,j=len-1;i<len;j=i++)p1=points[i],p2=points[j],f=p1.y*p2.x-p2.y*p1.x,x+=(p1.x+p2.x)*f,y+=(p1.y+p2.y)*f,area+=3*f;center=0===area?points[0]:[x/area,y/area];var latlngCenter=crs.unproject(toPoint(center));return toLatLng([latlngCenter.lat+centroidLatLng.lat,latlngCenter.lng+centroidLatLng.lng])}function centroid(coords){for(var latSum=0,lngSum=0,len=0,i=0;i<coords.length;i++){var latlng=toLatLng(coords[i]);latSum+=latlng.lat,lngSum+=latlng.lng,len++}return toLatLng([latSum/len,lngSum/len])}var _lastCode,PolyUtil={__proto__:null,clipPolygon,polygonCenter,centroid};function simplify(points,tolerance){if(!tolerance||!points.length)return points.slice();var sqTolerance=tolerance*tolerance;return points=_simplifyDP(points=_reducePoints(points,sqTolerance),sqTolerance)}function pointToSegmentDistance(p,p1,p2){return Math.sqrt(_sqClosestPointOnSegment(p,p1,p2,!0))}function closestPointOnSegment(p,p1,p2){return _sqClosestPointOnSegment(p,p1,p2)}function _simplifyDP(points,sqTolerance){var len=points.length,markers=new(typeof Uint8Array!=void 0+""?Uint8Array:Array)(len);markers[0]=markers[len-1]=1,_simplifyDPStep(points,markers,sqTolerance,0,len-1);var i,newPoints=[];for(i=0;i<len;i++)markers[i]&&newPoints.push(points[i]);return newPoints}function _simplifyDPStep(points,markers,sqTolerance,first,last){var index,i,sqDist,maxSqDist=0;for(i=first+1;i<=last-1;i++)(sqDist=_sqClosestPointOnSegment(points[i],points[first],points[last],!0))>maxSqDist&&(index=i,maxSqDist=sqDist);maxSqDist>sqTolerance&&(markers[index]=1,_simplifyDPStep(points,markers,sqTolerance,first,index),_simplifyDPStep(points,markers,sqTolerance,index,last))}function _reducePoints(points,sqTolerance){for(var reducedPoints=[points[0]],i=1,prev=0,len=points.length;i<len;i++)_sqDist(points[i],points[prev])>sqTolerance&&(reducedPoints.push(points[i]),prev=i);return prev<len-1&&reducedPoints.push(points[len-1]),reducedPoints}function clipSegment(a,b,bounds,useLastCode,round){var codeOut,p,newCode,codeA=useLastCode?_lastCode:_getBitCode(a,bounds),codeB=_getBitCode(b,bounds);for(_lastCode=codeB;;){if(!(codeA|codeB))return[a,b];if(codeA&codeB)return!1;newCode=_getBitCode(p=_getEdgeIntersection(a,b,codeOut=codeA||codeB,bounds,round),bounds),codeOut===codeA?(a=p,codeA=newCode):(b=p,codeB=newCode)}}function _getEdgeIntersection(a,b,code,bounds,round){var x,y,dx=b.x-a.x,dy=b.y-a.y,min=bounds.min,max=bounds.max;return 8&code?(x=a.x+dx*(max.y-a.y)/dy,y=max.y):4&code?(x=a.x+dx*(min.y-a.y)/dy,y=min.y):2&code?(x=max.x,y=a.y+dy*(max.x-a.x)/dx):1&code&&(x=min.x,y=a.y+dy*(min.x-a.x)/dx),new Point(x,y,round)}function _getBitCode(p,bounds){var code=0;return p.x<bounds.min.x?code|=1:p.x>bounds.max.x&&(code|=2),p.y<bounds.min.y?code|=4:p.y>bounds.max.y&&(code|=8),code}function _sqDist(p1,p2){var dx=p2.x-p1.x,dy=p2.y-p1.y;return dx*dx+dy*dy}function _sqClosestPointOnSegment(p,p1,p2,sqDist){var t,x=p1.x,y=p1.y,dx=p2.x-x,dy=p2.y-y,dot=dx*dx+dy*dy;return dot>0&&((t=((p.x-x)*dx+(p.y-y)*dy)/dot)>1?(x=p2.x,y=p2.y):t>0&&(x+=dx*t,y+=dy*t)),dx=p.x-x,dy=p.y-y,sqDist?dx*dx+dy*dy:new Point(x,y)}function isFlat(latlngs){return!isArray(latlngs[0])||"object"!=typeof latlngs[0][0]&&void 0!==latlngs[0][0]}function _flat(latlngs){return console.warn("Deprecated use of _flat, please use L.LineUtil.isFlat instead."),isFlat(latlngs)}function polylineCenter(latlngs,crs){var i,halfDist,segDist,dist,p1,p2,ratio,center;if(!latlngs||0===latlngs.length)throw new Error("latlngs not passed");isFlat(latlngs)||(console.warn("latlngs are not flat! Only the first ring will be used"),latlngs=latlngs[0]);var centroidLatLng=toLatLng([0,0]),bounds=toLatLngBounds(latlngs);bounds.getNorthWest().distanceTo(bounds.getSouthWest())*bounds.getNorthEast().distanceTo(bounds.getNorthWest())<1700&&(centroidLatLng=centroid(latlngs));var len=latlngs.length,points=[];for(i=0;i<len;i++){var latlng=toLatLng(latlngs[i]);points.push(crs.project(toLatLng([latlng.lat-centroidLatLng.lat,latlng.lng-centroidLatLng.lng])))}for(i=0,halfDist=0;i<len-1;i++)halfDist+=points[i].distanceTo(points[i+1])/2;if(0===halfDist)center=points[0];else for(i=0,dist=0;i<len-1;i++)if(p1=points[i],p2=points[i+1],(dist+=segDist=p1.distanceTo(p2))>halfDist){ratio=(dist-halfDist)/segDist,center=[p2.x-ratio*(p2.x-p1.x),p2.y-ratio*(p2.y-p1.y)];break}var latlngCenter=crs.unproject(toPoint(center));return toLatLng([latlngCenter.lat+centroidLatLng.lat,latlngCenter.lng+centroidLatLng.lng])}var LineUtil={__proto__:null,simplify,pointToSegmentDistance,closestPointOnSegment,clipSegment,_getEdgeIntersection,_getBitCode,_sqClosestPointOnSegment,isFlat,_flat,polylineCenter},LonLat={project:function(latlng){return new Point(latlng.lng,latlng.lat)},unproject:function(point){return new LatLng(point.y,point.x)},bounds:new Bounds([-180,-90],[180,90])},Mercator={R:6378137,R_MINOR:6356752.314245179,bounds:new Bounds([-20037508.34279,-15496570.73972],[20037508.34279,18764656.23138]),project:function(latlng){var d=Math.PI/180,r=this.R,y=latlng.lat*d,tmp=this.R_MINOR/r,e=Math.sqrt(1-tmp*tmp),con=e*Math.sin(y),ts=Math.tan(Math.PI/4-y/2)/Math.pow((1-con)/(1+con),e/2);return y=-r*Math.log(Math.max(ts,1e-10)),new Point(latlng.lng*d*r,y)},unproject:function(point){for(var con,d=180/Math.PI,r=this.R,tmp=this.R_MINOR/r,e=Math.sqrt(1-tmp*tmp),ts=Math.exp(-point.y/r),phi=Math.PI/2-2*Math.atan(ts),i=0,dphi=.1;i<15&&Math.abs(dphi)>1e-7;i++)con=e*Math.sin(phi),con=Math.pow((1-con)/(1+con),e/2),phi+=dphi=Math.PI/2-2*Math.atan(ts*con)-phi;return new LatLng(phi*d,point.x*d/r)}},index={__proto__:null,LonLat,Mercator,SphericalMercator},EPSG3395=extend({},Earth,{code:"EPSG:3395",projection:Mercator,transformation:function(){var scale=.5/(Math.PI*Mercator.R);return toTransformation(scale,.5,-scale,.5)}()}),EPSG4326=extend({},Earth,{code:"EPSG:4326",projection:LonLat,transformation:toTransformation(1/180,1,-1/180,.5)}),Simple=extend({},CRS,{projection:LonLat,transformation:toTransformation(1,0,-1,0),scale:function(zoom){return Math.pow(2,zoom)},zoom:function(scale){return Math.log(scale)/Math.LN2},distance:function(latlng1,latlng2){var dx=latlng2.lng-latlng1.lng,dy=latlng2.lat-latlng1.lat;return Math.sqrt(dx*dx+dy*dy)},infinite:!0});CRS.Earth=Earth,CRS.EPSG3395=EPSG3395,CRS.EPSG3857=EPSG3857,CRS.EPSG900913=EPSG900913,CRS.EPSG4326=EPSG4326,CRS.Simple=Simple;var Layer=Evented.extend({options:{pane:"overlayPane",attribution:null,bubblingMouseEvents:!0},addTo:function(map){return map.addLayer(this),this},remove:function(){return this.removeFrom(this._map||this._mapToAdd)},removeFrom:function(obj){return obj&&obj.removeLayer(this),this},getPane:function(name){return this._map.getPane(name?this.options[name]||name:this.options.pane)},addInteractiveTarget:function(targetEl){return this._map._targets[stamp(targetEl)]=this,this},removeInteractiveTarget:function(targetEl){return delete this._map._targets[stamp(targetEl)],this},getAttribution:function(){return this.options.attribution},_layerAdd:function(e){var map=e.target;if(map.hasLayer(this)){if(this._map=map,this._zoomAnimated=map._zoomAnimated,this.getEvents){var events=this.getEvents();map.on(events,this),this.once("remove",(function(){map.off(events,this)}),this)}this.onAdd(map),this.fire("add"),map.fire("layeradd",{layer:this})}}});Map.include({addLayer:function(layer){if(!layer._layerAdd)throw new Error("The provided object is not a Layer.");var id=stamp(layer);return this._layers[id]||(this._layers[id]=layer,layer._mapToAdd=this,layer.beforeAdd&&layer.beforeAdd(this),this.whenReady(layer._layerAdd,layer)),this},removeLayer:function(layer){var id=stamp(layer);return this._layers[id]?(this._loaded&&layer.onRemove(this),delete this._layers[id],this._loaded&&(this.fire("layerremove",{layer}),layer.fire("remove")),layer._map=layer._mapToAdd=null,this):this},hasLayer:function(layer){return stamp(layer)in this._layers},eachLayer:function(method,context){for(var i in this._layers)method.call(context,this._layers[i]);return this},_addLayers:function(layers){for(var i=0,len=(layers=layers?isArray(layers)?layers:[layers]:[]).length;i<len;i++)this.addLayer(layers[i])},_addZoomLimit:function(layer){isNaN(layer.options.maxZoom)&&isNaN(layer.options.minZoom)||(this._zoomBoundLayers[stamp(layer)]=layer,this._updateZoomLevels())},_removeZoomLimit:function(layer){var id=stamp(layer);this._zoomBoundLayers[id]&&(delete this._zoomBoundLayers[id],this._updateZoomLevels())},_updateZoomLevels:function(){var minZoom=1/0,maxZoom=-1/0,oldZoomSpan=this._getZoomSpan();for(var i in this._zoomBoundLayers){var options=this._zoomBoundLayers[i].options;minZoom=void 0===options.minZoom?minZoom:Math.min(minZoom,options.minZoom),maxZoom=void 0===options.maxZoom?maxZoom:Math.max(maxZoom,options.maxZoom)}this._layersMaxZoom=maxZoom===-1/0?void 0:maxZoom,this._layersMinZoom=minZoom===1/0?void 0:minZoom,oldZoomSpan!==this._getZoomSpan()&&this.fire("zoomlevelschange"),void 0===this.options.maxZoom&&this._layersMaxZoom&&this.getZoom()>this._layersMaxZoom&&this.setZoom(this._layersMaxZoom),void 0===this.options.minZoom&&this._layersMinZoom&&this.getZoom()<this._layersMinZoom&&this.setZoom(this._layersMinZoom)}});var LayerGroup=Layer.extend({initialize:function(layers,options){var i,len;if(setOptions(this,options),this._layers={},layers)for(i=0,len=layers.length;i<len;i++)this.addLayer(layers[i])},addLayer:function(layer){var id=this.getLayerId(layer);return this._layers[id]=layer,this._map&&this._map.addLayer(layer),this},removeLayer:function(layer){var id=layer in this._layers?layer:this.getLayerId(layer);return this._map&&this._layers[id]&&this._map.removeLayer(this._layers[id]),delete this._layers[id],this},hasLayer:function(layer){return("number"==typeof layer?layer:this.getLayerId(layer))in this._layers},clearLayers:function(){return this.eachLayer(this.removeLayer,this)},invoke:function(methodName){var i,layer,args=Array.prototype.slice.call(arguments,1);for(i in this._layers)(layer=this._layers[i])[methodName]&&layer[methodName].apply(layer,args);return this},onAdd:function(map){this.eachLayer(map.addLayer,map)},onRemove:function(map){this.eachLayer(map.removeLayer,map)},eachLayer:function(method,context){for(var i in this._layers)method.call(context,this._layers[i]);return this},getLayer:function(id){return this._layers[id]},getLayers:function(){var layers=[];return this.eachLayer(layers.push,layers),layers},setZIndex:function(zIndex){return this.invoke("setZIndex",zIndex)},getLayerId:function(layer){return stamp(layer)}}),layerGroup=function(layers,options){return new LayerGroup(layers,options)},FeatureGroup=LayerGroup.extend({addLayer:function(layer){return this.hasLayer(layer)?this:(layer.addEventParent(this),LayerGroup.prototype.addLayer.call(this,layer),this.fire("layeradd",{layer}))},removeLayer:function(layer){return this.hasLayer(layer)?(layer in this._layers&&(layer=this._layers[layer]),layer.removeEventParent(this),LayerGroup.prototype.removeLayer.call(this,layer),this.fire("layerremove",{layer})):this},setStyle:function(style){return this.invoke("setStyle",style)},bringToFront:function(){return this.invoke("bringToFront")},bringToBack:function(){return this.invoke("bringToBack")},getBounds:function(){var bounds=new LatLngBounds;for(var id in this._layers){var layer=this._layers[id];bounds.extend(layer.getBounds?layer.getBounds():layer.getLatLng())}return bounds}}),featureGroup=function(layers,options){return new FeatureGroup(layers,options)},Icon=Class.extend({options:{popupAnchor:[0,0],tooltipAnchor:[0,0],crossOrigin:!1},initialize:function(options){setOptions(this,options)},createIcon:function(oldIcon){return this._createIcon("icon",oldIcon)},createShadow:function(oldIcon){return this._createIcon("shadow",oldIcon)},_createIcon:function(name,oldIcon){var src=this._getIconUrl(name);if(!src){if("icon"===name)throw new Error("iconUrl not set in Icon options (see the docs).");return null}var img=this._createImg(src,oldIcon&&"IMG"===oldIcon.tagName?oldIcon:null);return this._setIconStyles(img,name),(this.options.crossOrigin||""===this.options.crossOrigin)&&(img.crossOrigin=!0===this.options.crossOrigin?"":this.options.crossOrigin),img},_setIconStyles:function(img,name){var options=this.options,sizeOption=options[name+"Size"];"number"==typeof sizeOption&&(sizeOption=[sizeOption,sizeOption]);var size=toPoint(sizeOption),anchor=toPoint("shadow"===name&&options.shadowAnchor||options.iconAnchor||size&&size.divideBy(2,!0));img.className="leaflet-marker-"+name+" "+(options.className||""),anchor&&(img.style.marginLeft=-anchor.x+"px",img.style.marginTop=-anchor.y+"px"),size&&(img.style.width=size.x+"px",img.style.height=size.y+"px")},_createImg:function(src,el){return(el=el||document.createElement("img")).src=src,el},_getIconUrl:function(name){return Browser.retina&&this.options[name+"RetinaUrl"]||this.options[name+"Url"]}});function icon(options){return new Icon(options)}var IconDefault=Icon.extend({options:{iconUrl:"marker-icon.png",iconRetinaUrl:"marker-icon-2x.png",shadowUrl:"marker-shadow.png",iconSize:[25,41],iconAnchor:[12,41],popupAnchor:[1,-34],tooltipAnchor:[16,-28],shadowSize:[41,41]},_getIconUrl:function(name){return"string"!=typeof IconDefault.imagePath&&(IconDefault.imagePath=this._detectIconPath()),(this.options.imagePath||IconDefault.imagePath)+Icon.prototype._getIconUrl.call(this,name)},_stripUrl:function(path){var strip=function(str,re,idx){var match=re.exec(str);return match&&match[idx]};return(path=strip(path,/^url\((['"])?(.+)\1\)$/,2))&&strip(path,/^(.*)marker-icon\.png$/,1)},_detectIconPath:function(){var el=create$1("div","leaflet-default-icon-path",document.body),path=getStyle(el,"background-image")||getStyle(el,"backgroundImage");if(document.body.removeChild(el),path=this._stripUrl(path))return path;var link=document.querySelector('link[href$="leaflet.css"]');return link?link.href.substring(0,link.href.length-11-1):""}}),MarkerDrag=Handler.extend({initialize:function(marker){this._marker=marker},addHooks:function(){var icon=this._marker._icon;this._draggable||(this._draggable=new Draggable(icon,icon,!0)),this._draggable.on({dragstart:this._onDragStart,predrag:this._onPreDrag,drag:this._onDrag,dragend:this._onDragEnd},this).enable(),addClass(icon,"leaflet-marker-draggable")},removeHooks:function(){this._draggable.off({dragstart:this._onDragStart,predrag:this._onPreDrag,drag:this._onDrag,dragend:this._onDragEnd},this).disable(),this._marker._icon&&removeClass(this._marker._icon,"leaflet-marker-draggable")},moved:function(){return this._draggable&&this._draggable._moved},_adjustPan:function(e){var marker=this._marker,map=marker._map,speed=this._marker.options.autoPanSpeed,padding=this._marker.options.autoPanPadding,iconPos=getPosition(marker._icon),bounds=map.getPixelBounds(),origin=map.getPixelOrigin(),panBounds=toBounds(bounds.min._subtract(origin).add(padding),bounds.max._subtract(origin).subtract(padding));if(!panBounds.contains(iconPos)){var movement=toPoint((Math.max(panBounds.max.x,iconPos.x)-panBounds.max.x)/(bounds.max.x-panBounds.max.x)-(Math.min(panBounds.min.x,iconPos.x)-panBounds.min.x)/(bounds.min.x-panBounds.min.x),(Math.max(panBounds.max.y,iconPos.y)-panBounds.max.y)/(bounds.max.y-panBounds.max.y)-(Math.min(panBounds.min.y,iconPos.y)-panBounds.min.y)/(bounds.min.y-panBounds.min.y)).multiplyBy(speed);map.panBy(movement,{animate:!1}),this._draggable._newPos._add(movement),this._draggable._startPos._add(movement),setPosition(marker._icon,this._draggable._newPos),this._onDrag(e),this._panRequest=requestAnimFrame(this._adjustPan.bind(this,e))}},_onDragStart:function(){this._oldLatLng=this._marker.getLatLng(),this._marker.closePopup&&this._marker.closePopup(),this._marker.fire("movestart").fire("dragstart")},_onPreDrag:function(e){this._marker.options.autoPan&&(cancelAnimFrame(this._panRequest),this._panRequest=requestAnimFrame(this._adjustPan.bind(this,e)))},_onDrag:function(e){var marker=this._marker,shadow=marker._shadow,iconPos=getPosition(marker._icon),latlng=marker._map.layerPointToLatLng(iconPos);shadow&&setPosition(shadow,iconPos),marker._latlng=latlng,e.latlng=latlng,e.oldLatLng=this._oldLatLng,marker.fire("move",e).fire("drag",e)},_onDragEnd:function(e){cancelAnimFrame(this._panRequest),delete this._oldLatLng,this._marker.fire("moveend").fire("dragend",e)}}),Marker=Layer.extend({options:{icon:new IconDefault,interactive:!0,keyboard:!0,title:"",alt:"Marker",zIndexOffset:0,opacity:1,riseOnHover:!1,riseOffset:250,pane:"markerPane",shadowPane:"shadowPane",bubblingMouseEvents:!1,autoPanOnFocus:!0,draggable:!1,autoPan:!1,autoPanPadding:[50,50],autoPanSpeed:10},initialize:function(latlng,options){setOptions(this,options),this._latlng=toLatLng(latlng)},onAdd:function(map){this._zoomAnimated=this._zoomAnimated&&map.options.markerZoomAnimation,this._zoomAnimated&&map.on("zoomanim",this._animateZoom,this),this._initIcon(),this.update()},onRemove:function(map){this.dragging&&this.dragging.enabled()&&(this.options.draggable=!0,this.dragging.removeHooks()),delete this.dragging,this._zoomAnimated&&map.off("zoomanim",this._animateZoom,this),this._removeIcon(),this._removeShadow()},getEvents:function(){return{zoom:this.update,viewreset:this.update}},getLatLng:function(){return this._latlng},setLatLng:function(latlng){var oldLatLng=this._latlng;return this._latlng=toLatLng(latlng),this.update(),this.fire("move",{oldLatLng,latlng:this._latlng})},setZIndexOffset:function(offset){return this.options.zIndexOffset=offset,this.update()},getIcon:function(){return this.options.icon},setIcon:function(icon){return this.options.icon=icon,this._map&&(this._initIcon(),this.update()),this._popup&&this.bindPopup(this._popup,this._popup.options),this},getElement:function(){return this._icon},update:function(){if(this._icon&&this._map){var pos=this._map.latLngToLayerPoint(this._latlng).round();this._setPos(pos)}return this},_initIcon:function(){var options=this.options,classToAdd="leaflet-zoom-"+(this._zoomAnimated?"animated":"hide"),icon=options.icon.createIcon(this._icon),addIcon=!1;icon!==this._icon&&(this._icon&&this._removeIcon(),addIcon=!0,options.title&&(icon.title=options.title),"IMG"===icon.tagName&&(icon.alt=options.alt||"")),addClass(icon,classToAdd),options.keyboard&&(icon.tabIndex="0",icon.setAttribute("role","button")),this._icon=icon,options.riseOnHover&&this.on({mouseover:this._bringToFront,mouseout:this._resetZIndex}),this.options.autoPanOnFocus&&on(icon,"focus",this._panOnFocus,this);var newShadow=options.icon.createShadow(this._shadow),addShadow=!1;newShadow!==this._shadow&&(this._removeShadow(),addShadow=!0),newShadow&&(addClass(newShadow,classToAdd),newShadow.alt=""),this._shadow=newShadow,options.opacity<1&&this._updateOpacity(),addIcon&&this.getPane().appendChild(this._icon),this._initInteraction(),newShadow&&addShadow&&this.getPane(options.shadowPane).appendChild(this._shadow)},_removeIcon:function(){this.options.riseOnHover&&this.off({mouseover:this._bringToFront,mouseout:this._resetZIndex}),this.options.autoPanOnFocus&&off(this._icon,"focus",this._panOnFocus,this),remove(this._icon),this.removeInteractiveTarget(this._icon),this._icon=null},_removeShadow:function(){this._shadow&&remove(this._shadow),this._shadow=null},_setPos:function(pos){this._icon&&setPosition(this._icon,pos),this._shadow&&setPosition(this._shadow,pos),this._zIndex=pos.y+this.options.zIndexOffset,this._resetZIndex()},_updateZIndex:function(offset){this._icon&&(this._icon.style.zIndex=this._zIndex+offset)},_animateZoom:function(opt){var pos=this._map._latLngToNewLayerPoint(this._latlng,opt.zoom,opt.center).round();this._setPos(pos)},_initInteraction:function(){if(this.options.interactive&&(addClass(this._icon,"leaflet-interactive"),this.addInteractiveTarget(this._icon),MarkerDrag)){var draggable=this.options.draggable;this.dragging&&(draggable=this.dragging.enabled(),this.dragging.disable()),this.dragging=new MarkerDrag(this),draggable&&this.dragging.enable()}},setOpacity:function(opacity){return this.options.opacity=opacity,this._map&&this._updateOpacity(),this},_updateOpacity:function(){var opacity=this.options.opacity;this._icon&&setOpacity(this._icon,opacity),this._shadow&&setOpacity(this._shadow,opacity)},_bringToFront:function(){this._updateZIndex(this.options.riseOffset)},_resetZIndex:function(){this._updateZIndex(0)},_panOnFocus:function(){var map=this._map;if(map){var iconOpts=this.options.icon.options,size=iconOpts.iconSize?toPoint(iconOpts.iconSize):toPoint(0,0),anchor=iconOpts.iconAnchor?toPoint(iconOpts.iconAnchor):toPoint(0,0);map.panInside(this._latlng,{paddingTopLeft:anchor,paddingBottomRight:size.subtract(anchor)})}},_getPopupAnchor:function(){return this.options.icon.options.popupAnchor},_getTooltipAnchor:function(){return this.options.icon.options.tooltipAnchor}});function marker(latlng,options){return new Marker(latlng,options)}var Path=Layer.extend({options:{stroke:!0,color:"#3388ff",weight:3,opacity:1,lineCap:"round",lineJoin:"round",dashArray:null,dashOffset:null,fill:!1,fillColor:null,fillOpacity:.2,fillRule:"evenodd",interactive:!0,bubblingMouseEvents:!0},beforeAdd:function(map){this._renderer=map.getRenderer(this)},onAdd:function(){this._renderer._initPath(this),this._reset(),this._renderer._addPath(this)},onRemove:function(){this._renderer._removePath(this)},redraw:function(){return this._map&&this._renderer._updatePath(this),this},setStyle:function(style){return setOptions(this,style),this._renderer&&(this._renderer._updateStyle(this),this.options.stroke&&style&&Object.prototype.hasOwnProperty.call(style,"weight")&&this._updateBounds()),this},bringToFront:function(){return this._renderer&&this._renderer._bringToFront(this),this},bringToBack:function(){return this._renderer&&this._renderer._bringToBack(this),this},getElement:function(){return this._path},_reset:function(){this._project(),this._update()},_clickTolerance:function(){return(this.options.stroke?this.options.weight/2:0)+(this._renderer.options.tolerance||0)}}),CircleMarker=Path.extend({options:{fill:!0,radius:10},initialize:function(latlng,options){setOptions(this,options),this._latlng=toLatLng(latlng),this._radius=this.options.radius},setLatLng:function(latlng){var oldLatLng=this._latlng;return this._latlng=toLatLng(latlng),this.redraw(),this.fire("move",{oldLatLng,latlng:this._latlng})},getLatLng:function(){return this._latlng},setRadius:function(radius){return this.options.radius=this._radius=radius,this.redraw()},getRadius:function(){return this._radius},setStyle:function(options){var radius=options&&options.radius||this._radius;return Path.prototype.setStyle.call(this,options),this.setRadius(radius),this},_project:function(){this._point=this._map.latLngToLayerPoint(this._latlng),this._updateBounds()},_updateBounds:function(){var r=this._radius,r2=this._radiusY||r,w=this._clickTolerance(),p=[r+w,r2+w];this._pxBounds=new Bounds(this._point.subtract(p),this._point.add(p))},_update:function(){this._map&&this._updatePath()},_updatePath:function(){this._renderer._updateCircle(this)},_empty:function(){return this._radius&&!this._renderer._bounds.intersects(this._pxBounds)},_containsPoint:function(p){return p.distanceTo(this._point)<=this._radius+this._clickTolerance()}});function circleMarker(latlng,options){return new CircleMarker(latlng,options)}var Circle=CircleMarker.extend({initialize:function(latlng,options,legacyOptions){if("number"==typeof options&&(options=extend({},legacyOptions,{radius:options})),setOptions(this,options),this._latlng=toLatLng(latlng),isNaN(this.options.radius))throw new Error("Circle radius cannot be NaN");this._mRadius=this.options.radius},setRadius:function(radius){return this._mRadius=radius,this.redraw()},getRadius:function(){return this._mRadius},getBounds:function(){var half=[this._radius,this._radiusY||this._radius];return new LatLngBounds(this._map.layerPointToLatLng(this._point.subtract(half)),this._map.layerPointToLatLng(this._point.add(half)))},setStyle:Path.prototype.setStyle,_project:function(){var lng=this._latlng.lng,lat=this._latlng.lat,map=this._map,crs=map.options.crs;if(crs.distance===Earth.distance){var d=Math.PI/180,latR=this._mRadius/Earth.R/d,top=map.project([lat+latR,lng]),bottom=map.project([lat-latR,lng]),p=top.add(bottom).divideBy(2),lat2=map.unproject(p).lat,lngR=Math.acos((Math.cos(latR*d)-Math.sin(lat*d)*Math.sin(lat2*d))/(Math.cos(lat*d)*Math.cos(lat2*d)))/d;(isNaN(lngR)||0===lngR)&&(lngR=latR/Math.cos(Math.PI/180*lat)),this._point=p.subtract(map.getPixelOrigin()),this._radius=isNaN(lngR)?0:p.x-map.project([lat2,lng-lngR]).x,this._radiusY=p.y-top.y}else{var latlng2=crs.unproject(crs.project(this._latlng).subtract([this._mRadius,0]));this._point=map.latLngToLayerPoint(this._latlng),this._radius=this._point.x-map.latLngToLayerPoint(latlng2).x}this._updateBounds()}});function circle(latlng,options,legacyOptions){return new Circle(latlng,options,legacyOptions)}var Polyline=Path.extend({options:{smoothFactor:1,noClip:!1},initialize:function(latlngs,options){setOptions(this,options),this._setLatLngs(latlngs)},getLatLngs:function(){return this._latlngs},setLatLngs:function(latlngs){return this._setLatLngs(latlngs),this.redraw()},isEmpty:function(){return!this._latlngs.length},closestLayerPoint:function(p){for(var p1,p2,minDistance=1/0,minPoint=null,closest=_sqClosestPointOnSegment,j=0,jLen=this._parts.length;j<jLen;j++)for(var points=this._parts[j],i=1,len=points.length;i<len;i++){var sqDist=closest(p,p1=points[i-1],p2=points[i],!0);sqDist<minDistance&&(minDistance=sqDist,minPoint=closest(p,p1,p2))}return minPoint&&(minPoint.distance=Math.sqrt(minDistance)),minPoint},getCenter:function(){if(!this._map)throw new Error("Must add layer to map before using getCenter()");return polylineCenter(this._defaultShape(),this._map.options.crs)},getBounds:function(){return this._bounds},addLatLng:function(latlng,latlngs){return latlngs=latlngs||this._defaultShape(),latlng=toLatLng(latlng),latlngs.push(latlng),this._bounds.extend(latlng),this.redraw()},_setLatLngs:function(latlngs){this._bounds=new LatLngBounds,this._latlngs=this._convertLatLngs(latlngs)},_defaultShape:function(){return isFlat(this._latlngs)?this._latlngs:this._latlngs[0]},_convertLatLngs:function(latlngs){for(var result=[],flat=isFlat(latlngs),i=0,len=latlngs.length;i<len;i++)flat?(result[i]=toLatLng(latlngs[i]),this._bounds.extend(result[i])):result[i]=this._convertLatLngs(latlngs[i]);return result},_project:function(){var pxBounds=new Bounds;this._rings=[],this._projectLatlngs(this._latlngs,this._rings,pxBounds),this._bounds.isValid()&&pxBounds.isValid()&&(this._rawPxBounds=pxBounds,this._updateBounds())},_updateBounds:function(){var w=this._clickTolerance(),p=new Point(w,w);this._rawPxBounds&&(this._pxBounds=new Bounds([this._rawPxBounds.min.subtract(p),this._rawPxBounds.max.add(p)]))},_projectLatlngs:function(latlngs,result,projectedBounds){var i,ring,flat=latlngs[0]instanceof LatLng,len=latlngs.length;if(flat){for(ring=[],i=0;i<len;i++)ring[i]=this._map.latLngToLayerPoint(latlngs[i]),projectedBounds.extend(ring[i]);result.push(ring)}else for(i=0;i<len;i++)this._projectLatlngs(latlngs[i],result,projectedBounds)},_clipPoints:function(){var bounds=this._renderer._bounds;if(this._parts=[],this._pxBounds&&this._pxBounds.intersects(bounds))if(this.options.noClip)this._parts=this._rings;else{var i,j,k,len,len2,segment,points,parts=this._parts;for(i=0,k=0,len=this._rings.length;i<len;i++)for(j=0,len2=(points=this._rings[i]).length;j<len2-1;j++)(segment=clipSegment(points[j],points[j+1],bounds,j,!0))&&(parts[k]=parts[k]||[],parts[k].push(segment[0]),segment[1]===points[j+1]&&j!==len2-2||(parts[k].push(segment[1]),k++))}},_simplifyPoints:function(){for(var parts=this._parts,tolerance=this.options.smoothFactor,i=0,len=parts.length;i<len;i++)parts[i]=simplify(parts[i],tolerance)},_update:function(){this._map&&(this._clipPoints(),this._simplifyPoints(),this._updatePath())},_updatePath:function(){this._renderer._updatePoly(this)},_containsPoint:function(p,closed){var i,j,k,len,len2,part,w=this._clickTolerance();if(!this._pxBounds||!this._pxBounds.contains(p))return!1;for(i=0,len=this._parts.length;i<len;i++)for(j=0,k=(len2=(part=this._parts[i]).length)-1;j<len2;k=j++)if((closed||0!==j)&&pointToSegmentDistance(p,part[k],part[j])<=w)return!0;return!1}});function polyline(latlngs,options){return new Polyline(latlngs,options)}Polyline._flat=_flat;var Polygon=Polyline.extend({options:{fill:!0},isEmpty:function(){return!this._latlngs.length||!this._latlngs[0].length},getCenter:function(){if(!this._map)throw new Error("Must add layer to map before using getCenter()");return polygonCenter(this._defaultShape(),this._map.options.crs)},_convertLatLngs:function(latlngs){var result=Polyline.prototype._convertLatLngs.call(this,latlngs),len=result.length;return len>=2&&result[0]instanceof LatLng&&result[0].equals(result[len-1])&&result.pop(),result},_setLatLngs:function(latlngs){Polyline.prototype._setLatLngs.call(this,latlngs),isFlat(this._latlngs)&&(this._latlngs=[this._latlngs])},_defaultShape:function(){return isFlat(this._latlngs[0])?this._latlngs[0]:this._latlngs[0][0]},_clipPoints:function(){var bounds=this._renderer._bounds,w=this.options.weight,p=new Point(w,w);if(bounds=new Bounds(bounds.min.subtract(p),bounds.max.add(p)),this._parts=[],this._pxBounds&&this._pxBounds.intersects(bounds))if(this.options.noClip)this._parts=this._rings;else for(var clipped,i=0,len=this._rings.length;i<len;i++)(clipped=clipPolygon(this._rings[i],bounds,!0)).length&&this._parts.push(clipped)},_updatePath:function(){this._renderer._updatePoly(this,!0)},_containsPoint:function(p){var part,p1,p2,i,j,k,len,len2,inside=!1;if(!this._pxBounds||!this._pxBounds.contains(p))return!1;for(i=0,len=this._parts.length;i<len;i++)for(j=0,k=(len2=(part=this._parts[i]).length)-1;j<len2;k=j++)p1=part[j],p2=part[k],p1.y>p.y!=p2.y>p.y&&p.x<(p2.x-p1.x)*(p.y-p1.y)/(p2.y-p1.y)+p1.x&&(inside=!inside);return inside||Polyline.prototype._containsPoint.call(this,p,!0)}});function polygon(latlngs,options){return new Polygon(latlngs,options)}var GeoJSON=FeatureGroup.extend({initialize:function(geojson,options){setOptions(this,options),this._layers={},geojson&&this.addData(geojson)},addData:function(geojson){var i,len,feature,features=isArray(geojson)?geojson:geojson.features;if(features){for(i=0,len=features.length;i<len;i++)((feature=features[i]).geometries||feature.geometry||feature.features||feature.coordinates)&&this.addData(feature);return this}var options=this.options;if(options.filter&&!options.filter(geojson))return this;var layer=geometryToLayer(geojson,options);return layer?(layer.feature=asFeature(geojson),layer.defaultOptions=layer.options,this.resetStyle(layer),options.onEachFeature&&options.onEachFeature(geojson,layer),this.addLayer(layer)):this},resetStyle:function(layer){return void 0===layer?this.eachLayer(this.resetStyle,this):(layer.options=extend({},layer.defaultOptions),this._setLayerStyle(layer,this.options.style),this)},setStyle:function(style){return this.eachLayer((function(layer){this._setLayerStyle(layer,style)}),this)},_setLayerStyle:function(layer,style){layer.setStyle&&("function"==typeof style&&(style=style(layer.feature)),layer.setStyle(style))}});function geometryToLayer(geojson,options){var latlng,latlngs,i,len,geometry="Feature"===geojson.type?geojson.geometry:geojson,coords=geometry?geometry.coordinates:null,layers=[],pointToLayer=options&&options.pointToLayer,_coordsToLatLng=options&&options.coordsToLatLng||coordsToLatLng;if(!coords&&!geometry)return null;switch(geometry.type){case"Point":return _pointToLayer(pointToLayer,geojson,latlng=_coordsToLatLng(coords),options);case"MultiPoint":for(i=0,len=coords.length;i<len;i++)latlng=_coordsToLatLng(coords[i]),layers.push(_pointToLayer(pointToLayer,geojson,latlng,options));return new FeatureGroup(layers);case"LineString":case"MultiLineString":return latlngs=coordsToLatLngs(coords,"LineString"===geometry.type?0:1,_coordsToLatLng),new Polyline(latlngs,options);case"Polygon":case"MultiPolygon":return latlngs=coordsToLatLngs(coords,"Polygon"===geometry.type?1:2,_coordsToLatLng),new Polygon(latlngs,options);case"GeometryCollection":for(i=0,len=geometry.geometries.length;i<len;i++){var geoLayer=geometryToLayer({geometry:geometry.geometries[i],type:"Feature",properties:geojson.properties},options);geoLayer&&layers.push(geoLayer)}return new FeatureGroup(layers);case"FeatureCollection":for(i=0,len=geometry.features.length;i<len;i++){var featureLayer=geometryToLayer(geometry.features[i],options);featureLayer&&layers.push(featureLayer)}return new FeatureGroup(layers);default:throw new Error("Invalid GeoJSON object.")}}function _pointToLayer(pointToLayerFn,geojson,latlng,options){return pointToLayerFn?pointToLayerFn(geojson,latlng):new Marker(latlng,options&&options.markersInheritOptions&&options)}function coordsToLatLng(coords){return new LatLng(coords[1],coords[0],coords[2])}function coordsToLatLngs(coords,levelsDeep,_coordsToLatLng){for(var latlng,latlngs=[],i=0,len=coords.length;i<len;i++)latlng=levelsDeep?coordsToLatLngs(coords[i],levelsDeep-1,_coordsToLatLng):(_coordsToLatLng||coordsToLatLng)(coords[i]),latlngs.push(latlng);return latlngs}function latLngToCoords(latlng,precision){return void 0!==(latlng=toLatLng(latlng)).alt?[formatNum(latlng.lng,precision),formatNum(latlng.lat,precision),formatNum(latlng.alt,precision)]:[formatNum(latlng.lng,precision),formatNum(latlng.lat,precision)]}function latLngsToCoords(latlngs,levelsDeep,closed,precision){for(var coords=[],i=0,len=latlngs.length;i<len;i++)coords.push(levelsDeep?latLngsToCoords(latlngs[i],isFlat(latlngs[i])?0:levelsDeep-1,closed,precision):latLngToCoords(latlngs[i],precision));return!levelsDeep&&closed&&coords.length>0&&coords.push(coords[0].slice()),coords}function getFeature(layer,newGeometry){return layer.feature?extend({},layer.feature,{geometry:newGeometry}):asFeature(newGeometry)}function asFeature(geojson){return"Feature"===geojson.type||"FeatureCollection"===geojson.type?geojson:{type:"Feature",properties:{},geometry:geojson}}var PointToGeoJSON={toGeoJSON:function(precision){return getFeature(this,{type:"Point",coordinates:latLngToCoords(this.getLatLng(),precision)})}};function geoJSON(geojson,options){return new GeoJSON(geojson,options)}Marker.include(PointToGeoJSON),Circle.include(PointToGeoJSON),CircleMarker.include(PointToGeoJSON),Polyline.include({toGeoJSON:function(precision){var multi=!isFlat(this._latlngs);return getFeature(this,{type:(multi?"Multi":"")+"LineString",coordinates:latLngsToCoords(this._latlngs,multi?1:0,!1,precision)})}}),Polygon.include({toGeoJSON:function(precision){var holes=!isFlat(this._latlngs),multi=holes&&!isFlat(this._latlngs[0]),coords=latLngsToCoords(this._latlngs,multi?2:holes?1:0,!0,precision);return holes||(coords=[coords]),getFeature(this,{type:(multi?"Multi":"")+"Polygon",coordinates:coords})}}),LayerGroup.include({toMultiPoint:function(precision){var coords=[];return this.eachLayer((function(layer){coords.push(layer.toGeoJSON(precision).geometry.coordinates)})),getFeature(this,{type:"MultiPoint",coordinates:coords})},toGeoJSON:function(precision){var type=this.feature&&this.feature.geometry&&this.feature.geometry.type;if("MultiPoint"===type)return this.toMultiPoint(precision);var isGeometryCollection="GeometryCollection"===type,jsons=[];return this.eachLayer((function(layer){if(layer.toGeoJSON){var json=layer.toGeoJSON(precision);if(isGeometryCollection)jsons.push(json.geometry);else{var feature=asFeature(json);"FeatureCollection"===feature.type?jsons.push.apply(jsons,feature.features):jsons.push(feature)}}})),isGeometryCollection?getFeature(this,{geometries:jsons,type:"GeometryCollection"}):{type:"FeatureCollection",features:jsons}}});var geoJson=geoJSON,ImageOverlay=Layer.extend({options:{opacity:1,alt:"",interactive:!1,crossOrigin:!1,errorOverlayUrl:"",zIndex:1,className:""},initialize:function(url,bounds,options){this._url=url,this._bounds=toLatLngBounds(bounds),setOptions(this,options)},onAdd:function(){this._image||(this._initImage(),this.options.opacity<1&&this._updateOpacity()),this.options.interactive&&(addClass(this._image,"leaflet-interactive"),this.addInteractiveTarget(this._image)),this.getPane().appendChild(this._image),this._reset()},onRemove:function(){remove(this._image),this.options.interactive&&this.removeInteractiveTarget(this._image)},setOpacity:function(opacity){return this.options.opacity=opacity,this._image&&this._updateOpacity(),this},setStyle:function(styleOpts){return styleOpts.opacity&&this.setOpacity(styleOpts.opacity),this},bringToFront:function(){return this._map&&toFront(this._image),this},bringToBack:function(){return this._map&&toBack(this._image),this},setUrl:function(url){return this._url=url,this._image&&(this._image.src=url),this},setBounds:function(bounds){return this._bounds=toLatLngBounds(bounds),this._map&&this._reset(),this},getEvents:function(){var events={zoom:this._reset,viewreset:this._reset};return this._zoomAnimated&&(events.zoomanim=this._animateZoom),events},setZIndex:function(value){return this.options.zIndex=value,this._updateZIndex(),this},getBounds:function(){return this._bounds},getElement:function(){return this._image},_initImage:function(){var wasElementSupplied="IMG"===this._url.tagName,img=this._image=wasElementSupplied?this._url:create$1("img");addClass(img,"leaflet-image-layer"),this._zoomAnimated&&addClass(img,"leaflet-zoom-animated"),this.options.className&&addClass(img,this.options.className),img.onselectstart=falseFn,img.onmousemove=falseFn,img.onload=bind(this.fire,this,"load"),img.onerror=bind(this._overlayOnError,this,"error"),(this.options.crossOrigin||""===this.options.crossOrigin)&&(img.crossOrigin=!0===this.options.crossOrigin?"":this.options.crossOrigin),this.options.zIndex&&this._updateZIndex(),wasElementSupplied?this._url=img.src:(img.src=this._url,img.alt=this.options.alt)},_animateZoom:function(e){var scale=this._map.getZoomScale(e.zoom),offset=this._map._latLngBoundsToNewLayerBounds(this._bounds,e.zoom,e.center).min;setTransform(this._image,offset,scale)},_reset:function(){var image=this._image,bounds=new Bounds(this._map.latLngToLayerPoint(this._bounds.getNorthWest()),this._map.latLngToLayerPoint(this._bounds.getSouthEast())),size=bounds.getSize();setPosition(image,bounds.min),image.style.width=size.x+"px",image.style.height=size.y+"px"},_updateOpacity:function(){setOpacity(this._image,this.options.opacity)},_updateZIndex:function(){this._image&&void 0!==this.options.zIndex&&null!==this.options.zIndex&&(this._image.style.zIndex=this.options.zIndex)},_overlayOnError:function(){this.fire("error");var errorUrl=this.options.errorOverlayUrl;errorUrl&&this._url!==errorUrl&&(this._url=errorUrl,this._image.src=errorUrl)},getCenter:function(){return this._bounds.getCenter()}}),imageOverlay=function(url,bounds,options){return new ImageOverlay(url,bounds,options)},VideoOverlay=ImageOverlay.extend({options:{autoplay:!0,loop:!0,keepAspectRatio:!0,muted:!1,playsInline:!0},_initImage:function(){var wasElementSupplied="VIDEO"===this._url.tagName,vid=this._image=wasElementSupplied?this._url:create$1("video");if(addClass(vid,"leaflet-image-layer"),this._zoomAnimated&&addClass(vid,"leaflet-zoom-animated"),this.options.className&&addClass(vid,this.options.className),vid.onselectstart=falseFn,vid.onmousemove=falseFn,vid.onloadeddata=bind(this.fire,this,"load"),wasElementSupplied){for(var sourceElements=vid.getElementsByTagName("source"),sources=[],j=0;j<sourceElements.length;j++)sources.push(sourceElements[j].src);this._url=sourceElements.length>0?sources:[vid.src]}else{isArray(this._url)||(this._url=[this._url]),!this.options.keepAspectRatio&&Object.prototype.hasOwnProperty.call(vid.style,"objectFit")&&(vid.style.objectFit="fill"),vid.autoplay=!!this.options.autoplay,vid.loop=!!this.options.loop,vid.muted=!!this.options.muted,vid.playsInline=!!this.options.playsInline;for(var i=0;i<this._url.length;i++){var source=create$1("source");source.src=this._url[i],vid.appendChild(source)}}}});function videoOverlay(video,bounds,options){return new VideoOverlay(video,bounds,options)}var SVGOverlay=ImageOverlay.extend({_initImage:function(){var el=this._image=this._url;addClass(el,"leaflet-image-layer"),this._zoomAnimated&&addClass(el,"leaflet-zoom-animated"),this.options.className&&addClass(el,this.options.className),el.onselectstart=falseFn,el.onmousemove=falseFn}});function svgOverlay(el,bounds,options){return new SVGOverlay(el,bounds,options)}var DivOverlay=Layer.extend({options:{interactive:!1,offset:[0,0],className:"",pane:void 0,content:""},initialize:function(options,source){options&&(options instanceof LatLng||isArray(options))?(this._latlng=toLatLng(options),setOptions(this,source)):(setOptions(this,options),this._source=source),this.options.content&&(this._content=this.options.content)},openOn:function(map){return(map=arguments.length?map:this._source._map).hasLayer(this)||map.addLayer(this),this},close:function(){return this._map&&this._map.removeLayer(this),this},toggle:function(layer){return this._map?this.close():(arguments.length?this._source=layer:layer=this._source,this._prepareOpen(),this.openOn(layer._map)),this},onAdd:function(map){this._zoomAnimated=map._zoomAnimated,this._container||this._initLayout(),map._fadeAnimated&&setOpacity(this._container,0),clearTimeout(this._removeTimeout),this.getPane().appendChild(this._container),this.update(),map._fadeAnimated&&setOpacity(this._container,1),this.bringToFront(),this.options.interactive&&(addClass(this._container,"leaflet-interactive"),this.addInteractiveTarget(this._container))},onRemove:function(map){map._fadeAnimated?(setOpacity(this._container,0),this._removeTimeout=setTimeout(bind(remove,void 0,this._container),200)):remove(this._container),this.options.interactive&&(removeClass(this._container,"leaflet-interactive"),this.removeInteractiveTarget(this._container))},getLatLng:function(){return this._latlng},setLatLng:function(latlng){return this._latlng=toLatLng(latlng),this._map&&(this._updatePosition(),this._adjustPan()),this},getContent:function(){return this._content},setContent:function(content){return this._content=content,this.update(),this},getElement:function(){return this._container},update:function(){this._map&&(this._container.style.visibility="hidden",this._updateContent(),this._updateLayout(),this._updatePosition(),this._container.style.visibility="",this._adjustPan())},getEvents:function(){var events={zoom:this._updatePosition,viewreset:this._updatePosition};return this._zoomAnimated&&(events.zoomanim=this._animateZoom),events},isOpen:function(){return!!this._map&&this._map.hasLayer(this)},bringToFront:function(){return this._map&&toFront(this._container),this},bringToBack:function(){return this._map&&toBack(this._container),this},_prepareOpen:function(latlng){var source=this._source;if(!source._map)return!1;if(source instanceof FeatureGroup){source=null;var layers=this._source._layers;for(var id in layers)if(layers[id]._map){source=layers[id];break}if(!source)return!1;this._source=source}if(!latlng)if(source.getCenter)latlng=source.getCenter();else if(source.getLatLng)latlng=source.getLatLng();else{if(!source.getBounds)throw new Error("Unable to get source layer LatLng.");latlng=source.getBounds().getCenter()}return this.setLatLng(latlng),this._map&&this.update(),!0},_updateContent:function(){if(this._content){var node=this._contentNode,content="function"==typeof this._content?this._content(this._source||this):this._content;if("string"==typeof content)node.innerHTML=content;else{for(;node.hasChildNodes();)node.removeChild(node.firstChild);node.appendChild(content)}this.fire("contentupdate")}},_updatePosition:function(){if(this._map){var pos=this._map.latLngToLayerPoint(this._latlng),offset=toPoint(this.options.offset),anchor=this._getAnchor();this._zoomAnimated?setPosition(this._container,pos.add(anchor)):offset=offset.add(pos).add(anchor);var bottom=this._containerBottom=-offset.y,left=this._containerLeft=-Math.round(this._containerWidth/2)+offset.x;this._container.style.bottom=bottom+"px",this._container.style.left=left+"px"}},_getAnchor:function(){return[0,0]}});Map.include({_initOverlay:function(OverlayClass,content,latlng,options){var overlay=content;return overlay instanceof OverlayClass||(overlay=new OverlayClass(options).setContent(content)),latlng&&overlay.setLatLng(latlng),overlay}}),Layer.include({_initOverlay:function(OverlayClass,old,content,options){var overlay=content;return overlay instanceof OverlayClass?(setOptions(overlay,options),overlay._source=this):(overlay=old&&!options?old:new OverlayClass(options,this)).setContent(content),overlay}});var Popup=DivOverlay.extend({options:{pane:"popupPane",offset:[0,7],maxWidth:300,minWidth:50,maxHeight:null,autoPan:!0,autoPanPaddingTopLeft:null,autoPanPaddingBottomRight:null,autoPanPadding:[5,5],keepInView:!1,closeButton:!0,autoClose:!0,closeOnEscapeKey:!0,className:""},openOn:function(map){return!(map=arguments.length?map:this._source._map).hasLayer(this)&&map._popup&&map._popup.options.autoClose&&map.removeLayer(map._popup),map._popup=this,DivOverlay.prototype.openOn.call(this,map)},onAdd:function(map){DivOverlay.prototype.onAdd.call(this,map),map.fire("popupopen",{popup:this}),this._source&&(this._source.fire("popupopen",{popup:this},!0),this._source instanceof Path||this._source.on("preclick",stopPropagation))},onRemove:function(map){DivOverlay.prototype.onRemove.call(this,map),map.fire("popupclose",{popup:this}),this._source&&(this._source.fire("popupclose",{popup:this},!0),this._source instanceof Path||this._source.off("preclick",stopPropagation))},getEvents:function(){var events=DivOverlay.prototype.getEvents.call(this);return(void 0!==this.options.closeOnClick?this.options.closeOnClick:this._map.options.closePopupOnClick)&&(events.preclick=this.close),this.options.keepInView&&(events.moveend=this._adjustPan),events},_initLayout:function(){var prefix="leaflet-popup",container=this._container=create$1("div",prefix+" "+(this.options.className||"")+" leaflet-zoom-animated"),wrapper=this._wrapper=create$1("div",prefix+"-content-wrapper",container);if(this._contentNode=create$1("div",prefix+"-content",wrapper),disableClickPropagation(container),disableScrollPropagation(this._contentNode),on(container,"contextmenu",stopPropagation),this._tipContainer=create$1("div",prefix+"-tip-container",container),this._tip=create$1("div",prefix+"-tip",this._tipContainer),this.options.closeButton){var closeButton=this._closeButton=create$1("a",prefix+"-close-button",container);closeButton.setAttribute("role","button"),closeButton.setAttribute("aria-label","Close popup"),closeButton.href="#close",closeButton.innerHTML='<span aria-hidden="true">&#215;</span>',on(closeButton,"click",(function(ev){preventDefault(ev),this.close()}),this)}},_updateLayout:function(){var container=this._contentNode,style=container.style;style.width="",style.whiteSpace="nowrap";var width=container.offsetWidth;width=Math.min(width,this.options.maxWidth),width=Math.max(width,this.options.minWidth),style.width=width+1+"px",style.whiteSpace="",style.height="";var height=container.offsetHeight,maxHeight=this.options.maxHeight,scrolledClass="leaflet-popup-scrolled";maxHeight&&height>maxHeight?(style.height=maxHeight+"px",addClass(container,scrolledClass)):removeClass(container,scrolledClass),this._containerWidth=this._container.offsetWidth},_animateZoom:function(e){var pos=this._map._latLngToNewLayerPoint(this._latlng,e.zoom,e.center),anchor=this._getAnchor();setPosition(this._container,pos.add(anchor))},_adjustPan:function(){if(this.options.autoPan)if(this._map._panAnim&&this._map._panAnim.stop(),this._autopanning)this._autopanning=!1;else{var map=this._map,marginBottom=parseInt(getStyle(this._container,"marginBottom"),10)||0,containerHeight=this._container.offsetHeight+marginBottom,containerWidth=this._containerWidth,layerPos=new Point(this._containerLeft,-containerHeight-this._containerBottom);layerPos._add(getPosition(this._container));var containerPos=map.layerPointToContainerPoint(layerPos),padding=toPoint(this.options.autoPanPadding),paddingTL=toPoint(this.options.autoPanPaddingTopLeft||padding),paddingBR=toPoint(this.options.autoPanPaddingBottomRight||padding),size=map.getSize(),dx=0,dy=0;containerPos.x+containerWidth+paddingBR.x>size.x&&(dx=containerPos.x+containerWidth-size.x+paddingBR.x),containerPos.x-dx-paddingTL.x<0&&(dx=containerPos.x-paddingTL.x),containerPos.y+containerHeight+paddingBR.y>size.y&&(dy=containerPos.y+containerHeight-size.y+paddingBR.y),containerPos.y-dy-paddingTL.y<0&&(dy=containerPos.y-paddingTL.y),(dx||dy)&&(this.options.keepInView&&(this._autopanning=!0),map.fire("autopanstart").panBy([dx,dy]))}},_getAnchor:function(){return toPoint(this._source&&this._source._getPopupAnchor?this._source._getPopupAnchor():[0,0])}}),popup=function(options,source){return new Popup(options,source)};Map.mergeOptions({closePopupOnClick:!0}),Map.include({openPopup:function(popup,latlng,options){return this._initOverlay(Popup,popup,latlng,options).openOn(this),this},closePopup:function(popup){return(popup=arguments.length?popup:this._popup)&&popup.close(),this}}),Layer.include({bindPopup:function(content,options){return this._popup=this._initOverlay(Popup,this._popup,content,options),this._popupHandlersAdded||(this.on({click:this._openPopup,keypress:this._onKeyPress,remove:this.closePopup,move:this._movePopup}),this._popupHandlersAdded=!0),this},unbindPopup:function(){return this._popup&&(this.off({click:this._openPopup,keypress:this._onKeyPress,remove:this.closePopup,move:this._movePopup}),this._popupHandlersAdded=!1,this._popup=null),this},openPopup:function(latlng){return this._popup&&(this instanceof FeatureGroup||(this._popup._source=this),this._popup._prepareOpen(latlng||this._latlng)&&this._popup.openOn(this._map)),this},closePopup:function(){return this._popup&&this._popup.close(),this},togglePopup:function(){return this._popup&&this._popup.toggle(this),this},isPopupOpen:function(){return!!this._popup&&this._popup.isOpen()},setPopupContent:function(content){return this._popup&&this._popup.setContent(content),this},getPopup:function(){return this._popup},_openPopup:function(e){if(this._popup&&this._map){stop(e);var target=e.layer||e.target;this._popup._source!==target||target instanceof Path?(this._popup._source=target,this.openPopup(e.latlng)):this._map.hasLayer(this._popup)?this.closePopup():this.openPopup(e.latlng)}},_movePopup:function(e){this._popup.setLatLng(e.latlng)},_onKeyPress:function(e){13===e.originalEvent.keyCode&&this._openPopup(e)}});var Tooltip=DivOverlay.extend({options:{pane:"tooltipPane",offset:[0,0],direction:"auto",permanent:!1,sticky:!1,opacity:.9},onAdd:function(map){DivOverlay.prototype.onAdd.call(this,map),this.setOpacity(this.options.opacity),map.fire("tooltipopen",{tooltip:this}),this._source&&(this.addEventParent(this._source),this._source.fire("tooltipopen",{tooltip:this},!0))},onRemove:function(map){DivOverlay.prototype.onRemove.call(this,map),map.fire("tooltipclose",{tooltip:this}),this._source&&(this.removeEventParent(this._source),this._source.fire("tooltipclose",{tooltip:this},!0))},getEvents:function(){var events=DivOverlay.prototype.getEvents.call(this);return this.options.permanent||(events.preclick=this.close),events},_initLayout:function(){var className="leaflet-tooltip "+(this.options.className||"")+" leaflet-zoom-"+(this._zoomAnimated?"animated":"hide");this._contentNode=this._container=create$1("div",className),this._container.setAttribute("role","tooltip"),this._container.setAttribute("id","leaflet-tooltip-"+stamp(this))},_updateLayout:function(){},_adjustPan:function(){},_setPosition:function(pos){var subX,subY,map=this._map,container=this._container,centerPoint=map.latLngToContainerPoint(map.getCenter()),tooltipPoint=map.layerPointToContainerPoint(pos),direction=this.options.direction,tooltipWidth=container.offsetWidth,tooltipHeight=container.offsetHeight,offset=toPoint(this.options.offset),anchor=this._getAnchor();"top"===direction?(subX=tooltipWidth/2,subY=tooltipHeight):"bottom"===direction?(subX=tooltipWidth/2,subY=0):"center"===direction?(subX=tooltipWidth/2,subY=tooltipHeight/2):"right"===direction?(subX=0,subY=tooltipHeight/2):"left"===direction?(subX=tooltipWidth,subY=tooltipHeight/2):tooltipPoint.x<centerPoint.x?(direction="right",subX=0,subY=tooltipHeight/2):(direction="left",subX=tooltipWidth+2*(offset.x+anchor.x),subY=tooltipHeight/2),pos=pos.subtract(toPoint(subX,subY,!0)).add(offset).add(anchor),removeClass(container,"leaflet-tooltip-right"),removeClass(container,"leaflet-tooltip-left"),removeClass(container,"leaflet-tooltip-top"),removeClass(container,"leaflet-tooltip-bottom"),addClass(container,"leaflet-tooltip-"+direction),setPosition(container,pos)},_updatePosition:function(){var pos=this._map.latLngToLayerPoint(this._latlng);this._setPosition(pos)},setOpacity:function(opacity){this.options.opacity=opacity,this._container&&setOpacity(this._container,opacity)},_animateZoom:function(e){var pos=this._map._latLngToNewLayerPoint(this._latlng,e.zoom,e.center);this._setPosition(pos)},_getAnchor:function(){return toPoint(this._source&&this._source._getTooltipAnchor&&!this.options.sticky?this._source._getTooltipAnchor():[0,0])}}),tooltip=function(options,source){return new Tooltip(options,source)};Map.include({openTooltip:function(tooltip,latlng,options){return this._initOverlay(Tooltip,tooltip,latlng,options).openOn(this),this},closeTooltip:function(tooltip){return tooltip.close(),this}}),Layer.include({bindTooltip:function(content,options){return this._tooltip&&this.isTooltipOpen()&&this.unbindTooltip(),this._tooltip=this._initOverlay(Tooltip,this._tooltip,content,options),this._initTooltipInteractions(),this._tooltip.options.permanent&&this._map&&this._map.hasLayer(this)&&this.openTooltip(),this},unbindTooltip:function(){return this._tooltip&&(this._initTooltipInteractions(!0),this.closeTooltip(),this._tooltip=null),this},_initTooltipInteractions:function(remove){if(remove||!this._tooltipHandlersAdded){var onOff=remove?"off":"on",events={remove:this.closeTooltip,move:this._moveTooltip};this._tooltip.options.permanent?events.add=this._openTooltip:(events.mouseover=this._openTooltip,events.mouseout=this.closeTooltip,events.click=this._openTooltip,this._map?this._addFocusListeners():events.add=this._addFocusListeners),this._tooltip.options.sticky&&(events.mousemove=this._moveTooltip),this[onOff](events),this._tooltipHandlersAdded=!remove}},openTooltip:function(latlng){return this._tooltip&&(this instanceof FeatureGroup||(this._tooltip._source=this),this._tooltip._prepareOpen(latlng)&&(this._tooltip.openOn(this._map),this.getElement?this._setAriaDescribedByOnLayer(this):this.eachLayer&&this.eachLayer(this._setAriaDescribedByOnLayer,this))),this},closeTooltip:function(){if(this._tooltip)return this._tooltip.close()},toggleTooltip:function(){return this._tooltip&&this._tooltip.toggle(this),this},isTooltipOpen:function(){return this._tooltip.isOpen()},setTooltipContent:function(content){return this._tooltip&&this._tooltip.setContent(content),this},getTooltip:function(){return this._tooltip},_addFocusListeners:function(){this.getElement?this._addFocusListenersOnLayer(this):this.eachLayer&&this.eachLayer(this._addFocusListenersOnLayer,this)},_addFocusListenersOnLayer:function(layer){var el="function"==typeof layer.getElement&&layer.getElement();el&&(on(el,"focus",(function(){this._tooltip._source=layer,this.openTooltip()}),this),on(el,"blur",this.closeTooltip,this))},_setAriaDescribedByOnLayer:function(layer){var el="function"==typeof layer.getElement&&layer.getElement();el&&el.setAttribute("aria-describedby",this._tooltip._container.id)},_openTooltip:function(e){if(this._tooltip&&this._map)if(this._map.dragging&&this._map.dragging.moving()&&!this._openOnceFlag){this._openOnceFlag=!0;var that=this;this._map.once("moveend",(function(){that._openOnceFlag=!1,that._openTooltip(e)}))}else this._tooltip._source=e.layer||e.target,this.openTooltip(this._tooltip.options.sticky?e.latlng:void 0)},_moveTooltip:function(e){var containerPoint,layerPoint,latlng=e.latlng;this._tooltip.options.sticky&&e.originalEvent&&(containerPoint=this._map.mouseEventToContainerPoint(e.originalEvent),layerPoint=this._map.containerPointToLayerPoint(containerPoint),latlng=this._map.layerPointToLatLng(layerPoint)),this._tooltip.setLatLng(latlng)}});var DivIcon=Icon.extend({options:{iconSize:[12,12],html:!1,bgPos:null,className:"leaflet-div-icon"},createIcon:function(oldIcon){var div=oldIcon&&"DIV"===oldIcon.tagName?oldIcon:document.createElement("div"),options=this.options;if(options.html instanceof Element?(empty(div),div.appendChild(options.html)):div.innerHTML=!1!==options.html?options.html:"",options.bgPos){var bgPos=toPoint(options.bgPos);div.style.backgroundPosition=-bgPos.x+"px "+-bgPos.y+"px"}return this._setIconStyles(div,"icon"),div},createShadow:function(){return null}});function divIcon(options){return new DivIcon(options)}Icon.Default=IconDefault;var GridLayer=Layer.extend({options:{tileSize:256,opacity:1,updateWhenIdle:Browser.mobile,updateWhenZooming:!0,updateInterval:200,zIndex:1,bounds:null,minZoom:0,maxZoom:void 0,maxNativeZoom:void 0,minNativeZoom:void 0,noWrap:!1,pane:"tilePane",className:"",keepBuffer:2},initialize:function(options){setOptions(this,options)},onAdd:function(){this._initContainer(),this._levels={},this._tiles={},this._resetView()},beforeAdd:function(map){map._addZoomLimit(this)},onRemove:function(map){this._removeAllTiles(),remove(this._container),map._removeZoomLimit(this),this._container=null,this._tileZoom=void 0},bringToFront:function(){return this._map&&(toFront(this._container),this._setAutoZIndex(Math.max)),this},bringToBack:function(){return this._map&&(toBack(this._container),this._setAutoZIndex(Math.min)),this},getContainer:function(){return this._container},setOpacity:function(opacity){return this.options.opacity=opacity,this._updateOpacity(),this},setZIndex:function(zIndex){return this.options.zIndex=zIndex,this._updateZIndex(),this},isLoading:function(){return this._loading},redraw:function(){if(this._map){this._removeAllTiles();var tileZoom=this._clampZoom(this._map.getZoom());tileZoom!==this._tileZoom&&(this._tileZoom=tileZoom,this._updateLevels()),this._update()}return this},getEvents:function(){var events={viewprereset:this._invalidateAll,viewreset:this._resetView,zoom:this._resetView,moveend:this._onMoveEnd};return this.options.updateWhenIdle||(this._onMove||(this._onMove=throttle(this._onMoveEnd,this.options.updateInterval,this)),events.move=this._onMove),this._zoomAnimated&&(events.zoomanim=this._animateZoom),events},createTile:function(){return document.createElement("div")},getTileSize:function(){var s=this.options.tileSize;return s instanceof Point?s:new Point(s,s)},_updateZIndex:function(){this._container&&void 0!==this.options.zIndex&&null!==this.options.zIndex&&(this._container.style.zIndex=this.options.zIndex)},_setAutoZIndex:function(compare){for(var zIndex,layers=this.getPane().children,edgeZIndex=-compare(-1/0,1/0),i=0,len=layers.length;i<len;i++)zIndex=layers[i].style.zIndex,layers[i]!==this._container&&zIndex&&(edgeZIndex=compare(edgeZIndex,+zIndex));isFinite(edgeZIndex)&&(this.options.zIndex=edgeZIndex+compare(-1,1),this._updateZIndex())},_updateOpacity:function(){if(this._map&&!Browser.ielt9){setOpacity(this._container,this.options.opacity);var now=+new Date,nextFrame=!1,willPrune=!1;for(var key in this._tiles){var tile=this._tiles[key];if(tile.current&&tile.loaded){var fade=Math.min(1,(now-tile.loaded)/200);setOpacity(tile.el,fade),fade<1?nextFrame=!0:(tile.active?willPrune=!0:this._onOpaqueTile(tile),tile.active=!0)}}willPrune&&!this._noPrune&&this._pruneTiles(),nextFrame&&(cancelAnimFrame(this._fadeFrame),this._fadeFrame=requestAnimFrame(this._updateOpacity,this))}},_onOpaqueTile:falseFn,_initContainer:function(){this._container||(this._container=create$1("div","leaflet-layer "+(this.options.className||"")),this._updateZIndex(),this.options.opacity<1&&this._updateOpacity(),this.getPane().appendChild(this._container))},_updateLevels:function(){var zoom=this._tileZoom,maxZoom=this.options.maxZoom;if(void 0!==zoom){for(var z in this._levels)z=Number(z),this._levels[z].el.children.length||z===zoom?(this._levels[z].el.style.zIndex=maxZoom-Math.abs(zoom-z),this._onUpdateLevel(z)):(remove(this._levels[z].el),this._removeTilesAtZoom(z),this._onRemoveLevel(z),delete this._levels[z]);var level=this._levels[zoom],map=this._map;return level||((level=this._levels[zoom]={}).el=create$1("div","leaflet-tile-container leaflet-zoom-animated",this._container),level.el.style.zIndex=maxZoom,level.origin=map.project(map.unproject(map.getPixelOrigin()),zoom).round(),level.zoom=zoom,this._setZoomTransform(level,map.getCenter(),map.getZoom()),falseFn(level.el.offsetWidth),this._onCreateLevel(level)),this._level=level,level}},_onUpdateLevel:falseFn,_onRemoveLevel:falseFn,_onCreateLevel:falseFn,_pruneTiles:function(){if(this._map){var key,tile,zoom=this._map.getZoom();if(zoom>this.options.maxZoom||zoom<this.options.minZoom)this._removeAllTiles();else{for(key in this._tiles)(tile=this._tiles[key]).retain=tile.current;for(key in this._tiles)if((tile=this._tiles[key]).current&&!tile.active){var coords=tile.coords;this._retainParent(coords.x,coords.y,coords.z,coords.z-5)||this._retainChildren(coords.x,coords.y,coords.z,coords.z+2)}for(key in this._tiles)this._tiles[key].retain||this._removeTile(key)}}},_removeTilesAtZoom:function(zoom){for(var key in this._tiles)this._tiles[key].coords.z===zoom&&this._removeTile(key)},_removeAllTiles:function(){for(var key in this._tiles)this._removeTile(key)},_invalidateAll:function(){for(var z in this._levels)remove(this._levels[z].el),this._onRemoveLevel(Number(z)),delete this._levels[z];this._removeAllTiles(),this._tileZoom=void 0},_retainParent:function(x,y,z,minZoom){var x2=Math.floor(x/2),y2=Math.floor(y/2),z2=z-1,coords2=new Point(+x2,+y2);coords2.z=+z2;var key=this._tileCoordsToKey(coords2),tile=this._tiles[key];return tile&&tile.active?(tile.retain=!0,!0):(tile&&tile.loaded&&(tile.retain=!0),z2>minZoom&&this._retainParent(x2,y2,z2,minZoom))},_retainChildren:function(x,y,z,maxZoom){for(var i=2*x;i<2*x+2;i++)for(var j=2*y;j<2*y+2;j++){var coords=new Point(i,j);coords.z=z+1;var key=this._tileCoordsToKey(coords),tile=this._tiles[key];tile&&tile.active?tile.retain=!0:(tile&&tile.loaded&&(tile.retain=!0),z+1<maxZoom&&this._retainChildren(i,j,z+1,maxZoom))}},_resetView:function(e){var animating=e&&(e.pinch||e.flyTo);this._setView(this._map.getCenter(),this._map.getZoom(),animating,animating)},_animateZoom:function(e){this._setView(e.center,e.zoom,!0,e.noUpdate)},_clampZoom:function(zoom){var options=this.options;return void 0!==options.minNativeZoom&&zoom<options.minNativeZoom?options.minNativeZoom:void 0!==options.maxNativeZoom&&options.maxNativeZoom<zoom?options.maxNativeZoom:zoom},_setView:function(center,zoom,noPrune,noUpdate){var tileZoom=Math.round(zoom);tileZoom=void 0!==this.options.maxZoom&&tileZoom>this.options.maxZoom||void 0!==this.options.minZoom&&tileZoom<this.options.minZoom?void 0:this._clampZoom(tileZoom);var tileZoomChanged=this.options.updateWhenZooming&&tileZoom!==this._tileZoom;noUpdate&&!tileZoomChanged||(this._tileZoom=tileZoom,this._abortLoading&&this._abortLoading(),this._updateLevels(),this._resetGrid(),void 0!==tileZoom&&this._update(center),noPrune||this._pruneTiles(),this._noPrune=!!noPrune),this._setZoomTransforms(center,zoom)},_setZoomTransforms:function(center,zoom){for(var i in this._levels)this._setZoomTransform(this._levels[i],center,zoom)},_setZoomTransform:function(level,center,zoom){var scale=this._map.getZoomScale(zoom,level.zoom),translate=level.origin.multiplyBy(scale).subtract(this._map._getNewPixelOrigin(center,zoom)).round();Browser.any3d?setTransform(level.el,translate,scale):setPosition(level.el,translate)},_resetGrid:function(){var map=this._map,crs=map.options.crs,tileSize=this._tileSize=this.getTileSize(),tileZoom=this._tileZoom,bounds=this._map.getPixelWorldBounds(this._tileZoom);bounds&&(this._globalTileRange=this._pxBoundsToTileRange(bounds)),this._wrapX=crs.wrapLng&&!this.options.noWrap&&[Math.floor(map.project([0,crs.wrapLng[0]],tileZoom).x/tileSize.x),Math.ceil(map.project([0,crs.wrapLng[1]],tileZoom).x/tileSize.y)],this._wrapY=crs.wrapLat&&!this.options.noWrap&&[Math.floor(map.project([crs.wrapLat[0],0],tileZoom).y/tileSize.x),Math.ceil(map.project([crs.wrapLat[1],0],tileZoom).y/tileSize.y)]},_onMoveEnd:function(){this._map&&!this._map._animatingZoom&&this._update()},_getTiledPixelBounds:function(center){var map=this._map,mapZoom=map._animatingZoom?Math.max(map._animateToZoom,map.getZoom()):map.getZoom(),scale=map.getZoomScale(mapZoom,this._tileZoom),pixelCenter=map.project(center,this._tileZoom).floor(),halfSize=map.getSize().divideBy(2*scale);return new Bounds(pixelCenter.subtract(halfSize),pixelCenter.add(halfSize))},_update:function(center){var map=this._map;if(map){var zoom=this._clampZoom(map.getZoom());if(void 0===center&&(center=map.getCenter()),void 0!==this._tileZoom){var pixelBounds=this._getTiledPixelBounds(center),tileRange=this._pxBoundsToTileRange(pixelBounds),tileCenter=tileRange.getCenter(),queue=[],margin=this.options.keepBuffer,noPruneRange=new Bounds(tileRange.getBottomLeft().subtract([margin,-margin]),tileRange.getTopRight().add([margin,-margin]));if(!(isFinite(tileRange.min.x)&&isFinite(tileRange.min.y)&&isFinite(tileRange.max.x)&&isFinite(tileRange.max.y)))throw new Error("Attempted to load an infinite number of tiles");for(var key in this._tiles){var c=this._tiles[key].coords;c.z===this._tileZoom&&noPruneRange.contains(new Point(c.x,c.y))||(this._tiles[key].current=!1)}if(Math.abs(zoom-this._tileZoom)>1)this._setView(center,zoom);else{for(var j=tileRange.min.y;j<=tileRange.max.y;j++)for(var i=tileRange.min.x;i<=tileRange.max.x;i++){var coords=new Point(i,j);if(coords.z=this._tileZoom,this._isValidTile(coords)){var tile=this._tiles[this._tileCoordsToKey(coords)];tile?tile.current=!0:queue.push(coords)}}if(queue.sort((function(a,b){return a.distanceTo(tileCenter)-b.distanceTo(tileCenter)})),0!==queue.length){this._loading||(this._loading=!0,this.fire("loading"));var fragment=document.createDocumentFragment();for(i=0;i<queue.length;i++)this._addTile(queue[i],fragment);this._level.el.appendChild(fragment)}}}}},_isValidTile:function(coords){var crs=this._map.options.crs;if(!crs.infinite){var bounds=this._globalTileRange;if(!crs.wrapLng&&(coords.x<bounds.min.x||coords.x>bounds.max.x)||!crs.wrapLat&&(coords.y<bounds.min.y||coords.y>bounds.max.y))return!1}if(!this.options.bounds)return!0;var tileBounds=this._tileCoordsToBounds(coords);return toLatLngBounds(this.options.bounds).overlaps(tileBounds)},_keyToBounds:function(key){return this._tileCoordsToBounds(this._keyToTileCoords(key))},_tileCoordsToNwSe:function(coords){var map=this._map,tileSize=this.getTileSize(),nwPoint=coords.scaleBy(tileSize),sePoint=nwPoint.add(tileSize);return[map.unproject(nwPoint,coords.z),map.unproject(sePoint,coords.z)]},_tileCoordsToBounds:function(coords){var bp=this._tileCoordsToNwSe(coords),bounds=new LatLngBounds(bp[0],bp[1]);return this.options.noWrap||(bounds=this._map.wrapLatLngBounds(bounds)),bounds},_tileCoordsToKey:function(coords){return coords.x+":"+coords.y+":"+coords.z},_keyToTileCoords:function(key){var k=key.split(":"),coords=new Point(+k[0],+k[1]);return coords.z=+k[2],coords},_removeTile:function(key){var tile=this._tiles[key];tile&&(remove(tile.el),delete this._tiles[key],this.fire("tileunload",{tile:tile.el,coords:this._keyToTileCoords(key)}))},_initTile:function(tile){addClass(tile,"leaflet-tile");var tileSize=this.getTileSize();tile.style.width=tileSize.x+"px",tile.style.height=tileSize.y+"px",tile.onselectstart=falseFn,tile.onmousemove=falseFn,Browser.ielt9&&this.options.opacity<1&&setOpacity(tile,this.options.opacity)},_addTile:function(coords,container){var tilePos=this._getTilePos(coords),key=this._tileCoordsToKey(coords),tile=this.createTile(this._wrapCoords(coords),bind(this._tileReady,this,coords));this._initTile(tile),this.createTile.length<2&&requestAnimFrame(bind(this._tileReady,this,coords,null,tile)),setPosition(tile,tilePos),this._tiles[key]={el:tile,coords,current:!0},container.appendChild(tile),this.fire("tileloadstart",{tile,coords})},_tileReady:function(coords,err,tile){err&&this.fire("tileerror",{error:err,tile,coords});var key=this._tileCoordsToKey(coords);(tile=this._tiles[key])&&(tile.loaded=+new Date,this._map._fadeAnimated?(setOpacity(tile.el,0),cancelAnimFrame(this._fadeFrame),this._fadeFrame=requestAnimFrame(this._updateOpacity,this)):(tile.active=!0,this._pruneTiles()),err||(addClass(tile.el,"leaflet-tile-loaded"),this.fire("tileload",{tile:tile.el,coords})),this._noTilesToLoad()&&(this._loading=!1,this.fire("load"),Browser.ielt9||!this._map._fadeAnimated?requestAnimFrame(this._pruneTiles,this):setTimeout(bind(this._pruneTiles,this),250)))},_getTilePos:function(coords){return coords.scaleBy(this.getTileSize()).subtract(this._level.origin)},_wrapCoords:function(coords){var newCoords=new Point(this._wrapX?wrapNum(coords.x,this._wrapX):coords.x,this._wrapY?wrapNum(coords.y,this._wrapY):coords.y);return newCoords.z=coords.z,newCoords},_pxBoundsToTileRange:function(bounds){var tileSize=this.getTileSize();return new Bounds(bounds.min.unscaleBy(tileSize).floor(),bounds.max.unscaleBy(tileSize).ceil().subtract([1,1]))},_noTilesToLoad:function(){for(var key in this._tiles)if(!this._tiles[key].loaded)return!1;return!0}});function gridLayer(options){return new GridLayer(options)}var TileLayer=GridLayer.extend({options:{minZoom:0,maxZoom:18,subdomains:"abc",errorTileUrl:"",zoomOffset:0,tms:!1,zoomReverse:!1,detectRetina:!1,crossOrigin:!1,referrerPolicy:!1},initialize:function(url,options){this._url=url,(options=setOptions(this,options)).detectRetina&&Browser.retina&&options.maxZoom>0?(options.tileSize=Math.floor(options.tileSize/2),options.zoomReverse?(options.zoomOffset--,options.minZoom=Math.min(options.maxZoom,options.minZoom+1)):(options.zoomOffset++,options.maxZoom=Math.max(options.minZoom,options.maxZoom-1)),options.minZoom=Math.max(0,options.minZoom)):options.zoomReverse?options.minZoom=Math.min(options.maxZoom,options.minZoom):options.maxZoom=Math.max(options.minZoom,options.maxZoom),"string"==typeof options.subdomains&&(options.subdomains=options.subdomains.split("")),this.on("tileunload",this._onTileRemove)},setUrl:function(url,noRedraw){return this._url===url&&void 0===noRedraw&&(noRedraw=!0),this._url=url,noRedraw||this.redraw(),this},createTile:function(coords,done){var tile=document.createElement("img");return on(tile,"load",bind(this._tileOnLoad,this,done,tile)),on(tile,"error",bind(this._tileOnError,this,done,tile)),(this.options.crossOrigin||""===this.options.crossOrigin)&&(tile.crossOrigin=!0===this.options.crossOrigin?"":this.options.crossOrigin),"string"==typeof this.options.referrerPolicy&&(tile.referrerPolicy=this.options.referrerPolicy),tile.alt="",tile.src=this.getTileUrl(coords),tile},getTileUrl:function(coords){var data={r:Browser.retina?"@2x":"",s:this._getSubdomain(coords),x:coords.x,y:coords.y,z:this._getZoomForUrl()};if(this._map&&!this._map.options.crs.infinite){var invertedY=this._globalTileRange.max.y-coords.y;this.options.tms&&(data.y=invertedY),data["-y"]=invertedY}return template(this._url,extend(data,this.options))},_tileOnLoad:function(done,tile){Browser.ielt9?setTimeout(bind(done,this,null,tile),0):done(null,tile)},_tileOnError:function(done,tile,e){var errorUrl=this.options.errorTileUrl;errorUrl&&tile.getAttribute("src")!==errorUrl&&(tile.src=errorUrl),done(e,tile)},_onTileRemove:function(e){e.tile.onload=null},_getZoomForUrl:function(){var zoom=this._tileZoom,maxZoom=this.options.maxZoom;return this.options.zoomReverse&&(zoom=maxZoom-zoom),zoom+this.options.zoomOffset},_getSubdomain:function(tilePoint){var index=Math.abs(tilePoint.x+tilePoint.y)%this.options.subdomains.length;return this.options.subdomains[index]},_abortLoading:function(){var i,tile;for(i in this._tiles)if(this._tiles[i].coords.z!==this._tileZoom&&((tile=this._tiles[i].el).onload=falseFn,tile.onerror=falseFn,!tile.complete)){tile.src=emptyImageUrl;var coords=this._tiles[i].coords;remove(tile),delete this._tiles[i],this.fire("tileabort",{tile,coords})}},_removeTile:function(key){var tile=this._tiles[key];if(tile)return tile.el.setAttribute("src",emptyImageUrl),GridLayer.prototype._removeTile.call(this,key)},_tileReady:function(coords,err,tile){if(this._map&&(!tile||tile.getAttribute("src")!==emptyImageUrl))return GridLayer.prototype._tileReady.call(this,coords,err,tile)}});function tileLayer(url,options){return new TileLayer(url,options)}var TileLayerWMS=TileLayer.extend({defaultWmsParams:{service:"WMS",request:"GetMap",layers:"",styles:"",format:"image/jpeg",transparent:!1,version:"1.1.1"},options:{crs:null,uppercase:!1},initialize:function(url,options){this._url=url;var wmsParams=extend({},this.defaultWmsParams);for(var i in options)i in this.options||(wmsParams[i]=options[i]);var realRetina=(options=setOptions(this,options)).detectRetina&&Browser.retina?2:1,tileSize=this.getTileSize();wmsParams.width=tileSize.x*realRetina,wmsParams.height=tileSize.y*realRetina,this.wmsParams=wmsParams},onAdd:function(map){this._crs=this.options.crs||map.options.crs,this._wmsVersion=parseFloat(this.wmsParams.version);var projectionKey=this._wmsVersion>=1.3?"crs":"srs";this.wmsParams[projectionKey]=this._crs.code,TileLayer.prototype.onAdd.call(this,map)},getTileUrl:function(coords){var tileBounds=this._tileCoordsToNwSe(coords),crs=this._crs,bounds=toBounds(crs.project(tileBounds[0]),crs.project(tileBounds[1])),min=bounds.min,max=bounds.max,bbox=(this._wmsVersion>=1.3&&this._crs===EPSG4326?[min.y,min.x,max.y,max.x]:[min.x,min.y,max.x,max.y]).join(","),url=TileLayer.prototype.getTileUrl.call(this,coords);return url+getParamString(this.wmsParams,url,this.options.uppercase)+(this.options.uppercase?"&BBOX=":"&bbox=")+bbox},setParams:function(params,noRedraw){return extend(this.wmsParams,params),noRedraw||this.redraw(),this}});function tileLayerWMS(url,options){return new TileLayerWMS(url,options)}TileLayer.WMS=TileLayerWMS,tileLayer.wms=tileLayerWMS;var Renderer=Layer.extend({options:{padding:.1},initialize:function(options){setOptions(this,options),stamp(this),this._layers=this._layers||{}},onAdd:function(){this._container||(this._initContainer(),addClass(this._container,"leaflet-zoom-animated")),this.getPane().appendChild(this._container),this._update(),this.on("update",this._updatePaths,this)},onRemove:function(){this.off("update",this._updatePaths,this),this._destroyContainer()},getEvents:function(){var events={viewreset:this._reset,zoom:this._onZoom,moveend:this._update,zoomend:this._onZoomEnd};return this._zoomAnimated&&(events.zoomanim=this._onAnimZoom),events},_onAnimZoom:function(ev){this._updateTransform(ev.center,ev.zoom)},_onZoom:function(){this._updateTransform(this._map.getCenter(),this._map.getZoom())},_updateTransform:function(center,zoom){var scale=this._map.getZoomScale(zoom,this._zoom),viewHalf=this._map.getSize().multiplyBy(.5+this.options.padding),currentCenterPoint=this._map.project(this._center,zoom),topLeftOffset=viewHalf.multiplyBy(-scale).add(currentCenterPoint).subtract(this._map._getNewPixelOrigin(center,zoom));Browser.any3d?setTransform(this._container,topLeftOffset,scale):setPosition(this._container,topLeftOffset)},_reset:function(){for(var id in this._update(),this._updateTransform(this._center,this._zoom),this._layers)this._layers[id]._reset()},_onZoomEnd:function(){for(var id in this._layers)this._layers[id]._project()},_updatePaths:function(){for(var id in this._layers)this._layers[id]._update()},_update:function(){var p=this.options.padding,size=this._map.getSize(),min=this._map.containerPointToLayerPoint(size.multiplyBy(-p)).round();this._bounds=new Bounds(min,min.add(size.multiplyBy(1+2*p)).round()),this._center=this._map.getCenter(),this._zoom=this._map.getZoom()}}),Canvas=Renderer.extend({options:{tolerance:0},getEvents:function(){var events=Renderer.prototype.getEvents.call(this);return events.viewprereset=this._onViewPreReset,events},_onViewPreReset:function(){this._postponeUpdatePaths=!0},onAdd:function(){Renderer.prototype.onAdd.call(this),this._draw()},_initContainer:function(){var container=this._container=document.createElement("canvas");on(container,"mousemove",this._onMouseMove,this),on(container,"click dblclick mousedown mouseup contextmenu",this._onClick,this),on(container,"mouseout",this._handleMouseOut,this),container._leaflet_disable_events=!0,this._ctx=container.getContext("2d")},_destroyContainer:function(){cancelAnimFrame(this._redrawRequest),delete this._ctx,remove(this._container),off(this._container),delete this._container},_updatePaths:function(){if(!this._postponeUpdatePaths){for(var id in this._redrawBounds=null,this._layers)this._layers[id]._update();this._redraw()}},_update:function(){if(!this._map._animatingZoom||!this._bounds){Renderer.prototype._update.call(this);var b=this._bounds,container=this._container,size=b.getSize(),m=Browser.retina?2:1;setPosition(container,b.min),container.width=m*size.x,container.height=m*size.y,container.style.width=size.x+"px",container.style.height=size.y+"px",Browser.retina&&this._ctx.scale(2,2),this._ctx.translate(-b.min.x,-b.min.y),this.fire("update")}},_reset:function(){Renderer.prototype._reset.call(this),this._postponeUpdatePaths&&(this._postponeUpdatePaths=!1,this._updatePaths())},_initPath:function(layer){this._updateDashArray(layer),this._layers[stamp(layer)]=layer;var order=layer._order={layer,prev:this._drawLast,next:null};this._drawLast&&(this._drawLast.next=order),this._drawLast=order,this._drawFirst=this._drawFirst||this._drawLast},_addPath:function(layer){this._requestRedraw(layer)},_removePath:function(layer){var order=layer._order,next=order.next,prev=order.prev;next?next.prev=prev:this._drawLast=prev,prev?prev.next=next:this._drawFirst=next,delete layer._order,delete this._layers[stamp(layer)],this._requestRedraw(layer)},_updatePath:function(layer){this._extendRedrawBounds(layer),layer._project(),layer._update(),this._requestRedraw(layer)},_updateStyle:function(layer){this._updateDashArray(layer),this._requestRedraw(layer)},_updateDashArray:function(layer){if("string"==typeof layer.options.dashArray){var dashValue,i,parts=layer.options.dashArray.split(/[, ]+/),dashArray=[];for(i=0;i<parts.length;i++){if(dashValue=Number(parts[i]),isNaN(dashValue))return;dashArray.push(dashValue)}layer.options._dashArray=dashArray}else layer.options._dashArray=layer.options.dashArray},_requestRedraw:function(layer){this._map&&(this._extendRedrawBounds(layer),this._redrawRequest=this._redrawRequest||requestAnimFrame(this._redraw,this))},_extendRedrawBounds:function(layer){if(layer._pxBounds){var padding=(layer.options.weight||0)+1;this._redrawBounds=this._redrawBounds||new Bounds,this._redrawBounds.extend(layer._pxBounds.min.subtract([padding,padding])),this._redrawBounds.extend(layer._pxBounds.max.add([padding,padding]))}},_redraw:function(){this._redrawRequest=null,this._redrawBounds&&(this._redrawBounds.min._floor(),this._redrawBounds.max._ceil()),this._clear(),this._draw(),this._redrawBounds=null},_clear:function(){var bounds=this._redrawBounds;if(bounds){var size=bounds.getSize();this._ctx.clearRect(bounds.min.x,bounds.min.y,size.x,size.y)}else this._ctx.save(),this._ctx.setTransform(1,0,0,1,0,0),this._ctx.clearRect(0,0,this._container.width,this._container.height),this._ctx.restore()},_draw:function(){var layer,bounds=this._redrawBounds;if(this._ctx.save(),bounds){var size=bounds.getSize();this._ctx.beginPath(),this._ctx.rect(bounds.min.x,bounds.min.y,size.x,size.y),this._ctx.clip()}this._drawing=!0;for(var order=this._drawFirst;order;order=order.next)layer=order.layer,(!bounds||layer._pxBounds&&layer._pxBounds.intersects(bounds))&&layer._updatePath();this._drawing=!1,this._ctx.restore()},_updatePoly:function(layer,closed){if(this._drawing){var i,j,len2,p,parts=layer._parts,len=parts.length,ctx=this._ctx;if(len){for(ctx.beginPath(),i=0;i<len;i++){for(j=0,len2=parts[i].length;j<len2;j++)p=parts[i][j],ctx[j?"lineTo":"moveTo"](p.x,p.y);closed&&ctx.closePath()}this._fillStroke(ctx,layer)}}},_updateCircle:function(layer){if(this._drawing&&!layer._empty()){var p=layer._point,ctx=this._ctx,r=Math.max(Math.round(layer._radius),1),s=(Math.max(Math.round(layer._radiusY),1)||r)/r;1!==s&&(ctx.save(),ctx.scale(1,s)),ctx.beginPath(),ctx.arc(p.x,p.y/s,r,0,2*Math.PI,!1),1!==s&&ctx.restore(),this._fillStroke(ctx,layer)}},_fillStroke:function(ctx,layer){var options=layer.options;options.fill&&(ctx.globalAlpha=options.fillOpacity,ctx.fillStyle=options.fillColor||options.color,ctx.fill(options.fillRule||"evenodd")),options.stroke&&0!==options.weight&&(ctx.setLineDash&&ctx.setLineDash(layer.options&&layer.options._dashArray||[]),ctx.globalAlpha=options.opacity,ctx.lineWidth=options.weight,ctx.strokeStyle=options.color,ctx.lineCap=options.lineCap,ctx.lineJoin=options.lineJoin,ctx.stroke())},_onClick:function(e){for(var layer,clickedLayer,point=this._map.mouseEventToLayerPoint(e),order=this._drawFirst;order;order=order.next)(layer=order.layer).options.interactive&&layer._containsPoint(point)&&("click"!==e.type&&"preclick"!==e.type||!this._map._draggableMoved(layer))&&(clickedLayer=layer);this._fireEvent(!!clickedLayer&&[clickedLayer],e)},_onMouseMove:function(e){if(this._map&&!this._map.dragging.moving()&&!this._map._animatingZoom){var point=this._map.mouseEventToLayerPoint(e);this._handleMouseHover(e,point)}},_handleMouseOut:function(e){var layer=this._hoveredLayer;layer&&(removeClass(this._container,"leaflet-interactive"),this._fireEvent([layer],e,"mouseout"),this._hoveredLayer=null,this._mouseHoverThrottled=!1)},_handleMouseHover:function(e,point){if(!this._mouseHoverThrottled){for(var layer,candidateHoveredLayer,order=this._drawFirst;order;order=order.next)(layer=order.layer).options.interactive&&layer._containsPoint(point)&&(candidateHoveredLayer=layer);candidateHoveredLayer!==this._hoveredLayer&&(this._handleMouseOut(e),candidateHoveredLayer&&(addClass(this._container,"leaflet-interactive"),this._fireEvent([candidateHoveredLayer],e,"mouseover"),this._hoveredLayer=candidateHoveredLayer)),this._fireEvent(!!this._hoveredLayer&&[this._hoveredLayer],e),this._mouseHoverThrottled=!0,setTimeout(bind((function(){this._mouseHoverThrottled=!1}),this),32)}},_fireEvent:function(layers,e,type){this._map._fireDOMEvent(e,type||e.type,layers)},_bringToFront:function(layer){var order=layer._order;if(order){var next=order.next,prev=order.prev;next&&(next.prev=prev,prev?prev.next=next:next&&(this._drawFirst=next),order.prev=this._drawLast,this._drawLast.next=order,order.next=null,this._drawLast=order,this._requestRedraw(layer))}},_bringToBack:function(layer){var order=layer._order;if(order){var next=order.next,prev=order.prev;prev&&(prev.next=next,next?next.prev=prev:prev&&(this._drawLast=prev),order.prev=null,order.next=this._drawFirst,this._drawFirst.prev=order,this._drawFirst=order,this._requestRedraw(layer))}}});function canvas(options){return Browser.canvas?new Canvas(options):null}var vmlCreate=function(){try{return document.namespaces.add("lvml","urn:schemas-microsoft-com:vml"),function(name){return document.createElement("<lvml:"+name+' class="lvml">')}}catch(e){}return function(name){return document.createElement("<"+name+' xmlns="urn:schemas-microsoft.com:vml" class="lvml">')}}(),vmlMixin={_initContainer:function(){this._container=create$1("div","leaflet-vml-container")},_update:function(){this._map._animatingZoom||(Renderer.prototype._update.call(this),this.fire("update"))},_initPath:function(layer){var container=layer._container=vmlCreate("shape");addClass(container,"leaflet-vml-shape "+(this.options.className||"")),container.coordsize="1 1",layer._path=vmlCreate("path"),container.appendChild(layer._path),this._updateStyle(layer),this._layers[stamp(layer)]=layer},_addPath:function(layer){var container=layer._container;this._container.appendChild(container),layer.options.interactive&&layer.addInteractiveTarget(container)},_removePath:function(layer){var container=layer._container;remove(container),layer.removeInteractiveTarget(container),delete this._layers[stamp(layer)]},_updateStyle:function(layer){var stroke=layer._stroke,fill=layer._fill,options=layer.options,container=layer._container;container.stroked=!!options.stroke,container.filled=!!options.fill,options.stroke?(stroke||(stroke=layer._stroke=vmlCreate("stroke")),container.appendChild(stroke),stroke.weight=options.weight+"px",stroke.color=options.color,stroke.opacity=options.opacity,options.dashArray?stroke.dashStyle=isArray(options.dashArray)?options.dashArray.join(" "):options.dashArray.replace(/( *, *)/g," "):stroke.dashStyle="",stroke.endcap=options.lineCap.replace("butt","flat"),stroke.joinstyle=options.lineJoin):stroke&&(container.removeChild(stroke),layer._stroke=null),options.fill?(fill||(fill=layer._fill=vmlCreate("fill")),container.appendChild(fill),fill.color=options.fillColor||options.color,fill.opacity=options.fillOpacity):fill&&(container.removeChild(fill),layer._fill=null)},_updateCircle:function(layer){var p=layer._point.round(),r=Math.round(layer._radius),r2=Math.round(layer._radiusY||r);this._setPath(layer,layer._empty()?"M0 0":"AL "+p.x+","+p.y+" "+r+","+r2+" 0,23592600")},_setPath:function(layer,path){layer._path.v=path},_bringToFront:function(layer){toFront(layer._container)},_bringToBack:function(layer){toBack(layer._container)}},create=Browser.vml?vmlCreate:svgCreate,SVG=Renderer.extend({_initContainer:function(){this._container=create("svg"),this._container.setAttribute("pointer-events","none"),this._rootGroup=create("g"),this._container.appendChild(this._rootGroup)},_destroyContainer:function(){remove(this._container),off(this._container),delete this._container,delete this._rootGroup,delete this._svgSize},_update:function(){if(!this._map._animatingZoom||!this._bounds){Renderer.prototype._update.call(this);var b=this._bounds,size=b.getSize(),container=this._container;this._svgSize&&this._svgSize.equals(size)||(this._svgSize=size,container.setAttribute("width",size.x),container.setAttribute("height",size.y)),setPosition(container,b.min),container.setAttribute("viewBox",[b.min.x,b.min.y,size.x,size.y].join(" ")),this.fire("update")}},_initPath:function(layer){var path=layer._path=create("path");layer.options.className&&addClass(path,layer.options.className),layer.options.interactive&&addClass(path,"leaflet-interactive"),this._updateStyle(layer),this._layers[stamp(layer)]=layer},_addPath:function(layer){this._rootGroup||this._initContainer(),this._rootGroup.appendChild(layer._path),layer.addInteractiveTarget(layer._path)},_removePath:function(layer){remove(layer._path),layer.removeInteractiveTarget(layer._path),delete this._layers[stamp(layer)]},_updatePath:function(layer){layer._project(),layer._update()},_updateStyle:function(layer){var path=layer._path,options=layer.options;path&&(options.stroke?(path.setAttribute("stroke",options.color),path.setAttribute("stroke-opacity",options.opacity),path.setAttribute("stroke-width",options.weight),path.setAttribute("stroke-linecap",options.lineCap),path.setAttribute("stroke-linejoin",options.lineJoin),options.dashArray?path.setAttribute("stroke-dasharray",options.dashArray):path.removeAttribute("stroke-dasharray"),options.dashOffset?path.setAttribute("stroke-dashoffset",options.dashOffset):path.removeAttribute("stroke-dashoffset")):path.setAttribute("stroke","none"),options.fill?(path.setAttribute("fill",options.fillColor||options.color),path.setAttribute("fill-opacity",options.fillOpacity),path.setAttribute("fill-rule",options.fillRule||"evenodd")):path.setAttribute("fill","none"))},_updatePoly:function(layer,closed){this._setPath(layer,pointsToPath(layer._parts,closed))},_updateCircle:function(layer){var p=layer._point,r=Math.max(Math.round(layer._radius),1),arc="a"+r+","+(Math.max(Math.round(layer._radiusY),1)||r)+" 0 1,0 ",d=layer._empty()?"M0 0":"M"+(p.x-r)+","+p.y+arc+2*r+",0 "+arc+2*-r+",0 ";this._setPath(layer,d)},_setPath:function(layer,path){layer._path.setAttribute("d",path)},_bringToFront:function(layer){toFront(layer._path)},_bringToBack:function(layer){toBack(layer._path)}});function svg(options){return Browser.svg||Browser.vml?new SVG(options):null}Browser.vml&&SVG.include(vmlMixin),Map.include({getRenderer:function(layer){var renderer=layer.options.renderer||this._getPaneRenderer(layer.options.pane)||this.options.renderer||this._renderer;return renderer||(renderer=this._renderer=this._createRenderer()),this.hasLayer(renderer)||this.addLayer(renderer),renderer},_getPaneRenderer:function(name){if("overlayPane"===name||void 0===name)return!1;var renderer=this._paneRenderers[name];return void 0===renderer&&(renderer=this._createRenderer({pane:name}),this._paneRenderers[name]=renderer),renderer},_createRenderer:function(options){return this.options.preferCanvas&&canvas(options)||svg(options)}});var Rectangle=Polygon.extend({initialize:function(latLngBounds,options){Polygon.prototype.initialize.call(this,this._boundsToLatLngs(latLngBounds),options)},setBounds:function(latLngBounds){return this.setLatLngs(this._boundsToLatLngs(latLngBounds))},_boundsToLatLngs:function(latLngBounds){return[(latLngBounds=toLatLngBounds(latLngBounds)).getSouthWest(),latLngBounds.getNorthWest(),latLngBounds.getNorthEast(),latLngBounds.getSouthEast()]}});function rectangle(latLngBounds,options){return new Rectangle(latLngBounds,options)}SVG.create=create,SVG.pointsToPath=pointsToPath,GeoJSON.geometryToLayer=geometryToLayer,GeoJSON.coordsToLatLng=coordsToLatLng,GeoJSON.coordsToLatLngs=coordsToLatLngs,GeoJSON.latLngToCoords=latLngToCoords,GeoJSON.latLngsToCoords=latLngsToCoords,GeoJSON.getFeature=getFeature,GeoJSON.asFeature=asFeature,Map.mergeOptions({boxZoom:!0});var BoxZoom=Handler.extend({initialize:function(map){this._map=map,this._container=map._container,this._pane=map._panes.overlayPane,this._resetStateTimeout=0,map.on("unload",this._destroy,this)},addHooks:function(){on(this._container,"mousedown",this._onMouseDown,this)},removeHooks:function(){off(this._container,"mousedown",this._onMouseDown,this)},moved:function(){return this._moved},_destroy:function(){remove(this._pane),delete this._pane},_resetState:function(){this._resetStateTimeout=0,this._moved=!1},_clearDeferredResetState:function(){0!==this._resetStateTimeout&&(clearTimeout(this._resetStateTimeout),this._resetStateTimeout=0)},_onMouseDown:function(e){if(!e.shiftKey||1!==e.which&&1!==e.button)return!1;this._clearDeferredResetState(),this._resetState(),disableTextSelection(),disableImageDrag(),this._startPoint=this._map.mouseEventToContainerPoint(e),on(document,{contextmenu:stop,mousemove:this._onMouseMove,mouseup:this._onMouseUp,keydown:this._onKeyDown},this)},_onMouseMove:function(e){this._moved||(this._moved=!0,this._box=create$1("div","leaflet-zoom-box",this._container),addClass(this._container,"leaflet-crosshair"),this._map.fire("boxzoomstart")),this._point=this._map.mouseEventToContainerPoint(e);var bounds=new Bounds(this._point,this._startPoint),size=bounds.getSize();setPosition(this._box,bounds.min),this._box.style.width=size.x+"px",this._box.style.height=size.y+"px"},_finish:function(){this._moved&&(remove(this._box),removeClass(this._container,"leaflet-crosshair")),enableTextSelection(),enableImageDrag(),off(document,{contextmenu:stop,mousemove:this._onMouseMove,mouseup:this._onMouseUp,keydown:this._onKeyDown},this)},_onMouseUp:function(e){if((1===e.which||1===e.button)&&(this._finish(),this._moved)){this._clearDeferredResetState(),this._resetStateTimeout=setTimeout(bind(this._resetState,this),0);var bounds=new LatLngBounds(this._map.containerPointToLatLng(this._startPoint),this._map.containerPointToLatLng(this._point));this._map.fitBounds(bounds).fire("boxzoomend",{boxZoomBounds:bounds})}},_onKeyDown:function(e){27===e.keyCode&&(this._finish(),this._clearDeferredResetState(),this._resetState())}});Map.addInitHook("addHandler","boxZoom",BoxZoom),Map.mergeOptions({doubleClickZoom:!0});var DoubleClickZoom=Handler.extend({addHooks:function(){this._map.on("dblclick",this._onDoubleClick,this)},removeHooks:function(){this._map.off("dblclick",this._onDoubleClick,this)},_onDoubleClick:function(e){var map=this._map,oldZoom=map.getZoom(),delta=map.options.zoomDelta,zoom=e.originalEvent.shiftKey?oldZoom-delta:oldZoom+delta;"center"===map.options.doubleClickZoom?map.setZoom(zoom):map.setZoomAround(e.containerPoint,zoom)}});Map.addInitHook("addHandler","doubleClickZoom",DoubleClickZoom),Map.mergeOptions({dragging:!0,inertia:!0,inertiaDeceleration:3400,inertiaMaxSpeed:1/0,easeLinearity:.2,worldCopyJump:!1,maxBoundsViscosity:0});var Drag=Handler.extend({addHooks:function(){if(!this._draggable){var map=this._map;this._draggable=new Draggable(map._mapPane,map._container),this._draggable.on({dragstart:this._onDragStart,drag:this._onDrag,dragend:this._onDragEnd},this),this._draggable.on("predrag",this._onPreDragLimit,this),map.options.worldCopyJump&&(this._draggable.on("predrag",this._onPreDragWrap,this),map.on("zoomend",this._onZoomEnd,this),map.whenReady(this._onZoomEnd,this))}addClass(this._map._container,"leaflet-grab leaflet-touch-drag"),this._draggable.enable(),this._positions=[],this._times=[]},removeHooks:function(){removeClass(this._map._container,"leaflet-grab"),removeClass(this._map._container,"leaflet-touch-drag"),this._draggable.disable()},moved:function(){return this._draggable&&this._draggable._moved},moving:function(){return this._draggable&&this._draggable._moving},_onDragStart:function(){var map=this._map;if(map._stop(),this._map.options.maxBounds&&this._map.options.maxBoundsViscosity){var bounds=toLatLngBounds(this._map.options.maxBounds);this._offsetLimit=toBounds(this._map.latLngToContainerPoint(bounds.getNorthWest()).multiplyBy(-1),this._map.latLngToContainerPoint(bounds.getSouthEast()).multiplyBy(-1).add(this._map.getSize())),this._viscosity=Math.min(1,Math.max(0,this._map.options.maxBoundsViscosity))}else this._offsetLimit=null;map.fire("movestart").fire("dragstart"),map.options.inertia&&(this._positions=[],this._times=[])},_onDrag:function(e){if(this._map.options.inertia){var time=this._lastTime=+new Date,pos=this._lastPos=this._draggable._absPos||this._draggable._newPos;this._positions.push(pos),this._times.push(time),this._prunePositions(time)}this._map.fire("move",e).fire("drag",e)},_prunePositions:function(time){for(;this._positions.length>1&&time-this._times[0]>50;)this._positions.shift(),this._times.shift()},_onZoomEnd:function(){var pxCenter=this._map.getSize().divideBy(2),pxWorldCenter=this._map.latLngToLayerPoint([0,0]);this._initialWorldOffset=pxWorldCenter.subtract(pxCenter).x,this._worldWidth=this._map.getPixelWorldBounds().getSize().x},_viscousLimit:function(value,threshold){return value-(value-threshold)*this._viscosity},_onPreDragLimit:function(){if(this._viscosity&&this._offsetLimit){var offset=this._draggable._newPos.subtract(this._draggable._startPos),limit=this._offsetLimit;offset.x<limit.min.x&&(offset.x=this._viscousLimit(offset.x,limit.min.x)),offset.y<limit.min.y&&(offset.y=this._viscousLimit(offset.y,limit.min.y)),offset.x>limit.max.x&&(offset.x=this._viscousLimit(offset.x,limit.max.x)),offset.y>limit.max.y&&(offset.y=this._viscousLimit(offset.y,limit.max.y)),this._draggable._newPos=this._draggable._startPos.add(offset)}},_onPreDragWrap:function(){var worldWidth=this._worldWidth,halfWidth=Math.round(worldWidth/2),dx=this._initialWorldOffset,x=this._draggable._newPos.x,newX1=(x-halfWidth+dx)%worldWidth+halfWidth-dx,newX2=(x+halfWidth+dx)%worldWidth-halfWidth-dx,newX=Math.abs(newX1+dx)<Math.abs(newX2+dx)?newX1:newX2;this._draggable._absPos=this._draggable._newPos.clone(),this._draggable._newPos.x=newX},_onDragEnd:function(e){var map=this._map,options=map.options,noInertia=!options.inertia||e.noInertia||this._times.length<2;if(map.fire("dragend",e),noInertia)map.fire("moveend");else{this._prunePositions(+new Date);var direction=this._lastPos.subtract(this._positions[0]),duration=(this._lastTime-this._times[0])/1e3,ease=options.easeLinearity,speedVector=direction.multiplyBy(ease/duration),speed=speedVector.distanceTo([0,0]),limitedSpeed=Math.min(options.inertiaMaxSpeed,speed),limitedSpeedVector=speedVector.multiplyBy(limitedSpeed/speed),decelerationDuration=limitedSpeed/(options.inertiaDeceleration*ease),offset=limitedSpeedVector.multiplyBy(-decelerationDuration/2).round();offset.x||offset.y?(offset=map._limitOffset(offset,map.options.maxBounds),requestAnimFrame((function(){map.panBy(offset,{duration:decelerationDuration,easeLinearity:ease,noMoveStart:!0,animate:!0})}))):map.fire("moveend")}}});Map.addInitHook("addHandler","dragging",Drag),Map.mergeOptions({keyboard:!0,keyboardPanDelta:80});var Keyboard=Handler.extend({keyCodes:{left:[37],right:[39],down:[40],up:[38],zoomIn:[187,107,61,171],zoomOut:[189,109,54,173]},initialize:function(map){this._map=map,this._setPanDelta(map.options.keyboardPanDelta),this._setZoomDelta(map.options.zoomDelta)},addHooks:function(){var container=this._map._container;container.tabIndex<=0&&(container.tabIndex="0"),on(container,{focus:this._onFocus,blur:this._onBlur,mousedown:this._onMouseDown},this),this._map.on({focus:this._addHooks,blur:this._removeHooks},this)},removeHooks:function(){this._removeHooks(),off(this._map._container,{focus:this._onFocus,blur:this._onBlur,mousedown:this._onMouseDown},this),this._map.off({focus:this._addHooks,blur:this._removeHooks},this)},_onMouseDown:function(){if(!this._focused){var body=document.body,docEl=document.documentElement,top=body.scrollTop||docEl.scrollTop,left=body.scrollLeft||docEl.scrollLeft;this._map._container.focus(),window.scrollTo(left,top)}},_onFocus:function(){this._focused=!0,this._map.fire("focus")},_onBlur:function(){this._focused=!1,this._map.fire("blur")},_setPanDelta:function(panDelta){var i,len,keys=this._panKeys={},codes=this.keyCodes;for(i=0,len=codes.left.length;i<len;i++)keys[codes.left[i]]=[-1*panDelta,0];for(i=0,len=codes.right.length;i<len;i++)keys[codes.right[i]]=[panDelta,0];for(i=0,len=codes.down.length;i<len;i++)keys[codes.down[i]]=[0,panDelta];for(i=0,len=codes.up.length;i<len;i++)keys[codes.up[i]]=[0,-1*panDelta]},_setZoomDelta:function(zoomDelta){var i,len,keys=this._zoomKeys={},codes=this.keyCodes;for(i=0,len=codes.zoomIn.length;i<len;i++)keys[codes.zoomIn[i]]=zoomDelta;for(i=0,len=codes.zoomOut.length;i<len;i++)keys[codes.zoomOut[i]]=-zoomDelta},_addHooks:function(){on(document,"keydown",this._onKeyDown,this)},_removeHooks:function(){off(document,"keydown",this._onKeyDown,this)},_onKeyDown:function(e){if(!(e.altKey||e.ctrlKey||e.metaKey)){var offset,key=e.keyCode,map=this._map;if(key in this._panKeys){if(!map._panAnim||!map._panAnim._inProgress)if(offset=this._panKeys[key],e.shiftKey&&(offset=toPoint(offset).multiplyBy(3)),map.options.maxBounds&&(offset=map._limitOffset(toPoint(offset),map.options.maxBounds)),map.options.worldCopyJump){var newLatLng=map.wrapLatLng(map.unproject(map.project(map.getCenter()).add(offset)));map.panTo(newLatLng)}else map.panBy(offset)}else if(key in this._zoomKeys)map.setZoom(map.getZoom()+(e.shiftKey?3:1)*this._zoomKeys[key]);else{if(27!==key||!map._popup||!map._popup.options.closeOnEscapeKey)return;map.closePopup()}stop(e)}}});Map.addInitHook("addHandler","keyboard",Keyboard),Map.mergeOptions({scrollWheelZoom:!0,wheelDebounceTime:40,wheelPxPerZoomLevel:60});var ScrollWheelZoom=Handler.extend({addHooks:function(){on(this._map._container,"wheel",this._onWheelScroll,this),this._delta=0},removeHooks:function(){off(this._map._container,"wheel",this._onWheelScroll,this)},_onWheelScroll:function(e){var delta=getWheelDelta(e),debounce=this._map.options.wheelDebounceTime;this._delta+=delta,this._lastMousePos=this._map.mouseEventToContainerPoint(e),this._startTime||(this._startTime=+new Date);var left=Math.max(debounce-(+new Date-this._startTime),0);clearTimeout(this._timer),this._timer=setTimeout(bind(this._performZoom,this),left),stop(e)},_performZoom:function(){var map=this._map,zoom=map.getZoom(),snap=this._map.options.zoomSnap||0;map._stop();var d2=this._delta/(4*this._map.options.wheelPxPerZoomLevel),d3=4*Math.log(2/(1+Math.exp(-Math.abs(d2))))/Math.LN2,d4=snap?Math.ceil(d3/snap)*snap:d3,delta=map._limitZoom(zoom+(this._delta>0?d4:-d4))-zoom;this._delta=0,this._startTime=null,delta&&("center"===map.options.scrollWheelZoom?map.setZoom(zoom+delta):map.setZoomAround(this._lastMousePos,zoom+delta))}});Map.addInitHook("addHandler","scrollWheelZoom",ScrollWheelZoom);var tapHoldDelay=600;Map.mergeOptions({tapHold:Browser.touchNative&&Browser.safari&&Browser.mobile,tapTolerance:15});var TapHold=Handler.extend({addHooks:function(){on(this._map._container,"touchstart",this._onDown,this)},removeHooks:function(){off(this._map._container,"touchstart",this._onDown,this)},_onDown:function(e){if(clearTimeout(this._holdTimeout),1===e.touches.length){var first=e.touches[0];this._startPos=this._newPos=new Point(first.clientX,first.clientY),this._holdTimeout=setTimeout(bind((function(){this._cancel(),this._isTapValid()&&(on(document,"touchend",preventDefault),on(document,"touchend touchcancel",this._cancelClickPrevent),this._simulateEvent("contextmenu",first))}),this),tapHoldDelay),on(document,"touchend touchcancel contextmenu",this._cancel,this),on(document,"touchmove",this._onMove,this)}},_cancelClickPrevent:function cancelClickPrevent(){off(document,"touchend",preventDefault),off(document,"touchend touchcancel",cancelClickPrevent)},_cancel:function(){clearTimeout(this._holdTimeout),off(document,"touchend touchcancel contextmenu",this._cancel,this),off(document,"touchmove",this._onMove,this)},_onMove:function(e){var first=e.touches[0];this._newPos=new Point(first.clientX,first.clientY)},_isTapValid:function(){return this._newPos.distanceTo(this._startPos)<=this._map.options.tapTolerance},_simulateEvent:function(type,e){var simulatedEvent=new MouseEvent(type,{bubbles:!0,cancelable:!0,view:window,screenX:e.screenX,screenY:e.screenY,clientX:e.clientX,clientY:e.clientY});simulatedEvent._simulated=!0,e.target.dispatchEvent(simulatedEvent)}});Map.addInitHook("addHandler","tapHold",TapHold),Map.mergeOptions({touchZoom:Browser.touch,bounceAtZoomLimits:!0});var TouchZoom=Handler.extend({addHooks:function(){addClass(this._map._container,"leaflet-touch-zoom"),on(this._map._container,"touchstart",this._onTouchStart,this)},removeHooks:function(){removeClass(this._map._container,"leaflet-touch-zoom"),off(this._map._container,"touchstart",this._onTouchStart,this)},_onTouchStart:function(e){var map=this._map;if(e.touches&&2===e.touches.length&&!map._animatingZoom&&!this._zooming){var p1=map.mouseEventToContainerPoint(e.touches[0]),p2=map.mouseEventToContainerPoint(e.touches[1]);this._centerPoint=map.getSize()._divideBy(2),this._startLatLng=map.containerPointToLatLng(this._centerPoint),"center"!==map.options.touchZoom&&(this._pinchStartLatLng=map.containerPointToLatLng(p1.add(p2)._divideBy(2))),this._startDist=p1.distanceTo(p2),this._startZoom=map.getZoom(),this._moved=!1,this._zooming=!0,map._stop(),on(document,"touchmove",this._onTouchMove,this),on(document,"touchend touchcancel",this._onTouchEnd,this),preventDefault(e)}},_onTouchMove:function(e){if(e.touches&&2===e.touches.length&&this._zooming){var map=this._map,p1=map.mouseEventToContainerPoint(e.touches[0]),p2=map.mouseEventToContainerPoint(e.touches[1]),scale=p1.distanceTo(p2)/this._startDist;if(this._zoom=map.getScaleZoom(scale,this._startZoom),!map.options.bounceAtZoomLimits&&(this._zoom<map.getMinZoom()&&scale<1||this._zoom>map.getMaxZoom()&&scale>1)&&(this._zoom=map._limitZoom(this._zoom)),"center"===map.options.touchZoom){if(this._center=this._startLatLng,1===scale)return}else{var delta=p1._add(p2)._divideBy(2)._subtract(this._centerPoint);if(1===scale&&0===delta.x&&0===delta.y)return;this._center=map.unproject(map.project(this._pinchStartLatLng,this._zoom).subtract(delta),this._zoom)}this._moved||(map._moveStart(!0,!1),this._moved=!0),cancelAnimFrame(this._animRequest);var moveFn=bind(map._move,map,this._center,this._zoom,{pinch:!0,round:!1},void 0);this._animRequest=requestAnimFrame(moveFn,this,!0),preventDefault(e)}},_onTouchEnd:function(){this._moved&&this._zooming?(this._zooming=!1,cancelAnimFrame(this._animRequest),off(document,"touchmove",this._onTouchMove,this),off(document,"touchend touchcancel",this._onTouchEnd,this),this._map.options.zoomAnimation?this._map._animateZoom(this._center,this._map._limitZoom(this._zoom),!0,this._map.options.zoomSnap):this._map._resetView(this._center,this._map._limitZoom(this._zoom))):this._zooming=!1}});Map.addInitHook("addHandler","touchZoom",TouchZoom),Map.BoxZoom=BoxZoom,Map.DoubleClickZoom=DoubleClickZoom,Map.Drag=Drag,Map.Keyboard=Keyboard,Map.ScrollWheelZoom=ScrollWheelZoom,Map.TapHold=TapHold,Map.TouchZoom=TouchZoom,exports.Bounds=Bounds,exports.Browser=Browser,exports.CRS=CRS,exports.Canvas=Canvas,exports.Circle=Circle,exports.CircleMarker=CircleMarker,exports.Class=Class,exports.Control=Control,exports.DivIcon=DivIcon,exports.DivOverlay=DivOverlay,exports.DomEvent=DomEvent,exports.DomUtil=DomUtil,exports.Draggable=Draggable,exports.Evented=Evented,exports.FeatureGroup=FeatureGroup,exports.GeoJSON=GeoJSON,exports.GridLayer=GridLayer,exports.Handler=Handler,exports.Icon=Icon,exports.ImageOverlay=ImageOverlay,exports.LatLng=LatLng,exports.LatLngBounds=LatLngBounds,exports.Layer=Layer,exports.LayerGroup=LayerGroup,exports.LineUtil=LineUtil,exports.Map=Map,exports.Marker=Marker,exports.Mixin=Mixin,exports.Path=Path,exports.Point=Point,exports.PolyUtil=PolyUtil,exports.Polygon=Polygon,exports.Polyline=Polyline,exports.Popup=Popup,exports.PosAnimation=PosAnimation,exports.Projection=index,exports.Rectangle=Rectangle,exports.Renderer=Renderer,exports.SVG=SVG,exports.SVGOverlay=SVGOverlay,exports.TileLayer=TileLayer,exports.Tooltip=Tooltip,exports.Transformation=Transformation,exports.Util=Util,exports.VideoOverlay=VideoOverlay,exports.bind=bind,exports.bounds=toBounds,exports.canvas=canvas,exports.circle=circle,exports.circleMarker=circleMarker,exports.control=control,exports.divIcon=divIcon,exports.extend=extend,exports.featureGroup=featureGroup,exports.geoJSON=geoJSON,exports.geoJson=geoJson,exports.gridLayer=gridLayer,exports.icon=icon,exports.imageOverlay=imageOverlay,exports.latLng=toLatLng,exports.latLngBounds=toLatLngBounds,exports.layerGroup=layerGroup,exports.map=createMap,exports.marker=marker,exports.point=toPoint,exports.polygon=polygon,exports.polyline=polyline,exports.popup=popup,exports.rectangle=rectangle,exports.setOptions=setOptions,exports.stamp=stamp,exports.svg=svg,exports.svgOverlay=svgOverlay,exports.tileLayer=tileLayer,exports.tooltip=tooltip,exports.transformation=toTransformation,exports.version=version,exports.videoOverlay=videoOverlay;var oldL=window.L;exports.noConflict=function(){return window.L=oldL,this},window.L=exports}(exports)},"./node_modules/moment/moment.js":function(module,__unused_webpack_exports,__webpack_require__){(module=__webpack_require__.nmd(module)).exports=function(){"use strict";var hookCallback,some;function hooks(){return hookCallback.apply(null,arguments)}function setHookCallback(callback){hookCallback=callback}function isArray(input){return input instanceof Array||"[object Array]"===Object.prototype.toString.call(input)}function isObject(input){return null!=input&&"[object Object]"===Object.prototype.toString.call(input)}function hasOwnProp(a,b){return Object.prototype.hasOwnProperty.call(a,b)}function isObjectEmpty(obj){if(Object.getOwnPropertyNames)return 0===Object.getOwnPropertyNames(obj).length;var k;for(k in obj)if(hasOwnProp(obj,k))return!1;return!0}function isUndefined(input){return void 0===input}function isNumber(input){return"number"==typeof input||"[object Number]"===Object.prototype.toString.call(input)}function isDate(input){return input instanceof Date||"[object Date]"===Object.prototype.toString.call(input)}function map(arr,fn){var i,res=[],arrLen=arr.length;for(i=0;i<arrLen;++i)res.push(fn(arr[i],i));return res}function extend(a,b){for(var i in b)hasOwnProp(b,i)&&(a[i]=b[i]);return hasOwnProp(b,"toString")&&(a.toString=b.toString),hasOwnProp(b,"valueOf")&&(a.valueOf=b.valueOf),a}function createUTC(input,format,locale,strict){return createLocalOrUTC(input,format,locale,strict,!0).utc()}function defaultParsingFlags(){return{empty:!1,unusedTokens:[],unusedInput:[],overflow:-2,charsLeftOver:0,nullInput:!1,invalidEra:null,invalidMonth:null,invalidFormat:!1,userInvalidated:!1,iso:!1,parsedDateParts:[],era:null,meridiem:null,rfc2822:!1,weekdayMismatch:!1}}function getParsingFlags(m){return null==m._pf&&(m._pf=defaultParsingFlags()),m._pf}function isValid(m){var flags=null,parsedParts=!1,isNowValid=m._d&&!isNaN(m._d.getTime());return isNowValid&&(flags=getParsingFlags(m),parsedParts=some.call(flags.parsedDateParts,(function(i){return null!=i})),isNowValid=flags.overflow<0&&!flags.empty&&!flags.invalidEra&&!flags.invalidMonth&&!flags.invalidWeekday&&!flags.weekdayMismatch&&!flags.nullInput&&!flags.invalidFormat&&!flags.userInvalidated&&(!flags.meridiem||flags.meridiem&&parsedParts),m._strict&&(isNowValid=isNowValid&&0===flags.charsLeftOver&&0===flags.unusedTokens.length&&void 0===flags.bigHour)),null!=Object.isFrozen&&Object.isFrozen(m)?isNowValid:(m._isValid=isNowValid,m._isValid)}function createInvalid(flags){var m=createUTC(NaN);return null!=flags?extend(getParsingFlags(m),flags):getParsingFlags(m).userInvalidated=!0,m}some=Array.prototype.some?Array.prototype.some:function(fun){var i,t=Object(this),len=t.length>>>0;for(i=0;i<len;i++)if(i in t&&fun.call(this,t[i],i,t))return!0;return!1};var momentProperties=hooks.momentProperties=[],updateInProgress=!1;function copyConfig(to,from){var i,prop,val,momentPropertiesLen=momentProperties.length;if(isUndefined(from._isAMomentObject)||(to._isAMomentObject=from._isAMomentObject),isUndefined(from._i)||(to._i=from._i),isUndefined(from._f)||(to._f=from._f),isUndefined(from._l)||(to._l=from._l),isUndefined(from._strict)||(to._strict=from._strict),isUndefined(from._tzm)||(to._tzm=from._tzm),isUndefined(from._isUTC)||(to._isUTC=from._isUTC),isUndefined(from._offset)||(to._offset=from._offset),isUndefined(from._pf)||(to._pf=getParsingFlags(from)),isUndefined(from._locale)||(to._locale=from._locale),momentPropertiesLen>0)for(i=0;i<momentPropertiesLen;i++)isUndefined(val=from[prop=momentProperties[i]])||(to[prop]=val);return to}function Moment(config){copyConfig(this,config),this._d=new Date(null!=config._d?config._d.getTime():NaN),this.isValid()||(this._d=new Date(NaN)),!1===updateInProgress&&(updateInProgress=!0,hooks.updateOffset(this),updateInProgress=!1)}function isMoment(obj){return obj instanceof Moment||null!=obj&&null!=obj._isAMomentObject}function warn(msg){!1===hooks.suppressDeprecationWarnings&&"undefined"!=typeof console&&console.warn&&console.warn("Deprecation warning: "+msg)}function deprecate(msg,fn){var firstTime=!0;return extend((function(){if(null!=hooks.deprecationHandler&&hooks.deprecationHandler(null,msg),firstTime){var arg,i,key,args=[],argLen=arguments.length;for(i=0;i<argLen;i++){if(arg="","object"==typeof arguments[i]){for(key in arg+="\n["+i+"] ",arguments[0])hasOwnProp(arguments[0],key)&&(arg+=key+": "+arguments[0][key]+", ");arg=arg.slice(0,-2)}else arg=arguments[i];args.push(arg)}warn(msg+"\nArguments: "+Array.prototype.slice.call(args).join("")+"\n"+(new Error).stack),firstTime=!1}return fn.apply(this,arguments)}),fn)}var keys,deprecations={};function deprecateSimple(name,msg){null!=hooks.deprecationHandler&&hooks.deprecationHandler(name,msg),deprecations[name]||(warn(msg),deprecations[name]=!0)}function isFunction(input){return"undefined"!=typeof Function&&input instanceof Function||"[object Function]"===Object.prototype.toString.call(input)}function set(config){var prop,i;for(i in config)hasOwnProp(config,i)&&(isFunction(prop=config[i])?this[i]=prop:this["_"+i]=prop);this._config=config,this._dayOfMonthOrdinalParseLenient=new RegExp((this._dayOfMonthOrdinalParse.source||this._ordinalParse.source)+"|"+/\d{1,2}/.source)}function mergeConfigs(parentConfig,childConfig){var prop,res=extend({},parentConfig);for(prop in childConfig)hasOwnProp(childConfig,prop)&&(isObject(parentConfig[prop])&&isObject(childConfig[prop])?(res[prop]={},extend(res[prop],parentConfig[prop]),extend(res[prop],childConfig[prop])):null!=childConfig[prop]?res[prop]=childConfig[prop]:delete res[prop]);for(prop in parentConfig)hasOwnProp(parentConfig,prop)&&!hasOwnProp(childConfig,prop)&&isObject(parentConfig[prop])&&(res[prop]=extend({},res[prop]));return res}function Locale(config){null!=config&&this.set(config)}hooks.suppressDeprecationWarnings=!1,hooks.deprecationHandler=null,keys=Object.keys?Object.keys:function(obj){var i,res=[];for(i in obj)hasOwnProp(obj,i)&&res.push(i);return res};var defaultCalendar={sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"};function calendar(key,mom,now){var output=this._calendar[key]||this._calendar.sameElse;return isFunction(output)?output.call(mom,now):output}function zeroFill(number,targetLength,forceSign){var absNumber=""+Math.abs(number),zerosToFill=targetLength-absNumber.length;return(number>=0?forceSign?"+":"":"-")+Math.pow(10,Math.max(0,zerosToFill)).toString().substr(1)+absNumber}var formattingTokens=/(\[[^\[]*\])|(\\)?([Hh]mm(ss)?|Mo|MM?M?M?|Do|DDDo|DD?D?D?|ddd?d?|do?|w[o|w]?|W[o|W]?|Qo?|N{1,5}|YYYYYY|YYYYY|YYYY|YY|y{2,4}|yo?|gg(ggg?)?|GG(GGG?)?|e|E|a|A|hh?|HH?|kk?|mm?|ss?|S{1,9}|x|X|zz?|ZZ?|.)/g,localFormattingTokens=/(\[[^\[]*\])|(\\)?(LTS|LT|LL?L?L?|l{1,4})/g,formatFunctions={},formatTokenFunctions={};function addFormatToken(token,padded,ordinal,callback){var func=callback;"string"==typeof callback&&(func=function(){return this[callback]()}),token&&(formatTokenFunctions[token]=func),padded&&(formatTokenFunctions[padded[0]]=function(){return zeroFill(func.apply(this,arguments),padded[1],padded[2])}),ordinal&&(formatTokenFunctions[ordinal]=function(){return this.localeData().ordinal(func.apply(this,arguments),token)})}function removeFormattingTokens(input){return input.match(/\[[\s\S]/)?input.replace(/^\[|\]$/g,""):input.replace(/\\/g,"")}function makeFormatFunction(format){var i,length,array=format.match(formattingTokens);for(i=0,length=array.length;i<length;i++)formatTokenFunctions[array[i]]?array[i]=formatTokenFunctions[array[i]]:array[i]=removeFormattingTokens(array[i]);return function(mom){var i,output="";for(i=0;i<length;i++)output+=isFunction(array[i])?array[i].call(mom,format):array[i];return output}}function formatMoment(m,format){return m.isValid()?(format=expandFormat(format,m.localeData()),formatFunctions[format]=formatFunctions[format]||makeFormatFunction(format),formatFunctions[format](m)):m.localeData().invalidDate()}function expandFormat(format,locale){var i=5;function replaceLongDateFormatTokens(input){return locale.longDateFormat(input)||input}for(localFormattingTokens.lastIndex=0;i>=0&&localFormattingTokens.test(format);)format=format.replace(localFormattingTokens,replaceLongDateFormatTokens),localFormattingTokens.lastIndex=0,i-=1;return format}var defaultLongDateFormat={LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"};function longDateFormat(key){var format=this._longDateFormat[key],formatUpper=this._longDateFormat[key.toUpperCase()];return format||!formatUpper?format:(this._longDateFormat[key]=formatUpper.match(formattingTokens).map((function(tok){return"MMMM"===tok||"MM"===tok||"DD"===tok||"dddd"===tok?tok.slice(1):tok})).join(""),this._longDateFormat[key])}var defaultInvalidDate="Invalid date";function invalidDate(){return this._invalidDate}var defaultOrdinal="%d",defaultDayOfMonthOrdinalParse=/\d{1,2}/;function ordinal(number){return this._ordinal.replace("%d",number)}var defaultRelativeTime={future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",w:"a week",ww:"%d weeks",M:"a month",MM:"%d months",y:"a year",yy:"%d years"};function relativeTime(number,withoutSuffix,string,isFuture){var output=this._relativeTime[string];return isFunction(output)?output(number,withoutSuffix,string,isFuture):output.replace(/%d/i,number)}function pastFuture(diff,output){var format=this._relativeTime[diff>0?"future":"past"];return isFunction(format)?format(output):format.replace(/%s/i,output)}var aliases={D:"date",dates:"date",date:"date",d:"day",days:"day",day:"day",e:"weekday",weekdays:"weekday",weekday:"weekday",E:"isoWeekday",isoweekdays:"isoWeekday",isoweekday:"isoWeekday",DDD:"dayOfYear",dayofyears:"dayOfYear",dayofyear:"dayOfYear",h:"hour",hours:"hour",hour:"hour",ms:"millisecond",milliseconds:"millisecond",millisecond:"millisecond",m:"minute",minutes:"minute",minute:"minute",M:"month",months:"month",month:"month",Q:"quarter",quarters:"quarter",quarter:"quarter",s:"second",seconds:"second",second:"second",gg:"weekYear",weekyears:"weekYear",weekyear:"weekYear",GG:"isoWeekYear",isoweekyears:"isoWeekYear",isoweekyear:"isoWeekYear",w:"week",weeks:"week",week:"week",W:"isoWeek",isoweeks:"isoWeek",isoweek:"isoWeek",y:"year",years:"year",year:"year"};function normalizeUnits(units){return"string"==typeof units?aliases[units]||aliases[units.toLowerCase()]:void 0}function normalizeObjectUnits(inputObject){var normalizedProp,prop,normalizedInput={};for(prop in inputObject)hasOwnProp(inputObject,prop)&&(normalizedProp=normalizeUnits(prop))&&(normalizedInput[normalizedProp]=inputObject[prop]);return normalizedInput}var priorities={date:9,day:11,weekday:11,isoWeekday:11,dayOfYear:4,hour:13,millisecond:16,minute:14,month:8,quarter:7,second:15,weekYear:1,isoWeekYear:1,week:5,isoWeek:5,year:1};function getPrioritizedUnits(unitsObj){var u,units=[];for(u in unitsObj)hasOwnProp(unitsObj,u)&&units.push({unit:u,priority:priorities[u]});return units.sort((function(a,b){return a.priority-b.priority})),units}var regexes,match1=/\d/,match2=/\d\d/,match3=/\d{3}/,match4=/\d{4}/,match6=/[+-]?\d{6}/,match1to2=/\d\d?/,match3to4=/\d\d\d\d?/,match5to6=/\d\d\d\d\d\d?/,match1to3=/\d{1,3}/,match1to4=/\d{1,4}/,match1to6=/[+-]?\d{1,6}/,matchUnsigned=/\d+/,matchSigned=/[+-]?\d+/,matchOffset=/Z|[+-]\d\d:?\d\d/gi,matchShortOffset=/Z|[+-]\d\d(?::?\d\d)?/gi,matchTimestamp=/[+-]?\d+(\.\d{1,3})?/,matchWord=/[0-9]{0,256}['a-z\u00A0-\u05FF\u0700-\uD7FF\uF900-\uFDCF\uFDF0-\uFF07\uFF10-\uFFEF]{1,256}|[\u0600-\u06FF\/]{1,256}(\s*?[\u0600-\u06FF]{1,256}){1,2}/i,match1to2NoLeadingZero=/^[1-9]\d?/,match1to2HasZero=/^([1-9]\d|\d)/;function addRegexToken(token,regex,strictRegex){regexes[token]=isFunction(regex)?regex:function(isStrict,localeData){return isStrict&&strictRegex?strictRegex:regex}}function getParseRegexForToken(token,config){return hasOwnProp(regexes,token)?regexes[token](config._strict,config._locale):new RegExp(unescapeFormat(token))}function unescapeFormat(s){return regexEscape(s.replace("\\","").replace(/\\(\[)|\\(\])|\[([^\]\[]*)\]|\\(.)/g,(function(matched,p1,p2,p3,p4){return p1||p2||p3||p4})))}function regexEscape(s){return s.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&")}function absFloor(number){return number<0?Math.ceil(number)||0:Math.floor(number)}function toInt(argumentForCoercion){var coercedNumber=+argumentForCoercion,value=0;return 0!==coercedNumber&&isFinite(coercedNumber)&&(value=absFloor(coercedNumber)),value}regexes={};var tokens={};function addParseToken(token,callback){var i,tokenLen,func=callback;for("string"==typeof token&&(token=[token]),isNumber(callback)&&(func=function(input,array){array[callback]=toInt(input)}),tokenLen=token.length,i=0;i<tokenLen;i++)tokens[token[i]]=func}function addWeekParseToken(token,callback){addParseToken(token,(function(input,array,config,token){config._w=config._w||{},callback(input,config._w,config,token)}))}function addTimeToArrayFromToken(token,input,config){null!=input&&hasOwnProp(tokens,token)&&tokens[token](input,config._a,config,token)}function isLeapYear(year){return year%4==0&&year%100!=0||year%400==0}var YEAR=0,MONTH=1,DATE=2,HOUR=3,MINUTE=4,SECOND=5,MILLISECOND=6,WEEK=7,WEEKDAY=8;function daysInYear(year){return isLeapYear(year)?366:365}addFormatToken("Y",0,0,(function(){var y=this.year();return y<=9999?zeroFill(y,4):"+"+y})),addFormatToken(0,["YY",2],0,(function(){return this.year()%100})),addFormatToken(0,["YYYY",4],0,"year"),addFormatToken(0,["YYYYY",5],0,"year"),addFormatToken(0,["YYYYYY",6,!0],0,"year"),addRegexToken("Y",matchSigned),addRegexToken("YY",match1to2,match2),addRegexToken("YYYY",match1to4,match4),addRegexToken("YYYYY",match1to6,match6),addRegexToken("YYYYYY",match1to6,match6),addParseToken(["YYYYY","YYYYYY"],YEAR),addParseToken("YYYY",(function(input,array){array[YEAR]=2===input.length?hooks.parseTwoDigitYear(input):toInt(input)})),addParseToken("YY",(function(input,array){array[YEAR]=hooks.parseTwoDigitYear(input)})),addParseToken("Y",(function(input,array){array[YEAR]=parseInt(input,10)})),hooks.parseTwoDigitYear=function(input){return toInt(input)+(toInt(input)>68?1900:2e3)};var indexOf,getSetYear=makeGetSet("FullYear",!0);function getIsLeapYear(){return isLeapYear(this.year())}function makeGetSet(unit,keepTime){return function(value){return null!=value?(set$1(this,unit,value),hooks.updateOffset(this,keepTime),this):get(this,unit)}}function get(mom,unit){if(!mom.isValid())return NaN;var d=mom._d,isUTC=mom._isUTC;switch(unit){case"Milliseconds":return isUTC?d.getUTCMilliseconds():d.getMilliseconds();case"Seconds":return isUTC?d.getUTCSeconds():d.getSeconds();case"Minutes":return isUTC?d.getUTCMinutes():d.getMinutes();case"Hours":return isUTC?d.getUTCHours():d.getHours();case"Date":return isUTC?d.getUTCDate():d.getDate();case"Day":return isUTC?d.getUTCDay():d.getDay();case"Month":return isUTC?d.getUTCMonth():d.getMonth();case"FullYear":return isUTC?d.getUTCFullYear():d.getFullYear();default:return NaN}}function set$1(mom,unit,value){var d,isUTC,year,month,date;if(mom.isValid()&&!isNaN(value)){switch(d=mom._d,isUTC=mom._isUTC,unit){case"Milliseconds":return void(isUTC?d.setUTCMilliseconds(value):d.setMilliseconds(value));case"Seconds":return void(isUTC?d.setUTCSeconds(value):d.setSeconds(value));case"Minutes":return void(isUTC?d.setUTCMinutes(value):d.setMinutes(value));case"Hours":return void(isUTC?d.setUTCHours(value):d.setHours(value));case"Date":return void(isUTC?d.setUTCDate(value):d.setDate(value));case"FullYear":break;default:return}year=value,month=mom.month(),date=29!==(date=mom.date())||1!==month||isLeapYear(year)?date:28,isUTC?d.setUTCFullYear(year,month,date):d.setFullYear(year,month,date)}}function stringGet(units){return isFunction(this[units=normalizeUnits(units)])?this[units]():this}function stringSet(units,value){if("object"==typeof units){var i,prioritized=getPrioritizedUnits(units=normalizeObjectUnits(units)),prioritizedLen=prioritized.length;for(i=0;i<prioritizedLen;i++)this[prioritized[i].unit](units[prioritized[i].unit])}else if(isFunction(this[units=normalizeUnits(units)]))return this[units](value);return this}function mod(n,x){return(n%x+x)%x}function daysInMonth(year,month){if(isNaN(year)||isNaN(month))return NaN;var modMonth=mod(month,12);return year+=(month-modMonth)/12,1===modMonth?isLeapYear(year)?29:28:31-modMonth%7%2}indexOf=Array.prototype.indexOf?Array.prototype.indexOf:function(o){var i;for(i=0;i<this.length;++i)if(this[i]===o)return i;return-1},addFormatToken("M",["MM",2],"Mo",(function(){return this.month()+1})),addFormatToken("MMM",0,0,(function(format){return this.localeData().monthsShort(this,format)})),addFormatToken("MMMM",0,0,(function(format){return this.localeData().months(this,format)})),addRegexToken("M",match1to2,match1to2NoLeadingZero),addRegexToken("MM",match1to2,match2),addRegexToken("MMM",(function(isStrict,locale){return locale.monthsShortRegex(isStrict)})),addRegexToken("MMMM",(function(isStrict,locale){return locale.monthsRegex(isStrict)})),addParseToken(["M","MM"],(function(input,array){array[MONTH]=toInt(input)-1})),addParseToken(["MMM","MMMM"],(function(input,array,config,token){var month=config._locale.monthsParse(input,token,config._strict);null!=month?array[MONTH]=month:getParsingFlags(config).invalidMonth=input}));var defaultLocaleMonths="January_February_March_April_May_June_July_August_September_October_November_December".split("_"),defaultLocaleMonthsShort="Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),MONTHS_IN_FORMAT=/D[oD]?(\[[^\[\]]*\]|\s)+MMMM?/,defaultMonthsShortRegex=matchWord,defaultMonthsRegex=matchWord;function localeMonths(m,format){return m?isArray(this._months)?this._months[m.month()]:this._months[(this._months.isFormat||MONTHS_IN_FORMAT).test(format)?"format":"standalone"][m.month()]:isArray(this._months)?this._months:this._months.standalone}function localeMonthsShort(m,format){return m?isArray(this._monthsShort)?this._monthsShort[m.month()]:this._monthsShort[MONTHS_IN_FORMAT.test(format)?"format":"standalone"][m.month()]:isArray(this._monthsShort)?this._monthsShort:this._monthsShort.standalone}function handleStrictParse(monthName,format,strict){var i,ii,mom,llc=monthName.toLocaleLowerCase();if(!this._monthsParse)for(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[],i=0;i<12;++i)mom=createUTC([2e3,i]),this._shortMonthsParse[i]=this.monthsShort(mom,"").toLocaleLowerCase(),this._longMonthsParse[i]=this.months(mom,"").toLocaleLowerCase();return strict?"MMM"===format?-1!==(ii=indexOf.call(this._shortMonthsParse,llc))?ii:null:-1!==(ii=indexOf.call(this._longMonthsParse,llc))?ii:null:"MMM"===format?-1!==(ii=indexOf.call(this._shortMonthsParse,llc))||-1!==(ii=indexOf.call(this._longMonthsParse,llc))?ii:null:-1!==(ii=indexOf.call(this._longMonthsParse,llc))||-1!==(ii=indexOf.call(this._shortMonthsParse,llc))?ii:null}function localeMonthsParse(monthName,format,strict){var i,mom,regex;if(this._monthsParseExact)return handleStrictParse.call(this,monthName,format,strict);for(this._monthsParse||(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[]),i=0;i<12;i++){if(mom=createUTC([2e3,i]),strict&&!this._longMonthsParse[i]&&(this._longMonthsParse[i]=new RegExp("^"+this.months(mom,"").replace(".","")+"$","i"),this._shortMonthsParse[i]=new RegExp("^"+this.monthsShort(mom,"").replace(".","")+"$","i")),strict||this._monthsParse[i]||(regex="^"+this.months(mom,"")+"|^"+this.monthsShort(mom,""),this._monthsParse[i]=new RegExp(regex.replace(".",""),"i")),strict&&"MMMM"===format&&this._longMonthsParse[i].test(monthName))return i;if(strict&&"MMM"===format&&this._shortMonthsParse[i].test(monthName))return i;if(!strict&&this._monthsParse[i].test(monthName))return i}}function setMonth(mom,value){if(!mom.isValid())return mom;if("string"==typeof value)if(/^\d+$/.test(value))value=toInt(value);else if(!isNumber(value=mom.localeData().monthsParse(value)))return mom;var month=value,date=mom.date();return date=date<29?date:Math.min(date,daysInMonth(mom.year(),month)),mom._isUTC?mom._d.setUTCMonth(month,date):mom._d.setMonth(month,date),mom}function getSetMonth(value){return null!=value?(setMonth(this,value),hooks.updateOffset(this,!0),this):get(this,"Month")}function getDaysInMonth(){return daysInMonth(this.year(),this.month())}function monthsShortRegex(isStrict){return this._monthsParseExact?(hasOwnProp(this,"_monthsRegex")||computeMonthsParse.call(this),isStrict?this._monthsShortStrictRegex:this._monthsShortRegex):(hasOwnProp(this,"_monthsShortRegex")||(this._monthsShortRegex=defaultMonthsShortRegex),this._monthsShortStrictRegex&&isStrict?this._monthsShortStrictRegex:this._monthsShortRegex)}function monthsRegex(isStrict){return this._monthsParseExact?(hasOwnProp(this,"_monthsRegex")||computeMonthsParse.call(this),isStrict?this._monthsStrictRegex:this._monthsRegex):(hasOwnProp(this,"_monthsRegex")||(this._monthsRegex=defaultMonthsRegex),this._monthsStrictRegex&&isStrict?this._monthsStrictRegex:this._monthsRegex)}function computeMonthsParse(){function cmpLenRev(a,b){return b.length-a.length}var i,mom,shortP,longP,shortPieces=[],longPieces=[],mixedPieces=[];for(i=0;i<12;i++)mom=createUTC([2e3,i]),shortP=regexEscape(this.monthsShort(mom,"")),longP=regexEscape(this.months(mom,"")),shortPieces.push(shortP),longPieces.push(longP),mixedPieces.push(longP),mixedPieces.push(shortP);shortPieces.sort(cmpLenRev),longPieces.sort(cmpLenRev),mixedPieces.sort(cmpLenRev),this._monthsRegex=new RegExp("^("+mixedPieces.join("|")+")","i"),this._monthsShortRegex=this._monthsRegex,this._monthsStrictRegex=new RegExp("^("+longPieces.join("|")+")","i"),this._monthsShortStrictRegex=new RegExp("^("+shortPieces.join("|")+")","i")}function createDate(y,m,d,h,M,s,ms){var date;return y<100&&y>=0?(date=new Date(y+400,m,d,h,M,s,ms),isFinite(date.getFullYear())&&date.setFullYear(y)):date=new Date(y,m,d,h,M,s,ms),date}function createUTCDate(y){var date,args;return y<100&&y>=0?((args=Array.prototype.slice.call(arguments))[0]=y+400,date=new Date(Date.UTC.apply(null,args)),isFinite(date.getUTCFullYear())&&date.setUTCFullYear(y)):date=new Date(Date.UTC.apply(null,arguments)),date}function firstWeekOffset(year,dow,doy){var fwd=7+dow-doy;return-(7+createUTCDate(year,0,fwd).getUTCDay()-dow)%7+fwd-1}function dayOfYearFromWeeks(year,week,weekday,dow,doy){var resYear,resDayOfYear,dayOfYear=1+7*(week-1)+(7+weekday-dow)%7+firstWeekOffset(year,dow,doy);return dayOfYear<=0?resDayOfYear=daysInYear(resYear=year-1)+dayOfYear:dayOfYear>daysInYear(year)?(resYear=year+1,resDayOfYear=dayOfYear-daysInYear(year)):(resYear=year,resDayOfYear=dayOfYear),{year:resYear,dayOfYear:resDayOfYear}}function weekOfYear(mom,dow,doy){var resWeek,resYear,weekOffset=firstWeekOffset(mom.year(),dow,doy),week=Math.floor((mom.dayOfYear()-weekOffset-1)/7)+1;return week<1?resWeek=week+weeksInYear(resYear=mom.year()-1,dow,doy):week>weeksInYear(mom.year(),dow,doy)?(resWeek=week-weeksInYear(mom.year(),dow,doy),resYear=mom.year()+1):(resYear=mom.year(),resWeek=week),{week:resWeek,year:resYear}}function weeksInYear(year,dow,doy){var weekOffset=firstWeekOffset(year,dow,doy),weekOffsetNext=firstWeekOffset(year+1,dow,doy);return(daysInYear(year)-weekOffset+weekOffsetNext)/7}function localeWeek(mom){return weekOfYear(mom,this._week.dow,this._week.doy).week}addFormatToken("w",["ww",2],"wo","week"),addFormatToken("W",["WW",2],"Wo","isoWeek"),addRegexToken("w",match1to2,match1to2NoLeadingZero),addRegexToken("ww",match1to2,match2),addRegexToken("W",match1to2,match1to2NoLeadingZero),addRegexToken("WW",match1to2,match2),addWeekParseToken(["w","ww","W","WW"],(function(input,week,config,token){week[token.substr(0,1)]=toInt(input)}));var defaultLocaleWeek={dow:0,doy:6};function localeFirstDayOfWeek(){return this._week.dow}function localeFirstDayOfYear(){return this._week.doy}function getSetWeek(input){var week=this.localeData().week(this);return null==input?week:this.add(7*(input-week),"d")}function getSetISOWeek(input){var week=weekOfYear(this,1,4).week;return null==input?week:this.add(7*(input-week),"d")}function parseWeekday(input,locale){return"string"!=typeof input?input:isNaN(input)?"number"==typeof(input=locale.weekdaysParse(input))?input:null:parseInt(input,10)}function parseIsoWeekday(input,locale){return"string"==typeof input?locale.weekdaysParse(input)%7||7:isNaN(input)?null:input}function shiftWeekdays(ws,n){return ws.slice(n,7).concat(ws.slice(0,n))}addFormatToken("d",0,"do","day"),addFormatToken("dd",0,0,(function(format){return this.localeData().weekdaysMin(this,format)})),addFormatToken("ddd",0,0,(function(format){return this.localeData().weekdaysShort(this,format)})),addFormatToken("dddd",0,0,(function(format){return this.localeData().weekdays(this,format)})),addFormatToken("e",0,0,"weekday"),addFormatToken("E",0,0,"isoWeekday"),addRegexToken("d",match1to2),addRegexToken("e",match1to2),addRegexToken("E",match1to2),addRegexToken("dd",(function(isStrict,locale){return locale.weekdaysMinRegex(isStrict)})),addRegexToken("ddd",(function(isStrict,locale){return locale.weekdaysShortRegex(isStrict)})),addRegexToken("dddd",(function(isStrict,locale){return locale.weekdaysRegex(isStrict)})),addWeekParseToken(["dd","ddd","dddd"],(function(input,week,config,token){var weekday=config._locale.weekdaysParse(input,token,config._strict);null!=weekday?week.d=weekday:getParsingFlags(config).invalidWeekday=input})),addWeekParseToken(["d","e","E"],(function(input,week,config,token){week[token]=toInt(input)}));var defaultLocaleWeekdays="Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),defaultLocaleWeekdaysShort="Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),defaultLocaleWeekdaysMin="Su_Mo_Tu_We_Th_Fr_Sa".split("_"),defaultWeekdaysRegex=matchWord,defaultWeekdaysShortRegex=matchWord,defaultWeekdaysMinRegex=matchWord;function localeWeekdays(m,format){var weekdays=isArray(this._weekdays)?this._weekdays:this._weekdays[m&&!0!==m&&this._weekdays.isFormat.test(format)?"format":"standalone"];return!0===m?shiftWeekdays(weekdays,this._week.dow):m?weekdays[m.day()]:weekdays}function localeWeekdaysShort(m){return!0===m?shiftWeekdays(this._weekdaysShort,this._week.dow):m?this._weekdaysShort[m.day()]:this._weekdaysShort}function localeWeekdaysMin(m){return!0===m?shiftWeekdays(this._weekdaysMin,this._week.dow):m?this._weekdaysMin[m.day()]:this._weekdaysMin}function handleStrictParse$1(weekdayName,format,strict){var i,ii,mom,llc=weekdayName.toLocaleLowerCase();if(!this._weekdaysParse)for(this._weekdaysParse=[],this._shortWeekdaysParse=[],this._minWeekdaysParse=[],i=0;i<7;++i)mom=createUTC([2e3,1]).day(i),this._minWeekdaysParse[i]=this.weekdaysMin(mom,"").toLocaleLowerCase(),this._shortWeekdaysParse[i]=this.weekdaysShort(mom,"").toLocaleLowerCase(),this._weekdaysParse[i]=this.weekdays(mom,"").toLocaleLowerCase();return strict?"dddd"===format?-1!==(ii=indexOf.call(this._weekdaysParse,llc))?ii:null:"ddd"===format?-1!==(ii=indexOf.call(this._shortWeekdaysParse,llc))?ii:null:-1!==(ii=indexOf.call(this._minWeekdaysParse,llc))?ii:null:"dddd"===format?-1!==(ii=indexOf.call(this._weekdaysParse,llc))||-1!==(ii=indexOf.call(this._shortWeekdaysParse,llc))||-1!==(ii=indexOf.call(this._minWeekdaysParse,llc))?ii:null:"ddd"===format?-1!==(ii=indexOf.call(this._shortWeekdaysParse,llc))||-1!==(ii=indexOf.call(this._weekdaysParse,llc))||-1!==(ii=indexOf.call(this._minWeekdaysParse,llc))?ii:null:-1!==(ii=indexOf.call(this._minWeekdaysParse,llc))||-1!==(ii=indexOf.call(this._weekdaysParse,llc))||-1!==(ii=indexOf.call(this._shortWeekdaysParse,llc))?ii:null}function localeWeekdaysParse(weekdayName,format,strict){var i,mom,regex;if(this._weekdaysParseExact)return handleStrictParse$1.call(this,weekdayName,format,strict);for(this._weekdaysParse||(this._weekdaysParse=[],this._minWeekdaysParse=[],this._shortWeekdaysParse=[],this._fullWeekdaysParse=[]),i=0;i<7;i++){if(mom=createUTC([2e3,1]).day(i),strict&&!this._fullWeekdaysParse[i]&&(this._fullWeekdaysParse[i]=new RegExp("^"+this.weekdays(mom,"").replace(".","\\.?")+"$","i"),this._shortWeekdaysParse[i]=new RegExp("^"+this.weekdaysShort(mom,"").replace(".","\\.?")+"$","i"),this._minWeekdaysParse[i]=new RegExp("^"+this.weekdaysMin(mom,"").replace(".","\\.?")+"$","i")),this._weekdaysParse[i]||(regex="^"+this.weekdays(mom,"")+"|^"+this.weekdaysShort(mom,"")+"|^"+this.weekdaysMin(mom,""),this._weekdaysParse[i]=new RegExp(regex.replace(".",""),"i")),strict&&"dddd"===format&&this._fullWeekdaysParse[i].test(weekdayName))return i;if(strict&&"ddd"===format&&this._shortWeekdaysParse[i].test(weekdayName))return i;if(strict&&"dd"===format&&this._minWeekdaysParse[i].test(weekdayName))return i;if(!strict&&this._weekdaysParse[i].test(weekdayName))return i}}function getSetDayOfWeek(input){if(!this.isValid())return null!=input?this:NaN;var day=get(this,"Day");return null!=input?(input=parseWeekday(input,this.localeData()),this.add(input-day,"d")):day}function getSetLocaleDayOfWeek(input){if(!this.isValid())return null!=input?this:NaN;var weekday=(this.day()+7-this.localeData()._week.dow)%7;return null==input?weekday:this.add(input-weekday,"d")}function getSetISODayOfWeek(input){if(!this.isValid())return null!=input?this:NaN;if(null!=input){var weekday=parseIsoWeekday(input,this.localeData());return this.day(this.day()%7?weekday:weekday-7)}return this.day()||7}function weekdaysRegex(isStrict){return this._weekdaysParseExact?(hasOwnProp(this,"_weekdaysRegex")||computeWeekdaysParse.call(this),isStrict?this._weekdaysStrictRegex:this._weekdaysRegex):(hasOwnProp(this,"_weekdaysRegex")||(this._weekdaysRegex=defaultWeekdaysRegex),this._weekdaysStrictRegex&&isStrict?this._weekdaysStrictRegex:this._weekdaysRegex)}function weekdaysShortRegex(isStrict){return this._weekdaysParseExact?(hasOwnProp(this,"_weekdaysRegex")||computeWeekdaysParse.call(this),isStrict?this._weekdaysShortStrictRegex:this._weekdaysShortRegex):(hasOwnProp(this,"_weekdaysShortRegex")||(this._weekdaysShortRegex=defaultWeekdaysShortRegex),this._weekdaysShortStrictRegex&&isStrict?this._weekdaysShortStrictRegex:this._weekdaysShortRegex)}function weekdaysMinRegex(isStrict){return this._weekdaysParseExact?(hasOwnProp(this,"_weekdaysRegex")||computeWeekdaysParse.call(this),isStrict?this._weekdaysMinStrictRegex:this._weekdaysMinRegex):(hasOwnProp(this,"_weekdaysMinRegex")||(this._weekdaysMinRegex=defaultWeekdaysMinRegex),this._weekdaysMinStrictRegex&&isStrict?this._weekdaysMinStrictRegex:this._weekdaysMinRegex)}function computeWeekdaysParse(){function cmpLenRev(a,b){return b.length-a.length}var i,mom,minp,shortp,longp,minPieces=[],shortPieces=[],longPieces=[],mixedPieces=[];for(i=0;i<7;i++)mom=createUTC([2e3,1]).day(i),minp=regexEscape(this.weekdaysMin(mom,"")),shortp=regexEscape(this.weekdaysShort(mom,"")),longp=regexEscape(this.weekdays(mom,"")),minPieces.push(minp),shortPieces.push(shortp),longPieces.push(longp),mixedPieces.push(minp),mixedPieces.push(shortp),mixedPieces.push(longp);minPieces.sort(cmpLenRev),shortPieces.sort(cmpLenRev),longPieces.sort(cmpLenRev),mixedPieces.sort(cmpLenRev),this._weekdaysRegex=new RegExp("^("+mixedPieces.join("|")+")","i"),this._weekdaysShortRegex=this._weekdaysRegex,this._weekdaysMinRegex=this._weekdaysRegex,this._weekdaysStrictRegex=new RegExp("^("+longPieces.join("|")+")","i"),this._weekdaysShortStrictRegex=new RegExp("^("+shortPieces.join("|")+")","i"),this._weekdaysMinStrictRegex=new RegExp("^("+minPieces.join("|")+")","i")}function hFormat(){return this.hours()%12||12}function kFormat(){return this.hours()||24}function meridiem(token,lowercase){addFormatToken(token,0,0,(function(){return this.localeData().meridiem(this.hours(),this.minutes(),lowercase)}))}function matchMeridiem(isStrict,locale){return locale._meridiemParse}function localeIsPM(input){return"p"===(input+"").toLowerCase().charAt(0)}addFormatToken("H",["HH",2],0,"hour"),addFormatToken("h",["hh",2],0,hFormat),addFormatToken("k",["kk",2],0,kFormat),addFormatToken("hmm",0,0,(function(){return""+hFormat.apply(this)+zeroFill(this.minutes(),2)})),addFormatToken("hmmss",0,0,(function(){return""+hFormat.apply(this)+zeroFill(this.minutes(),2)+zeroFill(this.seconds(),2)})),addFormatToken("Hmm",0,0,(function(){return""+this.hours()+zeroFill(this.minutes(),2)})),addFormatToken("Hmmss",0,0,(function(){return""+this.hours()+zeroFill(this.minutes(),2)+zeroFill(this.seconds(),2)})),meridiem("a",!0),meridiem("A",!1),addRegexToken("a",matchMeridiem),addRegexToken("A",matchMeridiem),addRegexToken("H",match1to2,match1to2HasZero),addRegexToken("h",match1to2,match1to2NoLeadingZero),addRegexToken("k",match1to2,match1to2NoLeadingZero),addRegexToken("HH",match1to2,match2),addRegexToken("hh",match1to2,match2),addRegexToken("kk",match1to2,match2),addRegexToken("hmm",match3to4),addRegexToken("hmmss",match5to6),addRegexToken("Hmm",match3to4),addRegexToken("Hmmss",match5to6),addParseToken(["H","HH"],HOUR),addParseToken(["k","kk"],(function(input,array,config){var kInput=toInt(input);array[HOUR]=24===kInput?0:kInput})),addParseToken(["a","A"],(function(input,array,config){config._isPm=config._locale.isPM(input),config._meridiem=input})),addParseToken(["h","hh"],(function(input,array,config){array[HOUR]=toInt(input),getParsingFlags(config).bigHour=!0})),addParseToken("hmm",(function(input,array,config){var pos=input.length-2;array[HOUR]=toInt(input.substr(0,pos)),array[MINUTE]=toInt(input.substr(pos)),getParsingFlags(config).bigHour=!0})),addParseToken("hmmss",(function(input,array,config){var pos1=input.length-4,pos2=input.length-2;array[HOUR]=toInt(input.substr(0,pos1)),array[MINUTE]=toInt(input.substr(pos1,2)),array[SECOND]=toInt(input.substr(pos2)),getParsingFlags(config).bigHour=!0})),addParseToken("Hmm",(function(input,array,config){var pos=input.length-2;array[HOUR]=toInt(input.substr(0,pos)),array[MINUTE]=toInt(input.substr(pos))})),addParseToken("Hmmss",(function(input,array,config){var pos1=input.length-4,pos2=input.length-2;array[HOUR]=toInt(input.substr(0,pos1)),array[MINUTE]=toInt(input.substr(pos1,2)),array[SECOND]=toInt(input.substr(pos2))}));var defaultLocaleMeridiemParse=/[ap]\.?m?\.?/i,getSetHour=makeGetSet("Hours",!0);function localeMeridiem(hours,minutes,isLower){return hours>11?isLower?"pm":"PM":isLower?"am":"AM"}var globalLocale,baseConfig={calendar:defaultCalendar,longDateFormat:defaultLongDateFormat,invalidDate:defaultInvalidDate,ordinal:defaultOrdinal,dayOfMonthOrdinalParse:defaultDayOfMonthOrdinalParse,relativeTime:defaultRelativeTime,months:defaultLocaleMonths,monthsShort:defaultLocaleMonthsShort,week:defaultLocaleWeek,weekdays:defaultLocaleWeekdays,weekdaysMin:defaultLocaleWeekdaysMin,weekdaysShort:defaultLocaleWeekdaysShort,meridiemParse:defaultLocaleMeridiemParse},locales={},localeFamilies={};function commonPrefix(arr1,arr2){var i,minl=Math.min(arr1.length,arr2.length);for(i=0;i<minl;i+=1)if(arr1[i]!==arr2[i])return i;return minl}function normalizeLocale(key){return key?key.toLowerCase().replace("_","-"):key}function chooseLocale(names){for(var j,next,locale,split,i=0;i<names.length;){for(j=(split=normalizeLocale(names[i]).split("-")).length,next=(next=normalizeLocale(names[i+1]))?next.split("-"):null;j>0;){if(locale=loadLocale(split.slice(0,j).join("-")))return locale;if(next&&next.length>=j&&commonPrefix(split,next)>=j-1)break;j--}i++}return globalLocale}function isLocaleNameSane(name){return!(!name||!name.match("^[^/\\\\]*$"))}function loadLocale(name){var oldLocale=null;if(void 0===locales[name]&&module&&module.exports&&isLocaleNameSane(name))try{oldLocale=globalLocale._abbr,Object(function webpackMissingModule(){var e=new Error("Cannot find module 'undefined'");throw e.code="MODULE_NOT_FOUND",e}()),getSetGlobalLocale(oldLocale)}catch(e){locales[name]=null}return locales[name]}function getSetGlobalLocale(key,values){var data;return key&&((data=isUndefined(values)?getLocale(key):defineLocale(key,values))?globalLocale=data:"undefined"!=typeof console&&console.warn&&console.warn("Locale "+key+" not found. Did you forget to load it?")),globalLocale._abbr}function defineLocale(name,config){if(null!==config){var locale,parentConfig=baseConfig;if(config.abbr=name,null!=locales[name])deprecateSimple("defineLocaleOverride","use moment.updateLocale(localeName, config) to change an existing locale. moment.defineLocale(localeName, config) should only be used for creating a new locale See http://momentjs.com/guides/#/warnings/define-locale/ for more info."),parentConfig=locales[name]._config;else if(null!=config.parentLocale)if(null!=locales[config.parentLocale])parentConfig=locales[config.parentLocale]._config;else{if(null==(locale=loadLocale(config.parentLocale)))return localeFamilies[config.parentLocale]||(localeFamilies[config.parentLocale]=[]),localeFamilies[config.parentLocale].push({name,config}),null;parentConfig=locale._config}return locales[name]=new Locale(mergeConfigs(parentConfig,config)),localeFamilies[name]&&localeFamilies[name].forEach((function(x){defineLocale(x.name,x.config)})),getSetGlobalLocale(name),locales[name]}return delete locales[name],null}function updateLocale(name,config){if(null!=config){var locale,tmpLocale,parentConfig=baseConfig;null!=locales[name]&&null!=locales[name].parentLocale?locales[name].set(mergeConfigs(locales[name]._config,config)):(null!=(tmpLocale=loadLocale(name))&&(parentConfig=tmpLocale._config),config=mergeConfigs(parentConfig,config),null==tmpLocale&&(config.abbr=name),(locale=new Locale(config)).parentLocale=locales[name],locales[name]=locale),getSetGlobalLocale(name)}else null!=locales[name]&&(null!=locales[name].parentLocale?(locales[name]=locales[name].parentLocale,name===getSetGlobalLocale()&&getSetGlobalLocale(name)):null!=locales[name]&&delete locales[name]);return locales[name]}function getLocale(key){var locale;if(key&&key._locale&&key._locale._abbr&&(key=key._locale._abbr),!key)return globalLocale;if(!isArray(key)){if(locale=loadLocale(key))return locale;key=[key]}return chooseLocale(key)}function listLocales(){return keys(locales)}function checkOverflow(m){var overflow,a=m._a;return a&&-2===getParsingFlags(m).overflow&&(overflow=a[MONTH]<0||a[MONTH]>11?MONTH:a[DATE]<1||a[DATE]>daysInMonth(a[YEAR],a[MONTH])?DATE:a[HOUR]<0||a[HOUR]>24||24===a[HOUR]&&(0!==a[MINUTE]||0!==a[SECOND]||0!==a[MILLISECOND])?HOUR:a[MINUTE]<0||a[MINUTE]>59?MINUTE:a[SECOND]<0||a[SECOND]>59?SECOND:a[MILLISECOND]<0||a[MILLISECOND]>999?MILLISECOND:-1,getParsingFlags(m)._overflowDayOfYear&&(overflow<YEAR||overflow>DATE)&&(overflow=DATE),getParsingFlags(m)._overflowWeeks&&-1===overflow&&(overflow=WEEK),getParsingFlags(m)._overflowWeekday&&-1===overflow&&(overflow=WEEKDAY),getParsingFlags(m).overflow=overflow),m}var extendedIsoRegex=/^\s*((?:[+-]\d{6}|\d{4})-(?:\d\d-\d\d|W\d\d-\d|W\d\d|\d\d\d|\d\d))(?:(T| )(\d\d(?::\d\d(?::\d\d(?:[.,]\d+)?)?)?)([+-]\d\d(?::?\d\d)?|\s*Z)?)?$/,basicIsoRegex=/^\s*((?:[+-]\d{6}|\d{4})(?:\d\d\d\d|W\d\d\d|W\d\d|\d\d\d|\d\d|))(?:(T| )(\d\d(?:\d\d(?:\d\d(?:[.,]\d+)?)?)?)([+-]\d\d(?::?\d\d)?|\s*Z)?)?$/,tzRegex=/Z|[+-]\d\d(?::?\d\d)?/,isoDates=[["YYYYYY-MM-DD",/[+-]\d{6}-\d\d-\d\d/],["YYYY-MM-DD",/\d{4}-\d\d-\d\d/],["GGGG-[W]WW-E",/\d{4}-W\d\d-\d/],["GGGG-[W]WW",/\d{4}-W\d\d/,!1],["YYYY-DDD",/\d{4}-\d{3}/],["YYYY-MM",/\d{4}-\d\d/,!1],["YYYYYYMMDD",/[+-]\d{10}/],["YYYYMMDD",/\d{8}/],["GGGG[W]WWE",/\d{4}W\d{3}/],["GGGG[W]WW",/\d{4}W\d{2}/,!1],["YYYYDDD",/\d{7}/],["YYYYMM",/\d{6}/,!1],["YYYY",/\d{4}/,!1]],isoTimes=[["HH:mm:ss.SSSS",/\d\d:\d\d:\d\d\.\d+/],["HH:mm:ss,SSSS",/\d\d:\d\d:\d\d,\d+/],["HH:mm:ss",/\d\d:\d\d:\d\d/],["HH:mm",/\d\d:\d\d/],["HHmmss.SSSS",/\d\d\d\d\d\d\.\d+/],["HHmmss,SSSS",/\d\d\d\d\d\d,\d+/],["HHmmss",/\d\d\d\d\d\d/],["HHmm",/\d\d\d\d/],["HH",/\d\d/]],aspNetJsonRegex=/^\/?Date\((-?\d+)/i,rfc2822=/^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),?\s)?(\d{1,2})\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s(\d{2,4})\s(\d\d):(\d\d)(?::(\d\d))?\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|([+-]\d{4}))$/,obsOffsets={UT:0,GMT:0,EDT:-240,EST:-300,CDT:-300,CST:-360,MDT:-360,MST:-420,PDT:-420,PST:-480};function configFromISO(config){var i,l,allowTime,dateFormat,timeFormat,tzFormat,string=config._i,match=extendedIsoRegex.exec(string)||basicIsoRegex.exec(string),isoDatesLen=isoDates.length,isoTimesLen=isoTimes.length;if(match){for(getParsingFlags(config).iso=!0,i=0,l=isoDatesLen;i<l;i++)if(isoDates[i][1].exec(match[1])){dateFormat=isoDates[i][0],allowTime=!1!==isoDates[i][2];break}if(null==dateFormat)return void(config._isValid=!1);if(match[3]){for(i=0,l=isoTimesLen;i<l;i++)if(isoTimes[i][1].exec(match[3])){timeFormat=(match[2]||" ")+isoTimes[i][0];break}if(null==timeFormat)return void(config._isValid=!1)}if(!allowTime&&null!=timeFormat)return void(config._isValid=!1);if(match[4]){if(!tzRegex.exec(match[4]))return void(config._isValid=!1);tzFormat="Z"}config._f=dateFormat+(timeFormat||"")+(tzFormat||""),configFromStringAndFormat(config)}else config._isValid=!1}function extractFromRFC2822Strings(yearStr,monthStr,dayStr,hourStr,minuteStr,secondStr){var result=[untruncateYear(yearStr),defaultLocaleMonthsShort.indexOf(monthStr),parseInt(dayStr,10),parseInt(hourStr,10),parseInt(minuteStr,10)];return secondStr&&result.push(parseInt(secondStr,10)),result}function untruncateYear(yearStr){var year=parseInt(yearStr,10);return year<=49?2e3+year:year<=999?1900+year:year}function preprocessRFC2822(s){return s.replace(/\([^()]*\)|[\n\t]/g," ").replace(/(\s\s+)/g," ").replace(/^\s\s*/,"").replace(/\s\s*$/,"")}function checkWeekday(weekdayStr,parsedInput,config){return!weekdayStr||defaultLocaleWeekdaysShort.indexOf(weekdayStr)===new Date(parsedInput[0],parsedInput[1],parsedInput[2]).getDay()||(getParsingFlags(config).weekdayMismatch=!0,config._isValid=!1,!1)}function calculateOffset(obsOffset,militaryOffset,numOffset){if(obsOffset)return obsOffsets[obsOffset];if(militaryOffset)return 0;var hm=parseInt(numOffset,10),m=hm%100;return(hm-m)/100*60+m}function configFromRFC2822(config){var parsedArray,match=rfc2822.exec(preprocessRFC2822(config._i));if(match){if(parsedArray=extractFromRFC2822Strings(match[4],match[3],match[2],match[5],match[6],match[7]),!checkWeekday(match[1],parsedArray,config))return;config._a=parsedArray,config._tzm=calculateOffset(match[8],match[9],match[10]),config._d=createUTCDate.apply(null,config._a),config._d.setUTCMinutes(config._d.getUTCMinutes()-config._tzm),getParsingFlags(config).rfc2822=!0}else config._isValid=!1}function configFromString(config){var matched=aspNetJsonRegex.exec(config._i);null===matched?(configFromISO(config),!1===config._isValid&&(delete config._isValid,configFromRFC2822(config),!1===config._isValid&&(delete config._isValid,config._strict?config._isValid=!1:hooks.createFromInputFallback(config)))):config._d=new Date(+matched[1])}function defaults(a,b,c){return null!=a?a:null!=b?b:c}function currentDateArray(config){var nowValue=new Date(hooks.now());return config._useUTC?[nowValue.getUTCFullYear(),nowValue.getUTCMonth(),nowValue.getUTCDate()]:[nowValue.getFullYear(),nowValue.getMonth(),nowValue.getDate()]}function configFromArray(config){var i,date,currentDate,expectedWeekday,yearToUse,input=[];if(!config._d){for(currentDate=currentDateArray(config),config._w&&null==config._a[DATE]&&null==config._a[MONTH]&&dayOfYearFromWeekInfo(config),null!=config._dayOfYear&&(yearToUse=defaults(config._a[YEAR],currentDate[YEAR]),(config._dayOfYear>daysInYear(yearToUse)||0===config._dayOfYear)&&(getParsingFlags(config)._overflowDayOfYear=!0),date=createUTCDate(yearToUse,0,config._dayOfYear),config._a[MONTH]=date.getUTCMonth(),config._a[DATE]=date.getUTCDate()),i=0;i<3&&null==config._a[i];++i)config._a[i]=input[i]=currentDate[i];for(;i<7;i++)config._a[i]=input[i]=null==config._a[i]?2===i?1:0:config._a[i];24===config._a[HOUR]&&0===config._a[MINUTE]&&0===config._a[SECOND]&&0===config._a[MILLISECOND]&&(config._nextDay=!0,config._a[HOUR]=0),config._d=(config._useUTC?createUTCDate:createDate).apply(null,input),expectedWeekday=config._useUTC?config._d.getUTCDay():config._d.getDay(),null!=config._tzm&&config._d.setUTCMinutes(config._d.getUTCMinutes()-config._tzm),config._nextDay&&(config._a[HOUR]=24),config._w&&void 0!==config._w.d&&config._w.d!==expectedWeekday&&(getParsingFlags(config).weekdayMismatch=!0)}}function dayOfYearFromWeekInfo(config){var w,weekYear,week,weekday,dow,doy,temp,weekdayOverflow,curWeek;null!=(w=config._w).GG||null!=w.W||null!=w.E?(dow=1,doy=4,weekYear=defaults(w.GG,config._a[YEAR],weekOfYear(createLocal(),1,4).year),week=defaults(w.W,1),((weekday=defaults(w.E,1))<1||weekday>7)&&(weekdayOverflow=!0)):(dow=config._locale._week.dow,doy=config._locale._week.doy,curWeek=weekOfYear(createLocal(),dow,doy),weekYear=defaults(w.gg,config._a[YEAR],curWeek.year),week=defaults(w.w,curWeek.week),null!=w.d?((weekday=w.d)<0||weekday>6)&&(weekdayOverflow=!0):null!=w.e?(weekday=w.e+dow,(w.e<0||w.e>6)&&(weekdayOverflow=!0)):weekday=dow),week<1||week>weeksInYear(weekYear,dow,doy)?getParsingFlags(config)._overflowWeeks=!0:null!=weekdayOverflow?getParsingFlags(config)._overflowWeekday=!0:(temp=dayOfYearFromWeeks(weekYear,week,weekday,dow,doy),config._a[YEAR]=temp.year,config._dayOfYear=temp.dayOfYear)}function configFromStringAndFormat(config){if(config._f!==hooks.ISO_8601)if(config._f!==hooks.RFC_2822){config._a=[],getParsingFlags(config).empty=!0;var i,parsedInput,tokens,token,skipped,era,tokenLen,string=""+config._i,stringLength=string.length,totalParsedInputLength=0;for(tokenLen=(tokens=expandFormat(config._f,config._locale).match(formattingTokens)||[]).length,i=0;i<tokenLen;i++)token=tokens[i],(parsedInput=(string.match(getParseRegexForToken(token,config))||[])[0])&&((skipped=string.substr(0,string.indexOf(parsedInput))).length>0&&getParsingFlags(config).unusedInput.push(skipped),string=string.slice(string.indexOf(parsedInput)+parsedInput.length),totalParsedInputLength+=parsedInput.length),formatTokenFunctions[token]?(parsedInput?getParsingFlags(config).empty=!1:getParsingFlags(config).unusedTokens.push(token),addTimeToArrayFromToken(token,parsedInput,config)):config._strict&&!parsedInput&&getParsingFlags(config).unusedTokens.push(token);getParsingFlags(config).charsLeftOver=stringLength-totalParsedInputLength,string.length>0&&getParsingFlags(config).unusedInput.push(string),config._a[HOUR]<=12&&!0===getParsingFlags(config).bigHour&&config._a[HOUR]>0&&(getParsingFlags(config).bigHour=void 0),getParsingFlags(config).parsedDateParts=config._a.slice(0),getParsingFlags(config).meridiem=config._meridiem,config._a[HOUR]=meridiemFixWrap(config._locale,config._a[HOUR],config._meridiem),null!==(era=getParsingFlags(config).era)&&(config._a[YEAR]=config._locale.erasConvertYear(era,config._a[YEAR])),configFromArray(config),checkOverflow(config)}else configFromRFC2822(config);else configFromISO(config)}function meridiemFixWrap(locale,hour,meridiem){var isPm;return null==meridiem?hour:null!=locale.meridiemHour?locale.meridiemHour(hour,meridiem):null!=locale.isPM?((isPm=locale.isPM(meridiem))&&hour<12&&(hour+=12),isPm||12!==hour||(hour=0),hour):hour}function configFromStringAndArray(config){var tempConfig,bestMoment,scoreToBeat,i,currentScore,validFormatFound,bestFormatIsValid=!1,configfLen=config._f.length;if(0===configfLen)return getParsingFlags(config).invalidFormat=!0,void(config._d=new Date(NaN));for(i=0;i<configfLen;i++)currentScore=0,validFormatFound=!1,tempConfig=copyConfig({},config),null!=config._useUTC&&(tempConfig._useUTC=config._useUTC),tempConfig._f=config._f[i],configFromStringAndFormat(tempConfig),isValid(tempConfig)&&(validFormatFound=!0),currentScore+=getParsingFlags(tempConfig).charsLeftOver,currentScore+=10*getParsingFlags(tempConfig).unusedTokens.length,getParsingFlags(tempConfig).score=currentScore,bestFormatIsValid?currentScore<scoreToBeat&&(scoreToBeat=currentScore,bestMoment=tempConfig):(null==scoreToBeat||currentScore<scoreToBeat||validFormatFound)&&(scoreToBeat=currentScore,bestMoment=tempConfig,validFormatFound&&(bestFormatIsValid=!0));extend(config,bestMoment||tempConfig)}function configFromObject(config){if(!config._d){var i=normalizeObjectUnits(config._i),dayOrDate=void 0===i.day?i.date:i.day;config._a=map([i.year,i.month,dayOrDate,i.hour,i.minute,i.second,i.millisecond],(function(obj){return obj&&parseInt(obj,10)})),configFromArray(config)}}function createFromConfig(config){var res=new Moment(checkOverflow(prepareConfig(config)));return res._nextDay&&(res.add(1,"d"),res._nextDay=void 0),res}function prepareConfig(config){var input=config._i,format=config._f;return config._locale=config._locale||getLocale(config._l),null===input||void 0===format&&""===input?createInvalid({nullInput:!0}):("string"==typeof input&&(config._i=input=config._locale.preparse(input)),isMoment(input)?new Moment(checkOverflow(input)):(isDate(input)?config._d=input:isArray(format)?configFromStringAndArray(config):format?configFromStringAndFormat(config):configFromInput(config),isValid(config)||(config._d=null),config))}function configFromInput(config){var input=config._i;isUndefined(input)?config._d=new Date(hooks.now()):isDate(input)?config._d=new Date(input.valueOf()):"string"==typeof input?configFromString(config):isArray(input)?(config._a=map(input.slice(0),(function(obj){return parseInt(obj,10)})),configFromArray(config)):isObject(input)?configFromObject(config):isNumber(input)?config._d=new Date(input):hooks.createFromInputFallback(config)}function createLocalOrUTC(input,format,locale,strict,isUTC){var c={};return!0!==format&&!1!==format||(strict=format,format=void 0),!0!==locale&&!1!==locale||(strict=locale,locale=void 0),(isObject(input)&&isObjectEmpty(input)||isArray(input)&&0===input.length)&&(input=void 0),c._isAMomentObject=!0,c._useUTC=c._isUTC=isUTC,c._l=locale,c._i=input,c._f=format,c._strict=strict,createFromConfig(c)}function createLocal(input,format,locale,strict){return createLocalOrUTC(input,format,locale,strict,!1)}hooks.createFromInputFallback=deprecate("value provided is not in a recognized RFC2822 or ISO format. moment construction falls back to js Date(), which is not reliable across all browsers and versions. Non RFC2822/ISO date formats are discouraged. Please refer to http://momentjs.com/guides/#/warnings/js-date/ for more info.",(function(config){config._d=new Date(config._i+(config._useUTC?" UTC":""))})),hooks.ISO_8601=function(){},hooks.RFC_2822=function(){};var prototypeMin=deprecate("moment().min is deprecated, use moment.max instead. http://momentjs.com/guides/#/warnings/min-max/",(function(){var other=createLocal.apply(null,arguments);return this.isValid()&&other.isValid()?other<this?this:other:createInvalid()})),prototypeMax=deprecate("moment().max is deprecated, use moment.min instead. http://momentjs.com/guides/#/warnings/min-max/",(function(){var other=createLocal.apply(null,arguments);return this.isValid()&&other.isValid()?other>this?this:other:createInvalid()}));function pickBy(fn,moments){var res,i;if(1===moments.length&&isArray(moments[0])&&(moments=moments[0]),!moments.length)return createLocal();for(res=moments[0],i=1;i<moments.length;++i)moments[i].isValid()&&!moments[i][fn](res)||(res=moments[i]);return res}function min(){return pickBy("isBefore",[].slice.call(arguments,0))}function max(){return pickBy("isAfter",[].slice.call(arguments,0))}var now=function(){return Date.now?Date.now():+new Date},ordering=["year","quarter","month","week","day","hour","minute","second","millisecond"];function isDurationValid(m){var key,i,unitHasDecimal=!1,orderLen=ordering.length;for(key in m)if(hasOwnProp(m,key)&&(-1===indexOf.call(ordering,key)||null!=m[key]&&isNaN(m[key])))return!1;for(i=0;i<orderLen;++i)if(m[ordering[i]]){if(unitHasDecimal)return!1;parseFloat(m[ordering[i]])!==toInt(m[ordering[i]])&&(unitHasDecimal=!0)}return!0}function isValid$1(){return this._isValid}function createInvalid$1(){return createDuration(NaN)}function Duration(duration){var normalizedInput=normalizeObjectUnits(duration),years=normalizedInput.year||0,quarters=normalizedInput.quarter||0,months=normalizedInput.month||0,weeks=normalizedInput.week||normalizedInput.isoWeek||0,days=normalizedInput.day||0,hours=normalizedInput.hour||0,minutes=normalizedInput.minute||0,seconds=normalizedInput.second||0,milliseconds=normalizedInput.millisecond||0;this._isValid=isDurationValid(normalizedInput),this._milliseconds=+milliseconds+1e3*seconds+6e4*minutes+1e3*hours*60*60,this._days=+days+7*weeks,this._months=+months+3*quarters+12*years,this._data={},this._locale=getLocale(),this._bubble()}function isDuration(obj){return obj instanceof Duration}function absRound(number){return number<0?-1*Math.round(-1*number):Math.round(number)}function compareArrays(array1,array2,dontConvert){var i,len=Math.min(array1.length,array2.length),lengthDiff=Math.abs(array1.length-array2.length),diffs=0;for(i=0;i<len;i++)(dontConvert&&array1[i]!==array2[i]||!dontConvert&&toInt(array1[i])!==toInt(array2[i]))&&diffs++;return diffs+lengthDiff}function offset(token,separator){addFormatToken(token,0,0,(function(){var offset=this.utcOffset(),sign="+";return offset<0&&(offset=-offset,sign="-"),sign+zeroFill(~~(offset/60),2)+separator+zeroFill(~~offset%60,2)}))}offset("Z",":"),offset("ZZ",""),addRegexToken("Z",matchShortOffset),addRegexToken("ZZ",matchShortOffset),addParseToken(["Z","ZZ"],(function(input,array,config){config._useUTC=!0,config._tzm=offsetFromString(matchShortOffset,input)}));var chunkOffset=/([\+\-]|\d\d)/gi;function offsetFromString(matcher,string){var parts,minutes,matches=(string||"").match(matcher);return null===matches?null:0===(minutes=60*(parts=((matches[matches.length-1]||[])+"").match(chunkOffset)||["-",0,0])[1]+toInt(parts[2]))?0:"+"===parts[0]?minutes:-minutes}function cloneWithOffset(input,model){var res,diff;return model._isUTC?(res=model.clone(),diff=(isMoment(input)||isDate(input)?input.valueOf():createLocal(input).valueOf())-res.valueOf(),res._d.setTime(res._d.valueOf()+diff),hooks.updateOffset(res,!1),res):createLocal(input).local()}function getDateOffset(m){return-Math.round(m._d.getTimezoneOffset())}function getSetOffset(input,keepLocalTime,keepMinutes){var localAdjust,offset=this._offset||0;if(!this.isValid())return null!=input?this:NaN;if(null!=input){if("string"==typeof input){if(null===(input=offsetFromString(matchShortOffset,input)))return this}else Math.abs(input)<16&&!keepMinutes&&(input*=60);return!this._isUTC&&keepLocalTime&&(localAdjust=getDateOffset(this)),this._offset=input,this._isUTC=!0,null!=localAdjust&&this.add(localAdjust,"m"),offset!==input&&(!keepLocalTime||this._changeInProgress?addSubtract(this,createDuration(input-offset,"m"),1,!1):this._changeInProgress||(this._changeInProgress=!0,hooks.updateOffset(this,!0),this._changeInProgress=null)),this}return this._isUTC?offset:getDateOffset(this)}function getSetZone(input,keepLocalTime){return null!=input?("string"!=typeof input&&(input=-input),this.utcOffset(input,keepLocalTime),this):-this.utcOffset()}function setOffsetToUTC(keepLocalTime){return this.utcOffset(0,keepLocalTime)}function setOffsetToLocal(keepLocalTime){return this._isUTC&&(this.utcOffset(0,keepLocalTime),this._isUTC=!1,keepLocalTime&&this.subtract(getDateOffset(this),"m")),this}function setOffsetToParsedOffset(){if(null!=this._tzm)this.utcOffset(this._tzm,!1,!0);else if("string"==typeof this._i){var tZone=offsetFromString(matchOffset,this._i);null!=tZone?this.utcOffset(tZone):this.utcOffset(0,!0)}return this}function hasAlignedHourOffset(input){return!!this.isValid()&&(input=input?createLocal(input).utcOffset():0,(this.utcOffset()-input)%60==0)}function isDaylightSavingTime(){return this.utcOffset()>this.clone().month(0).utcOffset()||this.utcOffset()>this.clone().month(5).utcOffset()}function isDaylightSavingTimeShifted(){if(!isUndefined(this._isDSTShifted))return this._isDSTShifted;var other,c={};return copyConfig(c,this),(c=prepareConfig(c))._a?(other=c._isUTC?createUTC(c._a):createLocal(c._a),this._isDSTShifted=this.isValid()&&compareArrays(c._a,other.toArray())>0):this._isDSTShifted=!1,this._isDSTShifted}function isLocal(){return!!this.isValid()&&!this._isUTC}function isUtcOffset(){return!!this.isValid()&&this._isUTC}function isUtc(){return!!this.isValid()&&this._isUTC&&0===this._offset}hooks.updateOffset=function(){};var aspNetRegex=/^(-|\+)?(?:(\d*)[. ])?(\d+):(\d+)(?::(\d+)(\.\d*)?)?$/,isoRegex=/^(-|\+)?P(?:([-+]?[0-9,.]*)Y)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)W)?(?:([-+]?[0-9,.]*)D)?(?:T(?:([-+]?[0-9,.]*)H)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)S)?)?$/;function createDuration(input,key){var sign,ret,diffRes,duration=input,match=null;return isDuration(input)?duration={ms:input._milliseconds,d:input._days,M:input._months}:isNumber(input)||!isNaN(+input)?(duration={},key?duration[key]=+input:duration.milliseconds=+input):(match=aspNetRegex.exec(input))?(sign="-"===match[1]?-1:1,duration={y:0,d:toInt(match[DATE])*sign,h:toInt(match[HOUR])*sign,m:toInt(match[MINUTE])*sign,s:toInt(match[SECOND])*sign,ms:toInt(absRound(1e3*match[MILLISECOND]))*sign}):(match=isoRegex.exec(input))?(sign="-"===match[1]?-1:1,duration={y:parseIso(match[2],sign),M:parseIso(match[3],sign),w:parseIso(match[4],sign),d:parseIso(match[5],sign),h:parseIso(match[6],sign),m:parseIso(match[7],sign),s:parseIso(match[8],sign)}):null==duration?duration={}:"object"==typeof duration&&("from"in duration||"to"in duration)&&(diffRes=momentsDifference(createLocal(duration.from),createLocal(duration.to)),(duration={}).ms=diffRes.milliseconds,duration.M=diffRes.months),ret=new Duration(duration),isDuration(input)&&hasOwnProp(input,"_locale")&&(ret._locale=input._locale),isDuration(input)&&hasOwnProp(input,"_isValid")&&(ret._isValid=input._isValid),ret}function parseIso(inp,sign){var res=inp&&parseFloat(inp.replace(",","."));return(isNaN(res)?0:res)*sign}function positiveMomentsDifference(base,other){var res={};return res.months=other.month()-base.month()+12*(other.year()-base.year()),base.clone().add(res.months,"M").isAfter(other)&&--res.months,res.milliseconds=+other-+base.clone().add(res.months,"M"),res}function momentsDifference(base,other){var res;return base.isValid()&&other.isValid()?(other=cloneWithOffset(other,base),base.isBefore(other)?res=positiveMomentsDifference(base,other):((res=positiveMomentsDifference(other,base)).milliseconds=-res.milliseconds,res.months=-res.months),res):{milliseconds:0,months:0}}function createAdder(direction,name){return function(val,period){var tmp;return null===period||isNaN(+period)||(deprecateSimple(name,"moment()."+name+"(period, number) is deprecated. Please use moment()."+name+"(number, period). See http://momentjs.com/guides/#/warnings/add-inverted-param/ for more info."),tmp=val,val=period,period=tmp),addSubtract(this,createDuration(val,period),direction),this}}function addSubtract(mom,duration,isAdding,updateOffset){var milliseconds=duration._milliseconds,days=absRound(duration._days),months=absRound(duration._months);mom.isValid()&&(updateOffset=null==updateOffset||updateOffset,months&&setMonth(mom,get(mom,"Month")+months*isAdding),days&&set$1(mom,"Date",get(mom,"Date")+days*isAdding),milliseconds&&mom._d.setTime(mom._d.valueOf()+milliseconds*isAdding),updateOffset&&hooks.updateOffset(mom,days||months))}createDuration.fn=Duration.prototype,createDuration.invalid=createInvalid$1;var add=createAdder(1,"add"),subtract=createAdder(-1,"subtract");function isString(input){return"string"==typeof input||input instanceof String}function isMomentInput(input){return isMoment(input)||isDate(input)||isString(input)||isNumber(input)||isNumberOrStringArray(input)||isMomentInputObject(input)||null==input}function isMomentInputObject(input){var i,property,objectTest=isObject(input)&&!isObjectEmpty(input),propertyTest=!1,properties=["years","year","y","months","month","M","days","day","d","dates","date","D","hours","hour","h","minutes","minute","m","seconds","second","s","milliseconds","millisecond","ms"],propertyLen=properties.length;for(i=0;i<propertyLen;i+=1)property=properties[i],propertyTest=propertyTest||hasOwnProp(input,property);return objectTest&&propertyTest}function isNumberOrStringArray(input){var arrayTest=isArray(input),dataTypeTest=!1;return arrayTest&&(dataTypeTest=0===input.filter((function(item){return!isNumber(item)&&isString(input)})).length),arrayTest&&dataTypeTest}function isCalendarSpec(input){var i,property,objectTest=isObject(input)&&!isObjectEmpty(input),propertyTest=!1,properties=["sameDay","nextDay","lastDay","nextWeek","lastWeek","sameElse"];for(i=0;i<properties.length;i+=1)property=properties[i],propertyTest=propertyTest||hasOwnProp(input,property);return objectTest&&propertyTest}function getCalendarFormat(myMoment,now){var diff=myMoment.diff(now,"days",!0);return diff<-6?"sameElse":diff<-1?"lastWeek":diff<0?"lastDay":diff<1?"sameDay":diff<2?"nextDay":diff<7?"nextWeek":"sameElse"}function calendar$1(time,formats){1===arguments.length&&(arguments[0]?isMomentInput(arguments[0])?(time=arguments[0],formats=void 0):isCalendarSpec(arguments[0])&&(formats=arguments[0],time=void 0):(time=void 0,formats=void 0));var now=time||createLocal(),sod=cloneWithOffset(now,this).startOf("day"),format=hooks.calendarFormat(this,sod)||"sameElse",output=formats&&(isFunction(formats[format])?formats[format].call(this,now):formats[format]);return this.format(output||this.localeData().calendar(format,this,createLocal(now)))}function clone(){return new Moment(this)}function isAfter(input,units){var localInput=isMoment(input)?input:createLocal(input);return!(!this.isValid()||!localInput.isValid())&&("millisecond"===(units=normalizeUnits(units)||"millisecond")?this.valueOf()>localInput.valueOf():localInput.valueOf()<this.clone().startOf(units).valueOf())}function isBefore(input,units){var localInput=isMoment(input)?input:createLocal(input);return!(!this.isValid()||!localInput.isValid())&&("millisecond"===(units=normalizeUnits(units)||"millisecond")?this.valueOf()<localInput.valueOf():this.clone().endOf(units).valueOf()<localInput.valueOf())}function isBetween(from,to,units,inclusivity){var localFrom=isMoment(from)?from:createLocal(from),localTo=isMoment(to)?to:createLocal(to);return!!(this.isValid()&&localFrom.isValid()&&localTo.isValid())&&("("===(inclusivity=inclusivity||"()")[0]?this.isAfter(localFrom,units):!this.isBefore(localFrom,units))&&(")"===inclusivity[1]?this.isBefore(localTo,units):!this.isAfter(localTo,units))}function isSame(input,units){var inputMs,localInput=isMoment(input)?input:createLocal(input);return!(!this.isValid()||!localInput.isValid())&&("millisecond"===(units=normalizeUnits(units)||"millisecond")?this.valueOf()===localInput.valueOf():(inputMs=localInput.valueOf(),this.clone().startOf(units).valueOf()<=inputMs&&inputMs<=this.clone().endOf(units).valueOf()))}function isSameOrAfter(input,units){return this.isSame(input,units)||this.isAfter(input,units)}function isSameOrBefore(input,units){return this.isSame(input,units)||this.isBefore(input,units)}function diff(input,units,asFloat){var that,zoneDelta,output;if(!this.isValid())return NaN;if(!(that=cloneWithOffset(input,this)).isValid())return NaN;switch(zoneDelta=6e4*(that.utcOffset()-this.utcOffset()),units=normalizeUnits(units)){case"year":output=monthDiff(this,that)/12;break;case"month":output=monthDiff(this,that);break;case"quarter":output=monthDiff(this,that)/3;break;case"second":output=(this-that)/1e3;break;case"minute":output=(this-that)/6e4;break;case"hour":output=(this-that)/36e5;break;case"day":output=(this-that-zoneDelta)/864e5;break;case"week":output=(this-that-zoneDelta)/6048e5;break;default:output=this-that}return asFloat?output:absFloor(output)}function monthDiff(a,b){if(a.date()<b.date())return-monthDiff(b,a);var wholeMonthDiff=12*(b.year()-a.year())+(b.month()-a.month()),anchor=a.clone().add(wholeMonthDiff,"months");return-(wholeMonthDiff+(b-anchor<0?(b-anchor)/(anchor-a.clone().add(wholeMonthDiff-1,"months")):(b-anchor)/(a.clone().add(wholeMonthDiff+1,"months")-anchor)))||0}function toString(){return this.clone().locale("en").format("ddd MMM DD YYYY HH:mm:ss [GMT]ZZ")}function toISOString(keepOffset){if(!this.isValid())return null;var utc=!0!==keepOffset,m=utc?this.clone().utc():this;return m.year()<0||m.year()>9999?formatMoment(m,utc?"YYYYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYYYY-MM-DD[T]HH:mm:ss.SSSZ"):isFunction(Date.prototype.toISOString)?utc?this.toDate().toISOString():new Date(this.valueOf()+60*this.utcOffset()*1e3).toISOString().replace("Z",formatMoment(m,"Z")):formatMoment(m,utc?"YYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYY-MM-DD[T]HH:mm:ss.SSSZ")}function inspect(){if(!this.isValid())return"moment.invalid(/* "+this._i+" */)";var prefix,year,datetime,suffix,func="moment",zone="";return this.isLocal()||(func=0===this.utcOffset()?"moment.utc":"moment.parseZone",zone="Z"),prefix="["+func+'("]',year=0<=this.year()&&this.year()<=9999?"YYYY":"YYYYYY",datetime="-MM-DD[T]HH:mm:ss.SSS",suffix=zone+'[")]',this.format(prefix+year+datetime+suffix)}function format(inputString){inputString||(inputString=this.isUtc()?hooks.defaultFormatUtc:hooks.defaultFormat);var output=formatMoment(this,inputString);return this.localeData().postformat(output)}function from(time,withoutSuffix){return this.isValid()&&(isMoment(time)&&time.isValid()||createLocal(time).isValid())?createDuration({to:this,from:time}).locale(this.locale()).humanize(!withoutSuffix):this.localeData().invalidDate()}function fromNow(withoutSuffix){return this.from(createLocal(),withoutSuffix)}function to(time,withoutSuffix){return this.isValid()&&(isMoment(time)&&time.isValid()||createLocal(time).isValid())?createDuration({from:this,to:time}).locale(this.locale()).humanize(!withoutSuffix):this.localeData().invalidDate()}function toNow(withoutSuffix){return this.to(createLocal(),withoutSuffix)}function locale(key){var newLocaleData;return void 0===key?this._locale._abbr:(null!=(newLocaleData=getLocale(key))&&(this._locale=newLocaleData),this)}hooks.defaultFormat="YYYY-MM-DDTHH:mm:ssZ",hooks.defaultFormatUtc="YYYY-MM-DDTHH:mm:ss[Z]";var lang=deprecate("moment().lang() is deprecated. Instead, use moment().localeData() to get the language configuration. Use moment().locale() to change languages.",(function(key){return void 0===key?this.localeData():this.locale(key)}));function localeData(){return this._locale}var MS_PER_SECOND=1e3,MS_PER_MINUTE=60*MS_PER_SECOND,MS_PER_HOUR=60*MS_PER_MINUTE,MS_PER_400_YEARS=3506328*MS_PER_HOUR;function mod$1(dividend,divisor){return(dividend%divisor+divisor)%divisor}function localStartOfDate(y,m,d){return y<100&&y>=0?new Date(y+400,m,d)-MS_PER_400_YEARS:new Date(y,m,d).valueOf()}function utcStartOfDate(y,m,d){return y<100&&y>=0?Date.UTC(y+400,m,d)-MS_PER_400_YEARS:Date.UTC(y,m,d)}function startOf(units){var time,startOfDate;if(void 0===(units=normalizeUnits(units))||"millisecond"===units||!this.isValid())return this;switch(startOfDate=this._isUTC?utcStartOfDate:localStartOfDate,units){case"year":time=startOfDate(this.year(),0,1);break;case"quarter":time=startOfDate(this.year(),this.month()-this.month()%3,1);break;case"month":time=startOfDate(this.year(),this.month(),1);break;case"week":time=startOfDate(this.year(),this.month(),this.date()-this.weekday());break;case"isoWeek":time=startOfDate(this.year(),this.month(),this.date()-(this.isoWeekday()-1));break;case"day":case"date":time=startOfDate(this.year(),this.month(),this.date());break;case"hour":time=this._d.valueOf(),time-=mod$1(time+(this._isUTC?0:this.utcOffset()*MS_PER_MINUTE),MS_PER_HOUR);break;case"minute":time=this._d.valueOf(),time-=mod$1(time,MS_PER_MINUTE);break;case"second":time=this._d.valueOf(),time-=mod$1(time,MS_PER_SECOND)}return this._d.setTime(time),hooks.updateOffset(this,!0),this}function endOf(units){var time,startOfDate;if(void 0===(units=normalizeUnits(units))||"millisecond"===units||!this.isValid())return this;switch(startOfDate=this._isUTC?utcStartOfDate:localStartOfDate,units){case"year":time=startOfDate(this.year()+1,0,1)-1;break;case"quarter":time=startOfDate(this.year(),this.month()-this.month()%3+3,1)-1;break;case"month":time=startOfDate(this.year(),this.month()+1,1)-1;break;case"week":time=startOfDate(this.year(),this.month(),this.date()-this.weekday()+7)-1;break;case"isoWeek":time=startOfDate(this.year(),this.month(),this.date()-(this.isoWeekday()-1)+7)-1;break;case"day":case"date":time=startOfDate(this.year(),this.month(),this.date()+1)-1;break;case"hour":time=this._d.valueOf(),time+=MS_PER_HOUR-mod$1(time+(this._isUTC?0:this.utcOffset()*MS_PER_MINUTE),MS_PER_HOUR)-1;break;case"minute":time=this._d.valueOf(),time+=MS_PER_MINUTE-mod$1(time,MS_PER_MINUTE)-1;break;case"second":time=this._d.valueOf(),time+=MS_PER_SECOND-mod$1(time,MS_PER_SECOND)-1}return this._d.setTime(time),hooks.updateOffset(this,!0),this}function valueOf(){return this._d.valueOf()-6e4*(this._offset||0)}function unix(){return Math.floor(this.valueOf()/1e3)}function toDate(){return new Date(this.valueOf())}function toArray(){var m=this;return[m.year(),m.month(),m.date(),m.hour(),m.minute(),m.second(),m.millisecond()]}function toObject(){var m=this;return{years:m.year(),months:m.month(),date:m.date(),hours:m.hours(),minutes:m.minutes(),seconds:m.seconds(),milliseconds:m.milliseconds()}}function toJSON(){return this.isValid()?this.toISOString():null}function isValid$2(){return isValid(this)}function parsingFlags(){return extend({},getParsingFlags(this))}function invalidAt(){return getParsingFlags(this).overflow}function creationData(){return{input:this._i,format:this._f,locale:this._locale,isUTC:this._isUTC,strict:this._strict}}function localeEras(m,format){var i,l,date,eras=this._eras||getLocale("en")._eras;for(i=0,l=eras.length;i<l;++i)switch("string"==typeof eras[i].since&&(date=hooks(eras[i].since).startOf("day"),eras[i].since=date.valueOf()),typeof eras[i].until){case"undefined":eras[i].until=1/0;break;case"string":date=hooks(eras[i].until).startOf("day").valueOf(),eras[i].until=date.valueOf()}return eras}function localeErasParse(eraName,format,strict){var i,l,name,abbr,narrow,eras=this.eras();for(eraName=eraName.toUpperCase(),i=0,l=eras.length;i<l;++i)if(name=eras[i].name.toUpperCase(),abbr=eras[i].abbr.toUpperCase(),narrow=eras[i].narrow.toUpperCase(),strict)switch(format){case"N":case"NN":case"NNN":if(abbr===eraName)return eras[i];break;case"NNNN":if(name===eraName)return eras[i];break;case"NNNNN":if(narrow===eraName)return eras[i]}else if([name,abbr,narrow].indexOf(eraName)>=0)return eras[i]}function localeErasConvertYear(era,year){var dir=era.since<=era.until?1:-1;return void 0===year?hooks(era.since).year():hooks(era.since).year()+(year-era.offset)*dir}function getEraName(){var i,l,val,eras=this.localeData().eras();for(i=0,l=eras.length;i<l;++i){if(val=this.clone().startOf("day").valueOf(),eras[i].since<=val&&val<=eras[i].until)return eras[i].name;if(eras[i].until<=val&&val<=eras[i].since)return eras[i].name}return""}function getEraNarrow(){var i,l,val,eras=this.localeData().eras();for(i=0,l=eras.length;i<l;++i){if(val=this.clone().startOf("day").valueOf(),eras[i].since<=val&&val<=eras[i].until)return eras[i].narrow;if(eras[i].until<=val&&val<=eras[i].since)return eras[i].narrow}return""}function getEraAbbr(){var i,l,val,eras=this.localeData().eras();for(i=0,l=eras.length;i<l;++i){if(val=this.clone().startOf("day").valueOf(),eras[i].since<=val&&val<=eras[i].until)return eras[i].abbr;if(eras[i].until<=val&&val<=eras[i].since)return eras[i].abbr}return""}function getEraYear(){var i,l,dir,val,eras=this.localeData().eras();for(i=0,l=eras.length;i<l;++i)if(dir=eras[i].since<=eras[i].until?1:-1,val=this.clone().startOf("day").valueOf(),eras[i].since<=val&&val<=eras[i].until||eras[i].until<=val&&val<=eras[i].since)return(this.year()-hooks(eras[i].since).year())*dir+eras[i].offset;return this.year()}function erasNameRegex(isStrict){return hasOwnProp(this,"_erasNameRegex")||computeErasParse.call(this),isStrict?this._erasNameRegex:this._erasRegex}function erasAbbrRegex(isStrict){return hasOwnProp(this,"_erasAbbrRegex")||computeErasParse.call(this),isStrict?this._erasAbbrRegex:this._erasRegex}function erasNarrowRegex(isStrict){return hasOwnProp(this,"_erasNarrowRegex")||computeErasParse.call(this),isStrict?this._erasNarrowRegex:this._erasRegex}function matchEraAbbr(isStrict,locale){return locale.erasAbbrRegex(isStrict)}function matchEraName(isStrict,locale){return locale.erasNameRegex(isStrict)}function matchEraNarrow(isStrict,locale){return locale.erasNarrowRegex(isStrict)}function matchEraYearOrdinal(isStrict,locale){return locale._eraYearOrdinalRegex||matchUnsigned}function computeErasParse(){var i,l,erasName,erasAbbr,erasNarrow,abbrPieces=[],namePieces=[],narrowPieces=[],mixedPieces=[],eras=this.eras();for(i=0,l=eras.length;i<l;++i)erasName=regexEscape(eras[i].name),erasAbbr=regexEscape(eras[i].abbr),erasNarrow=regexEscape(eras[i].narrow),namePieces.push(erasName),abbrPieces.push(erasAbbr),narrowPieces.push(erasNarrow),mixedPieces.push(erasName),mixedPieces.push(erasAbbr),mixedPieces.push(erasNarrow);this._erasRegex=new RegExp("^("+mixedPieces.join("|")+")","i"),this._erasNameRegex=new RegExp("^("+namePieces.join("|")+")","i"),this._erasAbbrRegex=new RegExp("^("+abbrPieces.join("|")+")","i"),this._erasNarrowRegex=new RegExp("^("+narrowPieces.join("|")+")","i")}function addWeekYearFormatToken(token,getter){addFormatToken(0,[token,token.length],0,getter)}function getSetWeekYear(input){return getSetWeekYearHelper.call(this,input,this.week(),this.weekday()+this.localeData()._week.dow,this.localeData()._week.dow,this.localeData()._week.doy)}function getSetISOWeekYear(input){return getSetWeekYearHelper.call(this,input,this.isoWeek(),this.isoWeekday(),1,4)}function getISOWeeksInYear(){return weeksInYear(this.year(),1,4)}function getISOWeeksInISOWeekYear(){return weeksInYear(this.isoWeekYear(),1,4)}function getWeeksInYear(){var weekInfo=this.localeData()._week;return weeksInYear(this.year(),weekInfo.dow,weekInfo.doy)}function getWeeksInWeekYear(){var weekInfo=this.localeData()._week;return weeksInYear(this.weekYear(),weekInfo.dow,weekInfo.doy)}function getSetWeekYearHelper(input,week,weekday,dow,doy){var weeksTarget;return null==input?weekOfYear(this,dow,doy).year:(week>(weeksTarget=weeksInYear(input,dow,doy))&&(week=weeksTarget),setWeekAll.call(this,input,week,weekday,dow,doy))}function setWeekAll(weekYear,week,weekday,dow,doy){var dayOfYearData=dayOfYearFromWeeks(weekYear,week,weekday,dow,doy),date=createUTCDate(dayOfYearData.year,0,dayOfYearData.dayOfYear);return this.year(date.getUTCFullYear()),this.month(date.getUTCMonth()),this.date(date.getUTCDate()),this}function getSetQuarter(input){return null==input?Math.ceil((this.month()+1)/3):this.month(3*(input-1)+this.month()%3)}addFormatToken("N",0,0,"eraAbbr"),addFormatToken("NN",0,0,"eraAbbr"),addFormatToken("NNN",0,0,"eraAbbr"),addFormatToken("NNNN",0,0,"eraName"),addFormatToken("NNNNN",0,0,"eraNarrow"),addFormatToken("y",["y",1],"yo","eraYear"),addFormatToken("y",["yy",2],0,"eraYear"),addFormatToken("y",["yyy",3],0,"eraYear"),addFormatToken("y",["yyyy",4],0,"eraYear"),addRegexToken("N",matchEraAbbr),addRegexToken("NN",matchEraAbbr),addRegexToken("NNN",matchEraAbbr),addRegexToken("NNNN",matchEraName),addRegexToken("NNNNN",matchEraNarrow),addParseToken(["N","NN","NNN","NNNN","NNNNN"],(function(input,array,config,token){var era=config._locale.erasParse(input,token,config._strict);era?getParsingFlags(config).era=era:getParsingFlags(config).invalidEra=input})),addRegexToken("y",matchUnsigned),addRegexToken("yy",matchUnsigned),addRegexToken("yyy",matchUnsigned),addRegexToken("yyyy",matchUnsigned),addRegexToken("yo",matchEraYearOrdinal),addParseToken(["y","yy","yyy","yyyy"],YEAR),addParseToken(["yo"],(function(input,array,config,token){var match;config._locale._eraYearOrdinalRegex&&(match=input.match(config._locale._eraYearOrdinalRegex)),config._locale.eraYearOrdinalParse?array[YEAR]=config._locale.eraYearOrdinalParse(input,match):array[YEAR]=parseInt(input,10)})),addFormatToken(0,["gg",2],0,(function(){return this.weekYear()%100})),addFormatToken(0,["GG",2],0,(function(){return this.isoWeekYear()%100})),addWeekYearFormatToken("gggg","weekYear"),addWeekYearFormatToken("ggggg","weekYear"),addWeekYearFormatToken("GGGG","isoWeekYear"),addWeekYearFormatToken("GGGGG","isoWeekYear"),addRegexToken("G",matchSigned),addRegexToken("g",matchSigned),addRegexToken("GG",match1to2,match2),addRegexToken("gg",match1to2,match2),addRegexToken("GGGG",match1to4,match4),addRegexToken("gggg",match1to4,match4),addRegexToken("GGGGG",match1to6,match6),addRegexToken("ggggg",match1to6,match6),addWeekParseToken(["gggg","ggggg","GGGG","GGGGG"],(function(input,week,config,token){week[token.substr(0,2)]=toInt(input)})),addWeekParseToken(["gg","GG"],(function(input,week,config,token){week[token]=hooks.parseTwoDigitYear(input)})),addFormatToken("Q",0,"Qo","quarter"),addRegexToken("Q",match1),addParseToken("Q",(function(input,array){array[MONTH]=3*(toInt(input)-1)})),addFormatToken("D",["DD",2],"Do","date"),addRegexToken("D",match1to2,match1to2NoLeadingZero),addRegexToken("DD",match1to2,match2),addRegexToken("Do",(function(isStrict,locale){return isStrict?locale._dayOfMonthOrdinalParse||locale._ordinalParse:locale._dayOfMonthOrdinalParseLenient})),addParseToken(["D","DD"],DATE),addParseToken("Do",(function(input,array){array[DATE]=toInt(input.match(match1to2)[0])}));var getSetDayOfMonth=makeGetSet("Date",!0);function getSetDayOfYear(input){var dayOfYear=Math.round((this.clone().startOf("day")-this.clone().startOf("year"))/864e5)+1;return null==input?dayOfYear:this.add(input-dayOfYear,"d")}addFormatToken("DDD",["DDDD",3],"DDDo","dayOfYear"),addRegexToken("DDD",match1to3),addRegexToken("DDDD",match3),addParseToken(["DDD","DDDD"],(function(input,array,config){config._dayOfYear=toInt(input)})),addFormatToken("m",["mm",2],0,"minute"),addRegexToken("m",match1to2,match1to2HasZero),addRegexToken("mm",match1to2,match2),addParseToken(["m","mm"],MINUTE);var getSetMinute=makeGetSet("Minutes",!1);addFormatToken("s",["ss",2],0,"second"),addRegexToken("s",match1to2,match1to2HasZero),addRegexToken("ss",match1to2,match2),addParseToken(["s","ss"],SECOND);var token,getSetMillisecond,getSetSecond=makeGetSet("Seconds",!1);for(addFormatToken("S",0,0,(function(){return~~(this.millisecond()/100)})),addFormatToken(0,["SS",2],0,(function(){return~~(this.millisecond()/10)})),addFormatToken(0,["SSS",3],0,"millisecond"),addFormatToken(0,["SSSS",4],0,(function(){return 10*this.millisecond()})),addFormatToken(0,["SSSSS",5],0,(function(){return 100*this.millisecond()})),addFormatToken(0,["SSSSSS",6],0,(function(){return 1e3*this.millisecond()})),addFormatToken(0,["SSSSSSS",7],0,(function(){return 1e4*this.millisecond()})),addFormatToken(0,["SSSSSSSS",8],0,(function(){return 1e5*this.millisecond()})),addFormatToken(0,["SSSSSSSSS",9],0,(function(){return 1e6*this.millisecond()})),addRegexToken("S",match1to3,match1),addRegexToken("SS",match1to3,match2),addRegexToken("SSS",match1to3,match3),token="SSSS";token.length<=9;token+="S")addRegexToken(token,matchUnsigned);function parseMs(input,array){array[MILLISECOND]=toInt(1e3*("0."+input))}for(token="S";token.length<=9;token+="S")addParseToken(token,parseMs);function getZoneAbbr(){return this._isUTC?"UTC":""}function getZoneName(){return this._isUTC?"Coordinated Universal Time":""}getSetMillisecond=makeGetSet("Milliseconds",!1),addFormatToken("z",0,0,"zoneAbbr"),addFormatToken("zz",0,0,"zoneName");var proto=Moment.prototype;function createUnix(input){return createLocal(1e3*input)}function createInZone(){return createLocal.apply(null,arguments).parseZone()}function preParsePostFormat(string){return string}proto.add=add,proto.calendar=calendar$1,proto.clone=clone,proto.diff=diff,proto.endOf=endOf,proto.format=format,proto.from=from,proto.fromNow=fromNow,proto.to=to,proto.toNow=toNow,proto.get=stringGet,proto.invalidAt=invalidAt,proto.isAfter=isAfter,proto.isBefore=isBefore,proto.isBetween=isBetween,proto.isSame=isSame,proto.isSameOrAfter=isSameOrAfter,proto.isSameOrBefore=isSameOrBefore,proto.isValid=isValid$2,proto.lang=lang,proto.locale=locale,proto.localeData=localeData,proto.max=prototypeMax,proto.min=prototypeMin,proto.parsingFlags=parsingFlags,proto.set=stringSet,proto.startOf=startOf,proto.subtract=subtract,proto.toArray=toArray,proto.toObject=toObject,proto.toDate=toDate,proto.toISOString=toISOString,proto.inspect=inspect,"undefined"!=typeof Symbol&&null!=Symbol.for&&(proto[Symbol.for("nodejs.util.inspect.custom")]=function(){return"Moment<"+this.format()+">"}),proto.toJSON=toJSON,proto.toString=toString,proto.unix=unix,proto.valueOf=valueOf,proto.creationData=creationData,proto.eraName=getEraName,proto.eraNarrow=getEraNarrow,proto.eraAbbr=getEraAbbr,proto.eraYear=getEraYear,proto.year=getSetYear,proto.isLeapYear=getIsLeapYear,proto.weekYear=getSetWeekYear,proto.isoWeekYear=getSetISOWeekYear,proto.quarter=proto.quarters=getSetQuarter,proto.month=getSetMonth,proto.daysInMonth=getDaysInMonth,proto.week=proto.weeks=getSetWeek,proto.isoWeek=proto.isoWeeks=getSetISOWeek,proto.weeksInYear=getWeeksInYear,proto.weeksInWeekYear=getWeeksInWeekYear,proto.isoWeeksInYear=getISOWeeksInYear,proto.isoWeeksInISOWeekYear=getISOWeeksInISOWeekYear,proto.date=getSetDayOfMonth,proto.day=proto.days=getSetDayOfWeek,proto.weekday=getSetLocaleDayOfWeek,proto.isoWeekday=getSetISODayOfWeek,proto.dayOfYear=getSetDayOfYear,proto.hour=proto.hours=getSetHour,proto.minute=proto.minutes=getSetMinute,proto.second=proto.seconds=getSetSecond,proto.millisecond=proto.milliseconds=getSetMillisecond,proto.utcOffset=getSetOffset,proto.utc=setOffsetToUTC,proto.local=setOffsetToLocal,proto.parseZone=setOffsetToParsedOffset,proto.hasAlignedHourOffset=hasAlignedHourOffset,proto.isDST=isDaylightSavingTime,proto.isLocal=isLocal,proto.isUtcOffset=isUtcOffset,proto.isUtc=isUtc,proto.isUTC=isUtc,proto.zoneAbbr=getZoneAbbr,proto.zoneName=getZoneName,proto.dates=deprecate("dates accessor is deprecated. Use date instead.",getSetDayOfMonth),proto.months=deprecate("months accessor is deprecated. Use month instead",getSetMonth),proto.years=deprecate("years accessor is deprecated. Use year instead",getSetYear),proto.zone=deprecate("moment().zone is deprecated, use moment().utcOffset instead. http://momentjs.com/guides/#/warnings/zone/",getSetZone),proto.isDSTShifted=deprecate("isDSTShifted is deprecated. See http://momentjs.com/guides/#/warnings/dst-shifted/ for more information",isDaylightSavingTimeShifted);var proto$1=Locale.prototype;function get$1(format,index,field,setter){var locale=getLocale(),utc=createUTC().set(setter,index);return locale[field](utc,format)}function listMonthsImpl(format,index,field){if(isNumber(format)&&(index=format,format=void 0),format=format||"",null!=index)return get$1(format,index,field,"month");var i,out=[];for(i=0;i<12;i++)out[i]=get$1(format,i,field,"month");return out}function listWeekdaysImpl(localeSorted,format,index,field){"boolean"==typeof localeSorted?(isNumber(format)&&(index=format,format=void 0),format=format||""):(index=format=localeSorted,localeSorted=!1,isNumber(format)&&(index=format,format=void 0),format=format||"");var i,locale=getLocale(),shift=localeSorted?locale._week.dow:0,out=[];if(null!=index)return get$1(format,(index+shift)%7,field,"day");for(i=0;i<7;i++)out[i]=get$1(format,(i+shift)%7,field,"day");return out}function listMonths(format,index){return listMonthsImpl(format,index,"months")}function listMonthsShort(format,index){return listMonthsImpl(format,index,"monthsShort")}function listWeekdays(localeSorted,format,index){return listWeekdaysImpl(localeSorted,format,index,"weekdays")}function listWeekdaysShort(localeSorted,format,index){return listWeekdaysImpl(localeSorted,format,index,"weekdaysShort")}function listWeekdaysMin(localeSorted,format,index){return listWeekdaysImpl(localeSorted,format,index,"weekdaysMin")}proto$1.calendar=calendar,proto$1.longDateFormat=longDateFormat,proto$1.invalidDate=invalidDate,proto$1.ordinal=ordinal,proto$1.preparse=preParsePostFormat,proto$1.postformat=preParsePostFormat,proto$1.relativeTime=relativeTime,proto$1.pastFuture=pastFuture,proto$1.set=set,proto$1.eras=localeEras,proto$1.erasParse=localeErasParse,proto$1.erasConvertYear=localeErasConvertYear,proto$1.erasAbbrRegex=erasAbbrRegex,proto$1.erasNameRegex=erasNameRegex,proto$1.erasNarrowRegex=erasNarrowRegex,proto$1.months=localeMonths,proto$1.monthsShort=localeMonthsShort,proto$1.monthsParse=localeMonthsParse,proto$1.monthsRegex=monthsRegex,proto$1.monthsShortRegex=monthsShortRegex,proto$1.week=localeWeek,proto$1.firstDayOfYear=localeFirstDayOfYear,proto$1.firstDayOfWeek=localeFirstDayOfWeek,proto$1.weekdays=localeWeekdays,proto$1.weekdaysMin=localeWeekdaysMin,proto$1.weekdaysShort=localeWeekdaysShort,proto$1.weekdaysParse=localeWeekdaysParse,proto$1.weekdaysRegex=weekdaysRegex,proto$1.weekdaysShortRegex=weekdaysShortRegex,proto$1.weekdaysMinRegex=weekdaysMinRegex,proto$1.isPM=localeIsPM,proto$1.meridiem=localeMeridiem,getSetGlobalLocale("en",{eras:[{since:"0001-01-01",until:1/0,offset:1,name:"Anno Domini",narrow:"AD",abbr:"AD"},{since:"0000-12-31",until:-1/0,offset:1,name:"Before Christ",narrow:"BC",abbr:"BC"}],dayOfMonthOrdinalParse:/\d{1,2}(th|st|nd|rd)/,ordinal:function(number){var b=number%10;return number+(1===toInt(number%100/10)?"th":1===b?"st":2===b?"nd":3===b?"rd":"th")}}),hooks.lang=deprecate("moment.lang is deprecated. Use moment.locale instead.",getSetGlobalLocale),hooks.langData=deprecate("moment.langData is deprecated. Use moment.localeData instead.",getLocale);var mathAbs=Math.abs;function abs(){var data=this._data;return this._milliseconds=mathAbs(this._milliseconds),this._days=mathAbs(this._days),this._months=mathAbs(this._months),data.milliseconds=mathAbs(data.milliseconds),data.seconds=mathAbs(data.seconds),data.minutes=mathAbs(data.minutes),data.hours=mathAbs(data.hours),data.months=mathAbs(data.months),data.years=mathAbs(data.years),this}function addSubtract$1(duration,input,value,direction){var other=createDuration(input,value);return duration._milliseconds+=direction*other._milliseconds,duration._days+=direction*other._days,duration._months+=direction*other._months,duration._bubble()}function add$1(input,value){return addSubtract$1(this,input,value,1)}function subtract$1(input,value){return addSubtract$1(this,input,value,-1)}function absCeil(number){return number<0?Math.floor(number):Math.ceil(number)}function bubble(){var seconds,minutes,hours,years,monthsFromDays,milliseconds=this._milliseconds,days=this._days,months=this._months,data=this._data;return milliseconds>=0&&days>=0&&months>=0||milliseconds<=0&&days<=0&&months<=0||(milliseconds+=864e5*absCeil(monthsToDays(months)+days),days=0,months=0),data.milliseconds=milliseconds%1e3,seconds=absFloor(milliseconds/1e3),data.seconds=seconds%60,minutes=absFloor(seconds/60),data.minutes=minutes%60,hours=absFloor(minutes/60),data.hours=hours%24,days+=absFloor(hours/24),months+=monthsFromDays=absFloor(daysToMonths(days)),days-=absCeil(monthsToDays(monthsFromDays)),years=absFloor(months/12),months%=12,data.days=days,data.months=months,data.years=years,this}function daysToMonths(days){return 4800*days/146097}function monthsToDays(months){return 146097*months/4800}function as(units){if(!this.isValid())return NaN;var days,months,milliseconds=this._milliseconds;if("month"===(units=normalizeUnits(units))||"quarter"===units||"year"===units)switch(days=this._days+milliseconds/864e5,months=this._months+daysToMonths(days),units){case"month":return months;case"quarter":return months/3;case"year":return months/12}else switch(days=this._days+Math.round(monthsToDays(this._months)),units){case"week":return days/7+milliseconds/6048e5;case"day":return days+milliseconds/864e5;case"hour":return 24*days+milliseconds/36e5;case"minute":return 1440*days+milliseconds/6e4;case"second":return 86400*days+milliseconds/1e3;case"millisecond":return Math.floor(864e5*days)+milliseconds;default:throw new Error("Unknown unit "+units)}}function makeAs(alias){return function(){return this.as(alias)}}var asMilliseconds=makeAs("ms"),asSeconds=makeAs("s"),asMinutes=makeAs("m"),asHours=makeAs("h"),asDays=makeAs("d"),asWeeks=makeAs("w"),asMonths=makeAs("M"),asQuarters=makeAs("Q"),asYears=makeAs("y"),valueOf$1=asMilliseconds;function clone$1(){return createDuration(this)}function get$2(units){return units=normalizeUnits(units),this.isValid()?this[units+"s"]():NaN}function makeGetter(name){return function(){return this.isValid()?this._data[name]:NaN}}var milliseconds=makeGetter("milliseconds"),seconds=makeGetter("seconds"),minutes=makeGetter("minutes"),hours=makeGetter("hours"),days=makeGetter("days"),months=makeGetter("months"),years=makeGetter("years");function weeks(){return absFloor(this.days()/7)}var round=Math.round,thresholds={ss:44,s:45,m:45,h:22,d:26,w:null,M:11};function substituteTimeAgo(string,number,withoutSuffix,isFuture,locale){return locale.relativeTime(number||1,!!withoutSuffix,string,isFuture)}function relativeTime$1(posNegDuration,withoutSuffix,thresholds,locale){var duration=createDuration(posNegDuration).abs(),seconds=round(duration.as("s")),minutes=round(duration.as("m")),hours=round(duration.as("h")),days=round(duration.as("d")),months=round(duration.as("M")),weeks=round(duration.as("w")),years=round(duration.as("y")),a=seconds<=thresholds.ss&&["s",seconds]||seconds<thresholds.s&&["ss",seconds]||minutes<=1&&["m"]||minutes<thresholds.m&&["mm",minutes]||hours<=1&&["h"]||hours<thresholds.h&&["hh",hours]||days<=1&&["d"]||days<thresholds.d&&["dd",days];return null!=thresholds.w&&(a=a||weeks<=1&&["w"]||weeks<thresholds.w&&["ww",weeks]),(a=a||months<=1&&["M"]||months<thresholds.M&&["MM",months]||years<=1&&["y"]||["yy",years])[2]=withoutSuffix,a[3]=+posNegDuration>0,a[4]=locale,substituteTimeAgo.apply(null,a)}function getSetRelativeTimeRounding(roundingFunction){return void 0===roundingFunction?round:"function"==typeof roundingFunction&&(round=roundingFunction,!0)}function getSetRelativeTimeThreshold(threshold,limit){return void 0!==thresholds[threshold]&&(void 0===limit?thresholds[threshold]:(thresholds[threshold]=limit,"s"===threshold&&(thresholds.ss=limit-1),!0))}function humanize(argWithSuffix,argThresholds){if(!this.isValid())return this.localeData().invalidDate();var locale,output,withSuffix=!1,th=thresholds;return"object"==typeof argWithSuffix&&(argThresholds=argWithSuffix,argWithSuffix=!1),"boolean"==typeof argWithSuffix&&(withSuffix=argWithSuffix),"object"==typeof argThresholds&&(th=Object.assign({},thresholds,argThresholds),null!=argThresholds.s&&null==argThresholds.ss&&(th.ss=argThresholds.s-1)),output=relativeTime$1(this,!withSuffix,th,locale=this.localeData()),withSuffix&&(output=locale.pastFuture(+this,output)),locale.postformat(output)}var abs$1=Math.abs;function sign(x){return(x>0)-(x<0)||+x}function toISOString$1(){if(!this.isValid())return this.localeData().invalidDate();var minutes,hours,years,s,totalSign,ymSign,daysSign,hmsSign,seconds=abs$1(this._milliseconds)/1e3,days=abs$1(this._days),months=abs$1(this._months),total=this.asSeconds();return total?(minutes=absFloor(seconds/60),hours=absFloor(minutes/60),seconds%=60,minutes%=60,years=absFloor(months/12),months%=12,s=seconds?seconds.toFixed(3).replace(/\.?0+$/,""):"",totalSign=total<0?"-":"",ymSign=sign(this._months)!==sign(total)?"-":"",daysSign=sign(this._days)!==sign(total)?"-":"",hmsSign=sign(this._milliseconds)!==sign(total)?"-":"",totalSign+"P"+(years?ymSign+years+"Y":"")+(months?ymSign+months+"M":"")+(days?daysSign+days+"D":"")+(hours||minutes||seconds?"T":"")+(hours?hmsSign+hours+"H":"")+(minutes?hmsSign+minutes+"M":"")+(seconds?hmsSign+s+"S":"")):"P0D"}var proto$2=Duration.prototype;return proto$2.isValid=isValid$1,proto$2.abs=abs,proto$2.add=add$1,proto$2.subtract=subtract$1,proto$2.as=as,proto$2.asMilliseconds=asMilliseconds,proto$2.asSeconds=asSeconds,proto$2.asMinutes=asMinutes,proto$2.asHours=asHours,proto$2.asDays=asDays,proto$2.asWeeks=asWeeks,proto$2.asMonths=asMonths,proto$2.asQuarters=asQuarters,proto$2.asYears=asYears,proto$2.valueOf=valueOf$1,proto$2._bubble=bubble,proto$2.clone=clone$1,proto$2.get=get$2,proto$2.milliseconds=milliseconds,proto$2.seconds=seconds,proto$2.minutes=minutes,proto$2.hours=hours,proto$2.days=days,proto$2.weeks=weeks,proto$2.months=months,proto$2.years=years,proto$2.humanize=humanize,proto$2.toISOString=toISOString$1,proto$2.toString=toISOString$1,proto$2.toJSON=toISOString$1,proto$2.locale=locale,proto$2.localeData=localeData,proto$2.toIsoString=deprecate("toIsoString() is deprecated. Please use toISOString() instead (notice the capitals)",toISOString$1),proto$2.lang=lang,addFormatToken("X",0,0,"unix"),addFormatToken("x",0,0,"valueOf"),addRegexToken("x",matchSigned),addRegexToken("X",matchTimestamp),addParseToken("X",(function(input,array,config){config._d=new Date(1e3*parseFloat(input))})),addParseToken("x",(function(input,array,config){config._d=new Date(toInt(input))})),hooks.version="2.30.1",setHookCallback(createLocal),hooks.fn=proto,hooks.min=min,hooks.max=max,hooks.now=now,hooks.utc=createUTC,hooks.unix=createUnix,hooks.months=listMonths,hooks.isDate=isDate,hooks.locale=getSetGlobalLocale,hooks.invalid=createInvalid,hooks.duration=createDuration,hooks.isMoment=isMoment,hooks.weekdays=listWeekdays,hooks.parseZone=createInZone,hooks.localeData=getLocale,hooks.isDuration=isDuration,hooks.monthsShort=listMonthsShort,hooks.weekdaysMin=listWeekdaysMin,hooks.defineLocale=defineLocale,hooks.updateLocale=updateLocale,hooks.locales=listLocales,hooks.weekdaysShort=listWeekdaysShort,hooks.normalizeUnits=normalizeUnits,hooks.relativeTimeRounding=getSetRelativeTimeRounding,hooks.relativeTimeThreshold=getSetRelativeTimeThreshold,hooks.calendarFormat=getCalendarFormat,hooks.prototype=proto,hooks.HTML5_FMT={DATETIME_LOCAL:"YYYY-MM-DDTHH:mm",DATETIME_LOCAL_SECONDS:"YYYY-MM-DDTHH:mm:ss",DATETIME_LOCAL_MS:"YYYY-MM-DDTHH:mm:ss.SSS",DATE:"YYYY-MM-DD",TIME:"HH:mm",TIME_SECONDS:"HH:mm:ss",TIME_MS:"HH:mm:ss.SSS",WEEK:"GGGG-[W]WW",MONTH:"YYYY-MM"},hooks}()},"./node_modules/react-leaflet/lib/MapContainer.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{"use strict";__webpack_require__.d(__webpack_exports__,{W:()=>MapContainer});var _react_leaflet_core__WEBPACK_IMPORTED_MODULE_2__=__webpack_require__("./node_modules/@react-leaflet/core/lib/context.js"),leaflet__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("./node_modules/leaflet/dist/leaflet-src.js"),react__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__("./node_modules/react/index.js");function _extends(){return _extends=Object.assign||function(target){for(var i=1;i<arguments.length;i++){var source=arguments[i];for(var key in source)Object.prototype.hasOwnProperty.call(source,key)&&(target[key]=source[key])}return target},_extends.apply(this,arguments)}function MapContainerComponent({bounds,boundsOptions,center,children,className,id,placeholder,style,whenReady,zoom,...options},forwardedRef){const[props]=(0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({className,id,style}),[context,setContext]=(0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);(0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(forwardedRef,(()=>context?.map??null),[context]);const mapRef=(0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((node=>{if(null!==node&&null===context){const map=new leaflet__WEBPACK_IMPORTED_MODULE_0__.Map(node,options);null!=center&&null!=zoom?map.setView(center,zoom):null!=bounds&&map.fitBounds(bounds,boundsOptions),null!=whenReady&&map.whenReady(whenReady),setContext((0,_react_leaflet_core__WEBPACK_IMPORTED_MODULE_2__.fB)(map))}}),[]);(0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)((()=>()=>{context?.map.remove()}),[context]);const contents=context?react__WEBPACK_IMPORTED_MODULE_1__.createElement(_react_leaflet_core__WEBPACK_IMPORTED_MODULE_2__.hL,{value:context},children):placeholder??null;return react__WEBPACK_IMPORTED_MODULE_1__.createElement("div",_extends({},props,{ref:mapRef}),contents)}const MapContainer=(0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(MapContainerComponent)},"./node_modules/react-leaflet/lib/Marker.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{"use strict";__webpack_require__.d(__webpack_exports__,{p:()=>Marker});var _react_leaflet_core__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__("./node_modules/@react-leaflet/core/lib/generic.js"),_react_leaflet_core__WEBPACK_IMPORTED_MODULE_2__=__webpack_require__("./node_modules/@react-leaflet/core/lib/element.js"),_react_leaflet_core__WEBPACK_IMPORTED_MODULE_3__=__webpack_require__("./node_modules/@react-leaflet/core/lib/context.js"),leaflet__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("./node_modules/leaflet/dist/leaflet-src.js");const Marker=(0,_react_leaflet_core__WEBPACK_IMPORTED_MODULE_1__.Nq)((function createMarker({position,...options},ctx){const marker=new leaflet__WEBPACK_IMPORTED_MODULE_0__.Marker(position,options);return(0,_react_leaflet_core__WEBPACK_IMPORTED_MODULE_2__.Q)(marker,(0,_react_leaflet_core__WEBPACK_IMPORTED_MODULE_3__.W4)(ctx,{overlayContainer:marker}))}),(function updateMarker(marker,props,prevProps){props.position!==prevProps.position&&marker.setLatLng(props.position),null!=props.icon&&props.icon!==prevProps.icon&&marker.setIcon(props.icon),null!=props.zIndexOffset&&props.zIndexOffset!==prevProps.zIndexOffset&&marker.setZIndexOffset(props.zIndexOffset),null!=props.opacity&&props.opacity!==prevProps.opacity&&marker.setOpacity(props.opacity),null!=marker.dragging&&props.draggable!==prevProps.draggable&&(!0===props.draggable?marker.dragging.enable():marker.dragging.disable())}))},"./node_modules/react-leaflet/lib/Popup.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{"use strict";__webpack_require__.d(__webpack_exports__,{z:()=>Popup});var _react_leaflet_core__WEBPACK_IMPORTED_MODULE_2__=__webpack_require__("./node_modules/@react-leaflet/core/lib/generic.js"),_react_leaflet_core__WEBPACK_IMPORTED_MODULE_3__=__webpack_require__("./node_modules/@react-leaflet/core/lib/element.js"),leaflet__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("./node_modules/leaflet/dist/leaflet-src.js"),react__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__("./node_modules/react/index.js");const Popup=(0,_react_leaflet_core__WEBPACK_IMPORTED_MODULE_2__.wk)((function createPopup(props,context){const popup=new leaflet__WEBPACK_IMPORTED_MODULE_0__.Popup(props,context.overlayContainer);return(0,_react_leaflet_core__WEBPACK_IMPORTED_MODULE_3__.Q)(popup,context)}),(function usePopupLifecycle(element,context,{position},setOpen){(0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)((function addPopup(){const{instance}=element;function onPopupOpen(event){event.popup===instance&&(instance.update(),setOpen(!0))}function onPopupClose(event){event.popup===instance&&setOpen(!1)}return context.map.on({popupopen:onPopupOpen,popupclose:onPopupClose}),null==context.overlayContainer?(null!=position&&instance.setLatLng(position),instance.openOn(context.map)):context.overlayContainer.bindPopup(instance),function removePopup(){context.map.off({popupopen:onPopupOpen,popupclose:onPopupClose}),context.overlayContainer?.unbindPopup(),context.map.removeLayer(instance)}}),[element,context,setOpen,position])}))},"./node_modules/react-leaflet/lib/TileLayer.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{"use strict";__webpack_require__.d(__webpack_exports__,{e:()=>TileLayer});var generic=__webpack_require__("./node_modules/@react-leaflet/core/lib/generic.js"),pane=__webpack_require__("./node_modules/@react-leaflet/core/lib/pane.js"),lib_element=__webpack_require__("./node_modules/@react-leaflet/core/lib/element.js");var leaflet_src=__webpack_require__("./node_modules/leaflet/dist/leaflet-src.js");const TileLayer=(0,generic.X3)((function createTileLayer({url,...options},context){const layer=new leaflet_src.TileLayer(url,(0,pane.P)(options,context));return(0,lib_element.Q)(layer,context)}),(function updateTileLayer(layer,props,prevProps){!function updateGridLayer(layer,props,prevProps){const{opacity,zIndex}=props;null!=opacity&&opacity!==prevProps.opacity&&layer.setOpacity(opacity),null!=zIndex&&zIndex!==prevProps.zIndex&&layer.setZIndex(zIndex)}(layer,props,prevProps);const{url}=props;null!=url&&url!==prevProps.url&&layer.setUrl(url)}))},"./node_modules/react-leaflet/lib/hooks.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{"use strict";__webpack_require__.d(__webpack_exports__,{Po:()=>useMapEvents,ko:()=>useMap});var _react_leaflet_core__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__("./node_modules/@react-leaflet/core/lib/context.js"),react__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("./node_modules/react/index.js");function useMap(){return(0,_react_leaflet_core__WEBPACK_IMPORTED_MODULE_1__.U$)().map}function useMapEvents(handlers){const map=useMap();return(0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)((function addMapEventHandlers(){return map.on(handlers),function removeMapEventHandlers(){map.off(handlers)}}),[map,handlers]),map}}}]);