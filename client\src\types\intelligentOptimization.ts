export interface AIOptimizationEngine {
  id: string;
  name: string;
  description: string;
  status: 'active' | 'paused' | 'disabled';
  lastRun: string;
  nextRun?: string;
  performance: OptimizationPerformance;
  config: OptimizationConfig;
}

export interface OptimizationConfig {
  autoABTesting: {
    enabled: boolean;
    testDuration: number; // in hours
    minSampleSize: number;
    confidenceLevel: number; // 0.95, 0.99, etc.
    metrics: string[];
  };
  parameterTuning: {
    enabled: boolean;
    frequency: 'hourly' | 'daily' | 'weekly';
    parameters: OptimizableParameter[];
    constraints: ParameterConstraint[];
  };
  predictiveAnalytics: {
    enabled: boolean;
    forecastHorizon: number; // in days
    models: PredictiveModel[];
    alertThresholds: AlertThreshold[];
  };
  autoScaling: {
    enabled: boolean;
    metrics: ScalingMetric[];
    rules: ScalingRule[];
  };
}

export interface OptimizableParameter {
  name: string;
  type: 'number' | 'string' | 'boolean' | 'enum';
  currentValue: any;
  range?: {
    min: number;
    max: number;
    step?: number;
  };
  options?: string[];
  impact: 'high' | 'medium' | 'low';
  description: string;
}

export interface ParameterConstraint {
  parameter: string;
  constraint: 'min' | 'max' | 'equals' | 'not_equals' | 'in_range';
  value: any;
  reason: string;
}

export interface ABTest {
  id: string;
  name: string;
  description: string;
  status: 'draft' | 'running' | 'completed' | 'paused' | 'cancelled';
  startDate: string;
  endDate?: string;
  variants: ABTestVariant[];
  metrics: ABTestMetric[];
  results?: ABTestResults;
  confidence: number;
  significance: number;
  winner?: string;
}

export interface ABTestVariant {
  id: string;
  name: string;
  description: string;
  parameters: Record<string, any>;
  trafficAllocation: number; // percentage
  performance: VariantPerformance;
}

export interface ABTestMetric {
  name: string;
  type: 'conversion' | 'engagement' | 'satisfaction' | 'performance';
  target: 'increase' | 'decrease';
  baseline: number;
  current: number;
  improvement: number;
  significance: number;
}

export interface ABTestResults {
  winner: string;
  confidence: number;
  improvement: number;
  recommendation: string;
  insights: string[];
  nextSteps: string[];
}

export interface VariantPerformance {
  conversions: number;
  sessions: number;
  conversionRate: number;
  averageSessionDuration: number;
  satisfactionScore: number;
  responseTime: number;
}

export interface PredictiveModel {
  id: string;
  name: string;
  type: 'time_series' | 'regression' | 'classification' | 'anomaly_detection';
  target: string;
  features: string[];
  accuracy: number;
  lastTrained: string;
  predictions: Prediction[];
}

export interface Prediction {
  timestamp: string;
  value: number;
  confidence: number;
  upperBound: number;
  lowerBound: number;
  factors: PredictionFactor[];
}

export interface PredictionFactor {
  name: string;
  impact: number; // -1 to 1
  description: string;
}

export interface AlertThreshold {
  metric: string;
  condition: 'above' | 'below' | 'change_rate';
  value: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
  action: 'notify' | 'auto_adjust' | 'escalate';
}

export interface ScalingMetric {
  name: string;
  source: 'system' | 'application' | 'business';
  threshold: number;
  direction: 'scale_up' | 'scale_down';
}

export interface ScalingRule {
  id: string;
  name: string;
  condition: string;
  action: ScalingAction;
  cooldown: number; // in minutes
  enabled: boolean;
}

export interface ScalingAction {
  type: 'adjust_resources' | 'modify_parameters' | 'enable_feature' | 'disable_feature';
  parameters: Record<string, any>;
  rollback?: ScalingAction;
}

export interface OptimizationPerformance {
  totalOptimizations: number;
  successfulOptimizations: number;
  averageImprovement: number;
  bestImprovement: number;
  lastOptimization: OptimizationResult;
  trends: PerformanceTrend[];
}

export interface OptimizationResult {
  id: string;
  timestamp: string;
  type: 'ab_test' | 'parameter_tuning' | 'auto_scaling' | 'predictive_adjustment';
  parameters: Record<string, any>;
  beforeMetrics: Record<string, number>;
  afterMetrics: Record<string, number>;
  improvement: number;
  confidence: number;
  status: 'success' | 'failure' | 'neutral';
  insights: string[];
  recommendations: string[];
}

export interface PerformanceTrend {
  date: string;
  metric: string;
  value: number;
  improvement: number;
}

export interface IntelligentRecommendation {
  id: string;
  type: 'parameter_adjustment' | 'feature_toggle' | 'workflow_optimization' | 'resource_allocation';
  title: string;
  description: string;
  impact: 'low' | 'medium' | 'high';
  effort: 'low' | 'medium' | 'high';
  confidence: number;
  expectedImprovement: number;
  reasoning: string[];
  implementation: RecommendationImplementation;
  status: 'pending' | 'approved' | 'implemented' | 'rejected';
  createdAt: string;
}

export interface RecommendationImplementation {
  steps: ImplementationStep[];
  estimatedTime: number; // in minutes
  rollbackPlan: ImplementationStep[];
  risks: Risk[];
  prerequisites: string[];
}

export interface ImplementationStep {
  id: string;
  description: string;
  action: 'api_call' | 'config_change' | 'feature_toggle' | 'manual_action';
  parameters: Record<string, any>;
  validation: ValidationRule[];
}

export interface ValidationRule {
  type: 'metric_threshold' | 'error_rate' | 'response_time' | 'user_feedback';
  condition: string;
  value: number;
  timeout: number; // in minutes
}

export interface Risk {
  type: 'performance' | 'availability' | 'user_experience' | 'data_integrity';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  mitigation: string;
  probability: number; // 0-1
}

export interface OptimizationDashboard {
  overview: {
    activeOptimizations: number;
    totalImprovements: number;
    averageImprovement: number;
    lastOptimization: string;
  };
  runningTests: ABTest[];
  pendingRecommendations: IntelligentRecommendation[];
  recentResults: OptimizationResult[];
  performanceTrends: PerformanceTrend[];
  alerts: OptimizationAlert[];
}

export interface OptimizationAlert {
  id: string;
  type: 'performance_degradation' | 'optimization_opportunity' | 'test_completion' | 'anomaly_detected';
  severity: 'info' | 'warning' | 'error' | 'critical';
  title: string;
  message: string;
  timestamp: string;
  actions: AlertAction[];
  acknowledged: boolean;
}

export interface AlertAction {
  id: string;
  label: string;
  type: 'view_details' | 'implement_fix' | 'schedule_optimization' | 'dismiss';
  parameters?: Record<string, any>;
}

// 预定义的优化模板
export const OPTIMIZATION_TEMPLATES = {
  responseTimeOptimization: {
    name: 'Response Time Optimization',
    description: 'Automatically optimize AI response times',
    parameters: ['temperature', 'max_tokens', 'model_selection'],
    metrics: ['average_response_time', 'user_satisfaction'],
    constraints: [
      { parameter: 'temperature', constraint: 'in_range', value: [0.1, 1.0], reason: 'Maintain response quality' },
      { parameter: 'max_tokens', constraint: 'min', value: 50, reason: 'Ensure complete responses' }
    ]
  },
  conversionOptimization: {
    name: 'Conversion Rate Optimization',
    description: 'Optimize conversation completion rates',
    parameters: ['greeting_style', 'escalation_threshold', 'follow_up_timing'],
    metrics: ['conversion_rate', 'completion_rate', 'user_engagement'],
    constraints: [
      { parameter: 'escalation_threshold', constraint: 'max', value: 0.8, reason: 'Prevent premature escalations' }
    ]
  },
  satisfactionOptimization: {
    name: 'User Satisfaction Optimization',
    description: 'Maximize user satisfaction scores',
    parameters: ['response_style', 'empathy_level', 'solution_depth'],
    metrics: ['satisfaction_score', 'positive_feedback_rate', 'return_user_rate'],
    constraints: [
      { parameter: 'empathy_level', constraint: 'min', value: 0.5, reason: 'Maintain human-like interaction' }
    ]
  }
};
