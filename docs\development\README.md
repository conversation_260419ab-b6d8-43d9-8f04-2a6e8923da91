# iBuddy2 开发指南

这个目录包含了iBuddy2项目的开发流程、规范和最佳实践文档，帮助新开发者快速加入项目。

## 目录

1. [快速入门](./getting-started.md) - 项目环境搭建和开发准备
2. [编码规范](./coding-standards.md) - 代码风格指南和最佳实践
3. [组件开发](./component-development.md) - UI组件开发流程和规范
4. [API开发](./api-development.md) - 后端API设计和开发指南
5. [测试指南](./testing.md) - 单元测试和集成测试编写指南
6. [工作流程](./workflow.md) - Git工作流和协作流程

## 原始文档索引

以下是原始开发文档的映射关系：

| 新文档 | 原始文档 |
|-------|---------|
| [快速入门](./getting-started.md) | /DEVELOPMENT_PLAN.md (入门部分) |
| [编码规范](./coding-standards.md) | /DEVELOPMENT_COMPLETION_SUMMARY.md (规范部分) |
| [组件开发](./component-development.md) | /client/CODE_QUALITY_OPTIMIZATION_SUMMARY.md |
| [测试指南](./testing.md) | /TESTING_GUIDE.md | 