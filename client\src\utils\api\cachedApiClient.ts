/**
 * 带缓存功能的API客户端
 * 提供三种缓存策略：默认(15分钟)、长期(1天)和无缓存
 */
import { AxiosRequestConfig, AxiosError } from 'axios';
import { createDefaultCache, createLongTermCache, createNoCache, cacheManager } from './cacheConfig';

// API客户端类型
export enum ApiClientType {
  DEFAULT = 'default', // 默认缓存（15分钟）
  LONG_TERM = 'longTerm', // 长期缓存（1天）
  NO_CACHE = 'noCache' // 不缓存
}

// 单例实例存储
let defaultCacheInstance: ReturnType<typeof createDefaultCache>;
let longTermCacheInstance: ReturnType<typeof createLongTermCache>;
let noCacheInstance: ReturnType<typeof createNoCache>;

// 获取指定类型的API客户端
export const getApiClient = (type: ApiClientType = ApiClientType.DEFAULT) => {
  switch (type) {
    case ApiClientType.LONG_TERM:
      if (!longTermCacheInstance) {
        longTermCacheInstance = createLongTermCache();
      }
      return longTermCacheInstance;
    
    case ApiClientType.NO_CACHE:
      if (!noCacheInstance) {
        noCacheInstance = createNoCache();
      }
      return noCacheInstance;
    
    case ApiClientType.DEFAULT:
    default:
      if (!defaultCacheInstance) {
        defaultCacheInstance = createDefaultCache();
      }
      return defaultCacheInstance;
  }
};

// 默认导出标准缓存客户端
export default getApiClient();

// 请求队列管理器 - 限制并发请求数量
class RequestQueueManager {
  private queue: Array<() => Promise<any>> = [];
  private activeRequests = 0;
  private maxConcurrent = 3; // 最大并发请求数
  private processingQueue = false;

  // 添加请求到队列
  enqueue<T>(requestFn: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      this.queue.push(async () => {
        try {
          const result = await requestFn();
          resolve(result);
          return result;
        } catch (error) {
          reject(error);
          throw error;
        }
      });
      
      this.processQueue();
    });
  }

  // 处理队列中的请求
  private async processQueue() {
    if (this.processingQueue) return;
    this.processingQueue = true;

    while (this.queue.length > 0 && this.activeRequests < this.maxConcurrent) {
      const request = this.queue.shift();
      if (request) {
        this.activeRequests++;
        
        try {
          await request();
        } catch (error) {
          console.error('Request failed:', error);
        } finally {
          this.activeRequests--;
        }
      }
    }
    
    this.processingQueue = false;
    
    // 如果队列中还有请求，继续处理
    if (this.queue.length > 0) {
      this.processQueue();
    }
  }
}

// 创建请求队列管理器实例
const requestQueue = new RequestQueueManager();

// 睡眠函数 - 用于请求重试间的延迟
const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// 带缓存控制和重试的请求函数
export const cachedRequest = async <T>(
  config: AxiosRequestConfig,
  cacheType: ApiClientType = ApiClientType.DEFAULT,
  options: {
    maxRetries?: number;
    initialRetryDelay?: number;
    maxRetryDelay?: number;
  } = {}
): Promise<T> => {
  const {
    maxRetries = 3,
    initialRetryDelay = 1000,
    maxRetryDelay = 10000
  } = options;
  
  // 通过队列管理器执行请求
  return requestQueue.enqueue(async () => {
    const client = getApiClient(cacheType);
    let retries = 0;
    let lastError: any;
    
    while (retries <= maxRetries) {
      try {
        // 如果不是第一次尝试，添加随机参数以避免缓存
        const finalConfig = retries > 0
          ? { 
              ...config, 
              params: { 
                ...config.params, 
                _retry: Date.now() 
              }
            }
          : config;
        
        const response = await client(finalConfig);
        return response.data as T;
      } catch (error) {
        lastError = error;
        const axiosError = error as AxiosError;
        
        // 对于429错误（请求过多）或网络错误，应用重试逻辑
        if (
          axiosError.response?.status === 429 || 
          axiosError.code === 'ECONNABORTED' ||
          axiosError.message === 'Network Error'
        ) {
          if (retries < maxRetries) {
            // 计算指数退避延迟时间（加入随机因子避免请求同步）
            const delay = Math.min(
              initialRetryDelay * Math.pow(2, retries) * (0.8 + Math.random() * 0.4), 
              maxRetryDelay
            );
            
            console.warn(
              `请求 ${config.url} 失败 (${axiosError.response?.status || axiosError.message}), ` +
              `将在 ${Math.round(delay)}ms 后重试 (${retries + 1}/${maxRetries})`
            );
            
            // 等待退避时间
            await sleep(delay);
            retries++;
            continue;
          }
        }
        
        // 对于其他错误或超过重试次数，直接抛出
        throw error;
      }
    }
    
    // 如果所有重试都失败了，抛出最后一个错误
    throw lastError;
  });
};

// 导出缓存管理器，方便外部使用
export { cacheManager }; 