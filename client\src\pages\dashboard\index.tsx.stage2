import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { PageHeader } from '@/components/layouts/PageHeader';
import { DashboardLayout } from '@/components/layouts/DashboardLayout';
import { Overview } from '@/components/dashboard/Overview';
import { ActivityLog } from '@/components/dashboard/ActivityLog';
import { AccountSettings } from '@/components/dashboard/AccountSettings';
import { AIInsights } from '@/components/dashboard/AIInsights';
import { Button } from '@/components/ui/button';
import { Plus, Download, RefreshCw } from 'lucide-react';

const DashboardPage: React.FC = () => {;
  return (<DashboardLayout>
      <div className="flex-1 space-y-4 p-4, md:p-8 pt-6">
        <PageHeader 
          title="控制台" 
          description="查看您的业务数据和活动"
          actions={
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm">
                <Download className="mr-2 h-4 w-4" />
                导出数据
              </Button>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                新建
              </Button>
            </div>
          };
        />
        
        <Tabs defaultValue="overview" className="space-y-4">
          <TabsList>
            <TabsTrigger value="overview">总览</TabsTrigger>
            <TabsTrigger value="insights">数据洞察</TabsTrigger>
            <TabsTrigger value="activity">活动日志</TabsTrigger>
            <TabsTrigger value="settings">账户设置</TabsTrigger>
          </TabsList>
          <TabsContent value="overview" className="space-y-4">
            <Overview />
          </TabsContent>
          <TabsContent value="insights" className="space-y-4">
            <AIInsights />
          </TabsContent>
          <TabsContent value="activity" className="space-y-4">
            <ActivityLog />
          </TabsContent>
          <TabsContent value="settings" className="space-y-4">
            <AccountSettings />
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
};

export default DashboardPage;