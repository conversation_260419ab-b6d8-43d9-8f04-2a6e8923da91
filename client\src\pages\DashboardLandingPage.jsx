import React from 'react'
import { useNavigate } from 'react-router-dom'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/design-system'
import { Button } from '@/components/design-system'
import { BarChart3, Settings, Database, Users, Activity } from 'lucide-react'
import { ArrowRightIcon } from '@/components/ui/icons'
import { getThemeClasses } from '@/theme/dashboard-theme'
import { cn } from '@/lib/utils'

function DashboardLandingPage() {
  const navigate = useNavigate();

  const dashboardSections = [
    {
      title: "Data Center",
      description: "Manage and analyze your AI agent's, bookings, analytics, and system data.",
      icon: Database,
      gradient: "from-blue-500 to-blue-600",
      iconBg: "bg-blue-100 dark:bg-blue-900",
      iconColor: "text-blue-600 dark:text-blue-400",
      path: "/data/dashboard",
      features: ["AI Agent Management", "Booking Analytics", "User Data", "Performance Metrics"],
    },
    {
      title: "Settings Center",
      description: "Configure and customize your application settings and preferences.",
      icon: Settings,
      gradient: "from-emerald-500 to-emerald-600",
      iconBg: "bg-emerald-100 dark:bg-emerald-900",
      iconColor: "text-emerald-600 dark:text-emerald-400",
      path: "/settings",
      features: ["User Preferences", "System Configuration", "API Settings", "Security Options"],
    },
  ];

  const quickStats = [
    { label: "Active Users", value: "2,847", icon: Users, change: "+12%" },
    { label: "System Health", value: "99.9%", icon: Activity, change: "Optimal" },
    { label: "Data Points", value: "1.2M", icon: BarChart3, change: "+24%" },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 p-6">
      <div className="max-w-6xl mx-auto">
        {/* Header Section */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-slate-900 dark:text-slate-100 mb-4">
            Welcome to Your Dashboard
          </h1>
          <p className="text-lg text-slate-600 dark:text-slate-400 max-w-2xl mx-auto">
            Manage your entire platform from one central location. Choose a section below to get started.
          </p>
        </div>

        {/* Quick Stats */}
        <div className="grid gap-4 md:grid-cols-3 mb-12">
          {quickStats.map((stat, i) => (
            <Card key={i} className={getThemeClasses('stats')}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-slate-600 dark:text-slate-400">{stat.label}</p>
                    <p className="text-2xl font-bold text-slate-900 dark:text-slate-100">{stat.value}</p>
                    <p className="text-xs text-emerald-600">{stat.change}</p>
                  </div>
                  <div className="p-3 bg-blue-100 dark:bg-blue-900 rounded-lg">
                    <stat.icon className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Main Dashboard Sections */}
        <div className="grid gap-8 md:grid-cols-2">
          {dashboardSections.map((section, index) => (
            <Card key={index} className={cn(getThemeClasses('interactive'), "group overflow-hidden")}>
              <CardHeader className="relative">
                <div className="flex items-center gap-4">
                  <div className={cn("p-3 rounded-xl", section.iconBg)}>
                    <section.icon className={cn("h-8 w-8", section.iconColor)} />
                  </div>
                  <div className="flex-1">
                    <CardTitle className="text-xl font-semibold text-slate-900 dark:text-slate-100">
                      {section.title}
                    </CardTitle>
                    <CardDescription className="text-slate-600 dark:text-slate-400 mt-1">
                      {section.description}
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              
              <CardContent className="space-y-4">
                <div className="grid gap-2">
                  {section.features.map((feature, i) => (
                    <div key={i} className="flex items-center gap-2 text-sm text-slate-600 dark:text-slate-400">
                      <div className="w-1.5 h-1.5 bg-slate-300 dark:bg-slate-600 rounded-full" />
                      {feature}
                    </div>
                  ))}
                </div>
                
                <Button 
                  onClick={() => navigate(section.path)}
                  className={cn(
                    "w-full group-hover:shadow-lg transition-all duration-200",
                    `bg-gradient-to-r ${section.gradient} hover:shadow-${section.gradient.split('-')[1]}-500/25`
                  )}>
                  Access {section.title}
                  <ArrowRightIcon className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Additional Quick Actions */}
        <div className="mt-12 text-center">
          <Card className={getThemeClasses('outlined')}>
            <CardContent className="p-8">
              <h3 className="text-lg font-semibold text-slate-900 dark:text-slate-100 mb-2">
                Need Something Else?
              </h3>
              <p className="text-slate-600 dark:text-slate-400 mb-6">
                Explore our full feature set or get help with your current setup.
              </p>
              <div className="flex gap-4 justify-center">
                <Button variant="outline" onClick={() => navigate('/docs')}>
                  Documentation
                </Button>
                <Button variant="outline" onClick={() => navigate('/support')}>
                  Get Support
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default DashboardLandingPage;