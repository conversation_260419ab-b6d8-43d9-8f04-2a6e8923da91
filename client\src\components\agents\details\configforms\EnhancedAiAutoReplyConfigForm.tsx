import React, { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { 
  Brain, 
  Settings, 
  Sparkles, 
  Eye,
  Zap,
  AlertCircle
} from 'lucide-react'
import { cn } from '@/lib/utils'

// 导入我们创建的组件
import EnhancedSmartConfigForm from './EnhancedSmartConfigForm'
import LivePreviewPanel from './LivePreviewPanel'
import AiAutoReplyConfigForm from './AiAutoReplyConfigForm'
import { useSmartAiConfig } from '@/hooks/useSmartAiConfig'

interface EnhancedAiAutoReplyConfigFormProps {
  config: Record<string, any>;
  onConfigChange: (key: string, value: any) => void;
  className?: string;
}

const EnhancedAiAutoReplyConfigForm: React.FC<EnhancedAiAutoReplyConfigFormProps> = ({
  config,
  onConfigChange,
  className
}) => {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { t, i18n } = useTranslation();
  const [activeTab, setActiveTab] = useState('smart');
  
  // 使用智能配置Hook
  const {
    config: smartConfig,
    updateConfig,
    smartSuggestions,
    completionPercentage,
    isValid,
    hasErrors,
    hasWarnings
  } = useSmartAiConfig(config);

  // 同步智能配置到父组件
  const handleSmartConfigChange = (key: string, value: any) => {
    updateConfig(key, value);
    onConfigChange(key, value);
  };

  // 获取配置状态指示器
  const getStatusIndicator = () => {
    if (hasErrors) {
      return { color: 'bg-red-500', text: 'Needs Attention', icon: AlertCircle };
    }
    if (hasWarnings) {
      return { color: 'bg-yellow-500', text: 'Review Suggested', icon: AlertCircle };
    }
    if (completionPercentage >= 80) {
      return { color: 'bg-green-500', text: 'Ready to Deploy', icon: Zap };
    }
    return { color: 'bg-blue-500', text: 'In Progress', icon: Settings };
  };

  const statusIndicator = getStatusIndicator();
  const StatusIcon = statusIndicator.icon;

  return (
    <div className={cn("space-y-6", className)}>
      {/* 配置状态概览 */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className={cn("w-3 h-3 rounded-full", statusIndicator.color)} />
              <div>
                <h4 className="font-medium">AI Auto-Reply Configuration</h4>
                <p className="text-sm text-muted-foreground">
                  {completionPercentage}% complete • {statusIndicator.text}
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <StatusIcon className="h-4 w-4 text-muted-foreground" />
              {smartSuggestions.length > 0 && (
                <Badge variant="secondary">
                  {smartSuggestions.length} suggestion{smartSuggestions.length !== 1 ? 's' : ''}
                </Badge>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 主配置界面 */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 配置表单区域 */}
        <div className="lg:col-span-2">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="smart" className="flex items-center gap-2">
                <Brain className="h-4 w-4" />
                <span className="hidden sm:inline">Smart Setup</span>
                <span className="sm:hidden">Smart</span>
              </TabsTrigger>
              <TabsTrigger value="advanced" className="flex items-center gap-2">
                <Settings className="h-4 w-4" />
                <span className="hidden sm:inline">Advanced</span>
                <span className="sm:hidden">Advanced</span>
              </TabsTrigger>
              <TabsTrigger value="preview" className="flex items-center gap-2">
                <Eye className="h-4 w-4" />
                <span className="hidden sm:inline">Preview</span>
                <span className="sm:hidden">Preview</span>
              </TabsTrigger>
            </TabsList>

            {/* 智能配置模式 */}
            <TabsContent value="smart" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Sparkles className="h-5 w-5 text-primary" />
                    Smart Configuration
                  </CardTitle>
                  <CardDescription>
                    Guided setup with intelligent recommendations and real-time validation
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <EnhancedSmartConfigForm
                    config={smartConfig}
                    onConfigChange={handleSmartConfigChange}
                  />
                </CardContent>
              </Card>
            </TabsContent>

            {/* 高级配置模式 */}
            <TabsContent value="advanced" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="h-5 w-5 text-primary" />
                    Advanced Configuration
                  </CardTitle>
                  <CardDescription>
                    Complete configuration options for power users
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <AiAutoReplyConfigForm
                    config={config}
                    onConfigChange={onConfigChange}
                  />
                </CardContent>
              </Card>
            </TabsContent>

            {/* 预览模式 */}
            <TabsContent value="preview" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Eye className="h-5 w-5 text-primary" />
                    Live Preview & Testing
                  </CardTitle>
                  <CardDescription>
                    Test your AI configuration with real-time conversation simulation
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <LivePreviewPanel config={smartConfig} />
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* 侧边栏 - 智能建议和快速操作 */}
        <div className="lg:col-span-1 space-y-4">
          {/* 智能建议面板 */}
          {smartSuggestions.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Brain className="h-4 w-4 text-blue-500" />
                  Smart Suggestions
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {smartSuggestions.slice(0, 3).map((suggestion, index) => (
                    <div key={index} className="flex items-start gap-2">
                      <Badge 
                        variant={suggestion.type === 'error' ? 'destructive' : 
                                suggestion.type === 'warning' ? 'secondary' : 'default'}
                        className="mt-0.5"
                      >
                        {suggestion.type}
                      </Badge>
                      <p className="text-sm flex-1">{suggestion.message}</p>
                    </div>
                  ))}
                  {smartSuggestions.length > 3 && (
                    <p className="text-xs text-muted-foreground">
                      +{smartSuggestions.length - 3} more suggestions
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* 快速操作面板 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button 
                variant="outline" 
                size="sm" 
                className="w-full justify-start"
                onClick={() => setActiveTab('preview')}
              >
                <Eye className="h-4 w-4 mr-2" />
                Test Configuration
              </Button>
              <Button 
                variant="outline" 
                size="sm" 
                className="w-full justify-start"
                disabled={!isValid}
              >
                <Zap className="h-4 w-4 mr-2" />
                Deploy Agent
              </Button>
              <Button 
                variant="outline" 
                size="sm" 
                className="w-full justify-start"
              >
                <Settings className="h-4 w-4 mr-2" />
                Export Config
              </Button>
            </CardContent>
          </Card>

          {/* 配置统计 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Configuration Stats</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span>Completion</span>
                  <span className="font-medium">{completionPercentage}%</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Validation</span>
                  <span className={cn("font-medium", isValid ? "text-green-600" : "text-red-600")}>
                    {isValid ? "Passed" : "Failed"}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Suggestions</span>
                  <span className="font-medium">{smartSuggestions.length}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default EnhancedAiAutoReplyConfigForm;
