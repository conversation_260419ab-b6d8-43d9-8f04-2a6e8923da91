-- 创建用户上下文表
CREATE TABLE user_context (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id TEXT NOT NULL,
    session_id TEXT,
    user_message TEXT,
    ai_response TEXT,
    timestamp TIMESTAMPTZ DEFAULT NOW(),
    intent TEXT,
    confidence DECIMAL,
    metadata JSONB,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 添加索引以提高查询性能
CREATE INDEX idx_user_context_user_id ON user_context(user_id);
CREATE INDEX idx_user_context_session_id ON user_context(session_id);
CREATE INDEX idx_user_context_timestamp ON user_context(timestamp);

-- 添加触发器更新updated_at字段
CREATE OR REPLACE FUNCTION update_timestamp()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = NOW();
   RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_user_context_timestamp
BEFORE UPDATE ON user_context
FOR EACH ROW
EXECUTE FUNCTION update_timestamp();

-- 添加表注释
COMMENT ON TABLE user_context IS '存储用户与AI助手之间的对话上下文';
COMMENT ON COLUMN user_context.user_id IS '用户ID';
COMMENT ON COLUMN user_context.session_id IS '会话ID，可以为空';
COMMENT ON COLUMN user_context.user_message IS '用户发送的消息';
COMMENT ON COLUMN user_context.ai_response IS 'AI助手的回复';
COMMENT ON COLUMN user_context.intent IS '识别出的意图';
COMMENT ON COLUMN user_context.confidence IS '意图置信度';
COMMENT ON COLUMN user_context.metadata IS '其他元数据，如处理时间、模型信息等'; 