# HeroSection Component

现代化的英雄区块组件，具有动画网格背景和渐变效果，适用于登陆页面和产品展示。

## 特性

- ✨ 动画网格背景效果
- 🎨 支持明暗主题
- 📱 完全响应式设计
- 🚀 流畅的渐变动画
- 🎯 可自定义的按钮和布局
- 🖼️ 支持明暗两种底部图片
- ⚙️ 可配置的网格动画参数

## 安装

确保项目已安装以下依赖：

```bash
npm install lucide-react
npm install clsx tailwind-merge
```

## 基本使用

```tsx
import { HeroSection } from "@/components/ui/hero-section-dark"
import { useNavigate } from 'react-router-dom'

function MyPage() {
  const navigate = useNavigate()

  return (
    <HeroSection
      title="欢迎使用我们的平台"
      subtitle={{
        regular: "将您的想法转化为 ",
        gradient: "美丽的数字体验",
      }}
      description="使用我们先进的AI平台改变您的内容创作流程。"
      ctaText="立即开始"
      onCtaClick={() => navigate('/register')}
      bottomImage={{
        light: "https://example.com/dashboard-light.png",
        dark: "https://example.com/dashboard-dark.png",
      }}
    />
  )
}
```

## API 参考

### Props

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `title` | `string` | `"Build products for everyone"` | 顶部小标题文本 |
| `subtitle` | `object` | `{ regular: string, gradient: string }` | 主标题，包含普通文本和渐变文本 |
| `description` | `string` | 默认描述文本 | 主标题下方的描述文本 |
| `ctaText` | `string` | `"Browse courses"` | 行动按钮文本 |
| `ctaHref` | `string` | `"#"` | 行动按钮链接（仅在未提供 onCtaClick 时使用） |
| `onCtaClick` | `() => void` | `undefined` | 行动按钮点击回调函数（推荐用于 React Router） |
| `bottomImage` | `object` | 默认图片对象 | 包含明暗主题图片的对象 |
| `gridOptions` | `object` | 默认网格配置 | 网格动画配置选项 |
| `className` | `string` | `undefined` | 附加的CSS类名 |

### subtitle 对象

```tsx
{
  regular: string;    // 普通文本部分
  gradient: string;   // 带渐变效果的文本部分
}
```

### bottomImage 对象

```tsx
{
  light: string;  // 浅色主题图片URL
  dark: string;   // 深色主题图片URL
}
```

### gridOptions 对象

```tsx
{
  angle?: number;           // 网格角度 (默认: 65)
  cellSize?: number;        // 网格单元大小 (默认: 60)
  opacity?: number;         // 网格透明度 (默认: 0.5)
  lightLineColor?: string;  // 浅色主题线条颜色 (默认: "gray")
  darkLineColor?: string;   // 深色主题线条颜色 (默认: "gray")
}
```

## 使用示例

### 基本示例

```tsx
<HeroSection
  title="简单标题"
  subtitle={{
    regular: "创建 ",
    gradient: "令人惊叹的产品",
  }}
  description="这是一个简单的描述文本。"
  ctaText="开始使用"
  onCtaClick={() => console.log('Button clicked!')}
/>
```

### 自定义网格效果

```tsx
<HeroSection
  title="自定义网格"
  subtitle={{
    regular: "体验 ",
    gradient: "独特的视觉效果",
  }}
  description="使用自定义网格配置创造独特的视觉体验。"
  ctaText="探索更多"
  onCtaClick={() => navigate('/explore')}
  gridOptions={{
    angle: 45,
    cellSize: 80,
    opacity: 0.3,
    lightLineColor: "#e0e0e0",
    darkLineColor: "#404040",
  }}
/>
```

### 无底部图片

```tsx
<HeroSection
  title="专注信息"
  subtitle={{
    regular: "有时少即是 ",
    gradient: "更有效",
  }}
  description="专注于信息传达，不使用分散注意力的图片。"
  ctaText="了解更多"
  onCtaClick={() => navigate('/learn')}
  bottomImage={undefined}
/>
```

### 与 React Router 集成

```tsx
import { useNavigate } from 'react-router-dom'

function LandingPage() {
  const navigate = useNavigate()

  const handleGetStarted = () => {
    // 可以添加额外的逻辑，如分析跟踪
    gtag('event', 'click', { event_category: 'CTA', event_label: 'Hero Get Started' })
    navigate('/register')
  }

  return (
    <HeroSection
      title="AI驱动的内容创作平台"
      subtitle={{
        regular: "将您的想法转化为 ",
        gradient: "美丽的数字体验",
      }}
      description="使用我们先进的AI平台改变您的内容创作流程。"
      ctaText="免费开始"
      onCtaClick={handleGetStarted}
      bottomImage={{
        light: "https://images.unsplash.com/photo-1551434678-e076c223a692",
        dark: "https://images.unsplash.com/photo-1518709268805-4e9042af2176",
      }}
    />
  )
}
```

## 样式自定义

组件使用 Tailwind CSS 构建。您可以通过 `className` prop 添加自定义样式：

```tsx
<HeroSection
  className="my-custom-hero bg-gradient-to-r from-blue-500 to-purple-600"
  // ... 其他 props
/>
```

## 无障碍性

- 组件支持键盘导航
- 使用语义化的HTML结构
- 图片包含适当的 alt 文本
- 按钮具有适当的 aria-label

## 浏览器支持

- 现代浏览器 (Chrome 88+, Firefox 85+, Safari 14+)
- 支持 CSS Grid 和 Flexbox
- 支持 CSS 自定义属性

## 注意事项

1. 确保您的 Tailwind 配置包含了必要的动画配置
2. 组件需要 `lucide-react` 库提供图标支持
3. 网格动画可能在低端设备上影响性能，可以通过调整 `gridOptions.opacity` 来优化
4. 推荐使用高质量的图片以获得最佳视觉效果

## Storybook

查看 Storybook 中的组件展示：

```bash
npm run storybook
```

然后导航到 "Components/UI/HeroSection" 查看各种使用场景和配置选项。 