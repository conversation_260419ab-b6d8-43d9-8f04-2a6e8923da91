/**
 * 🎨 Unified Chart Theme System - Soft Purple Theme
 * Chart theme configuration for Data Insights components
 */

// 🟣 Soft Purple Color Palette
export const chartColors = {
  // Primary soft purple color scale
  primary: {
    50: '#F7F1FF',
    100: '#E9D8FD',
    200: '#D6BCFA',
    300: '#B794F6',
    400: '#9F7AEA',
    500: '#805AD5',  // Main brand color
    600: '#6B46C1',
    700: '#553C9A',
    800: '#44337A',
    900: '#322659'
  },
  
  // Chart series colors (soft purple variations)
  series: [
    '#805AD5', // Primary purple
    '#3B82F6', // Blue
    '#10B981', // Green  
    '#F59E0B', // Amber
    '#EF4444', // Red
    '#8B5CF6', // Violet
    '#06B6D4', // Cyan
    '#84CC16', // Lime
    '#F97316', // Orange
    '#EC4899', // Pink
    '#6366F1', // Indigo
    '#14B8A6'  // Teal
  ],
  
  // Gradient series (for area charts)
  gradients: [
    '#F7F1FF', // Ultra light purple
    '#E9D8FD', // Very light purple
    '#D6BCFA'  // Light purple
  ],
  
  // Semantic colors
  semantic: {
    success: '#10B981',
    warning: '#F59E0B',
    error: '#EF4444',
    info: '#3B82F6',
    neutral: '#6B7280'
  },
  
  // Background and text
  background: '#FEFCFF',
  surface: '#FFFFFF',
  text: {
    primary: '#1F2937',
    secondary: '#6B7280',
    muted: '#9CA3AF'
  },
  
  // Grid and borders
  grid: '#E9D8FD',
  border: '#D6BCFA',
  divider: '#E9D8FD'
};

// 🎨 Chart Component Styles
export const chartTheme = {
  // Common chart configuration
  chart: {
    margin: { top: 20, right: 30, left: 20, bottom: 5 },
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    fontSize: 12,
    fontWeight: 400,
    
    // Grid styling
    grid: {
      stroke: chartColors.grid,
      strokeDasharray: '3 3',
      strokeWidth: 1,
      opacity: 0.6
    },
    
    // Animation settings
    animation: {
      duration: 750,
      easing: 'ease-in-out'
    }
  },

  // 🎯 Axis Styling
  axis: {
    x: {
      tick: {
        fontSize: 12,
        fill: chartColors.text.secondary,
        fontWeight: 400
      },
      tickLine: {
        stroke: chartColors.border,
        strokeWidth: 1
      },
      axisLine: {
        stroke: chartColors.border,
        strokeWidth: 1
      },
      label: {
        fontSize: 11,
        fill: chartColors.text.muted,
        textAnchor: 'middle'
      }
    },
    y: {
      tick: {
        fontSize: 12,
        fill: chartColors.text.secondary,
        fontWeight: 400
      },
      tickLine: {
        stroke: chartColors.border,
        strokeWidth: 1
      },
      axisLine: {
        stroke: chartColors.border,
        strokeWidth: 1
      },
      label: {
        fontSize: 11,
        fill: chartColors.text.muted,
        angle: -90,
        textAnchor: 'middle'
      }
    }
  },

  // 🎨 Tooltip Styling
  tooltip: {
    contentStyle: {
      background: 'rgba(255, 255, 255, 0.95)',
      border: `1px solid ${chartColors.border}`,
      borderRadius: '8px',
      boxShadow: '0 4px 12px rgba(159, 122, 234, 0.15)',
      padding: '12px',
      fontSize: '12px',
      fontWeight: 500,
      backdropFilter: 'blur(8px)'
    },
    labelStyle: {
      color: chartColors.text.primary,
      fontWeight: 600,
      marginBottom: '4px'
    },
    itemStyle: {
      color: chartColors.text.secondary,
      padding: '2px 0'
    },
    cursor: {
      stroke: chartColors.primary[400],
      strokeWidth: 1,
      strokeDasharray: '2 2'
    }
  },

  // 🏷️ Legend Styling  
  legend: {
    wrapperStyle: {
      fontSize: '12px',
      fontWeight: 500,
      color: chartColors.text.secondary
    },
    itemStyle: {
      color: chartColors.text.secondary
    },
    iconType: 'rect' as const,
    align: 'center' as const,
    verticalAlign: 'top' as const,
    height: 36
  },

  // 📊 Chart Type Specific Styles
  line: {
    strokeWidth: 2,
    dot: {
      r: 4,
      strokeWidth: 2,
      fill: chartColors.background
    },
    activeDot: {
      r: 6,
      strokeWidth: 0,
      fill: chartColors.primary[500]
    }
  },

  area: {
    strokeWidth: 2,
    fillOpacity: 0.6,
    gradientStops: [
      { offset: '0%', stopColor: chartColors.primary[500], stopOpacity: 0.8 },
      { offset: '100%', stopColor: chartColors.primary[500], stopOpacity: 0.1 }
    ]
  },

  bar: {
    fill: chartColors.primary[500],
    stroke: 'none',
    radius: [2, 2, 0, 0]
  },

  pie: {
    innerRadius: 0,
    outerRadius: 80,
    paddingAngle: 2,
    labelLine: false,
    label: {
      fontSize: 11,
      fill: chartColors.text.primary,
      fontWeight: 500
    }
  }
};

// 🎨 Generate Chart Color Scale
export const getChartColor = (index: number, opacity = 1): string => {
  const color = chartColors.series[index % chartColors.series.length];
  if (opacity === 1) return color;
  
  // Convert hex to rgba with opacity
  const r = parseInt(color.slice(1, 3), 16);
  const g = parseInt(color.slice(3, 5), 16);  
  const b = parseInt(color.slice(5, 7), 16);
  return `rgba(${r}, ${g}, ${b}, ${opacity})`;
};

// 🎨 Temperature-based colors for lead analysis
export const getTemperatureColor = (temperature: string): string => {
  switch (temperature.toLowerCase()) {
    case 'hot':
      return chartColors.semantic.error;
    case 'warm':
      return '#F97316'; // 使用更深的橙色改善对比度
    case 'cold':
      return chartColors.semantic.info;
    default:
      return chartColors.semantic.neutral;
  }
};

// 🎨 Status-based colors for leads
export const getStatusColor = (status: string): string => {
  switch (status.toLowerCase()) {
    case 'active':
    case 'success':
    case 'completed':
      return chartColors.semantic.success;
    case 'pending':
    case 'warning':
      return chartColors.semantic.warning;
    case 'error':
    case 'failed':
    case 'inactive':
      return chartColors.semantic.error;
    case 'info':
    case 'processing':
      return chartColors.semantic.info;
    default:
      return chartColors.semantic.neutral;
  }
};

// 🎨 Gradient Definitions for Advanced Charts
export const chartGradients = {
  purpleGradient: {
    id: 'purpleGradient',
    stops: [
      { offset: '0%', stopColor: chartColors.primary[400] },
      { offset: '100%', stopColor: chartColors.primary[600] }
    ]
  },
  areaGradient: {
    id: 'areaGradient',
    stops: [
      { offset: '0%', stopColor: chartColors.primary[500], stopOpacity: 0.8 },
      { offset: '100%', stopColor: chartColors.primary[500], stopOpacity: 0.1 }
    ]
  }
};

// 🎨 Responsive Chart Configuration
export const responsiveConfig = {
  mobile: {
    margin: { top: 10, right: 10, left: 10, bottom: 5 },
    fontSize: 10,
    legend: { height: 24 }
  },
  tablet: {
    margin: { top: 15, right: 20, left: 15, bottom: 5 },
    fontSize: 11,
    legend: { height: 30 }
  },
  desktop: {
    margin: { top: 20, right: 30, left: 20, bottom: 5 },
    fontSize: 12,
    legend: { height: 36 }
  }
};

// 🎨 Chart responsive breakpoints
export const chartBreakpoints = {
  sm: 480,
  md: 768,
  lg: 1024,
  xl: 1280
};

// 📱 Responsive chart configurations
export const getResponsiveChartConfig = (width: number) => {
  if (width < chartBreakpoints.sm) {
    return {
      margin: { top: 10, right: 15, left: 10, bottom: 5 },
      fontSize: 10,
      legend: { ...chartTheme.legend, height: 24 }
    };
  } else if (width < chartBreakpoints.md) {
    return {
      margin: { top: 15, right: 20, left: 15, bottom: 5 },
      fontSize: 11,
      legend: { ...chartTheme.legend, height: 30 }
    };
  }
  return chartTheme.chart;
};

export default chartTheme;