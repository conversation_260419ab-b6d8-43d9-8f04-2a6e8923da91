const express = require('express');
const router = express.Router();

// 导入数据洞察控制器
const dataInsightsController = require('../controllers/dataInsightsController');

/**
 * @route   GET /api/data-insights/kpis
 * @desc    Get Key Performance Indicators for data insights
 * @access  Private
 */
router.get('/kpis', dataInsightsController.getKPIs);

/**
 * @route   GET /api/data-insights/agents-performance
 * @desc    Get performance data for agents
 * @access  Private
 */
router.get('/agents-performance', dataInsightsController.getAgentPerformance);

/**
 * @route   GET /api/data-insights/user-engagement
 * @desc    Get user engagement data
 * @access  Private
 */
router.get('/user-engagement', dataInsightsController.getUserEngagement);

/**
 * @route   GET /api/data-insights/system-operations
 * @desc    Get system operations data
 * @access  Private
 */
router.get('/system-operations', dataInsightsController.getSystemOperations);

/**
 * @route   GET /api/data-insights/lead-generation-overview
 * @desc    Get lead generation overview across all agents
 * @access  Private
 */
router.get('/lead-generation-overview', dataInsightsController.getLeadGenerationOverview);

/**
 * @route   GET /api/data-insights/lead-generation/temperature-analysis
 * @desc    Get detailed lead temperature analysis data
 * @access  Private
 */
router.get('/lead-generation/temperature-analysis', dataInsightsController.getLeadTemperatureAnalysis);

// Add other data-insights related routes here
// Example: router.get('/user-activity', dataInsightsController.getUserActivity);

module.exports = router; 