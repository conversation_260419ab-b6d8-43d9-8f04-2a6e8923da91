#!/usr/bin/env node

/**
 * 检查 iBuddy2 项目各服务状态
 */

const fs = require('fs');
const path = require('path');

// 颜色代码
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

// 服务配置
const services = [
  {
    name: 'Client (React)',
    path: 'client',
    packageJson: 'client/package.json',
    port: 3000
  },
  {
    name: 'API Gateway',
    path: 'api-gateway',
    packageJson: 'api-gateway/package.json',
    port: 3001
  },
  {
    name: 'Core Service',
    path: 'core-service',
    packageJson: 'core-service/package.json',
    port: 3002
  },
  {
    name: 'AI Service',
    path: 'ai-service',
    packageJson: 'ai-service/package.json',
    port: 3003
  }
];

function checkFileExists(filePath) {
  return fs.existsSync(path.join(__dirname, '..', filePath));
}

function getPackageInfo(packagePath) {
  try {
    const fullPath = path.join(__dirname, '..', packagePath);
    if (!fs.existsSync(fullPath)) {
      return null;
    }
    const content = fs.readFileSync(fullPath, 'utf8');
    return JSON.parse(content);
  } catch (error) {
    return null;
  }
}

function checkServiceStatus(service) {
  const status = {
    name: service.name,
    port: service.port,
    directoryExists: checkFileExists(service.path),
    packageJsonExists: checkFileExists(service.packageJson),
    packageInfo: null,
    nodeModulesExists: checkFileExists(path.join(service.path, 'node_modules')),
    hasStartScript: false,
    hasDevScript: false
  };

  if (status.packageJsonExists) {
    status.packageInfo = getPackageInfo(service.packageJson);
    if (status.packageInfo) {
      status.hasStartScript = !!status.packageInfo.scripts?.start;
      status.hasDevScript = !!status.packageInfo.scripts?.dev;
    }
  }

  return status;
}

function getStatusIcon(condition) {
  return condition ? `${colors.green}✓${colors.reset}` : `${colors.red}✗${colors.reset}`;
}

function getStatusColor(condition) {
  return condition ? colors.green : colors.red;
}

function main() {
  console.log(`${colors.bright}${colors.cyan}`);
  console.log('╔══════════════════════════════════════════╗');
  console.log('║          iTeraBiz 项目状态检查            ║');
  console.log('╚══════════════════════════════════════════╝');
  console.log(`${colors.reset}\n`);

  let allGood = true;

  services.forEach((service, index) => {
    const status = checkServiceStatus(service);
    
    console.log(`${colors.bright}${index + 1}. ${status.name}${colors.reset}`);
    console.log(`   端口: ${colors.cyan}${status.port}${colors.reset}`);
    console.log(`   目录存在: ${getStatusIcon(status.directoryExists)}`);
    console.log(`   package.json: ${getStatusIcon(status.packageJsonExists)}`);
    console.log(`   node_modules: ${getStatusIcon(status.nodeModulesExists)}`);
    console.log(`   启动脚本: ${getStatusIcon(status.hasStartScript)}`);
    console.log(`   开发脚本: ${getStatusIcon(status.hasDevScript)}`);
    
    if (status.packageInfo) {
      console.log(`   版本: ${colors.yellow}${status.packageInfo.version || 'unknown'}${colors.reset}`);
    }

    // 检查是否有问题
    const hasIssues = !status.directoryExists || 
                      !status.packageJsonExists || 
                      !status.nodeModulesExists || 
                      !status.hasStartScript;

    if (hasIssues) {
      allGood = false;
      console.log(`   ${colors.red}状态: 需要修复${colors.reset}`);
      
      if (!status.directoryExists) {
        console.log(`   ${colors.yellow}• 目录不存在${colors.reset}`);
      }
      if (!status.packageJsonExists) {
        console.log(`   ${colors.yellow}• package.json文件缺失${colors.reset}`);
      }
      if (!status.nodeModulesExists) {
        console.log(`   ${colors.yellow}• 需要运行 npm install${colors.reset}`);
      }
      if (!status.hasStartScript) {
        console.log(`   ${colors.yellow}• 缺少启动脚本${colors.reset}`);
      }
    } else {
      console.log(`   ${colors.green}状态: 就绪${colors.reset}`);
    }
    
    console.log('');
  });

  // 检查重要文件
  console.log(`${colors.bright}重要文件检查:${colors.reset}`);
  const importantFiles = [
    'package.json',
    'tools/start-all-services.js',
    'tools/start-dev.js',
    'client/src/App.tsx',
    'api-gateway/src/index.js',
    'core-service/src/index.js',
    'ai-service/src/index.js'
  ];

  importantFiles.forEach(file => {
    const exists = checkFileExists(file);
    console.log(`   ${getStatusIcon(exists)} ${file}`);
    if (!exists) allGood = false;
  });

  console.log('');

  // 总结
  if (allGood) {
    console.log(`${colors.green}${colors.bright}✅ 所有服务状态良好，可以开始开发！${colors.reset}`);
    console.log(`${colors.cyan}运行 'npm start' 启动所有服务${colors.reset}`);
  } else {
    console.log(`${colors.red}${colors.bright}⚠️  发现问题，需要修复后才能正常运行${colors.reset}`);
    console.log(`${colors.yellow}建议执行以下命令:${colors.reset}`);
    console.log(`${colors.cyan}  npm run install:all  # 安装所有依赖${colors.reset}`);
    console.log(`${colors.cyan}  npm run setup        # 完整设置${colors.reset}`);
  }

  console.log('');
  
  return allGood ? 0 : 1;
}

if (require.main === module) {
  const exitCode = main();
  process.exit(exitCode);
}

module.exports = { checkServiceStatus, services }; 