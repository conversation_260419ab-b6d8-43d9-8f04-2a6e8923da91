"use strict";(self.webpackChunkclient=self.webpackChunkclient||[]).push([[899],{"./src/stories/UserMenu.stories.jsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{Default:()=>Default,NoAvatar:()=>NoAvatar,__namedExportsOrder:()=>__namedExportsOrder,default:()=>UserMenu_stories});__webpack_require__("./node_modules/react/index.js");var AuthContext=__webpack_require__("./src/context/AuthContext.js"),chunk_AYJ5UCUI=__webpack_require__("./node_modules/react-router/dist/development/chunk-AYJ5UCUI.mjs"),Image=__webpack_require__("./node_modules/react-bootstrap/esm/Image.js"),Dropdown=__webpack_require__("./node_modules/react-bootstrap/esm/Dropdown.js"),index_es=__webpack_require__("./node_modules/@fortawesome/react-fontawesome/index.es.js"),free_solid_svg_icons=__webpack_require__("./node_modules/@fortawesome/free-solid-svg-icons/index.mjs"),jsx_runtime=__webpack_require__("./node_modules/react/jsx-runtime.js");function UserMenu(){var _user$identities;const{user,logout}=(0,AuthContext.As)(),navigate=(0,chunk_AYJ5UCUI.Zp)(),toggleIcon=null!=user&&user.avatarUrl?(0,jsx_runtime.jsx)(Image.A,{src:user.avatarUrl,roundedCircle:!0,width:32,height:32}):(0,jsx_runtime.jsx)(index_es.g,{icon:free_solid_svg_icons.VFr,size:"2x",style:{color:"#fff"}});return(0,jsx_runtime.jsxs)(Dropdown.A,{align:"end",children:[(0,jsx_runtime.jsx)(Dropdown.A.Toggle,{variant:"light",id:"dropdown-user-menu",className:"d-flex align-items-center p-0 border-0 bg-transparent",children:toggleIcon}),(0,jsx_runtime.jsxs)(Dropdown.A.Menu,{children:[(0,jsx_runtime.jsx)(Dropdown.A.Item,{onClick:()=>navigate("/profile"),children:"My Profile"}),(null==user||null===(_user$identities=user.identities)||void 0===_user$identities?void 0:_user$identities.some((id=>"email"===id.provider)))&&(0,jsx_runtime.jsx)(Dropdown.A.Item,{onClick:()=>navigate("/profile/password"),children:"Change Password"}),(0,jsx_runtime.jsxs)(Dropdown.A.Item,{onClick:()=>navigate("/subscription"),children:["Subscription",user.plan]}),(0,jsx_runtime.jsx)(Dropdown.A.Divider,{}),(0,jsx_runtime.jsx)(Dropdown.A.Item,{onClick:logout,children:"Logout"})]})]})}UserMenu.__docgenInfo={description:"",methods:[],displayName:"UserMenu"};const UserMenu_stories={title:"Components/UserMenu",component:UserMenu,decorators:[(Story,_ref)=>{let{args}=_ref;return(0,jsx_runtime.jsx)(AuthContext.cy.Provider,{value:args,children:(0,jsx_runtime.jsx)(Story,{})})}],argTypes:{logout:{action:"logout"}}},Template=args=>(0,jsx_runtime.jsx)(UserMenu,{}),Default=Template.bind({});Default.args={user:{plan:"proA",avatarUrl:"https://via.placeholder.com/32"},logout:()=>{}};const NoAvatar=Template.bind({});NoAvatar.args={user:{plan:"free",avatarUrl:""},logout:()=>{}};const __namedExportsOrder=["Default","NoAvatar"];Default.parameters={...Default.parameters,docs:{...Default.parameters?.docs,source:{originalSource:"args => <UserMenu />",...Default.parameters?.docs?.source}}},NoAvatar.parameters={...NoAvatar.parameters,docs:{...NoAvatar.parameters?.docs,source:{originalSource:"args => <UserMenu />",...NoAvatar.parameters?.docs?.source}}}}}]);