"use strict";(self.webpackChunkclient=self.webpackChunkclient||[]).push([[758],{"./node_modules/@restart/hooks/esm/useWillUnmount.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.d(__webpack_exports__,{A:()=>useWillUnmount});var react=__webpack_require__("./node_modules/react/index.js");function useWillUnmount(fn){const onUnmount=function useUpdatedRef(value){const valueRef=(0,react.useRef)(value);return valueRef.current=value,valueRef}(fn);(0,react.useEffect)((()=>()=>onUnmount.current()),[])}},"./node_modules/@restart/ui/esm/ImperativeTransition.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.d(__webpack_exports__,{Yc:()=>renderTransition});var useMergedRefs=__webpack_require__("./node_modules/@restart/ui/node_modules/@restart/hooks/esm/useMergedRefs.js"),useEventCallback=__webpack_require__("./node_modules/@restart/ui/node_modules/@restart/hooks/esm/useEventCallback.js"),useIsomorphicEffect=__webpack_require__("./node_modules/@restart/ui/node_modules/@restart/hooks/esm/useIsomorphicEffect.js"),react=__webpack_require__("./node_modules/react/index.js"),utils=__webpack_require__("./node_modules/@restart/ui/esm/utils.js");const esm_NoopTransition=function NoopTransition({children,in:inProp,onExited,mountOnEnter,unmountOnExit}){const ref=(0,react.useRef)(null),hasEnteredRef=(0,react.useRef)(inProp),handleExited=(0,useEventCallback.A)(onExited);(0,react.useEffect)((()=>{inProp?hasEnteredRef.current=!0:handleExited(ref.current)}),[inProp,handleExited]);const combinedRef=(0,useMergedRefs.A)(ref,(0,utils.am)(children)),child=(0,react.cloneElement)(children,{ref:combinedRef});return inProp?child:unmountOnExit||!hasEnteredRef.current&&mountOnEnter?null:child},_excluded=["onEnter","onEntering","onEntered","onExit","onExiting","onExited","addEndListener","children"];var jsx_runtime=__webpack_require__("./node_modules/react/jsx-runtime.js");const RTGTransition_excluded=["component"];const esm_RTGTransition=react.forwardRef(((_ref,ref)=>{let{component:Component}=_ref;const transitionProps=function useRTGTransitionProps(_ref){let{onEnter,onEntering,onEntered,onExit,onExiting,onExited,addEndListener,children}=_ref,props=function _objectWithoutPropertiesLoose(r,e){if(null==r)return{};var t={};for(var n in r)if({}.hasOwnProperty.call(r,n)){if(e.indexOf(n)>=0)continue;t[n]=r[n]}return t}(_ref,_excluded);const nodeRef=(0,react.useRef)(null),mergedRef=(0,useMergedRefs.A)(nodeRef,(0,utils.am)(children)),normalize=callback=>param=>{callback&&nodeRef.current&&callback(nodeRef.current,param)},handleEnter=(0,react.useCallback)(normalize(onEnter),[onEnter]),handleEntering=(0,react.useCallback)(normalize(onEntering),[onEntering]),handleEntered=(0,react.useCallback)(normalize(onEntered),[onEntered]),handleExit=(0,react.useCallback)(normalize(onExit),[onExit]),handleExiting=(0,react.useCallback)(normalize(onExiting),[onExiting]),handleExited=(0,react.useCallback)(normalize(onExited),[onExited]),handleAddEndListener=(0,react.useCallback)(normalize(addEndListener),[addEndListener]);return Object.assign({},props,{nodeRef},onEnter&&{onEnter:handleEnter},onEntering&&{onEntering:handleEntering},onEntered&&{onEntered:handleEntered},onExit&&{onExit:handleExit},onExiting&&{onExiting:handleExiting},onExited&&{onExited:handleExited},addEndListener&&{addEndListener:handleAddEndListener},{children:"function"==typeof children?(status,innerProps)=>children(status,Object.assign({},innerProps,{ref:mergedRef})):(0,react.cloneElement)(children,{ref:mergedRef})})}(function RTGTransition_objectWithoutPropertiesLoose(r,e){if(null==r)return{};var t={};for(var n in r)if({}.hasOwnProperty.call(r,n)){if(e.indexOf(n)>=0)continue;t[n]=r[n]}return t}(_ref,RTGTransition_excluded));return(0,jsx_runtime.jsx)(Component,Object.assign({ref},transitionProps))}));function ImperativeTransition({children,in:inProp,onExited,onEntered,transition}){const[exited,setExited]=(0,react.useState)(!inProp);inProp&&exited&&setExited(!1);const ref=function useTransition({in:inProp,onTransition}){const ref=(0,react.useRef)(null),isInitialRef=(0,react.useRef)(!0),handleTransition=(0,useEventCallback.A)(onTransition);return(0,useIsomorphicEffect.A)((()=>{if(!ref.current)return;let stale=!1;return handleTransition({in:inProp,element:ref.current,initial:isInitialRef.current,isStale:()=>stale}),()=>{stale=!0}}),[inProp,handleTransition]),(0,useIsomorphicEffect.A)((()=>(isInitialRef.current=!1,()=>{isInitialRef.current=!0})),[]),ref}({in:!!inProp,onTransition:options=>{Promise.resolve(transition(options)).then((()=>{options.isStale()||(options.in?null==onEntered||onEntered(options.element,options.initial):(setExited(!0),null==onExited||onExited(options.element)))}),(error=>{throw options.in||setExited(!0),error}))}}),combinedRef=(0,useMergedRefs.A)(ref,(0,utils.am)(children));return exited&&!inProp?null:(0,react.cloneElement)(children,{ref:combinedRef})}function renderTransition(component,runTransition,props){return component?(0,jsx_runtime.jsx)(esm_RTGTransition,Object.assign({},props,{component})):runTransition?(0,jsx_runtime.jsx)(ImperativeTransition,Object.assign({},props,{transition:runTransition})):(0,jsx_runtime.jsx)(esm_NoopTransition,Object.assign({},props))}},"./node_modules/@restart/ui/esm/useWaitForDOMRef.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.d(__webpack_exports__,{A:()=>useWaitForDOMRef});var dom_helpers_ownerDocument__WEBPACK_IMPORTED_MODULE_2__=__webpack_require__("./node_modules/dom-helpers/esm/ownerDocument.js"),dom_helpers_canUseDOM__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("./node_modules/dom-helpers/esm/canUseDOM.js"),react__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__("./node_modules/react/index.js"),_useWindow__WEBPACK_IMPORTED_MODULE_3__=__webpack_require__("./node_modules/@restart/ui/esm/useWindow.js");const resolveContainerRef=(ref,document)=>dom_helpers_canUseDOM__WEBPACK_IMPORTED_MODULE_0__.A?null==ref?(document||(0,dom_helpers_ownerDocument__WEBPACK_IMPORTED_MODULE_2__.A)()).body:("function"==typeof ref&&(ref=ref()),ref&&"current"in ref&&(ref=ref.current),ref&&("nodeType"in ref||ref.getBoundingClientRect)?ref:null):null;function useWaitForDOMRef(ref,onResolved){const window=(0,_useWindow__WEBPACK_IMPORTED_MODULE_3__.A)(),[resolvedRef,setRef]=(0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((()=>resolveContainerRef(ref,null==window?void 0:window.document)));if(!resolvedRef){const earlyRef=resolveContainerRef(ref);earlyRef&&setRef(earlyRef)}return(0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)((()=>{onResolved&&resolvedRef&&onResolved(resolvedRef)}),[onResolved,resolvedRef]),(0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)((()=>{const nextRef=resolveContainerRef(ref);nextRef!==resolvedRef&&setRef(nextRef)}),[ref,resolvedRef]),resolvedRef}},"./node_modules/@restart/ui/node_modules/@restart/hooks/esm/useMergedRefs.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.d(__webpack_exports__,{A:()=>__WEBPACK_DEFAULT_EXPORT__});var react__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("./node_modules/react/index.js");const toFnRef=ref=>ref&&"function"!=typeof ref?value=>{ref.current=value}:ref;const __WEBPACK_DEFAULT_EXPORT__=function useMergedRefs(refA,refB){return(0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)((()=>function mergeRefs(refA,refB){const a=toFnRef(refA),b=toFnRef(refB);return value=>{a&&a(value),b&&b(value)}}(refA,refB)),[refA,refB])}},"./node_modules/dom-helpers/esm/hasClass.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{function hasClass(element,className){return element.classList?!!className&&element.classList.contains(className):-1!==(" "+(element.className.baseVal||element.className)+" ").indexOf(" "+className+" ")}__webpack_require__.d(__webpack_exports__,{A:()=>hasClass})},"./node_modules/react-bootstrap/esm/Modal.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.d(__webpack_exports__,{A:()=>react_bootstrap_esm_Modal});var size,classnames=__webpack_require__("./node_modules/classnames/index.js"),classnames_default=__webpack_require__.n(classnames),addEventListener=__webpack_require__("./node_modules/dom-helpers/esm/addEventListener.js"),canUseDOM=__webpack_require__("./node_modules/dom-helpers/esm/canUseDOM.js"),ownerDocument=__webpack_require__("./node_modules/dom-helpers/esm/ownerDocument.js"),removeEventListener=__webpack_require__("./node_modules/dom-helpers/esm/removeEventListener.js");function scrollbarSize(recalc){if((!size&&0!==size||recalc)&&canUseDOM.A){var scrollDiv=document.createElement("div");scrollDiv.style.position="absolute",scrollDiv.style.top="-9999px",scrollDiv.style.width="50px",scrollDiv.style.height="50px",scrollDiv.style.overflow="scroll",document.body.appendChild(scrollDiv),size=scrollDiv.offsetWidth-scrollDiv.clientWidth,document.body.removeChild(scrollDiv)}return size}var react=__webpack_require__("./node_modules/react/index.js");var useEventCallback=__webpack_require__("./node_modules/@restart/hooks/esm/useEventCallback.js"),useMergedRefs=__webpack_require__("./node_modules/@restart/hooks/esm/useMergedRefs.js"),useWillUnmount=__webpack_require__("./node_modules/@restart/hooks/esm/useWillUnmount.js"),transitionEnd=__webpack_require__("./node_modules/dom-helpers/esm/transitionEnd.js");function activeElement(doc){void 0===doc&&(doc=(0,ownerDocument.A)());try{var active=doc.activeElement;return active&&active.nodeName?active:null}catch(e){return doc.body}}var contains=__webpack_require__("./node_modules/dom-helpers/esm/contains.js"),listen=__webpack_require__("./node_modules/dom-helpers/esm/listen.js"),react_dom=__webpack_require__("./node_modules/react-dom/index.js"),useMounted=__webpack_require__("./node_modules/@restart/ui/node_modules/@restart/hooks/esm/useMounted.js");function useWillUnmount_useWillUnmount(fn){const onUnmount=function useUpdatedRef(value){const valueRef=(0,react.useRef)(value);return valueRef.current=value,valueRef}(fn);(0,react.useEffect)((()=>()=>onUnmount.current()),[])}var usePrevious=__webpack_require__("./node_modules/@restart/ui/node_modules/@restart/hooks/esm/usePrevious.js"),esm_useEventCallback=__webpack_require__("./node_modules/@restart/ui/node_modules/@restart/hooks/esm/useEventCallback.js"),css=__webpack_require__("./node_modules/dom-helpers/esm/css.js");const OPEN_DATA_ATTRIBUTE=(0,__webpack_require__("./node_modules/@restart/ui/esm/DataKey.js").sE)("modal-open");const esm_ModalManager=class ModalManager{constructor({ownerDocument,handleContainerOverflow=!0,isRTL=!1}={}){this.handleContainerOverflow=handleContainerOverflow,this.isRTL=isRTL,this.modals=[],this.ownerDocument=ownerDocument}getScrollbarWidth(){return function getBodyScrollbarWidth(ownerDocument=document){const window=ownerDocument.defaultView;return Math.abs(window.innerWidth-ownerDocument.documentElement.clientWidth)}(this.ownerDocument)}getElement(){return(this.ownerDocument||document).body}setModalAttributes(_modal){}removeModalAttributes(_modal){}setContainerStyle(containerState){const style={overflow:"hidden"},paddingProp=this.isRTL?"paddingLeft":"paddingRight",container=this.getElement();containerState.style={overflow:container.style.overflow,[paddingProp]:container.style[paddingProp]},containerState.scrollBarWidth&&(style[paddingProp]=`${parseInt((0,css.A)(container,paddingProp)||"0",10)+containerState.scrollBarWidth}px`),container.setAttribute(OPEN_DATA_ATTRIBUTE,""),(0,css.A)(container,style)}reset(){[...this.modals].forEach((m=>this.remove(m)))}removeContainerStyle(containerState){const container=this.getElement();container.removeAttribute(OPEN_DATA_ATTRIBUTE),Object.assign(container.style,containerState.style)}add(modal){let modalIdx=this.modals.indexOf(modal);return-1!==modalIdx?modalIdx:(modalIdx=this.modals.length,this.modals.push(modal),this.setModalAttributes(modal),0!==modalIdx||(this.state={scrollBarWidth:this.getScrollbarWidth(),style:{}},this.handleContainerOverflow&&this.setContainerStyle(this.state)),modalIdx)}remove(modal){const modalIdx=this.modals.indexOf(modal);-1!==modalIdx&&(this.modals.splice(modalIdx,1),!this.modals.length&&this.handleContainerOverflow&&this.removeContainerStyle(this.state),this.removeModalAttributes(modal))}isTopModal(modal){return!!this.modals.length&&this.modals[this.modals.length-1]===modal}};var useWaitForDOMRef=__webpack_require__("./node_modules/@restart/ui/esm/useWaitForDOMRef.js"),useWindow=__webpack_require__("./node_modules/@restart/ui/esm/useWindow.js"),ImperativeTransition=__webpack_require__("./node_modules/@restart/ui/esm/ImperativeTransition.js"),utils=__webpack_require__("./node_modules/@restart/ui/esm/utils.js"),jsx_runtime=__webpack_require__("./node_modules/react/jsx-runtime.js");const _excluded=["show","role","className","style","children","backdrop","keyboard","onBackdropClick","onEscapeKeyDown","transition","runTransition","backdropTransition","runBackdropTransition","autoFocus","enforceFocus","restoreFocus","restoreFocusOptions","renderDialog","renderBackdrop","manager","container","onShow","onHide","onExit","onExited","onExiting","onEnter","onEntering","onEntered"];let manager;function useModalManager(provided){const window=(0,useWindow.A)(),modalManager=provided||function getManager(window){return manager||(manager=new esm_ModalManager({ownerDocument:null==window?void 0:window.document})),manager}(window),modal=(0,react.useRef)({dialog:null,backdrop:null});return Object.assign(modal.current,{add:()=>modalManager.add(modal.current),remove:()=>modalManager.remove(modal.current),isTopModal:()=>modalManager.isTopModal(modal.current),setDialogRef:(0,react.useCallback)((ref=>{modal.current.dialog=ref}),[]),setBackdropRef:(0,react.useCallback)((ref=>{modal.current.backdrop=ref}),[])})}const Modal=(0,react.forwardRef)(((_ref,ref)=>{let{show=!1,role="dialog",className,style,children,backdrop=!0,keyboard=!0,onBackdropClick,onEscapeKeyDown,transition,runTransition,backdropTransition,runBackdropTransition,autoFocus=!0,enforceFocus=!0,restoreFocus=!0,restoreFocusOptions,renderDialog,renderBackdrop=props=>(0,jsx_runtime.jsx)("div",Object.assign({},props)),manager:providedManager,container:containerRef,onShow,onHide=()=>{},onExit,onExited,onExiting,onEnter,onEntering,onEntered}=_ref,rest=function _objectWithoutPropertiesLoose(r,e){if(null==r)return{};var t={};for(var n in r)if({}.hasOwnProperty.call(r,n)){if(e.indexOf(n)>=0)continue;t[n]=r[n]}return t}(_ref,_excluded);const ownerWindow=(0,useWindow.A)(),container=(0,useWaitForDOMRef.A)(containerRef),modal=useModalManager(providedManager),isMounted=(0,useMounted.A)(),prevShow=(0,usePrevious.A)(show),[exited,setExited]=(0,react.useState)(!show),lastFocusRef=(0,react.useRef)(null);(0,react.useImperativeHandle)(ref,(()=>modal),[modal]),canUseDOM.A&&!prevShow&&show&&(lastFocusRef.current=activeElement(null==ownerWindow?void 0:ownerWindow.document)),show&&exited&&setExited(!1);const handleShow=(0,esm_useEventCallback.A)((()=>{if(modal.add(),removeKeydownListenerRef.current=(0,listen.A)(document,"keydown",handleDocumentKeyDown),removeFocusListenerRef.current=(0,listen.A)(document,"focus",(()=>setTimeout(handleEnforceFocus)),!0),onShow&&onShow(),autoFocus){var _modal$dialog$ownerDo,_modal$dialog;const currentActiveElement=activeElement(null!=(_modal$dialog$ownerDo=null==(_modal$dialog=modal.dialog)?void 0:_modal$dialog.ownerDocument)?_modal$dialog$ownerDo:null==ownerWindow?void 0:ownerWindow.document);modal.dialog&&currentActiveElement&&!(0,contains.A)(modal.dialog,currentActiveElement)&&(lastFocusRef.current=currentActiveElement,modal.dialog.focus())}})),handleHide=(0,esm_useEventCallback.A)((()=>{var _lastFocusRef$current;(modal.remove(),null==removeKeydownListenerRef.current||removeKeydownListenerRef.current(),null==removeFocusListenerRef.current||removeFocusListenerRef.current(),restoreFocus)&&(null==(_lastFocusRef$current=lastFocusRef.current)||null==_lastFocusRef$current.focus||_lastFocusRef$current.focus(restoreFocusOptions),lastFocusRef.current=null)}));(0,react.useEffect)((()=>{show&&container&&handleShow()}),[show,container,handleShow]),(0,react.useEffect)((()=>{exited&&handleHide()}),[exited,handleHide]),useWillUnmount_useWillUnmount((()=>{handleHide()}));const handleEnforceFocus=(0,esm_useEventCallback.A)((()=>{if(!enforceFocus||!isMounted()||!modal.isTopModal())return;const currentActiveElement=activeElement(null==ownerWindow?void 0:ownerWindow.document);modal.dialog&&currentActiveElement&&!(0,contains.A)(modal.dialog,currentActiveElement)&&modal.dialog.focus()})),handleBackdropClick=(0,esm_useEventCallback.A)((e=>{e.target===e.currentTarget&&(null==onBackdropClick||onBackdropClick(e),!0===backdrop&&onHide())})),handleDocumentKeyDown=(0,esm_useEventCallback.A)((e=>{keyboard&&(0,utils.v$)(e)&&modal.isTopModal()&&(null==onEscapeKeyDown||onEscapeKeyDown(e),e.defaultPrevented||onHide())})),removeFocusListenerRef=(0,react.useRef)(),removeKeydownListenerRef=(0,react.useRef)();if(!container)return null;const dialogProps=Object.assign({role,ref:modal.setDialogRef,"aria-modal":"dialog"===role||void 0},rest,{style,className,tabIndex:-1});let dialog=renderDialog?renderDialog(dialogProps):(0,jsx_runtime.jsx)("div",Object.assign({},dialogProps,{children:react.cloneElement(children,{role:"document"})}));dialog=(0,ImperativeTransition.Yc)(transition,runTransition,{unmountOnExit:!0,mountOnEnter:!0,appear:!0,in:!!show,onExit,onExiting,onExited:(...args)=>{setExited(!0),null==onExited||onExited(...args)},onEnter,onEntering,onEntered,children:dialog});let backdropElement=null;return backdrop&&(backdropElement=renderBackdrop({ref:modal.setBackdropRef,onClick:handleBackdropClick}),backdropElement=(0,ImperativeTransition.Yc)(backdropTransition,runBackdropTransition,{in:!!show,appear:!0,mountOnEnter:!0,unmountOnExit:!0,children:backdropElement})),(0,jsx_runtime.jsx)(jsx_runtime.Fragment,{children:react_dom.createPortal((0,jsx_runtime.jsxs)(jsx_runtime.Fragment,{children:[backdropElement,dialog]}),container)})}));Modal.displayName="Modal";const esm_Modal=Object.assign(Modal,{Manager:esm_ModalManager});var hasClass=__webpack_require__("./node_modules/dom-helpers/esm/hasClass.js");var querySelectorAll=__webpack_require__("./node_modules/dom-helpers/esm/querySelectorAll.js");function replaceClassName(origClass,classToRemove){return origClass.replace(new RegExp("(^|\\s)"+classToRemove+"(?:\\s|$)","g"),"$1").replace(/\s+/g," ").replace(/^\s*|\s*$/g,"")}const Selector_FIXED_CONTENT=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",Selector_STICKY_CONTENT=".sticky-top",Selector_NAVBAR_TOGGLER=".navbar-toggler";class BootstrapModalManager extends esm_ModalManager{adjustAndStore(prop,element,adjust){const actual=element.style[prop];element.dataset[prop]=actual,(0,css.A)(element,{[prop]:`${parseFloat((0,css.A)(element,prop))+adjust}px`})}restore(prop,element){const value=element.dataset[prop];void 0!==value&&(delete element.dataset[prop],(0,css.A)(element,{[prop]:value}))}setContainerStyle(containerState){super.setContainerStyle(containerState);const container=this.getElement();if(function addClass(element,className){element.classList?element.classList.add(className):(0,hasClass.A)(element,className)||("string"==typeof element.className?element.className=element.className+" "+className:element.setAttribute("class",(element.className&&element.className.baseVal||"")+" "+className))}(container,"modal-open"),!containerState.scrollBarWidth)return;const paddingProp=this.isRTL?"paddingLeft":"paddingRight",marginProp=this.isRTL?"marginLeft":"marginRight";(0,querySelectorAll.A)(container,Selector_FIXED_CONTENT).forEach((el=>this.adjustAndStore(paddingProp,el,containerState.scrollBarWidth))),(0,querySelectorAll.A)(container,Selector_STICKY_CONTENT).forEach((el=>this.adjustAndStore(marginProp,el,-containerState.scrollBarWidth))),(0,querySelectorAll.A)(container,Selector_NAVBAR_TOGGLER).forEach((el=>this.adjustAndStore(marginProp,el,containerState.scrollBarWidth)))}removeContainerStyle(containerState){super.removeContainerStyle(containerState);const container=this.getElement();!function removeClass(element,className){element.classList?element.classList.remove(className):"string"==typeof element.className?element.className=replaceClassName(element.className,className):element.setAttribute("class",replaceClassName(element.className&&element.className.baseVal||"",className))}(container,"modal-open");const paddingProp=this.isRTL?"paddingLeft":"paddingRight",marginProp=this.isRTL?"marginLeft":"marginRight";(0,querySelectorAll.A)(container,Selector_FIXED_CONTENT).forEach((el=>this.restore(paddingProp,el))),(0,querySelectorAll.A)(container,Selector_STICKY_CONTENT).forEach((el=>this.restore(marginProp,el))),(0,querySelectorAll.A)(container,Selector_NAVBAR_TOGGLER).forEach((el=>this.restore(marginProp,el)))}}let sharedManager;var Fade=__webpack_require__("./node_modules/react-bootstrap/esm/Fade.js"),ThemeProvider=__webpack_require__("./node_modules/react-bootstrap/esm/ThemeProvider.js");const ModalBody=react.forwardRef((({className,bsPrefix,as:Component="div",...props},ref)=>(bsPrefix=(0,ThemeProvider.oU)(bsPrefix,"modal-body"),(0,jsx_runtime.jsx)(Component,{ref,className:classnames_default()(className,bsPrefix),...props}))));ModalBody.displayName="ModalBody";const esm_ModalBody=ModalBody,esm_ModalContext=react.createContext({onHide(){}}),ModalDialog=react.forwardRef((({bsPrefix,className,contentClassName,centered,size,fullscreen,children,scrollable,...props},ref)=>{const dialogClass=`${bsPrefix=(0,ThemeProvider.oU)(bsPrefix,"modal")}-dialog`,fullScreenClass="string"==typeof fullscreen?`${bsPrefix}-fullscreen-${fullscreen}`:`${bsPrefix}-fullscreen`;return(0,jsx_runtime.jsx)("div",{...props,ref,className:classnames_default()(dialogClass,className,size&&`${bsPrefix}-${size}`,centered&&`${dialogClass}-centered`,scrollable&&`${dialogClass}-scrollable`,fullscreen&&fullScreenClass),children:(0,jsx_runtime.jsx)("div",{className:classnames_default()(`${bsPrefix}-content`,contentClassName),children})})}));ModalDialog.displayName="ModalDialog";const esm_ModalDialog=ModalDialog,ModalFooter=react.forwardRef((({className,bsPrefix,as:Component="div",...props},ref)=>(bsPrefix=(0,ThemeProvider.oU)(bsPrefix,"modal-footer"),(0,jsx_runtime.jsx)(Component,{ref,className:classnames_default()(className,bsPrefix),...props}))));ModalFooter.displayName="ModalFooter";const esm_ModalFooter=ModalFooter;var CloseButton=__webpack_require__("./node_modules/react-bootstrap/esm/CloseButton.js");const esm_AbstractModalHeader=react.forwardRef((({closeLabel="Close",closeVariant,closeButton=!1,onHide,children,...props},ref)=>{const context=(0,react.useContext)(esm_ModalContext),handleClick=(0,useEventCallback.A)((()=>{null==context||context.onHide(),null==onHide||onHide()}));return(0,jsx_runtime.jsxs)("div",{ref,...props,children:[children,closeButton&&(0,jsx_runtime.jsx)(CloseButton.A,{"aria-label":closeLabel,variant:closeVariant,onClick:handleClick})]})})),ModalHeader=react.forwardRef((({bsPrefix,className,closeLabel="Close",closeButton=!1,...props},ref)=>(bsPrefix=(0,ThemeProvider.oU)(bsPrefix,"modal-header"),(0,jsx_runtime.jsx)(esm_AbstractModalHeader,{ref,...props,className:classnames_default()(className,bsPrefix),closeLabel,closeButton}))));ModalHeader.displayName="ModalHeader";const esm_ModalHeader=ModalHeader;const DivStyledAsH4=(0,__webpack_require__("./node_modules/react-bootstrap/esm/divWithClassName.js").A)("h4"),ModalTitle=react.forwardRef((({className,bsPrefix,as:Component=DivStyledAsH4,...props},ref)=>(bsPrefix=(0,ThemeProvider.oU)(bsPrefix,"modal-title"),(0,jsx_runtime.jsx)(Component,{ref,className:classnames_default()(className,bsPrefix),...props}))));ModalTitle.displayName="ModalTitle";const esm_ModalTitle=ModalTitle;function DialogTransition(props){return(0,jsx_runtime.jsx)(Fade.A,{...props,timeout:null})}function BackdropTransition(props){return(0,jsx_runtime.jsx)(Fade.A,{...props,timeout:null})}const Modal_Modal=react.forwardRef((({bsPrefix,className,style,dialogClassName,contentClassName,children,dialogAs:Dialog=esm_ModalDialog,"data-bs-theme":dataBsTheme,"aria-labelledby":ariaLabelledby,"aria-describedby":ariaDescribedby,"aria-label":ariaLabel,show=!1,animation=!0,backdrop=!0,keyboard=!0,onEscapeKeyDown,onShow,onHide,container,autoFocus=!0,enforceFocus=!0,restoreFocus=!0,restoreFocusOptions,onEntered,onExit,onExiting,onEnter,onEntering,onExited,backdropClassName,manager:propsManager,...props},ref)=>{const[modalStyle,setStyle]=(0,react.useState)({}),[animateStaticModal,setAnimateStaticModal]=(0,react.useState)(!1),waitingForMouseUpRef=(0,react.useRef)(!1),ignoreBackdropClickRef=(0,react.useRef)(!1),removeStaticModalAnimationRef=(0,react.useRef)(null),[modal,setModalRef]=function useCallbackRef(){return(0,react.useState)(null)}(),mergedRef=(0,useMergedRefs.A)(ref,setModalRef),handleHide=(0,useEventCallback.A)(onHide),isRTL=(0,ThemeProvider.Wz)();bsPrefix=(0,ThemeProvider.oU)(bsPrefix,"modal");const modalContext=(0,react.useMemo)((()=>({onHide:handleHide})),[handleHide]);function getModalManager(){return propsManager||function getSharedManager(options){return sharedManager||(sharedManager=new BootstrapModalManager(options)),sharedManager}({isRTL})}function updateDialogStyle(node){if(!canUseDOM.A)return;const containerIsOverflowing=getModalManager().getScrollbarWidth()>0,modalIsOverflowing=node.scrollHeight>(0,ownerDocument.A)(node).documentElement.clientHeight;setStyle({paddingRight:containerIsOverflowing&&!modalIsOverflowing?scrollbarSize():void 0,paddingLeft:!containerIsOverflowing&&modalIsOverflowing?scrollbarSize():void 0})}const handleWindowResize=(0,useEventCallback.A)((()=>{modal&&updateDialogStyle(modal.dialog)}));(0,useWillUnmount.A)((()=>{(0,removeEventListener.A)(window,"resize",handleWindowResize),null==removeStaticModalAnimationRef.current||removeStaticModalAnimationRef.current()}));const handleDialogMouseDown=()=>{waitingForMouseUpRef.current=!0},handleMouseUp=e=>{waitingForMouseUpRef.current&&modal&&e.target===modal.dialog&&(ignoreBackdropClickRef.current=!0),waitingForMouseUpRef.current=!1},handleStaticModalAnimation=()=>{setAnimateStaticModal(!0),removeStaticModalAnimationRef.current=(0,transitionEnd.A)(modal.dialog,(()=>{setAnimateStaticModal(!1)}))},handleClick=e=>{"static"!==backdrop?ignoreBackdropClickRef.current||e.target!==e.currentTarget?ignoreBackdropClickRef.current=!1:null==onHide||onHide():(e=>{e.target===e.currentTarget&&handleStaticModalAnimation()})(e)},renderBackdrop=(0,react.useCallback)((backdropProps=>(0,jsx_runtime.jsx)("div",{...backdropProps,className:classnames_default()(`${bsPrefix}-backdrop`,backdropClassName,!animation&&"show")})),[animation,backdropClassName,bsPrefix]),baseModalStyle={...style,...modalStyle};baseModalStyle.display="block";return(0,jsx_runtime.jsx)(esm_ModalContext.Provider,{value:modalContext,children:(0,jsx_runtime.jsx)(esm_Modal,{show,ref:mergedRef,backdrop,container,keyboard:!0,autoFocus,enforceFocus,restoreFocus,restoreFocusOptions,onEscapeKeyDown:e=>{keyboard?null==onEscapeKeyDown||onEscapeKeyDown(e):(e.preventDefault(),"static"===backdrop&&handleStaticModalAnimation())},onShow,onHide,onEnter:(node,isAppearing)=>{node&&updateDialogStyle(node),null==onEnter||onEnter(node,isAppearing)},onEntering:(node,isAppearing)=>{null==onEntering||onEntering(node,isAppearing),(0,addEventListener.Ay)(window,"resize",handleWindowResize)},onEntered,onExit:node=>{null==removeStaticModalAnimationRef.current||removeStaticModalAnimationRef.current(),null==onExit||onExit(node)},onExiting,onExited:node=>{node&&(node.style.display=""),null==onExited||onExited(node),(0,removeEventListener.A)(window,"resize",handleWindowResize)},manager:getModalManager(),transition:animation?DialogTransition:void 0,backdropTransition:animation?BackdropTransition:void 0,renderBackdrop,renderDialog:dialogProps=>(0,jsx_runtime.jsx)("div",{role:"dialog",...dialogProps,style:baseModalStyle,className:classnames_default()(className,bsPrefix,animateStaticModal&&`${bsPrefix}-static`,!animation&&"show"),onClick:backdrop?handleClick:void 0,onMouseUp:handleMouseUp,"data-bs-theme":dataBsTheme,"aria-label":ariaLabel,"aria-labelledby":ariaLabelledby,"aria-describedby":ariaDescribedby,children:(0,jsx_runtime.jsx)(Dialog,{...props,onMouseDown:handleDialogMouseDown,className:dialogClassName,contentClassName,children})})})})}));Modal_Modal.displayName="Modal";const react_bootstrap_esm_Modal=Object.assign(Modal_Modal,{Body:esm_ModalBody,Header:esm_ModalHeader,Title:esm_ModalTitle,Footer:esm_ModalFooter,Dialog:esm_ModalDialog,TRANSITION_DURATION:300,BACKDROP_TRANSITION_DURATION:150})}}]);