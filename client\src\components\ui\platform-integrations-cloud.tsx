"use client"

import React, { useState, useEffect, useMemo } from "react"
import { useTheme } from "next-themes"
import {
  Cloud,
  fetchSimpleIcons,
  ICloud,
  renderSimpleIcon,
  SimpleIcon,
} from "react-icon-cloud"

export const cloudProps: Omit<ICloud, "children"> = {
  containerProps: {
    style: {
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      width: "100%",
      paddingTop: 40,
    },
  },
  options: {
    reverse: true,
    depth: 1,
    wheelZoom: false,
    imageScale: 2,
    activeCursor: "default",
    tooltip: "native",
    initial: [0.1, -0.1],
    clickToFront: 500,
    tooltipDelay: 0,
    outlineColour: "#0000",
    maxSpeed: 0.04,
    minSpeed: 0.02,
    // dragControl: false,
  },
}

export const renderCustomIcon = (icon: any, theme: string) => {
  return renderSimpleIcon({
    icon,
    bgHex: theme === "light" ? "#f3f2ef" : "#080510",
    fallbackHex: theme === "light" ? "#6e6e73" : "#ffffff",
    minContrastRatio: theme === "dark" ? 2 : 1.2,
    size: 42,
    aProps: {
      href: undefined,
      target: undefined,
      rel: undefined,
      onClick: (e: any) => e.preventDefault(),
    },
  })
}

export type DynamicCloudProps = {
  iconSlugs?: string[]
}

type IconData = Awaited<ReturnType<typeof fetchSimpleIcons>>

// ------------- Platform Icons Definition -------------
// 使用对象数组明确每个图标的渲染方式（simple | img）
const platformIcons = [
  { slug: "gmail", type: "simple" },
  { slug: "lazada", type: "simple" },
  { slug: "shopee", type: "simple" },
  { slug: "tiktok", type: "simple" },
  { slug: "facebookmessenger", type: "simple" },
  { slug: "whatsapp", type: "simple" },
  { slug: "instagram", type: "simple" },
  { slug: "facebook", type: "simple" },
  { slug: "webhook", type: "img", src: "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQQPKChwoZePELXWz8WqJ1r1tGBcIHZbWjQig&s" },
] as const

// 提取需要 simple-icons 的 slug 数组
const simpleIconSlugs = platformIcons
  .filter((i) => i.type === "simple")
  .map((i) => i.slug)

export function PlatformIntegrationsCloud({ iconSlugs = simpleIconSlugs }: DynamicCloudProps & { iconSlugs?: string[] }) {
  const [data, setData] = useState<IconData | null>(null)
  const { theme } = useTheme()

  // 只拉取 simple-icons
  useEffect(() => {
    fetchSimpleIcons({ slugs: simpleIconSlugs }).then(setData)
  }, [])

  const renderedIcons = useMemo(() => {
    // 如果 simple-icons 还没拉到，只渲染自定义图片，避免空白
    const simpleIconsReady = !!data

    return platformIcons.map((icon) => {
      if (icon.type === "img") {
        return (
          <img
            key={icon.slug}
            src={icon.src}
            alt={icon.slug}
            title={icon.slug.charAt(0).toUpperCase() + icon.slug.slice(1)}
            style={{ width: 42, height: 42, borderRadius: 8, background: "#fff" }}
            crossOrigin="anonymous"
          />
        )
      }
      if (simpleIconsReady && data?.simpleIcons[icon.slug]) {
        return renderCustomIcon(data.simpleIcons[icon.slug] as any, theme || "light")
      }
      // simple icon 未就绪时返回占位符，避免布局抖动
      return (
        <span key={icon.slug} style={{ width: 42, height: 42 }} />
      )
    })
  }, [data, theme])

  return (
    // @ts-ignore
    <Cloud {...cloudProps}>
      <>{renderedIcons}</>
    </Cloud>
  )
}

// 更新Demo组件使用 platformIcons 自动构建图标
export function PlatformIntegrationsCloudDemo() {
  return (
    <div className="relative flex size-full max-w-lg items-center justify-center overflow-hidden rounded-lg border bg-background px-20 pb-20 pt-8 " data-testid="platform-integrations-cloud">
      <PlatformIntegrationsCloud />
    </div>
  )
}

// 集成展示section组件 - 供HomePage使用
export function IntegrationsSection() {
  return (
    <section className="py-20 px-4 bg-gradient-to-b from-background to-muted/20">
      <div className="container mx-auto max-w-4xl">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
            Powerful Platform Integrations
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Connect with your favorite platforms and tools to create seamless automated workflows
          </p>
        </div>

        {/* 居中显示图标云 */}
        <div className="flex justify-center">
          <div className="relative flex w-full max-w-lg items-center justify-center overflow-hidden rounded-lg bg-background px-8 pb-8 pt-8">
            <div className="w-full h-96 flex items-center justify-center">
              <PlatformIntegrationsCloud />
            </div>
          </div>
        </div>

        {/* 底部描述 */}
        <div className="text-center mt-12">
          <h3 className="text-xl font-semibold text-foreground mb-3">
            Seamless Integration Experience
          </h3>
          <p className="text-muted-foreground max-w-3xl mx-auto">
            Connect with hundreds of third-party applications and services through Webhook and API integrations,
            making your business processes more intelligent and automated.
          </p>
        </div>
      </div>
    </section>
  )
}


