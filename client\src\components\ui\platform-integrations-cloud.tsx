"use client"

import React, { useState, useEffect, Component, ReactNode } from "react"
import { IconCloud } from "./interactive-icon-cloud"

// 错误边界组件
interface ErrorBoundaryProps {
  children: ReactNode
  fallback: ReactNode
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void
}

interface ErrorBoundaryState {
  hasError: boolean
  error?: Error
}

class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('图标云组件渲染错误:', error, errorInfo)
    if (this.props.onError) {
      this.props.onError(error, errorInfo)
    }
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback
    }

    return this.props.children
  }
}

// 添加CSS动画
const styles = `
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
`

// 您平台提供的9个集成 - 使用确认存在的图标slug
// 您平台提供的9个集成 - 使用确认存在的正确图标名称
const platformIntegrations = [
  "whatsapp",
  "messenger",
  "shopee",
  "lazada", // ✅ 确认存在
  "gmail",
  "facebook",
  "instagram",
  "tiktok",
  "webhook" // ✅ 确认存在
]

interface PlatformIntegrationsCloudProps {
  className?: string
}

export function PlatformIntegrationsCloud({ className }: PlatformIntegrationsCloudProps) {
  const [showFallback, setShowFallback] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // 注入CSS样式
    const styleElement = document.createElement('style')
    styleElement.textContent = styles
    document.head.appendChild(styleElement)

    // 尝试加载图标云，如果失败则显示fallback
    const loadTimer = setTimeout(() => {
      console.log('🔄 尝试加载图标云...')
      setIsLoading(false)

      // 再给图标云5秒时间，如果还是没有显示则切换到fallback
      const fallbackTimer = setTimeout(() => {
        console.log('⚠️ 图标云加载超时，切换到fallback')
        setShowFallback(true)
      }, 5000)

      return () => clearTimeout(fallbackTimer)
    }, 1000)

    return () => {
      clearTimeout(loadTimer)
      if (document.head.contains(styleElement)) {
        document.head.removeChild(styleElement)
      }
    }
  }, [])

  if (isLoading) {
    return (
      <div className={`relative flex w-full max-w-lg items-center justify-center overflow-hidden rounded-lg bg-background px-8 pb-8 pt-8 ${className}`}>
        <div className="w-full h-96 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading platform integrations...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={`relative flex w-full max-w-lg items-center justify-center overflow-hidden rounded-lg bg-background px-8 pb-8 pt-8 ${className}`}>
      <div className="w-full h-96 flex items-center justify-center">
        {showFallback ? (
          <PlatformIconsFallback />
        ) : (
          <ErrorBoundary
            fallback={<PlatformIconsFallback />}
            onError={(error, errorInfo) => {
              console.error('❌ IconCloud渲染失败，切换到fallback:', error.message)
              setShowFallback(true)
            }}
          >
            <IconCloud iconSlugs={platformIntegrations} />
          </ErrorBoundary>
        )}
      </div>
    </div>
  )
}

// Fallback组件，显示静态图标网格
function PlatformIconsFallback() {
  const platforms = [
    { name: "WhatsApp", emoji: "💬", color: "text-green-500" },
    { name: "Messenger", emoji: "📱", color: "text-blue-500" },
    { name: "Instagram", emoji: "📸", color: "text-pink-500" },
    { name: "TikTok", emoji: "🎵", color: "text-black dark:text-white" },
    { name: "Gmail", emoji: "📧", color: "text-red-500" },
    { name: "Facebook", emoji: "👥", color: "text-blue-600" },
    { name: "Shopee", emoji: "🛒", color: "text-orange-500" },
    { name: "Lazada", emoji: "🛍️", color: "text-purple-500" },
    { name: "Webhook", emoji: "🔗", color: "text-gray-500" }
  ]

  return (
    <div className="grid grid-cols-3 gap-6 p-4">
      {platforms.map((platform, index) => (
        <div
          key={platform.name}
          className="flex flex-col items-center justify-center p-4 rounded-lg hover:bg-accent/50 transition-colors duration-200"
          style={{
            animation: `fadeInUp 0.6s ease-out ${index * 0.1}s both`
          }}
        >
          <div className={`text-3xl mb-2 ${platform.color}`}>
            {platform.emoji}
          </div>
          <span className="text-xs text-muted-foreground text-center">
            {platform.name}
          </span>
        </div>
      ))}
    </div>
  )
}

// 集成展示section组件
export function IntegrationsSection() {
  return (
    <section className="py-20 px-4 bg-gradient-to-b from-background to-muted/20">
      <div className="container mx-auto max-w-4xl">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
            Powerful Platform Integrations
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Connect with your favorite platforms and tools to create seamless automated workflows
          </p>
        </div>

        {/* 居中显示图标云 */}
        <div className="flex justify-center">
          <PlatformIntegrationsCloud className="w-full max-w-lg" />
        </div>

        {/* 底部描述 */}
        <div className="text-center mt-12">
          <h3 className="text-xl font-semibold text-foreground mb-3">
            Seamless Integration Experience
          </h3>
          <p className="text-muted-foreground max-w-3xl mx-auto">
            Connect with hundreds of third-party applications and services through Webhook and API integrations,
            making your business processes more intelligent and automated.
          </p>
        </div>
      </div>
    </section>
  )
}


