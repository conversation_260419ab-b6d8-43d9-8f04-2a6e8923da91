"use client"

import React, { useState, useEffect } from "react"
import { IconCloud } from "./interactive-icon-cloud"

// 添加CSS动画
const styles = `
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
`

// 您平台提供的9个集成 - 使用确认存在的图标slug
const platformIntegrations = [
  "whatsapp",
  "messenger",
  "shopee",
  "lazada",
  "gmail",
  "facebook",
  "instagram",
  "tiktok",
  "webhook"
]

interface PlatformIntegrationsCloudProps {
  className?: string
}

export function PlatformIntegrationsCloud({ className }: PlatformIntegrationsCloudProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [showFallback, setShowFallback] = useState(true) // 直接显示fallback

  useEffect(() => {
    // 注入CSS样式
    const styleElement = document.createElement('style')
    styleElement.textContent = styles
    document.head.appendChild(styleElement)

    return () => {
      if (document.head.contains(styleElement)) {
        document.head.removeChild(styleElement)
      }
    }
  }, [])

  return (
    <div className={`relative flex w-full max-w-lg items-center justify-center overflow-hidden rounded-lg bg-background px-8 pb-8 pt-8 ${className}`}>
      <div className="w-full h-96 flex items-center justify-center">
        {showFallback ? (
          <PlatformIconsFallback />
        ) : (
          <div className="relative w-full h-full">
            <IconCloud iconSlugs={platformIntegrations} />
            {isLoading && (
              <div className="absolute inset-0 flex items-center justify-center bg-background/80">
                <div className="flex flex-col items-center gap-2">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                  <span className="text-sm text-muted-foreground">Loading integrations...</span>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  )
}

// Fallback组件，显示静态图标网格
function PlatformIconsFallback() {
  const platforms = [
    { name: "WhatsApp", emoji: "💬", color: "text-green-500" },
    { name: "Messenger", emoji: "📱", color: "text-blue-500" },
    { name: "Instagram", emoji: "📸", color: "text-pink-500" },
    { name: "TikTok", emoji: "🎵", color: "text-black dark:text-white" },
    { name: "Gmail", emoji: "📧", color: "text-red-500" },
    { name: "Facebook", emoji: "👥", color: "text-blue-600" },
    { name: "Shopee", emoji: "🛒", color: "text-orange-500" },
    { name: "Lazada", emoji: "🛍️", color: "text-purple-500" },
    { name: "Webhook", emoji: "🔗", color: "text-gray-500" }
  ]

  return (
    <div className="grid grid-cols-3 gap-6 p-4">
      {platforms.map((platform, index) => (
        <div
          key={platform.name}
          className="flex flex-col items-center justify-center p-4 rounded-lg hover:bg-accent/50 transition-colors duration-200"
          style={{
            animation: `fadeInUp 0.6s ease-out ${index * 0.1}s both`
          }}
        >
          <div className={`text-3xl mb-2 ${platform.color}`}>
            {platform.emoji}
          </div>
          <span className="text-xs text-muted-foreground text-center">
            {platform.name}
          </span>
        </div>
      ))}
    </div>
  )
}

// 集成展示section组件
export function IntegrationsSection() {
  return (
    <section className="py-20 px-4 bg-gradient-to-b from-background to-muted/20">
      <div className="container mx-auto max-w-4xl">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
            Powerful Platform Integrations
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Connect with your favorite platforms and tools to create seamless automated workflows
          </p>
        </div>

        {/* 居中显示图标云 */}
        <div className="flex justify-center">
          <PlatformIntegrationsCloud className="w-full max-w-lg" />
        </div>

        {/* 底部描述 */}
        <div className="text-center mt-12">
          <h3 className="text-xl font-semibold text-foreground mb-3">
            Seamless Integration Experience
          </h3>
          <p className="text-muted-foreground max-w-3xl mx-auto">
            Connect with hundreds of third-party applications and services through Webhook and API integrations,
            making your business processes more intelligent and automated.
          </p>
        </div>
      </div>
    </section>
  )
}


