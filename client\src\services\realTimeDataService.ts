// 实时数据服务
export interface RealTimeUsageData {
  apiCalls: {
    current: number;
    limit: number;
    percentage: number;
    recentActivity: Array<{
      timestamp: Date;
      count: number;
      endpoint: string
}>
};
  storage: {
    current: number; // GB
    limit: number; // GB
    percentage: number;
    breakdown: {
      documents: number;
      images: number;
      videos: number;
      other: number
}
};
  aiProcessing: {
    current: number;
    limit: number;
    percentage: number;
    activeJobs: number;
    queueLength: number;
    avgProcessingTime: number; // seconds
  };
  bandwidthUsage: {
    current: number; // GB
    limit: number; // GB
    percentage: number;
    uploadSpeed: number; // Mbps
    downloadSpeed: number; // Mbps
  };
  systemHealth: {
    cpu: number;
    memory: number;
    disk: number;
    uptime: number; // hours
    responseTime: number; // ms
  }
}

class RealTimeDataService {
  private baseData: RealTimeUsageData = {
    apiCalls: {
      current: 18470,
      limit: 100000,
      percentage: 18.47,
      recentActivity: []
    },
    storage: {
      current: 23.7,
      limit: 100,
      percentage: 23.7,
      breakdown: {
        documents: 8.5,
        images: 12.3,
        videos: 2.1,
        other: 0.8
      }
    },
    aiProcessing: {
      current: 342,
      limit: 1000,
      percentage: 34.2,
      activeJobs: 5,
      queueLength: 12,
      avgProcessingTime: 45
    },
    bandwidthUsage: {
      current: 47.3,
      limit: 500,
      percentage: 9.46,
      uploadSpeed: 25.3,
      downloadSpeed: 82.1
    },
    systemHealth: {
      cpu: 32.5,
      memory: 68.2,
      disk: 45.8,
      uptime: 168.5,
      responseTime: 125
    }
  };

  private listeners: Set<(data: RealTimeUsageData) => void> = new Set();
  private intervalId: NodeJS.Timeout | null = null;
  private isConnected: boolean = false;

  /**
   * 启动实时数据更新
   */
  startRealTimeUpdates(intervalMs: number = 30000) {
    if (this.intervalId) {
      this.stopRealTimeUpdates()
}

    this.isConnected = true;
    this.intervalId = setInterval(() => {
      this.updateData();
      this.notifyListeners()
}, intervalMs);

    // 立即发送一次数据
    this.notifyListeners()
}

  /**
   * 停止实时数据更新
   */
  stopRealTimeUpdates() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null
}
    this.isConnected = false
}

  /**
   * 订阅数据更新
   */
  subscribe(listener: (data: RealTimeUsageData) => void) {
    this.listeners.add(listener);
    
    // 立即发送当前数据
    listener(this.getCurrentData());
    
    return () => {
      this.listeners.delete(listener)
}
}

  /**
   * 获取当前数据
   */
  getCurrentData(): RealTimeUsageData & { isConnected: boolean } {
    return {
      ...this.baseData,
      isConnected: this.isConnected
    }
}

  /**
   * 模拟数据更新
   */
  private updateData() {
    // API 调用增长
    const apiIncrement = Math.floor(Math.random() * 10) + 1;
    this.baseData.apiCalls.current += apiIncrement;
    this.baseData.apiCalls.percentage = (this.baseData.apiCalls.current / this.baseData.apiCalls.limit) * 100;
    
    // 添加API活动记录
    this.baseData.apiCalls.recentActivity.unshift({
      timestamp: new Date(),
      count: apiIncrement,
      endpoint: this.getRandomEndpoint()
    });
    
    // 只保留最近20条记录
    if (this.baseData.apiCalls.recentActivity.length > 20) {
      this.baseData.apiCalls.recentActivity = this.baseData.apiCalls.recentActivity.slice(0, 20)
}

    // 存储变化（较小）
    const storageChange = (Math.random() - 0.5) * 0.1; // ±0.05GB
    this.baseData.storage.current = Math.max(0, this.baseData.storage.current + storageChange);
    this.baseData.storage.percentage = (this.baseData.storage.current / this.baseData.storage.limit) * 100;

    // AI处理更新
    const processingChange = Math.floor(Math.random() * 5) - 2; // ±2
    this.baseData.aiProcessing.current = Math.max(0, this.baseData.aiProcessing.current + processingChange);
    this.baseData.aiProcessing.percentage = (this.baseData.aiProcessing.current / this.baseData.aiProcessing.limit) * 100;
    
    // 更新AI队列
    this.baseData.aiProcessing.activeJobs = Math.max(0, Math.floor(Math.random() * 10));
    this.baseData.aiProcessing.queueLength = Math.max(0, Math.floor(Math.random() * 20));
    this.baseData.aiProcessing.avgProcessingTime = 30 + Math.random() * 60; // 30-90秒

    // 带宽使用更新
    const bandwidthChange = (Math.random() - 0.5) * 0.2; // ±0.1GB
    this.baseData.bandwidthUsage.current = Math.max(0, this.baseData.bandwidthUsage.current + bandwidthChange);
    this.baseData.bandwidthUsage.percentage = (this.baseData.bandwidthUsage.current / this.baseData.bandwidthUsage.limit) * 100;
    
    // 更新网络速度
    this.baseData.bandwidthUsage.uploadSpeed = 20 + Math.random() * 20; // 20-40 Mbps
    this.baseData.bandwidthUsage.downloadSpeed = 60 + Math.random() * 40; // 60-100 Mbps

    // 系统健康状态更新
    this.baseData.systemHealth.cpu = Math.max(0, Math.min(100, this.baseData.systemHealth.cpu + (Math.random() - 0.5) * 10));
    this.baseData.systemHealth.memory = Math.max(0, Math.min(100, this.baseData.systemHealth.memory + (Math.random() - 0.5) * 5));
    this.baseData.systemHealth.disk = Math.max(0, Math.min(100, this.baseData.systemHealth.disk + (Math.random() - 0.5) * 2));
    this.baseData.systemHealth.uptime += 0.5; // 每次更新增加0.5小时
    this.baseData.systemHealth.responseTime = 100 + Math.random() * 100; // 100-200ms
  }

  /**
   * 获取随机API端点
   */
  private getRandomEndpoint(): string {
    const endpoints = [
      '/api/v1/chat/completions',
      '/api/v1/images/generate',
      '/api/v1/documents/analyze',
      '/api/v1/files/upload',
      '/api/v1/users/profile',
      '/api/v1/analytics/events'
    ];
    return endpoints[Math.floor(Math.random() * endpoints.length)]
}

  /**
   * 通知所有监听器
   */
  private notifyListeners() {
    const data = this.getCurrentData();
    this.listeners.forEach(listener => {
      try {
        listener(data)
} catch (error) {
        console.error('Error notifying listener:', error)
}
    })
}

  /**
   * 模拟连接断开
   */
  simulateDisconnection() {
    this.isConnected = false;
    this.notifyListeners();
    
    // 5秒后重新连接
    setTimeout(() => {
      this.isConnected = true;
      this.notifyListeners()
;
    }, 5000);
  }

  /**
   * 手动触发数据更新
   */
  forceUpdate() {
    this.updateData();
    this.notifyListeners()
}

  /**
   * 重置数据到初始状态
   */
  resetData() {
    this.baseData = {
      apiCalls: {
        current: 18470,
        limit: 100000,
        percentage: 18.47,
        recentActivity: []
      },
      storage: {
        current: 23.7,
        limit: 100,
        percentage: 23.7,
        breakdown: {
          documents: 8.5,
          images: 12.3,
          videos: 2.1,
          other: 0.8
        }
      },
      aiProcessing: {
        current: 342,
        limit: 1000,
        percentage: 34.2,
        activeJobs: 5,
        queueLength: 12,
        avgProcessingTime: 45
      },
      bandwidthUsage: {
        current: 47.3,
        limit: 500,
        percentage: 9.46,
        uploadSpeed: 25.3,
        downloadSpeed: 82.1
      },
      systemHealth: {
        cpu: 32.5,
        memory: 68.2,
        disk: 45.8,
        uptime: 168.5,
        responseTime: 125
      }
    };
    this.notifyListeners()
}
}

// 导出单例实例
export const realTimeDataService = new RealTimeDataService();