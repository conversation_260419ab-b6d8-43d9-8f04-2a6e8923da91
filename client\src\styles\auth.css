/* 身份验证页面样式 */

/* 输入框聚焦样式 */
.form-group input:focus {
  border-color: #8B5CF6;
  outline: none;
  box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.2);
}

/* 输入框组样式 */
.form-group {
  position: relative;
}

/* 错误消息样式 */
.error-message {
  color: #f56565;
  font-size: 0.875rem;
  margin-top: 0.5rem;
}

/* 社交登录按钮样式 */
.social-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 0.5rem 1rem;
  font-weight: 500;
  transition: all 150ms;
  border-radius: 0.375rem;
  margin-top: 0.75rem;
}

/* Google 按钮特定样式 */
.google-button {
  background-color: white;
  color: #4285F4;
  border: 1px solid #4285F4;
}

.google-button:hover {
  background-color: rgba(66, 133, 244, 0.1);
}

/* 按钮加载动画 */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading-spinner {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s linear infinite;
  margin-right: 0.5rem;
}

/* 卡片悬停动画 */
.auth-card-hover-effect:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* 媒体查询 - 响应式调整 */
@media (max-width: 640px) {
  .auth-page-container {
    padding: 1rem;
  }
}

/* 切换动画过渡 */
.fade-transition {
  transition: opacity 0.3s ease-in-out;
}

.fade-enter {
  opacity: 0;
}

.fade-enter-active {
  opacity: 1;
}

.fade-exit {
  opacity: 1;
}

.fade-exit-active {
  opacity: 0;
} 