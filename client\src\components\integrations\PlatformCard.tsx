import React from 'react';
import { <PERSON>, CardContent, CardHeader } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Switch } from '../ui/switch';
import { 
  Settings, 
  ExternalLink, 
  CheckCircle, 
  AlertCircle, 
  Clock 
} from 'lucide-react';

export interface Platform {
  id: string;
  name: string;
  description: string;
  icon?: React.ReactNode;
  status: 'connected' | 'disconnected' | 'pending' | 'error';
  enabled: boolean;
  lastSync?: string;
  connectionDate?: string;
  features?: string[];
}

interface PlatformCardProps {
  platform: Platform;
  onConnect?: (platformId: string) => void;
  onDisconnect?: (platformId: string) => void;
  onToggle?: (platformId: string, enabled: boolean) => void;
  onConfigure?: (platformId: string) => void;
  className?: string;
}

export const PlatformCard: React.FC<PlatformCardProps> = ({
  platform,
  onConnect,
  onDisconnect,
  onToggle,
  onConfigure,
  className
}) => {
  const getStatusIcon = () => {
    switch (platform.status) {
      case 'connected':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'pending':
        return <Clock className="w-4 h-4 text-yellow-600" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-600" />;
      default:
        return <AlertCircle className="w-4 h-4 text-gray-400" />;
    }
  };

  const getStatusBadge = () => {
    const variants = {
      connected: { label: '已连接', className: 'bg-green-100 text-green-800' },
      disconnected: { label: '未连接', className: 'bg-gray-100 text-gray-800' },
      pending: { label: '连接中', className: 'bg-yellow-100 text-yellow-800' },
      error: { label: '连接错误', className: 'bg-red-100 text-red-800' }
    };
    
    const variant = variants[platform.status];
    return <Badge className={variant.className}>{variant.label}</Badge>;
  };

  const handleConnect = () => {
    if (platform.status === 'connected') {
      onDisconnect?.(platform.id);
    } else {
      onConnect?.(platform.id);
    }
  };

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            {platform.icon && (
              <div className="w-10 h-10 flex items-center justify-center bg-gray-100 rounded-lg">
                {platform.icon}
              </div>
            )}
            <div>
              <h3 className="font-semibold text-gray-900">{platform.name}</h3>
              <p className="text-sm text-gray-600">{platform.description}</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            {getStatusIcon()}
            {getStatusBadge()}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* 连接信息 */}
        {platform.status === 'connected' && (
          <div className="space-y-2 text-sm text-gray-600">
            {platform.connectionDate && (
              <div>连接时间: {platform.connectionDate}</div>
            )}
            {platform.lastSync && (
              <div>最后同步: {platform.lastSync}</div>
            )}
          </div>
        )}

        {/* 功能列表 */}
        {platform.features && platform.features.length > 0 && (
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-2">支持功能</h4>
            <div className="flex flex-wrap gap-2">
              {platform.features.map((feature, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {feature}
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* 启用开关 */}
        {platform.status === 'connected' && (
          <div className="flex items-center justify-between py-2">
            <span className="text-sm font-medium text-gray-900">启用集成</span>
            <Switch
              checked={platform.enabled}
              onCheckedChange={(enabled) => onToggle?.(platform.id, enabled)}
            />
          </div>
        )}

        {/* 操作按钮 */}
        <div className="flex space-x-2 pt-2">
          <Button
            variant={platform.status === 'connected' ? 'outline' : 'default'}
            size="sm"
            onClick={handleConnect}
            className="flex-1"
          >
            {platform.status === 'connected' ? (
              <>
                <ExternalLink className="w-4 h-4 mr-2" />
                断开连接
              </>
            ) : (
              '连接'
            )}
          </Button>

          {platform.status === 'connected' && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => onConfigure?.(platform.id)}
            >
              <Settings className="w-4 h-4" />
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default PlatformCard; 