const supabase = require('../config/supabase');
const notificationService = require('../services/notificationService');

/**
 * 获取所有通知规则
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getAllRules = async (req, res) => {
  try {
    const { data, error } = await supabase
      .from('notification_rules')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      return res.status(500).json({ error: error.message });
    }

    res.status(200).json(data);
  } catch (error) {
    console.error('Error getting notification rules:', error); 
    res.status(500).json({ error: 'Failed to retrieve notification rules' });
  }
};

/**
 * 获取单个通知规则
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getRuleById = async (req, res) => {
  try {
    const { id } = req.params;

    const { data, error } = await supabase
      .from('notification_rules')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return res.status(404).json({ error: 'Notification rule not found' });
      }
      return res.status(500).json({ error: error.message });
    }

    res.status(200).json(data);
  } catch (error) {
    console.error(`Error getting notification rule with ID ${req.params.id}:`, error);
    res.status(500).json({ error: 'Failed to retrieve notification rule' });
  }
};

/**
 * 创建新通知规则
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.createRule = async (req, res) => {
  try {
    const {
      name,
      service_id,
      team_member_id,
      booking_type,
      reminder_value,
      reminder_unit,
      notification_channel,
      message_template_id,
      message_content,
      is_active
    } = req.body;

    // 验证必填字段
    if (!name || !reminder_value || !reminder_unit || !notification_channel) {
      return res.status(400).json({ 
        error: 'Missing required fields: name, reminder_value, reminder_unit, notification_channel' 
      });
    }

    // 验证提醒值
    if (typeof reminder_value !== 'number' || reminder_value <= 0) {
      return res.status(400).json({ error: 'reminder_value must be a positive number' });
    }

    // 验证提醒单位
    if (!['minutes', 'hours', 'days'].includes(reminder_unit)) {
      return res.status(400).json({ error: 'reminder_unit must be one of: minutes, hours, days' });
    }

    // 验证通知渠道
    if (!['email', 'whatsapp', 'messenger', 'instagram', 'sms', 'same_as_booking'].includes(notification_channel)) {
      return res.status(400).json({ 
        error: 'notification_channel must be one of: email, whatsapp, messenger, instagram, sms, same_as_booking' 
      });
    }

    // 验证消息内容
    if (!message_content && !message_template_id) {
      return res.status(400).json({ error: 'Either message_content or message_template_id is required' });
    }

    const ruleData = {
      name,
      service_id,
      team_member_id,
      booking_type,
      reminder_value,
      reminder_unit,
      notification_channel,
      message_template_id,
      message_content,
      is_active: is_active !== undefined ? is_active : true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const { data, error } = await supabase
      .from('notification_rules')
      .insert(ruleData)
      .select();

    if (error) {
      return res.status(500).json({ error: error.message });
    }

    res.status(201).json(data[0]);
  } catch (error) {
    console.error('Error creating notification rule:', error); 
    res.status(500).json({ error: 'Failed to create notification rule' });
  }
};

/**
 * 更新通知规则
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.updateRule = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      service_id,
      team_member_id,
      booking_type,
      reminder_value,
      reminder_unit,
      notification_channel,
      message_template_id,
      message_content,
      is_active
    } = req.body;

    // 验证提醒值（如果提供）
    if (reminder_value !== undefined && (typeof reminder_value !== 'number' || reminder_value <= 0)) {
      return res.status(400).json({ error: 'reminder_value must be a positive number' });
    }

    // 验证提醒单位（如果提供）
    if (reminder_unit !== undefined && !['minutes', 'hours', 'days'].includes(reminder_unit)) {
      return res.status(400).json({ error: 'reminder_unit must be one of: minutes, hours, days' });
    }

    // 验证通知渠道（如果提供）
    if (notification_channel !== undefined && 
        !['email', 'whatsapp', 'messenger', 'instagram', 'sms', 'same_as_booking'].includes(notification_channel)) {
      return res.status(400).json({ 
        error: 'notification_channel must be one of: email, whatsapp, messenger, instagram, sms, same_as_booking' 
      });
    }

    const updateData = {
      ...(name !== undefined && { name }),
      ...(service_id !== undefined && { service_id }),
      ...(team_member_id !== undefined && { team_member_id }),
      ...(booking_type !== undefined && { booking_type }),
      ...(reminder_value !== undefined && { reminder_value }),
      ...(reminder_unit !== undefined && { reminder_unit }),
      ...(notification_channel !== undefined && { notification_channel }),
      ...(message_template_id !== undefined && { message_template_id }),
      ...(message_content !== undefined && { message_content }),
      ...(is_active !== undefined && { is_active }),
      updated_at: new Date().toISOString()
    };

    const { data, error } = await supabase
      .from('notification_rules')
      .update(updateData)
      .eq('id', id)
      .select();

    if (error) {
      return res.status(500).json({ error: error.message });
    }

    if (!data || data.length === 0) {
      return res.status(404).json({ error: 'Notification rule not found' });
    }

    res.status(200).json(data[0]);
  } catch (error) {
    console.error(`Error updating notification rule with ID ${req.params.id}:`, error);
    res.status(500).json({ error: 'Failed to update notification rule' });
  }
};

/**
 * 删除通知规则
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.deleteRule = async (req, res) => {
  try {
    const { id } = req.params;

    const { error } = await supabase
      .from('notification_rules')
      .delete()
      .eq('id', id);

    if (error) {
      return res.status(500).json({ error: error.message });
    }

    res.status(200).json({ message: 'Notification rule deleted successfully' });
  } catch (error) {
    console.error(`Error deleting notification rule with ID ${req.params.id}:`, error);
    res.status(500).json({ error: 'Failed to delete notification rule' });
  }
};

/**
 * 获取规则运行历史记录
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getRuleHistory = async (req, res) => {
  try {
    const { id } = req.params;
    const { page = 1, perPage = 10 } = req.query;
    
    // 转换为数字
    const pageNum = parseInt(page, 10);
    const limit = parseInt(perPage, 10);
    const offset = (pageNum - 1) * limit;

    // 先检查规则是否存在
    const { data: rule, error: ruleError } = await supabase
      .from('notification_rules')
      .select('id')
      .eq('id', id)
      .single();

    if (ruleError || !rule) {
      return res.status(404).json({ error: 'Notification rule not found' });
    }

    // 获取规则的历史记录
    const { data, error, count } = await supabase
      .from('notification_history')
      .select('*', { count: 'exact' })
      .eq('rule_id', id)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      return res.status(500).json({ error: error.message });
    }

    res.status(200).json({
      history: data,
      pagination: {
        total: count,
        page: pageNum,
        perPage: limit,
        totalPages: Math.ceil(count / limit)
      }
    });
  } catch (error) {
    console.error(`Error getting history for notification rule with ID ${req.params.id}:`, error);
    res.status(500).json({ error: 'Failed to retrieve notification rule history' });
  }
};

/**
 * 手动触发通知处理
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.processNotifications = async (req, res) => {
  try {
    const { hoursAhead = 24, notificationChannels, bookingTypes } = req.body;

    const options = {
      hoursAhead: hoursAhead
    };

    // 添加可选过滤条件
    if (notificationChannels && Array.isArray(notificationChannels) && notificationChannels.length > 0) {
      options.notificationChannels = notificationChannels;
    }

    if (bookingTypes && Array.isArray(bookingTypes) && bookingTypes.length > 0) {
      options.bookingTypes = bookingTypes;
    }

    // 处理通知
    const results = await notificationService.processAllPendingNotifications(options);

    res.status(200).json(results);
  } catch (error) {
    console.error('Error processing notifications:', error); 
    res.status(500).json({ error: 'Failed to process notifications' });
  }
}; 