import React from "react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
// import { LogIn, LogOut, UserPlus } from "lucide-react";
// import { Button } from '@/components/ui/button';

interface AuthButtonProps extends Omit<React.ButtonHTMLAttributes<HTMLButtonElement>, 
  "onDrag" | "onDragStart" | "onDragEnd" | "onDragEnter" | "onDragLeave" | "onDragOver" | "onDrop" |
  "onAnimationStart" | "onAnimationEnd" | "onAnimationIteration" | "onTransitionEnd"
> {
  variant?: "primary" | "outline" | "ghost" | "social";
  size?: "sm" | "md" | "lg";
  loading?: boolean;
  icon?: React.ReactNode;
  children: React.ReactNode;
  fullWidth?: boolean;
}

const buttonVariants = {
  primary: `
    bg-gradient-to-r from-purple-600 to-pink-600 text-white
    hover:from-purple-700 hover:to-pink-700
    focus:ring-2 focus:ring-purple-500 focus:ring-offset-2
    shadow-lg hover:shadow-xl
    transition-all duration-300 ease-out
    hover:scale-105 active:scale-95
  `,
  outline: `
    border-2 border-purple-200 bg-white text-purple-600
    hover:border-purple-300 hover:bg-purple-50
    focus:ring-2 focus:ring-purple-500 focus:ring-offset-2
    transition-all duration-200 ease-out
    hover:scale-105 active:scale-95
  `,
  ghost: `
    bg-transparent text-purple-600 
    hover:bg-purple-50 hover:text-purple-700
    focus:ring-2 focus:ring-purple-500 focus:ring-offset-2
    transition-all duration-200 ease-out
  `,
  social: `
    border border-gray-200 bg-white text-gray-700
    hover:border-gray-300 hover:bg-gray-50
    focus:ring-2 focus:ring-gray-500 focus:ring-offset-2
    transition-all duration-200 ease-out
    hover:scale-105 active:scale-95
  `
};

const sizeVariants = {
  sm: "px-4 py-2 text-sm rounded-lg",
  md: "px-6 py-3 text-base rounded-xl",
  lg: "px-8 py-4 text-lg rounded-xl"
};

const AuthButton = React.forwardRef<HTMLButtonElement, AuthButtonProps>(
  ({ 
    variant = "primary",
    size = "md",
    loading = false, 
    icon, 
    children, 
    fullWidth = false,
    className, 
    disabled,
    ...props 
  }, ref) => {
    const isDisabled = disabled || loading;

    const {
      onDrag,
      onDragStart,
      onDragEnd,
      onDragEnter,
      onDragLeave,
      onDragOver,
      onDrop,
      onAnimationStart,
      onAnimationEnd,
      onAnimationIteration,
      onTransitionEnd,
      ...filteredProps
    } = props as any;

    return (
      <motion.button
        ref={ref}
        className={cn(
          "inline-flex items-center justify-center font-semibold",
          "focus:outline-none focus:ring-offset-white",
          "disabled:opacity-50 disabled:cursor-not-allowed",
          "relative overflow-hidden",
          buttonVariants[variant],
          sizeVariants[size],
          fullWidth && "w-full",
          isDisabled && "hover:scale-100 active:scale-100",
          className
        )}
        disabled={isDisabled}
        whileTap={!isDisabled ? { scale: 0.95 } : {}}
        whileHover={!isDisabled ? { scale: 1.02 } : {}}
        transition={{ duration: 0.2, ease: "easeOut" }}
        {...filteredProps}
      >
        {loading && (
          <motion.div
            initial={{ opacity: 0, scale: 0 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0 }}
            className="mr-2"
          >
            <div className="w-4 h-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
          </motion.div>
        )}
        
        {!loading && icon && (
          <span className="mr-2 flex items-center">
            {icon}
          </span>
        )}
        
        <span className={loading ? "ml-2" : ""}>
          {children}
        </span>

        {variant === "primary" && (
          <motion.div
            className="absolute inset-0 bg-gradient-to-r from-purple-400 to-pink-400 opacity-0"
            whileHover={{ opacity: 0.1 }}
            transition={{ duration: 0.3 }}
          />
        )}
      </motion.button>
    );
  }
);

AuthButton.displayName = "AuthButton";

export { AuthButton, type AuthButtonProps };
