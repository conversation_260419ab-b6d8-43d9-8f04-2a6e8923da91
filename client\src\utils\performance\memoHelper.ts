import { memo, ComponentType, FC } from 'react';

/**
 * 组件属性比较函数类型
 */
export type PropsCompareFunction<P> = (prevProps: Readonly<P>, nextProps: Readonly<P>) => boolean;

/**
 * 默认属性比较函数 - 浅比较所有属性
 * 返回true表示相等，不需要重新渲染
 */
export const defaultPropsAreEqual = <P>(prevProps: P, nextProps: P): boolean => {
  if (prevProps === nextProps) return true;
  
  if (
    typeof prevProps !== 'object' || 
    prevProps === null || 
    typeof nextProps !== 'object' || 
    nextProps === null
  ) {
    return prevProps === nextProps;
  }
  
  const prevKeys = Object.keys(prevProps);
  const nextKeys = Object.keys(nextProps);
  
  if (prevKeys.length !== nextKeys.length) return false;
  
  return prevKeys.every(key => {
    return (
      Object.prototype.hasOwnProperty.call(nextProps, key) && 
      (prevProps as any)[key] === (nextProps as any)[key]
    );
  });
};

/**
 * 创建深度比较函数 - 用于复杂对象的比较
 * @param keys 需要深度比较的属性名数组
 * @param ignoreKeys 需要忽略的属性名数组
 */
export const createDeepCompare = <P>(keys: (keyof P)[] = [], ignoreKeys: (keyof P)[] = []): PropsCompareFunction<P> => {
  return (prevProps, nextProps) => {
    // 首先进行基本的浅比较
    if (prevProps === nextProps) return true;
    
    // 检查两个对象的所有键
    const prevObjKeys = Object.keys(prevProps) as (keyof P)[];
    const nextObjKeys = Object.keys(nextProps) as (keyof P)[];
    
    // 如果键的数量不同，则属性肯定发生了变化
    if (prevObjKeys.length !== nextObjKeys.length) return false;
    
    // 比较每个属性
    return prevObjKeys.every(key => {
      // 如果属性应该被忽略，则认为它没有变化
      if (ignoreKeys.includes(key)) return true;
      
      const prevValue = prevProps[key];
      const nextValue = nextProps[key];
      
      // 如果属性是需要深度比较的，并且是对象类型
      if (
        keys.includes(key) && 
        typeof prevValue === 'object' && prevValue !== null &&
        typeof nextValue === 'object' && nextValue !== null
      ) {
        // 递归比较对象内部
        return Object.keys(prevValue).every(subKey => {
          return (
            Object.prototype.hasOwnProperty.call(nextValue, subKey) &&
            (prevValue as any)[subKey] === (nextValue as any)[subKey]
          );
        });
      }
      
      // 对于其他属性，使用浅比较
      return prevValue === nextValue;
    });
  };
};

/**
 * 创建基于选择器的比较函数
 * 允许指定只关心哪些props变化
 * @param selector 选择要比较的属性函数
 */
export const createSelectorCompare = <P, S>(selector: (props: P) => S): PropsCompareFunction<P> => {
  return (prevProps, nextProps) => {
    const prevSelected = selector(prevProps);
    const nextSelected = selector(nextProps);
    return prevSelected === nextSelected;
  };
};

/**
 * 增强版memo函数，提供多种比较策略选项
 * @param component 要记忆化的组件
 * @param compareType 比较类型: 'default'|'deep'|'selector'
 * @param options 比较函数选项
 */
export function memoWithStrategy<P>(
  component: ComponentType<P>,
  compareType: 'default' | 'deep' | 'selector' = 'default',
  options?: {
    deepCompareKeys?: (keyof P)[];
    ignoreKeys?: (keyof P)[];
    selector?: (props: P) => any;
  }
): FC<P> {
  let compareFunction: PropsCompareFunction<P>;
  
  switch (compareType) {
    case 'deep':
      compareFunction = createDeepCompare<P>(
        options?.deepCompareKeys || [],
        options?.ignoreKeys || []
      );
      break;
    case 'selector':
      if (!options?.selector) {
        console.warn('使用selector比较策略需要提供selector函数');
        compareFunction = defaultPropsAreEqual;
      } else {
        compareFunction = createSelectorCompare(options.selector);
      }
      break;
    case 'default':
    default:
      compareFunction = defaultPropsAreEqual;
  }
  
  return memo(component, compareFunction);
}

/**
 * 使用示例：
 * 
 * // 使用默认浅比较
 * const MemoComponent = memoWithStrategy(MyComponent);
 * 
 * // 使用深度比较，比较items属性的每个元素
 * const MemoWithDeepCompare = memoWithStrategy(MyComponent, 'deep', {
 *   deepCompareKeys: ['items']
 * });
 * 
 * // 使用选择器比较，只比较id和name属性
 * const MemoWithSelector = memoWithStrategy(MyComponent, 'selector', {
 *   selector: (props) => ({ id: props.id, name: props.name })
 * });
 */

export default memoWithStrategy;