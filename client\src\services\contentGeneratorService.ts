import axiosInstance from '@/api/axiosInstance'

/**
 * Interface for content generation parameters
 */
export interface ContentGenerationParams {
  contentType: string;
  prompt: string;
  tone?: string;
  wordCount?: number | string;
  outputFormat?: string;
  advancedOptions?: {
    temperature?: number;
    maxLength?: number;
    brandVoice?: string;
    includeSEO?: boolean;
    includeHeadings?: boolean;
    includeCTA?: boolean;
    [key: string]: any;
  };
}

/**
 * Interface for content template
 */
export interface ContentTemplate {
  id?: string;
  name: string;
  description: string;
  content: string;
  agentId?: string;
  lastUpdated?: string;
}

/**
 * Interface for content history item
 */
export interface ContentHistoryItem {
  id: string;
  title: string;
  content: string;
  contentType: string;
  preview: string;
  createdAt: string;
  metadata?: any;
}

/**
 * Interface for pagination info
 */
export interface PaginationInfo {
  page: number;
  limit: number;
  totalItems: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

/**
 * Generate content using the AI service
 * @param params Content generation parameters
 * @returns Generated content and metadata
 */
export const generateContent = async (params: ContentGenerationParams) => {
  try {
    const response = await axiosInstance.post('/api/content-generator/generate', params);
    return response.data;
  } catch (error) {
    console.error('Error generating content:', error);
    throw error;
  }
};

/**
 * Save a content template
 * @param template Template data
 * @returns Saved template
 */
export const saveTemplate = async (template: ContentTemplate) => {
  try {
    const response = await axiosInstance.post('/api/content-generator/templates', template);
    return response.data.template;
  } catch (error) {
    console.error('Error saving template:', error);
    throw error;
  }
};

/**
 * Get all templates for an agent
 * @param agentId Agent ID
 * @returns List of templates
 */
export const getTemplates = async (agentId: string) => {
  try {
    const response = await axiosInstance.get(`/api/content-generator/templates/${agentId}`);
    return response.data.templates;
  } catch (error) {
    console.error('Error fetching templates:', error);
    throw error;
  }
};

/**
 * Delete a template
 * @param templateId Template ID
 * @returns Success message
 */
export const deleteTemplate = async (templateId: string) => {
  try {
    const response = await axiosInstance.delete(`/api/content-generator/templates/${templateId}`);
    return response.data;
  } catch (error) {
    console.error('Error deleting template:', error);
    throw error;
  }
};

/**
 * Save generated content to history
 * @param data Content data
 * @returns Saved content entry
 */
export const saveToHistory = async (data: {
  title: string;
  content: string;
  contentType: string;
  metadata?: any;
}) => {
  try {
    const response = await axiosInstance.post('/api/content-generator/history', data);
    return response.data.savedContent;
  } catch (error) {
    console.error('Error saving to history:', error);
    throw error;
  }
};

/**
 * Get content history with pagination
 * @param page Page number
 * @param limit Items per page
 * @returns History items and pagination info
 */
export const getHistory = async (page = 1, limit = 10): Promise<{
  items: ContentHistoryItem[];
  pagination: PaginationInfo;
}> => {
  try {
    const response = await axiosInstance.get('/api/content-generator/history', {
      params: { page, limit }
    });
    return {
      items: response.data.history,
      pagination: response.data.pagination
    };
  } catch (error) {
    console.error('Error fetching history:', error);
    throw error;
  }
}; 