// 模拟数据 - 实际应用中，这些数据应从API获取
const mockTemplates = [
  {
    id: '1',
    name: '每周行业更新',
    platform: 'twitter',
    content: '本周{{industry}}行业最新动态：{{news}}。想了解更多，请访问我们的网站！#{{industry}} #行业动态',
    tags: ['行业', '新闻', '周更'],
    isActive: true,
    createdAt: '2023-11-15T09:30:00.000Z',
    usageCount: 28
  },
  {
    id: '2',
    name: '产品发布模板',
    platform: 'facebook',
    content: '我们激动地宣布推出新产品{{productName}}！{{productDescription}}。立即访问{{link}}了解更多详情。',
    tags: ['产品', '发布', '营销'],
    isActive: true,
    createdAt: '2023-10-20T14:45:00.000Z',
    usageCount: 15
  },
  {
    id: '3',
    name: '促销活动模板',
    platform: 'instagram',
    content: '限时优惠！从现在起至{{endDate}}，所有{{category}}产品享受{{discount}}折扣。点击链接立即选购：{{link}} #特惠 #限时',
    tags: ['促销', '折扣', '营销'],
    isActive: true,
    createdAt: '2023-12-05T11:20:00.000Z',
    usageCount: 42
  },
  {
    id: '4',
    name: '专业知识分享',
    platform: 'linkedin',
    content: '【{{topic}}专业解析】\n\n{{content}}\n\n想要深入了解更多相关内容，欢迎联系我们的专家团队。',
    tags: ['专业', '知识', '分享'],
    isActive: true,
    createdAt: '2023-09-10T16:15:00.000Z',
    usageCount: 19
  },
  {
    id: '5',
    name: '客户案例分享',
    platform: 'wechat',
    content: '【成功案例】\n\n客户：{{clientName}}\n行业：{{industry}}\n\n挑战：{{challenge}}\n\n解决方案：{{solution}}\n\n成果：{{results}}\n\n了解我们如何帮助更多企业，请点击阅读原文。',
    tags: ['案例', '客户', '成功'],
    isActive: true,
    createdAt: '2023-11-25T13:40:00.000Z',
    usageCount: 12
  },
  {
    id: '6',
    name: '团队活动分享',
    platform: 'twitter',
    content: '今天我们的团队参加了{{activity}}活动！{{description}} #团队建设 #公司文化',
    tags: ['团队', '活动', '公司'],
    isActive: false,
    createdAt: '2023-08-15T10:10:00.000Z',
    usageCount: 8
  },
  {
    id: '7',
    name: '季节性促销',
    platform: 'instagram',
    content: '{{season}}特惠来啦！从即日起至{{endDate}}，全场{{category}}类产品享{{discount}}折优惠。限时限量，先到先得！#{{season}}特惠 #限时优惠',
    tags: ['季节', '促销', '特惠'],
    isActive: true,
    createdAt: '2023-12-01T08:30:00.000Z',
    usageCount: 5
  },
  {
    id: '8',
    name: '行业专家观点',
    platform: 'linkedin',
    content: '【专家观点】{{expertName}}谈{{topic}}：\n\n"{{quote}}"\n\n想了解更多{{topic}}的见解和分析，请关注我们的专栏或预约咨询。',
    tags: ['专家', '观点', '行业'],
    isActive: true,
    createdAt: '2023-10-05T14:20:00.000Z',
    usageCount: 11
  }
];

const mockGeneratedContent = [
  {
    id: '101',
    title: '人工智能行业周报',
    platform: 'twitter',
    content: '本周人工智能行业最新动态：Google发布全新AI模型，性能提升30%。想了解更多，请访问我们的网站！#人工智能 #行业动态',
    createdAt: '2023-12-12T08:15:00.000Z',
    status: 'published',
    performance: 78
  },
  {
    id: '102',
    title: 'SmartDesk Pro发布',
    platform: 'facebook',
    content: '我们激动地宣布推出新产品SmartDesk Pro！内置AI助手，支持语音控制和自动高度调节。立即访问shop.example.com了解更多详情。',
    createdAt: '2023-11-28T14:30:00.000Z',
    status: 'published',
    performance: 92
  },
  {
    id: '103',
    title: '黑色星期五特惠',
    platform: 'instagram',
    content: '限时优惠！从现在起至12月3日，所有电子产品享受7折扣。点击链接立即选购：sale.example.com #特惠 #限时',
    createdAt: '2023-11-24T09:45:00.000Z',
    status: 'published',
    performance: 85
  },
  {
    id: '104',
    title: '区块链技术解析',
    platform: 'linkedin',
    content: '【区块链技术专业解析】\n\n区块链不仅仅是加密货币的基础技术，它在供应链管理、身份验证和智能合约等领域有广泛应用...\n\n想要深入了解更多相关内容，欢迎联系我们的专家团队。',
    createdAt: '2023-12-05T11:20:00.000Z',
    status: 'published',
    performance: 63
  },
  {
    id: '105',
    title: '新年促销',
    platform: 'wechat',
    content: '新年特惠！1月1日至1月15日，全场商品8折起，会员额外享95折。更有精美礼品相送，先到先得！',
    createdAt: '2024-01-01T00:05:00.000Z',
    status: 'scheduled',
    performance: 0
  },
  {
    id: '106',
    title: '数据安全白皮书',
    platform: 'linkedin',
    content: '我们发布了最新的《企业数据安全白皮书》，深入分析当前面临的挑战和应对策略。免费下载链接：example.com/whitepaper',
    createdAt: '2023-10-15T16:40:00.000Z',
    status: 'published',
    performance: 45
  },
  {
    id: '107',
    title: '春节祝福',
    platform: 'wechat',
    content: '恭贺新春！值此新春佳节，祝愿大家龙年大吉，身体健康，万事如意！',
    createdAt: '2024-01-15T10:30:00.000Z',
    status: 'draft',
    performance: 0
  },
  {
    id: '108',
    title: '团队拓展活动',
    platform: 'twitter',
    content: '今天我们的团队参加了户外拓展活动！通过一系列挑战，增强了团队凝聚力和协作能力。 #团队建设 #公司文化',
    createdAt: '2023-09-25T18:20:00.000Z',
    status: 'published',
    performance: 32
  }
];

// 模拟内容生成设置
const mockContentSettings = {
  general: {
    schedule: 'daily',
    selectedTime: '10:00',
    contentSource: 'template_A',
    platforms: {
      facebook: true,
      twitter: false,
      linkedin: true,
      instagram: false,
      wechat: false,
      tiktok: false
    },
    enableApproval: false
  },
  contentStyle: {
    style: 'professional',
    tone: 'formal',
    length: 'medium',
    enableAutoHashtags: true,
    hashtagCount: 3,
    priorityKeywords: '产品,行业,特色',
    forbiddenWords: '竞争对手'
  },
  aiSettings: {
    maxGenerationAttempts: 3,
    filterLevel: 'standard',
    saveHistory: true,
    promptTemplate: '生成一篇关于{{topic}}的{{contentStyle}}风格的内容，适合在{{platform}}平台发布。语气应当{{contentTone}}，长度大约{{contentLength}}字。'
  },
  localizationSettings: {
    mainLanguage: 'zh_CN',
    enableMultiLanguage: false,
    additionalLanguages: [],
    localizeCulturalReferences: true,
    localizeDateFormats: true, 
    localizeCurrencyFormats: true
  },
  mediaSettings: {
    includeImages: true,
    imageSource: 'auto',
    imageStyle: 'modern',
    imageCount: 1,
    optimizeImageSize: true,
    addWatermark: false
  },
  advancedSettings: {
    autoReplyComments: false,
    receiveNotifications: true,
    generatePerformanceReports: true,
    apiEndpoint: '',
    apiKey: '',
    cacheTime: 7,
    enableEmergencyPause: true,
    dataExportFormat: 'json'
  }
};

// 获取所有内容模板
export const fetchContentTemplates = async () => {
  // 在实际应用中，这里应该是API调用
  // 例如：const response = await axiosInstance.get('/api/content-templates');
  // 返回 response.data;
  
  // 模拟API延迟
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve([...mockTemplates]);
    }, 800);
  });
};

// 获取所有生成的内容
export const fetchGeneratedContent = async () => {
  // 在实际应用中，这里应该是API调用
  // 例如：const response = await axiosInstance.get('/api/generated-content');
  // 返回 response.data;
  
  // 模拟API延迟
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve([...mockGeneratedContent]);
    }, 1000);
  });
};

// 保存模板（创建或更新）
export const saveTemplate = async (templateData) => {
  // 在实际应用中，这里应该是API调用
  // 例如：
  // if (templateData.id) {
  //   const response = await axiosInstance.put(`/api/content-templates/${templateData.id}`, templateData);
  //   return response.data;
  // } else {
  //   const response = await axiosInstance.post('/api/content-templates' templateData);
  //   return response.data;
  // }
  
  // 模拟API延迟和响应
  return new Promise((resolve) => {
    setTimeout(() => {
      // 返回提交的数据（在实际API中，可能会有服务器生成的ID等字段）
      resolve({...templateData});
    }, 800);
  });
};

// 删除模板
export const deleteTemplate = async (templateId) => {
  // 在实际应用中，这里应该是API调用
  // 例如：const response = await axiosInstance.delete(`/api/content-templates/${templateId}`);
  // 返回 response.data;
  
  // 模拟API延迟
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({success: true});
    }, 600);
  });
};

// 生成内容
export const generateContent = async (templateId, variables) => {
  // 在实际应用中，这里应该是API调用生成内容
  // 例如：const response = await axiosInstance.post('/api/generate-content' { templateId, variables });
  // 返回 response.data;
  
  // 模拟API延迟
  return new Promise((resolve) => {
    setTimeout(() => {
      const template = mockTemplates.find(t => t.id === templateId);
      if (!template) {
        resolve({error: 'Template not found,'});
        return;
      };
      
      // 简单的变量替换逻辑（实际实现可能更复杂）
      let content = template.content;
      for (const [key, value] of Object.entries(variables)) {
        content = content.replace(new RegExp(`{{${key}}}`, 'g'), value);
      };
      
      // 模拟生成的内容
      const generatedContent = {
        id: `gen_${Date.now()}`,
        title: variables.title || template.name,
        platform: template.platform,
        content: content,
        createdAt: new Date().toISOString(),
        status: 'draft',
        performance: 0
      };
      
      resolve(generatedContent);
    }, 1200);
  });
};

// 其他可能的API功能，如获取内容性能分析等
export const getContentPerformanceStats = async (dateRange) => {
  // 实现内容性能分析API调用
  // 暂时返回模拟数据
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        totalImpressions: 15420,
        totalEngagements: 2835,
        engagementRate: 18.4,
        performanceByPlatform: [
          { platform: 'twitter', impressions: 4250, engagements: 7480 },
          { platform: 'facebook', impressions: 6120, engagements: 12450 },
          { platform: 'instagram', impressions: 3280, engagements: 6240 },
          { platform: 'linkedin', impressions: 1320, engagements: 1580 },
          { platform: 'wechat', impressions: 450, engagements: 600 }
        ]
      });
    }, 1000);
  });
};

// 获取内容生成器设置
export const fetchContentGeneratorSettings = async () => {
  // 实际应用中应该从API获取
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({...mockContentSettings});
    }, 800);
  });
};

// 保存内容生成器设置
export const saveContentGeneratorSettings = async (settings) => {
  // 实际应用中应该调用API保存
  return new Promise((resolve) => {
    setTimeout(() => {
      console.log('保存的设置:', settings);
      resolve({success: true, message: '设置已成功保存,'});
    }, 1000);
  });
};

// 获取支持的语言列表
export const fetchSupportedLanguages = async () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve([
        { code: 'zh_CN', name: '简体中文' },
        { code: 'zh_TW', name: '繁体中文' },
        { code: 'en_US', name: '英语（美国）' },
        { code: 'en_GB', name: '英语（英国）' },
        { code: 'ja_JP', name: '日语' },
        { code: 'ko_KR', name: '韩语' },
        { code: 'fr_FR', name: '法语' },
        { code: 'de_DE', name: '德语' },
        { code: 'es_ES', name: '西班牙语' },
        { code: 'ru_RU', name: '俄语' },
        { code: 'pt_BR', name: '葡萄牙语（巴西）' },
        { code: 'it_IT', name: '意大利语' },
        { code: 'nl_NL', name: '荷兰语' },
        { code: 'th_TH', name: '泰语' },
        { code: 'vi_VN', name: '越南语' }
      ]);
    }, 500);
  });
};

// 测试生成内容样本
export const generateContentSample = async (settings) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const samples = {
        twitter: '本周科技行业最新动态：苹果发布全新MacBook Pro，搭载M2芯片性能提升40%。想了解更多，请访问我们的网站！#科技 #苹果新品,',
        facebook: '我们激动地宣布推出全新数据分析平台DataInsight Pro！通过AI驱动的实时分析，帮助企业提升决策效率高达35%。立即访问example.com/launch了解更多详情.,',
        linkedin: '【数据安全专业解析】\n\n随着远程办公常态化，企业数据安全面临前所未有的挑战。基于零信任架构的安全策略正成为行业新标准。\n\n想要深入了解更多相关内容，欢迎联系我们的专家团队.,',
        instagram: '限时优惠！从现在起至12月25日，所有冬季服装享受6折优惠。点击链接立即选购：sale.example.com #冬季特惠 #限时,',
        wechat: '【成功案例】\n\n客户：某全球500强制造企业\n行业：工业自动化\n\n挑战：生产数据分散，难以实时监控\n\n解决方案：部署我们的IoT集成平台\n\n成果：效率提升28%，节约成本1500万/年\n\n了解我们如何帮助更多企业，请点击阅读原文。'
      };
      
      // 根据设置中的平台和风格选择样本
      const platform = Object.keys(settings.general.platforms).find(p => settings.general.platforms[p]) || 'twitter'
      
      resolve({
        sample: samples[platform],
        platform: platform
      });
    }, 1200);
  });
};

// 获取内容生成可用的变量列表
export const fetchContentVariables = async () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve([
        { name: 'industry', description: '行业名称', example: '科技、金融、教育' },
        { name: 'news', description: '新闻内容', example: '最新产品发布、行业动态' },
        { name: 'productName', description: '产品名称', example: 'SuperApp Pr,o, SmartDesk' },
        { name: 'productDescription', description: '产品描述', example: '内置AI助手，支持语音控制' },
        { name: 'link', description: '链接URL', example: 'example.com/product' },
        { name: 'endDate', description: '结束日期', example: '12月31,日, 2023年底' },
        { name: 'category', description: '产品类别', example: '电,子, 服装, 家居' },
        { name: 'discount', description: '折扣信息', example: '7,折, 半价' },
        { name: 'topic', description: '主题', example: '数据安,全, 人工智能' },
        { name: 'content', description: '主要内容', example: '详细的分析或说明' },
        { name: 'clientName', description: '客户名称', example: '某跨国企,业, ABC公司' },
        { name: 'challenge', description: '挑战', example: '业务扩展困,难, 成本高' },
        { name: 'solution', description: '解决方案', example: '实施我们的SaaS平台' },
        { name: 'results', description: '成果', example: '效率提升30,%, 成本降低25%' },
        { name: 'activity', description: '活动名称', example: '团,建, 研讨会, 培训' },
        { name: 'description', description: '活动描述', example: '增强了团队协作能力' },
        { name: 'season', description: '季节', example: '春,季, 夏季, 圣诞节' },
        { name: 'expertName', description: '专家姓名', example: '张教,授, 李博士' },
        { name: 'quote', description: '引用语', example: '专家的观点或分析' }
      ]);
    }, 600);
  });
}; 