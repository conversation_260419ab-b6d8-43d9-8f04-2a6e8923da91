/**
 * Ultimate CTA + Footer Integration Component
 * Seamless integration of enhanced CTA and Footer with unified theming
 */

import React from 'react';
import { motion } from 'framer-motion';
import UltimateCT<PERSON> from './UltimateCTA';
import UltimateFooter from './UltimateFooter';
import { motionVariants } from "../../design-system/enhanced-motion-system";

interface UltimateCtaFooterSectionProps {
  onStartTrial?: () => void;
  onWatchDemo?: () => void;
}

const UltimateCtaFooterSection: React.FC<UltimateCtaFooterSectionProps> = ({
  onStartTrial,
  onWatchDemo
}) => {
  // Default handlers if not provided
  const handleStartTrial = onStartTrial || (() => {
    console.log('Start trial clicked');
    // You can implement default behavior here, such as:
    // - Opening a modal
    // - Redirecting to signup page
    // - Triggering analytics event
  });

  const handleWatchDemo = onWatchDemo || (() => {
    console.log('Watch demo clicked');
    // You can implement default behavior here, such as:
    // - Opening a video modal
    // - Redirecting to demo page
    // - Triggering analytics event
  });

  return (
    <motion.div
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, margin: "-100px" }}
      variants={motionVariants.ctaContainer}
      className="relative"
    >
      {/* CTA Section */}
      <UltimateCTA
        onStartTrial={handleStartTrial}
        onWatchDemo={handleWatchDemo}
      />
      
      {/* Seamless Transition Element */}
      <div className="relative h-1">
        {/* Gradient divider that creates smooth visual flow */}
        <div 
          className="absolute inset-0" 
          style={{
            background: 'linear-gradient(180deg, rgba(245, 243, 255, 0.8) 0%, rgba(248, 250, 252, 0.9) 50%, rgba(255, 255, 255, 1) 100%)'
          }}
        />
      </div>
      
      {/* Footer Section */}
      <UltimateFooter />
    </motion.div>
  );
};

export default UltimateCtaFooterSection;