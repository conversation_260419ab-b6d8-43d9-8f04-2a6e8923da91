@echo off
title ibuddy2 Development Mode
echo.
echo ================================================
echo    🛠️ ibuddy2 开发模式启动脚本
echo ================================================
echo.

echo 📋 检查开发环境...
where npm >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到 npm，请先安装 Node.js
    pause
    exit /b 1
)

echo ✅ Node.js 环境检查通过
echo.

echo 🔄 检查依赖安装状态...
if not exist "node_modules" (
    echo 📦 安装根目录依赖...
    npm install
)

if not exist "client\node_modules" (
    echo 📦 安装客户端依赖...
    cd client
    npm install
    cd ..
)

if not exist "api-gateway\node_modules" (
    echo 📦 安装API网关依赖...
    cd api-gateway
    npm install
    cd ..
)

if not exist "core-service\node_modules" (
    echo 📦 安装核心服务依赖...
    cd core-service
    npm install
    cd ..
)

if not exist "ai-service\node_modules" (
    echo 📦 安装AI服务依赖...
    cd ai-service
    npm install
    cd ..
)

echo.
echo 🔧 启动开发服务（监听文件变化）...
echo.

echo 📡 启动 API Gateway (开发模式)...
start "API Gateway Dev" cmd /k "cd api-gateway && npm run dev"
timeout /t 3 /nobreak >nul

echo 🔧 启动 Core Service (开发模式)...
start "Core Service Dev" cmd /k "cd core-service && npm run dev"
timeout /t 3 /nobreak >nul

echo 🤖 启动 AI Service (开发模式)...
start "AI Service Dev" cmd /k "cd ai-service && npm run dev"
timeout /t 3 /nobreak >nul

echo 🌐 启动 Client (开发模式)...
start "Client Dev" cmd /k "cd client && npm start"
timeout /t 3 /nobreak >nul

echo.
echo ================================================
echo    ✅ 开发环境启动完成！
echo ================================================
echo.
echo 🛠️ 开发服务信息:
echo    • 客户端:     http://localhost:3000 (热重载)
echo    • API网关:    http://localhost:3001 (自动重启)
echo    • 核心服务:   http://localhost:3002 (自动重启)
echo    • AI服务:     http://localhost:3003 (自动重启)
echo.
echo 📝 开发提示:
echo    • 文件保存后会自动重新加载
echo    • ESLint 和类型检查已启用
echo    • 开发者工具可用
echo.
echo 🛑 停止开发服务: 关闭对应的命令行窗口
echo.
echo 按任意键退出此窗口...
pause >nul 