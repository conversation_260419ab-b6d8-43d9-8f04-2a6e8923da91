// 环境变量动态设置工具
// 在客户端应用启动时运行此脚本来设置必要的环境变量

// 从后端获取Stripe公钥
export async function setupStripeConfig() {
  try {
    // Ensure apiUrl does not end with '/api' to avoid duplicate '/api/api'
    const rawApiUrl = process.env.REACT_APP_API_URL || 'http://localhost:3001';
    const apiUrl = rawApiUrl.replace(/\/api$/, '');
    
    console.log('🔄 正在获取Stripe配置...');
    const response = await fetch(`${apiUrl}/api/billing/config`);
    
    // 检查响应状态
    if (!response.ok) {
      const errorText = await response.text();
      let errorData;
      try {
        errorData = JSON.parse(errorText);
      } catch {
        errorData = { message: errorText };
      }
      
      console.warn(`⚠️ Stripe配置获取失败 (${response.status}):`, errorData.message);
      
      return {
        success: false,
        error: 'fetch_failed',
        message: errorData.message || `服务器响应错误: ${response.status}`,
        details: {
          status: response.status,
          statusText: response.statusText,
          apiUrl: apiUrl
        }
      };
    }
    
    const data = await response.json();
    
    if (data.success && data.data && data.data.publishableKey) {
      // 动态设置环境变量到window对象
      window.REACT_APP_STRIPE_PUBLISHABLE_KEY = data.data.publishableKey;
      window.REACT_APP_API_URL = apiUrl; // 不要包含/api前缀
      
      console.log('✅ Stripe配置加载成功');
      console.log('📊 Publishable Key:', data.data.publishableKey.substring(0, 12) + '...');
      console.log('🌐 API URL:', apiUrl);
      
      return {
        success: true,
        publishableKey: data.data.publishableKey,
        apiUrl: apiUrl,
        message: 'Stripe配置加载成功'
      };
    } else {
      console.warn('⚠️ Stripe配置响应格式无效:', data);
      return {
        success: false,
        error: 'invalid_response',
        message: 'Stripe配置响应格式无效',
        details: {
          response: data,
          expectedFormat: {
            success: true,
            data: {
              publishableKey: 'pk_test_...'
            }
          }
        }
      };
    }
  } catch (error) {
    console.error('❌ Stripe配置加载异常:', error);
    
    // 根据错误类型提供更具体的错误信息
    let errorMessage = 'Stripe配置加载失败';
    let errorType = 'network_error';
    
    if (error.name === 'TypeError' && error.message.includes('fetch')) {
      errorMessage = '无法连接到服务器，请检查网络连接或服务器状态';
      errorType = 'connection_error';
    } else if (error.name === 'AbortError') {
      errorMessage = '请求超时，请稍后重试';
      errorType = 'timeout_error';
    } else if (error.message.includes('JSON')) {
      errorMessage = '服务器响应格式错误';
      errorType = 'parse_error';
    }
    
    return {
      success: false,
      error: errorType,
      message: errorMessage,
      details: {
        originalError: error.message,
        stack: error.stack
      }
    };
  }
}

// 检查环境变量配置状态
export function checkEnvironmentConfig() {
  const config = {
    hasStripeKey: !!(window.REACT_APP_STRIPE_PUBLISHABLE_KEY || process.env.REACT_APP_STRIPE_PUBLISHABLE_KEY),
    hasApiUrl: !!(window.REACT_APP_API_URL || process.env.REACT_APP_API_URL),
    stripeKey: window.REACT_APP_STRIPE_PUBLISHABLE_KEY || process.env.REACT_APP_STRIPE_PUBLISHABLE_KEY || '',
    apiUrl: (window.REACT_APP_API_URL || process.env.REACT_APP_API_URL || 'http://localhost:3001').replace('/api', ''), // 移除重复的/api前缀
    isConfigured: false // 先设置为false，下面会计算
  };
  
  // 计算是否已配置
  config.isConfigured = config.hasStripeKey && config.hasApiUrl;
  
  return config;
}

// 初始化环境配置（在应用启动时调用）
export async function initializeEnvironment() {
  console.log('🔧 正在初始化iBuddy2环境...');
  
  const currentConfig = checkEnvironmentConfig();
  console.log('📋 当前配置状态:', {
    hasStripeKey: currentConfig.hasStripeKey,
    hasApiUrl: currentConfig.hasApiUrl,
    isConfigured: currentConfig.isConfigured
  });
  
  // 如果没有Stripe配置，尝试从后端获取
  if (!currentConfig.hasStripeKey) {
    console.log('🔄 尝试从后端加载Stripe配置...');
    const result = await setupStripeConfig();
    
    if (result.success) {
      console.log('✅ 环境初始化完成');
      return { 
        success: true, 
        message: result.message || 'Stripe配置已从后端加载',
        config: {
          stripeConfigured: true,
          apiUrl: result.apiUrl
        }
      };
    } else {
      console.log('⚠️ 使用备用配置');
      return { 
        success: false, 
        message: result.message || '无法加载Stripe配置',
        error: result.error,
        details: result.details,
        fallback: {
          message: '系统将使用备用配置运行，某些功能可能受限',
          actions: [
            '检查服务器连接状态',
            '确认Stripe配置是否正确',
            '联系系统管理员'
          ]
        }
      };
    }
  } else {
    console.log('✅ 环境已配置完毕');
    return { 
      success: true, 
      message: '环境已配置完毕',
      config: {
        stripeConfigured: true,
        source: 'existing'
      }
    };
  }
}