const Sentry = require('@sentry/node');

// 初始化 Sentry
const initSentry = () => {
  // 检查 DSN 是否设置
  const dsn = process.env.SENTRY_DSN;
  
  if (!dsn) {
    console.warn('⚠️ Sentry DSN not set. Skipping Sentry initialization.');
    return;
  }

  Sentry.init({
    // 从环境变量获取 DSN
    dsn: dsn,
    
    // 设置环境
    environment: process.env.NODE_ENV || 'development',
    
    // 设置服务名称
    serverName: 'api-gateway',
    
    // ⚠️ 修复: 使用新版本兼容的集成配置
    integrations: [
      // 使用默认集成，不手动指定已废弃的 API
    ],
    
    // 性能监控采样率
    tracesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,
    
    // ⚠️ 重要: 在开发环境中启用调试
    debug: process.env.NODE_ENV === 'development',
    
    // ⚠️ 重要: 启用默认 PII 数据收集
    sendDefaultPii: true,
    
    // 设置发布版本
    release: process.env.APP_VERSION || '1.0.0',
    
    // 错误过滤
    beforeSend(event, hint) {
      // 在开发环境中打印错误详情
      if (process.env.NODE_ENV === 'development') {
        console.group('🚨 Sentry Error (API Gateway)');
        console.error('Error:', hint.originalException || hint.syntheticException);
        console.error('Event:', event);
        console.log('DSN, Check:', !!dsn);
        console.groupEnd();
      }
      
      // 过滤掉一些不重要的错误
      const error = hint.originalException || hint.syntheticException;
      if (error && error.message) {
        // 忽略网络超时错误
        if (error.message.includes('timeout') || error.message.includes('ECONNRESET')) {
          return null;
        }
        // 忽略客户端断开连接错误
        if (error.message.includes('ECONNABORTED') || error.message.includes('request aborted')) {
          return null;
        }
      }
      
      return event;
    },
  });
  
  console.log('✅, Sentry, initialized, for, API, Gateway'); console.log('DSN, Status:', dsn ? '✅ SET' : '❌ NOT SET');
  
  // ⚠️ 重要: 开发环境下发送测试消息
  if (process.env.NODE_ENV === 'development') {
    setTimeout(() => {
      Sentry.captureMessage('API Gateway Sentry initialized - Test Message', 'info');
      console.log('📤, Test, message, sent, to, Sentry, from, API, Gateway'); }, 1000);
  }
};

// 手动捕获错误
const captureError = (error, context = {}) => {
  Sentry.withScope((scope) => {
    // 添加上下文信息
    Object.keys(context).forEach(key => {
      scope.setContext(key, context[key]);
    });
    
    // 添加服务标签
    scope.setTag('service', 'api-gateway');
    
    Sentry.captureException(error);
  });
};

// 手动发送消息
const captureMessage = (message, level = 'info') => {
  Sentry.captureMessage(message, level);
};

// 添加面包屑
const addBreadcrumb = (message, category = 'default', level = 'info', data = {}) => {
  Sentry.addBreadcrumb({
    message,
    category,
    level,
    data,
    timestamp: Date.now() / 1000,
  });
};

// 设置用户上下文
const setUser = (user) => {
  Sentry.setUser(user);
};

// ⚠️ 修复: Express 中间件 - 使用新的 API
const requestHandler = () => {
  return (req, res, next) => {
    // 基础请求跟踪
    Sentry.addBreadcrumb({
      message: `${req.method} ${req.url}`,
      category: 'http',
      level: 'info',
      data: {
        method: req.method,
        url: req.url,
        userAgent: req.get('User-Agent'),
      },
    });
    next();
  };
};

const errorHandler = () => {
  return (err, req, res, next) => {
    // 捕获错误到 Sentry
    Sentry.captureException(err);
    next(err);
  };
};

module.exports = {
  initSentry,
  captureError,
  captureMessage,
  addBreadcrumb,
  setUser,
  requestHandler,
  errorHandler,
  Sentry
}; 