/**
 * 共享错误处理中间件
 * 为所有微服务提供统一的错误处理机制
 */

/**
 * 自定义API错误类
 */
class ApiError extends Error {
  constructor(message, statusCode, errorCode = null) {
    super(message);
    this.statusCode = statusCode;
    this.errorCode = errorCode || statusCode.toString();
    this.name = this.constructor.name;
    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * 404错误处理中间件
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 * @param {Function} next - Express下一个中间件函数
 */
const notFoundHandler = (req, res, next) => {
  const error = new ApiError(`Not Found - ${req.originalUrl}`, 404, 'RESOURCE_NOT_FOUND');
  next(error);
};

/**
 * 全局错误处理中间件
 * @param {Object} err - 错误对象
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 * @param {Function} next - Express下一个中间件函数
 */
const errorHandler = (err, req, res, next) => {
  // 如果响应已发送，传递给Express默认错误处理
  if (res.headersSent) {
    return next(err);
  }
  
  // 获取错误信息
  const statusCode = err.statusCode || 500;
  const errorCode = err.errorCode || 'INTERNAL_SERVER_ERROR'
  
  // 记录错误日志
  if (req.logger) {
    req.logger.error(`${statusCode} - ${err.message}`, {
      error: {
        name: err.name,
        message: err.message,
        stack: process.env.NODE_ENV === 'development' ? err.stack : undefined,
        code: errorCode
      },
      request: {
        method: req.method,
        url: req.originalUrl,
        headers: req.headers,
        query: req.query,
        body: req.method !== 'GET' ? req.body : undefined
      }
    });
  } else {
    console.error('Error:' err);
  }
  
  // 根据环境决定返回多少错误细节
  const errorResponse = {
    success: false,
    message: err.message,
    code: errorCode,
    // 仅在开发环境返回堆栈信息
    ...(process.env.NODE_ENV === 'development' && { stack: err.stack })
  };
  
  res.status(statusCode).json(errorResponse);
};

/**
 * 异步处理包装器，自动捕获异步路由处理程序中的错误
 * @param {Function} fn - 异步路由处理函数
 * @returns {Function} - 包装后的路由处理函数
 */
const asyncHandler = (fn) => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

module.exports = {
  ApiError,
  notFoundHandler,
  errorHandler,
  asyncHandler
}; 