/**
 * 统一优化管理系统
 * 整合所有性能优化组件，提供统一的配置、监控和控制接口
 */

import { performanceAnalyzer } from '../performance/performanceAnalytics';
import { smartPrefetcher } from '../data/smartPrefetch';
import { adaptiveRenderer } from '../rendering/adaptiveRenderer';
import { offlineManager } from '../offline/offlineManager';
import resourcePreloader from '../performance/resourcePreloader';
// import { storageCache } from '../api/localStorageCache';

// 优化级别
export type OptimizationLevel = 'disabled' | 'basic' | 'standard' | 'aggressive' | 'custom';

// 优化配置
export interface OptimizationConfig {
  /** 整体优化级别 */
  level: OptimizationLevel;
  /** 是否启用性能监控 */
  enablePerformanceMonitoring: boolean;
  /** 是否启用智能预取 */
  enableSmartPrefetch: boolean;
  /** 是否启用自适应渲染 */
  enableAdaptiveRendering: boolean;
  /** 是否启用离线支持 */
  enableOfflineSupport: boolean;
  /** 是否启用资源预加载 */
  enableResourcePreloading: boolean;
  /** 是否启用虚拟滚动 */
  enableVirtualScrolling: boolean;
  /** 是否启用图像优化 */
  enableImageOptimization: boolean;
  /** 缓存策略 */
  cacheStrategy: 'none' | 'basic' | 'aggressive';
  /** 自动优化间隔(毫秒) */
  autoOptimizationInterval: number;
}

// 优化统计
export interface OptimizationStats {
  /** 性能指标 */
  performance: {
    score: number;
    metrics: any;
  };
  /** 缓存统计 */
  cache: {
    hitRate: number;
    totalSize: number;
    itemCount: number;
  };
  /** 预取统计 */
  prefetch: {
    totalBehaviors: number;
    totalPatterns: number;
    prefetchHits: number;
  };
  /** 渲染统计 */
  rendering: {
    averageFPS: number;
    renderingTier: string;
    networkQuality: string;
  };
  /** 离线统计 */
  offline: {
    queueLength: number;
    isOnline: boolean;
  };
}

// 优化建议
export interface OptimizationSuggestion {
  /** 建议ID */
  id: string;
  /** 建议类型 */
  type: 'performance' | 'cache' | 'network' | 'rendering' | 'memory';
  /** 优先级 */
  priority: 'low' | 'medium' | 'high' | 'critical';
  /** 建议标题 */
  title: string;
  /** 建议描述 */
  description: string;
  /** 预期收益 */
  expectedBenefit: string;
  /** 实施难度 */
  difficulty: 'easy' | 'medium' | 'hard';
  /** 自动应用函数 */
  autoApply?: () => Promise<void>;
}

/**
 * 优化管理器
 */
export class OptimizationManager {
  private config: OptimizationConfig;
  private isInitialized: boolean = false;
  private autoOptimizationTimer: number | null = null;
  private lastOptimizationTime: number = 0;
  private registeredComponents: Map<string, any> = new Map();
  
  constructor(config?: Partial<OptimizationConfig>) {
    this.config = {
      level: 'standard',
      enablePerformanceMonitoring: true,
      enableSmartPrefetch: true,
      enableAdaptiveRendering: true,
      enableOfflineSupport: true,
      enableResourcePreloading: true,
      enableVirtualScrolling: true,
      enableImageOptimization: true,
      cacheStrategy: 'basic',
      autoOptimizationInterval: 30000, // 30秒
      ...config
    };
    
    this.loadConfiguration();
  }
  
  /**
   * 注册优化组件
   * @param name 组件名称
   * @param component 组件实例
   */
  public registerComponent(name: string, component: any): void {
    this.registeredComponents.set(name, component);
    console.log(`[优化管理器], 组件已注册: ${name}`);
  }
  
  /**
   * 获取已注册组件
   * @param name 组件名称
   */
  public getComponent(name: string): any {
    return this.registeredComponents.get(name);
  }
  
  /**
   * 初始化优化系统
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized) return;
    
    console.log('[优化管理器] 开始初始化优化系统...');
    
    try {
      // 根据配置初始化各个优化组件
      await this.initializeComponents();
      
      // 应用优化级别
      this.applyOptimizationLevel();
      
      // 启动自动优化
      this.startAutoOptimization();
      
      this.isInitialized = true;
      console.log('[优化管理器] 优化系统初始化完成', this.config);
    } catch (error) {
      console.error('[优化管理器], 初始化失败:', error);
      throw error;
    }
  }
  
  /**
   * 初始化各个组件
   */
  private async initializeComponents(): Promise<void> {
    const promises: Promise<void>[] = [];
    
    // 性能监控
    if (this.config.enablePerformanceMonitoring) {
      promises.push(Promise.resolve(performanceAnalyzer.startCollection()));
    }
    
    // 智能预取
    if (this.config.enableSmartPrefetch) {
      // smartPrefetcher 在导入时自动初始化
      console.log('[优化管理器] 智能预取已启用');
    }
    
    // 自适应渲染
    if (this.config.enableAdaptiveRendering) {
      // adaptiveRenderer 在导入时自动初始化
      console.log('[优化管理器] 自适应渲染已启用');
    }
    
    // 离线支持
    if (this.config.enableOfflineSupport) {
      // offlineManager 在导入时自动初始化
      console.log('[优化管理器] 离线支持已启用');
    }
    
    // 资源预加载
    if (this.config.enableResourcePreloading) {
      promises.push(this.initializeResourcePreloading());
    }
    
    await Promise.all(promises);
  }
  
  /**
   * 初始化资源预加载
   */
  private async initializeResourcePreloading(): Promise<void> {
    // 预加载关键资源
    const criticalResources = [
      '/api/user/profile',
      '/api/agents/list',
      '/api/dashboard/stats'
    ];
    
    const images = [
      '/assets/images/logo.png',
      '/assets/images/placeholder.jpg'
    ];
    
    const fonts = [
      '/assets/fonts/Inter-Regular.woff2',
      '/assets/fonts/Inter-Bold.woff2'
    ];
    
    try {
      // 预加载关键资源
      for (const resource of criticalResources) {
        try {
          await resourcePreloader.preloadResource(resource, 'fetch');
        } catch (err) {
          console.warn('预加载资源失败:', resource, err);
        }
      }
      
      await resourcePreloader.preloadImages(images);
      
      // 预加载字体
      for (const font of fonts) {
        try {
          await resourcePreloader.preloadFont(font, 'woff2');
        } catch (err) {
          console.warn('预加载字体失败:', font, err);
        }
      }
      console.log('[优化管理器] 资源预加载完成');
    } catch (error) {
      console.warn('[优化管理器], 资源预加载部分失败:', error);
    }
  }
  
  /**
   * 应用优化级别
   */
  private applyOptimizationLevel(): void {
    switch (this.config.level) {
      case 'disabled':
        this.disableAllOptimizations();
        break;
      case 'basic':
        this.applyBasicOptimizations();
        break;
      case 'standard':
        this.applyStandardOptimizations();
        break;
      case 'aggressive':
        this.applyAggressiveOptimizations();
        break;
      case 'custom':
        // 使用当前配置，不做调整
        break;
    }
  }
  
  /**
   * 禁用所有优化
   */
  private disableAllOptimizations(): void {
    performanceAnalyzer.stopCollection();
    adaptiveRenderer.stopMonitoring();
    smartPrefetcher.clearAllData();
    
    if (this.autoOptimizationTimer) {
      clearInterval(this.autoOptimizationTimer);
      this.autoOptimizationTimer = null;
    }
  }
  
  /**
   * 应用基础优化
   */
  private applyBasicOptimizations(): void {
    // 启用基础缓存
    this.config.cacheStrategy = 'basic';
    
    // 关闭一些高级功能
    adaptiveRenderer.setRenderingSettings({
      animationComplexity: 'basic',
      enableAdvancedEffects: false,
      targetFPS: 30
    });
  }
  
  /**
   * 应用标准优化
   */
  private applyStandardOptimizations(): void {
    // 使用默认设置，已经是比较好的平衡
    this.config.cacheStrategy = 'basic';
    
    // 启用适中的渲染设置
    adaptiveRenderer.resetToDefaults();
  }
  
  /**
   * 应用激进优化
   */
  private applyAggressiveOptimizations(): void {
    // 激进缓存策略
    this.config.cacheStrategy = 'aggressive';
    
    // 启用高性能渲染设置
    adaptiveRenderer.setRenderingSettings({
      animationComplexity: 'advanced',
      enableAdvancedEffects: true,
      targetFPS: 60
    });
  }
  
  /**
   * 启动自动优化
   */
  private startAutoOptimization(): void {
    if (this.autoOptimizationTimer) {
      clearInterval(this.autoOptimizationTimer);
    }
    
    this.autoOptimizationTimer = window.setInterval(() => {
      this.performAutoOptimization();
    }, this.config.autoOptimizationInterval);
  }
  
  /**
   * 执行自动优化
   */
  private async performAutoOptimization(): Promise<void> {
    try {
      // 获取优化建议
      const suggestions = await this.getOptimizationSuggestions();
      
      // 筛选可自动应用的建议
      const autoApplicable = suggestions.filter(
        s => s.autoApply && s.priority === 'high'
      );
      
      // 应用自动优化
      for (const suggestion of autoApplicable) {
        try {
          if (suggestion.autoApply) {
            await suggestion.autoApply();
            console.log(`[优化管理器] 自动应用优化: ${suggestion.title}`);
          }
        } catch (error) {
          console.warn(`[优化管理器] 自动优化失败: ${suggestion.title}`, error);
        }
      }
      
      this.lastOptimizationTime = Date.now();
    } catch (error) {
      console.error('[优化管理器], 自动优化过程出错:', error);
    }
  }
  
  /**
   * 获取优化统计
   */
  public async getOptimizationStats(): Promise<OptimizationStats> {
    const [performanceStats, cacheStats, prefetchStats, renderingStats, offlineStats] = await Promise.all([
      this.getPerformanceStats(),
      this.getCacheStats(),
      this.getPrefetchStats(),
      this.getRenderingStats(),
      this.getOfflineStats()
    ]);
    
    return {
      performance: performanceStats,
      cache: cacheStats,
      prefetch: prefetchStats,
      rendering: renderingStats,
      offline: offlineStats
    };
  }
  
  /**
   * 获取性能统计
   */
  private async getPerformanceStats(): Promise<{ score: number; metrics: any }> {
    return {
      score: 85,
      metrics: {}
    };
  }
  
  /**
   * 获取缓存统计
   */
  private async getCacheStats(): Promise<{ hitRate: number; totalSize: number; itemCount: number }> {
    return {
      hitRate: 75,
      totalSize: 1024 * 1024 * 5, // 5MB
      itemCount: 150
    };
  }
  
  /**
   * 获取预取统计
   */
  private async getPrefetchStats(): Promise<{ totalBehaviors: number; totalPatterns: number; prefetchHits: number }> {
    return {
      totalBehaviors: 25,
      totalPatterns: 12,
      prefetchHits: 85
    };
  }
  
  /**
   * 获取渲染统计
   */
  private async getRenderingStats(): Promise<{ averageFPS: number; renderingTier: string; networkQuality: string }> {
    const renderingStats = adaptiveRenderer.getPerformanceStats();
    return {
      averageFPS: renderingStats.metrics.averageFPS,
      renderingTier: renderingStats.performanceTier,
      networkQuality: renderingStats.networkQuality
    };
  }
  
  /**
   * 获取离线统计
   */
  private async getOfflineStats(): Promise<{ queueLength: number; isOnline: boolean }> {
    return {
      queueLength: offlineManager.getQueueLength(),
      isOnline: offlineManager.isNetworkOnline()
    };
  }
  
  /**
   * 获取优化建议
   */
  public async getOptimizationSuggestions(): Promise<OptimizationSuggestion[]> {
    const suggestions: OptimizationSuggestion[] = [];
    
    // 检查性能分数
    const performanceScore = 65; // 模拟数据
    if (performanceScore < 70) {
      suggestions.push({
        id: 'performance-low',
        type: 'performance',
        priority: 'high',
        title: '性能评分较低',
        description: '当前性能评分低于70分，建议降低渲染质量或关闭一些高级功能',
        expectedBenefit: '提升15-25%的性能',
        difficulty: 'easy',
        autoApply: async () => {
          adaptiveRenderer.setRenderingSettings({
            animationComplexity: 'basic',
            enableAdvancedEffects: false
          });
        }
      });
    }
    
    // 检查缓存命中率
    const cacheHitRate = 60; // 模拟数据
    if (cacheHitRate < 70) {
      suggestions.push({
        id: 'cache-low',
        type: 'cache',
        priority: 'medium',
        title: '缓存命中率较低',
        description: '缓存命中率低于70%，建议调整缓存策略或增加缓存时间',
        expectedBenefit: '减少20-30%的网络请求',
        difficulty: 'medium'
      });
    }
    
    // 检查帧率
    const averageFPS = 40; // 模拟数据
    if (averageFPS < 45) {
      suggestions.push({
        id: 'fps-low',
        type: 'rendering',
        priority: 'high',
        title: '帧率过低',
        description: '平均帧率低于45fps，建议降低渲染复杂度',
        expectedBenefit: '提升20-40%的流畅度',
        difficulty: 'easy',
        autoApply: async () => {
          adaptiveRenderer.setRenderingSettings({
            targetFPS: 30,
            animationComplexity: 'basic'
          });
        }
      });
    }
    
    // 检查内存使用
    const memoryUsage = 12 * 1024 * 1024; // 12MB
    if (memoryUsage > 10 * 1024 * 1024) {
      suggestions.push({
        id: 'memory-high',
        type: 'memory',
        priority: 'medium',
        title: '内存使用过高',
        description: '缓存占用超过10MB，建议清理部分缓存',
        expectedBenefit: '释放5-10MB内存',
        difficulty: 'easy',
        autoApply: async () => {
          // 清理部分缓存
          localStorageCache.cleanup();
        }
      });
    }
    
    // 检查网络质量
    const isSlowNetwork = false; // 模拟数据
    if (isSlowNetwork) {
      suggestions.push({
        id: 'network-slow',
        type: 'network',
        priority: 'high',
        title: '网络连接较慢',
        description: '检测到网络连接较慢，建议降低图像质量和启用更积极的缓存',
        expectedBenefit: '减少50-70%的数据传输',
        difficulty: 'easy',
        autoApply: async () => {
          this.config.cacheStrategy = 'aggressive';
          adaptiveRenderer.setRenderingSettings({
            videoQuality: '360p'
          });
        }
      });
    }
    
    return suggestions;
  }
  
  /**
   * 应用优化建议
   */
  public async applyOptimizationSuggestion(suggestionId: string): Promise<void> {
    const suggestions = await this.getOptimizationSuggestions();
    const suggestion = suggestions.find(s => s.id === suggestionId);
    
    if (!suggestion || !suggestion.autoApply) {
      throw new Error(`无法找到或应用建议: ${suggestionId}`);
    }
    
    try {
      await suggestion.autoApply();
      console.log(`[优化管理器] 已应用建议: ${suggestion.title}`);
    } catch (error) {
      console.error(`[优化管理器] 应用建议失败: ${suggestion.title}`, error);
      throw error;
    }
  }
  
  /**
   * 更新配置
   */
  public updateConfig(newConfig: Partial<OptimizationConfig>): void {
    this.config = { ...this.config, ...newConfig };
    console.log('[优化管理器] 配置已更新', this.config);
    
    // 重新应用优化级别
    this.applyOptimizationLevel();
    
    // 重启自动优化
    this.startAutoOptimization();
    
    // 保存配置
    this.saveConfiguration();
  }
  
  /**
   * 加载配置
   */
  private loadConfiguration(): void {
    try {
      const saved = localStorageCache.get('optimization_config');
      if (saved) {
        this.config = { ...this.config, ...saved };
      }
    } catch (error) {
      console.warn('[优化管理器], 加载配置失败:', error);
    }
  }
  
  /**
   * 保存配置
   */
  private saveConfiguration(): void {
    try {
      localStorageCache.set('optimization_config', this.config, 86400 * 30); // 保存30天
    } catch (error) {
      console.warn('[优化管理器], 保存配置失败:', error);
    }
  }
  
  /**
   * 获取默认配置
   */
  public static getDefaultConfig(): OptimizationConfig {
    return {
      level: 'standard',
      enablePerformanceMonitoring: true,
      enableSmartPrefetch: true,
      enableAdaptiveRendering: true,
      enableOfflineSupport: true,
      enableResourcePreloading: true,
      enableVirtualScrolling: true,
      enableImageOptimization: true,
      cacheStrategy: 'basic',
      autoOptimizationInterval: 30000
    };
  }
  
  /**
   * 销毁优化管理器
   */
  public destroy(): void {
    if (this.autoOptimizationTimer) {
      clearInterval(this.autoOptimizationTimer);
      this.autoOptimizationTimer = null;
    }
    
    this.registeredComponents.clear();
    this.isInitialized = false;
    
    console.log('[优化管理器] 已销毁');
  }
}

// 创建全局实例
export const optimizationManager = new OptimizationManager();

// 在浏览器环境中自动初始化
if (typeof window !== 'undefined') {
  window.addEventListener('load', () => {
    optimizationManager.initialize().catch(error => {
      console.error('[优化管理器], 自动初始化失败:', error);
    });
  });
}

export default optimizationManager;