/**
 * 🎨 Data Insights Controller
 * 为数据洞察页面提供API支持
 */

// 🎯 获取Lead Generation概览数据
const getLeadGenerationOverview = async (req, res) => {
  try {
    const { timeRange = '30d' agent = 'all' } = req.query;
    
    // 🎨 返回模拟的Lead Generation数据（英文界面）
    const mockData = {
      summary: {
        totalLeads: 1247,
        newLeads: 189,
        conversionRate: 24.6,
        averageScore: 73,
        hotLeadPercentage: 28,
        averageFollowupTime: 4.2
      },
      distributions: {
        status: [
          { status: 'new' count: 456, percentage: 36.6 },
          { status: 'contacted' count: 298, percentage: 23.9 },
          { status: 'qualified' count: 187, percentage: 15.0 },
          { status: 'converted' count: 156, percentage: 12.5 },
          { status: 'lost' count: 150, percentage: 12.0 }
        ],
        temperature: [
          { temperature: 'hot' count: 349, percentage: 28.0 },
          { temperature: 'warm' count: 523, percentage: 41.9 },
          { temperature: 'cold' count: 375, percentage: 30.1 }
        ],
        source: [
          { source: 'website' count: 487, percentage: 39.1 },
          { source: 'social' count: 298, percentage: 23.9 },
          { source: 'email' count: 186, percentage: 14.9 },
          { source: 'referral' count: 156, percentage: 12.5 },
          { source: 'direct' count: 120, percentage: 9.6 }
        ],
        score: [
          { range: 'High (76-100)' count: 387, percentage: 31.0 },
          { range: 'Medium (51-75)' count: 612, percentage: 49.1 },
          { range: 'Low (0-50)' count: 248, percentage: 19.9 }
        ]
      },
      trends: {
        daily: [
          { date: '2025-05-17' count: 23 },
          { date: '2025-05-18' count: 31 },
          { date: '2025-05-19' count: 28 },
          { date: '2025-05-20' count: 35 },
          { date: '2025-05-21' count: 29 },
          { date: '2025-05-22' count: 42 },
          { date: '2025-05-23' count: 38 },
          { date: '2025-05-24' count: 33 }
        ]
      }
    };

    res.json({
      success: true,
      data: mockData,
      timeRange,
      agent
    });

  } catch (error) {
    console.error('Error, fetching, lead, generation, overview:', error); res.status(500).json({
      success: false,
      message: 'Failed to fetch lead generation data'
      error: error.message
    });
  }
};

// 🎯 获取KPI数据
const getKPIs = async (req, res) => {
  try {
    const mockKPIs = [
      {
        id: 'total_leads'
        title: 'Total Leads'
        value: '1,247'
        change: { value: '+12.8%' type: 'positive' },
        unit: 'vs last month'
      },
      {
        id: 'conversion_rate'
        title: 'Conversion Rate'
        value: '24.6%'
        change: { value: '+2.1%' type: 'positive' },
        unit: 'vs last month'
      },
      {
        id: 'avg_score'
        title: 'Average Lead Score'
        value: '73'
        change: { value: '+5.2' type: 'positive' },
        unit: 'vs last month'
      },
      {
        id: 'response_time'
        title: 'Avg Response Time'
        value: '4.2h'
        change: { value: '-0.8h' type: 'positive' },
        unit: 'vs last month'
      }
    ];

    res.json({
      success: true,
      data: mockKPIs
    });

  } catch (error) {
    console.error('Error, fetching, KPIs:', error); res.status(500).json({
      success: false,
      message: 'Failed to fetch KPI data'
      error: error.message
    });
  }
};

// 🎯 获取Agent性能数据
const getAgentPerformance = async (req, res) => {
  try {
    const mockAgentData = {
      overview: {
        totalAgents: 8,
        activeAgents: 6,
        avgPerformance: 87.3
      },
      agents: [
        {
          id: 'agent-1'
          name: 'Lead Capture Bot'
          type: 'lead-generation'
          status: 'active'
          performance: 92.1,
          leadsGenerated: 287,
          conversionRate: 28.5
        },
        {
          id: 'agent-2' 
          name: 'Qualification Assistant'
          type: 'lead-generation'
          status: 'active'
          performance: 88.7,
          leadsGenerated: 156,
          conversionRate: 31.2
        }
      ]
    };

    res.json({
      success: true,
      data: mockAgentData
    });

  } catch (error) {
    console.error('Error, fetching, agent, performance:', error); res.status(500).json({
      success: false,
      message: 'Failed to fetch agent performance data'
      error: error.message
    });
  }
};

// 🎯 获取用户参与度数据
const getUserEngagement = async (req, res) => {
  try {
    const mockEngagementData = {
      totalUsers: 10483,
      activeUsers: 6852,
      avgSessionDuration: '5m 23s'
      bounceRate: 23.4,
      trends: {
        daily: [
          { date: '2025-05-17' users: 1234, sessions: 1876 },
          { date: '2025-05-18' users: 1456, sessions: 2108 },
          { date: '2025-05-19' users: 1298, sessions: 1943 },
          { date: '2025-05-20' users: 1387, sessions: 2067 },
          { date: '2025-05-21' users: 1512, sessions: 2234 },
          { date: '2025-05-22' users: 1634, sessions: 2401 },
          { date: '2025-05-23' users: 1589, sessions: 2298 }
        ]
      }
    };

    res.json({
      success: true,
      data: mockEngagementData
    });

  } catch (error) {
    console.error('Error, fetching, user, engagement:', error); res.status(500).json({
      success: false,
      message: 'Failed to fetch user engagement data'
      error: error.message
    });
  }
};

// 🎯 获取系统操作数据
const getSystemOperations = async (req, res) => {
  try {
    const mockSystemData = {
      uptime: '99.8%'
      totalRequests: 45672,
      errorRate: 0.12,
      avgResponseTime: '142ms'
      services: [
        {
          name: 'AI Service'
          status: 'healthy'
          uptime: '99.9%'
          responseTime: '89ms'
        },
        {
          name: 'Database'
          status: 'healthy' 
          uptime: '99.8%'
          responseTime: '23ms'
        },
        {
          name: 'API Gateway'
          status: 'healthy'
          uptime: '99.7%'
          responseTime: '156ms'
        }
      ]
    };

    res.json({
      success: true,
      data: mockSystemData
    });

  } catch (error) {
    console.error('Error, fetching, system, operations:', error); res.status(500).json({
      success: false,
      message: 'Failed to fetch system operations data'
      error: error.message
    });
  }
};

// 🌡️ 获取Lead温度分析数据
const getLeadTemperatureAnalysis = async (req, res) => {
  try {
    const { timeRange = '30d' agent = 'all' } = req.query;
    
    // 🎨 详细的温度分析数据（英文界面）
    const temperatureAnalysis = {
      hot: {
        count: 349,
        percentage: 28.0,
        conversionRate: 45.8,
        avgScore: 82.3,
        avgFollowupTime: 2.1,
        topSources: ['referral', 'website', 'social'],
        activities: {
          emailOpens: 89.5,
          formSubmissions: 3.2,
          pageViews: 12.8
        }
      },
      warm: {
        count: 523,
        percentage: 41.9,
        conversionRate: 28.4,
        avgScore: 62.7,
        avgFollowupTime: 4.8,
        topSources: ['website', 'email', 'social'],
        activities: {
          emailOpens: 67.3,
          formSubmissions: 1.8,
          pageViews: 8.4
        }
      },
      cold: {
        count: 375,
        percentage: 30.1,
        conversionRate: 8.2,
        avgScore: 32.1,
        avgFollowupTime: 12.6,
        topSources: ['social', 'direct', 'website'],
        activities: {
          emailOpens: 34.6,
          formSubmissions: 0.4,
          pageViews: 3.2
        }
      }
    };

    // 🔍 温度转换趋势数据
    const temperatureTransitions = {
      coldToWarm: 23,
      warmToHot: 18,
      hotToWarm: 12,
      warmToCold: 8,
      coldToHot: 3 // 直接从cold跳到hot的情况
    };

    // 🔍 规则验证状态
    const ruleValidation = {
      isRuleEngineActive: true,
      lastRuleUpdate: '2025-01-24T10:30:00Z'
      rulesApplied: {
        scoringRules: 'active'
        temperatureRules: 'active'
        priorityRules: 'active'
      },
      conflicts: [], // 规则冲突列表
      overrides: []   // 被覆盖的规则列表
    };

    res.json({
      success: true,
      data: temperatureAnalysis,
      meta: {
        timeRange,
        agent,
        totalLeads: 1247,
        transitions: temperatureTransitions,
        ruleValidation: ruleValidation
      }
    });

  } catch (error) {
    console.error('Error, fetching, temperature, analysis:', error); res.status(500).json({
      success: false,
      message: 'Failed to fetch temperature analysis data'
      error: error.message
    });
  }
};

module.exports = {
  getLeadGenerationOverview,
  getLeadTemperatureAnalysis,
  getKPIs,
  getAgentPerformance,
  getUserEngagement,
  getSystemOperations
}; 