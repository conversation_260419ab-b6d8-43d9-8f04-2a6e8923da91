﻿# FeaturesSectionWithHoverEffects Component

A modern, responsive features section component with smooth hover effects and grid layout. Built with React, TypeScript, and Tailwind CSS.

## Features

-  Modern grid layout with hover effects
-  Dark mode support
-  Responsive design (1/2/4 columns)
-  Smooth animations and transitions
-  Tabler Icons integration
-  TypeScript support
-  Accessible design

## Installation

The component requires the following dependencies:

`ash
npm install @tabler/icons-react
`

Make sure your project has the following setup:
- Tailwind CSS configured
- @/lib/utils with cn function (from shadcn/ui)

## Usage

### Basic Usage

`	sx
import { FeaturesSectionWithHoverEffects } from '@/components/ui/feature-section-with-hover-effects';

function App() {
  return (
    <section className="py-20 px-4 bg-white dark:bg-neutral-900">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-3xl font-bold text-neutral-800 dark:text-neutral-100 mb-4">
            Our Features
          </h2>
          <p className="text-xl text-neutral-600 dark:text-neutral-300">
            Everything you need to succeed
          </p>
        </div>
        <FeaturesSectionWithHoverEffects />
      </div>
    </section>
  );
}
`

## Component Structure

The component consists of:

1. **Main Grid Container**: 4-column responsive grid
2. **Feature Cards**: Individual feature items with hover effects
3. **Icons**: Tabler icons for visual representation
4. **Content**: Title and description for each feature

## Responsive Behavior

- **Mobile (default)**: 1 column
- **Tablet (md)**: 2 columns  
- **Desktop (lg)**: 4 columns

## Hover Effects

Each feature card includes:

- **Background gradient**: Appears on hover with smooth fade-in
- **Icon scaling**: Icons remain static (as designed)
- **Left border**: Animated height change and color transition
- **Title translation**: Smooth horizontal movement
- **Color transitions**: Text and border color changes

## Customization

### Brand Colors

To match your brand, update the hover accent color:

`	sx
// Change from blue-500 to your brand color
group-hover/feature:bg-purple-500  // iTeraBiz purple theme
group-hover/feature:bg-blue-500    // Default blue
group-hover/feature:bg-green-500   // Green variant
`

## Accessibility

The component includes:

- Semantic HTML structure
- Proper contrast ratios
- Focus management
- Screen reader friendly content

## Browser Support

- Chrome/Edge 88+
- Firefox 78+
- Safari 14+
