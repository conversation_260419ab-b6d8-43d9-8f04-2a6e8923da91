import { Home, User, Briefcase, FileText, Phone } from 'lucide-react'
import { NavBar } from "@/components/ui/tubelight-navbar"
import { useState } from 'react'

export function NavBarDemo() {
  const [isDarkMode, setIsDarkMode] = useState(false)
  
  const navItems = [
    { name: 'Home', url: '#hero', icon: Home },
    { name: 'Features', url: '#features', icon: Briefcase },
    { name: 'Pricing', url: '#pricing', icon: FileText },
    { name: 'About', url: '#testimonials', icon: User },
    { name: 'Contact', url: '#cta', icon: Phone }
  ]

  return (
    <div className="min-h-screen bg-background">
      <NavBar 
        items={navItems} 
        isDarkMode={isDarkMode}
        onDarkModeToggle={setIsDarkMode}
      />
      
      {/* Demo content sections */}
      <section id="hero" className="h-screen flex items-center justify-center bg-gradient-to-br from-primary/20 to-secondary/20">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-primary mb-4">Hero Section</h1>
          <p className="text-muted-foreground">This is the hero section with smooth scrolling navigation</p>
        </div>
      </section>
      
      <section id="features" className="h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-primary mb-4">Features Section</h1>
          <p className="text-muted-foreground">Showcase your amazing features here</p>
        </div>
      </section>
      
      <section id="pricing" className="h-screen flex items-center justify-center bg-muted/30">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-primary mb-4">Pricing Section</h1>
          <p className="text-muted-foreground">Display your pricing plans here</p>
        </div>
      </section>
      
      <section id="testimonials" className="h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-primary mb-4">About/Testimonials Section</h1>
          <p className="text-muted-foreground">Share testimonials and about information</p>
        </div>
      </section>

      <section id="cta" className="h-screen flex items-center justify-center bg-muted/30">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-primary mb-4">Contact Us (CTA)</h1>
          <p className="text-muted-foreground">Get in touch with our team.</p>
        </div>
      </section>
    </div>
  )
} 