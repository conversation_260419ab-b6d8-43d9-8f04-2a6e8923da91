import React from 'react'
import Link from 'next/link' // Or your preferred routing library
import { Home, Bell, UserCircle, Settings, LogOut, User } from 'lucide-react' // Icon imports

import { Button } from '@/components/ui/button' // Adjust path if needed
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu' // Adjust path if needed
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar" // Adjust path if needed

export function DashboardHeader() {
  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-16 items-center px-4 md:px-6">
        {/* Left Side: Home Icon & App Name (Optional) */}
        <div className="mr-auto flex items-center gap-2">
          <Link href="/" aria-label="Home">
            <Button variant="ghost" size="icon">
              <Home className="h-5 w-5" />
            </Button>
          </Link>
          {/* Optional: Add your App Name here */}
          {/* <Link href="/" className="text-lg font-semibold">
            MyApp
          </Link> */}
        </div>

        {/* Right Side: Icons */}
        <div className="flex items-center space-x-2 md:space-x-4">
          {/* Notification Icon */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" aria-label="Notifications">
                <Bell className="h-5 w-5" />
                {/* Optional: Add a badge for notification count here if needed */}
                {/* <span className="absolute top-1 right-1 flex h-2 w-2">
                  <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-sky-400 opacity-75"></span>
                  <span className="relative inline-flex rounded-full h-2 w-2 bg-sky-500"></span>
                </span> */}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-80">
              <DropdownMenuLabel>Notifications</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem className="flex flex-col items-start p-3">
                <p className="text-sm font-medium">New feature released!</p>
                <p className="text-xs text-muted-foreground">
                  Check out the latest updates to our platform.
                </p>
              </DropdownMenuItem>
              <DropdownMenuItem className="flex flex-col items-start p-3">
                <p className="text-sm font-medium">System Maintenance</p>
                <p className="text-xs text-muted-foreground">
                  Scheduled for tomorrow at 2 AM.
                </p>
              </DropdownMenuItem>
               <DropdownMenuSeparator />
              <DropdownMenuItem className="text-center text-sm text-muted-foreground hover:underline">
                View all notifications
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* User Menu Icon */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="relative h-9 w-9 rounded-full">
                <Avatar className="h-8 w-8">
                  {/* Replace with actual user image or keep fallback */}
                  <AvatarImage src="https://placehold.co/40x40?text=U" alt="User avatar" />
                  <AvatarFallback>
                    <User className="h-5 w-5" />
                  </AvatarFallback>
                </Avatar>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuLabel>
                <div className="flex flex-col space-y-1">
                  <p className="text-sm font-medium leading-none">User Name</p>
                  <p className="text-xs leading-none text-muted-foreground">
                    <EMAIL>
                  </p>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <User className="mr-2 h-4 w-4" />
                <span>Profile</span>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Settings className="mr-2 h-4 w-4" />
                <span>Settings</span>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <LogOut className="mr-2 h-4 w-4" />
                <span>Log out</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  );
} 