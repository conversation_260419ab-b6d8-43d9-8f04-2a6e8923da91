/*! For license information please see components-ui-dialog-stories.7c26fcc6.iframe.bundle.js.LICENSE.txt */
(self.webpackChunkclient=self.webpackChunkclient||[]).push([[0],{"./node_modules/@radix-ui/react-compose-refs/dist/index.js":(module,__unused_webpack_exports,__webpack_require__)=>{"use strict";var mod,__create=Object.create,__defProp=Object.defineProperty,__getOwnPropDesc=Object.getOwnPropertyDescriptor,__getOwnPropNames=Object.getOwnPropertyNames,__getProtoOf=Object.getPrototypeOf,__hasOwnProp=Object.prototype.hasOwnProperty,__copyProps=(to,from,except,desc)=>{if(from&&"object"==typeof from||"function"==typeof from)for(let key of __getOwnPropNames(from))__hasOwnProp.call(to,key)||key===except||__defProp(to,key,{get:()=>from[key],enumerable:!(desc=__getOwnPropDesc(from,key))||desc.enumerable});return to},index_exports={};((target,all)=>{for(var name in all)__defProp(target,name,{get:all[name],enumerable:!0})})(index_exports,{composeRefs:()=>composeRefs,useComposedRefs:()=>useComposedRefs}),module.exports=(mod=index_exports,__copyProps(__defProp({},"__esModule",{value:!0}),mod));var React=((mod,isNodeMode,target)=>(target=null!=mod?__create(__getProtoOf(mod)):{},__copyProps(!isNodeMode&&mod&&mod.__esModule?target:__defProp(target,"default",{value:mod,enumerable:!0}),mod)))(__webpack_require__("./node_modules/react/index.js"));function setRef(ref,value){if("function"==typeof ref)return ref(value);null!=ref&&(ref.current=value)}function composeRefs(...refs){return node=>{let hasCleanup=!1;const cleanups=refs.map((ref=>{const cleanup=setRef(ref,node);return hasCleanup||"function"!=typeof cleanup||(hasCleanup=!0),cleanup}));if(hasCleanup)return()=>{for(let i=0;i<cleanups.length;i++){const cleanup=cleanups[i];"function"==typeof cleanup?cleanup():setRef(refs[i],null)}}}}function useComposedRefs(...refs){return React.useCallback(composeRefs(...refs),refs)}},"./node_modules/@radix-ui/react-slot/dist/index.js":(module,__unused_webpack_exports,__webpack_require__)=>{"use strict";var mod,__create=Object.create,__defProp=Object.defineProperty,__getOwnPropDesc=Object.getOwnPropertyDescriptor,__getOwnPropNames=Object.getOwnPropertyNames,__getProtoOf=Object.getPrototypeOf,__hasOwnProp=Object.prototype.hasOwnProperty,__copyProps=(to,from,except,desc)=>{if(from&&"object"==typeof from||"function"==typeof from)for(let key of __getOwnPropNames(from))__hasOwnProp.call(to,key)||key===except||__defProp(to,key,{get:()=>from[key],enumerable:!(desc=__getOwnPropDesc(from,key))||desc.enumerable});return to},index_exports={};((target,all)=>{for(var name in all)__defProp(target,name,{get:all[name],enumerable:!0})})(index_exports,{Root:()=>Slot,Slot:()=>Slot,Slottable:()=>Slottable,createSlot:()=>createSlot,createSlottable:()=>createSlottable}),module.exports=(mod=index_exports,__copyProps(__defProp({},"__esModule",{value:!0}),mod));var React=((mod,isNodeMode,target)=>(target=null!=mod?__create(__getProtoOf(mod)):{},__copyProps(!isNodeMode&&mod&&mod.__esModule?target:__defProp(target,"default",{value:mod,enumerable:!0}),mod)))(__webpack_require__("./node_modules/react/index.js")),import_react_compose_refs=__webpack_require__("./node_modules/@radix-ui/react-compose-refs/dist/index.js"),import_jsx_runtime=__webpack_require__("./node_modules/react/jsx-runtime.js");function createSlot(ownerName){const SlotClone=createSlotClone(ownerName),Slot2=React.forwardRef(((props,forwardedRef)=>{const{children,...slotProps}=props,childrenArray=React.Children.toArray(children),slottable=childrenArray.find(isSlottable);if(slottable){const newElement=slottable.props.children,newChildren=childrenArray.map((child=>child===slottable?React.Children.count(newElement)>1?React.Children.only(null):React.isValidElement(newElement)?newElement.props.children:null:child));return(0,import_jsx_runtime.jsx)(SlotClone,{...slotProps,ref:forwardedRef,children:React.isValidElement(newElement)?React.cloneElement(newElement,void 0,newChildren):null})}return(0,import_jsx_runtime.jsx)(SlotClone,{...slotProps,ref:forwardedRef,children})}));return Slot2.displayName=`${ownerName}.Slot`,Slot2}var Slot=createSlot("Slot");function createSlotClone(ownerName){const SlotClone=React.forwardRef(((props,forwardedRef)=>{const{children,...slotProps}=props,childrenRef=React.isValidElement(children)?function getElementRef(element){let getter=Object.getOwnPropertyDescriptor(element.props,"ref")?.get,mayWarn=getter&&"isReactWarning"in getter&&getter.isReactWarning;if(mayWarn)return element.ref;if(getter=Object.getOwnPropertyDescriptor(element,"ref")?.get,mayWarn=getter&&"isReactWarning"in getter&&getter.isReactWarning,mayWarn)return element.props.ref;return element.props.ref||element.ref}(children):void 0,ref=(0,import_react_compose_refs.useComposedRefs)(childrenRef,forwardedRef);if(React.isValidElement(children)){const props2=function mergeProps(slotProps,childProps){const overrideProps={...childProps};for(const propName in childProps){const slotPropValue=slotProps[propName],childPropValue=childProps[propName];/^on[A-Z]/.test(propName)?slotPropValue&&childPropValue?overrideProps[propName]=(...args)=>{const result=childPropValue(...args);return slotPropValue(...args),result}:slotPropValue&&(overrideProps[propName]=slotPropValue):"style"===propName?overrideProps[propName]={...slotPropValue,...childPropValue}:"className"===propName&&(overrideProps[propName]=[slotPropValue,childPropValue].filter(Boolean).join(" "))}return{...slotProps,...overrideProps}}(slotProps,children.props);return children.type!==React.Fragment&&(props2.ref=ref),React.cloneElement(children,props2)}return React.Children.count(children)>1?React.Children.only(null):null}));return SlotClone.displayName=`${ownerName}.SlotClone`,SlotClone}var SLOTTABLE_IDENTIFIER=Symbol("radix.slottable");function createSlottable(ownerName){const Slottable2=({children})=>(0,import_jsx_runtime.jsx)(import_jsx_runtime.Fragment,{children});return Slottable2.displayName=`${ownerName}.Slottable`,Slottable2.__radixId=SLOTTABLE_IDENTIFIER,Slottable2}var Slottable=createSlottable("Slottable");function isSlottable(child){return React.isValidElement(child)&&"function"==typeof child.type&&"__radixId"in child.type&&child.type.__radixId===SLOTTABLE_IDENTIFIER}},"./node_modules/class-variance-authority/dist/index.js":(__unused_webpack_module,exports,__webpack_require__)=>{"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),function _export(target,all){for(var name in all)Object.defineProperty(target,name,{enumerable:!0,get:all[name]})}(exports,{cva:function(){return cva},cx:function(){return cx}});const _clsx=__webpack_require__("./node_modules/clsx/dist/clsx.js"),falsyToString=value=>"boolean"==typeof value?`${value}`:0===value?"0":value,cx=_clsx.clsx,cva=(base,config)=>props=>{var _config_compoundVariants;if(null==(null==config?void 0:config.variants))return cx(base,null==props?void 0:props.class,null==props?void 0:props.className);const{variants,defaultVariants}=config,getVariantClassNames=Object.keys(variants).map((variant=>{const variantProp=null==props?void 0:props[variant],defaultVariantProp=null==defaultVariants?void 0:defaultVariants[variant];if(null===variantProp)return null;const variantKey=falsyToString(variantProp)||falsyToString(defaultVariantProp);return variants[variant][variantKey]})),propsWithoutUndefined=props&&Object.entries(props).reduce(((acc,param)=>{let[key,value]=param;return void 0===value||(acc[key]=value),acc}),{}),getCompoundVariantClassNames=null==config||null===(_config_compoundVariants=config.compoundVariants)||void 0===_config_compoundVariants?void 0:_config_compoundVariants.reduce(((acc,param)=>{let{class:cvClass,className:cvClassName,...compoundVariantOptions}=param;return Object.entries(compoundVariantOptions).every((param=>{let[key,value]=param;return Array.isArray(value)?value.includes({...defaultVariants,...propsWithoutUndefined}[key]):{...defaultVariants,...propsWithoutUndefined}[key]===value}))?[...acc,cvClass,cvClassName]:acc}),[]);return cx(base,getVariantClassNames,getCompoundVariantClassNames,null==props?void 0:props.class,null==props?void 0:props.className)}},"./node_modules/clsx/dist/clsx.js":module=>{function r(e){var o,t,f="";if("string"==typeof e||"number"==typeof e)f+=e;else if("object"==typeof e)if(Array.isArray(e)){var n=e.length;for(o=0;o<n;o++)e[o]&&(t=r(e[o]))&&(f&&(f+=" "),f+=t)}else for(t in e)e[t]&&(f&&(f+=" "),f+=t);return f}function e(){for(var e,o,t=0,f="",n=arguments.length;t<n;t++)(e=arguments[t])&&(o=r(e))&&(f&&(f+=" "),f+=o);return f}module.exports=e,module.exports.clsx=e},"./src/components/ui/button.js":function(__unused_webpack_module,exports,__webpack_require__){var __assign=this&&this.__assign||function(){return __assign=Object.assign||function(t){for(var s,i=1,n=arguments.length;i<n;i++)for(var p in s=arguments[i])Object.prototype.hasOwnProperty.call(s,p)&&(t[p]=s[p]);return t},__assign.apply(this,arguments)},__rest=this&&this.__rest||function(s,e){var t={};for(var p in s)Object.prototype.hasOwnProperty.call(s,p)&&e.indexOf(p)<0&&(t[p]=s[p]);if(null!=s&&"function"==typeof Object.getOwnPropertySymbols){var i=0;for(p=Object.getOwnPropertySymbols(s);i<p.length;i++)e.indexOf(p[i])<0&&Object.prototype.propertyIsEnumerable.call(s,p[i])&&(t[p[i]]=s[p[i]])}return t};exports.__esModule=!0,exports.buttonVariants=exports.Button=void 0;var React=__webpack_require__("./node_modules/react/index.js"),react_slot_1=__webpack_require__("./node_modules/@radix-ui/react-slot/dist/index.js"),class_variance_authority_1=__webpack_require__("./node_modules/class-variance-authority/dist/index.js"),utils_1=__webpack_require__("./src/lib/utils.js"),buttonVariants=(0,class_variance_authority_1.cva)("inline-flex items-center justify-center rounded-md text-sm font-bold tracking-wide ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-violet-primary focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 text-violet-primary dark:text-violet-light",{variants:{variant:{default:"bg-violet-primary text-white hover:bg-violet-dark dark:bg-violet-primary dark:hover:bg-violet-dark",destructive:"bg-status-error text-white hover:bg-red-600 dark:bg-status-error dark:hover:bg-red-700",outline:"border-2 border-violet-light bg-white dark:border-violet-dark dark:bg-slate-900 hover:bg-violet-primary hover:text-white dark:hover:bg-violet-primary dark:hover:text-white font-semibold",secondary:"bg-violet-secondary text-white hover:bg-violet-secondary/90 dark:bg-violet-secondary dark:hover:bg-violet-secondary/80",ghost:"hover:bg-violet-light/20 hover:text-violet-dark dark:hover:bg-violet-dark/20 dark:hover:text-violet-light",link:"text-violet-primary underline-offset-4 hover:underline dark:text-violet-light",accent:"bg-status-success text-white hover:bg-status-success/90 dark:bg-status-success dark:hover:bg-status-success/80"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}});exports.buttonVariants=buttonVariants;var Button=React.forwardRef((function(_a,ref){var className=_a.className,variant=_a.variant,size=_a.size,_b=_a.asChild,asChild=void 0!==_b&&_b,isLoading=_a.isLoading,props=__rest(_a,["className","variant","size","asChild","isLoading"]),Comp=asChild?react_slot_1.Slot:"button",isDisabled=props.disabled||isLoading,restProps=__rest(props,["disabled"]),_c=React.useState(!1),isHovered=_c[0],setIsHovered=_c[1],inlineStyle="outline"===variant&&isHovered?{backgroundColor:"#8B5CF6",color:"white"}:{};return React.createElement(Comp,__assign({className:(0,utils_1.cn)(buttonVariants({variant,size,className})),ref,disabled:isDisabled,style:inlineStyle,onMouseEnter:function(){setIsHovered(!0)},onMouseLeave:function(){setIsHovered(!1)}},restProps))}));exports.Button=Button,Button.displayName="Button",Button.__docgenInfo={description:"Button component - Used to trigger actions\r\n\r\nFollows WAI-ARIA button design pattern, supports keyboard navigation and screen readers",methods:[],displayName:"Button"}},"./src/components/ui/dialog.stories.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{"use strict";__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{Default:()=>Default,__namedExportsOrder:()=>__namedExportsOrder,default:()=>dialog_stories});var react=__webpack_require__("./node_modules/react/index.js"),ui_button=__webpack_require__("./src/components/ui/button.js"),dist=__webpack_require__("./node_modules/@radix-ui/primitive/dist/index.mjs"),react_compose_refs_dist=__webpack_require__("./node_modules/@radix-ui/react-compose-refs/dist/index.mjs"),react_context_dist=__webpack_require__("./node_modules/@radix-ui/react-context/dist/index.mjs"),react_id_dist=__webpack_require__("./node_modules/@radix-ui/react-id/dist/index.mjs"),react_use_controllable_state_dist=__webpack_require__("./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs"),react_dismissable_layer_dist=__webpack_require__("./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs"),react_focus_scope_dist=__webpack_require__("./node_modules/@radix-ui/react-focus-scope/dist/index.mjs"),react_portal_dist=__webpack_require__("./node_modules/@radix-ui/react-portal/dist/index.mjs"),react_use_layout_effect_dist=__webpack_require__("./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs");var Presence=props=>{const{present,children}=props,presence=function usePresence(present){const[node,setNode]=react.useState(),stylesRef=react.useRef(null),prevPresentRef=react.useRef(present),prevAnimationNameRef=react.useRef("none"),initialState=present?"mounted":"unmounted",[state,send]=function useStateMachine(initialState,machine){return react.useReducer(((state,event)=>machine[state][event]??state),initialState)}(initialState,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return react.useEffect((()=>{const currentAnimationName=getAnimationName(stylesRef.current);prevAnimationNameRef.current="mounted"===state?currentAnimationName:"none"}),[state]),(0,react_use_layout_effect_dist.N)((()=>{const styles=stylesRef.current,wasPresent=prevPresentRef.current;if(wasPresent!==present){const prevAnimationName=prevAnimationNameRef.current,currentAnimationName=getAnimationName(styles);if(present)send("MOUNT");else if("none"===currentAnimationName||"none"===styles?.display)send("UNMOUNT");else{send(wasPresent&&prevAnimationName!==currentAnimationName?"ANIMATION_OUT":"UNMOUNT")}prevPresentRef.current=present}}),[present,send]),(0,react_use_layout_effect_dist.N)((()=>{if(node){let timeoutId;const ownerWindow=node.ownerDocument.defaultView??window,handleAnimationEnd=event=>{const isCurrentAnimation=getAnimationName(stylesRef.current).includes(event.animationName);if(event.target===node&&isCurrentAnimation&&(send("ANIMATION_END"),!prevPresentRef.current)){const currentFillMode=node.style.animationFillMode;node.style.animationFillMode="forwards",timeoutId=ownerWindow.setTimeout((()=>{"forwards"===node.style.animationFillMode&&(node.style.animationFillMode=currentFillMode)}))}},handleAnimationStart=event=>{event.target===node&&(prevAnimationNameRef.current=getAnimationName(stylesRef.current))};return node.addEventListener("animationstart",handleAnimationStart),node.addEventListener("animationcancel",handleAnimationEnd),node.addEventListener("animationend",handleAnimationEnd),()=>{ownerWindow.clearTimeout(timeoutId),node.removeEventListener("animationstart",handleAnimationStart),node.removeEventListener("animationcancel",handleAnimationEnd),node.removeEventListener("animationend",handleAnimationEnd)}}send("ANIMATION_END")}),[node,send]),{isPresent:["mounted","unmountSuspended"].includes(state),ref:react.useCallback((node2=>{stylesRef.current=node2?getComputedStyle(node2):null,setNode(node2)}),[])}}(present),child="function"==typeof children?children({present:presence.isPresent}):react.Children.only(children),ref=(0,react_compose_refs_dist.s)(presence.ref,function getElementRef(element){let getter=Object.getOwnPropertyDescriptor(element.props,"ref")?.get,mayWarn=getter&&"isReactWarning"in getter&&getter.isReactWarning;if(mayWarn)return element.ref;if(getter=Object.getOwnPropertyDescriptor(element,"ref")?.get,mayWarn=getter&&"isReactWarning"in getter&&getter.isReactWarning,mayWarn)return element.props.ref;return element.props.ref||element.ref}(child));return"function"==typeof children||presence.isPresent?react.cloneElement(child,{ref}):null};function getAnimationName(styles){return styles?.animationName||"none"}Presence.displayName="Presence";var react_primitive_dist=__webpack_require__("./node_modules/@radix-ui/react-primitive/dist/index.mjs"),react_focus_guards_dist=__webpack_require__("./node_modules/@radix-ui/react-focus-guards/dist/index.mjs"),Combination=__webpack_require__("./node_modules/react-remove-scroll/dist/es2015/Combination.js"),es2015=__webpack_require__("./node_modules/aria-hidden/dist/es2015/index.js"),react_slot_dist=__webpack_require__("./node_modules/@radix-ui/react-slot/dist/index.mjs"),jsx_runtime=__webpack_require__("./node_modules/react/jsx-runtime.js"),[createDialogContext,createDialogScope]=(0,react_context_dist.A)("Dialog"),[DialogProvider,useDialogContext]=createDialogContext("Dialog"),Dialog=props=>{const{__scopeDialog,children,open:openProp,defaultOpen,onOpenChange,modal=!0}=props,triggerRef=react.useRef(null),contentRef=react.useRef(null),[open,setOpen]=(0,react_use_controllable_state_dist.i)({prop:openProp,defaultProp:defaultOpen??!1,onChange:onOpenChange,caller:"Dialog"});return(0,jsx_runtime.jsx)(DialogProvider,{scope:__scopeDialog,triggerRef,contentRef,contentId:(0,react_id_dist.B)(),titleId:(0,react_id_dist.B)(),descriptionId:(0,react_id_dist.B)(),open,onOpenChange:setOpen,onOpenToggle:react.useCallback((()=>setOpen((prevOpen=>!prevOpen))),[setOpen]),modal,children})};Dialog.displayName="Dialog";var DialogTrigger=react.forwardRef(((props,forwardedRef)=>{const{__scopeDialog,...triggerProps}=props,context=useDialogContext("DialogTrigger",__scopeDialog),composedTriggerRef=(0,react_compose_refs_dist.s)(forwardedRef,context.triggerRef);return(0,jsx_runtime.jsx)(react_primitive_dist.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":context.open,"aria-controls":context.contentId,"data-state":getState(context.open),...triggerProps,ref:composedTriggerRef,onClick:(0,dist.m)(props.onClick,context.onOpenToggle)})}));DialogTrigger.displayName="DialogTrigger";var[PortalProvider,usePortalContext]=createDialogContext("DialogPortal",{forceMount:void 0}),DialogPortal=props=>{const{__scopeDialog,forceMount,children,container}=props,context=useDialogContext("DialogPortal",__scopeDialog);return(0,jsx_runtime.jsx)(PortalProvider,{scope:__scopeDialog,forceMount,children:react.Children.map(children,(child=>(0,jsx_runtime.jsx)(Presence,{present:forceMount||context.open,children:(0,jsx_runtime.jsx)(react_portal_dist.Z,{asChild:!0,container,children:child})})))})};DialogPortal.displayName="DialogPortal";var DialogOverlay=react.forwardRef(((props,forwardedRef)=>{const portalContext=usePortalContext("DialogOverlay",props.__scopeDialog),{forceMount=portalContext.forceMount,...overlayProps}=props,context=useDialogContext("DialogOverlay",props.__scopeDialog);return context.modal?(0,jsx_runtime.jsx)(Presence,{present:forceMount||context.open,children:(0,jsx_runtime.jsx)(DialogOverlayImpl,{...overlayProps,ref:forwardedRef})}):null}));DialogOverlay.displayName="DialogOverlay";var Slot=(0,react_slot_dist.TL)("DialogOverlay.RemoveScroll"),DialogOverlayImpl=react.forwardRef(((props,forwardedRef)=>{const{__scopeDialog,...overlayProps}=props,context=useDialogContext("DialogOverlay",__scopeDialog);return(0,jsx_runtime.jsx)(Combination.A,{as:Slot,allowPinchZoom:!0,shards:[context.contentRef],children:(0,jsx_runtime.jsx)(react_primitive_dist.sG.div,{"data-state":getState(context.open),...overlayProps,ref:forwardedRef,style:{pointerEvents:"auto",...overlayProps.style}})})})),DialogContent=react.forwardRef(((props,forwardedRef)=>{const portalContext=usePortalContext("DialogContent",props.__scopeDialog),{forceMount=portalContext.forceMount,...contentProps}=props,context=useDialogContext("DialogContent",props.__scopeDialog);return(0,jsx_runtime.jsx)(Presence,{present:forceMount||context.open,children:context.modal?(0,jsx_runtime.jsx)(DialogContentModal,{...contentProps,ref:forwardedRef}):(0,jsx_runtime.jsx)(DialogContentNonModal,{...contentProps,ref:forwardedRef})})}));DialogContent.displayName="DialogContent";var DialogContentModal=react.forwardRef(((props,forwardedRef)=>{const context=useDialogContext("DialogContent",props.__scopeDialog),contentRef=react.useRef(null),composedRefs=(0,react_compose_refs_dist.s)(forwardedRef,context.contentRef,contentRef);return react.useEffect((()=>{const content=contentRef.current;if(content)return(0,es2015.Eq)(content)}),[]),(0,jsx_runtime.jsx)(DialogContentImpl,{...props,ref:composedRefs,trapFocus:context.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,dist.m)(props.onCloseAutoFocus,(event=>{event.preventDefault(),context.triggerRef.current?.focus()})),onPointerDownOutside:(0,dist.m)(props.onPointerDownOutside,(event=>{const originalEvent=event.detail.originalEvent,ctrlLeftClick=0===originalEvent.button&&!0===originalEvent.ctrlKey;(2===originalEvent.button||ctrlLeftClick)&&event.preventDefault()})),onFocusOutside:(0,dist.m)(props.onFocusOutside,(event=>event.preventDefault()))})})),DialogContentNonModal=react.forwardRef(((props,forwardedRef)=>{const context=useDialogContext("DialogContent",props.__scopeDialog),hasInteractedOutsideRef=react.useRef(!1),hasPointerDownOutsideRef=react.useRef(!1);return(0,jsx_runtime.jsx)(DialogContentImpl,{...props,ref:forwardedRef,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:event=>{props.onCloseAutoFocus?.(event),event.defaultPrevented||(hasInteractedOutsideRef.current||context.triggerRef.current?.focus(),event.preventDefault()),hasInteractedOutsideRef.current=!1,hasPointerDownOutsideRef.current=!1},onInteractOutside:event=>{props.onInteractOutside?.(event),event.defaultPrevented||(hasInteractedOutsideRef.current=!0,"pointerdown"===event.detail.originalEvent.type&&(hasPointerDownOutsideRef.current=!0));const target=event.target,targetIsTrigger=context.triggerRef.current?.contains(target);targetIsTrigger&&event.preventDefault(),"focusin"===event.detail.originalEvent.type&&hasPointerDownOutsideRef.current&&event.preventDefault()}})})),DialogContentImpl=react.forwardRef(((props,forwardedRef)=>{const{__scopeDialog,trapFocus,onOpenAutoFocus,onCloseAutoFocus,...contentProps}=props,context=useDialogContext("DialogContent",__scopeDialog),contentRef=react.useRef(null),composedRefs=(0,react_compose_refs_dist.s)(forwardedRef,contentRef);return(0,react_focus_guards_dist.Oh)(),(0,jsx_runtime.jsxs)(jsx_runtime.Fragment,{children:[(0,jsx_runtime.jsx)(react_focus_scope_dist.n,{asChild:!0,loop:!0,trapped:trapFocus,onMountAutoFocus:onOpenAutoFocus,onUnmountAutoFocus:onCloseAutoFocus,children:(0,jsx_runtime.jsx)(react_dismissable_layer_dist.qW,{role:"dialog",id:context.contentId,"aria-describedby":context.descriptionId,"aria-labelledby":context.titleId,"data-state":getState(context.open),...contentProps,ref:composedRefs,onDismiss:()=>context.onOpenChange(!1)})}),(0,jsx_runtime.jsxs)(jsx_runtime.Fragment,{children:[(0,jsx_runtime.jsx)(TitleWarning,{titleId:context.titleId}),(0,jsx_runtime.jsx)(DescriptionWarning,{contentRef,descriptionId:context.descriptionId})]})]})})),DialogTitle=react.forwardRef(((props,forwardedRef)=>{const{__scopeDialog,...titleProps}=props,context=useDialogContext("DialogTitle",__scopeDialog);return(0,jsx_runtime.jsx)(react_primitive_dist.sG.h2,{id:context.titleId,...titleProps,ref:forwardedRef})}));DialogTitle.displayName="DialogTitle";var DialogDescription=react.forwardRef(((props,forwardedRef)=>{const{__scopeDialog,...descriptionProps}=props,context=useDialogContext("DialogDescription",__scopeDialog);return(0,jsx_runtime.jsx)(react_primitive_dist.sG.p,{id:context.descriptionId,...descriptionProps,ref:forwardedRef})}));DialogDescription.displayName="DialogDescription";var DialogClose=react.forwardRef(((props,forwardedRef)=>{const{__scopeDialog,...closeProps}=props,context=useDialogContext("DialogClose",__scopeDialog);return(0,jsx_runtime.jsx)(react_primitive_dist.sG.button,{type:"button",...closeProps,ref:forwardedRef,onClick:(0,dist.m)(props.onClick,(()=>context.onOpenChange(!1)))})}));function getState(open){return open?"open":"closed"}DialogClose.displayName="DialogClose";var[WarningProvider,useWarningContext]=(0,react_context_dist.q)("DialogTitleWarning",{contentName:"DialogContent",titleName:"DialogTitle",docsSlug:"dialog"}),TitleWarning=({titleId})=>{const titleWarningContext=useWarningContext("DialogTitleWarning"),MESSAGE=`\`${titleWarningContext.contentName}\` requires a \`${titleWarningContext.titleName}\` for the component to be accessible for screen reader users.\n\nIf you want to hide the \`${titleWarningContext.titleName}\`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/${titleWarningContext.docsSlug}`;return react.useEffect((()=>{if(titleId){document.getElementById(titleId)||console.error(MESSAGE)}}),[MESSAGE,titleId]),null},DescriptionWarning=({contentRef,descriptionId})=>{const MESSAGE=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${useWarningContext("DialogDescriptionWarning").contentName}}.`;return react.useEffect((()=>{const describedById=contentRef.current?.getAttribute("aria-describedby");if(descriptionId&&describedById){document.getElementById(descriptionId)||console.warn(MESSAGE)}}),[MESSAGE,contentRef,descriptionId]),null},dist_Root=Dialog,Trigger=DialogTrigger,Portal=DialogPortal,Overlay=DialogOverlay,Content=DialogContent,Title=DialogTitle,Description=DialogDescription,Close=DialogClose;const X=(0,__webpack_require__("./node_modules/lucide-react/dist/esm/createLucideIcon.js").A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);var utils=__webpack_require__("./src/lib/utils.js"),visually_hidden=__webpack_require__("./src/components/ui/visually-hidden.tsx");const dialog_Dialog=dist_Root,dialog_DialogTrigger=Trigger,dialog_DialogPortal=Portal,dialog_DialogClose=Close,dialog_DialogOverlay=react.forwardRef(((_ref,ref)=>{let{className,...props}=_ref;return(0,jsx_runtime.jsx)(Overlay,{ref,className:(0,utils.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",className),...props})}));dialog_DialogOverlay.displayName=Overlay.displayName;const dialog_DialogContent=react.forwardRef(((_ref2,ref)=>{let{className,children,description,closeButtonLabel="关闭",...props}=_ref2;return(0,jsx_runtime.jsxs)(dialog_DialogPortal,{children:[(0,jsx_runtime.jsx)(dialog_DialogOverlay,{}),(0,jsx_runtime.jsxs)(Content,{ref,className:(0,utils.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",className),role:"dialog","aria-modal":"true","aria-describedby":description?"dialog-description":void 0,...props,children:[children,description&&(0,jsx_runtime.jsx)(visually_hidden.s,{id:"dialog-description",children:description}),(0,jsx_runtime.jsxs)(Close,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground","aria-label":closeButtonLabel,children:[(0,jsx_runtime.jsx)(X,{className:"h-4 w-4","aria-hidden":"true"}),(0,jsx_runtime.jsx)(visually_hidden.s,{children:closeButtonLabel})]})]})]})}));dialog_DialogContent.displayName=Content.displayName;const DialogHeader=_ref3=>{let{className,...props}=_ref3;return(0,jsx_runtime.jsx)("div",{className:(0,utils.cn)("flex flex-col space-y-1.5 text-center sm:text-left",className),...props})};DialogHeader.displayName="DialogHeader";const DialogFooter=_ref4=>{let{className,...props}=_ref4;return(0,jsx_runtime.jsx)("div",{className:(0,utils.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",className),...props})};DialogFooter.displayName="DialogFooter";const dialog_DialogTitle=react.forwardRef(((_ref5,ref)=>{let{className,...props}=_ref5;return(0,jsx_runtime.jsx)(Title,{ref,className:(0,utils.cn)("text-lg font-semibold leading-none tracking-tight",className),id:"dialog-title",...props})}));dialog_DialogTitle.displayName=Title.displayName;const dialog_DialogDescription=react.forwardRef(((_ref6,ref)=>{let{className,...props}=_ref6;return(0,jsx_runtime.jsx)(Description,{ref,className:(0,utils.cn)("text-sm text-muted-foreground",className),...props})}));dialog_DialogDescription.displayName=Description.displayName,dialog_DialogOverlay.__docgenInfo={description:"对话框覆盖层 - 提供背景遮罩",methods:[]},dialog_DialogContent.__docgenInfo={description:"对话框内容 - 符合WAI-ARIA对话框模式",methods:[],props:{description:{required:!1,tsType:{name:"string"},description:"对话框的描述性文本，用于辅助技术"},closeButtonLabel:{required:!1,tsType:{name:"string"},description:"自定义关闭按钮的标签",defaultValue:{value:'"关闭"',computed:!1}}}},DialogHeader.__docgenInfo={description:"对话框头部 - 包含标题和描述",methods:[],displayName:"DialogHeader"},DialogFooter.__docgenInfo={description:"对话框底部 - 通常包含操作按钮",methods:[],displayName:"DialogFooter"},dialog_DialogTitle.__docgenInfo={description:"对话框标题 - 用于提供对话框的主要标识",methods:[]},dialog_DialogDescription.__docgenInfo={description:"对话框描述 - 提供对话框的附加上下文",methods:[]};const dialog_stories={title:"UI/Dialog",component:dialog_Dialog,tags:["autodocs"]},Default={render:()=>(0,jsx_runtime.jsxs)(dialog_Dialog,{children:[(0,jsx_runtime.jsx)(dialog_DialogTrigger,{asChild:!0,children:(0,jsx_runtime.jsx)(ui_button.Button,{children:"Open Dialog"})}),(0,jsx_runtime.jsxs)(dialog_DialogContent,{description:"This is a simple dialog description",children:[(0,jsx_runtime.jsx)(DialogHeader,{children:(0,jsx_runtime.jsx)(dialog_DialogTitle,{children:"Dialog Title"})}),(0,jsx_runtime.jsx)("div",{children:"This is the content inside the dialog."}),(0,jsx_runtime.jsx)(DialogFooter,{children:(0,jsx_runtime.jsx)(dialog_DialogClose,{asChild:!0,children:(0,jsx_runtime.jsx)(ui_button.Button,{variant:"secondary",children:"Close"})})})]})]})},__namedExportsOrder=["Default"];Default.parameters={...Default.parameters,docs:{...Default.parameters?.docs,source:{originalSource:'{\n  render: () => <Dialog>\r\n      <DialogTrigger asChild>\r\n        <Button>Open Dialog</Button>\r\n      </DialogTrigger>\r\n      <DialogContent description="This is a simple dialog description">\r\n        <DialogHeader>\r\n          <DialogTitle>Dialog Title</DialogTitle>\r\n        </DialogHeader>\r\n        <div>\r\n          This is the content inside the dialog.\r\n        </div>\r\n        <DialogFooter>\r\n          <DialogClose asChild>\r\n            <Button variant="secondary">Close</Button>\r\n          </DialogClose>\r\n        </DialogFooter>\r\n      </DialogContent>\r\n    </Dialog>\n}',...Default.parameters?.docs?.source}}}},"./src/components/ui/visually-hidden.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{"use strict";__webpack_require__.d(__webpack_exports__,{s:()=>VisuallyHidden});var react__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("./node_modules/react/index.js"),_lib_utils__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__("./src/lib/utils.js"),react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__=__webpack_require__("./node_modules/react/jsx-runtime.js");const VisuallyHidden=react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(((_ref,ref)=>{let{className,...props}=_ref;return(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)("span",{ref,className:(0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)("sr-only",className),...props})}));VisuallyHidden.displayName="VisuallyHidden",VisuallyHidden.__docgenInfo={description:"VisuallyHidden组件 - 在视觉上隐藏内容，但保持其对屏幕阅读器的可访问性\r\n用于提供额外的可访问性上下文，而不影响视觉设计",methods:[],displayName:"VisuallyHidden"}},"./src/lib/utils.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{"use strict";__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{absoluteUrl:()=>absoluteUrl,cn:()=>cn,formatPrice:()=>formatPrice});var clsx__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__("./node_modules/clsx/dist/clsx.mjs"),tailwind_merge__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("./node_modules/tailwind-merge/dist/bundle-mjs.mjs"),process=__webpack_require__("./node_modules/process/browser.js");function cn(){for(var _len=arguments.length,inputs=new Array(_len),_key=0;_key<_len;_key++)inputs[_key]=arguments[_key];return(0,tailwind_merge__WEBPACK_IMPORTED_MODULE_0__.QP)((0,clsx__WEBPACK_IMPORTED_MODULE_1__.$)(inputs))}function formatPrice(price){return new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(price)}function absoluteUrl(path){return`${process.env.NEXT_PUBLIC_APP_URL||""}${path}`}}}]);