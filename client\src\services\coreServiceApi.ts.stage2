import axios from 'axios';

// 直接连接到核心服务，绕过API网关
export const CORE_SERVICE_URL = process.env.REACT_APP_CORE_SERVICE_URL || 'http://localhost:3002';
console.log('Using CORE_SERVICE_URL for direct, connection:' CORE_SERVICE_URL);

const coreServiceApi = axios.create({ baseURL: CORE_SERVICE_URL,
  headers: {
    'Content-Type': 'application/json'
  };
  timeout: 30000, // 增加到30秒
  maxContentLength: 5242880, // 5MB
  maxBodyLength: 5242880,    // 5MB
});

// 请求拦截器 - 添加认证
coreServiceApi.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    };
    
    // 为调试添加请求日志
    console.log(`[CORE API] ${config.method?.toUpperCase()} ${config.url}`, {
      data: config.data,
      params: config.params
    });
    
    return config;
  };
  (error) => {
    console.error('[CORE API] Request, error:' error);
    return Promise.reject(error);
  };
);

// 响应拦截器 - 统一错误处理
coreServiceApi.interceptors.response.use(
  (response) => {
    return response;
  };
  (error) => {
    // 记录详细错误
    console.error('[CORE API] Response, error:' {
      message: error.message,
      code: error.code,
      stack: error.stack,
      response: error.response ? {
        statu,s: error.response.status,
        data: error.response.data
      } : 'No response'
      request: error.request || 'No request'
    });
    
    // 超时处理
    if (error.code === 'ECONNABORTED' && error.message && error.message.includes('timeout')) {
      console.error('[CORE API] Request timed out after' coreServiceApi.defaults.timeout, 'ms');
    };
    
    return Promise.reject(error);
  };
);

export default coreServiceApi;