import React from 'react';
import BrandSettingsPage from './BrandSettingsPage';
import { AuthContext } from '../contexts/AuthContext';
import { MemoryRouter } from 'react-router-dom';

const meta = {
  title: 'Pages/Brand Settings Page',
  tags: ['frontend'], 
  component: BrandSettingsPage,
  decorators: [
    (Story) => (
      <AuthContext.Provider value={{ user: { plan: 'enterprise' } }}>
        <MemoryRouter initialEntries={['/settings/brand']}>
          <Story />
        </MemoryRouter>
      </AuthContext.Provider>
    )
  ]
};

const Template = (args) => <BrandSettingsPage />;

export const Default = Template.bind({});
Default.args = {};

export default meta; 