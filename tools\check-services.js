#!/usr/bin/env node

const http = require('http');

console.log('╔════════════════════════════════════════════╗');
console.log('║       iTeraBiz Services Status Check       ║');
console.log('╚════════════════════════════════════════════╝');

const services = [
  {
    name: 'API Gateway',
    port: 3001,
    path: '/health'
  },
  {
    name: 'Core Service',
    port: 3002,
    path: '/health'
  },
  {
    name: 'AI Service',
    port: 3003,
    path: '/health'
  },
  {
    name: 'React Client',
    port: 3000,
    path: '/'
  }
];

function checkService(service) {
  return new Promise((resolve) => {
    const options = {
      hostname: 'localhost',
      port: service.port,
      path: service.path,
      method: 'GET',
      timeout: 3000
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        resolve({
          ...service,
          status: res.statusCode === 200 ? '✅ 运行中' : `❌ 错误 (${res.statusCode})`,
          response: data.substring(0, 100)
        });
      });
    });

    req.on('error', (err) => {
      resolve({
        ...service,
        status: '❌ 不可用',
        error: err.message
      });
    });

    req.on('timeout', () => {
      resolve({
        ...service,
        status: '⏰ 超时',
        error: 'Request timeout'
      });
    });

    req.end();
  });
}

async function checkAllServices() {
  console.log('\\n检查服务状态...');
  
  for (const service of services) {
    const result = await checkService(service);
    console.log(`\\n[${result.name}] ${result.status}`);
    console.log(`   端口: ${result.port}`);
    console.log(`   路径: ${result.path}`);
    
    if (result.error) {
      console.log(`   错误: ${result.error}`);
    } else if (result.response) {
      console.log(`   响应: ${result.response}${result.response.length >= 100 ? '...' : ''}`);
    }
  }
  
  console.log('\\n检查完成！');
}

checkAllServices().catch(console.error); 