import api from './axiosInstance';

// 创建专门用于聊天的API客户端，不包含授权头
// 因为AI服务后端有特殊处理允许匿名聊天请求通过
const chatApi = api.create({
  baseURL: process.env.REACT_APP_API_BASE_URL || 'http://localhost:3001/api',
  timeout: 60000, // 增加到60秒以匹配服务器端配置
  headers: {
    'Content-Type': 'application/json'
  }
});

// 测试函数，用于验证与AI服务的连接
export const testAiService = async () => {
  try {
    // 尝试访问健康检查端点
    const response = await chatApi.get('/ai/health');
    console.log('AI服务连接测试成功:', response.data);
    return {
      success: true,
      data: response.data
    };
  } catch (error) {
    console.error('AI服务连接测试失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// 直接导出，不添加授权拦截器
export default chatApi;

export const sendMessage = async (message, sessionId) => {
  try {
    const response = await chatApi.post('/api/chat/send', {
      message,
      sessionId
    });
    return response.data;
  } catch (error) {
    console.error('Error sending message:', error);
    throw error;
  }
};

export const startSession = async () => {
  try {
    const response = await chatApi.post('/api/chat/start');
    return response.data;
  } catch (error) {
    console.error('Error starting chat session:', error);
    throw error;
  }
};

export const endSession = async (sessionId) => {
  try {
    const response = await chatApi.post(`/api/chat/end/${sessionId}`);
    return response.data;
  } catch (error) {
    console.error('Error ending chat session:', error);
    throw error;
  }
};
