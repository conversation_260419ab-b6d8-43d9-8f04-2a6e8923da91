/**
 * 统一类型定义入口
 * 使用命名空间导出避免命名冲突，提供清晰的类型组织结构
 */

// 基础通用类型
export * from './common';

// 设计系统类型 - 优先导出
export * from './design-system';
export * from './theme';
export * from './component';

// 业务模块类型 - 使用命名空间避免冲突
export * as Analytics from './analytics';
export * as Agent from './agent';
export * as Booking from './booking';
export * as Platform from './platform';
export * as Dashboard from './dashboard';
export * as DataInsight from './dataInsight';

// 设置相关类型
export * as Settings from './settingsCommon';
export * as OnsiteSettings from './onsiteSettings';
export * as WalkinSettings from './walkinSettings';

// 功能模块类型
export * as FlowBuilder from './flowBuilder';
export * as IntelligentOptimization from './intelligentOptimization';
export * as CrmIntegration from './crmIntegration';
export * as AutoReply from './autoReplyTypes';

// 权限和员工类型
export * as Staff from './staffTypes';
export * as Module from './module';

// 重新导出最常用的基础类型，提供便捷访问
export type {
  // 基础类型
  GeoLocation,
  TimeRange, 
  DateRange,
  PaginationParams,
  SortParams,
  FilterParams
} from './common';

// 重新导出设计系统核心类型
export type {
  DesignTokens,
  ThemeMode,
  ColorScale
} from './design-system';

export type {
  ThemeProviderProps,
  UseThemeReturn,
  ThemeContextValue
} from './theme';

export type {
  BaseComponentProps,
  ButtonProps,
  InputProps,
  CardProps,
  ModalProps,
  ComponentSize,
  ComponentVariant,
  ColorScheme
} from './component';

// 类型工具函数
export type Optional<T, K extends keyof T> = Pick<Partial<T>, K> & Omit<T, K>;
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

// 重新定义NonNullable以排除null和undefined
export type NonNullable<T> = T extends null | undefined ? never : T;

// 联合类型助手
export type UnionToIntersection<U> = (U extends any ? (k: U) => void : never) extends (k: infer I) => void ? I : never;
export type Prettify<T> = { [K in keyof T]: T[K] } & {};

// 条件类型助手
export type If<C extends boolean, T, F> = C extends true ? T : F;
export type IsEqual<T, U> = T extends U ? (U extends T ? true : false) : false; 