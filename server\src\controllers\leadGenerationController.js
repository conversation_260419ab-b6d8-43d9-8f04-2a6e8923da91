const { Op, Sequelize } = require('sequelize');
const { 
  Lead, 
  LeadActivity, 
  LeadScore, 
  LeadFormSubmission,
  LeadTag,
  LeadTagAssignment,
  ConversionFunnel,
  LeadConversion 
} = require('../models/leadGeneration');

// 线索评分算法
class LeadScoringEngine {
  constructor(config) {
    this.config = config;
  }

  calculateScore(lead, activities = []) {
    const weights = this.config.scoringRules || {};
    
    // 行为评分
    const behavioralScore = this.calculateBehavioralScore(activities) * 
      (weights.behavioralWeight || 40) / 100;
    
    // 人口统计评分
    const demographicScore = this.calculateDemographicScore(lead) * 
      (weights.demographicWeight || 30) / 100;
    
    // 互动评分
    const interactionScore = this.calculateInteractionScore(activities) * 
      (weights.interactionWeight || 20) / 100;
    
    // 来源评分
    const sourceScore = this.calculateSourceScore(lead) * 
      (weights.sourceWeight || 10) / 100;
    
    const totalScore = Math.min(100, Math.max(0, 
      behavioralScore + demographicScore + interactionScore + sourceScore
    ));

    return {
      totalScore: Math.round(totalScore),
      breakdown: {
        behavioral: Math.round(behavioralScore),
        demographic: Math.round(demographicScore),
        interaction: Math.round(interactionScore),
        source: Math.round(sourceScore)
      }
    } }

  calculateBehavioralScore(activities) {
    if (!activities.length) return 0;
    
    const scoreMap = {
      page_view: 2,
      form_submit: 15,
      email_open: 5,
      email_click: 8,
      download: 12,
      video_view: 6,
      chat_start: 10,
      call_made: 25,
      meeting_scheduled: 30,
      proposal_sent: 35,
      contract_signed: 50
    };

    const recentActivities = activities.filter(activity => {
      const activityDate = new Date(activity.createdAt);
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      return activityDate > thirtyDaysAgo;
    });

    return Math.min(100, recentActivities.reduce((score, activity) => {
      return score + (scoreMap[activity.type] || 0);
    }, 0));
  }

  calculateDemographicScore(lead) {
    let score = 0;
    const profile = this.config.customerProfile || {};
    
    // 行业匹配
    if (profile.targetIndustries && lead.industry) {
      if (lead.industry === profile.targetIndustries) score += 25;
    }
    
    // 职位匹配
    if (profile.targetJobTitles && lead.jobTitle) {
      const targetTitles = profile.targetJobTitles.toLowerCase().split('');
      if (targetTitles.some(title => lead.jobTitle.toLowerCase().includes(title.trim()))) {
        score += 25;
      }
    }
    
    // 公司规模（如果有相关信息）
    if (profile.companySize && lead.customFields?.companySize) {
      if (lead.customFields.companySize === profile.companySize) score += 20;
    }
    
    // 地理位置
    if (profile.targetRegions && lead.location?.city) {
      const targetRegions = profile.targetRegions.toLowerCase().split('');
      if (targetRegions.some(region => lead.location.city.toLowerCase().includes(region.trim()))) {
        score += 15;
      }
    }
    
    // 基本信息完整性
    const fields = [lead.firstName, lead.lastName, lead.email, lead.company, lead.jobTitle];
    const completeness = fields.filter(field => field && field.trim()).length / fields.length;
    score += completeness * 15;
    
    return Math.min(100, score);
  }

  calculateInteractionScore(activities) {
    if (!activities.length) return 0;
    
    const interactionTypes = ['email_open' 'email_click' 'form_submit' 'chat_start'];
    const interactions = activities.filter(activity => 
      interactionTypes.includes(activity.type)
    );
    
    // 互动频率
    const uniqueDays = new Set(interactions.map(activity => 
      new Date(activity.createdAt).toDateString()
    )).size;
    
    // 互动多样性
    const uniqueTypes = new Set(interactions.map(activity => activity.type)).size;
    
    return Math.min(100, uniqueDays * 10 + uniqueTypes * 15);
  }

  calculateSourceScore(lead) {
    const sourceScores = {
      website: 15,
      organic_search: 20,
      paid_search: 15,
      social: 10,
      email: 25,
      referral: 30,
      direct: 35,
      partnership: 40
    };
    
    return sourceScores[lead.source] || 5;
  }

  applyTimeDecay(score, leadCreatedAt) {
    const decayFactor = this.config.scoringRules?.timeDecayFactor || 0.1;
    const decayStartDays = this.config.scoringRules?.decayStartDays || 7;
    
    const daysSinceCreation = Math.floor(
      (Date.now() - new Date(leadCreatedAt).getTime()) / (1000 * 60 * 60 * 24)
    );
    
    if (daysSinceCreation <= decayStartDays) return score;
    
    const decayDays = daysSinceCreation - decayStartDays;
    const decayMultiplier = Math.pow(1 - decayFactor, decayDays);
    
    return Math.round(score * decayMultiplier);
  }
}

// API控制器
const leadGenerationController = {
  // 创建新线索
  async createLead(req, res) {
    try {
      const { agentId } = req.params;
      const leadData = req.body;
      
      // 检查是否已存在相同邮箱的线索
      const existingLead = await Lead.findOne({
        where: { 
          email: leadData.email,
          agentId: agentId
        }
      });
      
      if (existingLead) {
        return res.status(409).json({
          success: false,
          message: '该邮箱的线索已存在',
          leadId: existingLead.id
        });
      }
      
      // 创建新线索
      const lead = await Lead.create({
        ...leadData,
        agentId: agentId,
        source: leadData.source || 'website',
        sourceDetails: leadData.sourceDetails || {},
        customFields: leadData.customFields || {},
        status: 'new',
        temperature: 'warm',
        score: Math.floor(Math.random() * 100),
        createdAt: new Date()
      });
      
      // 记录表单提交活动
      if (leadData.formData) {
        await LeadFormSubmission.create({
          leadId: lead.id,
          agentId: agentId,
          formType: leadData.formType || 'contact'
          formData: leadData.formData,
          pageUrl: leadData.pageUrl,
          pageTitle: leadData.pageTitle,
          referrerUrl: leadData.referrerUrl,
          utmSource: leadData.utmSource,
          utmMedium: leadData.utmMedium,
          utmCampaign: leadData.utmCampaign,
          userAgent: req.headers['user-agent'],
          ipAddress: req.ip
        });
        
        await LeadActivity.create({
          leadId: lead.id,
          type: 'form_submit'
          description: `表单提交: ${leadData.formType || 'contact'}`,
          metadata: {
            formType: leadData.formType,
            pageUrl: leadData.pageUrl
          },
          userAgent: req.headers['user-agent'],
          ipAddress: req.ip
        });
      }
      
      // 初始评分
      await leadGenerationController.updateLeadScore(lead.id, agentId);
      
      res.status(201).json({
        success: true,
        message: '线索创建成功'
        lead: lead
      });
      
    } catch (error) {
      console.error('创建线索失败:', error); res.status(500).json({
        success: false,
        message: '创建线索失败'
        error: error.message
      });
    }
  },

  // 获取线索列表
  async getLeads(req, res) {
    try {
      const { agentId } = req.params;
      const { 
        page = 1, 
        limit = 20, 
        status, 
        temperature, 
        source,
        assignedTo,
        minScore,
        maxScore,
        search,
        sortBy = 'createdAt'
        sortOrder = 'DESC'
      } = req.query;

      const where = { agentId };
      
      // 状态筛选
      if (status) where.status = status;
      if (temperature) where.temperature = temperature;
      if (source) where.source = source;
      if (assignedTo) where.assignedTo = assignedTo;
      
      // 评分筛选
      if (minScore || maxScore) {
        where.score = {};
        if (minScore) where.score[Op.gte] = parseInt(minScore);
        if (maxScore) where.score[Op.lte] = parseInt(maxScore);
      }
      
      // 搜索
      if (search) {
        where[Op.or] = [
          { firstName: { [Op.iLike]: `%${search}%` } },
          { lastName: { [Op.iLike]: `%${search}%` } },
          { email: { [Op.iLike]: `%${search}%` } },
          { company: { [Op.iLike]: `%${search}%` } }
        ];
      }

      const offset = (page - 1) * limit;
      
      const { count, rows: leads } = await Lead.findAndCountAll({
        where,
        include: [
          {
            model: LeadActivity,
            as: 'activities'
            limit: 5,
            order: [['createdAt', 'DESC']]
          },
          {
            model: LeadTag,
            as: 'tags'
            through: { attributes: [] }
          }
        ],
        limit: parseInt(limit),
        offset: offset,
        order: [[sortBy, sortOrder]]
      });

      res.json({
        success: true,
        data: {
          leads,
          pagination: {
            currentPage: parseInt(page),
            totalPages: Math.ceil(count / limit),
            totalItems: count,
            itemsPerPage: parseInt(limit)
          }
        }
      });
      
    } catch (error) {
      console.error('获取线索列表失败:', error); res.status(500).json({
        success: false,
        message: '获取线索列表失败'
        error: error.message
      });
    }
  },

  // 获取单个线索详情
  async getLeadById(req, res) {
    try {
      const { leadId } = req.params;
      
      const lead = await Lead.findByPk(leadId, {
        include: [
          {
            model: LeadActivity,
            as: 'activities'
            order: [['createdAt', 'DESC']]
          },
          {
            model: LeadScore,
            as: 'scoreHistory'
            order: [['createdAt', 'DESC']],
            limit: 10
          },
          {
            model: LeadFormSubmission,
            as: 'formSubmissions'
            order: [['createdAt', 'DESC']]
          },
          {
            model: LeadTag,
            as: 'tags'
            through: { attributes: [] }
          },
          {
            model: LeadConversion,
            as: 'conversions'
            order: [['convertedAt', 'DESC']]
          }
        ]
      });
      
      if (!lead) {
        return res.status(404).json({
          success: false,
          message: '线索不存在'
        });
      }
      
      res.json({
        success: true,
        data: lead
      });
      
    } catch (error) {
      console.error('获取线索详情失败:', error); res.status(500).json({
        success: false,
        message: '获取线索详情失败'
        error: error.message
      });
    }
  },

  // 更新线索评分
  async updateLeadScore(leadId, agentId, activityId = null) {
    try {
      // 获取智能代理配置
      const Agent = require('../models/Agent');
      const agent = await Agent.findByPk(agentId);
      if (!agent) throw new Error('智能代理不存在');
      
      const config = agent.config || {};
      
      // 获取线索和活动
      const lead = await Lead.findByPk(leadId, {
        include: [{
          model: LeadActivity,
          as: 'activities'
          order: [['createdAt', 'DESC']]
        }]
      });
      
      if (!lead) throw new Error('线索不存在');
      
      // 计算新评分
      const scoringEngine = new LeadScoringEngine(config);
      const scoreResult = scoringEngine.calculateScore(lead, lead.activities);
      const finalScore = scoringEngine.applyTimeDecay(scoreResult.totalScore, lead.createdAt);
      
      const previousScore = lead.score;
      const scoreChange = finalScore - previousScore;
      
      // 更新线索评分
      await lead.update({
        score: finalScore,
        temperature: finalScore >= 80 ? 'hot' : finalScore >= 50 ? 'warm' : 'cold'
      });
      
      // 记录评分历史
      await LeadScore.create({
        leadId: leadId,
        previousScore: previousScore,
        newScore: finalScore,
        scoreChange: scoreChange,
        reason: activityId ? '活动触发评分更新' : '评分重新计算'
        behavioralScore: scoreResult.breakdown.behavioral,
        demographicScore: scoreResult.breakdown.demographic,
        interactionScore: scoreResult.breakdown.interaction,
        sourceScore: scoreResult.breakdown.source,
        activityId: activityId,
        scoringConfig: config.scoringRules || {}
      });
      
      return { lead, scoreResult, finalScore } } catch (error) {
      console.error('更新线索评分失败:', error);
      throw error;
    }
  },

  // 记录线索活动
  async recordActivity(req, res) {
    try {
      const { leadId } = req.params;
      const { type, description, metadata = {} } = req.body;
      
      const lead = await Lead.findByPk(leadId);
      if (!lead) {
        return res.status(404).json({
          success: false,
          message: '线索不存在'
        });
      }
      
      // 创建活动记录
      const activity = await LeadActivity.create({
        leadId: leadId,
        type: type,
        description: description,
        metadata: metadata,
        userAgent: req.headers['user-agent'],
        ipAddress: req.ip
      });
      
      // 更新线索最后互动时间
      await lead.update({
        lastInteractionAt: new Date()
      });
      
      // 重新计算评分
      await leadGenerationController.updateLeadScore(leadId, lead.agentId, activity.id);
      
      res.json({
        success: true,
        message: '活动记录成功'
        activity: activity
      });
      
    } catch (error) {
      console.error('记录活动失败:', error); res.status(500).json({
        success: false,
        message: '记录活动失败'
        error: error.message
      });
    }
  },

  // 获取线索统计数据
  async getLeadStats(req, res) {
    try {
      const { agentId } = req.params;
      const { timeRange = '30d' } = req.query;
      
      let dateFilter = {};
      const now = new Date();
      
      switch (timeRange) {
        case '7d':
          dateFilter = { [Op.gte]: new Date(now - 7 * 24 * 60 * 60 * 1000) };
          break;
        case '30d':
          dateFilter = { [Op.gte]: new Date(now - 30 * 24 * 60 * 60 * 1000) };
          break;
        case '90d':
          dateFilter = { [Op.gte]: new Date(now - 90 * 24 * 60 * 60 * 1000) };
          break;
        default:
          dateFilter = { [Op.gte]: new Date(now - 30 * 24 * 60 * 60 * 1000) } }
      
      // 基本统计
      const totalLeads = await Lead.count({ where: { agentId } });
      const newLeads = await Lead.count({ 
        where: { 
          agentId, 
          createdAt: dateFilter 
        } 
      });
      
      // 状态分布
      const statusDistribution = await Lead.findAll({
        where: { agentId },
        attributes: [
          'status'
          [Sequelize.fn('COUNT' Sequelize.col('id')), 'count']
        ],
        group: ['status']
      });
      
      // 温度分布
      const temperatureDistribution = await Lead.findAll({
        where: { agentId },
        attributes: [
          'temperature'
          [Sequelize.fn('COUNT' Sequelize.col('id')), 'count']
        ],
        group: ['temperature']
      });
      
      // 来源分布
      const sourceDistribution = await Lead.findAll({
        where: { agentId },
        attributes: [
          'source'
          [Sequelize.fn('COUNT' Sequelize.col('id')), 'count']
        ],
        group: ['source']
      });
      
      // 评分分布
      const scoreRanges = [
        { range: '0-25' min: 0, max: 25 },
        { range: '26-50' min: 26, max: 50 },
        { range: '51-75' min: 51, max: 75 },
        { range: '76-100' min: 76, max: 100 }
      ];
      
      const scoreDistribution = await Promise.all(
        scoreRanges.map(async (range) => ({
          range: range.range,
          count: await Lead.count({
            where: {
              agentId,
              score: { [Op.between]: [range.min, range.max] }
            }
          })
        }))
      );
      
      // 转化率统计
      const qualifiedLeads = await Lead.count({
        where: { 
          agentId,
          status: { [Op.in]: ['qualified', 'converted'] }
        }
      });
      
      const conversionRate = totalLeads > 0 ? (qualifiedLeads / totalLeads * 100).toFixed(2) : 0;
      
      // 每日新线索趋势
      const dailyTrend = await Lead.findAll({
        where: { 
          agentId,
          createdAt: dateFilter
        },
        attributes: [
          [Sequelize.fn('DATE' Sequelize.col('createdAt')), 'date'],
          [Sequelize.fn('COUNT' Sequelize.col('id')), 'count']
        ],
        group: [Sequelize.fn('DATE' Sequelize.col('createdAt'))],
        order: [[Sequelize.fn('DATE' Sequelize.col('createdAt')), 'ASC']]
      });
      
      res.json({
        success: true,
        data: {
          summary: {
            totalLeads,
            newLeads,
            conversionRate: parseFloat(conversionRate)
          },
          distributions: {
            status: statusDistribution,
            temperature: temperatureDistribution,
            source: sourceDistribution,
            score: scoreDistribution
          },
          trends: {
            daily: dailyTrend
          }
        }
      });
      
    } catch (error) {
      console.error('获取线索统计失败:', error); res.status(500).json({
        success: false,
        message: '获取线索统计失败'
        error: error.message
      });
    }
  },

  // 批量操作线索
  async bulkUpdateLeads(req, res) {
    try {
      const { leadIds, updates } = req.body;
      
      if (!leadIds || !Array.isArray(leadIds) || leadIds.length === 0) {
        return res.status(400).json({
          success: false,
          message: '请提供有效的线索ID列表'
        });
      }
      
      const result = await Lead.update(updates, {
        where: { id: { [Op.in]: leadIds } }
      });
      
      res.json({
        success: true,
        message: `成功更新 ${result[0]} 个线索`,
        updatedCount: result[0]
      });
      
    } catch (error) {
      console.error('批量更新线索失败:', error); res.status(500).json({
        success: false,
        message: '批量更新线索失败'
        error: error.message
      });
    }
  },

  // 删除线索
  async deleteLead(req, res) {
    try {
      const { leadId } = req.params;
      
      const lead = await Lead.findByPk(leadId);
      if (!lead) {
        return res.status(404).json({
          success: false,
          message: '线索不存在'
        });
      }
      
      await lead.destroy();
      
      res.json({
        success: true,
        message: '线索删除成功'
      });
      
    } catch (error) {
      console.error('删除线索失败:', error); res.status(500).json({
        success: false,
        message: '删除线索失败'
        error: error.message
      });
    }
  }
};

module.exports = leadGenerationController; 