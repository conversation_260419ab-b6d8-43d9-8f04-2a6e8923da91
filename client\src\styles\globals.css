/* 全局基础样式 - 统一UI/UX设计系统 */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import "tailwindcss/base";
@import "tailwindcss/components";
@import "tailwindcss/utilities";

/* CSS变量定义 - 紫色主题设计系统 */
:root {
  --color-1: 265 83% 58%;
  --color-2: 280 90% 65%;
  --color-3: 310 90% 65%;
  --color-4: 240 90% 70%;
  --color-5: 257 90% 67%;
  /* 主色调 - 紫色系 */
  --color-primary-50: #f5f3ff;
  --color-primary-100: #ede9fe;
  --color-primary-200: #ddd6fe;
  --color-primary-300: #c4b5fd;
  --color-primary-400: #a78bfa;
  --color-primary-500: #8b5cf6;
  --color-primary-600: #7c3aed;
  --color-primary-700: #6d28d9;
  --color-primary-800: #5b21b6;
  --color-primary-900: #4c1d95;

  /* 背景色系 */
  --color-background-primary: #ffffff;
  --color-background-secondary: #f8fafc;
  --color-background-accent: #f1f5f9;
  --color-background-muted: #f9fafb;
  --color-background-hover: #f3f4f6;

  /* 文字色系 */
  --color-text-primary: #1e293b;
  --color-text-secondary: #64748b;
  --color-text-muted: #94a3b8;
  --color-text-accent: #8b5cf6;
  --color-text-inverse: #ffffff;

  /* 边框色 */
  --color-border-light: #f1f5f9;
  --color-border-default: #e2e8f0;
  --color-border-dark: #cbd5e1;
  --color-border-accent: #8b5cf6;

  /* 语义色彩 */
  --color-success: #22c55e;
  --color-success-light: #f0fdf4;
  --color-warning: #f59e0b;
  --color-warning-light: #fffbeb;
  --color-error: #ef4444;
  --color-error-light: #fef2f2;
  --color-info: #3b82f6;
  --color-info-light: #eff6ff;

  /* 间距系统 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-xxl: 48px;

  /* 圆角系统 */
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --border-radius-lg: 12px;
  --border-radius-xl: 16px;

  /* 阴影系统 */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-card: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-hover: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-focus: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);

  /* 动画系统 */
  --duration-fast: 150ms;
  --duration-normal: 300ms;
  --duration-slow: 500ms;
  --easing-ease-out: cubic-bezier(0, 0, 0.2, 1);
  --easing-ease-in: cubic-bezier(0.4, 0, 1, 1);
  --easing-ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
}

/* 基础元素样式重置 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
  font-size: 16px;
  line-height: 1.5;
  color: var(--color-text-primary);
  background-color: var(--color-background-primary);
  scroll-behavior: smooth;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  font-family: inherit;
  background-color: var(--color-background-secondary);
  min-height: 100vh;
  overflow-x: hidden;
}

/* 全局组件样式 */

/* 卡片组件统一样式 */
.card-unified {
  background-color: var(--color-background-primary);
  border: 1px solid var(--color-border-light);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-card);
  transition: all var(--duration-normal) var(--easing-ease-out);
}

.card-unified:hover {
  box-shadow: var(--shadow-hover);
  border-color: var(--color-border-default);
}

/* 按钮组件统一样式 */
.button-primary {
  background: linear-gradient(135deg, var(--color-primary-500), var(--color-primary-600));
  color: var(--color-text-inverse);
  border: none;
  border-radius: var(--border-radius-md);
  padding: var(--spacing-sm) var(--spacing-md);
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
  transition: all var(--duration-normal) var(--easing-ease-out);
  box-shadow: var(--shadow-sm);
}

.button-primary:hover {
  background: linear-gradient(135deg, var(--color-primary-600), var(--color-primary-700));
  box-shadow: var(--shadow-hover);
  transform: translateY(-1px);
}

.button-primary:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.button-secondary {
  background-color: var(--color-background-primary);
  color: var(--color-text-primary);
  border: 1px solid var(--color-border-default);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-sm) var(--spacing-md);
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
  transition: all var(--duration-normal) var(--easing-ease-out);
}

.button-secondary:hover {
  border-color: var(--color-primary-300);
  color: var(--color-primary-600);
  background-color: var(--color-primary-50);
}

/* 徽章组件统一样式 */
.badge-success {
  background-color: var(--color-success-light);
  color: var(--color-success);
  border: 1px solid var(--color-success);
  border-radius: var(--border-radius-sm);
  padding: 2px var(--spacing-sm);
  font-size: 12px;
  font-weight: 500;
}

.badge-warning {
  background-color: var(--color-warning-light);
  color: var(--color-warning);
  border: 1px solid var(--color-warning);
  border-radius: var(--border-radius-sm);
  padding: 2px var(--spacing-sm);
  font-size: 12px;
  font-weight: 500;
}

.badge-error {
  background-color: var(--color-error-light);
  color: var(--color-error);
  border: 1px solid var(--color-error);
  border-radius: var(--border-radius-sm);
  padding: 2px var(--spacing-sm);
  font-size: 12px;
  font-weight: 500;
}

.badge-info {
  background-color: var(--color-info-light);
  color: var(--color-info);
  border: 1px solid var(--color-info);
  border-radius: var(--border-radius-sm);
  padding: 2px var(--spacing-sm);
  font-size: 12px;
  font-weight: 500;
}

/* 输入框组件统一样式 */
.input-unified {
  background-color: var(--color-background-primary);
  border: 1px solid var(--color-border-default);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: 14px;
  color: var(--color-text-primary);
  transition: all var(--duration-normal) var(--easing-ease-out);
  width: 100%;
}

.input-unified:focus {
  outline: none;
  border-color: var(--color-primary-500);
  box-shadow: 0 0 0 3px rgb(139 92 246 / 0.1);
}

.input-unified::placeholder {
  color: var(--color-text-muted);
}

/* 表格组件统一样式 */
.table-unified {
  width: 100%;
  border-collapse: collapse;
  background-color: var(--color-background-primary);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-card);
}

.table-unified th {
  background-color: var(--color-background-accent);
  color: var(--color-text-primary);
  font-weight: 600;
  font-size: 14px;
  padding: var(--spacing-md);
  text-align: left;
  border-bottom: 1px solid var(--color-border-default);
}

.table-unified td {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--color-border-light);
  color: var(--color-text-secondary);
  font-size: 14px;
}

.table-unified tr:hover {
  background-color: var(--color-primary-50);
}

/* 图表容器统一样式 */
.chart-container {
  background-color: var(--color-background-primary);
  border: 1px solid var(--color-border-light);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-card);
  transition: all var(--duration-normal) var(--easing-ease-out);
}

.chart-container:hover {
  box-shadow: var(--shadow-hover);
}

/* 页面布局统一样式 */
.page-header {
  margin-bottom: var(--spacing-xl);
  padding-bottom: var(--spacing-lg);
  border-bottom: 1px solid var(--color-border-light);
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-sm);
}

.page-description {
  font-size: 16px;
  color: var(--color-text-muted);
  line-height: 1.6;
}

/* 加载状态动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* 图表进入动画 */
@keyframes chart-enter {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.chart-enter-active {
  animation: chart-enter var(--duration-slow) var(--easing-ease-out);
}

/* 卡片进入动画 */
@keyframes card-enter {
  0% {
    opacity: 0;
    transform: translateY(10px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.card-enter-active {
  animation: card-enter var(--duration-normal) var(--easing-ease-out);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-title {
    font-size: 24px;
  }
  
  .page-description {
    font-size: 14px;
  }
  
  .card-unified {
    margin-bottom: var(--spacing-md);
  }
  
  .button-primary,
  .button-secondary {
    padding: var(--spacing-sm);
    font-size: 13px;
  }
}

@media (max-width: 640px) {
  .page-title {
    font-size: 20px;
  }
  
  .chart-container {
    padding: var(--spacing-md);
  }
  
  .table-unified th,
  .table-unified td {
    padding: var(--spacing-sm);
    font-size: 12px;
  }
}

/* 工具类 */
.text-gradient-primary {
  background: linear-gradient(135deg, var(--color-primary-500), var(--color-primary-700));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.bg-gradient-primary {
  background: linear-gradient(135deg, var(--color-primary-500), var(--color-primary-600));
}

.shadow-glow {
  box-shadow: 0 0 20px rgb(139 92 246 / 0.15);
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-background-muted);
  border-radius: var(--border-radius-sm);
}

::-webkit-scrollbar-thumb {
  background: var(--color-border-default);
  border-radius: var(--border-radius-sm);
  transition: background var(--duration-normal);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-primary-300);
}

/* 焦点样式 */
.focus-ring:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgb(139 92 246 / 0.1);
  border-color: var(--color-primary-500);
}

/* 禁用状态 */
.disabled {
  opacity: 0.5;
  pointer-events: none;
  cursor: not-allowed;
}

/* 成功状态颜色 */
.text-success {
  color: var(--color-success);
}

.bg-success-light {
  background-color: var(--color-success-light);
}

/* 警告状态颜色 */
.text-warning {
  color: var(--color-warning);
}

.bg-warning-light {
  background-color: var(--color-warning-light);
}

/* 错误状态颜色 */
.text-error {
  color: var(--color-error);
}

.bg-error-light {
  background-color: var(--color-error-light);
}

/* 信息状态颜色 */
.text-info {
  color: var(--color-info);
}

.bg-info-light {
  background-color: var(--color-info-light);
}

/* 强制图表文字可见性 - 最高优先级覆盖 */
.recharts-wrapper text,
.recharts-pie-label-text,
.recharts-label,
.recharts-text,
.recharts-cartesian-axis-tick-value,
.recharts-default-legend .recharts-legend-item-text,
.recharts-pie-label,
.recharts-tooltip-item-name,
.recharts-tooltip-item-value,
.recharts-legend-item text {
  fill: #1f2937 !important;
  color: #1f2937 !important;
  font-weight: 700 !important;
  font-size: 13px !important;
  opacity: 1 !important;
  text-shadow: 0 0 3px rgba(255, 255, 255, 0.8), 0 0 6px rgba(255, 255, 255, 0.6) !important;
  z-index: 1000 !important;
  visibility: visible !important;
  display: block !important;
}

/* 饼图标签线强制可见 */
.recharts-pie-label-line {
  stroke: #1f2937 !important;
  stroke-width: 2px !important;
  opacity: 1 !important;
  visibility: visible !important;
}

/* 强制RealTimeIndicator可见性 */
.real-time-indicator-container {
  position: relative !important;
  z-index: 1000 !important;
}

.real-time-indicator-container .lucide,
.real-time-indicator-container .lucide-activity,
.real-time-indicator-container .lucide-wifi,
.real-time-indicator-container .lucide-wifi-off {
  color: currentColor !important;
  fill: currentColor !important;
  opacity: 1 !important;
}

/* Badge强制可见性样式 */
[data-testid="badge"],
.badge,
.bg-green-600,
.bg-red-600 {
  opacity: 1 !important;
  visibility: visible !important;
  z-index: 1000 !important;
}

/* 深色模式下的文字可见性 */
@media (prefers-color-scheme: dark) {
  .recharts-wrapper text,
  .recharts-pie-label-text,
  .recharts-label,
  .recharts-text,
  .recharts-cartesian-axis-tick-value,
  .recharts-default-legend .recharts-legend-item-text,
  .recharts-pie-label {
    fill: #f8fafc !important;
    color: #f8fafc !important;
    text-shadow: 0 0 3px rgba(0, 0, 0, 0.8), 0 0 6px rgba(0, 0, 0, 0.6) !important;
  }
  
  .recharts-pie-label-line {
    stroke: #f8fafc !important;
  }
} 