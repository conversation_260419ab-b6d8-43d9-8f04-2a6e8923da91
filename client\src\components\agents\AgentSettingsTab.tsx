import React, { useState, useEffect } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Agent, AgentSettings, AgentTypes, AgentType } from "@/types/agent";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { getAgentSettings, updateAgentSettings } from "@/services/agentService";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { useForm } from "react-hook-form";
import { useToast } from "@/hooks/use-toast";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { CheckCircle2 } from "lucide-react";

interface AgentSettingsTabProps {
  agent: Agent;
  onUpdate: () => void;
}

const baseSettingsFormSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters" }),
  description: z.string().optional(),
  active: z.boolean()
});

const agentSettingsFormSchema = baseSettingsFormSchema.extend({
  config: z.record(z.any())
});

type AgentSettingsFormData = z.infer<typeof agentSettingsFormSchema>;

export function AgentSettingsTab({ agent, onUpdate }: AgentSettingsTabProps) {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const [lastSavedAt, setLastSavedAt] = useState<Date | null>(null);
  const [prevSettings, setPrevSettings] = useState<AgentSettingsFormData | null>(null);
  const [localConfig, setLocalConfig] = useState<Record<string, any>>({});

  console.log("AgentSettingsTab: Received agent object:", {
    id: agent.id,
    name: agent.name,
    type: agent.type,
    status: agent.status,
    fullAgent: agent
  });

  const isValidAgentType = agent.type && Object.values(AgentTypes).includes(agent.type);
  console.log("AgentSettingsTab: Agent type validation:", {
    agentType: agent.type,
    isValid: isValidAgentType,
    availableTypes: Object.values(AgentTypes)
  });

  const { data: currentSettings, isLoading: isLoadingSettings, error: settingsError } = useQuery<AgentSettings, Error>({
    queryKey: ["agentSettings", agent.id],
    queryFn: () => getAgentSettings(agent.id),
    enabled: !!agent.id
  });

  const form = useForm<AgentSettingsFormData>({
    mode: "onChange",
    reValidateMode: "onChange",
    resolver: zodResolver(agentSettingsFormSchema),
    defaultValues: React.useMemo(() => ({
      name: currentSettings?.name || agent.name || "",
      description: currentSettings?.description || agent.description || "",
      active: currentSettings?.active ?? true,
      config: currentSettings?.config || {}
    }), [currentSettings, agent.name, agent.description])
  });

  React.useEffect(() => {
    if (currentSettings) {
      const formValues = {
        name: currentSettings.name || agent.name || "",
        description: currentSettings.description || agent.description || "",
        active: currentSettings.active ?? true,
        config: currentSettings.config || {}
      };
      
      form.reset(formValues);
      setLocalConfig(currentSettings.config || {});
      console.log("AgentSettingsTab: Settings loaded, localConfig set to:", currentSettings.config || {});
    }
  }, [currentSettings, agent.name, agent.description, agent.id, form]);

  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (form.formState.isDirty && !form.formState.isSubmitting) {
        e.preventDefault();
        e.returnValue = "";
      }
    };
    window.addEventListener("beforeunload", handleBeforeUnload);
    return () => window.removeEventListener("beforeunload", handleBeforeUnload);
  }, [form.formState.isDirty, form.formState.isSubmitting]);

  const { handleSubmit, formState: { isDirty, isSubmitting, isValid }, register } = form;

  const mutation = useMutation<
    AgentSettings,
    Error,
    { agentId: string; settings: Partial<AgentSettings> }
  >({
    mutationFn: ({ agentId, settings }) => {
      console.log("开始提交设置到服务器:", {
        agentId,
        settingsSize: JSON.stringify(settings).length,
        configSize: settings.config ? JSON.stringify(settings.config).length : 0
      });
      return updateAgentSettings(agentId, settings);
    },
    onSuccess: (data) => {
      console.log("设置保存成功，服务器返回:", data);
      queryClient.invalidateQueries({ queryKey: ["agentSettings", agent.id] });
      queryClient.invalidateQueries({ queryKey: ["agent", agent.id] });
      
      const formValues = {
        name: data.name || "",
        description: data.description || "",
        active: data.active || false,
        config: data.config || {}
      };
      form.reset(formValues);
      setLocalConfig(data.config || {});
      
      onUpdate();
      toast({ title: "Settings Updated", description: `Agent "${agent.name}" settings have been saved successfully.` });
      setLastSavedAt(new Date());
      setPrevSettings({
        name: data.name || "",
        description: data.description || "",
        active: data.active || false,
        config: data.config || {}
      });
    },
    onError: (error) => {
      console.error("设置保存失败:", {
        message: error.message,
        stack: error.stack
      });
      toast({ title: "Update Failed", description: error.message });
    }
  });

  // const handleConfigChange = React.useCallback((fieldName: string, value: any) => {
  //   console.log(`配置字段变更: ${fieldName}`, value);
  //   
  //   setValue(`config.${fieldName}` as any, value, { 
  //     shouldDirty: true, 
  //     shouldTouch: true, 
  //     shouldValidate: true 
  //   });
  //   
  //   setLocalConfig(prev => {
  //     const newConfig = { ...prev };
  //     const keys = fieldName.split(".");
  //     let current = newConfig;
  //     
  //     for (let i = 0; i < keys.length - 1; i++) {
  //       const key = keys[i];
  //       if (!current[key] || typeof current[key] !== "object") {
  //         current[key] = {};
  //       }
  //       current = current[key];
  //     }
  //     
  //     const lastKey = keys[keys.length - 1];
  //     current[lastKey] = value;
  //     
  //     console.log("AgentSettingsTab: localConfig updated for path:", fieldName, "value:", value, "newConfig:", newConfig);
  //     return newConfig;
  //   });
  // }, [setValue]);

  const onSubmit = (formData: AgentSettingsFormData) => {
    const submitData = {
      ...formData,
      config: localConfig
    };
    
    console.log("提交表单数据:", {
      name: submitData.name,
      description: submitData.description,
      active: submitData.active,
      configSize: JSON.stringify(submitData.config).length,
      configFields: Object.keys(submitData.config || {}),
      configContent: submitData.config
    });
    
    setPrevSettings(submitData);
    mutation.mutate({ agentId: agent.id, settings: submitData });
  };

  const handleUndo = () => {
    if (!prevSettings) return;
    mutation.mutate({ agentId: agent.id, settings: prevSettings });
  };

  const renderConfigForm = () => {
    console.log("AgentSettingsTab: renderConfigForm called with agent.type:", agent.type);
    console.log("AgentSettingsTab: Available AgentTypes:", AgentTypes);
    console.log("AgentSettingsTab: localConfig:", localConfig);
    
    if (!agent.type) {
      return (
        <div className="py-6 text-center text-muted-foreground border-2 border-dashed border-amber-200 bg-amber-50 rounded-lg">
          <p className="text-amber-700 font-medium">代理类型未定义</p>
          <p className="text-amber-600 text-sm mt-2">无法加载配置表单，因为代理类型为空</p>
          <p className="text-xs mt-2 text-amber-500">代理 ID: {agent.id}</p>
          <p className="text-xs text-amber-500">代理名称: {agent.name}</p>
          <p className="text-xs text-amber-500">请检查后端API是否正确返回了代理类型字段</p>
        </div>
      );
    }

    if (!Object.values(AgentTypes).includes(agent.type as AgentType)) {
      return (
        <div className="py-6 text-center text-muted-foreground border-2 border-dashed border-red-200 bg-red-50 rounded-lg">
          <p className="text-red-700 font-medium">不支持的代理类型</p>
          <p className="text-red-600 text-sm mt-2">当前代理类型: <span className="font-mono">{agent.type}</span></p>
          <p className="text-xs mt-2 text-red-500">支持的类型: {Object.values(AgentTypes).join(" ")}</p>
        </div>
      );
    }
    
    return (
      <div className="space-y-4">
        <div className="text-sm text-muted-foreground">
          Agent Type: {agent.type}
        </div>
        <div className="p-4 border rounded-lg bg-gray-50">
          <p className="text-sm">Agent specific configuration will be loaded here.</p>
          <p className="text-xs text-muted-foreground mt-2">
            Config data: {JSON.stringify(localConfig, null, 2)}
          </p>
        </div>
      </div>
    );
  };

  if (isLoadingSettings) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <div className="h-6 bg-gray-200 rounded animate-pulse" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="h-4 bg-gray-200 rounded animate-pulse" />
              <div className="h-4 bg-gray-200 rounded animate-pulse" />
              <div className="h-4 bg-gray-200 rounded animate-pulse" />
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (settingsError) {
    return (
      <div className="p-6 text-center text-red-600 border-2 border-dashed border-red-200 bg-red-50 rounded-lg">
        <p className="font-medium">加载设置失败</p>
        <p className="text-sm mt-2">{settingsError.message}</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              基本设置
              {lastSavedAt && (
                <div className="flex items-center text-sm text-green-600">
                  <CheckCircle2 className="w-4 h-4 mr-1" />
                  已保存于 {lastSavedAt.toLocaleTimeString()}
                </div>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">代理名称</Label>
              <Input 
                id="name"
                placeholder="输入代理名称" 
                {...register("name")} 
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">描述</Label>
              <Textarea 
                id="description"
                placeholder="输入代理描述" 
                {...register("description")} 
              />
            </div>

            <div className="flex flex-row items-center justify-between rounded-lg border p-4">
              <div className="space-y-0.5">
                <Label className="text-base">启用代理</Label>
                <p className="text-sm text-muted-foreground">
                  启用后代理将开始处理消息
                </p>
              </div>
              <Switch
                {...register("active")}
              />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>专用配置</CardTitle>
            <CardDescription>
              根据代理类型配置专用参数
            </CardDescription>
          </CardHeader>
          <CardContent>
            {renderConfigForm()}
          </CardContent>
        </Card>

        <div className="flex justify-end space-x-4">
          {prevSettings && (
            <Button type="button" variant="outline" onClick={handleUndo}>
              撤销更改
            </Button>
          )}
          <Button
            type="submit"
            disabled={!isDirty || isSubmitting || !isValid}
            className="min-w-[120px]"
          >
            {isSubmitting ? "保存中..." : "保存设置"}
          </Button>
        </div>
      </form>
    </div>
  );
}
