#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');

console.log('╔══════════════════════════════════════════╗');
console.log('║       iTeraBiz Core Service 启动         ║');
console.log('╚══════════════════════════════════════════╝');

// 启动Core Service
console.log('[启动] Core Service...');

const coreServicePath = path.join(__dirname, '../core-service');
const coreService = spawn('node', ['src/index.js'], {
  cwd: coreServicePath,
  stdio: 'inherit',
  shell: true,
  env: {
    ...process.env,
    NODE_ENV: 'development',
    PORT: '3002'
  }
});

// 处理退出
process.on('SIGINT', () => {
  console.log('\\n[退出] 停止 Core Service...');
  coreService.kill('SIGTERM');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\\n[退出] 停止 Core Service...');
  coreService.kill('SIGTERM');
  process.exit(0);
});

coreService.on('exit', (code) => {
  console.log(`[Core Service] 退出，代码: ${code}`);
  process.exit(code);
});

coreService.on('error', (err) => {
  console.error(`[Core Service] 启动错误: ${err.message}`);
  process.exit(1);
}); 