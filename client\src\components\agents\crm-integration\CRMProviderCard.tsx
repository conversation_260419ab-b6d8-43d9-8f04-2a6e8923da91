import React from 'react'
import { useTranslation } from 'react-i18next'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  CheckCircle, 
  Plus, 
  ExternalLink, 
  Star,
  Zap,
  Shield,
  Clock,
  Users
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { CRMProvider } from '@/types/crmIntegration'

interface CRMProviderCardProps {
  provider: CRMProvider;
  isConnected?: boolean;
  onConnect: () => void;
  compact?: boolean;
  className?: string;
}

const CRMProviderCard: React.FC<CRMProviderCardProps> = ({
  provider,
  isConnected = false,
  onConnect,
  compact = false,
  className
}) => {
  useTranslation();

  // 获取层级颜色
  const getTierColor = (tier: string) => {
    switch (tier) {
      case 'free':
        return 'bg-green-100 text-green-800';
      case 'premium':
        return 'bg-blue-100 text-blue-800';
      case 'enterprise':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'beta':
        return 'bg-yellow-100 text-yellow-800';
      case 'deprecated':
        return 'bg-red-100 text-red-800';
      case 'coming_soon':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // 获取分类图标
  const getCategoryIcon = () => {
    switch (provider.category) {
      case 'crm':
        return <Users className="h-4 w-4" />;
      case 'marketing':
        return <Zap className="h-4 w-4" />;
      case 'sales':
        return <Star className="h-4 w-4" />;
      case 'support':
        return <Shield className="h-4 w-4" />;
      case 'analytics':
        return <Clock className="h-4 w-4" />;
      default:
        return <Users className="h-4 w-4" />;
    }
  };

  // 计算支持的功能数量
  const supportedFeatures = provider.features.filter(f => f.supported).length;
  const totalFeatures = provider.features.length;

  return (
    <Card className={cn(
      "transition-all duration-200 hover:shadow-md",
      isConnected && "ring-2 ring-green-500 ring-opacity-20",
      className
    )}>
      <CardHeader className={cn("pb-3", compact && "pb-2")}>
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            {/* 提供商Logo */}
            <div className="w-10 h-10 rounded-lg bg-gray-100 flex items-center justify-center">
              {/* 这里应该是实际的logo图片 */}
              {getCategoryIcon()}
            </div>
            <div>
              <CardTitle className={cn("text-base", compact && "text-sm")}>
                {provider.displayName}
              </CardTitle>
              {!compact && (
                <div className="flex items-center gap-2 mt-1">
                  <Badge className={getTierColor(provider.tier)} variant="secondary">
                    {provider.tier}
                  </Badge>
                  <Badge className={getStatusColor(provider.status)} variant="secondary">
                    {provider.status}
                  </Badge>
                </div>
              )}
            </div>
          </div>
          
          {isConnected && (
            <CheckCircle className="h-5 w-5 text-green-600" />
          )}
        </div>

        {!compact && (
          <CardDescription className="text-sm">
            {provider.description}
          </CardDescription>
        )}
      </CardHeader>

      <CardContent className={cn("space-y-4", compact && "space-y-2")}>
        {/* 功能列表 */}
        {!compact && (
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="font-medium">Features</span>
              <span className="text-muted-foreground">
                {supportedFeatures}/{totalFeatures} supported
              </span>
            </div>
            <div className="grid grid-cols-1 gap-1">
              {provider.features.slice(0, 4).map((feature) => (
                <div key={feature.id} className="flex items-center gap-2 text-xs">
                  <div className={cn(
                    "w-2 h-2 rounded-full",
                    feature.supported ? "bg-green-500" : "bg-gray-300"
                  )} />
                  <span className={cn(
                    feature.supported ? "text-gray-900" : "text-gray-500"
                  )}>
                    {feature.name}
                  </span>
                  {feature.premium && (
                    <Badge variant="outline" className="text-xs px-1 py-0">
                      Pro
                    </Badge>
                  )}
                </div>
              ))}
              {provider.features.length > 4 && (
                <div className="text-xs text-muted-foreground">
                  +{provider.features.length - 4} more features
                </div>
              )}
            </div>
          </div>
        )}

        {/* 技术信息 */}
        {!compact && (
          <div className="flex items-center gap-4 text-xs text-muted-foreground">
            <div className="flex items-center gap-1">
              <Shield className="h-3 w-3" />
              {provider.authType.replace(/_/g, ' ')}
            </div>
            {provider.webhookSupport && (
              <div className="flex items-center gap-1">
                <Zap className="h-3 w-3" />
                Webhooks
              </div>
            )}
            <div className="flex items-center gap-1">
              <Clock className="h-3 w-3" />
              {provider.rateLimits.requests.toLocaleString()}/{provider.rateLimits.period}
            </div>
          </div>
        )}

        {/* 操作按钮 */}
        <div className="flex gap-2">
          {isConnected ? (
            <Button variant="outline" size="sm" className="flex-1">
              <CheckCircle className="h-4 w-4 mr-1" />
              Connected
            </Button>
          ) : (
            <Button onClick={onConnect} size="sm" className="flex-1">
              <Plus className="h-4 w-4 mr-1" />
              Connect
            </Button>
          )}
          
          {!compact && (
            <Button variant="ghost" size="sm">
              <ExternalLink className="h-4 w-4" />
            </Button>
          )}
        </div>

        {/* 快速统计 */}
        {compact && (
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <span>{supportedFeatures} features</span>
            <Badge className={getTierColor(provider.tier)} variant="secondary">
              {provider.tier}
            </Badge>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default CRMProviderCard;
