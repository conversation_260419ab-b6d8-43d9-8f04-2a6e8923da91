/**
 * Dashboard Theme Configuration
 * Enhanced design tokens for improved UI/UX
 */

export const dashboardTheme = {
  // Enhanced status colors with better contrast
  status: {
    success: {
      bg: 'bg-emerald-50 dark:bg-emerald-950',
      text: 'text-emerald-700 dark:text-emerald-300',
      border: 'border-emerald-200 dark:border-emerald-800',
      icon: 'text-emerald-500',
      accent: 'bg-emerald-500'
    },
    warning: {
      bg: 'bg-amber-50 dark:bg-amber-950', 
      text: 'text-amber-700 dark:text-amber-300',
      border: 'border-amber-200 dark:border-amber-800',
      icon: 'text-amber-500',
      accent: 'bg-amber-500'
    },
    error: {
      bg: 'bg-red-50 dark:bg-red-950',
      text: 'text-red-700 dark:text-red-300', 
      border: 'border-red-200 dark:border-red-800',
      icon: 'text-red-500',
      accent: 'bg-red-500'
    },
    info: {
      bg: 'bg-blue-50 dark:bg-blue-950',
      text: 'text-blue-700 dark:text-blue-300',
      border: 'border-blue-200 dark:border-blue-800',
      icon: 'text-blue-500',
      accent: 'bg-blue-500'
    }
  },

  // Enhanced button styles for better accessibility and visual appeal
  buttons: {
    primary: 'bg-blue-600 hover:bg-blue-700 text-white font-semibold px-4 py-2 rounded-lg shadow-lg shadow-blue-500/25 hover:shadow-blue-500/40 border-0 transition-all duration-200',
    secondary: 'bg-slate-100 hover:bg-slate-200 text-slate-700 border border-slate-300 shadow-sm hover:shadow-md transition-all duration-200',
    danger: 'bg-red-600 hover:bg-red-700 text-white shadow-lg shadow-red-500/25 hover:shadow-red-500/40 border-0 transition-all duration-200',
    success: 'bg-emerald-600 hover:bg-emerald-700 text-white shadow-lg shadow-emerald-500/25 hover:shadow-emerald-500/40 border-0 transition-all duration-200',
    outline: 'border-2 border-slate-300 hover:border-slate-400 bg-transparent hover:bg-slate-50 text-slate-700 transition-all duration-200'
  },

  // Card variations with enhanced visual hierarchy
  cards: {
    elevated: 'bg-white dark:bg-slate-900 border border-slate-200 dark:border-slate-800 rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-200',
    outlined: 'border border-slate-200 dark:border-slate-800 shadow-sm bg-white dark:bg-slate-900 hover:shadow-md transition-shadow duration-200',
    interactive: 'hover:shadow-xl hover:scale-[1.02] transition-all duration-200 cursor-pointer border border-slate-200 dark:border-slate-800',
    stats: 'bg-gradient-to-r from-white to-slate-50 dark:from-slate-900 dark:to-slate-800 border border-slate-200 dark:border-slate-700 shadow-sm',
    feature: 'bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950 dark:to-indigo-950 border border-blue-200 dark:border-blue-800'
  },

  // Enhanced chart colors for better data visualization
  charts: {
    colors: {
      primary: ['#3B82F6', '#1D4ED8', '#1E40AF'],
      secondary: ['#EF4444', '#F59E0B', '#10B981', '#3B82F6', '#8B5CF6'],
      gradients: {
        blue: 'from-blue-500 to-blue-600',
        green: 'from-emerald-500 to-emerald-600',
        red: 'from-red-500 to-red-600',
        purple: 'from-purple-500 to-purple-600'
      }
    },
    grid: {
      lightColor: '#E2E8F0',
      darkColor: '#334155'
    },
    axis: {
      lightColor: '#64748B',
      titleColor: '#F1F5F9',
      borderColor: '#475569'
    }
  },

  // Animation classes for micro-interactions
  animations: {
    fadeIn: 'animate-in fade-in duration-500',
    scaleIn: 'animate-in zoom-in-95 duration-200',
    bounce: 'animate-bounce'
  },

  // Spacing tokens for consistent layout
  spacing: {
    cardPadding: 'p-6',
    cardMargin: 'mb-6',
    sectionGap: 'gap-6',
    gridGap: 'gap-4 md:gap-6'
  }
};

// Utility function to get theme classes with optional overrides
export const getThemeClasses = (variant: keyof typeof dashboardTheme.cards, ...additionalClasses: string[]) => {
  return [dashboardTheme.cards[variant], ...additionalClasses].join(' ');
};

// Status badge utility
export const getStatusClasses = (status: 'success' | 'warning' | 'error' | 'info') => {
  const statusTheme = dashboardTheme.status[status];
  return `${statusTheme.bg} ${statusTheme.text} ${statusTheme.border} border px-3 py-1 rounded-full text-sm font-medium`;
};

export default dashboardTheme;