"use strict";(self.webpackChunkclient=self.webpackChunkclient||[]).push([[421],{"./src/components/ui/card.stories.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{Data:()=>Data,Default:()=>Default,Interactive:()=>Interactive,WithHeaderAndFooter:()=>WithHeaderAndFooter,__namedExportsOrder:()=>__namedExportsOrder,default:()=>card_stories});var react=__webpack_require__("./node_modules/react/index.js"),utils=__webpack_require__("./src/lib/utils.js"),jsx_runtime=__webpack_require__("./node_modules/react/jsx-runtime.js");const Card=react.forwardRef(((_ref,ref)=>{let{className,variant="default",...props}=_ref;return(0,jsx_runtime.jsx)("div",{ref,className:(0,utils.cn)("rounded-lg border p-4 transition-all duration-200",{default:"border-violet-light hover:border-violet-secondary hover:shadow-md hover:shadow-violet-light/20",data:"border-violet-light bg-gradient-to-br from-white to-violet-light/10 hover:shadow-md hover:shadow-violet-light/20",interactive:"border-violet-light hover:border-violet-secondary hover:shadow-md hover:shadow-violet-primary/20 cursor-pointer",success:"border-status-success bg-status-success/10 hover:shadow-status-success/20",warning:"border-status-warning bg-status-warning/10 hover:shadow-status-warning/20",error:"border-status-error bg-status-error/10 hover:shadow-status-error/20",purple:"border-violet-light/30 bg-violet-50/80 dark:bg-violet-900/20 hover:border-violet-secondary hover:shadow-md hover:shadow-violet-light/30"}[variant],className),...props})}));Card.displayName="Card";const CardHeader=react.forwardRef(((_ref2,ref)=>{let{className,...props}=_ref2;return(0,jsx_runtime.jsx)("div",{ref,className:(0,utils.cn)("flex flex-col space-y-1.5 p-1",className),...props})}));CardHeader.displayName="CardHeader";const CardTitle=react.forwardRef(((_ref3,ref)=>{let{className,children,...props}=_ref3;return(0,jsx_runtime.jsx)("h3",{ref,className:(0,utils.cn)("text-lg font-semibold leading-none tracking-tight text-violet-dark dark:text-violet-light",className),...props,children})}));CardTitle.displayName="CardTitle";const CardDescription=react.forwardRef(((_ref4,ref)=>{let{className,...props}=_ref4;return(0,jsx_runtime.jsx)("p",{ref,className:(0,utils.cn)("text-sm text-violet-neutral",className),...props})}));CardDescription.displayName="CardDescription";const CardContent=react.forwardRef(((_ref5,ref)=>{let{className,...props}=_ref5;return(0,jsx_runtime.jsx)("div",{ref,className:(0,utils.cn)("p-1 pt-0",className),...props})}));CardContent.displayName="CardContent";const CardFooter=react.forwardRef(((_ref6,ref)=>{let{className,...props}=_ref6;return(0,jsx_runtime.jsx)("div",{ref,className:(0,utils.cn)("flex items-center pt-4",className),...props})}));CardFooter.displayName="CardFooter",Card.__docgenInfo={description:"",methods:[],displayName:"Card",props:{variant:{required:!1,tsType:{name:"union",raw:'"default" | "data" | "interactive" | "success" | "warning" | "error" | "purple"',elements:[{name:"literal",value:'"default"'},{name:"literal",value:'"data"'},{name:"literal",value:'"interactive"'},{name:"literal",value:'"success"'},{name:"literal",value:'"warning"'},{name:"literal",value:'"error"'},{name:"literal",value:'"purple"'}]},description:"",defaultValue:{value:'"default"',computed:!1}}}},CardHeader.__docgenInfo={description:"",methods:[],displayName:"CardHeader"},CardFooter.__docgenInfo={description:"",methods:[],displayName:"CardFooter"},CardTitle.__docgenInfo={description:"",methods:[],displayName:"CardTitle"},CardDescription.__docgenInfo={description:"",methods:[],displayName:"CardDescription"},CardContent.__docgenInfo={description:"",methods:[],displayName:"CardContent"};const card_stories={title:"UI/Card",component:Card,tags:["autodocs"],argTypes:{variant:{control:{type:"select"},options:["default","data","interactive","success","warning","error","purple"]}},args:{variant:"default",children:"Card content"}},Default={},Data={args:{variant:"data",children:"Data variant card"}},Interactive={args:{variant:"interactive",children:"Interactive variant card"}},WithHeaderAndFooter={render:args=>(0,jsx_runtime.jsxs)(Card,{...args,children:[(0,jsx_runtime.jsxs)(CardHeader,{children:[(0,jsx_runtime.jsx)(CardTitle,{children:"Card Title"}),(0,jsx_runtime.jsx)(CardDescription,{children:"Card description goes here"})]}),(0,jsx_runtime.jsx)(CardContent,{children:"Main content of the card"}),(0,jsx_runtime.jsx)(CardFooter,{children:"Footer content"})]})},__namedExportsOrder=["Default","Data","Interactive","WithHeaderAndFooter"];Default.parameters={...Default.parameters,docs:{...Default.parameters?.docs,source:{originalSource:"{}",...Default.parameters?.docs?.source}}},Data.parameters={...Data.parameters,docs:{...Data.parameters?.docs,source:{originalSource:"{\n  args: {\n    variant: 'data',\n    children: 'Data variant card'\n  }\n}",...Data.parameters?.docs?.source}}},Interactive.parameters={...Interactive.parameters,docs:{...Interactive.parameters?.docs,source:{originalSource:"{\n  args: {\n    variant: 'interactive',\n    children: 'Interactive variant card'\n  }\n}",...Interactive.parameters?.docs?.source}}},WithHeaderAndFooter.parameters={...WithHeaderAndFooter.parameters,docs:{...WithHeaderAndFooter.parameters?.docs,source:{originalSource:"{\n  render: args => <Card {...args}>\r\n      <CardHeader>\r\n        <CardTitle>Card Title</CardTitle>\r\n        <CardDescription>Card description goes here</CardDescription>\r\n      </CardHeader>\r\n      <CardContent>Main content of the card</CardContent>\r\n      <CardFooter>Footer content</CardFooter>\r\n    </Card>\n}",...WithHeaderAndFooter.parameters?.docs?.source}}}},"./src/lib/utils.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{absoluteUrl:()=>absoluteUrl,cn:()=>cn,formatPrice:()=>formatPrice});var clsx__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__("./node_modules/clsx/dist/clsx.mjs"),tailwind_merge__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("./node_modules/tailwind-merge/dist/bundle-mjs.mjs"),process=__webpack_require__("./node_modules/process/browser.js");function cn(){for(var _len=arguments.length,inputs=new Array(_len),_key=0;_key<_len;_key++)inputs[_key]=arguments[_key];return(0,tailwind_merge__WEBPACK_IMPORTED_MODULE_0__.QP)((0,clsx__WEBPACK_IMPORTED_MODULE_1__.$)(inputs))}function formatPrice(price){return new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(price)}function absoluteUrl(path){return`${process.env.NEXT_PUBLIC_APP_URL||""}${path}`}}}]);